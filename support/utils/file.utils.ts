import { error } from '../../support/utils/logger';
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { load as safeLoad, dump as safeDump } from 'js-yaml';

const readYAML = (path: string): unknown => {
  try {
    return safeLoad(readFileSync(path, 'utf8'));
  } catch (err) {
    error(err);
    throw new Error(`There is no such file: ${path}`);
  }
};

const writeYAML = (path: string, data: unknown): void => {
  writeFileSync(path, safeDump(data), 'utf8');
};

const writeFile = (path: string, data: string): void => {
  const folders = path.split('/');
  let foldersPath = '';

  if (folders.length >= 2) {
    for (let i = 0; i < folders.length - 1; i++) {
      foldersPath += `${folders[i]}/`;
      if (!existsSync(foldersPath)) {
        mkdirSync(foldersPath, { recursive: true });
      }
    }
  }

  writeFileSync(path, data, 'utf8');
};

export { readYAML, writeYAML, writeFile };
