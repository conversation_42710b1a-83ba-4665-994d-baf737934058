const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

const getYear = (): number => {
  return new Date().getFullYear();
};

const getMonthName = (): string => {
  return monthNames[new Date().getMonth()];
};

const getDayNum = (): string => {
  const date = new Date().getDate();
  return date < 10 ? `0${date}` : `${date}`;
};

const getTime = (): string => {
  let hours = `${new Date().getHours()}`;
  let minutes = `${new Date().getMinutes()}`;
  if (`${new Date().getHours()}`.length === 1) {
    hours = `0${new Date().getHours()}`;
  }
  if (`${new Date().getMinutes()}`.length === 1) {
    minutes = `0${new Date().getMinutes()}`;
  }
  return `${hours}:${minutes}`;
};

export { getTime, getDayNum, getMonthName, getYear };
