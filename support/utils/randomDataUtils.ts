import { debug } from '../../support/utils/logger';
const randomNumberFromRange = (min: number, max: number): Promise<number> => {
  return new Promise(resolve => {
    resolve(Math.floor(Math.random() * (max - min)) + min);
  });
};

const randomString = async (mode: StringMode, length: number): Promise<string> => {
  let text = '';
  const modeValueArr = mode.split('');
  for (let i = 0; i < length; i++) {
    text += modeValueArr[await randomNumberFromRange(0, modeValueArr.length - 1)];
  }
  debug(text);
  return text;
};

enum StringMode {
  ALPHA = 'abcdifghigklmnopqrstuvwxyz',
  NUMERIC = '1234567890',
  ALPHANUMERIC = 'a1b2c3d4i5f6g7h8i9g0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z',
}

export { randomNumberFromRange, randomString, StringMode };
