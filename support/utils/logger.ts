/**
 * Улучшенный модуль логирования
 * Все логи выводятся только если LOG_ENABLED=true
 */

/**
 * Проверяет, включен ли режим логирования через переменную окружения LOG_ENABLED
 * @returns {boolean} true, если LOG_ENABLED=true, иначе false
 */
export const isLoggingEnabled = (): boolean => process.env.LOG_ENABLED === 'true';

/**
 * Форматирует сообщение для логирования
 * Преобразует объекты в JSON и объединяет аргументы
 * @param {any} message - Основное сообщение
 * @param {any[]} args - Дополнительные аргументы
 * @returns {string} Отформатированное сообщение
 */
const formatLogMessage = (message: any, args: any[] = []): string => {
  if (typeof message === 'object') {
    return JSON.stringify(message, null, 2);
  } else if (args.length > 0) {
    return `${message} ${args.map(arg => (typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg)).join(' ')}`;
  }
  return message;
};

/**
 * Базовая функция логирования
 * @param {string} level - Уровень логирования ('debug', 'info', 'warn', 'error')
 * @param {any} message - Сообщение или объект для логирования
 * @param {any[]} args - Дополнительные аргументы
 */
const log = (level: string, message: any, ...args: any[]): void => {
  if (isLoggingEnabled()) {
    const formattedMessage = formatLogMessage(message, args);
    const timestamp = new Date().toISOString();
    console.log(`${timestamp} [${level.toUpperCase()}]: ${formattedMessage}`);
  }
};

/**
 * Экспортируем базовый логгер для совместимости с существующим кодом
 */
export const logger = {
  debug: (message: string): void => log('debug', message),
  info: (message: string): void => log('info', message),
  warn: (message: string): void => log('warn', message),
  error: (message: string): void => log('error', message),
};

/**
 * Функция для логирования объектов в формате JSON
 * @param {any} obj - Объект для логирования
 * @param {string} [label] - Опциональная метка для объекта
 */
export const logObject = (obj: any, label?: string): void => {
  if (isLoggingEnabled()) {
    const message = label ? `${label}: ${JSON.stringify(obj, null, 2)}` : JSON.stringify(obj, null, 2);
    log('debug', message);
  }
};

/**
 * Функция для логирования сообщений уровня debug
 * @param {any} message - Сообщение или объект для логирования
 * @param {...any} args - Дополнительные аргументы
 */
export const debug = (message: any, ...args: any[]): void => log('debug', message, ...args);

/**
 * Функция для логирования сообщений уровня info
 * @param {any} message - Сообщение или объект для логирования
 * @param {...any} args - Дополнительные аргументы
 */
export const info = (message: any, ...args: any[]): void => log('info', message, ...args);

/**
 * Функция для логирования сообщений уровня warn
 * @param {any} message - Сообщение или объект для логирования
 * @param {...any} args - Дополнительные аргументы
 */
export const warn = (message: any, ...args: any[]): void => log('warn', message, ...args);

/**
 * Функция для логирования сообщений уровня error
 * @param {any} message - Сообщение или объект для логирования
 * @param {...any} args - Дополнительные аргументы
 */
export const error = (message: any, ...args: any[]): void => log('error', message, ...args);
