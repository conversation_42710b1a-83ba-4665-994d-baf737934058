const axios = require('axios');
const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs');
const FormData = require('form-data');

function calculateDuration(startDate, endDate) {
  const durationInMilliseconds = endDate - startDate;
  const durationInSeconds = durationInMilliseconds / 1000;
  return durationInSeconds;
}

class CustomReporter {
  constructor(globalConfig, reporterOptions, reporterContext) {
    this._globalConfig = globalConfig;
    this._options = reporterOptions;
    this._context = reporterContext;
  }

  async onRunComplete(testContexts, results, report) {
    const FAILED = results.numFailedTests;
    const PASS = results.numPassedTests;
    const PENDING = results.numPendingTests;
    const TOTAL = results.numTotalTests;

    if (process.env.CI) {
      //
      if (process.argv.includes('tests/api/apiGW')) {
      } else {
        const reportHtmlPath = 'report/html-report/CorezoidReport.html';
        const pdfFilePath = 'report/to/Report.pdf';

        const browser = await puppeteer.launch({ args: ['--no-sandbox'] });
        const page = await browser.newPage();

        await page.goto(`file://${path.resolve(reportHtmlPath)}`, { waitUntil: 'networkidle0' });

        // Создаем директорию, если она не существует
        const dirPath = path.dirname(pdfFilePath);
        if (!fs.existsSync(dirPath)) {
          fs.mkdirSync(dirPath, { recursive: true });
        }

        // Создаем PDF из HTML
        await page.pdf({ path: pdfFilePath, format: 'A4', landscape: true, scale: 0.9 });

        await browser.close();
        console.log('PDF-отчет успешно создан:', pdfFilePath);
      }
    }

    // console.log('report', report);
    // console.log('results', results);
    // console.log('testContexts', testContexts);

    // получение тестов в которых ошибки, для отправки в телеграм, локально работает, а в сообщениях пусто :(
    const regex = /● .+› (.+)\x1B\[39m\x1B\[22m/g;
    const maxFailedTestsToShow = 10;
    const allFailedTests = [];

    results.testResults.forEach(testResult => {
      const failureMessage = testResult.failureMessage;
      const failedTests = [];
      let match;

      while ((match = regex.exec(failureMessage)) !== null) {
        failedTests.push(match[1]);
      }

      allFailedTests.push(...failedTests);
    });
    const limitedFailedTests = allFailedTests.slice(0, maxFailedTestsToShow);
    const failedTestsMessage = limitedFailedTests.join('\n');

    console.log('Failed tests', failedTestsMessage);

    // if (process.env.CI) {
    //   if (process.argv.includes('tests/api/apiGW')) {
    //     console.log('Тесты для APIGW не требуют отправки сообщения в Telegram.');
    //   } else {
    //     const URL = 'https://api.telegram.org/bot2022623153:AAFMmczgHAyXq1hY0TjIcG28kLBd6r5R3ic/sendDocument';
    //     const CHAT_ID = '-584920612';
    //     const pdfFilePath = 'report/to/Report.pdf';

    //     if (FAILED > 0) {
    //       const message = `Run status: failed 🚨\n\nPass: ${PASS} Fail: ${FAILED} Pending: ${PENDING}\n\nProject: ${process.env.CI_PROJECT_NAME}\nURL: ${process.env.CI_PROJECT_URL}/pipelines/${process.env.CI_PIPELINE_ID}/\nENV: ${process.env.API_URL}\nRunner email: ${process.env.GITLAB_USER_EMAIL}\nSuite: ${process.env.SUITE}\nReport: https://mw-qa-reports.middleware.biz/${process.env.UUID}.zip`;

    //       console.log('message', message);

    //       const pdfStream = fs.createReadStream(pdfFilePath);
    //       const form = new FormData();
    //       form.append('chat_id', CHAT_ID);
    //       form.append('document', pdfStream);
    //       form.append('caption', message);

    //       axios.post(URL, form, {
    //         headers: {
    //           'Content-Type': `multipart/form-data; boundary=${form._boundary}`,
    //         }
    //       })
    //         .then(() => {
    //           console.log('Message with PDF document sent successfully');
    //         })
    //         .catch((error) => {
    //           console.error('Failed to send message with PDF document:', error);
    //         });
    //     } else {
    //       const message = `Run status: successful 🎉\n\nPass: ${PASS} Fail: ${FAILED} Pending: ${PENDING}\n\nURL: ${process.env.CI_PROJECT_URL}/pipelines/${process.env.CI_PIPELINE_ID}/\nENV: ${process.env.API_URL}\nRunner email: ${process.env.GITLAB_USER_EMAIL}\nSuite: ${process.env.SUITE}`;
    //       // const message = `Run status: successful 🎉\n\nPass: ${PASS} Fail: ${FAILED} Pending: ${PENDING}\n\nProject: ${process.env.CI_PROJECT_NAME}\nURL: ${process.env.CI_PROJECT_URL}/pipelines/${process.env.CI_PIPELINE_ID}/\nENV: ${process.env.API_URL}\nRunner email: ${process.env.GITLAB_USER_EMAIL}\nSuite: ${process.env.SUITE}\nReport: https://mw-qa-reports.middleware.biz/${process.env.UUID}.zip`;

    //       console.log('message', message);

    //       // const pdfStream = fs.createReadStream(pdfFilePath);
    //       const form = new FormData();
    //       form.append('chat_id', CHAT_ID);
    //       // form.append('document', pdfStream);
    //       form.append('caption', message);
    //       form.append('disable_notification', 'true');

    //       axios.post(URL, form, {
    //         headers: {
    //           'Content-Type': `multipart/form-data; boundary=${form._boundary}`,
    //         }
    //       })
    //         .then(() => {
    //           console.log('Message sent successfully');
    //         })
    //         .catch((error) => {
    //           console.error('Failed to send message:', error);
    //         });
    //     }
    //   }
    // }

    if (process.env.CI) {
      if (process.argv.includes('tests/api/apiGW')) {
        console.log('Тесты для APIGW не требуют отправки сообщения в Telegram.');
      } else {
        const URL_MESSAGE = 'https://api.telegram.org/bot2022623153:AAFMmczgHAyXq1hY0TjIcG28kLBd6r5R3ic/sendMessage';
        const URL_DOCUMENT = 'https://api.telegram.org/bot2022623153:AAFMmczgHAyXq1hY0TjIcG28kLBd6r5R3ic/sendDocument';
        const CHAT_ID = '-584920612';
        const pdfFilePath = 'report/to/Report.pdf';

        if (FAILED > 0) {
          const message = `Run status: failed 🚨\n\nPass: ${PASS} Fail: ${FAILED} Pending: ${PENDING}\n\nProject: ${process.env.CI_PROJECT_NAME}\nURL: ${process.env.CI_PROJECT_URL}/pipelines/${process.env.CI_PIPELINE_ID}/\nENV: ${process.env.API_URL}\nRunner email: ${process.env.GITLAB_USER_EMAIL}\nSuite: ${process.env.SUITE}\nReport: https://mw-qa-reports.middleware.biz/${process.env.UUID}.zip`;

          console.log('message', message);

          // Создаем директорию, если она не существует
          const dirPath = path.dirname(pdfFilePath);
          if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
          }

          const pdfStream = fs.createReadStream(pdfFilePath);
          const form = new FormData();
          form.append('chat_id', CHAT_ID);
          form.append('document', pdfStream);
          form.append('caption', message);

          axios
            .post(URL_DOCUMENT, form, {
              headers: {
                'Content-Type': `multipart/form-data; boundary=${form._boundary}`,
              },
            })
            .then(() => {
              console.log('Message with PDF document sent successfully');
            })
            .catch(error => {
              console.error('Failed to send message with PDF document:', error);
            });
        } else {
          const message = `Run status: successful 🎉\n\nPass: ${PASS} Fail: ${FAILED} Pending: ${PENDING}\n\nURL: ${process.env.CI_PROJECT_URL}/pipelines/${process.env.CI_PIPELINE_ID}/\nENV: ${process.env.API_URL}\nRunner email: ${process.env.GITLAB_USER_EMAIL}\nSuite: ${process.env.SUITE}`;

          console.log('message', message);

          axios
            .post(URL_MESSAGE, {
              chat_id: CHAT_ID,
              text: message,
              disable_notification: true,
            })
            .then(() => {
              console.log('Message sent successfully');
            })
            .catch(error => {
              console.error('Failed to send message:', error);
            });
        }
      }
    }

    // отправка сообщений на webhook для карты тестирования
    if (process.env.CI) {
      const jobId = process.env.CI_JOB_ID;
      const startedAtTimestamp = process.env.CI_JOB_STARTED_AT;
      const startedAt = new Date(startedAtTimestamp);
      const duration = calculateDuration(startedAt, new Date());
      const name = process.env.SUITE;
      const projectName = process.env.CI_PROJECT_NAME;
      const url = process.env.API_URL;
      const env = process.env.ENV;
      const url_report = `https://mw-qa-reports.middleware.biz/${process.env.UUID}.zip`;

      const jsonData = {
        pass: PASS,
        fail: FAILED,
        pending: PENDING,
        total: TOTAL,
        name,
        started_at: startedAt,
        duration,
        jobId,
        project: projectName,
        url,
        env,
        url_report,
      };

      const webhookURL = 'https://www.corezoid.com/api/2/json/public/1390616/74e1f4fb31b53490acb9e46dcd820ca4246280e6';

      try {
        await axios.post(webhookURL, jsonData, {
          headers: {
            'Content-Type': 'application/json',
          },
        });
        console.log('JSON data sent to webhook successfully');
      } catch (error) {
        console.error('Failed to send JSON data to webhook:', error);
      }
    }
  }

  getLastError() {
    if (this._shouldFail) {
      return new Error('Custom error reported!');
    }
  }
}

module.exports = CustomReporter;
