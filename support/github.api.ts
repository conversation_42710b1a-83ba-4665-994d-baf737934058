import axios from 'axios';
import { User } from '../infrastructure/model/User';
import { logger } from './utils/logger';

const setDeployKey = async (user: User, key: string): Promise<void> => {
  try {
    await axios({
      url: 'https://api.github.com/repos/GrigurkoAlexey/git-call-private/keys',
      method: 'POST',
      headers: {
        Authorization: 'token 914c3a313e12aa2674a10fa2f1721cd214c38c4e',
      },
      data: {
        title: user.email,
        read_only: true,
        key,
      },
    });
  } catch (error) {
    logger.info('[GIT CALL API] SSH Key already present');
  }
};

export { setDeployKey };
