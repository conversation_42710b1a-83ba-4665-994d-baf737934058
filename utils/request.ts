import { axiosInstance } from '../application/api/AxiosClient';
import { AxiosResponse } from 'axios';
import { MyApiResponse, RetryOptions, makeRequestWithRetries } from './requestRetries';

export enum Method {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
}

class UrlFactory {
  API_JSON2: string;
  WORKSPACE: string;
  TACTS_INFO: string;
  API_FACE: string;

  constructor(host: string) {
    this.API_JSON2 = `${host}api/2/json`;
    this.WORKSPACE = `${host}face/api/1/workspaces`;
    this.TACTS_INFO = `${host}system/tacts_info`;
    this.API_FACE = `${host}face/api/1`;
  }
}

export interface UrlParams {
  company_id?: string;
  workspace_id?: string;
  [key: string]: string | undefined;
}

export interface RequestConfig {
  url: string;
  params?: UrlParams;
  method: Method;
  data?: any;
}

class UrlBuilder {
  buildUrl(url: string, params?: UrlParams): string {
    let path = url;

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        path = path.replace(`\${${key}}`, value || '');
      });
    }

    return path;
  }
}

export type AuthType = {
  type: 'cookie' | 'token';
  value: string;
};

interface RequestHeaders {
  // 'Content-Type': string;
  Origin: string;
  Cookie?: string;
  Authorization?: string;
}

export class ApiService {
  private urlBuilder: UrlBuilder;

  constructor() {
    this.urlBuilder = new UrlBuilder();
  }

  private adaptResponse(axiosResponse: AxiosResponse): MyApiResponse {
    return {
      body: axiosResponse.data,
      status: axiosResponse.status,
    };
  }

  private createHeaders(origin: string, cookie: string): RequestHeaders {
    return {
      // 'Content-Type': 'application/json',
      Origin: origin,
      Cookie: cookie,
    };
  }

  async request(config: RequestConfig & { headers: RequestHeaders }): Promise<AxiosResponse> {
    const url = this.urlBuilder.buildUrl(config.url, config.params);

    return axiosInstance({
      method: config.method,
      url,
      data: config.data,
      headers: config.headers,
    });
  }

  async requestWithRetries(
    config: RequestConfig & { headers: RequestHeaders },
    checkConditions: (response: MyApiResponse) => boolean,
    retryOptions?: RetryOptions,
  ): Promise<MyApiResponse> {
    return makeRequestWithRetries(
      async () => {
        const axiosResponse = await this.request(config);
        return this.adaptResponse(axiosResponse);
      },
      checkConditions,
      retryOptions,
    );
  }
}

export class AuthUser {
  private apiService: ApiService;

  constructor(private auth: AuthType) {
    this.apiService = new ApiService();
  }

  private createConfig(config: RequestConfig): RequestConfig & { headers: RequestHeaders } {
    const origin = new URL(config.url).origin;
    const headers: RequestHeaders = {
      // 'Content-Type': 'application/json',
      Origin: origin,
    };

    if (this.auth.type === 'cookie') {
      headers.Cookie = this.auth.value;
    } else if (this.auth.type === 'token') {
      headers.Authorization = this.auth.value;
    }

    return {
      ...config,
      headers,
    };
  }

  async request(config: RequestConfig): Promise<AxiosResponse> {
    const fullConfig = this.createConfig(config);
    return this.apiService.request(fullConfig);
  }

  async requestWithRetries(
    config: RequestConfig,
    checkConditions: (response: MyApiResponse) => boolean,
    retryOptions?: RetryOptions,
  ): Promise<MyApiResponse> {
    const fullConfig = this.createConfig(config);
    return this.apiService.requestWithRetries(fullConfig, checkConditions, retryOptions);
  }
}

export const createAuthUser = (auth: string, type: 'cookie' | 'token' = 'cookie'): AuthUser => {
  return new AuthUser({ type, value: auth });
};

export const createApiUrls = (host: string): UrlFactory => new UrlFactory(host);
