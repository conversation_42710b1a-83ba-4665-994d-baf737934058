import { v4 as uuidv4 } from 'uuid';

export enum REQUEST_TYPE {
  CREATE = 'create',
  DELETE = 'delete',
  DESTROY = 'destroy',
  RESTORE = 'restore',
  FAVORITE = 'favorite',
  LIST = 'list',
  LINK = 'link',
  MODIFY = 'modify',
  SHOW = 'show',
  CONFIRM = 'confirm',
  GET = 'get',
  STEP_NEXT = 'step_next',
  STEP_PREV = 'step_prev',
  STEP_GOTO = 'step_goto',
  COMPILE = 'compile',
  LOAD = 'load',
  CHECK = 'check',
  MERGE = 'merge',
  RESET = 'reset',
  UPLOAD = 'upload',
  COMPARE = 'compare',
  CLONE = 'clone',
  UPSERT = 'upsert',
  STOP = 'stop',
  DOWNLOAD = 'download',
}

export enum OBJ_TYPE {
  DASHBOARD = 'dashboard',
  DASHBOARDS = 'dashboards',
  CONV = 'conv',
  CONVS = 'convs',
  FOLDER = 'folder',
  TASK = 'task',
  NODE = 'node',
  INSTANCE = 'instance',
  INSTANCES = 'instances',
  OBJ_COPY = 'obj_copy',
  OBJ_SCHEME = 'obj_scheme',
  SCHEME = 'scheme',
  SYS_STAT = 'sys_stat',
  USER_TRAF = 'used_traff',
  COMPANY_USERS = 'company_users',
  PATH_TO_FOLDER = 'path_to_folder',
  COMPANY = 'company',
  PROJECT = 'project',
  PROJECTS = 'projects',
  STAGE = 'stage',
  VERSION = 'version',
  VERSIONS = 'versions',
  ALIAS = 'alias',
  ALIASES = 'aliases',
  API_KEY = 'api_key',
  CALLBACK_HASH = 'callback_hash',
  API_CODE = 'api_code',
  CHART = 'chart',
  COMMITS = 'commits',
  COMMIT = 'commit',
  CONV_PARAMS = 'conv_params',
  CONV_TITLE = 'conv_title',
  FOLDER_BRANCH = 'folder_branch',
  GROUP = 'group',
  USER = 'user',
  USER_PERMISSIONS = 'user_permissions',
  HISTORY = 'history',
  TASK_HISTORY = 'task_history',
  INVITE = 'invite',
  OBJS = 'objs',
  PRIVS = 'privs',
  STATISTICS = 'statistics',
  OWNER = 'owner',
  STAT = 'stat',
  GIT_CALL = 'git_call',
  OBJECT = 'object',
  ENV_VAR = 'env_var',
  USER_STATUS = 'user_status',
  CONV_AUTH = 'conv_auth',
  BOT_WIZZARD = 'bot_wizzard',
  GLOBAL_COUNTERS = 'global_counters',
  CHANNEL = 'channel',
  NODES = 'nodes',
  CONVEYOR_BILLING = 'conveyor_billing',
  CONF = 'conf',
  UPLOAD = 'upload',
  USER_TO_COMPANY = 'user_to_company',
  OTP = 'otp',
}

export enum RESP_STATUS {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  INTERNAL_ERROR = 500,
}

export enum PROC_STATUS {
  OK = 'ok',
  ERROR = 'error',
}

export enum PATH {
  JSON = 'api/2/json',
  UPLOAD = 'api/2/upload',
  DOWNLOAD = 'api/2/download',
  MERGE = 'api/2/merge',
  COMPARE = 'api/2/compare',
  COPY = 'api/2/copy',
}

interface RequestParams {
  type?: string;
  obj?: string;
  [key: string]: any;
}

export interface CorezoidRequest {
  ops: RequestParams[];
}

export const generateName = (prefix: OBJ_TYPE): string => {
  const uniqueId = uuidv4().split('-')[0];
  if (!Object.values(OBJ_TYPE).includes(prefix)) {
    throw new Error('Invalid OBJ_TYPE prefix');
  }
  return `${prefix}-${uniqueId}`;
};

export interface Corezoidrequest {
  path: string;
  body: { ops: RequestParams[] } | RequestParams;
}

function getPath(params: RequestParams): PATH {
  const { type, obj } = params;
  switch (true) {
    case obj === OBJ_TYPE.OBJ_COPY:
      return PATH.COPY;
    case type === REQUEST_TYPE.COMPARE:
      return PATH.COMPARE;
    case type === REQUEST_TYPE.UPLOAD:
      return PATH.UPLOAD;
    case type === REQUEST_TYPE.MERGE:
      return PATH.MERGE;
    case type === REQUEST_TYPE.DOWNLOAD:
      return PATH.DOWNLOAD;
    default:
      return PATH.JSON;
  }
}

export const createRequestWithOps = (params: RequestParams): Corezoidrequest => {
  const path = getPath(params);
  return { path, body: { ops: [{ ...params }] } };
};

export const createRequestWithObj = (params: RequestParams): Corezoidrequest => {
  const path = getPath(params);
  return { path, body: { ...params } };
};
