import { debug, error } from '../support/utils/logger';
export interface MyApiResponse<T = any> {
  body: T;
  status: number;
}

export interface RetryOptions {
  maxRetries?: number;
  initialDelay?: number;
  maxDelay?: number;
}

export async function makeRequestWithRetries<T>(
  makeRequest: () => Promise<MyApiResponse<T>>,
  checkConditions: (response: MyApiResponse<T>) => boolean,
  maxRetriesOrOptions: number | RetryOptions = 5,
): Promise<MyApiResponse<T>> {
  const options =
    typeof maxRetriesOrOptions === 'number'
      ? { maxRetries: maxRetriesOrOptions, delay: 2000 }
      : { maxRetries: 10, delay: 1000, ...maxRetriesOrOptions };

  let response = await makeRequest();
  let conditionsPassed = checkConditions(response);
  let retries = 0;

  while (!conditionsPassed && retries < options.maxRetries) {
    debug(`Conditions not met. Waiting ${options.delay}ms before retry (${retries + 1}/${options.maxRetries})...`);

    await new Promise(resolve => setTimeout(resolve, options.delay));

    try {
      response = await makeRequest();
      conditionsPassed = checkConditions(response);
    } catch (err) {
      error(`Retry ${retries + 1} failed:`, err);
    }

    retries++;
  }

  if (!conditionsPassed) {
    throw new Error(`Max ${options.maxRetries} retries exceeded. Last response: ${JSON.stringify(response)}`);
  }

  return response;
}
