export type Permiss = 'view' | 'create' | 'modify' | 'delete';

export interface Permission {
  type: Permiss;
  list_obj: string[];
}

export interface PermissionsFlag {
  view: boolean;
  create: boolean;
  modify: boolean;
  delete: boolean;
}

export interface PermissionCombinationResult {
  description: string;
  privsFirstObject: Permission[];
  privsSecondObject: Permission[];
  privsProc: Permission[];
  expectFirstObject: PermissionsFlag;
  expectSecondObject: PermissionsFlag;
  expectConvPerm: PermissionsFlag;
}

const VALID_TYPES: Permiss[] = ['view', 'create', 'modify', 'delete'];

const viewPerm: Permission = { type: 'view', list_obj: ['all'] };
const taskPerm: Permission = { type: 'create', list_obj: ['all'] };
const modifyPerm: Permission[] = [
  { type: 'modify', list_obj: ['all'] },
  { type: 'delete', list_obj: ['all'] },
];

const PERMISSION_COMBINATIONS: { name: string; permissions: Permission[] }[] = [
  { name: 'View', permissions: [viewPerm] },
  { name: 'Task', permissions: [taskPerm] },
  { name: 'View + Task', permissions: [viewPerm, taskPerm] },
  { name: 'View + Modify', permissions: [viewPerm, ...modifyPerm] },
  { name: 'View + Task + Modify', permissions: [viewPerm, taskPerm, ...modifyPerm] },
  { name: 'No Rights', permissions: [] },
];

function filterValidPermissions(perms: Permission[]): Permission[] {
  return perms.filter(p => VALID_TYPES.includes(p.type));
}

function mapPermissionsToObject(perms: Permission[]): PermissionsFlag {
  return perms.reduce<PermissionsFlag>(
    (acc, { type }): PermissionsFlag => {
      acc[type] = true;
      return acc;
    },
    { create: false, view: false, modify: false, delete: false },
  );
}

function generateExpectedPermissions(basePerms: PermissionsFlag, inheritedPerms: PermissionsFlag): PermissionsFlag {
  const result: PermissionsFlag = { create: false, view: false, modify: false, delete: false };

  for (const key in inheritedPerms) {
    result[key as keyof PermissionsFlag] = inheritedPerms[key as keyof PermissionsFlag];
  }

  for (const key in basePerms) {
    if (basePerms[key as keyof PermissionsFlag]) {
      result[key as keyof PermissionsFlag] = true;
    }
  }
  return result;
}

function generatePairwiseCombinations<T>(arr1: T[], arr2: T[], arr3: T[]): T[][] {
  if (arr1.length === 0 || arr2.length === 0 || arr3.length === 0) {
    return [];
  }

  const pairs12 = new Set<string>();
  for (let i = 0; i < arr1.length; i++) {
    for (let j = 0; j < arr2.length; j++) {
      pairs12.add(`${i},${j}`);
    }
  }
  const pairs13 = new Set<string>();
  for (let i = 0; i < arr1.length; i++) {
    for (let k = 0; k < arr3.length; k++) {
      pairs13.add(`${i},${k}`);
    }
  }
  const pairs23 = new Set<string>();
  for (let j = 0; j < arr2.length; j++) {
    for (let k = 0; k < arr3.length; k++) {
      pairs23.add(`${j},${k}`);
    }
  }

  const candidates: [number, number, number][] = [];
  for (let i = 0; i < arr1.length; i++) {
    for (let j = 0; j < arr2.length; j++) {
      for (let k = 0; k < arr3.length; k++) {
        candidates.push([i, j, k]);
      }
    }
  }

  const selected: [number, number, number][] = [];
  while (pairs12.size > 0 || pairs13.size > 0 || pairs23.size > 0) {
    let bestCandidate: [number, number, number] | null = null;
    let bestCoverage = -1;
    for (const candidate of candidates) {
      const [i, j, k] = candidate;
      let coverage = 0;
      if (pairs12.has(`${i},${j}`)) coverage++;
      if (pairs13.has(`${i},${k}`)) coverage++;
      if (pairs23.has(`${j},${k}`)) coverage++;
      if (coverage > bestCoverage) {
        bestCoverage = coverage;
        bestCandidate = candidate;
      }
    }
    if (!bestCandidate) break;
    selected.push(bestCandidate);
    const [i, j, k] = bestCandidate;
    pairs12.delete(`${i},${j}`);
    pairs13.delete(`${i},${k}`);
    pairs23.delete(`${j},${k}`);
    candidates.splice(candidates.indexOf(bestCandidate), 1);
  }
  return selected.map(([i, j, k]) => [arr1[i], arr2[j], arr3[k]]);
}

function getLabels(firstType: 'project' | 'folder') {
  return firstType === 'project'
    ? { firstLabel: 'Project', secondLabel: 'Stage', thirdLabel: 'Process' }
    : { firstLabel: 'Folder', secondLabel: 'Folder', thirdLabel: 'Process' };
}

export function getArrayPermissionsCombinations(
  firstType: 'project' | 'folder',
  mode: 'full' | 'pairwise' = 'pairwise',
): PermissionCombinationResult[] {
  if (!firstType || !mode) {
    throw new Error("Invalid parameters: 'firstType' and 'mode' are required.");
  }

  const results: PermissionCombinationResult[] = [];
  const labels = getLabels(firstType);

  if (mode === 'full') {
    for (const first of PERMISSION_COMBINATIONS) {
      for (const second of PERMISSION_COMBINATIONS) {
        for (const proc of PERMISSION_COMBINATIONS) {
          const expectFirstObject = generateExpectedPermissions(
            mapPermissionsToObject(filterValidPermissions(first.permissions)),
            { create: false, view: false, modify: false, delete: false },
          );
          const expectSecondObject = generateExpectedPermissions(
            mapPermissionsToObject(filterValidPermissions(second.permissions)),
            expectFirstObject,
          );
          const expectConvPerm = generateExpectedPermissions(
            mapPermissionsToObject(filterValidPermissions(proc.permissions)),
            expectSecondObject,
          );
          results.push({
            description: `Testing combination: ${labels.firstLabel}: ${first.name}, ${labels.secondLabel}: ${second.name}, ${labels.thirdLabel}: ${proc.name}`,
            privsFirstObject: first.permissions,
            privsSecondObject: second.permissions,
            privsProc: proc.permissions,
            expectFirstObject,
            expectSecondObject,
            expectConvPerm,
          });
        }
      }
    }
  } else if (mode === 'pairwise') {
    const combinations = generatePairwiseCombinations(
      PERMISSION_COMBINATIONS,
      PERMISSION_COMBINATIONS,
      PERMISSION_COMBINATIONS,
    );
    for (const combination of combinations) {
      const [first, second, proc] = combination;
      const expectFirstObject = generateExpectedPermissions(
        mapPermissionsToObject(filterValidPermissions(first.permissions)),
        { create: false, view: false, modify: false, delete: false },
      );
      const expectSecondObject = generateExpectedPermissions(
        mapPermissionsToObject(filterValidPermissions(second.permissions)),
        expectFirstObject,
      );
      const expectConvPerm = generateExpectedPermissions(
        mapPermissionsToObject(filterValidPermissions(proc.permissions)),
        expectSecondObject,
      );
      results.push({
        description: `Testing combination: ${labels.firstLabel}: ${first.name}, ${labels.secondLabel}: ${second.name}, ${labels.thirdLabel}: ${proc.name}`,
        privsFirstObject: first.permissions,
        privsSecondObject: second.permissions,
        privsProc: proc.permissions,
        expectFirstObject,
        expectSecondObject,
        expectConvPerm,
      });
    }
  }

  return results;
}
