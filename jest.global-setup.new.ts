import { debug, error, info } from './support/utils/logger';
import { application } from './application/Application';
import { ConfigurationManager } from './infrastructure/config/ConfigurationManager';
import axios from 'axios';

/**
 * Глобальная настройка для тестов
 * Запускается один раз перед всеми тестами
 * Создает необходимые ресурсы для тестов и сохраняет их в переменные окружения
 * @returns {Promise<void>}
 */
module.exports = async (): Promise<void> => {
  try {
    info('Начало глобальной настройки тестов');
    const config = ConfigurationManager.getConfiguration();
    const baseUrl = config.getUrl();

    // Получаем авторизованного пользователя
    const user = await application.getAuthorizedUser({ company: {} }, 0);
    const cookieUserOwner = user.cookieUser;
    debug(`Авторизованный пользователь: ${user.email}`);
    debug(`Создан API клиент для пользователя: ${user.email}`);

    // Создаем компанию с уникальным именем
    const companyName = `TestCompany_${Date.now()}`;
    debug(`Создание компании: ${companyName}`);

    const createCompany = await axios({
      method: 'POST',
      url: `${baseUrl}api/2/json`,
      data: {
        ops: [
          {
            type: 'create',
            obj: 'company',
            name: companyName,
            description: 'company',
            site: 'corezoid.com',
          },
        ],
      },
      headers: {
        Cookie: cookieUserOwner,
        Origin: baseUrl,
        Referer: 'https://admin-pre.corezoid.com/workspace/folder/0',
      },
    });

    // Проверка ответа (без expect, так как @jest/globals не доступен вне тестов)
    if (createCompany.status !== 200 || createCompany.data?.ops?.[0]?.proc !== 'ok') {
      error('Ошибка при создании компании, статус:', createCompany.status);
      error('Ответ:', createCompany.data);
      throw new Error('Ошибка при создании компании');
    }

    const company_id = createCompany.data?.ops?.[0]?.obj_id;

    if (!company_id) {
      error('Ошибка: не удалось получить ID компании из ответа.');
      error('Ответ:', JSON.stringify(createCompany.data, null, 2));
      throw new Error('Неверный формат ответа при создании компании');
    }

    debug(`Создана компания с ID: ${company_id}`);

    // Создаем API ключ в новой компании
    debug('Создание API ключа для тестов');

    const createKeyRequest = await axios({
      method: 'POST',
      url: `${baseUrl}api/2/json`,
      data: {
        ops: [{ type: 'create', obj: 'user', company_id, title: `Key_${Date.now()}`, logins: [{ type: 'api' }] }],
      },
      headers: {
        Cookie: cookieUserOwner,
        Origin: baseUrl,
      },
    });

    // Проверка ответа без expect

    if (createKeyRequest.status !== 200) {
      error('Ошибка при создании API ключа, статус:', createKeyRequest.status);
      error('Ответ:', createKeyRequest.data);
      throw new Error('Ошибка при создании API ключа');
    }
    console.log(createKeyRequest.data.ops[0].user);

    const createdKey = createKeyRequest.data?.ops?.[0]?.users?.[0]?.logins?.[0];
    const createdUser = createKeyRequest.data?.ops?.[0]?.users?.[0];

    if (!createdKey || !createdUser) {
      error('Ошибка: не удалось получить данные ключа или пользователя из ответа.');
      error('Ответ:', JSON.stringify(createKeyRequest.data, null, 2));
      throw new Error('Неверный формат ответа при создании API ключа');
    }

    // Сохраняем данные в переменные окружения для использования в тестах
    process.env.API_SECRET = createdKey.key;
    process.env.API_KEY = createdKey.obj_id;
    process.env.OBJ_ID_KEY = createdUser.obj_id;
    process.env.KEY_TITLE = createdUser.title;
    process.env.COMPANY_ID = company_id;
    process.env.COMPANY_NAME = companyName;

    console.log('=============================================');
    console.log('Глобальная настройка тестов завершена успешно');
    console.log(`Авторизованный пользователь: ${user.email}`);
    console.log('Создана Компания: ', process.env.COMPANY_NAME);
    console.log('COMPANY_ID: ', process.env.COMPANY_ID);
    console.log('Создан API ключ: ', process.env.API_KEY);
    console.log('Создан API ключ ID: ', process.env.OBJ_ID_KEY);
    console.log('Создан API ключ Title: ', process.env.KEY_TITLE);
    console.log('=============================================');
  } catch (err) {
    error('Ошибка в globalSetup:', err);
    throw err; // Прерываем выполнение тестов при ошибке настройки
  }
};
