# План стабилизации тестов

## Выполненные шаги

1. ✅ **Создание изолированной среды для тестов**
   - Добавление нового конфига `jest.universal.new.config.js`
   - Автоматическое создание отдельной тестовой компании для каждого запуска
   - Автоматическое создание нового пользователя для выполнения всех тестов
   - Очистка созданных ресурсов после завершения тестов

## Текущие задачи

2. 🔄 **Анализ нестабильных тестов**

   - Выявить тесты, которые стабильно падают в изолированной среде
   - Классифицировать типы ошибок и сгруппировать по потенциальным причинам
   - Документировать условия воспроизведения ошибок

3. 🔄 **Мониторинг стабильности тестов**
   - Выполнение N запусков тестов в новой изолированной среде
   - Отслеживание отчетов выполнения тестов в течение нескольких дней
   - Анализ паттернов нестабильности и факторов, влияющих на успешность выполнения

## Запланированные шаги

4. 📝 **Устранение выявленных проблем**

   - Для тестов, зависящих от порядка выполнения

     - Добавление дополнительной изоляции через уникальные имена ресурсов
     - Разбивка на более мелкие группы тестов

   - Для тестов с race conditions

     - Улучшение ожидания завершения асинхронных операций
     - Добавление явных проверок состояния перед продолжением теста

   - Для тестов, зависящих от внешних ресурсов
     - Реализация моков или стабов для внешних зависимостей
     - Добавление механизмов повторных попыток для нестабильных API

5. 📝 **Улучшение структуры тестов**

   - Выделение повторяющихся паттернов в общие функции-помощники
   - Улучшение изоляции между различными наборами тестов

6. 📝 **Улучшение инфраструктуры тестирования**

   - Реализация механизма автоматического повторного запуска нестабильных тестов
   - Создание более детальных отчетов о причинах падения тестов
   - Добавление метрик стабильности тестов в CI

7. 📝 **Мониторинг результатов улучшений**
   - Отслеживание процента нестабильных тестов до и после улучшений
   - Сбор статистики о времени выполнения тестов
   - Регулярные обзоры стабильности тестовой инфраструктуры

## Потенциальные решения для нестабильных тестов

- Использование механизма повторов для нестабильных тестов (например, jest-retries)
- Добавление таймаутов для тестов, взаимодействующих с медленными API
- Изоляция состояния базы данных для тестов, зависящих от данных
- Явное создание предусловий для каждого теста вместо зависимости от предыдущих тестов
- Улучшение логирования для облегчения отладки падающих тестов

## Метрики успеха

- Полное устранение нестабильных тестов - все тесты должны стабильно проходить в любой среде выполнения
- Стабильное прохождение CI/CD без ложных срабатываний
- Уменьшение времени выполнения тестов за счет лучшей изоляции
- Улучшение документации по отладке проблем в тестах
- Повышение общего качества и надежности тестовой инфраструктуры
