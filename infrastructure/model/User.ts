import { ApiCookies, WebCookies } from '../auth/SignIn';

export class User {
  id: number;
  name: string;
  email: string;
  password: string;
  companies: Array<{ id: string; name?: string; role?: string }>;
  superadmin: boolean;
  webCookies: WebCookies[];
  apiCookies: ApiCookies[];
  cookieUser: any;

  constructor(
    name: string,
    email: string,
    password: string,
    companies: Array<{ id: string; name?: string; role?: string }>,
    superadmin: boolean,
    id: number,
  ) {
    this.name = name;
    this.email = email;
    this.password = password;
    this.companies = companies;
    this.superadmin = superadmin;
    this.id = id;
    this.webCookies = [];
    this.apiCookies = [];
  }

  public setWebCookies(cookies: WebCookies[]): this {
    this.webCookies = cookies;
    return this;
  }

  public setApiCookies(cookies: ApiCookies[]): this {
    this.apiCookies = cookies;
    return this;
  }

  public clearCookies(): void {
    this.webCookies = [];
    this.apiCookies = [];
  }
}
