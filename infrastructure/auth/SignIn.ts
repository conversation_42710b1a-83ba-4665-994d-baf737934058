import axios, { AxiosResponse } from 'axios';
import SetCookieParser, { parse } from 'set-cookie-parser';
import { debug, info, error } from '../../support/utils/logger';
import { User } from '../model/User';
import { ApplicationConfig } from '../interface/ApplicationConfig';
import { ConfigurationManager } from '../config/ConfigurationManager';
import { v4 as uuidv4 } from 'uuid';

const uuid = uuidv4();

debug('Generated UUID:', uuid);

export class SignIn {
  private static apiCookies: ApiCookies[] = [];
  private static webCookies: WebCookies[] = [];

  static async authorize(user: User): Promise<{ webCookies: WebCookies[]; apiCookies: ApiCookies[] }> {
    const applicationConfig = await ConfigurationManager.getConfiguration().getCorezoidConfig();

    if (applicationConfig?.auth_single_account) {
      await this.singleAccountAuthorization(user, applicationConfig);

      if (!this.apiCookies.length) {
        await this.singleAccountAuthorization(user, applicationConfig);
        if (!this.apiCookies.length) {
          await this.singleAccountAuthorization(user, applicationConfig);
        }
      }
    } else {
      await this.basicAuthorization(user);
    }

    return { webCookies: this.webCookies, apiCookies: this.apiCookies };
  }

  private static async basicAuthorization(user: User): Promise<User | void> {
    info('[SignIn] Authorize through the Base Corezoid Authorization');

    try {
      const response = await axios({
        method: 'POST',
        url: `${ConfigurationManager.getConfiguration().getApiUrl()}auth/corezoid/auth`,
        headers: {
          'content-type': 'application/x-www-form-urlencoded',
        },
        data: `login=${encodeURIComponent(user.email)}&password=${user.password}`,
      });

      info('[SignIn] Get Corezoid cookies');
      await this.handleAuthorizationResponse(response);

      const core = this.apiCookies[0].value;
      const __Host_core_ = this.apiCookies[1].value;
      const cookieUser = `${this.apiCookies[0].name}=${core};` + `${this.apiCookies[1].name}=${__Host_core_}`;

      user.cookieUser = cookieUser;
      return user;
    } catch (err) {
      error(`[ERROR]:[SignIn] Cannot authorize through the Base Corezoid Authorization\n${err}`);
    }
  }

  private static async handleAuthorizationResponse(response: AxiosResponse<any>): Promise<void> {
    if (response.headers && response.headers['set-cookie']) {
      await this.getCookies(parse(response.headers['set-cookie']));
    }
  }

  private static async singleAccountAuthorization(user: User, applicationConfig: ApplicationConfig): Promise<void> {
    try {
      this.apiCookies = [];
      this.webCookies = [];

      const clientId =
        applicationConfig.web_settings.env === 'pre' ? '5cb58faaa2710f3e2a000002' : '5c2e0718ee91e40aa0000001';

      const baseUrl = await ConfigurationManager.getConfiguration().getApiUrl();

      const domain = baseUrl.split('//')[1].split('/')[0];

      info('[SignIn] Authorize through the Single Account');

      const response = await axios({
        method: 'POST',
        url: `https://${applicationConfig.web_settings.host.single_account}/auth/single_account/auth`,
        headers: {
          'content-type': 'application/x-www-form-urlencoded',
          'X-Request-Id': uuid,
          origin: baseUrl,
          referer: baseUrl,
        },
        data: `login=${encodeURIComponent(user.email)}&password=${user.password}&redirect_uri=https%3A%2F%2F${domain}`,
      });

      info('[SignIn] Get Single Account cookies');

      await this.handleAuthorizationResponse(response);

      const tempCookies: string = this.apiCookies.map(item => `${item.name}=${item.value}`).join(';');

      const singleAccountUrl =
        applicationConfig.web_settings.env === 'pre'
          ? `https://account.pre.corezoid.com/oauth2/authorize?state=https%3A%2F%2Fadmin-pre.corezoid.com%2Fworkspace%2Ffolder%2F0&scope=single_account%3Aaccount.read&response_type=code&redirect_uri=https%3A%2F%2Fadmin-pre.corezoid.com%2Fauth2%2Fsingle_account%2Freturn%2F&client_id=${clientId}`
          : `https://account.dev.corezoid.com/oauth2/authorize/?client_id=${clientId}&redirect_uri=https%3A%2F%2Fadmin.dev.corezoid.com%2Fauth2%2Fsingle_account%2Freturn&response_type=code&scope=single_account%3Aaccount.read&state=https%3A%2F%2Fadmin.dev.corezoid.com%2F`;

      await axios({
        method: 'GET',
        url: singleAccountUrl,
        headers: {
          cookie: tempCookies,
          origin: baseUrl,
          referer: baseUrl,
          'X-Request-Id': uuid,
        },
        maxRedirects: 0,
      }).catch(async res => {
        await axios({
          method: 'GET',
          url: res.response.headers.location,
          headers: {
            cookie: tempCookies,
            origin: baseUrl,
            referer: baseUrl,
            'X-Request-Id': uuid,
          },
          maxRedirects: 0,
        }).catch(async res => {
          info('[SignIn] Get Corezoid cookies');
          debug('Response headers:', res.response.headers);
          debug('Location header:', res.response.headers.location);
          await this.getCookies(parse(res.response.headers['set-cookie']));

          const single_account = this.apiCookies[0].value;
          const core = this.apiCookies[1].value;
          const __Host_core_ = this.apiCookies[2].value;
          const core_pre_UUID_cookie = this.apiCookies.find(cookie => cookie.name === 'core_pre_UUID');
          const cookieUser =
            `${this.apiCookies[0].name}=` +
            single_account +
            ';' +
            `${this.apiCookies[1].name}=` +
            core +
            ';' +
            `${this.apiCookies[2].name}=` +
            __Host_core_ +
            (core_pre_UUID_cookie ? ';' + `${core_pre_UUID_cookie.name}=` + core_pre_UUID_cookie.value : '');
          user.cookieUser = cookieUser;

          return user;
        });
      });
    } catch (err) {
      error(`[ERROR]:[SignIn] Can not authorize through the Single Account\n${err}`);
      throw new Error(`Authorization failed`);
    }
  }

  static async getCookies(cookiesArr: SetCookieParser.Cookie[]): Promise<void> {
    return new Promise<void>(resolve => {
      for (const cookie of cookiesArr) {
        this.apiCookies.push({ name: cookie.name, value: cookie.value });
        this.webCookies.push({
          name: cookie.name,
          value: cookie.value,
          domain: '.corezoid.com',
          path: cookie.path ?? '/',
        });
      }
      resolve();
      return cookiesArr;
    });
  }
}

export interface WebCookies {
  name: string;
  value: string;
  domain: string;
  path: string;
}
export interface ApiCookies {
  name: string;
  value: string;
}
