import axios from 'axios';
import { ConfigurationManager } from '../config/ConfigurationManager';
import { User } from '../model/User';

export class SignUp {
  public static async signUp(user: User): Promise<User> {
    await axios({
      baseURL: `${ConfigurationManager.getConfiguration().getUrl()}auth2/test_tools/register`,
      method: 'POST',
      headers: {
        'content-type': 'application/x-www-form-urlencoded',
      },
      data: `login=${encodeURIComponent(user.email)}&password=${user.password}&nick=${user.name}`,
    });
    return user;
  }
}
