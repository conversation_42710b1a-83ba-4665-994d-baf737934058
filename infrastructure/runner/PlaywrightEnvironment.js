const playwright = require('playwright');
const NodeEnvironment = require('jest-environment-node');
const RunnerConfiguration = require('./config/RunnerConfiguration');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const runId = uuidv4();
fs.writeFileSync('uuid.txt', runId);

const eventListener = require('./utils/elkReporter');
const networkListener = require('./utils/networkListener');
const consoleListener = require('./utils/consoleListener');
const screenshoter = require('./utils/screenshoter');
const videoRecorder = require('./utils/videoRecorder');

class PlaywrightEnvironment extends NodeEnvironment {
  async setup() {
    await super.setup();
    const config = new RunnerConfiguration().getConfig();
    this.global.browser = await playwright.chromium.launch({
      headless: config.LaunchOptions.headless,
      browserType: config.LaunchOptions.browserType
    });
    this.global.context = await this.global.browser.newContext({
      viewport: config.LaunchOptions.viewport,
      ignoreHTTPSErrors: true,
      acceptDownloads: true,
    });
    this.global.page = await this.global.context.newPage();
  }

  async teardown() {
    const { browser } = this.global;
    if (browser) {
      await browser.close();
    }
    await super.teardown();
  }

  async handleTestEvent(event) {
    if (process.env.NETWORK_LISTENER === 'on') await networkListener(event, this.global.page);
    if (process.env.CONSOLE_LISTENER === 'on') await consoleListener(event, this.global.page);
    if (process.env.ELK_REPORT === 'on') await eventListener(event, this.global.page);
    // if (process.env.VIDEO_RECORDER === 'on') await videoRecorder(event, this.global.page);
    if (process.env.SCREENSHOTER === 'on') await screenshoter(event, this.global.page);
  }
}

module.exports = PlaywrightEnvironment;
