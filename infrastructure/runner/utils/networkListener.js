const fs = require('fs');

let networkLogs = [];
const rules = [
  'text/html',
  'text/css',
  'text/plain',
  'text/javascript',
  'image/gif',
  'image/png',
  'image/svg+xml',
  'image/jpeg',
  'binary/octet-stream',
  'application/javascript',
];

module.exports = async (event, page) => {
  switch (event.name) {
    case 'setup':
      console.log('[NetWork][Optional] Clear logs before new suite');
      networkLogs = [];
      page.on('response', async response => {
        if (response.url().includes('corezoid') && !rules.includes(response.headers()['content-type'])) {
          try {
            const res = await response.body();
            networkLogs.push({
              time: new Date().toISOString(),
              request: {
                url: response.request().url(),
                headers: response.request().headers(),
                body: JSON.parse(response.request().postData()),
              },
              response: { url: response.url(), headers: response.headers(), body: res.toString() },
            });
          } catch (err) {
            console.log(`[NetWork][Optional] Listener can't parse response`);
          }
        }
      });
      break;
    case 'test_fn_success':
      console.log('[NetWork][Optional] Clear logs before new test');
      networkLogs = [];
      break;
    case 'test_fn_failure':
      const testName = `${event.test.parent.name.replace(/\W/g, '-')}_${event.test.name.replace(/\W/g, '-')}`;
      fs.mkdirSync('report/network-logs', { recursive: true });
      console.log('[NetWork][Optional] Test failed, save network logs');
      fs.writeFileSync(`report/network-logs/${testName}.txt`, JSON.stringify(networkLogs, null, 2), err => {
        console.log(err);
      });
      networkLogs = [];
      break;
  }
};
