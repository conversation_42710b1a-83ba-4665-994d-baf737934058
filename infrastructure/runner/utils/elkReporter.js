const axios = require('axios')
const { safeLoad } = require('js-yaml')
const fs = require('fs')
const { v4: uuidv4 } = require('uuid')

const runId = uuidv4()

const testReport = {}

module.exports = async event => {
  switch (event.name) {
    case 'run_start':
      testReport.timeStart = Date.now()
      break
    case 'test_fn_success':
      testReport.timeEnd = Date.now()
      testReport.suitName = event.test.parent.name
      testReport.testName = event.test.name
      testReport.testStatus = 'success'
      await reportToElasticsearch(testReport)
      break
    case 'test_skip':
      testReport.timeEnd = Date.now()
      testReport.suitName = event.test.parent.name
      testReport.testName = event.test.name
      testReport.testStatus = 'skip'
      await reportToElasticsearch(testReport)
      break
    case 'test_fn_failure':
      testReport.timeEnd = Date.now()
      testReport.suitName = event.test.parent.name
      testReport.testName = event.test.name
      testReport.testStatus = 'failure'
      await reportToElasticsearch(testReport)
      break
  }
}

const reportToElasticsearch = async test => {
  const config = safeLoad(
    fs.readFileSync(
      `${process.cwd()}/runner.config.yml`,
      'utf8'
    )
  )
  fs.writeFileSync('uuid.txt', runId)
  const env = process.env.ENVIRONMENT ? process.env.ENVIRONMENT : 'pre'
  await axios({
    method: 'POST',
    url: `http://${config.reporter.elkHost}/${config.reporter.elkIndex}/_doc/?pretty`,
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      runId,
      env,
      timestamp: new Date().toISOString(),
      timeStart: test.timeStart,
      timeEnd: test.timeEnd,
      suitName: test.suitName,
      testName: test.testName,
      testStatus: test.testStatus,
    },
  })
}