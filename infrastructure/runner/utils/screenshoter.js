const fs = require('fs');
const path = require('path');

module.exports = async (event, page) => {
  if (event.name === 'test_done' && event.test.errors.length > 0) {
    const testName = `${event.test.parent.name.replace(/\W/g, '-')}_${event.test.name.replace(/\W/g, '-')}`;

    fs.mkdirSync('report/screenshots', { recursive: true });
    await page.screenshot({
      path: path.resolve(`report/screenshots/${testName}.jpg`),
    });
  }
};