const fs = require('fs');
const { saveVideo } = require('playwright-video');

let video;
let testName;

module.exports = async (event, page) => {
  if (event.name === 'test_fn_start') {
    testName = `${event.test.parent.name.replace(/\W/g, '-')}_${event.test.name.replace(/\W/g, '-')}`;
    video = await saveVideo(page, `temp/video/${testName}.mp4`, { fps: 60 });
  }
  if (event.name === 'test_done' && event.test.errors.length > 0) {
    await video.stop();
    fs.mkdirSync('report/video', { recursive: true });
    fs.renameSync(`temp/video/${testName}.mp4`, `report/video/${testName}.mp4`);
  } else if (event.name === 'test_done') {
    await video.stop();
  }
};
