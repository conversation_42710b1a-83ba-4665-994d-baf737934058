const AWS = require('aws-sdk');
const axios = require('axios');
const fs = require('fs');
const AdmZip = require('adm-zip');
const runId = fs.readFileSync('uuid.txt');

const saveReportToS3 = async () => {
  const zip = new AdmZip();
  try {
    zip.addLocalFolder('report', 'report');
  } catch (err) {}
  zip.writeZip(`${runId}.zip`);
  upload(`${runId}.zip`);
};

async function upload(path) {
  const fileContent = fs.readFileSync(path);
  const credentials = await getCredentials();

  const s3 = new AWS.S3({
    accessKeyId: credentials.data.AccessKeyId,
    secretAccessKey: credentials.data.SecretAccessKey,
    sessionToken: credentials.data.Token,
  });
  s3.upload(
    {
      Bucket: 'mw-qa-reports',
      Key: `${path}`,
      Body: fileContent,
    },
    (err, data) => {
      if (err) {
        throw err;
      }
      data.Location = `https://mw-qa-reports.middleware.biz/${runId}.zip`;
      console.log(data);
    },
  );
}

async function getCredentials() {
  const resp = await axios({
    method: 'GET',
    url: `http://***************/latest/meta-data/iam/info`,
  });
  const role = resp.data.InstanceProfileArn;
  return await axios({
    method: 'GET',
    url: `http://***************/latest/meta-data/iam/security-credentials/${role.substring(
      role.lastIndexOf(''),
      role.lastIndexOf('/') + 1,
    )}`,
  });
}

saveReportToS3();
