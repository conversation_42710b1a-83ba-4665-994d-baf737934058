const fs = require('fs');

let consoleLogs = [];

module.exports = async (event, page) => {
  switch (event.name) {
    case 'setup':
      console.log('[BrowserConsole][Optional] Clear logs before new suite');
      consoleLogs = [];
      page.on('console', listener => {
        if (listener.type() === 'error' || listener.type() === 'warning') {
          consoleLogs.push({
            time: new Date().toISOString(),
            type: listener.type(),
            location: listener.location(),
            text: listener.text(),
            args: listener.args(),
          });
        }
      });
      break;
    case 'test_fn_success':
      console.log('[BrowserConsole][Options] Clear logs before new test');
      consoleLogs = [];
      break;
    case 'test_fn_failure':
      const parentName = event.test.parent.name.replace(/\W/g, '-');
      const specName = event.test.name.replace(/\W/g, '-');
      fs.mkdirSync('report/console-logs', { recursive: true });
      fs.writeFileSync(
        `report/console-logs/${parentName}_${specName}.txt`,
        JSON.stringify(consoleLogs, null, 2),
        err => {
          console.log(err);
        },
      );
      break;
  }
};
