const { readFileSync } = require('fs');
const { safeLoad } = require('js-yaml');

class RunnerConfiguration {
  constructor() {
    if (RunnerConfiguration._instance) {
      return RunnerConfiguration._instance;
    }
    RunnerConfiguration._instance = this;
  }

  getConfig() {
    try {
      return safeLoad(readFileSync(`${process.cwd()}/runner.config.yml`, 'utf8'));
    } catch (err) {
      console.log(err);
      throw new Error(`There are no such config file: runner.config.yml`);
    }
  }
}

module.exports = RunnerConfiguration;
