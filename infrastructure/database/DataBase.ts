import { MongoClient } from 'mongodb';
import { ConfigurationManager } from '../config/ConfigurationManager';
import { ApiKey } from '../model/ApiKey';
import { User } from '../model/User';

export class DataBase {
  public static async insertUser(user: User): Promise<void> {
    const mongoClient = new MongoClient('mongodb://**********:27017/', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    const appConfig = await ConfigurationManager.getConfiguration().getCorezoidConfig();
    const client = await mongoClient.connect().catch(err => {
      throw new Error(err.message);
    });

    const collection = client.db('corezoid').collection('users');
    const userDao = {
      env: appConfig.web_settings.host.site,
      email: user.email,
      password: user.password,
      superadmin: user.superadmin,
      auth: appConfig.auth_single_account ? 'sa' : 'basic',
      companies: user.companies,
    };

    await collection
      .insertOne(userDao)
      .then(() => {
        client.close();
      })
      .catch(err => {
        if (err) throw new Error(err.message);
        client.close();
      });
  }

  public static async insertApiKey(apiKey: ApiKey): Promise<void> {
    const mongoClient = new MongoClient('mongodb://**********:27017/', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    const appConfig = await ConfigurationManager.getConfiguration().getCorezoidConfig();
    const client = await mongoClient.connect().catch(err => {
      throw new Error(err.message);
    });

    const collection = client.db('corezoid').collection('apikeys');
    await collection
      .insertOne({
        env: appConfig.web_settings.host.site,
        ...apiKey,
      })
      .then(() => {
        client.close();
      })
      .catch(err => {
        if (err) throw new Error(err.message);
        client.close();
      });
  }

  public static async getUser(filter: {}): Promise<User> {
    for (const user of await this.getUsers(filter)) {
      if (await this.insertUserToPool(user)) return user;
    }
    throw new Error('[Get User] Error: there are no free user');
  }

  public static async getApiKey(filter: {}): Promise<ApiKey> {
    const mongoClient = new MongoClient('mongodb://**********:27017/', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    return new Promise(async resolve => {
      const client = await mongoClient.connect().catch(err => {
        throw new Error(err.message);
      });

      const collection = client.db('corezoid').collection('apikeys');
      const result = await collection.findOne(JSON.parse(JSON.stringify(filter))).catch(err => {
        throw new Error(err.message);
      });
      client.close();
      resolve(result);
    });
  }

  public static async getApiKeySuper(): Promise<ApiKey> {
    const mongoClient = new MongoClient('mongodb://**********:27017/', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    return new Promise(async resolve => {
      const client = await mongoClient.connect().catch(err => {
        throw new Error(err.message);
      });

      const collection = client.db('corezoid').collection('apikeys');
      const result = await collection.findOne(JSON.parse(JSON.stringify({ superadmin: true })));
      client.close();
      resolve(result);
    });
  }

  public static async reliseUser(user: User): Promise<void> {
    const mongoClient = new MongoClient('mongodb://**********:27017/', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    const client = await mongoClient.connect().catch(err => {
      throw new Error(err.message);
    });

    const collection = client.db('corezoid').collection('pool');
    await collection.deleteOne({ _id: user.id }).catch(err => {
      client.close();
      throw new Error(err.message);
    });
    client.close();
  }

  private static getUsers(filter: {}): Promise<User[]> {
    const mongoClient = new MongoClient('mongodb://**********:27017/', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    return new Promise(async resolve => {
      const users: User[] = [];
      const client = await mongoClient.connect().catch(err => {
        throw new Error(err.message);
      });

      const collection = client.db('corezoid').collection('users');
      const result = await collection
        .find(filter)
        .toArray()
        .catch(err => {
          throw new Error(err.message);
        });
      result.map(item =>
        users.push(
          new User(`${item.name}`, `${item.email}`, `${item.password}`, item.companies, item.superadmin, item._id),
        ),
      );
      client.close();
      resolve(users);
    });
  }

  private static async insertUserToPool(user: User): Promise<boolean> {
    const mongoClient = new MongoClient('mongodb://**********:27017/', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    const client = await mongoClient.connect().catch(err => {
      throw new Error(err.message);
    });

    const collection = client.db('corezoid').collection('pool');
    const result = await collection
      .insertOne({ _id: user.id, time: new Date() })
      .then(() => {
        return true;
      })
      .catch(() => {
        return false;
      });
    client.close();
    return result;
  }
}
