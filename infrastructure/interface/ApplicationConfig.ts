export interface ApplicationConfig {
  result: string;
  box_solution: boolean;
  web_settings: {
    env: string;
    ws_reconnect: boolean;
    host: {
      site: string;
      doc: string;
      market: string;
      ws: string;
      webhook: string;
      auth: string;
      single_account: string;
    };
    path: {
      api: string;
      upload: string;
      download: string;
      ws: string;
      webhook: string;
      auth: string;
      base_path: string;
    };
    whitelist: string[];
  };
  auth_single_account: boolean;
  first_day_of_week: string;
  multitenancy: boolean;
  components: string[];
}
