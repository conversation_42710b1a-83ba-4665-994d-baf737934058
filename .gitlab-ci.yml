stages:
  - pr_agent
  - build
  - test
  - loadtest-local
  - zap_scan
  - analyze-results

.default-vars: &default-vars
  API_URL: 'https://admin-pre.corezoid.com/'
  API_TEST: 'true'
  ENV: 'PRE'
  ACTIVE_CONFIG: 'true'
  FORCE_COLOR: '1'
  LOG_ENABLED: 'false'

.before-script: &before-script
  - bash print_env.sh
  - node --version && curl -o- -L https://yarnpkg.com/install.sh | bash
  - export PATH="$HOME/.yarn/bin:$HOME/.config/yarn/global/node_modules/.bin:$PATH"
  - rm -rf node_modules && yarn cache clean && yarn install
  - npx ts-node utils/uuid.ts && export UUID=$(cat uuid.txt) && export CI=true && mkdir -p report/junit

.test-template: &test-template
  stage: test
  image: mcr.microsoft.com/playwright:v1.35.1-jammy
  before_script: *before-script
  script:
    - yarn $SUITE
  after_script:
    - node infrastructure/runner/utils/awsClient.js
  artifacts:
    when: always
    paths:
      - report/html-report/CorezoidReport.html
      - report/junit/junit.xml
      - uuid.txt
    reports:
      junit: report/junit/junit.xml
    expire_in: 1 week
  tags:
    - gitlab-qa
  when: manual

test:Unstable test US:
  <<: *test-template
  variables:
    <<: *default-vars
    SUITE: 'test:unstable'

test:API Universal:
  <<: *test-template
  variables:
    <<: *default-vars
    SUITE: 'test:api:universal'

test:API V2 part1 - All:
  <<: *test-template
  variables:
    <<: *default-vars
    SUITE: 'test:api:v2:part1'

test:API V2 part1 - Stable:
  <<: *test-template
  variables:
    <<: *default-vars
    SUITE: 'test:api:v2:part1:Stable'

test:API V2 part1 - Unstable:
  <<: *test-template
  variables:
    <<: *default-vars
    SUITE: 'test:api:v2:part1:Unstable'

test:API V2 part2 - All:
  <<: *test-template
  variables:
    <<: *default-vars
    SUITE: 'test:api:v2:part2'

test:API V2 part2 - Stable:
  <<: *test-template
  variables:
    <<: *default-vars
    SUITE: 'test:api:v2:part2:Stable'

test:API V2 part2 - Unstable:
  <<: *test-template
  variables:
    <<: *default-vars
    SUITE: 'test:api:v2:part2:Unstable'

test:User story:
  <<: *test-template
  variables:
    <<: *default-vars
    SUITE: 'test:api:story'

test:Git-Call story:
  <<: *test-template
  variables:
    <<: *default-vars
    SUITE: 'test:api:git-call'

test:k8s-multitenant:
  <<: *test-template
  variables:
    PASS_6: 'Test12345!'
    API_TEST: 'true'
    ENV: 'K8S'
    API_SECRET_SUPER: 'u1CL9iKLmnR2PkRWFcR5TBY6cNTBBBba9n56YKRlp65Y74r4ep'
    API_KEY_SUPER: '8'
    COMPANY_ID: 'i000000000'
    OBJ_ID_KEY: '8'
    API_SECRET: 'BaxuwInfoMkt6JrNcj6NfM8Zvh05XvAUoHGExS1iJSngACH5XD'
    API_KEY: '7'
    API_SYNC_URL: 'https://syncapi-k8s-develop.dev-devops.corezoid.com/'
    API_URL: 'https://corezoid-k8s-develop.dev-devops.corezoid.com/'
    ACTIVE_CONFIG: 'true'
    SUITE: 'test:api:multitenant'

test:Suite with company:
  <<: *test-template
  variables:
    PASS: 'Corezoid111!'
    LOGIN: '<EMAIL>'
    COMPANY_ID: 'i434426147'
    API_SYNC_URL: 'https://syncapi-ae-7423.middleware.biz'
    API_URL: 'https://ae-7423.middleware.biz'
    API_TEST: 'true'
    ACTIVE_CONFIG: 'true'
    SUITE: 'test:api:universal'

test:prod-universal:
  <<: *test-template
  variables:
    API_TEST: 'true'
    ENV: 'PROD'
    SUITE: 'test:api:universal'
    API_SYNC_URL: 'https://sync-api.corezoid.com/'
    API_URL: 'https://admin.corezoid.com/'
    ACTIVE_CONFIG: 'true'
    COMPANY_ID: 'i480320652'
    API_SECRET: 'DTmirm92dRZ5frGjjdjHEujqaa3gthpI34gWkNy3c5o5kPTpmi'
    API_KEY: '118414'

pr_agent_job:
  stage: pr_agent
  image:
    name: codiumai/pr-agent:latest
    entrypoint: ['']
  script:
    - cd /app
    - export MR_URL="$CI_MERGE_REQUEST_PROJECT_URL/merge_requests/$CI_MERGE_REQUEST_IID"
    - export gitlab__url="$CI_SERVER_PROTOCOL://$CI_SERVER_FQDN"
    - export gitlab__PERSONAL_ACCESS_TOKEN="$GITLAB_PERSONAL_ACCESS_TOKEN"
    - export config__git_provider="gitlab"
    - export openai__key="$OPENAI_KEY"
    - python -m pr_agent.cli --pr_url="$MR_URL" describe
    - python -m pr_agent.cli --pr_url="$MR_URL" review
    - python -m pr_agent.cli --pr_url="$MR_URL" improve
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  tags:
    - gitlab

schedule-run:
  stage: test
  image: mcr.microsoft.com/playwright:v1.35.1-jammy
  variables:
    ENV: pre
    NETWORK_LISTENER: 'on'
    CONSOLE_LISTENER: 'on'
    VIDEO_RECORDER: 'on'
    SCREENSHOTER: 'on'
    SUITE: 'test:web:all'
    FORCE_COLOR: '1'
    LOG_ENABLED: 'false'
  before_script: *before-script
  script:
    - yarn $SUITE
  after_script:
    - node infrastructure/runner/utils/awsClient.js
  artifacts:
    when: always
    paths:
      - report/html-report/CorezoidReport.html
      - report/junit/junit.xml
      - uuid.txt
    reports:
      junit: report/junit/junit.xml
    expire_in: 1 week
  tags:
    - gitlab-qa
  only:
    variables:
      - $API_TEST == "true"

loadtest-local:
  image:
    name: grafana/k6:latest
    entrypoint: ['']
  stage: loadtest-local
  allow_failure: true
  variables:
    FORCE_COLOR: '1'
    LOG_ENABLED: 'false'
  script:
    - echo "executing local k6 in k6 container..."
    - mkdir -p report/K6/log
    - check_log_size.sh &
    - k6 run $SUITE_LOAD 2> >(tee report/K6/log/stderr.log >&2)
  artifacts:
    paths:
      - report/K6
  tags:
    - gitlab-qa
  only:
    variables:
      - $LOAD_TEST == "true"

zap_scan:
  image: ghcr.io/zaproxy/zaproxy:weekly
  stage: zap_scan
  variables:
    FORCE_COLOR: '1'
    LOG_ENABLED: 'false'
  script:
    - mkdir -p /zap/wrk/
    - zap-full-scan.py -t $API_URL -r /zap/wrk/zap_report.html
  artifacts:
    when: always
    paths:
      - zap_report.html
    expire_in: 1 week
  tags:
    - gitlab-qa
  only:
    variables:
      - $ZAP_SCAN == "true"

analyze-results:
  image:
    name: node:latest
  stage: analyze-results
  variables:
    FORCE_COLOR: '1'
    LOG_ENABLED: 'false'
  script:
    - echo "analyzing K6 results..."
    - node tests/K6/utils/parse_errors.js
  dependencies:
    - loadtest-local
  artifacts:
    paths:
      - report/K6
  tags:
    - gitlab-qa
  only:
    variables:
      - $LOAD_TEST == "true"
  when: always
