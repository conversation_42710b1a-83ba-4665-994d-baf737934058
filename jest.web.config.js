module.exports = {
    testRunner: 'jest-circus/runner',
    testEnvironment: './infrastructure/runner/PlaywrightEnvironment.js',
    setupFilesAfterEnv: ['./jest.setup.js', 'jest-extended'],
    reporters: [
      'default',
      [
        'jest-html-reporters',
        {
          publicPath: './report/html-report',
          filename: 'CorezoidReport.html',
          expand: false,
        },
      ],
    ],
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
};
