import { axiosInstance } from '../../application/api/AxiosClient';
import * as fs from 'fs';

let apiUrl: string;
let cookie: string;
let newFolder: string;
let process_node_ID: string | number;
let final_node_ID: string | number;
let error_node_ID: string | number;

interface TestResult {
  conv_id: string;
}

describe('Script', () => {
  apiUrl = `https://admin-perf.corezoid.com/api/2/json`;
  cookie =
    '_ga=GA1.2.865131701.1675149648; _hjSessionUser_2128548=eyJpZCI6IjkwYTNkOWZmLTQ2N2QtNThjMi1iZmNjLTQ1MzlmNzdjMmEwZiIsImNyZWF0ZWQiOjE3MTYyNzE1MzE0NzYsImV4aXN0aW5nIjp0cnVlfQ==; core_pre_UUID=X7IbmahCRZaLs1643AfxMZfWaocfoIHznJm/zet+nXwQ+BpWT0ISPAhy+YBrJ2hVRP4G4qDcEsESSsCxKTcvKw==; _gid=GA1.2.1670431692.1722845233; mw_avatar=https://lh3.googleusercontent.com/a/ACg8ocJLa7X3CE2s8k7oNZD4BndbG4xUd2MAvY7W7hsY9xskDA=s96-c; mw_UUID=ZHImx5zCRSqGOPVOB3av5PIV2JD8uW/KBlEAO5Z/4vSxc6W1AiEJofmIb1twpaelrQk/NbE67+fvjgKPTQg38w==; mw=3|MTcyMzE4NzYxM3xnMndBQUFBRFlnQUJRZTV0QUFBQUJIUmxjM1JrQUFSdWRXeHNhZz09fDE3MjI5MjYzODI4NDR8ZDJhYWQ0Nzg3N2Q4NTVjYTRkZWQyYTM4ZjZjOWFlODNiYzRkNjc5MQ==; __Host_mw=3|MTcyMzE4NzYxM3xnMndBQUFBRFlnQUJRZTV0QUFBQUJIUmxjM1JrQUFSdWRXeHNhZz09fDE3MjI5MjYzODI4NDR8ZDJhYWQ0Nzg3N2Q4NTVjYTRkZWQyYTM4ZjZjOWFlODNiYzRkNjc5MQ==; core_pre_avatar=https://lh3.googleusercontent.com/a/ACg8ocKMdDIdRPkfOeD5kr_E-NlqPgqpIkZDx8YoZCSNRBejNBs=s96-c; core_pre=3|MTcyMzAxNDkxN3xnMndBQUFBRFlnQUFGUVZ0QUFBQUc5Q3UwTHZSbHRHUElOQ2EwTDdRdk5DMTBZRFF1TkdCMFlMUXNHUUFCRzUxYkd4cXwxNzIyOTMxNzg1NDc3fGY2MjRjNDEyNGZiMzA1NjI5NDJlNTc4OTU0Y2Y2MDU3NzY4MjdiNzU=; __Host_core_pre=3|MTcyMzAxNDkxN3xnMndBQUFBRFlnQUFGUVZ0QUFBQUc5Q3UwTHZSbHRHUElOQ2EwTDdRdk5DMTBZRFF1TkdCMFlMUXNHUUFCRzUxYkd4cXwxNzIyOTMxNzg1NDc3fGY2MjRjNDEyNGZiMzA1NjI5NDJlNTc4OTU0Y2Y2MDU3NzY4MjdiNzU=; admin-perf_UUID=oZCaclrSRmSLHubAbSrqWZdieZxzBWw3TTmtchU5c1EGBPC/hTDOJOEaBzczlVGqbO/uyj82O4bZaaIhRruXrg==; admin-perf_avatar=https://admin-perf.corezoid.com/avatars/6.jpg; _gat_UA-100140652-2=1; _ga_YFF9BLS7LN=GS1.2.1722942054.1247.1.1722945919.60.0.0; admin-perf=3|MTcyMzc5MTkyMXxnMndBQUFBRFlRWnRBQUFBQm1wMWJHbDVZV1FBQkc1MWJHeHF8MTcyMjk0NDA4ODQxMHw3MDAzY2RkNGMwMDE4OGQxNDk0NWM4NGY1YmQ2NDAzZDY2YzlhMTM3; __Host_admin-perf=3|MTcyMzc5MTkyMXxnMndBQUFBRFlRWnRBQUFBQm1wMWJHbDVZV1FBQkc1MWJHeHF8MTcyMjk0NDA4ODQxMHw3MDAzY2RkNGMwMDE4OGQxNDk0NWM4NGY1YmQ2NDAzZDY2YzlhMTM3';
});
const jsonFilePath = 'tests/K6/data/dataConvCodeJS.json';
const testData = JSON.parse(fs.readFileSync(jsonFilePath, 'utf8'));

beforeAll(async () => {
  const responseCreateFolder = await axiosInstance({
    method: 'POST',
    url: apiUrl,
    headers: {
      origin: apiUrl,
      cookie,
    },
    data: {
      ops: [
        {
          title: 'testCodeFolder',
          folder_id: 4,
          obj: 'folder',
          type: 'create',
          company_id: null,
        },
      ],
    },
  });
  expect(responseCreateFolder.status).toBe(200);
  newFolder = responseCreateFolder.data.ops[0].obj_id;
});

const numberOfRuns = 20;
const results: TestResult[] = [];

for (let i = 0; i < numberOfRuns; i++) {
  test(`create conv - Run ${i + 1}`, async () => {
    const responseCreate = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            title: `CodeConv-${i}`,
            folder_id: newFolder,
            obj: 'conv',
            company_id: null,
            conv_type: 'process',
            type: 'create',
            obj_type: 0,
            status: 'active',
          },
        ],
      },
    });
    expect(responseCreate.status).toBe(200);

    const conv_id = responseCreate.data.ops[0].obj_id;

    const responseList = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            obj_id: conv_id,
            obj: 'conv',
            type: 'list',
          },
        ],
      },
    });
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.data.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.data.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateNode = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            conv_id,
            obj: 'node',
            type: 'create',
            title: 'error',
            obj_type: 2,
            version: 100,
          },
        ],
      },
    });
    expect(responseCreateNode.status).toBe(200);
    error_node_ID = responseCreateNode.data.ops[0].obj_id;

    const responseModifyNode = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            conv_id,
            obj: 'node',
            type: 'modify',
            obj_id: process_node_ID,
            obj_type: 0,
            title: 'processCode',
            logics: [
              {
                type: 'api_code',
                err_node_id: error_node_ID,
                lang: 'js',
                src: `${testData[i].code}`,
              },
              {
                to_node_id: final_node_ID,
                format: 'json',
                type: 'go',
                node_title: 'final',
              },
            ],
            semaphors: [],
            position: [0, 12],
            version: 100,
          },
        ],
      },
    });
    expect(responseModifyNode.status).toBe(200);

    const responseCompile = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            conv_id,
            node_id: process_node_ID,
            obj: 'api_code',
            type: 'compile',
            lang: 'js',
            src: `${testData[i].code}`,
          },
        ],
      },
    });
    expect(responseCompile.status).toBe(200);

    const responseLoad = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            conv_id,
            node_id: process_node_ID,
            obj: 'api_code',
            type: 'load',
            lang: 'js',
            src: 'data.a=123;',
            env: 'sandbox',
          },
        ],
      },
    });
    expect(responseLoad.status).toBe(200);

    const responseModify = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            conv_id,
            obj: 'node',
            type: 'modify',
            obj_id: final_node_ID,
            obj_type: 2,
            title: 'final',
            options: { save_task: false },
            logics: [],
            semaphors: [],
            position: [76, 224],
            version: 100,
          },
        ],
      },
    });
    expect(responseModify.status).toBe(200);

    const responseCommit = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            conv_id,
            version: 100,
            company_id: null,
            obj: 'commit',
            type: 'confirm',
          },
        ],
      },
    });
    expect(responseCommit.status).toBe(200);

    results.push({ conv_id });

    fs.writeFileSync('tests/K6/data/dataConvCode.json', JSON.stringify(results, null, 2));
  });
}
