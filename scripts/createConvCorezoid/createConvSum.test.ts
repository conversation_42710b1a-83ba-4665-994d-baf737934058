import { axiosInstance } from '../../application/api/AxiosClient';
import * as fs from 'fs';

let apiUrl: string;
let cookie: string;
let newFolder: string;
let process_node_ID: string | number;
let final_node_ID: string | number;
let error_node_ID: string | number;
let setP_node_ID: string | number;
let delay_node_ID: string | number;

interface TestResult {
  conv_id: string;
}

describe('Script', () => {
  apiUrl = `https://update.corezoid.com/api/2/json`;
  cookie =
    '_ga=GA1.2.**********.**********; _hjSessionUser_2128548=********************************************************************************************************************; _gid=GA1.2.**********.**********; core_pre_UUID=0hthaorSNfITB6bGpauFeZxqogXWCXgNmPMzQevm8TG3RsfeBNz6nRbMvXRUMzVOlzdg75ry7LxN7jvPRGbTIA==; mw_avatar=https://account.corezoid.com/avatars/66256.jpg; core_pre_avatar=https://lh3.googleusercontent.com/a/ACg8ocKMdDIdRPkfOeD5kr_E-NlqPgqpIkZDx8YoZCSNRBejNBs=s96-c; mw=3|********************************************************************************************************************************************************************; __Host_mw=3|********************************************************************************************************************************************************************; core_pre=3|************************************************************************************************************************************************************************************; __Host_core_pre=3|************************************************************************************************************************************************************************************; update-corezoid-com_avatar=https://account-update.corezoid.com/avatars/GSrnDp33j_ZYcod9VJPFK17HqRRusvk_Im_GoeVA; update-corezoid-com_UUID=WFDjRfsLTVoxP+EUgCNOslM01RGiRS+Y8RxJH0SLAfFCrXRJd9JcfbuGHbW3/zjswUKVs4PlwJ7G1k2RRvre9w==; _gat_UA-*********-2=1; _ga_YFF9BLS7LN=GS1.2.**********.427.1.**********.48.0.0; update-corezoid-com=3|****************************************************************************************************************************************; __Host_update-corezoid-com=3|****************************************************************************************************************************************';
});

beforeAll(async () => {
  const responseCreateFolder = await axiosInstance({
    method: 'POST',
    url: apiUrl,
    headers: {
      origin: apiUrl,
      cookie,
    },
    data: {
      ops: [
        {
          title: 'testSumFolder',
          folder_id: 14102,
          obj: 'folder',
          type: 'create',
          company_id: null,
        },
      ],
    },
  });
  expect(responseCreateFolder.status).toBe(200);
  newFolder = responseCreateFolder.data.ops[0].obj_id;
});

const numberOfRuns = 100;
const results: TestResult[] = [];

for (let i = 0; i < numberOfRuns; i++) {
  test(`create conv - Run ${i + 1}`, async () => {
    const responseCreate = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            title: `SumConv-${i}`,
            folder_id: newFolder,
            obj: 'conv',
            company_id: null,
            conv_type: 'process',
            type: 'create',
            obj_type: 0,
            status: 'active',
          },
        ],
      },
    });
    expect(responseCreate.status).toBe(200);

    const conv_id = responseCreate.data.ops[0].obj_id;

    const responseList = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            obj_id: conv_id,
            obj: 'conv',
            type: 'list',
          },
        ],
      },
    });
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.data.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.data.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseDelayNode = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            conv_id,
            obj: 'node',
            type: 'create',
            title: 'delay',
            obj_type: 0,
            version: 100,
          },
        ],
      },
    });
    expect(responseDelayNode.status).toBe(200);
    delay_node_ID = responseDelayNode.data.ops[0].obj_id;

    const responseSetPNode = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            conv_id,
            obj: 'node',
            type: 'create',
            title: 'setP',
            obj_type: 0,
            version: 100,
          },
        ],
      },
    });
    expect(responseSetPNode.status).toBe(200);
    setP_node_ID = responseSetPNode.data.ops[0].obj_id;

    const responseErrorNode = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            conv_id,
            obj: 'node',
            type: 'create',
            title: 'error',
            obj_type: 2,
            version: 100,
          },
        ],
      },
    });
    expect(responseErrorNode.status).toBe(200);
    error_node_ID = responseErrorNode.data.ops[0].obj_id;

    const responseModifyNodeSum = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            conv_id,
            obj: 'node',
            type: 'modify',
            obj_id: process_node_ID,
            obj_type: 0,
            title: 'processSum',
            logics: [
              {
                type: 'api_sum',
                extra: [{ id: '1735383036883', name: 'a', value: '2' }],
              },
              {
                to_node_id: delay_node_ID,
                format: 'json',
                type: 'go',
              },
            ],
            semaphors: [],
            position: [76, 72],
            version: 100,
          },
        ],
      },
    });
    expect(responseModifyNodeSum.status).toBe(200);

    const responseModifyDelay = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            conv_id,
            obj: 'node',
            type: 'modify',
            obj_id: delay_node_ID,
            obj_type: 0,
            title: 'delay',
            logics: [],
            semaphors: [{ type: 'time', value: 1, dimension: 'sec', to_node_id: setP_node_ID }],
            position: [76, 232],
            version: 100,
          },
        ],
      },
    });
    expect(responseModifyDelay.status).toBe(200);

    const responseModifyNodeSetParameter = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            conv_id,
            obj: 'node',
            type: 'modify',
            obj_id: setP_node_ID,
            obj_type: 0,
            title: 'setP',
            logics: [
              {
                type: 'set_param',
                extra: { sum: `{{node[${process_node_ID}].1735383036883}}` },
                extra_type: { sum: 'string' },
                err_node_id: error_node_ID,
              },
              { to_node_id: final_node_ID, type: 'go', node_title: 'final' },
            ],
            semaphors: [],
            position: [76, 452],
            version: 100,
          },
        ],
      },
    });
    expect(responseModifyNodeSetParameter.status).toBe(200);

    const responseModifyError = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            conv_id,
            obj: 'node',
            type: 'modify',
            obj_id: final_node_ID,
            obj_type: 2,
            title: 'final',
            logics: [],
            semaphors: [],
            position: [176, 664],
            version: 100,
          },
          {
            conv_id,
            obj: 'node',
            type: 'modify',
            obj_id: error_node_ID,
            obj_type: 2,
            title: 'error',
            logics: [],
            semaphors: [],
            position: [372, 544],
            version: 100,
          },
        ],
      },
    });
    expect(responseModifyError.status).toBe(200);

    const responseCommit = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            conv_id,
            version: 100,
            company_id: null,
            obj: 'commit',
            type: 'confirm',
          },
        ],
      },
    });
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.data.ops[0].proc).toBe('ok');

    results.push({ conv_id });

    fs.writeFileSync('tests/K6/data/dataConvTest.json', JSON.stringify(results, null, 2));
  });
}
