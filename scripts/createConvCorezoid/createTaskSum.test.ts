import { debug } from '../../support/utils/logger';
jest.setTimeout(7200000);
import { axiosInstance } from '../../application/api/AxiosClient';
import * as fs from 'fs';
import * as path from 'path';

let GMT_UNIXTIME: string;
const apiUrl = `https://update.corezoid.com/api/2/json`;
const cookie =
  '_ga=GA1.2.**********.**********; _hjSessionUser_2128548=********************************************************************************************************************; _gid=GA1.2.**********.**********; core_pre_UUID=0hthaorSNfITB6bGpauFeZxqogXWCXgNmPMzQevm8TG3RsfeBNz6nRbMvXRUMzVOlzdg75ry7LxN7jvPRGbTIA==; mw_avatar=https://account.corezoid.com/avatars/66256.jpg; mw=3|********************************************************************************************************************************************************************; __Host_mw=3|********************************************************************************************************************************************************************; update-corezoid-com_avatar=https://account-update.corezoid.com/avatars/GSrnDp33j_ZYcod9VJPFK17HqRRusvk_Im_GoeVA; update-corezoid-com_UUID=WFDjRfsLTVoxP+EUgCNOslM01RGiRS+Y8RxJH0SLAfFCrXRJd9JcfbuGHbW3/zjswUKVs4PlwJ7G1k2RRvre9w==; core_pre_avatar=https://lh3.googleusercontent.com/a/ACg8ocKMdDIdRPkfOeD5kr_E-NlqPgqpIkZDx8YoZCSNRBejNBs=s96-c; core_pre=3|************************************************************************************************************************************************************************************; __Host_core_pre=3|************************************************************************************************************************************************************************************; _gat_UA-*********-2=1; _ga_YFF9BLS7LN=GS1.2.**********.427.1.**********.60.0.0; update-corezoid-com=3|****************************************************************************************************************************************; __Host_update-corezoid-com=3|****************************************************************************************************************************************';

interface TestResult {
  conv_id: number;
}

const jsonFilePath = path.join(__dirname, '../../tests/K6/data/dataConvTest.json');
const testData = JSON.parse(fs.readFileSync(jsonFilePath, 'utf-8'));

const numberOfCycles = 1000;

const results: TestResult[] = [];

describe('Create Task for Each Conv_ID', () => {
  beforeAll(async () => {
    GMT_UNIXTIME = Math.floor(Date.now() / 1000).toString();
  });

  for (let cycle = 1; cycle <= numberOfCycles; cycle++) {
    test(`Cycle ${cycle}: Process each conv_id`, async () => {
      jest.setTimeout(7200000);
      debug(`🌀 Начало цикла ${cycle}`);

      for (let i = 0; i < testData.length; i++) {
        const convID = testData[i].conv_id;
        debug(`📤 Отправка запроса для conv_id: ${convID}`);

        const responseCreate = await axiosInstance({
          method: 'POST',
          url: apiUrl,
          headers: {
            origin: apiUrl,
            cookie,
          },
          data: {
            ops: [
              {
                conv_id: convID,
                type: 'create',
                obj: 'task',
                action: 'user',
                ref: `${GMT_UNIXTIME}`,
                data: { a: 1 },
              },
            ],
          },
        });

        expect(responseCreate.status).toBe(200);
        debug(`✅ Успешно создана задача для conv_id: ${convID}`);

        results.push({ conv_id: convID });
      }

      debug(`🛑 Цикл ${cycle} завершён.`);
    });
  }
});
