import { axiosInstance } from '../../application/api/AxiosClient';
import * as fs from 'fs';

let apiUrl: string;
let cookie: string;

interface TestResult {
  hash: string;
  conv_id: string;
}

describe('Script', () => {
  apiUrl = `https://corezoid-k8s.dev-devops.corezoid.com/api/2/json`;
  cookie =
    'corezoid-k8s.dev-devops.corezoid.com_UUID=HVK3a4fHTHG6FeMpl5kfYiYnKhp0bkaggzhsmOb6DvtgtYN1Obz1KUW9DqMtlYDMBOeoTJBrGIX/W3r8b8VZkQ==; corezoid-k8s-develop.dev-devops.corezoid.com_avatar=https://corezoid-k8s-develop.dev-devops.corezoid.com/avatars/34.jpg; corezoid-k8s-develop.dev-devops.corezoid.com=3|****************************************************************************************************************************************; __Host_corezoid-k8s-develop.dev-devops.corezoid.com=3|****************************************************************************************************************************************; _ga=GA1.2.**********.**********; _gid=GA1.2.**********.**********; single_account_avatar=https://account.corezoid.com/avatars/Zs9AZVJMDoOa7tN353ngCDuTgQujrOUuOBTR3c4W; single_account=3|********************************************************************************************************************************************************; mw_avatar=https://account.corezoid.com/avatars/Zs9AZVJMDoOa7tN353ngCDuTgQujrOUuOBTR3c4W; mw_UUID=O8OLIvKcgttU5xkyO+IDIBW+VatBv92Wsd7xA8iCZ3WtXVSK1mZLtyhOhkQ3RoJ1/+It9DEVUu86W3oIW0/yMw==; mw=3|************************************************************************************************************************************************************************; __Host_mw=3|************************************************************************************************************************************************************************; _ga_YFF9BLS7LN=GS1.2.**********.5.1.**********.60.0.0; _gat_UA-*********-2=1; corezoid-k8s.dev-devops.corezoid.com=3|****************************************************************************************************************************************; __Host_corezoid-k8s.dev-devops.corezoid.com=3|****************************************************************************************************************************************';
});

const numberOfRuns = 20;
const results: TestResult[] = [];

for (let i = 0; i < numberOfRuns; i++) {
  test(`create conv - Run ${i + 1}`, async () => {
    const title = `Conv_${i + 1}`;

    const responseCreate = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            title,
            folder_id: 3,
            company_id: 'i000000000',
            obj: 'conv',
            conv_type: 'process',
            type: 'create',
            obj_type: 0,
            status: 'active',
          },
        ],
      },
    });
    expect(responseCreate.status).toBe(200);

    const hash = responseCreate.data.ops[0].hash;
    const conv_id = responseCreate.data.ops[0].obj_id;

    const responseCommit = await axiosInstance({
      method: 'POST',
      url: apiUrl,
      headers: {
        origin: apiUrl,
        cookie,
      },
      data: {
        ops: [
          {
            conv_id,
            version: 22,
            company_id: null,
            obj: 'commit',
            type: 'confirm',
          },
        ],
      },
    });
    expect(responseCommit.status).toBe(200);

    results.push({ conv_id, hash });

    fs.writeFileSync('tests/Load/dataConv.json', JSON.stringify(results, null, 2));
  });
}
