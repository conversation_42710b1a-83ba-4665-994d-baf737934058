/**
 * <PERSON><PERSON>t to replace direct console.log, console.error, console.warn, and console.info calls
 * with the appropriate logger functions from support/utils/logger.ts
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const isDryRun = process.argv.includes('--dry-run');
const mode = isDryRun ? 'Dry run' : 'Applying changes';
const verbose = process.argv.includes('--verbose');
const singleFile = process.argv.includes('--file') ? 
  process.argv[process.argv.indexOf('--file') + 1] : null;

const excludeFiles = [
  './support/utils/logger.ts', // Don't modify the logger itself
  './scripts/replace-console-logs.js', // Don't modify this script
];

const k6LoggerTemplate = `
const isLoggingEnabled = () => process.env.LOG_ENABLED === 'true';

const debug = (message, ...args) => {
  if (isLoggingEnabled()) {
    console.log(\`\${new Date().toISOString()} [DEBUG]: \${message}\`, ...args);
  }
};

const info = (message, ...args) => {
  if (isLoggingEnabled()) {
    console.log(\`\${new Date().toISOString()} [INFO]: \${message}\`, ...args);
  }
};

const warn = (message, ...args) => {
  if (isLoggingEnabled()) {
    console.log(\`\${new Date().toISOString()} [WARN]: \${message}\`, ...args);
  }
};

const error = (message, ...args) => {
  if (isLoggingEnabled()) {
    console.log(\`\${new Date().toISOString()} [ERROR]: \${message}\`, ...args);
  }
};
`;

console.log(`${mode} mode: Starting replacement of console.log calls with logger functions`);

const getFilesWithConsoleCalls = () => {
  try {
    if (singleFile) {
      console.log(`Processing single file: ${singleFile}`);
      return [singleFile];
    }
    
    console.log('Finding files with console calls...');
    const output = execSync(
      'grep -l "console\\.log\\|console\\.error\\|console\\.warn\\|console\\.info" --include="*.ts" --include="*.js" --exclude-dir="node_modules" --exclude-dir="dist" --exclude-dir="build" -r .'
    ).toString();
    const files = output.split('\n').filter(Boolean);
    console.log(`Found ${files.length} files with console calls`);
    return files;
  } catch (error) {
    console.error('Error finding files with console calls:', error);
    return [];
  }
};

const processFileWithTimeout = (filePath, timeoutMs = 15000) => {
  return new Promise((resolve) => {
    const timeout = setTimeout(() => {
      console.error(`Timeout processing file: ${filePath}`);
      resolve({ success: false, timedOut: true });
    }, timeoutMs);
    
    try {
      if (excludeFiles.includes(filePath)) {
        console.log(`Skipping excluded file: ${filePath}`);
        clearTimeout(timeout);
        resolve({ success: false, skipped: true });
        return;
      }

      console.log(`Processing file: ${filePath}`);
      
      let content = fs.readFileSync(filePath, 'utf8');
      const originalContent = content;

      const isK6File = filePath.includes('/tests/K6/');
      if (isK6File) {
        console.log(`  K6 file detected: ${filePath}`);
      }

      const hasLoggerImport = /import.*from.*['"].*\/support\/utils\/logger['"]/.test(content);
      if (verbose) console.log(`  Has logger import: ${hasLoggerImport}`);
      
      content = content.replace(/console\.log\(/g, 'debug(');
      
      content = content.replace(/console\.error\(/g, 'error(');
      
      content = content.replace(/console\.warn\(/g, 'warn(');
      
      content = content.replace(/console\.info\(/g, 'info(');

      if (content !== originalContent) {
        if (isK6File) {
          console.log(`  Adding K6 logger template to ${filePath}`);
          
          if (!content.includes('const isLoggingEnabled')) {
            content = k6LoggerTemplate + '\n' + content;
          }
        } 
        else if (!hasLoggerImport) {
          console.log(`  Adding logger import to ${filePath}`);
          
          const depth = filePath.split('/').filter(Boolean).length - 1;
          let importPath = '';
          
          if (depth <= 1) {
            importPath = './support/utils/logger';
          } else {
            importPath = '../'.repeat(depth - 1) + 'support/utils/logger';
          }
          
          console.log(`  Using import path: ${importPath} for file at depth ${depth}`);
          
          const usesDebug = content.includes('debug(');
          const usesInfo = content.includes('info(');
          const usesWarn = content.includes('warn(');
          const usesError = content.includes('error(');
          
          const loggerFunctions = [];
          if (usesDebug) loggerFunctions.push('debug');
          if (usesInfo) loggerFunctions.push('info');
          if (usesWarn) loggerFunctions.push('warn');
          if (usesError) loggerFunctions.push('error');
          
          console.log(`  Using logger functions: ${loggerFunctions.join(', ')}`);
          
          const importStatement = `import { ${loggerFunctions.join(', ')} } from '${importPath}';\n`;
          content = importStatement + content;
        }
      }

      if (content !== originalContent) {
        if (!isDryRun) {
          fs.writeFileSync(filePath, content, 'utf8');
        }
        clearTimeout(timeout);
        resolve({ success: true, modified: true });
      } else {
        clearTimeout(timeout);
        resolve({ success: true, modified: false });
      }
    } catch (error) {
      console.error(`Error processing file ${filePath}:`, error);
      clearTimeout(timeout);
      resolve({ success: false, error: error.message });
    }
  });
};

const main = async () => {
  const maxFiles = process.argv.includes('--max-files') ? 
    parseInt(process.argv[process.argv.indexOf('--max-files') + 1]) : 
    Number.MAX_SAFE_INTEGER;
  
  const startIndex = process.argv.includes('--start-index') ? 
    parseInt(process.argv[process.argv.indexOf('--start-index') + 1]) : 
    0;
  
  const batchSize = process.argv.includes('--batch-size') ? 
    parseInt(process.argv[process.argv.indexOf('--batch-size') + 1]) : 
    5;
  
  const files = getFilesWithConsoleCalls();
  
  if (files.length === 0) {
    console.log('No files with console calls found');
    return;
  }
  
  console.log(`Found ${files.length} files with console calls to check`);
  console.log(`Starting at index ${startIndex} with batch size ${batchSize}`);
  console.log(`Processing up to ${maxFiles < files.length ? maxFiles : 'all'} files`);
  
  let modifiedCount = 0;
  let skippedCount = 0;
  let errorCount = 0;
  let timeoutCount = 0;
  
  const endIndex = Math.min(startIndex + batchSize, files.length, startIndex + maxFiles);
  const filesToProcess = files.slice(startIndex, endIndex);
  
  console.log(`Processing files ${startIndex + 1} to ${endIndex} of ${files.length}`);
  
  for (let i = 0; i < filesToProcess.length; i++) {
    const file = filesToProcess[i];
    const globalIndex = startIndex + i;
    
    console.log(`Processing file ${globalIndex + 1}/${files.length}: ${file}`);
    
    const result = await processFileWithTimeout(file);
    
    if (result.success) {
      if (result.modified) {
        console.log(`${isDryRun ? 'Would modify' : 'Modified'}: ${file}`);
        modifiedCount++;
      } else {
        console.log(`No changes needed for: ${file}`);
        skippedCount++;
      }
    } else if (result.timedOut) {
      console.error(`Timed out processing: ${file}`);
      timeoutCount++;
    } else if (result.skipped) {
      console.log(`Skipped excluded file: ${file}`);
      skippedCount++;
    } else {
      console.error(`Error processing: ${file} - ${result.error}`);
      errorCount++;
    }
  }
  
  console.log(`
Batch Summary (${startIndex + 1}-${endIndex} of ${files.length}):
  ${isDryRun ? 'Would modify' : 'Modified'}: ${modifiedCount} files
  Skipped: ${skippedCount} files
  Errors: ${errorCount} files
  Timeouts: ${timeoutCount} files
  
${isDryRun ? 'Run without --dry-run to apply changes' : 'All changes have been applied'}

Next batch command (if needed):
node scripts/replace-console-logs.js --start-index ${endIndex} --batch-size ${batchSize}
  `);
};

main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
