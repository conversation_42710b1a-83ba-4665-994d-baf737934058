# Instructions for Adding Return Types to Functions

## Task Overview

Your task is to add explicit return types to all functions in the TypeScript files of the project to comply with the `@typescript-eslint/explicit-function-return-type` rule.

## Steps to Complete

1. **Identify Files Requiring Changes**
   - Run the command `npx eslint --ext .ts . --max-warnings=0` to identify all files with missing return types
   - Note that there are many files that need to be fixed, primarily in the `tests/` directory

2. **Add Return Types to Functions**
   - For each function without an explicit return type, analyze the function and add the appropriate return type
   - Follow the TypeScript best practices for typing functions

3. **Types to Use**
   - For functions that don't return anything, use `void`
   - For async functions, use `Promise<T>` where T is the appropriate type
   - For functions returning multiple types, use union types (e.g., `string | number`)
   - Use specific types instead of `any` whenever possible
   - For complex return types, consider creating interfaces or type aliases

4. **Examples of Fixes**

   **Example 1: Void Function**
   ```typescript
   // Before
   function logMessage(message: string) {
     console.log(message);
   }
   
   // After
   function logMessage(message: string): void {
     console.log(message);
   }
   ```

   **Example 2: Function Returning a Value**
   ```typescript
   // Before
   function getUser(id: number) {
     return { id, name: 'User' };
   }
   
   // After
   function getUser(id: number): { id: number; name: string } {
     return { id, name: 'User' };
   }
   ```

   **Example 3: Async Function**
   ```typescript
   // Before
   async function fetchData(url: string) {
     const response = await fetch(url);
     return response.json();
   }
   
   // After
   async function fetchData(url: string): Promise<any> {
     const response = await fetch(url);
     return response.json();
   }
   ```

   **Example 4: Arrow Function**
   ```typescript
   // Before
   const multiply = (a: number, b: number) => a * b;
   
   // After
   const multiply = (a: number, b: number): number => a * b;
   ```

5. **Verification**
   - After making changes to each file, run ESLint on that file to ensure all warnings are fixed
   - Once all files are fixed, run `npx eslint --ext .ts . --max-warnings=0` to verify there are no remaining warnings

6. **Important Notes**
   - Do not change the functionality of the code
   - Only add return types to functions
   - Be careful with callback functions and higher-order functions
   - Pay attention to conditional returns that might have different types

## Common Patterns in the Codebase

Based on the ESLint output, you'll need to fix functions in:

1. API utility files like `websocketUtils.ts`
2. Jest configuration files (`jest.global-setup.ts`, `jest.global-teardown.ts`)
3. Logger utilities (`support/utils/logger.ts`)
4. Many test files in the `tests/` directory

## Testing Your Changes

After making changes, ensure that:
1. The code compiles without errors
2. ESLint runs without warnings
3. All tests continue to pass

## Completion Criteria

Your task is complete when:
1. All functions have explicit return types
2. Running `npx eslint --ext .ts . --max-warnings=0` produces no warnings
3. The functionality of the code remains unchanged
