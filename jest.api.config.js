module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  setupFilesAfterEnv: ['./jest.setup.js', 'jest-extended'],
  reporters: [
    'default',
    [
      'jest-junit',
      {
        outputDirectory: './report/junit',
        outputName: 'junit.xml',
        classNameTemplate: '{classname}',
        titleTemplate: '{title}',
        ancestorSeparator: ' › ',
        usePathForSuiteName: 'true',
      },
    ],
    [
      'jest-html-reporters',
      {
        publicPath: './report/html-report',
        filename: 'CorezoidReport.html',
        expand: false,
        openReport: true,
        darkTheme: true,
      },
    ],
    ['./support/utils/custom-reporter.js', {}],
  ],
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
};
