---
- rule:
    - key: test1
      rule: '=='
      value: text
      type: string
  validTask:
    key: test1
    value: text
    type: string 
  invalidTask:
    key: test1
    value: textInvalid
    type: string
  validResult:
    test1: text
  invalidResult:
    test1: textInvalid
- rule:
    - key: test2
      rule: '>'
      value: '1'
      type: number
  validTask:
    key: test2
    value: '2'
    type: number
  invalidTask:
    key: test2
    value: '1'
    type: number
  validResult:
    test2: 2
  invalidResult:
    test2: 1
- rule:
    - key: test2
      rule: '<'
      value: '1'
      type: number
  validTask:
    key: test2
    value: '0'
    type: number
  invalidTask:
    key: test2
    value: '1'
    type: number
  validResult:
    test2: 0
  invalidResult:
    test2: 1
- rule:
    - key: test3
      rule: '=='
      value: 'true'
      type: boolean
  validTask:
    key: test3
    value: 'true'
    type: boolean
  invalidTask:
    key: test3
    value: 'false'
    type: boolean
  validResult:
    test3: true
  invalidResult:
    test3: false
- rule:
    - key: test4
      rule: '=='
      value: '{"a":"1"}'
      type: object
  validTask:
    key: test4
    value: '{"a":"1"}'
    type: object
  invalidTask:
    key: test4
    value: '{"b":"2"}'
    type: object
  validResult:
    test4:
      a: '1'
  invalidResult:
    test4:
      b: '2'
- rule:
    - key: test5
      rule: '=='
      value: '[{"b":"1"}]'
      type: array
  validTask:
    key: test5
    value: '[{"b":"1"}]'
    type: array
  invalidTask:
    key: test5
    value: '[{"a":"1"}]'
    type: array
  validResult:
    test5:
      - b: '1'
  invalidResult:
    test5:
      - a: '1'
- rule:
    - key: test6
      rule: RE
      value: '[0-3][0-9].(0|1)[0-9].(19|20)[0-9]{2}'
      type: string
  validTask:
    key: test6
    value: 17.08.2020
    type: string
  invalidTask:
    key: test6
    value: '17082020'
    type: string
  validResult:
    test6: 17.08.2020
  invalidResult:
    test6: '17082020'
- rule:
    - key: test7
      rule: '!='
      value: text
      type: string
  validTask:
    key: test7
    value: 17.08.2020
    type: string
  invalidTask:
    key: test7
    value: text
    type: string
  validResult:
    test7: 17.08.2020
  invalidResult:
    test7: text
