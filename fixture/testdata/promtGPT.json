{"system": {"role": "system", "content": "You are an IT professional working for a software development company."}, "generateCode": {"JavaScript": "Write JS code for a Code node in Corezoid to retrieve a recursive value by key from objects in an array. Here is an example of the syntax: \n data.key_dashboard = Object.keys(data.source)[0]\n;const sourceObject = data.source[data.key_dashboard];\nsourceObject.accounts.forEach(account => {\naccount.currencyId = data.currencyId;\n});\ndata.source = sourceObject;\ndata.source = JSON.stringify(data.source);\n", "Erlang": "Write Erlang code for a Code node in Corezoid to retrieve a recursive value by key from objects in an array. Here is an example of the syntax: \n data.key_dashboard = Object.keys(data.source)[0]\n;const sourceObject = data.source[data.key_dashboard];\nsourceObject.accounts.forEach(account => {\naccount.currencyId = data.currencyId;\n});\ndata.source = sourceObject;\ndata.source = JSON.stringify(data.source);\n"}, "codeRating": {"ratingExplain": "Here is the original code:  \n${generateCode}\n  Here is the generated explain:  \n${resultsExplain}\n  Rate the accuracy and quality of the explain on a scale from 1 to 10. Provide only the rating in the format: {{rating}}", "ratingCompletion": "Here is the original code:  \n${generateCode}\n  Here is the generated completion:  \n${resultsExplain}\n  Rate the accuracy and quality of the completion on a scale from 1 to 10. Provide only the rating in the format: {{rating}}"}}