{"deleteUser": {"company_id": null, "group_id": 0, "obj_id": 1227, "type": "delete", "obj": "user", "level": ""}, "createInvite": {"company_id": null, "login": "<EMAIL>", "login_type": "google", "type": "create", "obj": "invite"}, "createApiKey": {"company_id": null, "logins": [{"type": "api"}], "title": "Test", "type": "create", "obj": "user"}, "createGroup": {"company_id": null, "title": "Test", "obj_type": "admins", "type": "create", "obj": "group"}, "deleteGroup": {"company_id": null, "obj_id": 111, "type": "delete", "obj": "group"}, "modifyGroup": {"company_id": null, "obj_id": 111, "title": "Test", "type": "modify", "obj": "group", "obj_type": "admins", "status": "actived"}, "linkUserToGroup": {"company_id": null, "group_id": 10, "obj_id": 111, "level": 1, "type": "link", "obj": "user"}}