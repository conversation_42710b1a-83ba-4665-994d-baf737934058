---
codeEditor:
  js:
    module.exports = (data) => {return new Promise(resolve => {data.hello = 'Hello
    world!';resolve(data);});};
  golang: |-
    package main
     import (
    "context"
    "github.com/corezoid/gitcall-go-runner/gitcall")
    func usercode(_ context.Context, data map[string]interface{}) error {data["hello"] = "Hello world!"
    return nil}
    func main() {gitcall.Handle(usercode)}
  php: <?php function handle($data) { $data['hello'] = "Hello world!"; return $data;}
  result: '"hello": "Hello world!"'
rebuildCodeEditor:
  js:
    module.exports = (data) => {return new Promise(resolve => {data.hello = 'Hello
    rebuild!';resolve(data);});};
  golang: |-
    package main
     import (
    "context"
    "github.com/corezoid/gitcall-go-runner/gitcall")
    func usercode(_ context.Context, data map[string]interface{}) error {data["hello"] = "Hello rebuild!"
    return nil}
    func main() {gitcall.Handle(usercode)}
  php: <?php function handle($data) { $data['hello'] = "Hello rebuild!"; return $data;}
  result: '"hello": "Hello rebuild!"'
gitRepository:
  js:
    repo: https://github.com/GrigurkoAlexey/git-call-test-repo.git
    commit: 7f2db3751607731b0700daca15fc8841cdfb7a1e
    buildcmd: build-js.sh
  golang:
    repo: https://github.com/GrigurkoAlexey/git-call-test-repo.git
    commit: 7f2db3751607731b0700daca15fc8841cdfb7a1e
    buildcmd: build-go.sh
  php:
    repo: https://github.com/GrigurkoAlexey/git-call-test-repo.git
    commit: 7f2db3751607731b0700daca15fc8841cdfb7a1e
    buildcmd: build-php.sh
  result: '"hello": "Hello world!"'
rebuildGitRepository:
  js:
    repo: https://github.com/GrigurkoAlexey/git-call-test-repo.git
    commit: ba4683d892b48180c5d2b89aef7a1fad79d6d9b4
    buildcmd: build-js.sh
  golang:
    repo: https://github.com/GrigurkoAlexey/git-call-test-repo.git
    commit: ba4683d892b48180c5d2b89aef7a1fad79d6d9b4
    buildcmd: build-go.sh
  php:
    repo: https://github.com/GrigurkoAlexey/git-call-test-repo.git
    commit: ba4683d892b48180c5d2b89aef7a1fad79d6d9b4
    buildcmd: build-php.sh
  result: '"hello": "Hello rebuild!"'
