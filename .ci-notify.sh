#!/bin/bash

while read UUI<PERSON>; do export UUID=$UUID; done < uuid.txt

TIME="10"
URL="https://api.telegram.org/bot2022623153:AAFMmczgHAyXq1hY0TjIcG28kLBd6r5R3ic/sendMessage"
TEXT="Test run status:%0A%0APass: $PASS; Fail: $FAILED; Pending: $PENDING%0A%0AProject: $CI_PROJECT_NAME%0AURL: $CI_PROJECT_URL/pipelines/$CI_PIPELINE_ID/%0AENV: $API_URL%0ARunner email: $GITLAB_USER_EMAIL%0ASuite: $SUITE%0AReport: https://mw-qa-reports.middleware.biz/$UUID.zip"

curl -s --max-time $TIME -d "chat_id=-584920612&disable_web_page_preview=1&text=$TEXT" $URL > /dev/null
