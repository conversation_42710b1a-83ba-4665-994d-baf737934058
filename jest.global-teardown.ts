import { application } from './application/Application';
import axios from 'axios';
import { ConfigurationManager } from './infrastructure/config/ConfigurationManager';
import { error, info } from './support/utils/logger';

module.exports = async (): Promise<void> => {
  try {
    const user = await application.getAuthorizedUser({ company: {} }, 0);

    const cookie = user.cookieUser;
    const company_id = user.companies[0].id;
    const config = ConfigurationManager.getConfiguration();
    const host = config.getApiUrl();

    const responseDeleteKey = await axios({
      method: 'POST',
      url: `${host}api/2/json`,
      data: { ops: [{ type: 'delete', obj: 'user', company_id, obj_id: process.env.OBJ_ID_KEY }] },
      headers: {
        Cookie: cookie,
        Origin: host,
      },
    });

    if (responseDeleteKey.status !== 200) {
      throw new Error('Ошибка при удалении API ключа');
    }
  } catch (err) {
    error('Ошибка в globalSetup:', err);
  }
  info('Delete Api key');
};
