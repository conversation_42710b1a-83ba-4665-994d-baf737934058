.PHONY: docker-build
## docker-build: builds the docker image to registry
docker-build:
	docker build --pull -t "e2e-tests:build" .

.PHONY: docker-push
## docker-push: pushes the docker image to registry
docker-push:
	docker tag "e2e-tests:build" $(TAG)
	docker push $(TAG)

.PHONY: help
## help: prints this help message
help:
	@echo "Usage: \n"
	@sed -n 's/^##//p' ${MAKEFILE_LIST} | column -t -s ':' |  sed -e 's/^/ /'

.DEFAULT_GOAL := help
