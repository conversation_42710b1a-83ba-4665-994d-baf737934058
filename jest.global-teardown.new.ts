import { application } from './application/Application';
import { error, info } from './support/utils/logger';
import { ConfigurationManager } from './infrastructure/config/ConfigurationManager';
import axios from 'axios';

/**
 * Глобальная очистка для тестов
 * Запускается один раз после всех тестов
 * Удаляет ресурсы, созданные в глобальной настройке
 * @returns {Promise<void>}
 */
module.exports = async (): Promise<void> => {
  try {
    info('Начало глобальной очистки тестов');

    // Получаем авторизованного пользователя
    const user_super = await application.getAuthorizedUser({}, 1);
    const cookie = user_super.cookieUser;

    // Получаем конфигурацию
    const config = ConfigurationManager.getConfiguration();
    const hostSS = config.getSSUrl();

    const company_id = process.env.COMPANY_ID;

    // Удаляем компанию, созданную в глобальной настройке
    if (company_id) {
      info(`Удаление компании: ${process.env.COMPANY_NAME} (ID: ${company_id})`);
      info(`Cookie СА аккаунт: ${cookie}`);

      try {
        const deleteCompany = await axios({
          method: 'DELETE',
          url: `${hostSS}face/api/1/workspaces/${company_id}`,
          headers: {
            cookie,
          },
        });
        if (deleteCompany.status !== 200) {
          error('Ошибка при удалении компании:', deleteCompany.status);
          error('Ответ:', deleteCompany.data);
        } else {
          info('Компания успешно удалена');
        }
      } catch (deleteErr) {
        error('Исключение при удалении компании:', deleteErr);
      }
    }

    info('Глобальная очистка тестов завершена успешно');
  } catch (err) {
    error('Ошибка в globalTeardown:', err);
    // Не прерываем выполнение тестов при ошибке очистки, чтобы не скрыть результаты тестов
  }
};
