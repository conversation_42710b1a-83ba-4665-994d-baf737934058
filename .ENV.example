# Дефолтный файл с конфигами infrastructure/config/ConfigurationManager.ts


API_URL=https://test-corezoid62-azure.corezoid.com/

API_SYNC_URL=https://syncapi.test-corezoid62-azure.corezoid.com/

SUPERADMIN_URL=https://superadmin-pre.corezoid.com/

API_URL_GW=https://apigw-pre.eks.corezoid.com/api/

JWT=Simulator eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM4NCIsIm5hbWUiOiJUZXN0IFVzZXIiLCJpYXQiOjE2ODU5NTMyNTIsImV4cCI6MTgxMjE4MzY1Mn0.SOJx6R3v6ULcnQY-qrveMQNJDIE3xj7928vgi1cvkcY

API_KEY=12

API_SECRET=IC6hHJ9NF44boyJ2VK8FjBrqcCndCa9UsHcqt23nsb8gqxUSTa

API_KEY_SUPER=11553

API_SECRET_SUPER=YTicXbjieqGRlScazX6K9xPflP6yungIOK4tLJNM3ZzG27bPGD

OBJ_ID_KEY=13

COMPANY_ID=i892600491

# отключаем проверку сертификата, для тестов образа маркета, по дефолту true(включена проверка)  
CERT=true 

# если параметр установлен true то мы берем апи ключ который указан в .ENV или ConfigurationManager, если false то получаем ключ с БД
ACTIVE_CONFIG=true



