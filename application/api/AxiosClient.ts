import axios from 'axios';
import { ConfigurationManager } from '../../infrastructure/config/ConfigurationManager';
import { addMsg } from 'jest-html-reporters/helper';
import https from 'https';
import * as URL from 'url';

let cert: boolean;

const rejectUnauthorizedValue = ConfigurationManager.getConfiguration().rejectUnauthorized();
if (rejectUnauthorizedValue === 'true') {
  cert = true;
} else {
  cert = false;
}

export const axiosInstance = axios.create({
  timeout: 61000,
  httpsAgent: new https.Agent({
    rejectUnauthorized: cert,
  }),
  validateStatus: function validateStatus(status): boolean {
    return status >= 200 && status < 600;
  },
});

axiosInstance.interceptors.request.use(function(config): Promise<any> {
  return new Promise((resolve): void => {
    const delay = 10;

    setTimeout((): void => {
      if (config.method) {
        addMsg({ message: 'Method:' + JSON.stringify(config.method, null, 2), context: '' });
      }
      if (config.url) {
        addMsg({ message: 'URL:' + JSON.stringify(config.url, null, 2), context: '' });
      }
      if (config.data) {
        addMsg({ message: 'REQ:' + JSON.stringify(config.data, null, 2), context: '' });
      }
      if (config.headers) {
        addMsg({ message: 'REQ headers:' + JSON.stringify(config.headers, null, 2), context: '' });
      }
      if (config.headers['X-Request-Id']) {
        addMsg({ message: 'Request-Id:' + JSON.stringify(config.headers['X-Request-Id']), context: '' });
      }
      if (config.params) {
        addMsg({ message: 'PAR:' + JSON.stringify(config.params, null, 2), context: '' });
      }

      const url = URL.parse(config.url as string);
      if (url.hostname && url.hostname.includes('.localhost')) {
        config.headers = config.headers || {};
        config.headers.Host = url.hostname;
        url.hostname = '127.0.0.1';
        url.host = null;
        config.url = URL.format(url);
      }

      resolve(config);
    }, delay);
  });
});

axiosInstance.interceptors.response.use(
  function(response): any {
    const data = response.data;
    addMsg({ message: 'Status: ' + JSON.stringify(response.status, null, 2), context: '' });
    addMsg({ message: 'RES:' + JSON.stringify(data, null, 2), context: '' });
    addMsg({ message: 'RES headers:' + JSON.stringify(response.headers, null, 2), context: '' });
    return response;
  },
  function(error): Promise<never> {
    return Promise.reject(error);
  },
);
