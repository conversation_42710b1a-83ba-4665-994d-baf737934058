import { UsersGroupsApi } from './usergroup/UsersGroupsApi';
import { ConfigurationManager } from '../../infrastructure/config/ConfigurationManager';
import { DBCallApi } from './db-call/DBCallApi';
import { User } from '../../infrastructure/model/User';
import { CompnayApi } from './company/CompanyApi';
import { ApiClient } from './ApiClient';
import { axiosInstance } from '../../application/api/AxiosClient';
import { RESP_STATUS, Corezoidrequest } from '../../utils/corezoidRequest';
import { application } from '../Application';

export class ApiUserClient implements ApiClient {
  private config = ConfigurationManager.getConfiguration();
  public userGroupApi: UsersGroupsApi;
  public dbCallApi: DBCallApi;
  public companyApi: CompnayApi;

  constructor(protected user: User) {
    this.userGroupApi = new UsersGroupsApi(this);
    this.dbCallApi = new DBCallApi(this);
    this.companyApi = new CompnayApi(this);
  }

  async request({ body, path }: Corezoidrequest): Promise<ApiClientResponse> {
    const baseUrl = this.config.getApiUrl();
    const uri = `${baseUrl}${path}`;
    const cookies = this.user.apiCookies.map(item => `${item.name}=${item.value}`).join(';');

    try {
      const response = await axiosInstance({
        method: 'POST',
        url: uri,
        data: body,
        headers: {
          referer: baseUrl,
          cookie: cookies,
        },
      });
      return { status: response.status, headers: response.headers, body: response.data };
    } catch (err) {
      throw err;
    }
  }
  async requestWithToken({ body, path }: Corezoidrequest): Promise<ApiClientResponse> {
    const baseUrl = this.config.getApiUrl();
    const uri = `${baseUrl}${path}`;
    const token = await application.createToken(0);

    try {
      const response = await axiosInstance({
        method: 'POST',
        url: uri,
        data: body,
        headers: {
          Authorization: token,
          Origin: ConfigurationManager.getConfiguration().getApiUrl(),
        },
      });
      return { status: response.status, headers: response.headers, body: response.data };
    } catch (err) {
      throw err;
    }
  }
}

export interface CorezoidConfig {
  result: string;
  box_solution: boolean;
  web_settings: {
    env: string;
    ws_reconnect: boolean;
    host: {
      site: string;
      doc: string;
      market: string;
      ws: string;
      webhook: string;
      auth: string;
      single_account: string;
    };
    path: {
      api: string;
      upload: string;
      download: string;
      ws: string;
      webhook: string;
      auth: string;
      base_path: string;
    };
    whitelist: string[];
  };
  auth_single_account: boolean;
  first_day_of_week: string;
  multitenancy: boolean;
  components: string[];
}

export interface ApiClientResponse {
  status: RESP_STATUS;
  headers: any;
  body: any;
}
