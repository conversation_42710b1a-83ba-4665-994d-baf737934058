import { ApiClient } from './ApiClient';
import { ApiClientResponse } from './ApiUserClient';
import CryptoJ<PERSON> from 'crypto-js';
import { ConfigurationManager } from '../../infrastructure/config/ConfigurationManager';
import { UserController } from './usergroup/controllers/user.controller';
import { GroupController } from './usergroup/controllers/group.controller';
import { ApiKey } from '../../infrastructure/model/ApiKey';
import { addMsg } from 'jest-html-reporters/helper';
import { axiosInstance } from '../../application/api/AxiosClient';
import { Corezoidrequest } from '../../utils/corezoidRequest';
import { error, debug } from '../../support/utils/logger';

export class ApiKeyClient implements ApiClient {
  userController: UserController;
  groupController: GroupController;

  constructor(private apikey: ApiKey) {
    this.userController = new UserController(this);
    this.groupController = new GroupController(this);
  }

  async request({ body, path }: Corezoidrequest): Promise<ApiClientResponse> {
    const { time, hex } = this.generateSignature(body);
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}${path}/${this.apikey.key}/${time}/${hex}`;
    try {
      const response = await axiosInstance({
        method: 'POST',
        url: uri,
        data: body,
      });
      return { status: response.status, headers: response.headers, body: response.data };
    } catch (err) {
      if (err instanceof Error) {
        error(err.message);
      }
      const axiosError = err as any;
      if (axiosError.response?.data) {
        debug(JSON.stringify(axiosError.response.data, null, 2));
        await addMsg({
          message: JSON.stringify(axiosError.response.data, null, 2),
          context: 'API Request Error',
        });
      }
      throw err;
    }
  }

  async requestSuperadmin(path: string, body: any, headers?: any): Promise<ApiClientResponse> {
    const { time, hex } = this.generateSignature(body);
    const uri = `${ConfigurationManager.getConfiguration().getSuperadminUrl()}${path}/${
      this.apikey.key
    }/${time}/${hex}`;
    try {
      const response = await axiosInstance({
        method: 'POST',
        url: uri,
        data: body,
        headers,
      });
      return { status: response.status, headers: response.headers, body: response.data };
    } catch (err) {
      if (err instanceof Error) {
        error(err.message);
      }
      const axiosError = err as any;
      if (axiosError.response?.data) {
        debug(JSON.stringify(axiosError.response.data, null, 2));
        await addMsg({
          message: JSON.stringify(axiosError.response.data, null, 2),
          context: 'API Request Error',
        });
      }
      throw err;
    }
  }

  private generateSignature(data: any): { time: number; hex: string } {
    const time = Math.floor(Date.now() / 1000);
    const secret = this.apikey.secret;
    return {
      time,
      hex: CryptoJS.enc.Hex.stringify(CryptoJS.SHA1(time + secret + JSON.stringify(data) + secret)),
    };
  }

  async requestSyncApi({ body }: Corezoidrequest): Promise<ApiClientResponse> {
    const { time, hex } = this.generateSignature(body);
    const uri = `${ConfigurationManager.getConfiguration().getApiSyncUrl()}${'api/1/json'}/${
      this.apikey.key
    }/${time}/${hex}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: body,
    });
    return { status: response.status, headers: response.headers, body: response.data };
  }

  async requestGET(path: string, param?: string): Promise<ApiClientResponse> {
    const { time, hex } = this.generateHEXGET();
    const uri = `${path}/${this.apikey.key}/${time}/${hex}?${param}`;
    try {
      const response = await axiosInstance({
        method: 'GET',
        url: uri,
      });
      await addMsg({ message: JSON.stringify(response.data, null, 2), context: 'API Request Error' });
      return { status: response.status, headers: response.headers, body: response.data };
    } catch (err) {
      if (err instanceof Error) {
        error(err.message);
      }
      const axiosError = err as any;
      if (axiosError.response?.data) {
        debug(JSON.stringify(axiosError.response.data, null, 2));
      }
      throw err;
    }
  }

  private generateHEXGET(): { time: number; hex: string } {
    const time = Math.floor(Date.now() / 1000);
    const secret = this.apikey.secret;
    return {
      time,
      hex: CryptoJS.enc.Hex.stringify(CryptoJS.SHA1(time + secret + secret)),
    };
  }
}
