import { OBJ_TYPE, PROC_STATUS, REQUEST_TYPE } from '../../../utils/corezoidRequest';

export enum ALIAS_STATUS {
  Active = 'active',
  Deleted = 'deleted',
  Favorite = 'favorite',
}

export enum ALIAS_TYPE {
  Simple = 'simple',
  Linked = 'linked',
}

export enum ALIAS_LINK_STATUS {
  Active = 'active',
  Inactive = 'inactive',
}

export enum ALIAS_LINK_TYPE {
  Process = 'process',
  StateDiagram = 'state_diagram',
  Dashboard = 'dashboard',
}

interface AliasRequest {
  obj: OBJ_TYPE.ALIAS;
  project_id: number;
  stage_id: number;
  company_id?: string;
}

interface CreateAliasRequest extends AliasRequest {
  type: REQUEST_TYPE.CREATE;
  title: string;
  short_name: string;
  value: string;
  alias_type: ALIAS_TYPE;
  status: ALIAS_STATUS;
}

interface ModifyAliasRequest extends AliasRequest {
  type: REQUEST_TYPE.MODIFY;
  alias_id: number;
  title?: string;
  short_name?: string;
  value?: string;
  status?: ALIAS_STATUS;
}

interface ShowAliasRequest extends AliasRequest {
  type: REQUEST_TYPE.SHOW;
  alias_id: number;
}

interface DeleteAliasRequest extends AliasRequest {
  type: REQUEST_TYPE.DELETE;
  alias_id: number;
}

interface FavoriteAliasRequest extends AliasRequest {
  type: REQUEST_TYPE.FAVORITE;
  alias_id: number;
  favorite: boolean;
}

interface LinkAliasRequest extends AliasRequest {
  type: REQUEST_TYPE.LINK;
  alias_id: number;
  obj_to_id: number;
  obj_to_type: ALIAS_LINK_TYPE;
}

interface UpsertAliasRequest extends AliasRequest {
  type: REQUEST_TYPE.UPSERT;
  alias_id?: number;
  title: string;
  short_name: string;
  description?: string;
  status?: ALIAS_STATUS;
}

interface DestroyAliasRequest extends AliasRequest {
  type: REQUEST_TYPE.DESTROY;
  alias_id: number;
}

interface ApiResponse<T> {
  request_proc: string;
  ops: T[];
}

interface AliasResponse {
  obj_id: number;
  alias_id: number;
  title: string;
  short_name: string;
  value: string;
  alias_type: ALIAS_TYPE;
  status: ALIAS_STATUS;
  project_id: number;
  stage_id: number;
  company_id?: string;
  create_time: number;
  change_time: number;
  uuid: string;
}

interface LinkAliasRespons {
  proc: PROC_STATUS;
  alias_id: number;
  obj_to_id: number;
  obj_to_type: ALIAS_LINK_TYPE;
  status: ALIAS_LINK_STATUS;
  create_time: number;
  change_time: number;
}

export type CreateAliasResponse = ApiResponse<AliasResponse>;
export type ModifyAliasResponse = ApiResponse<AliasResponse>;
export type ShowAliasResponse = ApiResponse<AliasResponse>;
export type DeleteAliasResponse = ApiResponse<{ obj_id: number; proc: string }>;
export type FavoriteAliasResponse = ApiResponse<{ obj_id: number; proc: string }>;
export type LinkAliasResponse = ApiResponse<LinkAliasRespons>;
export type UpsertAliasResponse = ApiResponse<AliasResponse>;
export type DestroyAliasResponse = ApiResponse<{ obj_id: number; proc: string }>;
