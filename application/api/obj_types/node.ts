import { OBJ_TYPE, PROC_STATUS, REQUEST_TYPE } from '../../../utils/corezoidRequest';

export enum NODE_TYPE {
  Normal = 0,
  Start = 1,
  Final = 2,
  Escalation = 3,
}

export enum NODE_LOGIC_TYPE {
  Go = 'go',
  GoIfConst = 'go_if_const',
  SetParam = 'set_param',
  Api = 'api',
  ApiCallback = 'api_callback',
  ApiSum = 'api_sum',
  ApiCode = 'api_code',
  ApiCopy = 'api_copy',
  ApiRpc = 'api_rpc',
  ApiRpcReply = 'api_rpc_reply',
  ApiQueue = 'api_queue',
  ApiGetTask = 'api_get_task',
  ApiForm = 'api_form',
  GitCall = 'git_call',
  DbCall = 'db_call',
}

interface ApiResponse<T> {
  request_proc: PROC_STATUS;
  ops: T[];
}

interface CreateNodeRequest {
  type: REQUEST_TYPE.CREATE;
  obj: OBJ_TYPE.NODE;
  conv_id: number | string;
  title: string;
  description?: string;
  obj_type: NODE_TYPE;
  version?: number;
}

export interface ModifyNodeRequest {
  type: REQUEST_TYPE.MODIFY;
  obj: OBJ_TYPE.NODE;
  obj_id: string;
  conv_id: number | string;
  title?: string;
  description?: string;
  logics?: Logic[];
  version?: number;
}

interface DeleteNodeRequest {
  type: REQUEST_TYPE.DELETE;
  obj: OBJ_TYPE.NODE;
  obj_id: string;
  conv_id: number | string;
}

interface ListNodeRequest {
  type: REQUEST_TYPE.LIST;
  obj: OBJ_TYPE.NODE;
  conv_id: number | string;
  limit?: number;
  offset?: number;
}

interface NodeResponse {
  id: string;
  title: string;
  description?: string;
  obj_type: NODE_TYPE;
  conv_id: number | string;
  version?: number;
  proc: string;
}

export type CreateNodeResponse = ApiResponse<NodeResponse>;
export type ModifyNodeResponse = ApiResponse<NodeResponse>;

export type DeleteNodeResponse = ApiResponse<DeleteNodeOps>;

interface DeleteNodeOps {
  id?: string;
  proc: PROC_STATUS;
  obj: OBJ_TYPE.NODE;
  obj_id: string;
}

export type ListNodeResponse = ApiResponse<{ id?: string; proc: PROC_STATUS; list: NodeResponse[] }>;

interface Logic {
  type: NODE_LOGIC_TYPE;
  conditions?: Condition[];
  to_node_id?: string;
}

interface Condition {
  param: string;
  const: string;
  fun: 'more' | 'less' | 'eq' | 'not_eq' | 'regexp';
  cast: 'string' | 'number' | 'boolean' | 'array' | 'object';
}

interface Node {
  id: string;
  title: string;
  description?: string;
  obj_type: NODE_TYPE;
  conv_id: number | string;
  version?: number;
}
