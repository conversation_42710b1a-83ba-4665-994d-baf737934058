import { OBJ_TYPE, PROC_STATUS } from '../../../utils/corezoidRequest';

export enum DIFF_STATUS {
  Added = 'added',
  Changed = 'changed',
  Deleted = 'deleted',
}

interface CompareRequest {
  obj: string;
  obj_id: number | string;
  obj_type: OBJ_TYPE;
  obj_to_id: number | string;
  obj_to_type: OBJ_TYPE;
  company_id: string;
  diff_status?: boolean;
  num_stat?: boolean;
}

export interface CompareResponse {
  request_proc: PROC_STATUS;
  ops: CompareItem[];
}

interface CompareItem {
  parent_id: number;
  obj_id: number;
  obj: string;
  proc: PROC_STATUS;
  list: CompareItem[];
  title: string;
  obj_type: string;
  __status?: DIFF_STATUS;
  __added?: object;
  __changed?: object;
  __deleted?: object;
  __num_stat?: {
    added: number;
    changed: number;
    deleted: number;
  };
}

interface MergeRequest {
  obj: string;
  obj_id: number | string;
  obj_type: OBJ_TYPE;
  obj_to_id: number | string;
  obj_to_type: OBJ_TYPE;
  company_id: string;
  apply_mode?: boolean;
  async?: boolean;
}

interface MergeRequestBody {
  ops: MergeRequest[];
}

export interface MergeResponse {
  request_proc: PROC_STATUS;
  ops: MergeItem[];
}

interface MergeItem {
  parent_id: number;
  obj_id: number;
  obj: string;
  title: string;
  proc: PROC_STATUS;
  errors: any;
  diff: { obj_id: number | string };
  obj_type: string;
  __status?: DIFF_STATUS;
  __added?: object;
  __changed?: object;
  __deleted?: object;
  __num_stat?: {
    added: number;
    changed: number;
    deleted: number;
  };
}

interface DeleteMergeResponse {
  obj_id: string;
  proc: string;
}

interface UpsertMergeResponse {
  obj_id: string;
  proc: string;
}
