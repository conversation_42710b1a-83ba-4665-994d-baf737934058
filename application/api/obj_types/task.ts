import { OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';

export enum TASK_STATUS {
  Created = 'created',
  Processing = 'processing',
  Delayed = 'delayed',
  Processed = 'processed',
  Canceled = 'canceled',
  Deleted = 'deleted',
}

export enum TASK_PRIORITY {
  Low = 'low',
  Medium = 'medium',
  High = 'high',
}

export enum REQUEST_TYPE_DEBUG {
  STEP_NEXT = 'debug_step_next',
  STEP_PREV = 'debug_step_prev',
  STEP_GOTO = 'debug_step_goto',
}
interface TaskRequest {
  obj: OBJ_TYPE.TASK;
  company_id: string;
  conv_id: string;
  obj_id?: string;
}

interface CreateTaskRequest extends TaskRequest {
  type: REQUEST_TYPE.CREATE;
  data: object;
  ref: string;
}

interface ModifyTaskRequest extends TaskRequest {
  type: REQUEST_TYPE.MODIFY;
  task_id: string;
  data: object;
}

interface ShowTaskRequest extends TaskRequest {
  type: REQUEST_TYPE.SHOW;
  task_id: string;
}

interface DeleteTaskRequest extends TaskRequest {
  type: REQUEST_TYPE.DELETE;
  task_id: string;
}

interface DebugStepNextTaskRequest extends TaskRequest {
  type: REQUEST_TYPE_DEBUG.STEP_NEXT;
  obj_id: string;
  branch: 'timer' | 'logic';
  data?: object;
}

interface DebugStepPrevTaskRequest extends TaskRequest {
  type: REQUEST_TYPE_DEBUG.STEP_PREV;
  obj_id: string;
  branch: 'timer' | 'logic';
  data?: object;
}

interface DebugStepGotoTaskRequest extends TaskRequest {
  type: REQUEST_TYPE_DEBUG.STEP_GOTO;
  obj_id: string;
  branch: 'timer' | 'logic';
  data?: object;
}

interface TaskResponse {
  obj: number;
  obj_id: string;
  task_id: string;
  status: TASK_STATUS;
  priority: TASK_PRIORITY;
  create_time: number;
  change_time: number;
  ref: string;
  data: object;
  proc: string;
}

interface DebugStepResponse {
  obj_id: string;
  task_id: string;
  status: TASK_STATUS;
  branch: 'timer' | 'logic';
  data: object;
}

export type CreateTaskResponse = ApiResponse<TaskResponse>;
export type ModifyTaskResponse = ApiResponse<TaskResponse>;
export type ShowTaskResponse = ApiResponse<TaskResponse>;
export type DeleteTaskResponse = ApiResponse<{ obj_id: string; proc: string }>;
export type DebugStepNextTaskResponse = ApiResponse<DebugStepResponse>;
export type DebugStepPrevTaskResponse = ApiResponse<DebugStepResponse>;
export type DebugStepGotoTaskResponse = ApiResponse<DebugStepResponse>;

interface ApiResponse<T> {
  request_proc: string;
  ops: T[];
}
