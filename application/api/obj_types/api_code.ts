import { REQUEST_TYPE, OBJ_TYPE, PROC_STATUS } from '../../../utils/corezoidRequest';

export enum LANG {
  JS = 'js',
  ERLANG = 'erl',
  PYTHON = 'python',
  JAVA = 'java',
}

enum ENV_API_CODE {
  PRODUCTION = 'production',
  SANDBOX = 'sandbox',
}

export enum METHOD_SHOW {
  EXPLAIN = 'explain',
  COMPLETION = 'completion',
}

interface BaseApiCodeRequest {
  obj: OBJ_TYPE.API_CODE;
  conv_id: number;
  node_id: string;
}

interface CompileApiCodeRequest extends BaseApiCodeRequest {
  type: REQUEST_TYPE.COMPILE;
  lang: LANG;
  src: string;
}

interface GetApiCodeRequest extends BaseApiCodeRequest {
  type: REQUEST_TYPE.GET;
  env: ENV_API_CODE;
}

interface LoadApiCodeRequest extends BaseApiCodeRequest {
  type: REQUEST_TYPE.LOAD;
  lang: LANG;
  src: string;
  env: ENV_API_CODE;
}

interface ApiResponse<T> {
  request_proc: PROC_STATUS;
  ops: T[];
}

interface ApiCodeResponse {
  id?: string;
  proc: PROC_STATUS;
  obj: OBJ_TYPE.API_CODE;
  lang?: LANG;
  src?: string;
  method: METHOD_SHOW;
  result: string;
  description?: string;
}

export type CompileApiCodeResponse = ApiResponse<ApiCodeResponse>;
export type GetApiCodeResponse = ApiResponse<ApiCodeResponse>;
export type LoadApiCodeResponse = ApiResponse<ApiCodeResponse>;
export type ShowApiCodeResponse = ApiResponse<ApiCodeResponse>;

interface ApiCode {
  id: string;
  conv_id: number;
  node_id: string;
  lang: LANG;
  method: METHOD_SHOW;
  src: string;
  env: ENV_API_CODE;
  description?: string;
}
