import { REQUEST_TYPE, OBJ_TYPE, PROC_STATUS } from '../../../utils/corezoidRequest';

export enum ENV_VAR_TYPE {
  Secret = 'secret',
  Visible = 'visible',
}

export enum ENV_VAR_DATA_TYPE {
  JSON = 'json',
  RAW = 'raw',
}

export enum SCOPE_TYPE {
  ApiCall = 'api_call',
  SetParam = 'set_param',
}

type Fingerprint = {
  algo: 'md5' | 'sha256';
  value: string;
};

type SCOPE = {
  type: SCOPE_TYPE;
  fields: string[];
};

interface ApiResponse<T> {
  request_proc: PROC_STATUS;
  ops: T[];
}

interface CreateEnvVarRequest {
  type: REQUEST_TYPE.CREATE;
  obj: OBJ_TYPE.ENV_VAR;
  env_var_type: ENV_VAR_TYPE;
  title: string;
  short_name: string;
  value: string;
  data_type: ENV_VAR_DATA_TYPE;
  company_id?: string;
  project_id: number;
  stage_id: number;
  scopes: SCOPE[];
}

interface ModifyEnvVarRequest {
  type: REQUEST_TYPE.MODIFY;
  obj: OBJ_TYPE.ENV_VAR;
  obj_id: number;
  title?: string;
  short_name?: string;
  env_var_type?: ENV_VAR_TYPE;
  scopes?: SCOPE[];
  project_id: number;
  stage_id: number;
}

interface DeleteEnvVarRequest {
  type: REQUEST_TYPE.DELETE;
  obj: OBJ_TYPE.ENV_VAR;
  obj_id: number;
  project_id: number;
  stage_id: number;
}

interface ListEnvVarRequest {
  type: REQUEST_TYPE.LIST;
  obj: OBJ_TYPE.ENV_VAR;
  company_id?: string;
  project_id: number;
  stage_id: number;
}

interface EnvVarResponse {
  obj_id: number;
  obj: OBJ_TYPE.ENV_VAR;
  env_var_type: ENV_VAR_TYPE;
  data_type: ENV_VAR_DATA_TYPE;
  title: string;
  short_name: string;
  description?: string;
  company_id?: string;
  project_id: number;
  stage_id: number;
  create_time: number;
  change_time: number;
  uuid: string;
  scopes: SCOPE[];
  value?: string;
  fingerprints: Fingerprint[];
}

export type CreateEnvVarResponse = ApiResponse<EnvVarResponse>;
export type ModifyEnvVarResponse = ApiResponse<EnvVarResponse>;

export interface DeleteEnvVarOps {
  id?: string;
  proc: PROC_STATUS;
  obj: OBJ_TYPE.ENV_VAR;
  obj_id: number;
}
export type DeleteEnvVarResponse = ApiResponse<DeleteEnvVarOps>;

export type ListEnvVarResponse = ApiResponse<{ id?: string; proc: PROC_STATUS; list: EnvVarResponse[] }>;
