import { ConfigurationManager } from '../../infrastructure/config/ConfigurationManager';
import { v4 as uuidv4 } from 'uuid';
import { axiosInstance } from '../../application/api/AxiosClient';

export async function request(
  method: any,
  path: string,
  body?: any,
  params?: any,
  userId = 0,
): Promise<ApiClientResponse> {
  const config = ConfigurationManager.getConfiguration();
  const baseUrl = config.getApiGWUrl();
  const authorization = config.getJwtApi(userId);
  const uri = `${baseUrl}${path}`;

  try {
    const headers = {
      referer: baseUrl,
      Authorization: authorization,
      'X-Request-Id': uuidv4(),
    };
    const response = await axiosInstance({
      method: method,
      url: uri,
      data: body,
      params: params,
      headers: headers,
    });
    return { status: response.status, headers: response.headers, body: response.data };
  } catch (err) {
    throw err;
  }
}

export async function requestUser1(method: any, path: string, body?: any, params?: any): Promise<ApiClientResponse> {
  return request(method, path, body, params, 0);
}

export async function requestUser2(method: any, path: string, body?: any, params?: any): Promise<ApiClientResponse> {
  return request(method, path, body, params, 1);
}

export interface ApiClientResponse {
  status: number;
  headers: any;
  body: any;
}

export interface CorezoidConfig {
  result: string;
  box_solution: boolean;
  web_settings: {
    env: string;
    ws_reconnect: boolean;
    host: {
      site: string;
      doc: string;
      market: string;
      ws: string;
      webhook: string;
      auth: string;
      single_account: string;
    };
    path: {
      api: string;
      upload: string;
      download: string;
      ws: string;
      webhook: string;
      auth: string;
      base_path: string;
    };
    whitelist: string[];
  };
  auth_single_account: boolean;
  first_day_of_week: string;
  multitenancy: boolean;
  components: string[];
}
