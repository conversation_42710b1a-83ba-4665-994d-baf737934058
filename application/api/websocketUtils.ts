import WebSocket from 'ws';
import { debug, error, info } from '../../support/utils/logger';

export class WebSocketClient {
  private client: WebSocket;
  private url: string;
  private path: string;
  private cookie: string;
  private origin: string;
  private reconnectInterval: number;
  private reconnectTimeout?: NodeJS.Timeout;
  private isClosed = false;
  private pendingMessage?: any;
  private resolvePendingMessage?: () => void;
  private rejectPendingMessage?: (err: Error) => void;

  constructor(url: string, path: string, cookie: string, origin: string, reconnectInterval = 5000) {
    this.url = url;
    this.path = path;
    this.cookie = cookie;
    this.origin = origin;
    this.reconnectInterval = reconnectInterval;
    this.client = this.createWebSocket();
  }

  private createWebSocket(): WebSocket {
    const ws = new WebSocket(`${this.url}${this.path}`, {
      headers: {
        Cookie: this.cookie,
        Origin: this.origin,
      },
    });

    ws.on('open', (): void => {
      info('WebSocket client connected');
      if (this.reconnectTimeout) {
        clearTimeout(this.reconnectTimeout);
      }

      if (this.pendingMessage) {
        debug('Resending pending message after reconnect:', this.pendingMessage);
        this.resendPendingMessage();
      }
    });

    ws.on('error', (err): void => {
      error('WebSocket client error:', err);
      this.handleError(err);
    });

    ws.on('close', (): void => {
      if (!this.isClosed) {
        info('WebSocket client connection closed, attempting to reconnect...');
        this.reconnect();
      } else {
        info('WebSocket connection closed by client');
      }
    });

    ws.on('ping', (): void => {
      debug('Received ping, sending pong...');
      ws.pong();
    });

    return ws;
  }

  private handleError(err: Error): void {
    if (this.rejectPendingMessage) {
      this.rejectPendingMessage(err);
      this.clearPendingMessage();
    }
  }

  private reconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }

    this.reconnectTimeout = setTimeout(() => {
      if (!this.isClosed) {
        info('Attempting to reconnect WebSocket...');
        this.client = this.createWebSocket();
      }
    }, this.reconnectInterval);
  }

  private resendPendingMessage(): void {
    if (this.pendingMessage) {
      const message = this.pendingMessage;
      const resolve = this.resolvePendingMessage;
      const reject = this.rejectPendingMessage;

      this.clearPendingMessage();

      this.sendMessage(message)
        .then(resolve)
        .catch(reject);
    }
  }

  private clearPendingMessage(): void {
    this.pendingMessage = undefined;
    this.resolvePendingMessage = undefined;
    this.rejectPendingMessage = undefined;
  }

  public async connect(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.client.on('open', () => resolve());
      this.client.on('error', reject);
    });
  }

  public async sendMessage(message: any): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      let messageHandled = false;

      const handleMessage = (data: WebSocket.RawData): void => {
        const messageString = data.toString();
        let parsedData;

        try {
          parsedData = JSON.parse(messageString);
        } catch (err) {
          error('Failed to parse message:', messageString);
          return;
        }

        debug('WebSocket client received message:', JSON.stringify(parsedData));

        const logType = parsedData.ops[0]?.log?.type;

        if (logType === 'done' || logType === 'error') {
          if (!messageHandled) {
            messageHandled = true;
            debug('Message matched:', logType);
            this.client.removeListener('message', handleMessage);
            clearTimeout(
              setTimeout(() => {
                if (!messageHandled) {
                  this.client.removeListener('message', handleMessage);
                  reject(new Error('Timeout waiting for response'));
                  this.clearPendingMessage();
                }
              }, 600000),
            );
            resolve();
            this.clearPendingMessage();
          }
        }
      };

      const send = (): void => {
        if (this.client.readyState === WebSocket.OPEN) {
          debug('WebSocket client sending message:', message);
          this.client.send(JSON.stringify(message));
        } else {
          error('WebSocket is not open. Cannot send message.');
          reject(new Error('WebSocket is not open.'));
        }
      };

      if (this.client.readyState === WebSocket.OPEN) {
        send();
      } else {
        this.client.once('open', send);
      }

      this.pendingMessage = message;
      this.resolvePendingMessage = resolve;
      this.rejectPendingMessage = reject;

      this.client.on('message', handleMessage);
    });
  }

  public close(): void {
    this.isClosed = true;
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }
    this.client.close();
  }
}
