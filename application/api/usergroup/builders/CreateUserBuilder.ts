import { CreateUserRequest } from '../modesl/user.model';

export class CreateUserBuilder {
  private model: CreateUserRequest;

  constructor() {
    this.model = {
      type: 'create',
      obj: 'invite',
      login: '',
      login_type: 'google',
    };
  }

  public withLogin(email: string): this {
    this.model.login = email;
    return this;
  }

  public withLoginType(type: string): this {
    this.model.login_type = type;
    return this;
  }

  public withCompanyId(companyId: string): this {
    this.model.company_id = companyId;
    return this;
  }

  public build(): CreateUserRequest {
    return JSON.parse(JSON.stringify(this.model));
  }
}
