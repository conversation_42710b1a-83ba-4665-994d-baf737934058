import { DeleteApiKeyRequest } from '../modesl/apikey.model';

export class DeleteApiKeyBuilder {
  private model: DeleteApiKeyRequest;

  constructor() {
    this.model = {
      type: 'delete',
      obj: 'user',
      obj_id: '',
      group_id: '',
      level: undefined,
      company_id: undefined,
    };
  }

  public withObjId(objId: string): this {
    this.model.obj_id = objId;
    return this;
  }

  public withGroupId(groupId: string): this {
    this.model.group_id = groupId;
    return this;
  }

  public withLevel(level: string): this {
    this.model.level = level;
    return this;
  }

  public withCompanyId(companyId: string): this {
    this.model.company_id = companyId;
    return this;
  }

  public build(): DeleteApiKeyRequest {
    return JSON.parse(JSON.stringify(this.model));
  }
}
