import { CreateApiKeyRequest } from '../modesl/apikey.model';

export class CreateApiKeyBuilder {
  private model: CreateApiKeyRequest;

  constructor() {
    this.model = {
      type: 'create',
      obj: 'user',
      title: '',
      logins: [{ type: 'api' }],
      company_id: undefined,
    };
  }

  public withTitle(title: string): this {
    this.model.title = title;
    return this;
  }

  public withCompanyId(companyId: string): this {
    this.model.company_id = companyId;
    return this;
  }

  public build(): CreateApiKeyRequest {
    return JSON.parse(JSON.stringify(this.model));
  }
}
