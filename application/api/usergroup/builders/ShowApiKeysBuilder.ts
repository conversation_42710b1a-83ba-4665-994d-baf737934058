import { ShowApiKeysRequest } from '../modesl/apikey.model';

export class ShowApiKeysBuilder {
  private model: ShowApiKeysRequest;

  constructor() {
    this.model = {
      type: 'list',
      obj: 'company_users',
      filter: 'api_key',
      sort: 'title',
      order: 'desc',
      company_id: undefined,
    };
  }

  public withCompanyId(companyId: string): this {
    this.model.company_id = companyId;
    return this;
  }

  public build(): ShowApiKeysRequest {
    return JSON.parse(JSON.stringify(this.model));
  }
}
