import { BaseController } from '../../base/BaseController';
import {
  CreateApiKeyRequest,
  CreateApiKeyResponse,
  DeleteApiKeyRequest,
  DeleteApiKeyResponse,
  ShowApiKeysRequest,
  ShowApiKeysResponse,
} from '../modesl/apikey.model';

export class ApiKeyController extends BaseController {
  public async createApiKey(
    request: CreateApiKeyRequest[],
  ): Promise<{ code: number; headers: any; body: CreateApiKeyResponse }> {
    const response = await this.apiClient.request({ ops: [request] }, 'api/2/json');
    return { code: response.status, headers: response.headers, body: response.body as CreateApiKeyResponse };
  }
  public async showApiKeys(
    request: ShowApiKeysRequest,
  ): Promise<{ code: number; headers: any; body: ShowApiKeysResponse }> {
    const response = await this.apiClient.request({ ops: [request] }, 'api/2/json');
    return { code: response.status, headers: response.headers, body: response.body as ShowApiKeysResponse };
  }
  public async deleteApiKey(
    ...request: DeleteApiKeyRequest[]
  ): Promise<{ code: number; headers: any; body: DeleteApiKeyResponse }> {
    const response = await this.apiClient.request({ ops: [request] }, 'api/2/json');
    return { code: response.status, headers: response.headers, body: response.body as DeleteApiKeyResponse };
  }
}
