import { BaseController } from '../../base/BaseController';
import { CreateUserRequest, CreateUserResponse, ShowUsersRequest, ShowUsersResponse } from '../modesl/user.model';
import {
  CreateApiKeyRequest,
  CreateApiKeyResponse,
  DeleteApiKeyRequest,
  DeleteApiKeyResponse,
  ShowApiKeysRequest,
  ShowApiKeysResponse,
} from '../modesl/apikey.model';

export class UserController extends BaseController {
  public async createUser(
    request: CreateUserRequest,
  ): Promise<{ code: number; headers: any; body: CreateUserResponse }> {
    const response = await this.apiClient.request({ ops: [request] }, 'api/2/json');
    return { code: response.status, headers: response.headers, body: response.body as CreateUserResponse };
  }

  public async showUsers(request: ShowUsersRequest): Promise<{ code: number; headers: any; body: ShowUsersResponse }> {
    const response = await this.apiClient.request({ ops: [request] }, 'api/2/json');
    return { code: response.status, headers: response.headers, body: response.body as ShowUsersResponse };
  }

  public async createapiKey(
    request: CreateApiKeyRequest,
  ): Promise<{ code: number; headers: any; body: CreateApiKeyResponse }> {
    const response = await this.apiClient.request({ ops: [request] }, 'api/2/json');
    return { code: response.status, headers: response.headers, body: response.body as CreateApiKeyResponse };
  }

  public async showapiKey(
    request: ShowApiKeysRequest,
  ): Promise<{ code: number; headers: any; body: ShowApiKeysResponse }> {
    const response = await this.apiClient.request({ ops: [request] }, 'api/2/json');
    return { code: response.status, headers: response.headers, body: response.body as ShowApiKeysResponse };
  }

  public async deleteapiKey(
    request: DeleteApiKeyRequest,
  ): Promise<{ code: number; headers: any; body: DeleteApiKeyResponse }> {
    const response = await this.apiClient.request({ ops: [request] }, 'api/2/json');
    return { code: response.status, headers: response.headers, body: response.body as DeleteApiKeyResponse };
  }
}
