import { BaseController } from '../../base/BaseController';
import {
  AddUserToGroupRequest,
  AddUserToGroupResponse,
  CreateGroupRequest,
  CreateGroupResponse,
  DeleteGroupRequest,
  DeleteGroupResponse,
  DeleteUserFromGroupRequest,
  DeleteUserFromGroupResponse,
  ListGroupsRequest,
  ListGroupsResponse,
  ListOfUsersInGroupRequest,
  ListOfUsersInGroupResponse,
} from '../modesl/group.model';

export class GroupController extends BaseController {
  public async createGroup(
    request: CreateGroupRequest,
  ): Promise<{ code: number; headers: any; body: CreateGroupResponse }> {
    const response = await this.apiClient.request({ ops: [request] }, 'api/2/json');
    return { code: response.status, headers: response.headers, body: response.body as CreateGroupResponse };
  }
  public async listGroups(
    request: ListGroupsRequest,
  ): Promise<{ code: number; headers: any; body: ListGroupsResponse }> {
    const response = await this.apiClient.request({ ops: [request] }, 'api/2/json');
    return { code: response.status, headers: response.headers, body: response.body as ListGroupsResponse };
  }
  public async deleteGroup(
    request: DeleteGroupRequest,
  ): Promise<{ code: number; headers: any; body: DeleteGroupResponse }> {
    const response = await this.apiClient.request({ ops: [request] }, 'api/2/json');
    return { code: response.status, headers: response.headers, body: response.body as DeleteGroupResponse };
  }
  public async addUserToGroup(
    request: AddUserToGroupRequest,
  ): Promise<{ code: number; headers: any; body: AddUserToGroupResponse }> {
    const response = await this.apiClient.request({ ops: [request] }, 'api/2/json');
    return { code: response.status, headers: response.headers, body: response.body as AddUserToGroupResponse };
  }
  public async listOfUsersInGroup(
    request: ListOfUsersInGroupRequest,
  ): Promise<{ code: number; headers: any; body: ListOfUsersInGroupResponse }> {
    const response = await this.apiClient.request({ ops: [request] }, 'api/2/json');
    return { code: response.status, headers: response.headers, body: response.body as ListOfUsersInGroupResponse };
  }
  public async deleteUserFromGroup(
    request: DeleteUserFromGroupRequest,
  ): Promise<{ code: number; headers: any; body: DeleteUserFromGroupResponse }> {
    const response = await this.apiClient.request({ ops: [request] }, 'api/2/json');
    return { code: response.status, headers: response.headers, body: response.body as DeleteUserFromGroupResponse };
  }
}
