import { CorezoidGroup, Data } from '../../common/Interfaces';
import * as data from '../../../fixture/testdata/userGroup.json';
import { ApiClient } from '../ApiClient';
import { Api<PERSON>eyController } from './controllers/apikey.controller';
import { UserController } from './controllers/user.controller';

export class UsersGroupsApi {
  apiKeyController: ApiKeyController;
  userController: UserController;

  constructor(protected httpClient: ApiClient) {
    this.apiKeyController = new ApiKeyController(this.httpClient);
    this.userController = new UserController(this.httpClient);
  }

  async deleteUser(objectType: string, objectId: string | number, companyId?: string): Promise<void> {
    const body = data as Data;
    const object = body[`delete${objectType}`];
    if (companyId) {
      object.company_id = companyId;
    }
    object.obj_id = objectId;
    await this.httpClient.request({ ops: [object] }, 'api/2/json');
  }

  async createUser(requestModel: CreateUserRequest): Promise<CreateUserResponse> {
    const response = await this.httpClient.request({ ops: [requestModel] }, 'api/2/json');
    if (response.status === 200 && response.body.request_proc === 'ok') {
      return response.body.ops[0] as CreateUserResponse;
    } else {
      throw new Error(`[UserGroupApi][createUser] Error: \n${JSON.stringify(response.body, null, 2)}`);
    }
  }

  async createInvite(
    objectType: string,
    login: string,
    options?: { companyId?: string; groupId?: string },
  ): Promise<string | undefined> {
    const body = data as Data;
    const object = body[`create${objectType}`];
    if (options) {
      if (options.companyId) {
        object.company_id = options.companyId;
      }
      if (options.groupId) {
        object.link_to_obj = 'group';
        object.link_to_obj_id = options.groupId;
      }
    }
    object.login = login;
    const response = await this.httpClient.request({ ops: [object] }, 'api/2/json');
    let url;
    if (response) {
      url = response.body.ops[0].url;
    }
    return url;
  }

  async createApiKey(
    objectType: string,
    name: string,
    companyId?: string,
  ): Promise<{ loginId: string; obj_id: string }> {
    const body = data as Data;
    const object = body[`create${objectType}`];
    if (companyId) {
      object.company_id = companyId;
    }
    object.title = name;
    const response = await this.httpClient.request({ ops: [object] }, 'api/2/json');
    let loginId;
    let obj_id;
    if (response) {
      loginId = response.body.ops[0].users[0].logins[0].obj_id;
      obj_id = response.body.ops[0].users[0].obj_id;
    }
    return { loginId, obj_id };
  }

  async deleteGroup(objectType: string, objectId: number | string, companyId?: string): Promise<void> {
    const body = data as Data;
    const object = body[`delete${objectType}`];
    if (companyId) {
      object.company_id = companyId;
    }
    object.obj_id = objectId;
    await this.httpClient.request({ ops: [object] }, 'api/2/json');
  }

  async createGroup(companyId?: string): Promise<CorezoidGroup> {
    const body = data as Data;
    const object = body[`createGroup`];
    if (companyId) {
      object.company_id = companyId;
    }
    object.title = `group_${Date.now()}`;
    const response = await this.httpClient.request({ ops: [object] }, 'api/2/json');
    return { name: object.title, login: '', id: response.body.ops[0].obj_id } as CorezoidGroup;
  }

  async linkUserToGroup(
    objectType: string,
    objectId: number | string,
    groupId: number | string,
    companyId?: string,
  ): Promise<void> {
    const body = data as Data;
    const object = body[`link${objectType}`];
    if (companyId) {
      object.company_id = companyId;
    }
    object.obj_id = objectId;
    object.group_id = groupId;
    await this.httpClient.request({ ops: [object] }, 'api/2/json');
  }
}

interface CreateUserRequest {
  login: string;
  login_type: 'google' | 'corezoid';
  company_id: string | null;
  type: 'create';
  obj: 'invite';
}

interface CreateUserResponse {
  id: string;
  obj: string;
  url: string;
  proc: string;
}
