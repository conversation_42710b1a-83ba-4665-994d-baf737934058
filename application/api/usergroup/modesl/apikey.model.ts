export interface CreateApiKeyRequest {
  readonly type: 'create';
  readonly obj: 'user';
  title: any;
  readonly logins: Array<{ type: any }>;
  company_id: any;
}

export interface CreateApiKeyResponse {
  request_proc: string;
  ops: {
    id: string;
    obj: string;
    proc: string;
    users: {
      obj_id: number;
      title: string;
      logins: {
        type: string;
        key: string;
        obj_id: number;
      }[];
    }[];
  }[];
}

export interface CreateApiKeyErrorResponse {
  request_proc: string;
  ops: {
    id: string;
    proc: string;
    description: string;
  }[];
}

export interface CreateApiKeyError2Response {
  request_proc: string;
  ops: {
    id: string;
    proc: string;
    message: string;
    key: string;
    value: any;
  }[];
}

export interface ShowApiKeysRequest {
  readonly type: 'list';
  readonly obj: 'company_users';
  readonly filter: 'api_key';
  sort?: string;
  order?: string;
  company_id?: string | null;
}

export interface ShowApiKeysResponse {
  request_proc: string;
  ops: {
    id: string;
    proc: string;
    obj: string;
    list: {
      obj_id: number;
      title: string;
      logins: {
        key: string;
        obj_id: string;
        type: string;
        login: string;
      }[];
    }[];
  }[];
}

export interface DeleteApiKeyRequest {
  readonly type: 'delete';
  readonly obj: 'user';
  obj_id: any;
  group_id?: string;
  level?: string;
  company_id?: string | null;
}

export interface DeleteApiKeyResponse {
  request_proc: string;
  ops: {
    id: string;
    proc: string;
    obj_id: number;
    company_id: string | null;
  }[];
}

export interface DeleteApiKeyErrorResponse {
  request_proc: string;
  ops: {
    id: string;
    proc: string;
    description: string;
  }[];
}

export interface DeleteApiKeyError2Response {
  request_proc: string;
  ops: {
    id: string;
    proc: string;
    description: string;
    key: string;
    value: any;
  }[];
}
