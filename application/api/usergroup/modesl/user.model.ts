export interface CreateUserRequest {
  readonly type: 'create';
  readonly obj: 'invite';
  login: any;
  login_type: any;
  company_id?: any;
}

export interface CreateUserResponse {
  request_proc: string;
  ops: {
    id: string;
    proc: string;
    obj: string;
    url: string;
  }[];
}

export interface CreateUserErrorResponse {
  request_proc: string;
  ops: {
    id: string;
    proc: string;
    description: string;
    key: string;
    value: any;
  }[];
}

export interface ShowUsersRequest {
  readonly type: 'list';
  readonly obj: 'company_users';
  filter: 'user' | 'api_key' | 'group';
  sort?: 'title';
  order?: 'asc' | 'desc';
  company_id?: string | null;
}

export interface ShowUsersResponse {
  request_proc: string;
  ops: {
    id: string;
    proc: string;
    obj: string;
    list: {
      obj_id: string;
      title: string;
      logins: {
        obj_id: string;
        type: string;
        login: string;
      }[];
      status: string;
      is_superadmin: boolean;
      groups: {
        id: number;
        name: string;
      }[];
    }[];
  }[];
}
