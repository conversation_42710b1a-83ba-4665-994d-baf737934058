export interface CreateGroupRequest {
  type: 'create';
  obj: 'group';
  obj_type: any;
  title: any;
  company_id: any;
}

export interface CreateGroupResponse {
  request_proc: string;
  ops: {
    id: string;
    obj: string;
    obj_id: number | string;
    proc: string;
  }[];
}

export interface CreateGroupErrorResponse {
  request_proc: string;
  ops: {
    id: string;
    proc: string;
    description: string;
    key: string;
    value: any;
  }[];
}

export interface ListGroupsRequest {
  type: 'list';
  obj: 'company_users';
  filter: any;
  sort: any;
  order: any;
  company_id: any;
}

export interface ListGroupsResponse {
  request_proc: string;
  ops: {
    id: string;
    obj: string;
    proc: string;
    list: {
      obj_id: string | number;
      size: number;
      title: string;
      type: string;
      create_time: number;
      owner_id: number;
      owner_name: string;
      is_owner: boolean;
    }[];
  }[];
}

export interface DeleteGroupRequest {
  type: 'delete';
  obj: 'group';
  obj_id: any;
  company_id: any;
}

export interface DeleteGroupResponse {
  request_proc: string;
  ops: {
    id: string;
    obj: string;
    obj_id: number | string;
    proc: string;
  }[];
}

export interface AddUserToGroupRequest {
  type: 'link';
  obj: 'user';
  obj_id: any;
  group_id: any;
  level: any;
  company_id: any;
}

export interface AddUserToGroupResponse {
  request_proc: string;
  ops: {
    id: string;
    proc: string;
    obj: string;
    logins: {
      type: string;
      login: string;
      key: string;
      obj_id: number;
    }[];
    obj_id: number;
  }[];
}

export interface ListOfUsersInGroupRequest {
  type: 'list';
  obj: 'group';
  obj_id: any;
  list_obj: 'user';
  sort?: 'title';
  order?: 'asc';
  company_id: any;
}

export interface ListOfUsersInGroupResponse {
  request_proc: string;
  ops: {
    id: string;
    obj: string;
    obj_id: number;
    owner_id: number;
    owner_name: string;
    proc: string;
    list: {
      obj: string;
      obj_id: number;
      title: string;
      logins: {
        type: string;
        login: string;
        key: string;
        obj_id: number;
      }[];
    }[];
  }[];
}

export interface DeleteUserFromGroupRequest {
  type: 'link';
  obj: 'user';
  obj_id: any;
  group_id: any;
  company_id: any;
}

export interface DeleteUserFromGroupResponse {
  request_proc: string;
  ops: {
    id: string;
    proc: string;
    obj: string;
    obj_id: number;
    logins: {
      type: string;
      login: string;
      key: string;
      obj_id: number;
    }[];
  }[];
}
