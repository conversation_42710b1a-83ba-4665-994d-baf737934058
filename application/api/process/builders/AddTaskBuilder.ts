import { AddTaskRequest } from '../models/ITasks';

export class AddTaskBuilder {
  private model: AddTaskRequest;

  constructor() {
    this.model = {
      type: 'create',
      obj: 'task',
      obj_alias: undefined,
      conv_id: undefined,
      ref: `${Date.now() + Date.now() / 2}`,
      data: {},
      company_id: undefined,
    };
  }

  public withConvId(convId: string | number): this {
    this.model.conv_id = convId;
    return this;
  }

  public withData(data: Record<string, any>): this {
    this.model.data = data;
    return this;
  }

  public withObjAlias(aliasShortName: string): this {
    this.model.obj_alias = aliasShortName;
    return this;
  }

  public withCompanyId(companyId: string): this {
    this.model.company_id = companyId;
    return this;
  }

  public build(): AddTaskRequest {
    return JSON.parse(JSON.stringify(this.model));
  }
}
