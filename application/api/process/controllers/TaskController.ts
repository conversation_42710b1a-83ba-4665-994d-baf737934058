import { BaseController } from '../../base/BaseController';
import { AddTaskRequest, AddTaskResponse } from '../models/ITasks';

export class TaskController extends BaseController {
  public async addTask(
    path: 'api/1/json' | 'api/2/json',
    ...request: AddTaskRequest[]
  ): Promise<{ code: number; headers: any; body: AddTaskResponse }> {
    const response = await this.apiClient.request({ ops: request }, path);
    return { code: response.status, headers: response.headers, body: response.body as AddTaskResponse };
  }
}
