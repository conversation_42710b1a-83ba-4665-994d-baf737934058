import { logger } from '../../../support/utils/logger';
import { ApiClient } from '../ApiClient';
import { TaskController } from './controllers/TaskController';

export class ProcessApi {
  taskController: TaskController;

  constructor(protected httpclient: ApiClient) {
    this.taskController = new TaskController(this.httpclient);
  }

  getRandomInt(max: number): number {
    return Math.floor(Math.random() * Math.floor(max));
  }

  async sendTask(
    conv_id: number,
    data?: any,
    options?: { reference?: string },
  ): Promise<{ ref: string; response: any }> {
    const ref = options?.reference ? options?.reference : `${Date.now() + this.getRandomInt(100000)}`;
    const showtask = {
      ops: [
        {
          type: 'create',
          conv_id: conv_id,
          obj: 'task',
          action: 'user',
          data: data ? data : {},
          ref: `${ref}`,
        },
      ],
    };
    const response = await this.httpclient.request(showtask, 'api/2/json');
    logger.info(`[SendTask Response] \n${JSON.stringify(response, null, 2)}`);
    return { ref: ref, response: response.body };
  }

  private sleep(milliseconds: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, milliseconds));
  }

  async getTaskFromProcess(conv_id: number, ref: number | string): Promise<any> {
    let counter = 0;
    let responseError;
    const showtask = {
      ops: [
        {
          type: 'show',
          obj: 'task',
          conv_id: conv_id,
          company_id: null,
          ref: `${ref}`,
        },
      ],
    };

    while (counter != 10) {
      this.sleep(400);
      const response = await this.httpclient.request(showtask, 'api/2/json');
      if (response.body.ops[0].description !== 'task not found') {
        logger.debug(JSON.stringify(response, null, 2));
        return response.body.ops[0];
      }
      responseError = response;
      counter++;
    }
    logger.error(JSON.stringify(responseError, null, 2));
    throw new Error(`[Process API] Task ref: ${ref} in conv: ${conv_id} Not Found`);
  }
}
