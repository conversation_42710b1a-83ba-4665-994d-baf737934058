import Ajv from 'ajv';
export class SchemaValidator {
  public static validate(schema: any, data: any): void {
    const ajv = new Ajv({
      strict: false,
      allErrors: true,
      verbose: false,
    });
    const validate = ajv.compile(schema);
    const valid = validate(data);
    if (!valid) {
      throw new Error(
        `Schema validation error: \n${JSON.stringify(
          {
            errors: validate.errors,
          },
          null,
          2,
        )}`,
      );
    }
  }
}
