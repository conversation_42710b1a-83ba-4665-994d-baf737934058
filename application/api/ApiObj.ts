import { OBJ_TYPE, REQUEST_TYPE, createRequestWithOps } from '../../utils/corezoidRequest';

export const requestCreateObj = async (
  api_cor: any,
  OBJ_TYPE: any,
  company_id: any,
  title = `Conv`,
  folder_id?: string | number,
  conv_type = `process`,
  project_id?: any,
  stage_id?: any,
): Promise<any> => {
  const responseList = await api_cor.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE,
      company_id,
      title,
      status: 'actived',
      folder_id,
      conv_type,
      project_id,
      stage_id,
    }),
  );
  expect(responseList.status).toBe(200);
  return responseList;
};

export const requestCreateObjNew = async (
  api_cor: any,
  OBJ_TYPE: any,
  company_id: any,
  title: any,
  keys: Record<string, any>,
): Promise<{ obj_id: any } & any> => {
  const responseList = await api_cor.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE,
      company_id,
      title,
      ...keys,
    }),
  );
  expect(responseList.status).toBe(200);

  const obj_id = responseList.body?.ops?.[0]?.obj_id ?? null;
  return { ...responseList, obj_id };
};

export const requestShow = async (
  api_cor: any,
  OBJ_TYPE: any,
  obj_id: any,
  keys: Record<string, any>,
): Promise<any> => {
  const responseShow = await api_cor.request(
    createRequestWithOps({
      type: REQUEST_TYPE.SHOW,
      obj: OBJ_TYPE,
      obj_id,
      ...keys,
    }),
  );
  expect(responseShow.status).toBe(200);
  return responseShow;
};

export const requestLinkObj = async (
  api_cor: any,
  OBJ_TYPE: any,
  company_id: any,
  obj_id: any,
  link: any,
  obj_to_id: any,
  obj_to_type: any,
  keys: Record<string, any> = {},
): Promise<any> => {
  const responseLink = await api_cor.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LINK,
      obj: OBJ_TYPE,
      company_id,
      obj_id,
      link,
      obj_to_id,
      obj_to_type,
      ...keys,
    }),
  );
  expect(responseLink.status).toBe(200);
  return responseLink;
};

export const requestDeleteObj = async (api_cor: any, OBJ_TYPE: any, obj_id: any, company_id: any): Promise<any> => {
  const responseDelete = await api_cor.request(
    createRequestWithOps({
      type: REQUEST_TYPE.DELETE,
      obj: OBJ_TYPE,
      obj_id,
      company_id,
    }),
  );
  expect(responseDelete.status).toBe(200);
  return responseDelete;
};

export const requestRestoreObj = async (api_cor: any, OBJ_TYPE: any, obj_id: any, company_id: any): Promise<any> => {
  const responseRestore = await api_cor.request(
    createRequestWithOps({
      type: REQUEST_TYPE.RESTORE,
      obj: OBJ_TYPE,
      obj_id,
      company_id,
    }),
  );
  expect(responseRestore.status).toBe(200);
  return responseRestore;
};

export const requestList = async (
  api_cor: any,
  obj_id: any,
  conv_id: number,
  company_id: any,
  limit = 1,
): Promise<any> => {
  const responseList = await api_cor.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id,
      company_id,
      conv_id,
      limit,
    }),
  );
  expect(responseList.status).toBe(200);
  return responseList;
};

export const requestListConv = async (
  api_cor: any,
  obj_id: any,
  company_id: any,
  project_id?: any,
  stage_id?: any,
): Promise<any> => {
  const responseCommit = await api_cor.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.CONV,
      obj_id,
      company_id,
      project_id,
      stage_id,
    }),
  );
  expect(responseCommit.status).toBe(200);
  return responseCommit;
};

export const requestConfirm = async (api_cor: any, conv_id: any, company_id: any): Promise<any> => {
  const responseCommit = await api_cor.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      company_id,
      conv_id,
      version: 22,
    }),
  );
  expect(responseCommit.status).toBe(200);
  return responseCommit;
};

export const requestModifyConvStatus = async (
  api_cor: any,
  obj_id: any,
  company_id: any,
  status: any,
  title?: string,
  description?: string,
): Promise<any> => {
  const responseCommit = await api_cor.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.CONV,
      company_id,
      obj_id,
      status,
      title,
      description,
    }),
  );
  expect(responseCommit.status).toBe(200);
  return responseCommit;
};

export const requestCreateTask = async (
  api_cor: any,
  OBJ_TYPE: any,
  company_id: any,
  conv_id: any,
  ref: any,
  data: any,
  keys?: Record<string, any>,
): Promise<any> => {
  const responseCreateTask = await api_cor.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE,
      company_id,
      conv_id,
      ref,
      data,
      ...keys,
    }),
  );
  expect(responseCreateTask.status).toBe(200);
  return responseCreateTask;
};
