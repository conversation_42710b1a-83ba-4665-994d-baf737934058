import { ApiUserClient } from '../ApiUserClient';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';

export class CompnayApi {
  constructor(protected httpClient: ApiUserClient) {}

  async createCompany(): Promise<{ name?: string; id?: string }> {
    const createResponse = await this.httpClient.request(
      createRequestWithOps({
        site: 'corezoid.com',
        name: `TestCompany-${Date.now()}`,
        phone: '1234567890',
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,
        description: 'Corezoid company',
      }),
    );

    const confirmREsponse = await this.httpClient.request(
      createRequestWithOps({
        code: '11111111',
        type: REQUEST_TYPE.LOAD,
        obj: OBJ_TYPE.OTP,
        token: createResponse.body.ops[0].token,
      }),
    );

    return {
      name: confirmREsponse.body.ops[0]?.company_list[0]?.name,
      id: confirmREsponse.body.ops[0]?.company_list[0]?.id,
    };
  }
}
