import { ConfigurationManager } from '../../infrastructure/config/ConfigurationManager';
import contentGPT from '../../fixture/testdata/promtGPT.json';
import { Method, createAuthUser } from '../../utils/request';
import { METHOD_SHOW } from './obj_types/api_code';
import { error } from '../../support/utils/logger';

interface ContentGPT {
  system: {
    role: string;
    content: string;
  };
  generateCode: Record<string, string>;
  codeRating: {
    ratingExplain: string;
    ratingCompletion: string;
  };
}

const dataGPT = ConfigurationManager.getConfiguration().getDataGPT();
const tokenUser = createAuthUser(dataGPT.token, 'token');

const contentObj: ContentGPT = contentGPT as ContentGPT;

export async function requestGPT(content: string): Promise<string> {
  const response = await tokenUser.request({
    url: dataGPT.url,
    method: Method.POST,
    data: {
      model: 'gpt-4o',
      messages: [
        contentGPT.system,
        {
          role: 'user',
          content,
        },
      ],
    },
  });
  try {
    const respMessage = response?.data?.choices?.[0]?.message?.content;
    if (!respMessage) {
      throw new Error(`Invalid API response: ${JSON.stringify(response.data, null, 2)}`);
    }
    return respMessage;
  } catch (err) {
    error('Error:', err);
    throw err;
  }
}

export async function generateCode(lang: string): Promise<string> {
  return (await requestGPT(contentObj.generateCode[lang])).split(`\`\`\`${lang.toLowerCase()}`)[1].split('```')[0];
}

export async function codeRating(
  retingMethod: METHOD_SHOW,
  generateCode: string,
  resultsExplain: string,
): Promise<number> {
  const ratingTemplates: Record<string, string> = {
    explain: contentGPT.codeRating.ratingExplain,
    completion: contentGPT.codeRating.ratingCompletion,
  };
  const str = ratingTemplates[retingMethod] || '';
  if (!str) {
    throw new Error(`Unknown rating method: ${retingMethod}`);
  }
  const contentRating = str.replace('${generateCode}', generateCode).replace('${resultsExplain}', resultsExplain);
  return parseInt((await requestGPT(contentRating)).replace(/[^\d]/g, ''));
}
