import { logger } from '../../../support/utils/logger';
import { ApiUserClient } from '../ApiUserClient';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';

export class DBCallApi {
  constructor(protected httpClient: ApiUserClient) {}

  async checkConnection(config: ConnectionConfig): Promise<any> {
    const requestObject = {
      title: `Autotest_${Date.now()}`,
      description: '',
      type: 'check',
      obj: 'instance',
      instance_type: 'db_call',
      data: config,
    };
    return await this.sendRequest(requestObject);
  }

  async createConnection(config: ConnectionConfig, company_id?: string): Promise<any> {
    const requestObject = {
      title: `Autotest_${Date.now()}`,
      description: '',
      folder_id: 0,
      obj: OBJ_TYPE.INSTANCE,
      instance_type: 'db_call',
      data: config,
      type: REQUEST_TYPE.CREATE,
      obj_type: 0,
      status: 'active',
      company_id,
    };
    return await this.sendRequest(requestObject);
  }

  private async sendRequest(requestObject: any): Promise<any> {
    logger.info(`[DBCallApi] Request:\n${JSON.stringify(requestObject, null, 2)}`);
    const response = await this.httpClient.request(
      createRequestWithOps({
        ...requestObject,
      }),
    );

    if (response.status !== 200) {
      throw new Error(`[DBCallApi] ERROR: \ncode: ${response.status}\nmsg:${JSON.stringify(response.body, null, 2)}`);
    } else {
      logger.info(`[DBCallApi] Response:\n${JSON.stringify(response.body, null, 2)}`);
      return { request: requestObject, response: response };
    }
  }
}

export interface ConnectionConfig {
  driver: 'postgres' | 'mysql' | 'mssql' | 'mongo' | 'oracle';
  host: string;
  port: string;
  username: string;
  password: string;
  ssl: boolean;
  database: string;
  timeoutMs: string;
}
