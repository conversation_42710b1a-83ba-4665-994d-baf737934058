export interface AuthResponse {
  data: {
    auth_single_account: boolean;
  };
}

export interface User {
  email: string;
  password: string;
}

export interface MultitenantCompany {
  id: string;
  name: string;
  email: string;
  status: boolean;
}

export interface CorezoidObject {
  name: string;
  owner: string;
  id: number;
}

export interface Data {
  [key: string]: any;
}

export interface CorezoidGroup {
  name: string;
  login: string;
  id?: number;
}
