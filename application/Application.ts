import { ApiUserClient } from './api/ApiUserClient';
import { SignIn } from '../infrastructure/auth/SignIn';
import { logger } from '../support/utils/logger';
import { ConfigurationManager } from '../infrastructure/config/ConfigurationManager';
import { User } from '../infrastructure/model/User';
import { ApiKeyClient } from './api/ApiKeyClient';
import { ApiKey } from '../infrastructure/model/ApiKey';
import { axiosInstance } from '../application/api/AxiosClient';

class Application {
  configurationManager = ConfigurationManager.getConfiguration();

  async getAuthorizedUser(options?: AuthorizationUserOptions, id = 0): Promise<User> {
    const useConfig = this.configurationManager.getApiKey().active_config;

    let user: User | undefined;
    if (useConfig) {
      user = this.configurationManager.getUserConfig(id);
    } else {
      user = await this.configurationManager.getUser({});
    }

    if (!user) {
      throw new Error('User not found');
    }

    logger.info(`[Application] Try to authorize as \n${JSON.stringify(user, null, 2)}`);
    if (!user.apiCookies.length && !user.webCookies.length) {
      const { webCookies, apiCookies } = await SignIn.authorize(user);
      user.setApiCookies(apiCookies).setWebCookies(webCookies);
    }
    return user;
  }

  async getApiKey(): Promise<ApiKey> {
    await this.configurationManager.getCorezoidConfig();
    return this.configurationManager.getApiKey();
  }

  async getApiKeySuper(): Promise<ApiKey> {
    await this.configurationManager.getCorezoidConfig();
    return this.configurationManager.getApiKeySuper();
  }

  async createToken(id: number): Promise<string> {
    const user_super = await this.getAuthorizedUser({}, 1);
    const cookie = user_super.cookieUser;
    const user = this.configurationManager.getUserConfig(id);
    const user_id = user.id;

    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}system/tests/jwt_token/${user_id}/600`;
    const response = await axiosInstance({
      method: 'GET',
      url: uri,
      headers: {
        cookie,
      },
    });
    expect(response.status).toBe(200);
    const token = `Simulator ${response.data.token}`;

    return token;
  }

  public async getApiUserClient(user: User): Promise<ApiUserClient> {
    if (!user?.apiCookies.length) throw new Error(`[Application Error] User '${user?.email}' is not authorize`);
    return new ApiUserClient(user);
  }

  public getApiKeyClient(apikey: ApiKey): ApiKeyClient {
    return new ApiKeyClient(apikey);
  }
}

export const application = new Application();

interface AuthorizationUserOptions {
  superadmin?: boolean;
  company?: { name?: string; id?: string; role?: string };
}

interface KeyOptions {
  superadmin?: boolean;
  company?: { name?: string; id?: string };
}
