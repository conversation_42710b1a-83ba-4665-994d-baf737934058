{"compilerOptions": {"strict": true, "baseUrl": "../node_modules", "lib": ["es2018", "dom"], "resolveJsonModule": true, "types": ["node", "jest"], "typeRoots": ["node_modules/@types"], "outDir": "build", "esModuleInterop": true, "target": "es5"}, "include": ["**/*.ts", "tests/K6/Load-test/loadWebhook.js", "tests/K6/Load-test/config/envconfig.js", "scripts/deleteObj.test.ts", "tests/K6/Load-test/loadReq.js"], "files": ["infrastructure/runner/types/global.d.ts"]}