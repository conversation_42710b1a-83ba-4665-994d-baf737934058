#!/bin/bash

# Путь к файлу лога
LOG_FILE="report/K6/log/stderr.log"
# Лимит размера лога в байтах (4 MB)
LOG_LIMIT=$((3 * 1024 * 1024))

while true; do
  # Проверка существования файла лога
  if [ -f "$LOG_FILE" ]; then
    LOG_SIZE=$(stat -c%s "$LOG_FILE")
    if [ "$LOG_SIZE" -ge "$LOG_LIMIT" ]; then
      echo "Log file size exceeded limit of $LOG_LIMIT bytes."
      exit 1
    fi
  fi
  sleep 2  # Период проверки каждые 2 секунды
done
