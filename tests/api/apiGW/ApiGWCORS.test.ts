import { request } from '../../../application/api/ApiGWUserClient';
import { axiosInstance } from '../../../application/api/AxiosClient';
import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';

describe('ApiGWCors', () => {
  let endpoint_id: number;
  let path: string;
  let uri: string;
  let uri_proxy: string;
  let host: string;
  let protocol: string;

  beforeAll(
    async (): Promise<void> => {
      uri = `${ConfigurationManager.getConfiguration().getApiGWUrl()}auth/me`;
      const parsedUrl = new URL(`${ConfigurationManager.getConfiguration().getApiGWUrl()}`);
      protocol = parsedUrl.protocol;

      const response = await request('post', `apis`, { name: `auto_test${Date.now()}`, description: '' });
      expect(response.status).toBe(200);
      endpoint_id = response.body.api.id;
      host = response.body.api.host;

      const responsePath = await request('post', `apis/${endpoint_id}/paths`, {
        path: '/get/path',
        description: 'test_description',
        method: 'GET',
        timeout: 29,
        async: true,
        proxy_headers: true,
        proxy_raw_body: true,
        public: { api_login: 777 },
        process_id: 3,
        paused: true,
      });
      expect(responsePath.status).toBe(200);
      expect(responsePath.body.path.api_id).toBe(endpoint_id);
      path = responsePath.body.path.path;
      uri_proxy = `${protocol}//${host}${path}`;
    },
  );

  test(`create request 'OPTIONS' on the api with headers`, async (): Promise<void> => {
    const response = await axiosInstance({
      method: 'OPTIONS',
      url: uri,
      headers: {
        'Access-Control-Request-Method': 'GET',
        Origin: 'https://fiddle.jshell.net',
      },
    });
    expect(response.status).toBe(200);

    expect(response.headers['vary']).toBe(`Origin, Access-Control-Request-Method, Access-Control-Request-Headers`);
  });

  test(`create request 'OPTIONS' on the api without headers`, async (): Promise<void> => {
    const response = await axiosInstance({
      method: 'OPTIONS',
      url: uri,
    });
    expect(response.status).toBe(405);
  });

  test(`create request 'OPTIONS' on the proxy with headers`, async (): Promise<void> => {
    const response = await axiosInstance({
      method: 'OPTIONS',
      url: uri_proxy,
      headers: {
        'Access-Control-Request-Method': 'GET',
      },
    });
    expect(response.status).toBe(200);

    expect(response.headers['vary']).toBe(`Origin, Access-Control-Request-Method, Access-Control-Request-Headers`);
  });

  test(`create request 'OPTIONS' on the proxy without headers`, async (): Promise<void> => {
    const response = await axiosInstance({
      method: 'OPTIONS',
      url: uri_proxy,
    });
    expect(response.status).toBe(405);
  });

  afterAll(
    async (): Promise<void> => {
      const response = await request('delete', `apis/${endpoint_id}`, {});
      expect(response.status).toBe(204);
    },
  );
});
