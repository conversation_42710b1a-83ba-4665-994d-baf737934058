import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import { SchemaValidator } from '../../../application/api/SchemaValidator';
import listTask from './schemas/listTaskSyncApiSchema.json';
import createTask from './schemas/createTaskSyncApiSchema.json';
import { requestList, requestDeleteObj, requestCreateObj, requestListConv } from '../../../application/api/ApiObj';

describe('SyncApi Task (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: number;
  let newConv2: number;
  let nodeLogic: string;
  let nodeStart: string;
  let nodeFinal: string;
  let nodeLogic2: string;
  let nodeStart2: string;
  let nodeFinal2: string;
  let company_id: any;
  let newNodeApi: string;
  let newNodeApi2: string;
  let newAlias: string | number;
  let newAliasShortName: string;
  let newAlias2: string | number;
  let aliasShortName2: string;
  let newProject: string | number;
  let newStage: string | number;
  let projectShortName: string;

  const modifyRequestAndUpdateNode = async (conv_id: any, obj_id: any, to_node_id: any): Promise<void> => {
    const responseModifyNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id,
        company_id,
        conv_id,
        title: 'api',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'GET',
            url: 'https://www.w3schools.com/php/books.xml',
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: '',
            extra_headers: {},
            send_sys: false,
            cert_pem: '',
            content_type: 'text/xml',
          },
          { to_node_id, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifyNode.status).toBe(200);

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        conv_id,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
  };

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;
      newAliasShortName = `alias-test-sync-api-${Date.now()}`;
      aliasShortName2 = `alias2-test-sync-api-${Date.now()}`;
      projectShortName = `project-${Date.now()}`;

      const responseConv = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `${OBJ_TYPE.CONV}_${Date.now()}`);
      newConv = responseConv.body.ops[0].obj_id;

      const responseList = await requestListConv(api, newConv, company_id);
      nodeLogic = responseList.body.ops[0].list[1].obj_id;
      nodeStart = responseList.body.ops[0].list[0].obj_id;
      nodeFinal = responseList.body.ops[0].list[2].obj_id;

      await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: nodeLogic,
          company_id,
          conv_id: newConv,
          title: 'process',
          description: '',
          obj_type: 0,
          logics: [
            {
              type: 'api',
              format: '',
              method: 'POST',
              url: '{{__callback_url}}',
              extra: { a: '{{bookstore}}' },
              extra_type: { a: 'array' },
              max_threads: 5,
              err_node_id: '',
              extra_headers: {},
              send_sys: false,
              cert_pem: '',
              content_type: 'application/json',
            },
            { node_title: 'final', to_node_id: nodeFinal, format: 'json', type: 'go' },
          ],
          semaphors: [],
          version: 22,
        }),
      );

      const responseNewNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.NODE,
          company_id,
          title: 'api_sync',
          conv_id: newConv,
          obj_type: 0,
          version: 22,
        }),
      );
      newNodeApi = responseNewNode.body.ops[0].obj_id;

      await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: nodeStart,
          company_id,
          conv_id: newConv,
          title: 'start',
          description: '',
          obj_type: 1,
          logics: [{ to_node_id: newNodeApi, type: 'go' }],
          semaphors: [],
          version: 22,
        }),
      );

      await modifyRequestAndUpdateNode(newConv, newNodeApi, nodeLogic);

      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          short_name: newAliasShortName,
          description: `aliasDesk-${Date.now()}`,
          title: `aliasTitle-${Date.now()}`,
          project_id: 0,
        }),
      );
      newAlias = response.body.ops[0].obj_id;

      await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.ALIAS,
          obj_id: newAlias,
          company_id,
          obj_to_id: newConv,
          obj_to_type: 'conv',
          link: true,
          project_id: 0,
        }),
      );

      const responseProject = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.PROJECT,
          company_id,
          title: `Project_${Date.now()}`,
          short_name: projectShortName,
          description: 'test',
          stages: ['production'],
          status: 'active',
        }),
      );
      expect(responseProject.status).toBe(200);
      newProject = responseProject.body.ops[0].obj_id;
      newStage = responseProject.body.ops[0].stages[0];

      const responseConv2 = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.CONV,
          company_id,
          project_id: newProject,
          stage_id: newStage,
          title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        }),
      );
      newConv2 = responseConv2.body.ops[0].obj_id;

      const responseList2 = await requestListConv(api, newConv2, company_id);
      nodeLogic2 = responseList2.body.ops[0].list[1].obj_id;
      nodeStart2 = responseList2.body.ops[0].list[0].obj_id;
      nodeFinal2 = responseList2.body.ops[0].list[2].obj_id;

      await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: nodeLogic2,
          company_id,
          conv_id: newConv2,
          title: 'process',
          description: '',
          obj_type: 0,
          logics: [
            {
              type: 'api',
              format: '',
              method: 'POST',
              url: '{{__callback_url}}',
              extra: { a: '{{bookstore}}' },
              extra_type: { a: 'array' },
              max_threads: 5,
              err_node_id: '',
              extra_headers: {},
              send_sys: false,
              cert_pem: '',
              content_type: 'application/json',
            },
            { node_title: 'final', to_node_id: nodeFinal2, format: 'json', type: 'go' },
          ],
          semaphors: [],
          version: 22,
        }),
      );

      const responseNewNode2 = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.NODE,
          company_id,
          title: 'api_sync',
          conv_id: newConv2,
          obj_type: 0,
          version: 22,
        }),
      );
      newNodeApi2 = responseNewNode2.body.ops[0].obj_id;

      await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: nodeStart2,
          company_id,
          conv_id: newConv2,
          title: 'start',
          description: '',
          obj_type: 1,
          logics: [{ to_node_id: newNodeApi2, type: 'go' }],
          semaphors: [],
          version: 22,
        }),
      );

      await modifyRequestAndUpdateNode(newConv2, newNodeApi2, nodeLogic2);

      const responseAlias = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          short_name: aliasShortName2,
          description: `aliasDesk-${Date.now()}`,
          title: `aliasTitle-${Date.now()}`,
          project_id: newProject,
          stage_id: newStage,
        }),
      );
      newAlias2 = responseAlias.body.ops[0].obj_id;

      await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.ALIAS,
          obj_id: newAlias2,
          company_id,
          obj_to_id: newConv2,
          obj_to_type: 'conv',
          link: true,
          project_id: newProject,
          stage_id: newStage,
        }),
      );
    },
  );

  test('should create task sync_api by conv_id number', async (): Promise<void> => {
    const responseTesk = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: newConv,
        data: { test: '{{body.ops[0].data.a}}', task: 1 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTesk.status).toBe(200);
    expect(responseTesk.body.ops[0].proc).toEqual('ok');
    expect(responseTesk.body.ops[0].data.a[0]).toContainKey('book');
    SchemaValidator.validate(createTask, responseTesk.body);

    await new Promise(r => setTimeout(r, 2000));
    const responseList = await requestList(api, nodeFinal, newConv, company_id);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.bookstore.book).toBeArray;
    expect(responseList.body.ops[0].list[0].data.__callback_url).toBeString();
    expect(responseList.body.ops[0].list[0].data.task).toBe(1);
    SchemaValidator.validate(listTask, responseList.body);
  });

  test('should create task sync_api by conv_id string', async (): Promise<void> => {
    const responseTesk = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: `${newConv}`,
        data: { test: '{{body.ops[0].data.a}}', task: 2 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTesk.status).toBe(200);
    expect(responseTesk.body.ops[0].proc).toEqual('ok');
    expect(responseTesk.body.ops[0].data.a[0]).toContainKey('book');
    SchemaValidator.validate(createTask, responseTesk.body);

    await new Promise(r => setTimeout(r, 2000));
    const responseList = await requestList(api, nodeFinal, newConv, company_id);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.bookstore.book).toBeArray;
    expect(responseList.body.ops[0].list[0].data.__callback_url).toBeString();
    expect(responseList.body.ops[0].list[0].data.task).toBe(2);
    SchemaValidator.validate(listTask, responseList.body);
  });

  test('should create task sync_api by alias', async (): Promise<void> => {
    const responseTesk = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        obj_alias: newAliasShortName,
        data: { test: '{{body.ops[0].data.a}}', task: 3 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTesk.status).toBe(200);
    expect(responseTesk.body.ops[0].proc).toEqual('ok');
    expect(responseTesk.body.ops[0].data.a[0]).toContainKey('book');
    SchemaValidator.validate(createTask, responseTesk.body);

    await new Promise(r => setTimeout(r, 2000));
    const responseList = await requestList(api, nodeFinal, newConv, company_id);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.bookstore.book).toBeArray;
    expect(responseList.body.ops[0].list[0].data.__callback_url).toBeString();
    expect(responseList.body.ops[0].list[0].data.task).toBe(3);
    SchemaValidator.validate(listTask, responseList.body);
  });

  test('should create task sync_api by conv_id data {}', async (): Promise<void> => {
    const responseTesk = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: newConv,
        data: {},
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTesk.status).toBe(200);
    expect(responseTesk.body.ops[0].proc).toEqual('ok');
    expect(responseTesk.body.ops[0].data.a[0]).toContainKey('book');
    SchemaValidator.validate(createTask, responseTesk.body);

    await new Promise(r => setTimeout(r, 2000));
    const responseList = await requestList(api, nodeFinal, newConv, company_id);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.bookstore.book).toBeArray;
    expect(responseList.body.ops[0].list[0].data.__callback_url).toBeString();
    SchemaValidator.validate(listTask, responseList.body);
  });

  test('should create task sync_api by alias data {}', async (): Promise<void> => {
    const responseTesk = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        obj_alias: newAliasShortName,
        data: {},
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTesk.status).toBe(200);
    expect(responseTesk.body.ops[0].proc).toEqual('ok');
    expect(responseTesk.body.ops[0].data.a[0]).toContainKey('book');
    SchemaValidator.validate(createTask, responseTesk.body);

    await new Promise(r => setTimeout(r, 2000));
    const responseList = await requestList(api, nodeFinal, newConv, company_id);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.bookstore.book).toBeArray;
    expect(responseList.body.ops[0].list[0].data.__callback_url).toBeString();
    SchemaValidator.validate(listTask, responseList.body);
  });

  test('should create task sync_api by conv_id in project', async (): Promise<void> => {
    const responseTesk = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: newConv2,
        data: { test: '{{body.ops[0].data.a}}', task: 1 },
        ref: `ref_${Date.now()}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseTesk.status).toBe(200);
    expect(responseTesk.body.ops[0].proc).toEqual('ok');
    expect(responseTesk.body.ops[0].data.a[0]).toContainKey('book');
    SchemaValidator.validate(createTask, responseTesk.body);

    await new Promise(r => setTimeout(r, 2000));
    const responseList = await requestList(api, nodeFinal2, newConv2, company_id);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.bookstore.book).toBeArray;
    expect(responseList.body.ops[0].list[0].data.__callback_url).toBeString();
    expect(responseList.body.ops[0].list[0].data.task).toBe(1);
    SchemaValidator.validate(listTask, responseList.body);
  });

  test('should create task sync_api by by conv_id/project_id/stage_id string in project', async (): Promise<void> => {
    const responseTesk = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: `${newConv2}`,
        data: { test: '{{body.ops[0].data.a}}', task: 1 },
        ref: `ref_${Date.now()}`,
        project_id: `${newProject}`,
        stage_id: `${newStage}`,
      }),
    );
    expect(responseTesk.status).toBe(200);
    expect(responseTesk.body.ops[0].proc).toEqual('ok');
    expect(responseTesk.body.ops[0].data.a[0]).toContainKey('book');
    SchemaValidator.validate(createTask, responseTesk.body);

    await new Promise(r => setTimeout(r, 2000));
    const responseList = await requestList(api, nodeFinal2, newConv2, company_id);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.bookstore.book).toBeArray;
    expect(responseList.body.ops[0].list[0].data.__callback_url).toBeString();
    expect(responseList.body.ops[0].list[0].data.task).toBe(1);
    SchemaValidator.validate(listTask, responseList.body);
  });

  test('should create task sync_api by alias in project', async (): Promise<void> => {
    const responseTesk = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        obj_alias: aliasShortName2,
        data: { test: '{{body.ops[0].data.a}}', task: 2 },
        ref: `ref_${Date.now()}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseTesk.status).toBe(200);
    expect(responseTesk.body.ops[0].proc).toEqual('ok');
    expect(responseTesk.body.ops[0].data.a[0]).toContainKey('book');
    SchemaValidator.validate(createTask, responseTesk.body);

    await new Promise(r => setTimeout(r, 2000));
    const responseList = await requestList(api, nodeFinal2, newConv2, company_id);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.bookstore.book).toBeArray;
    expect(responseList.body.ops[0].list[0].data.__callback_url).toBeString();
    expect(responseList.body.ops[0].list[0].data.task).toBe(2);
    SchemaValidator.validate(listTask, responseList.body);
  });

  test('should create task sync_api by alias and project/stage short_name', async (): Promise<void> => {
    const responseTesk = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        obj_alias: aliasShortName2,
        data: { test: '{{body.ops[0].data.a}}', task: 3 },
        ref: `ref_${Date.now()}`,
        project_short_name: projectShortName,
        stage_short_name: 'production',
      }),
    );
    expect(responseTesk.status).toBe(200);
    expect(responseTesk.body.ops[0].proc).toEqual('ok');
    expect(responseTesk.body.ops[0].data.a[0]).toContainKey('book');
    SchemaValidator.validate(createTask, responseTesk.body);

    await new Promise(r => setTimeout(r, 2000));
    const responseList = await requestList(api, nodeFinal2, newConv2, company_id);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.bookstore.book).toBeArray;
    expect(responseList.body.ops[0].list[0].data.__callback_url).toBeString();
    expect(responseList.body.ops[0].list[0].data.task).toBe(3);
    SchemaValidator.validate(listTask, responseList.body);
  });

  test('should create task sync_api by conv_id data {} in project', async (): Promise<void> => {
    const responseTesk = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: newConv2,
        data: {},
        ref: `ref_${Date.now()}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseTesk.status).toBe(200);
    expect(responseTesk.body.ops[0].proc).toEqual('ok');
    expect(responseTesk.body.ops[0].data.a[0]).toContainKey('book');
    SchemaValidator.validate(createTask, responseTesk.body);

    await new Promise(r => setTimeout(r, 2000));
    const responseList = await requestList(api, nodeFinal2, newConv2, company_id);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.bookstore.book).toBeArray;
    expect(responseList.body.ops[0].list[0].data.__callback_url).toBeString();
    SchemaValidator.validate(listTask, responseList.body);
  });

  test('should create task sync_api by alias data{} in project', async (): Promise<void> => {
    const responseTesk = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        obj_alias: aliasShortName2,
        data: {},
        ref: `ref_${Date.now()}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseTesk.status).toBe(200);
    expect(responseTesk.body.ops[0].proc).toEqual('ok');
    expect(responseTesk.body.ops[0].data.a[0]).toContainKey('book');
    SchemaValidator.validate(createTask, responseTesk.body);

    await new Promise(r => setTimeout(r, 2000));
    const responseList = await requestList(api, nodeFinal2, newConv2, company_id);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.bookstore.book).toBeArray;
    expect(responseList.body.ops[0].list[0].data.__callback_url).toBeString();
    SchemaValidator.validate(listTask, responseList.body);
  });

  afterAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);

      const response = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, company_id);
      expect(response.status).toBe(200);

      const response2 = await requestDeleteObj(api, OBJ_TYPE.ALIAS, newAlias, company_id);
      expect(response2.status).toBe(200);

      const response3 = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
      expect(response3.status).toBe(200);
    },
  );
});
