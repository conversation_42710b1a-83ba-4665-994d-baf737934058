import { request } from '../../../application/api/ApiGWUserClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import { axiosInstance } from '../../../application/api/AxiosClient';
import { SchemaValidator } from '../../../application/api/SchemaValidator';
import LisTaskSchema from './schemas/listTaskSchema.json';
import LisTaskHeadersSchema from './schemas/listTaskHeadersSchema.json';
import listTaskXSchema from './schemas/listTaskSchemaX-Task-Format.json';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
} from '../../../application/api/ApiObj';
import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';
import { ApiUserClient } from '../../../application/api/ApiUserClient';
import { User } from '../../../infrastructure/model/User';

describe('ApiGWCor (positive)', (): void => {
  let api_cor: ApiKeyClient;
  let apikey: ApiKey;
  let api_user: ApiUserClient;
  let user: User;
  let endpoint_id: number;
  let company_id: any;
  let workspace_id: any;
  let path: string;
  let path_id: number;
  let path_id_post: number;
  let path_post: string;
  let path_put: string;
  let path_patch: string;
  let path_delete: string;
  let path_id_modify: number;
  let host: string;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let final_node_ID_2: string | number;
  let conv_id: number;
  let conv_id_2: number;
  let folder_id: number;
  let newApiKeyId: string | number;
  let newApiKeyLogin: number;
  let newApiKeySecret: string | number;
  let protocol: string;
  let project_short_name: string;
  let stage_short_name: string;
  let alias_short_name: string;
  let project_id: number;
  let stage_id: number;
  let alias_id: string | number;

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api_cor = application.getApiKeyClient(apikey);

      user = await application.getAuthorizedUser({ company: {} }, 0);
      api_user = await application.getApiUserClient(user);
      workspace_id = user.companies[0].id;
      company_id = apikey.companies[0].id;
      const parsedUrl = new URL(`${ConfigurationManager.getConfiguration().getApiGWUrl()}`);
      protocol = parsedUrl.protocol;
      project_short_name = `project-${Date.now()}`;
      stage_short_name = `stage-${Date.now()}`;
      alias_short_name = `alias-${Date.now()}-alias`;

      const CreateKeyResponse = await api_cor.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.USER,
          company_id,
          title: 'API',
          logins: [{ type: 'api' }],
        }),
      );
      expect(CreateKeyResponse.status).toBe(200);
      const user_api = CreateKeyResponse.body;
      const newApiKey = {
        key: `${user_api.ops[0].users[0].logins[0].obj_id}`,
        secret: user_api.ops[0].users[0].logins[0].key,
        companies: [],
        title: '',
        id: `${user_api.ops[0].users[0].obj_id}`,
      } as ApiKey;
      newApiKeyId = +newApiKey.id;
      newApiKeyLogin = +newApiKey.key;
      newApiKeySecret = newApiKey.secret;

      const responseFolder = await requestCreateObj(api_cor, OBJ_TYPE.FOLDER, company_id, `ApiGW`);
      folder_id = responseFolder.body.ops[0].obj_id;

      const responseConv = await requestCreateObj(api_cor, OBJ_TYPE.CONV, company_id, `Api_Call`, folder_id);
      conv_id = responseConv.body.ops[0].obj_id;

      const responseList = await requestListConv(api_cor, conv_id, company_id);
      expect(responseList.status).toBe(200);
      process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
      final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

      const responseCreateNode = await api_cor.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id,
          title: 'ApiGW',
          description: '',
          obj_type: 0,
          logics: [
            {
              type: 'api',
              format: '',
              method: 'POST',
              url: '{{__callback_url}}',
              extra: { proxy: '{{code}}' },
              extra_type: { proxy: 'string' },
              max_threads: 5,
              err_node_id: '',
              extra_headers: { ['content-type']: 'application/json; charset=utf-8' },
              debug_info: false,
              send_sys: true,
              rfc_format: true,
              cert_pem: '',
              content_type: 'application/json',
              version: 2,
              response: {
                header: '{{header}}',
                body: '{{body}}',
              },
              response_type: {
                header: 'object',
                body: 'object',
              },
            },
            { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseCreateNode.status).toBe(200);

      const responseCommit = await requestConfirm(api_cor, conv_id, company_id);
      expect(responseCommit.status).toBe(200);

      const responseLink = await api_cor.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.FOLDER,
          obj_id: folder_id,
          company_id,
          obj_to: 'user',
          obj_to_id: newApiKeyId,
          is_need_to_notify: false,
          privs: [
            { type: 'create', list_obj: ['all'] },
            { type: 'modify', list_obj: ['all'] },
            { type: 'delete', list_obj: ['all'] },
            { type: 'view', list_obj: ['all'] },
          ],
        }),
      );
      expect(responseLink.status).toBe(200);

      const responseProject = await api_user.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.PROJECT,
          company_id,

          title: project_short_name,
          short_name: project_short_name,
          description: 'test',
          stages: [
            {
              title: `develop`,
              immutable: false,
            },
          ],
          status: 'active',
        }),
      );
      project_id = responseProject.body.ops[0].obj_id;
      stage_id = responseProject.body.ops[0].stages[0];

      const responseConv2 = await api_user.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.CONV,
          company_id,
          project_id,
          stage_id,
          title: `Api_Call`,
          conv_type: 'process',
          status: 'actived',
        }),
      );
      conv_id_2 = responseConv2.body.ops[0].obj_id;

      const responseAlias = await api_user.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ALIAS,
          company_id,

          short_name: alias_short_name,
          description: `aliasDesk-${Date.now()}`,
          title: `aliasTitle-${Date.now()}`,
          project_id,
          stage_id,
        }),
      );
      expect(responseAlias.status).toBe(200);
      alias_id = responseAlias.body.ops[0].obj_id;

      const responseList2 = await api_user.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.CONV,
          obj_id: conv_id_2,
          company_id,
          project_id,
          stage_id,
        }),
      );
      expect(responseList2.status).toBe(200);
      final_node_ID_2 = (responseList2.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

      const responseLinkAlias = await api_user.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.ALIAS,
          obj_id: alias_id,
          company_id,

          obj_to_id: conv_id_2,
          obj_to_type: 'conv',
          link: true,
          project_id,
          stage_id,
        }),
      );
      expect(responseLinkAlias.status).toBe(200);

      const responseLinkStage = await api_user.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.STAGE,
          obj_id: stage_id,
          company_id,

          obj_to: 'user',
          obj_to_id: newApiKeyId,
          is_need_to_notify: false,
          privs: [
            { type: 'create', list_obj: ['all'] },
            { type: 'modify', list_obj: ['all'] },
            { type: 'delete', list_obj: ['all'] },
            { type: 'view', list_obj: ['all'] },
          ],
        }),
      );
      expect(responseLinkStage.status).toBe(200);
    },
  );

  test(`create endpoint`, async (): Promise<void> => {
    const response = await request('post', `apis`, { name: 'auto_test', description: '' });
    expect(response.status).toBe(200);
    endpoint_id = response.body.api.id;
    host = response.body.api.host;
  });

  test(`create paths for get method public`, async (): Promise<void> => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path/',
      description: '',
      method: 'GET',
      timeout: 28,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.api_id).toBe(endpoint_id);

    expect(response.body.path.path).toBe('/get/path');
    path = response.body.path.path;
    path_id = response.body.path.id;
  });

  test('create request apiGW method GET', async (): Promise<void> => {
    await new Promise(r => setTimeout(r, 1800));
    const response = await axiosInstance({
      method: 'GET',
      url: `${protocol}//${host}${path}`,
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1200));
    const responseList = await requestList(api_cor, final_node_ID, conv_id, company_id);

    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path);
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('GET');
    SchemaValidator.validate(LisTaskSchema, responseList.body);
  });

  test('create request apiGW method POST, request with GET and POST', async (): Promise<void> => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: '',
      method: 'POST',
      timeout: 28,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
    });
    expect(responseModify.status).toBe(200);
    expect(responseModify.body.path.id).toBe(path_id);

    await new Promise(r => setTimeout(r, 2000));
    const response = await axiosInstance({
      method: 'GET',
      url: `${protocol}//${host}${path}`,
      params: {
        key1: 'value1',
        key2: 'value2',
      },
    });
    expect(response.status).toBe(404);
    expect(response.data.description).toBe('path not found');

    const responsePost = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host}${path}`,
      data: { b: 'param', code: 'AP-236' },
    });
    expect(responsePost.status).toBe(200);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(api_cor, final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path);
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('POST');
    SchemaValidator.validate(LisTaskSchema, responseList.body);
  });

  test('create request apiGW method POST, with headers [X-Task-Format: sync-api]', async (): Promise<void> => {
    const responsePost = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host}${path}`,
      data: { b: 'headers', code: 'AP-236' },
      headers: {
        'X-Task-Format': 'sync-api',
      },
    });
    expect(responsePost.status).toBe(200);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(api_cor, final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path);
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('POST');
    SchemaValidator.validate(listTaskXSchema, responseList.body);
  });

  test(`create paths for post method`, async (): Promise<void> => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/post/path',
      description: '',
      method: 'POST',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      process_id: conv_id,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.api_id).toBe(endpoint_id);
    path_post = response.body.path.path;
    path_id_post = response.body.path.id;
  });

  test('create request apiGW method POST', async (): Promise<void> => {
    await new Promise(r => setTimeout(r, 2000));
    const authHeader = 'Basic ' + Buffer.from(`${newApiKeyLogin}:${newApiKeySecret}`).toString('base64');
    const response = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host}${path_post}`,
      data: { b: 'param', code: 200 },
      headers: {
        Authorization: authHeader,
        origin: host,
      },
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);
    expect(response.data.proxy).toEqual('200');

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(api_cor, final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path_post);
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('POST');
    expect(responseList.body.ops[0].list[0].data.b).toEqual('param');
    expect(responseList.body.ops[0].list[0].data.code).toEqual(200);
    SchemaValidator.validate(LisTaskSchema, responseList.body);
  });

  test('create request apiGW method POST with `/`', async (): Promise<void> => {
    await new Promise(r => setTimeout(r, 1800));
    const authHeader = 'Basic ' + Buffer.from(`${newApiKeyLogin}:${newApiKeySecret}`).toString('base64');
    const response = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host}${path_post}/`,
      data: { b: 'slash', code: 200 },
      headers: {
        Authorization: authHeader,
        origin: host,
      },
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);
    expect(response.data.proxy).toEqual('200');

    await new Promise(r => setTimeout(r, 1200));
    const responseList = await requestList(api_cor, final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path_post);
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('POST');
    expect(responseList.body.ops[0].list[0].data.b).toEqual('slash');
    expect(responseList.body.ops[0].list[0].data.code).toEqual(200);
    SchemaValidator.validate(LisTaskSchema, responseList.body);
  });

  test(`create paths for put method public, async = true`, async (): Promise<void> => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/put',
      description: '',
      method: 'PUT',
      timeout: 2,
      async: true,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.api_id).toBe(endpoint_id);
    path_put = response.body.path.path;
  });

  test(`link endpoint to stage`, async (): Promise<void> => {
    const response = await request('post', `apis/${workspace_id}/${project_id}/${stage_id}`, {
      apiId: endpoint_id,
    });
    expect(response.status).toBe(200);
    expect(response.body.api.id).toEqual(endpoint_id);
  });

  test('create request apiGW method PUT, async = true', async (): Promise<void> => {
    await new Promise(r => setTimeout(r, 2000));
    const response = await axiosInstance({
      method: 'PUT',
      url: `${protocol}//${host}${path_put}`,
      data: { b: 'put', code: 200 },
    });
    expect(response.status).toBe(204);

    await new Promise(r => setTimeout(r, 2000));
    const responseList = await requestList(api_cor, final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path_put);
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('PUT');
    expect(responseList.body.ops[0].list[0].data.b).toEqual('put');
    expect(responseList.body.ops[0].list[0].data.code).toEqual(200);
    SchemaValidator.validate(LisTaskSchema, responseList.body);
  });

  test(`create paths for patch method public, async = true`, async (): Promise<void> => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/patch',
      description: '',
      method: 'PATCH',
      timeout: 15,
      async: true,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id_2,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.api_id).toBe(endpoint_id);
    path_patch = response.body.path.path;
  });

  test('create request apiGW method PATCH, async = true, without node apicall', async (): Promise<void> => {
    await new Promise(r => setTimeout(r, 2000));
    const response = await axiosInstance({
      method: 'PATCH',
      url: `${protocol}//${host}${path_patch}`,
      data: { b: 'patch', code: 200 },
    });
    expect(response.status).toBe(204);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await api_user.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID_2,
        company_id,

        conv_id: conv_id_2,
        limit: 1,
      }),
    );
    expect(responseList.body.ops[0].list[0].data.__request.sync).toEqual(false);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path_patch);
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('PATCH');
    expect(responseList.body.ops[0].list[0].data.b).toEqual('patch');
    expect(responseList.body.ops[0].list[0].data.code).toEqual(200);
  });

  test(`create paths for delete method public`, async (): Promise<void> => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/delete',
      description: '',
      method: 'DELETE',
      timeout: 2,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.api_id).toBe(endpoint_id);
    path_delete = response.body.path.path;
  });

  test('create request apiGW method DELETE', async (): Promise<void> => {
    await new Promise(r => setTimeout(r, 2000));
    const response = await axiosInstance({
      method: 'DELETE',
      url: `${protocol}//${host}${path_delete}`,
    });
    expect(response.status).toBe(200);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(api_cor, final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path_delete);
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('DELETE');
    SchemaValidator.validate(LisTaskSchema, responseList.body);
  });

  test(`create request apiGW method GET with timeout`, async (): Promise<void> => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: '',
      method: 'GET',
      timeout: 1,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id_2,
    });
    expect(responseModify.status).toBe(200);
    expect(responseModify.body.path.id).toBe(path_id);

    await new Promise(r => setTimeout(r, 2000));
    const response = await axiosInstance({
      method: 'GET',
      url: `${protocol}//${host}${path}`,
    });
    expect(response.status).toBe(504);
    expect(response.data.proc).toEqual('error');
    expect(response.data.description).toEqual('System timeout exceeded');
  });

  test('create request apiGW method GET, proxy_headers = true, without node apicall', async (): Promise<void> => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: '',
      method: 'GET',
      timeout: 1,
      async: true,
      proxy_headers: true,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      alias_id,
      alias_short_name,
    });
    expect(responseModify.status).toBe(200);
    expect(responseModify.body.path.id).toBe(path_id);

    await new Promise(r => setTimeout(r, 2000));
    const response = await axiosInstance({
      method: 'GET',
      url: `${protocol}//${host}${path}`,
      params: {
        key1: 'value1',
        key2: 'value2',
      },
    });
    expect(response.status).toBe(204);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await api_user.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID_2,
        company_id,

        conv_id: conv_id_2,
        limit: 1,
      }),
    );
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path);
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('GET');
    SchemaValidator.validate(LisTaskHeadersSchema, responseList.body);
  });

  test('create request apiGW method POST, proxy_headers = true', async (): Promise<void> => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id_post}`, {
      path: '/post/path/',
      description: '',
      method: 'POST',
      timeout: 10,
      async: false,
      proxy_headers: true,
      proxy_raw_body: false,
      process_id: conv_id,
    });
    expect(responseModify.status).toBe(200);
    expect(responseModify.body.path.id).toBe(path_id_post);

    expect(responseModify.body.path.path).toBe('/post/path');

    const authHeader = 'Basic ' + Buffer.from(`${newApiKeyLogin}:${newApiKeySecret}`).toString('base64');

    await new Promise(r => setTimeout(r, 2000));
    const response = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host}${path_post}`,
      data: { b: 'post', code: 200 },
      headers: {
        Authorization: authHeader,
        origin: host,
        ['content-type']: 'application/json',
      },
    });
    expect(response.status).toBe(200);

    await new Promise(r => setTimeout(r, 1300));
    const responseList = await requestList(api_cor, final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path_post);
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('POST');
    expect(responseList.body.ops[0].list[0].data.b).toEqual('post');
    expect(responseList.body.ops[0].list[0].data.code).toEqual(200);
    expect(responseList.body.ops[0].list[0].data.__headers.Authorization).not.toBeString();
    SchemaValidator.validate(LisTaskHeadersSchema, responseList.body);
  });

  test('create request apiGW method POST, proxy_raw_body = true json', async (): Promise<void> => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id_post}`, {
      path: '/post/path',
      description: '',
      method: 'POST',
      timeout: 10,
      async: false,
      proxy_headers: true,
      proxy_raw_body: true,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
    });
    expect(responseModify.status).toBe(200);
    expect(responseModify.body.path.id).toBe(path_id_post);

    await new Promise(r => setTimeout(r, 2000));
    const response = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host}${path_post}`,
      data: { b: 'post', code: 200 },
      headers: {
        origin: host,
        ['content-type']: 'application/json',
      },
    });
    expect(response.status).toBe(200);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(api_cor, final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.host).toBeString();
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path_post);
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('POST');
    expect(responseList.body.ops[0].list[0].data.body).toEqual(`{\"b\":\"post\",\"code\":200}`);
  });

  test('create request apiGW method GET, proxy_raw_body = true, without node apicall', async (): Promise<void> => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: '',
      method: 'GET',
      timeout: 1,
      async: true,
      proxy_headers: true,
      proxy_raw_body: true,
      public: { api_login: newApiKeyLogin },
      alias_id,
      alias_short_name,
    });
    expect(responseModify.status).toBe(200);
    expect(responseModify.body.path.id).toBe(path_id);

    await new Promise(r => setTimeout(r, 2000));
    const response = await axiosInstance({
      method: 'GET',
      url: `${protocol}//${host}${path}`,
      params: {
        key1: 'value1',
        key2: 'value2',
      },
    });
    expect(response.status).toBe(204);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await api_user.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID_2,
        company_id,

        conv_id: conv_id_2,
        limit: 1,
      }),
    );
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path);
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('GET');
    SchemaValidator.validate(LisTaskHeadersSchema, responseList.body);
  });

  test('create request apiGW method POST, proxy_raw_body = true xml', async (): Promise<void> => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id_post}`, {
      path: '/post/path',
      description: '',
      method: 'POST',
      timeout: 10,
      async: false,
      proxy_headers: true,
      proxy_raw_body: true,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
    });
    expect(responseModify.status).toBe(200);
    expect(responseModify.body.path.id).toBe(path_id_post);

    const authHeader = 'Basic ' + Buffer.from(`${newApiKeyLogin}:${newApiKeySecret}`).toString('base64');

    await new Promise(r => setTimeout(r, 2000));
    const response = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host}${path_post}`,
      data: `<b>post</b><code>200</code>`,
      headers: {
        Authorization: authHeader,
        origin: host,
        ['Content-Type']: 'text/xml',
      },
    });
    expect(response.status).toBe(200);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(api_cor, final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.host).toBeString();
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path_post);
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('POST');
    expect(responseList.body.ops[0].list[0].data.__headers.authorization).toBeString();
    expect(responseList.body.ops[0].list[0].data.body).toEqual(`<b>post</b><code>200</code>`);
  });

  test('create request apiGW method POST, conversion from XML to JSON', async (): Promise<void> => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id_post}`, {
      path: '/post/path',
      description: '',
      method: 'POST',
      timeout: 10,
      async: false,
      proxy_headers: true,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
    });
    expect(responseModify.status).toBe(200);
    expect(responseModify.body.path.id).toBe(path_id_post);

    const responseCreateNode = await api_cor.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,

        conv_id,
        title: 'ApiGW',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'POST',
            url: '{{__callback_url}}',
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: final_node_ID,
            extra_headers: { ['content-type']: 'application/xml; charset=utf-8' },
            debug_info: false,
            send_sys: true,
            rfc_format: true,
            cert_pem: '',
            is_migrate: true,
            content_type: 'application/xml',
            customize_response: false,
            version: 2,
            response: {
              header: '{{header}}',
              body: '{{body}}',
            },
            response_type: {
              header: 'object',
              body: 'object',
            },
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);

    const responseCommit = await requestConfirm(api_cor, conv_id, company_id);
    expect(responseCommit.status).toBe(200);

    await new Promise(r => setTimeout(r, 2000));
    const response = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host}${path_post}`,
      data: '<b>post</b><code>200</code>',
      headers: {
        origin: host,
        ['Content-Type']: 'text/xml',
      },
    });
    expect(response.status).toBe(200);
    expect(response.data).toBe(`<?xml version=\"1.0\" encoding=\"UTF-8\" ?><doc></doc>`);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(api_cor, final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.host).toBeString();
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path_post);
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('POST');
    expect(responseList.body.ops[0].list[0].data.b).toEqual('post');
    expect(responseList.body.ops[0].list[0].data.code).toEqual('200');
  });

  test('create request apiGW method POST, conversion_2 from XML to JSON', async (): Promise<void> => {
    await new Promise(r => setTimeout(r, 1800));
    const response = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host}${path_post}`,
      data: `<?xml version="1.0" encoding="UTF-8"?>
      <osm version="0.6" generator="CGImap 0.0.2">
       <bounds minlat="54.0889580" minlon="12.2487570" maxlat="54.0913900" maxlon="12.2524800"/>
       <foo>bar</foo>
       <list>
            <item attr="54">1</item>
            <item attr="55">2</item>
            <item attr="56">3</item>
       </list>
       <exampleOfAComment>
      <!--
          Since this is a comment
          I can use all sorts of reserved characters
          like > < " and &
          or write things like
          <foo></bar>
          but my document is still well-formed!
      -->
      </exampleOfAComment>
      <exampleOfACDATA>
      <![CDATA[
          Since this is a CDATA section
          I can use all sorts of reserved characters
          like > < " and &
          or write things like
          <foo></bar>
          but my document is still well formed!
      ]]>
      </exampleOfACDATA>
      </osm>`,
      headers: {
        origin: host,
        ['Content-Type']: 'text/xml',
      },
    });
    expect(response.status).toBe(200);
    expect(response.data).toBe(`<?xml version=\"1.0\" encoding=\"UTF-8\" ?><doc></doc>`);

    await new Promise(r => setTimeout(r, 1200));
    const responseList = await requestList(api_cor, final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.host).toBeString();
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path_post);
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('POST');
    expect(responseList.body.ops[0].list[0].data.osm).toEqual({
      '@generator': 'CGImap 0.0.2',
      '@version': '0.6',
      bounds: {
        '@maxlat': '54.0913900',
        '@maxlon': '12.2524800',
        '@minlat': '54.0889580',
        '@minlon': '12.2487570',
      },
      exampleOfACDATA:
        'Since this is a CDATA section\n          I can use all sorts of reserved characters\n          like > < " and &\n          or write things like\n          <foo></bar>\n          but my document is still well formed!',
      exampleOfAComment: '',
      foo: 'bar',
      list: {
        item: [
          {
            '#content': '1',
            '@attr': '54',
          },
          {
            '#content': '2',
            '@attr': '55',
          },
          {
            '#content': '3',
            '@attr': '56',
          },
        ],
      },
    });
  });

  test('create request apiGW method POST, conversion from XML to JSON case from WU', async (): Promise<void> => {
    const raw_body = `<?xml version=\"1.0\" encoding=\"UTF-8\"?>
    <NS1:Envelope
        xmlns:NS1=\"http://schemas.xmlsoap.org/soap/envelope/\">
        <NS1:Body>
            <NS2:nis-notification-reply
                xmlns:NS2=\"http://www.test.com/schema/xrsi\">
                <sender>
                    <address>
                        <addr_line1>NEW FALAH</addr_line1>
                        <city>ABU DHABI</city>
                        <state>AE</state>
                        <country_iso_code>AED</country_iso_code>
                    </address>
                    <bank_account>
                        <name>NoBankName</name>
                        <routing_number>NoRoutingNum</routing_number>
                    </bank_account>
                </sender>
                <receiver>
                    <address>
                        <country_iso_code>AED</country_iso_code>
                    </address>
                    <mobile_phone>
                        <phone_number/>
                    </mobile_phone>
                </receiver>
                <payment_details>
                    <origination>
                        <principal_amount>60000</principal_amount>
                        <currency_iso_code>AED</currency_iso_code>
                        <country_iso_code>AED</country_iso_code>
                    </origination>
                    <destination>
                        <expected_payout_amount>41977</expected_payout_amount>
                        <currency_iso_code>AED</currency_iso_code>
                        <country_iso_code>AED</country_iso_code>
                    </destination>
                </payment_details>
                <notification_type>BIS010</notification_type>
                <ack_message>SUCCESS</ack_message>
            </NS2:nis-notification-reply>
        </NS1:Body>
    </NS1:Envelope>`;

    const responseCreateNode = await api_cor.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,

        conv_id,
        title: 'ApiGW',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: 'raw',
            method: 'POST',
            url: '{{__callback_url}}',
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: final_node_ID,
            extra_headers: { ['content-type']: 'application/xml; charset=utf-8' },
            debug_info: false,
            send_sys: true,
            rfc_format: true,
            cert_pem: '',
            is_migrate: true,
            content_type: 'application/xml',
            customize_response: false,
            version: 2,
            raw_body,
            response: {
              header: '{{header}}',
              body: '{{body}}',
            },
            response_type: {
              header: 'object',
              body: 'object',
            },
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);

    const responseCommit = await requestConfirm(api_cor, conv_id, company_id);
    expect(responseCommit.status).toBe(200);

    await new Promise(r => setTimeout(r, 2000));
    const response = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host}${path_post}`,
      data: raw_body,
      headers: {
        origin: host,
        ['Content-Type']: 'application/xml',
      },
    });
    expect(response.status).toBe(200);
    expect(response.headers['content-type']).toBe('application/xml; charset=utf-8');
    expect(response.data).toBe(raw_body);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(api_cor, final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.host).toBeString();
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path_post);
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('POST');
    expect(responseList.body.ops[0].list[0].data.Envelope).toEqual({
      '@NS1': 'http://schemas.xmlsoap.org/soap/envelope/',
      Body: {
        'nis-notification-reply': {
          '@NS2': 'http://www.test.com/schema/xrsi',
          ack_message: 'SUCCESS',
          notification_type: 'BIS010',
          payment_details: {
            destination: {
              country_iso_code: 'AED',
              currency_iso_code: 'AED',
              expected_payout_amount: '41977',
            },
            origination: {
              country_iso_code: 'AED',
              currency_iso_code: 'AED',
              principal_amount: '60000',
            },
          },
          receiver: {
            address: {
              country_iso_code: 'AED',
            },
            mobile_phone: {
              phone_number: '',
            },
          },
          sender: {
            address: {
              addr_line1: 'NEW FALAH',
              city: 'ABU DHABI',
              country_iso_code: 'AED',
              state: 'AE',
            },
            bank_account: {
              name: 'NoBankName',
              routing_number: 'NoRoutingNum',
            },
          },
        },
      },
    });
  });

  test('create request apiGW method GET, proxy_headers = true application/json with an empty data', async (): Promise<
    void
  > => {
    const responseCreateNode = await api_cor.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,

        conv_id,
        title: 'ApiGW',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'POST',
            url: '{{__callback_url}}',
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: final_node_ID,
            extra_headers: { ['content-type']: 'application/json; charset=utf-8' },
            debug_info: false,
            send_sys: true,
            rfc_format: true,
            cert_pem: '',
            is_migrate: true,
            content_type: 'application/json',
            customize_response: false,
            version: 2,
            response: {
              header: '{{header}}',
              body: '{{body}}',
            },
            response_type: {
              header: 'object',
              body: 'object',
            },
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);

    const responseCommit = await requestConfirm(api_cor, conv_id, company_id);
    expect(responseCommit.status).toBe(200);

    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: '',
      method: 'GET',
      timeout: 1,
      async: false,
      proxy_headers: true,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
    });
    expect(responseModify.status).toBe(200);
    expect(responseModify.body.path.id).toBe(path_id);

    await new Promise(r => setTimeout(r, 1800));
    const response = await axiosInstance({
      method: 'GET',
      url: `${protocol}//${host}${path}`,
      params: {
        key1: 'value1',
        key2: 'value2',
      },
      headers: {
        ['Content-Type']: 'application/json',
      },
    });
    expect(response.status).toBe(200);

    await new Promise(r => setTimeout(r, 1200));
    const responseList = await requestList(api_cor, final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.api_gateway.request.path).toEqual(path);
    expect(responseList.body.ops[0].list[0].data.api_gateway.request.method).toEqual('GET');
    SchemaValidator.validate(LisTaskHeadersSchema, responseList.body);

    await new Promise(r => setTimeout(r, 2000));
    const response2 = await axiosInstance({
      method: 'GET',
      url: `${protocol}//${host}${path}`,
      data: {},
      headers: {
        ['Content-Type']: 'application/json',
      },
    });
    expect(response2.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1000));
    const responseList2 = await requestList(api_cor, final_node_ID, conv_id, company_id);
    expect(responseList2.body.ops[0].list[0].data.api_gateway.request.path).toEqual(path);
    expect(responseList2.body.ops[0].list[0].data.api_gateway.request.method).toEqual('GET');
    SchemaValidator.validate(LisTaskHeadersSchema, responseList.body);
  });

  test('create request apiGW method GET, proxy_headers = false application/json with an empty data', async (): Promise<
    void
  > => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: '',
      method: 'GET',
      timeout: 1,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
    });
    expect(responseModify.status).toBe(200);
    expect(responseModify.body.path.id).toBe(path_id);

    await new Promise(r => setTimeout(r, 2000));
    const response = await axiosInstance({
      method: 'GET',
      url: `${protocol}//${host}${path}`,
      params: {
        key1: 'value1',
        key2: 'value2',
      },
      headers: {
        ['Content-Type']: 'application/json',
      },
    });
    expect(response.status).toBe(200);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(api_cor, final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.host).toBeString();
    expect(responseList.body.ops[0].list[0].data.api_gateway.request.path).toEqual(path);
    expect(responseList.body.ops[0].list[0].data.api_gateway.request.method).toEqual('GET');

    await new Promise(r => setTimeout(r, 2000));
    const response2 = await axiosInstance({
      method: 'GET',
      url: `${protocol}//${host}${path}`,
      headers: {
        ['Content-Type']: 'application/json',
      },
    });
    expect(response2.status).toBe(200);
    expect(response2.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1000));
    const responseList2 = await requestList(api_cor, final_node_ID, conv_id, company_id);
    expect(responseList2.body.ops[0].list[0].data.__request.host).toBeString();
    expect(responseList2.body.ops[0].list[0].data.api_gateway.request.path).toEqual(path);
    expect(responseList2.body.ops[0].list[0].data.api_gateway.request.method).toEqual('GET');
  });

  test('create request apiGW method GET, paused=true', async (): Promise<void> => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: '',
      method: 'GET',
      timeout: 1,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
      paused: true,
    });
    expect(responseModify.status).toBe(200);
    expect(responseModify.body.path.id).toBe(path_id);

    await new Promise(r => setTimeout(r, 2000));
    const response = await axiosInstance({
      method: 'GET',
      url: `${protocol}//${host}${path}`,
      params: {
        key1: 'value1',
        key2: 'value2',
      },
    });
    expect(response.status).toBe(404);
    expect(response.data.description).toEqual(`path not found`);
    expect(response.data.proc).toEqual(`error`);
  });

  test('create request apiGW method GET, proxy_headers = false application/xml with an empty data, paused=false', async (): Promise<
    void
  > => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: '',
      method: 'GET',
      timeout: 1,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
      paused: false,
    });
    expect(responseModify.status).toBe(200);
    expect(responseModify.body.path.id).toBe(path_id);

    await new Promise(r => setTimeout(r, 50));
    const response3 = await axiosInstance({
      method: 'GET',
      url: `${protocol}//${host}${path}`,
      headers: {
        ['Content-Type']: 'application/xml',
      },
    });
    expect(response3.status).toBe(200);
    expect(response3.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1000));
    const responseList3 = await requestList(api_cor, final_node_ID, conv_id, company_id);
    expect(responseList3.body.ops[0].list[0].data.api_gateway.request.path).toEqual(path);
    expect(responseList3.body.ops[0].list[0].data.api_gateway.request.method).toEqual('GET');
    SchemaValidator.validate(LisTaskSchema, responseList3.body);
  });

  test('create request apiGW method POST, with application/x-www-form-urlencoded', async (): Promise<void> => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: '',
      method: 'POST',
      timeout: 1,
      async: false,
      proxy_headers: true,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
      paused: false,
    });
    expect(responseModify.status).toBe(200);
    expect(responseModify.body.path.id).toBe(path_id);

    await new Promise(r => setTimeout(r, 50));

    const data = new URLSearchParams();
    data.append('key1', 'value1');
    data.append('key2', '323123123123');
    data.append('key3', 'application/x-www-form-urlencoded');

    const response3 = await axiosInstance.post(`${protocol}//${host}${path}`, data.toString(), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    expect(response3.status).toBe(200);
    expect(response3.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1000));
    const responseList3 = await requestList(api_cor, final_node_ID, conv_id, company_id);
    expect(responseList3.body.ops[0].list[0].data.api_gateway.request.path).toEqual(path);
    expect(responseList3.body.ops[0].list[0].data.api_gateway.request.method).toEqual('POST');
    expect(responseList3.body.ops[0].list[0].data.api_gateway.request.body.key3).toEqual(
      'application/x-www-form-urlencoded',
    );
    expect(responseList3.body.ops[0].list[0].data.api_gateway.request.body.key1).toEqual('value1');
  });

  test(`update project_short_name and stage_short_name after create path`, async (): Promise<void> => {
    const responseProject = await api_user.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.PROJECT,
        company_id: workspace_id,
        obj_id: project_id,

        title: project_short_name,
        short_name: 'modify',
        description: 'test',
      }),
    );
    expect(responseProject.status).toBe(200);

    const responseStage = await api_user.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.PROJECT,
        company_id: workspace_id,
        obj_id: stage_id,

        title: stage_short_name,
        short_name: 'modify',
        description: 'test',
      }),
    );
    expect(responseStage.status).toBe(200);

    const responseShow = await request('get', `apis/${endpoint_id}`);
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.api.stage_short_name).toEqual('develop');
    expect(responseShow.body.api.project_short_name).toEqual(project_short_name);

    const responseCreate = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path/mod',
      description: 'test_description',
      method: 'GET',
      timeout: 29,
      async: true,
      proxy_headers: true,
      proxy_raw_body: true,
      public: { api_login: 777 },
      process_id: 3,
      paused: true,
    });
    expect(responseCreate.status).toBe(200);
    expect(responseCreate.body.path.api_id).toBe(endpoint_id);
    path_id_modify = responseCreate.body.path.id;

    const responseShow2 = await request('get', `apis/${endpoint_id}`);
    expect(responseShow2.status).toBe(200);
    expect(responseShow2.body.api.stage_short_name).toEqual('modify');
    expect(responseShow2.body.api.project_short_name).toEqual('modify');
  });

  test(`update project_short_name and stage_short_name after modify path`, async (): Promise<void> => {
    const responseProject = await api_user.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.PROJECT,
        company_id: workspace_id,
        obj_id: project_id,

        title: project_short_name,
        short_name: project_short_name,
        description: 'test',
      }),
    );
    expect(responseProject.status).toBe(200);

    const responseStage = await api_user.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.PROJECT,
        company_id: workspace_id,
        obj_id: stage_id,

        title: stage_short_name,
        short_name: project_short_name,
        description: 'test',
      }),
    );
    expect(responseStage.status).toBe(200);

    const responseShow = await request('get', `apis/${endpoint_id}`);
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.api.stage_short_name).toEqual('modify');
    expect(responseShow.body.api.project_short_name).toEqual('modify');

    const responseCreate = await request('put', `apis/${endpoint_id}/paths/${path_id_modify}`, {
      path: '/get/path/mod',
      description: 'test_description',
      method: 'POST',
      timeout: 29,
      async: true,
      proxy_headers: true,
      proxy_raw_body: true,
      public: { api_login: 777 },
      process_id: 3,
      paused: true,
    });
    expect(responseCreate.status).toBe(200);
    expect(responseCreate.body.path.api_id).toBe(endpoint_id);

    const responseShow2 = await request('get', `apis/${endpoint_id}`);
    expect(responseShow2.status).toBe(200);
    expect(responseShow2.body.api.stage_short_name).toEqual(project_short_name);
    expect(responseShow2.body.api.project_short_name).toEqual(project_short_name);
  });

  test(`delete endpoint`, async (): Promise<void> => {
    const response = await request('delete', `apis/${endpoint_id}`, {});
    expect(response.status).toBe(204);
  });

  afterAll(
    async (): Promise<void> => {
      const response = await requestDeleteObj(api_cor, OBJ_TYPE.FOLDER, folder_id, company_id);
      expect(response.status).toBe(200);

      const ResponseDeleteApiKey = await requestDeleteObj(api_cor, OBJ_TYPE.USER, newApiKeyId, company_id);
      expect(ResponseDeleteApiKey.status).toBe(200);

      const responseProject = await api_user.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.PROJECT,
          obj_id: project_id,
          company_id,
        }),
      );
      expect(responseProject.status).toBe(200);
    },
  );
});
