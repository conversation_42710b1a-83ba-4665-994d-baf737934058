import { request } from '../../../application/api/ApiGWUserClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import { axiosInstance } from '../../../application/api/AxiosClient';
import { requestConfirm, requestDeleteObj, requestCreateObj, requestListConv } from '../../../application/api/ApiObj';
import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';

describe('ApiGWCor (negative)', () => {
  let api_cor: ApiKeyClient;
  let apikey: ApiKey;
  let endpoint_id: number;
  let company_id: any;
  let path: string;
  let path_id: number;
  let host: string;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let conv_id: number;
  let newApiKeyId: string | number;
  let newApiKeyLogin: number;
  let newApiKeySecret: string | number;
  let protocol: string;
  let alias_short_name: string;
  let alias_id: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api_cor = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    const parsedUrl = new URL(`${ConfigurationManager.getConfiguration().getApiGWUrl()}`);
    protocol = parsedUrl.protocol;
    alias_short_name = `alias-${Date.now()}-alias`;

    const CreateKeyResponse = await api_cor.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(200);
    const user_api = CreateKeyResponse.body;
    const newApiKey = {
      key: `${user_api.ops[0].users[0].logins[0].obj_id}`,
      secret: user_api.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user_api.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newApiKeyId = +newApiKey.id;
    newApiKeyLogin = +newApiKey.key;
    newApiKeySecret = newApiKey.secret;

    const responseConv = await requestCreateObj(api_cor, OBJ_TYPE.CONV, company_id, `Api_Call`);
    conv_id = responseConv.body.ops[0].obj_id;

    const responseList = await requestListConv(api_cor, conv_id, company_id);
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateNode = await api_cor.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'ApiGW',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'POST',
            url: '{{__callback_url}}',
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: '',
            extra_headers: { ['content-type']: 'application/json; charset=utf-8' },
            debug_info: false,
            send_sys: true,
            rfc_format: true,
            cert_pem: '',
            content_type: 'application/json',
            version: 2,
            response: {
              header: '{{header}}',
              body: '{{body}}',
            },
            response_type: {
              header: 'object',
              body: 'object',
            },
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);

    const responseCommit = await requestConfirm(api_cor, conv_id, company_id);
    expect(responseCommit.status).toBe(200);

    const responseAlias = await api_cor.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: alias_short_name,
        title: `alias-${Date.now()}`,
      }),
    );
    expect(responseAlias.status).toBe(200);
    alias_id = responseAlias.body.ops[0].obj_id;
  });

  test(`create endpoint`, async () => {
    const response = await request('post', `apis`, { name: 'auto_test', description: '' });
    expect(response.status).toBe(200);
    endpoint_id = response.body.api.id;
    host = response.body.api.host;
  });

  test(`create paths for get method public with not valid key`, async () => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path',
      description: '',
      method: 'GET',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: 12345678 },
      process_id: conv_id,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.api_id).toBe(endpoint_id);
    path = response.body.path.path;
    path_id = response.body.path.id;
  });

  test(`create the same path - error`, async () => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path',
      description: '',
      method: 'GET',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: 12345678 },
      process_id: conv_id,
    });
    expect(response.status).toBe(400);
    expect(response.body.description).toBe('path already taken');
    expect(response.body.proc).toBe('error');
  });

  test('create request apiGW if not found login', async () => {
    await new Promise(r => setTimeout(r, 4000));
    const uri = `${protocol}//${host}${path}`;
    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(401);
    expect(response.data.description).toEqual('op proc error: not found login');
    expect(response.data.proc).toEqual('error');
  });

  test(`modify paths with valid key`, async () => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: '',
      method: 'GET',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.api_id).toBe(endpoint_id);
  });

  test('create request apiGW public if access denied', async () => {
    await new Promise(r => setTimeout(r, 4000));
    const uri = `${protocol}//${host}${path}`;
    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(400);
    expect(response.data.description).toEqual(`user: ${newApiKeyId}, conv_id: ${conv_id}`);
    expect(response.data.proc).toEqual('access_denied');
  });

  test(`modify paths not public`, async () => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: '',
      method: 'GET',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      process_id: conv_id,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.api_id).toBe(endpoint_id);
  });

  test('create request apiGW not publci if reqired authorization', async () => {
    await new Promise(r => setTimeout(r, 4000));
    const uri = `${protocol}//${host}${path}`;
    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(401);
    expect(response.data.description).toEqual(`request rejected: reqired authorization`);
    expect(response.data.proc).toEqual('error');
  });

  test('create request apiGW not publci if access denied', async () => {
    const authHeader = 'Basic ' + Buffer.from(`${newApiKeyLogin}:${newApiKeySecret}`).toString('base64');
    const uri = `${protocol}//${host}${path}`;
    const response = await axiosInstance({
      method: 'GET',
      url: uri,
      headers: {
        Authorization: authHeader,
      },
    });
    expect(response.status).toBe(400);
    expect(response.data.description).toEqual(`user: ${newApiKeyId}, conv_id: ${conv_id}`);
    expect(response.data.proc).toEqual('access_denied');
  });

  test(`create GET/PUT request in apiGW with path POST method`, async () => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/post/path',
      description: '',
      method: 'POST',
      timeout: 20,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
    });
    expect(responseModify.status).toBe(200);
    expect(responseModify.body.path.api_id).toBe(endpoint_id);
    path = responseModify.body.path.path;

    await new Promise(r => setTimeout(r, 2000));

    const uri = `${protocol}//${host}${path}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
    });
    expect(response.status).toBe(400);
    expect(response.data.proc).toEqual(`access_denied`);

    await new Promise(r => setTimeout(r, 4000));
    const response2 = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response2.status).toBe(404);
    expect(response2.data.description).toEqual(`path not found`);
    expect(response2.data.proc).toEqual('error');

    const response3 = await axiosInstance({
      method: 'PUT',
      url: uri,
    });
    expect(response3.status).toBe(404);
    expect(response3.data.description).toEqual(`path not found`);
  });

  test(`create POST/PATCH/DELETE request in apiGW with path GET method`, async () => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: '',
      method: 'GET',
      timeout: 20,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
    });
    expect(responseModify.status).toBe(200);
    expect(responseModify.body.path.api_id).toBe(endpoint_id);
    path = responseModify.body.path.path;

    await new Promise(r => setTimeout(r, 50));

    const uri = `${protocol}//${host}${path}`;
    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(400);
    expect(response.data.proc).toEqual(`access_denied`);

    await new Promise(r => setTimeout(r, 4000));
    const response2 = await axiosInstance({
      method: 'POST',
      url: uri,
    });
    expect(response2.status).toBe(404);
    expect(response2.data.description).toEqual(`path not found`);
    expect(response2.data.proc).toEqual('error');

    const response3 = await axiosInstance({
      method: 'PATCH',
      url: uri,
    });
    expect(response3.status).toBe(404);
    expect(response3.data.description).toEqual(`path not found`);

    const response4 = await axiosInstance({
      method: 'DELETE',
      url: uri,
    });
    expect(response4.status).toBe(404);
    expect(response4.data.description).toEqual(`path not found`);
  });

  test(`create request in apiGW with alias id - Object alias not found`, async () => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: '',
      method: 'GET',
      timeout: 20,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      alias_id,
      alias_short_name,
    });
    expect(responseModify.status).toBe(200);
    expect(responseModify.body.path.api_id).toBe(endpoint_id);
    path = responseModify.body.path.path;

    await new Promise(r => setTimeout(r, 50));

    const uri = `${protocol}//${host}${path}`;
    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(400);
    expect(response.data.description).toEqual(`Object alias not found`);
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api_cor, OBJ_TYPE.CONV, conv_id, company_id);
    expect(response.status).toBe(200);

    const ResponseDeleteApiKey = await requestDeleteObj(api_cor, OBJ_TYPE.USER, newApiKeyId, company_id);
    expect(ResponseDeleteApiKey.status).toBe(200);

    const responseDeleteEndpoint = await request('delete', `apis/${endpoint_id}`, {});
    expect(responseDeleteEndpoint.status).toBe(204);

    const responseAlias = await api_cor.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: alias_id,
        company_id,
      }),
    );
    expect(responseAlias.status).toBe(200);
  });
});
