{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["apis"], "additionalProperties": false, "properties": {"apis": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["project_id", "workspace_id", "stage_short_name", "stage_id", "project_short_name", "description", "id", "host", "name", "updated_at", "owner_id", "created_at"], "additionalProperties": false, "properties": {"project_id": {"type": "integer"}, "workspace_id": {"type": "string"}, "stage_short_name": {"type": "string"}, "stage_id": {"type": "integer"}, "project_short_name": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "string"}, "host": {"type": "string"}, "name": {"type": "string"}, "updated_at": {"type": "integer"}, "owner_id": {"type": "integer"}, "created_at": {"type": "integer"}, "paths": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["description", "api_id", "url", "method", "path", "id", "owner_id", "created_at", "timeout", "updated_at", "paused", "proxy_headers", "proxy_raw_body", "async"], "additionalProperties": false, "properties": {"description": {"type": "string"}, "process_id": {"type": "integer"}, "api_id": {"type": "string"}, "url": {"type": "string"}, "method": {"type": "string"}, "path": {"type": "string"}, "id": {"type": "integer"}, "owner_id": {"type": "integer"}, "created_at": {"type": "integer"}, "timeout": {"type": "integer"}, "updated_at": {"type": "integer"}, "paused": {"type": "boolean"}, "proxy_headers": {"type": "boolean"}, "proxy_raw_body": {"type": "boolean"}, "async": {"type": "boolean"}, "alias_id": {"type": "integer"}, "alias_short_name": {"type": "string"}, "public": {"type": "object", "required": ["api_login"], "additionalProperties": false, "properties": {"api_login": {"type": "integer"}}}}}, "uniqueItems": true}, "external_id": {"type": "string"}}}, "uniqueItems": true}}}