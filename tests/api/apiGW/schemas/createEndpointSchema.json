{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["api"], "additionalProperties": false, "properties": {"api": {"type": "object", "required": ["workspace_id", "description", "stage_short_name", "stage_id", "project_short_name", "project_id", "id", "name", "host", "updated_at", "owner_id", "created_at"], "additionalProperties": false, "properties": {"workspace_id": {"type": ["null", "string"]}, "description": {"type": "string"}, "stage_short_name": {"type": ["null", "string"]}, "external_id": {"type": "string"}, "stage_id": {"type": ["null", "integer"]}, "project_short_name": {"type": ["null", "string"]}, "project_id": {"type": ["null", "integer"]}, "id": {"type": "string"}, "name": {"type": "string"}, "host": {"type": "string"}, "updated_at": {"type": "integer"}, "owner_id": {"type": "integer"}, "created_at": {"type": "integer"}}}}}