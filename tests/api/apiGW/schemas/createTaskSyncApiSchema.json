{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["proc", "data"], "additionalProperties": false, "properties": {"proc": {"type": "string"}, "data": {"type": "object", "required": ["a"], "additionalProperties": false, "properties": {"a": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["book"], "additionalProperties": false, "properties": {"book": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["@category", "title", "author", "year", "price"], "additionalProperties": false, "properties": {"@category": {"type": "string"}, "title": {"type": "object", "required": ["@lang", "#value"], "additionalProperties": false, "properties": {"@lang": {"type": "string"}, "#value": {"type": "string"}}}, "author": {"type": "object", "required": ["#value"], "additionalProperties": false, "properties": {"#value": {"type": "string"}}}, "year": {"type": "object", "required": ["#value"], "additionalProperties": false, "properties": {"#value": {"type": "string"}}}, "price": {"type": "object", "required": ["#value"], "additionalProperties": false, "properties": {"#value": {"type": "string"}}}}}, "uniqueItems": true}}}, "uniqueItems": true}}}}}, "uniqueItems": true}}}