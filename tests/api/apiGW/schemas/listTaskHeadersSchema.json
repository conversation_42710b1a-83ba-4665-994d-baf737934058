{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "obj_id", "count", "proc", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "string"}, "count": {"type": "integer"}, "proc": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["data", "obj", "obj_id", "ref", "status", "user_id", "create_time", "change_time", "node_prev_id"], "additionalProperties": false, "properties": {"data": {"type": "object", "required": ["__callback_url", "__headers", "__request", "api_gateway"], "additionalProperties": false, "properties": {"__callback_url": {"type": "string"}, "__headers": {"type": "object", "required": ["user-agent"], "additionalProperties": false, "properties": {"authorization": {"type": "string"}, "content-type": {"type": "string"}, "origin": {"type": "string"}, "user-agent": {"type": "string"}, "x-forwarded-for": {"type": "string"}, "x-forwarded-host": {"type": "string"}, "x-forwarded-port": {"type": "string"}, "x-forwarded-proto": {"type": "string"}, "x-forwarded-scheme": {"type": "string"}, "x-real-ip": {"type": "string"}, "x-request-id": {"type": "string"}, "x-scheme": {"type": "string"}}}, "__request": {"type": "object", "required": ["host", "method", "path", "sync"], "additionalProperties": false, "properties": {"host": {"type": "string"}, "method": {"type": "string"}, "path": {"type": "string"}, "query": {"type": "string"}, "query_params": {"type": "object"}, "sync": {"type": "boolean"}}}, "api_gateway": {"type": "object", "required": ["callback_url", "request"], "additionalProperties": false, "properties": {"callback_url": {"type": "string"}, "request": {"type": "object", "required": ["headers", "host", "method", "path", "sync"], "additionalProperties": false, "properties": {"body": {"type": "object", "required": [], "additionalProperties": false, "properties": {"b": {"type": "string"}, "code": {"type": "integer"}}}, "headers": {"type": "object", "required": ["Accept", "User-Agent"], "additionalProperties": false, "properties": {"Accept": {"type": "string"}, "Connection": {"type": "string"}, "Authorization": {"type": "string"}, "Content-Length": {"type": "string"}, "Content-Type": {"type": "string"}, "Origin": {"type": "string"}, "User-Agent": {"type": "string"}, "X-Forwarded-For": {"type": "string"}, "X-Forwarded-Host": {"type": "string"}, "X-Forwarded-Port": {"type": "string"}, "X-Forwarded-Proto": {"type": "string"}, "X-Forwarded-Scheme": {"type": "string"}, "X-Real-Ip": {"type": "string"}, "X-Request-Id": {"type": "string"}, "X-Scheme": {"type": "string"}}}, "host": {"type": "string"}, "method": {"type": "string"}, "path": {"type": "string"}, "query": {"type": "string"}, "query_params": {"type": "object"}, "sync": {"type": "boolean"}}}}}, "request_proc": {"type": "string"}, "b": {"type": "string"}, "code": {"type": "integer"}}}, "obj": {"type": "string"}, "obj_id": {"type": "string"}, "ref": {"type": "null"}, "status": {"type": "string"}, "user_id": {"type": "integer"}, "create_time": {"type": "integer"}, "change_time": {"type": "integer"}, "node_prev_id": {"type": "string"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}