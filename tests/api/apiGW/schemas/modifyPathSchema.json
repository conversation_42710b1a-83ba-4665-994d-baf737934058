{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["path"], "additionalProperties": false, "properties": {"path": {"type": "object", "required": ["path", "url", "api_id", "method", "id", "owner_id", "created_at", "timeout", "updated_at", "paused", "proxy_headers", "proxy_raw_body", "async"], "additionalProperties": false, "properties": {"public": {"type": "object", "required": ["api_login"], "additionalProperties": false, "properties": {"api_login": {"type": "integer"}}}, "path": {"type": "string"}, "url": {"type": "string"}, "description": {"type": "string"}, "alias_id": {"type": "integer"}, "alias_short_name": {"type": "string"}, "api_id": {"type": "string"}, "method": {"type": "string"}, "id": {"type": "integer"}, "owner_id": {"type": "integer"}, "process_id": {"type": "integer"}, "created_at": {"type": "integer"}, "timeout": {"type": "integer"}, "updated_at": {"type": "integer"}, "paused": {"type": "boolean"}, "proxy_headers": {"type": "boolean"}, "proxy_raw_body": {"type": "boolean"}, "async": {"type": "boolean"}}}}}