{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "obj_id", "count", "proc", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "string"}, "count": {"type": "integer"}, "proc": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["data", "obj", "obj_id", "ref", "status", "user_id", "create_time", "change_time", "node_prev_id"], "additionalProperties": false, "properties": {"data": {"type": "object", "required": ["__callback_url", "__headers", "bookstore", "request_proc"], "additionalProperties": false, "properties": {"__callback_url": {"type": "string"}, "__headers": {"type": "object", "required": ["content-type", "user-agent"], "additionalProperties": false, "properties": {"content-type": {"type": "string"}, "user-agent": {"type": "string"}, "x-forwarded-for": {"type": "string"}, "x-forwarded-host": {"type": "string"}, "x-forwarded-port": {"type": "string"}, "x-forwarded-proto": {"type": "string"}, "x-forwarded-scheme": {"type": "string"}, "x-real-ip": {"type": "string"}, "x-request-id": {"type": "string"}, "x-scheme": {"type": "string"}}}, "bookstore": {"type": "object", "required": ["book"], "additionalProperties": false, "properties": {"book": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["@category", "title", "author", "year", "price"], "additionalProperties": false, "properties": {"@category": {"type": "string"}, "title": {"type": "object", "required": ["@lang", "#value"], "additionalProperties": false, "properties": {"@lang": {"type": "string"}, "#value": {"type": "string"}}}, "author": {"type": "object", "required": ["#value"], "additionalProperties": false, "properties": {"#value": {"type": "string"}}}, "year": {"type": "object", "required": ["#value"], "additionalProperties": false, "properties": {"#value": {"type": "string"}}}, "price": {"type": "object", "required": ["#value"], "additionalProperties": false, "properties": {"#value": {"type": "string"}}}}}, "uniqueItems": true}}}, "request_proc": {"type": "string"}, "task": {"type": "integer"}, "test": {"type": "string"}}}, "obj": {"type": "string"}, "obj_id": {"type": "string"}, "ref": {"type": "string"}, "status": {"type": "string"}, "user_id": {"type": "integer"}, "create_time": {"type": "integer"}, "change_time": {"type": "integer"}, "node_prev_id": {"type": "string"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}