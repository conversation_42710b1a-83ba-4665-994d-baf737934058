{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "obj_id", "count", "proc", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "string"}, "count": {"type": "integer"}, "proc": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["data", "obj", "obj_id", "ref", "status", "user_id", "create_time", "change_time", "node_prev_id"], "additionalProperties": false, "properties": {"data": {"type": "object", "required": ["__callback_url", "__request", "b", "code", "request_proc"], "additionalProperties": false, "properties": {"__callback_url": {"type": "string"}, "__request": {"type": "object", "required": ["host", "method", "path", "sync"], "additionalProperties": false, "properties": {"host": {"type": "string"}, "method": {"type": "string"}, "path": {"type": "string"}, "sync": {"type": "boolean"}}}, "b": {"type": "string"}, "code": {"type": "string"}, "request_proc": {"type": "string"}}}, "obj": {"type": "string"}, "obj_id": {"type": "string"}, "ref": {"type": "null"}, "status": {"type": "string"}, "user_id": {"type": "integer"}, "create_time": {"type": "integer"}, "change_time": {"type": "integer"}, "node_prev_id": {"type": "string"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}