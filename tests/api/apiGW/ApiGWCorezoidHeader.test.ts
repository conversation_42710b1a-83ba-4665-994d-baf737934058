import { request } from '../../../application/api/ApiGWUserClient';
import { application } from '../../../application/Application';
import { Api<PERSON>ey } from '../../../infrastructure/model/ApiKey';
import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import { axiosInstance } from '../../../application/api/AxiosClient';
import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';
import { requestConfirm, requestDeleteObj, requestCreateObj, requestListConv } from '../../../application/api/ApiObj';

describe('ApiGWCorezoidHeader', () => {
  let api_cor: ApiKeyClient;
  let apikey: ApiKey;
  let endpoint_id: number;
  let company_id: any;
  let path: string;
  let host: string;
  let uri: string;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let conv_id: number;
  let path_id: number;
  let newApiKeyId: string | number;
  let newApiKeyLogin: number;
  let protocol: string;

  const createRequestAndUpdateNode = async (extraHeaders: any): Promise<void> => {
    const responseCreateNode = await api_cor.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'ApiGW',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'POST',
            url: '{{__callback_url}}',
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: '',
            extra_headers: extraHeaders,
            debug_info: false,
            send_sys: true,
            rfc_format: true,
            cert_pem: '',
            content_type: 'application/json',
            version: 2,
            raw_body: ``,
            response: {
              header: '{{header}}',
              body: '{{body}}',
            },
            response_type: {
              header: 'object',
              body: 'object',
            },
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);

    const responseCommit = await api_cor.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        conv_id,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
  };

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api_cor = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;
      const parsedUrl = new URL(`${ConfigurationManager.getConfiguration().getApiGWUrl()}`);
      protocol = parsedUrl.protocol;

      const CreateKeyResponse = await api_cor.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.USER,
          company_id,
          title: 'API',
          logins: [{ type: 'api' }],
        }),
      );
      expect(CreateKeyResponse.status).toBe(200);
      const user_api = CreateKeyResponse.body;
      const newApiKey = {
        key: `${user_api.ops[0].users[0].logins[0].obj_id}`,
        secret: user_api.ops[0].users[0].logins[0].key,
        companies: [],
        title: '',
        id: `${user_api.ops[0].users[0].obj_id}`,
      } as ApiKey;
      newApiKeyId = +newApiKey.id;
      newApiKeyLogin = +newApiKey.key;

      const responseConv = await requestCreateObj(api_cor, OBJ_TYPE.CONV, company_id, `Api_Call`);
      conv_id = responseConv.body.ops[0].obj_id;

      const responseList = await requestListConv(api_cor, conv_id, company_id);
      expect(responseList.status).toBe(200);
      process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
      final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

      const extraHeaders = { ['content-type']: 'application/json; charset=utf-8', ['Cz-Ag-Status-Code']: '202' };
      await createRequestAndUpdateNode(extraHeaders);

      const responseLink = await api_cor.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.CONV,
          obj_id: conv_id,
          company_id,
          obj_to: 'user',
          obj_to_id: newApiKeyId,
          is_need_to_notify: false,
          privs: [
            { type: 'create', list_obj: ['all'] },
            { type: 'modify', list_obj: ['all'] },
            { type: 'delete', list_obj: ['all'] },
            { type: 'view', list_obj: ['all'] },
          ],
        }),
      );
      expect(responseLink.status).toBe(200);

      const response = await request('post', `apis`, { name: 'auto_test', description: '' });
      expect(response.status).toBe(200);
      endpoint_id = response.body.api.id;
      host = response.body.api.host;

      const responsePath = await request('post', `apis/${endpoint_id}/paths`, {
        path: '/get/path',
        description: '',
        method: 'GET',
        timeout: 29,
        async: false,
        proxy_headers: false,
        proxy_raw_body: false,
        public: { api_login: newApiKeyLogin },
        process_id: conv_id,
      });
      expect(responsePath.status).toBe(200);
      expect(responsePath.body.path.api_id).toBe(endpoint_id);
      path = responsePath.body.path.path;
      path_id = responsePath.body.path.id;
      uri = `${protocol}//${host}${path}`;
    },
  );

  test('create request apiGW with Cz-Ag-Status-Code:202', async (): Promise<void> => {
    await new Promise(r => setTimeout(r, 4000));
    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(202);
    expect(response.data.sys.conv_id).toEqual(conv_id);
  });

  test('create request apiGW with Cz-Ag-Status-Code:302', async (): Promise<void> => {
    const location = `${ConfigurationManager.getConfiguration().getApiGWUrl()}auth/me`;

    const extraHeaders = {
      ['content-type']: 'application/json; charset=utf-8',
      ['Cz-Ag-Status-Code']: '302',
      ['Cz-Ag-Location']: location,
    };
    await createRequestAndUpdateNode(extraHeaders);

    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(401);
  });

  test('create request apiGW with Cz-Ag-Status-Code:403', async (): Promise<void> => {
    const extraHeaders = { ['content-type']: 'application/json; charset=utf-8', ['Cz-Ag-Status-Code']: '403' };
    await createRequestAndUpdateNode(extraHeaders);

    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(403);
    expect(response.data.sys.conv_id).toEqual(conv_id);
  });

  test('create request apiGW with Cz-Ag-Status-Code:504', async (): Promise<void> => {
    const extraHeaders = { ['content-type']: 'application/json; charset=utf-8', ['Cz-Ag-Status-Code']: '504' };
    await createRequestAndUpdateNode(extraHeaders);

    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(504);
    expect(response.data.sys.conv_id).toEqual(conv_id);
  });

  test('create request apiGW with X-Status-Code:199', async (): Promise<void> => {
    const extraHeaders = { ['content-type']: 'application/json; charset=utf-8', ['X-Status-Code']: '199' };
    await createRequestAndUpdateNode(extraHeaders);

    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);
  });

  test('create request apiGW with X-Status-Code:200 and response html', async (): Promise<void> => {
    const raw_body = `<div class=\"ed__topSharedSession\" el=\"TopSharedSession\" hidden=\"\"><div class=\"ed__topSharedSession__text__container\"><div style=\"width: 140px;\"></div><div class=\"ed__topSharedSession__text\" el=\"TopSharedSeessionText\">You logged as  </div></div><div class=\"ed__topSharedSession__buttonGroup\"><button type=\"button\" class=\"button__blue\" el=\"SuperadminShared\">Back to Superadmin</button><button type=\"button\" class=\"button__red\" el=\"DisconnectShared\">Disconnect</button></div></div>`;
    const responseCreateNode = await api_cor.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'ApiGW',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: 'raw',
            method: 'POST',
            url: '{{__callback_url}}',
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: '',
            extra_headers: { ['X-Status-Code']: '200', ['Cz-Ag-content-type']: 'text/html' },
            debug_info: false,
            send_sys: true,
            rfc_format: true,
            cert_pem: '',
            content_type: 'application/json',
            version: 2,
            raw_body,
            response: {
              header: '{{header}}',
              body: '{{body}}',
            },
            response_type: {
              header: 'object',
              body: 'object',
            },
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);

    const responseCommit = await requestConfirm(api_cor, conv_id, company_id);
    expect(responseCommit.status).toBe(200);

    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(200);
    expect(response.headers['content-type']).toBe('text/html');
    expect(response.data).toEqual(raw_body);
  });

  test('create request apiGW with X-Status-Code:203', async (): Promise<void> => {
    const extraHeaders = { ['content-type']: 'application/json; charset=utf-8', ['X-Status-Code']: '203' };
    await createRequestAndUpdateNode(extraHeaders);

    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(203);
    expect(response.data.sys.conv_id).toEqual(conv_id);
  });

  test('create request apiGW with X-Status-Code:308', async (): Promise<void> => {
    const extraHeaders = { ['content-type']: 'application/json; charset=utf-8', ['X-Status-Code']: '308' };
    await createRequestAndUpdateNode(extraHeaders);

    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(308);
    expect(response.data.sys.conv_id).toEqual(conv_id);
  });

  test('create request apiGW with X-Status-Code:404', async (): Promise<void> => {
    const extraHeaders = { ['content-type']: 'application/json; charset=utf-8', ['X-Status-Code']: '404' };
    await createRequestAndUpdateNode(extraHeaders);

    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(404);
    expect(response.data.sys.conv_id).toEqual(conv_id);
  });

  test('create request apiGW with X-Status-Code:500', async (): Promise<void> => {
    const extraHeaders = { ['content-type']: 'application/json; charset=utf-8', ['X-Status-Code']: '500' };
    await createRequestAndUpdateNode(extraHeaders);

    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(500);
    expect(response.data.sys.conv_id).toEqual(conv_id);
  });

  test('create request apiGW with X-Status-Code:599', async (): Promise<void> => {
    const extraHeaders = { ['content-type']: 'application/json; charset=utf-8', ['X-Status-Code']: '599' };
    await createRequestAndUpdateNode(extraHeaders);

    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(599);
    expect(response.data.sys.conv_id).toEqual(conv_id);
  });

  test('create request apiGW with X-Status-Code:600', async (): Promise<void> => {
    const extraHeaders = { ['content-type']: 'application/json; charset=utf-8', ['X-Status-Code']: '600' };
    await createRequestAndUpdateNode(extraHeaders);

    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);
  });

  test('create request apiGW with X-Status-Code:test', async (): Promise<void> => {
    const extraHeaders = { ['content-type']: 'application/json; charset=utf-8', ['X-Status-Code']: 'test' };
    await createRequestAndUpdateNode(extraHeaders);

    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);
  });

  test('create request apiGW with X-Status-Code:500 async=true', async (): Promise<void> => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: '',
      method: 'GET',
      timeout: 29,
      async: true,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
    });
    expect(responseModify.status).toBe(200);

    await new Promise(r => setTimeout(r, 4000));

    const extraHeaders = { ['content-type']: 'application/json; charset=utf-8', ['X-Status-Code']: '500' };
    await createRequestAndUpdateNode(extraHeaders);

    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(204);
  });

  afterAll(
    async (): Promise<void> => {
      const responseDeleteEndpoint = await request('delete', `apis/${endpoint_id}`, {});
      expect(responseDeleteEndpoint.status).toBe(204);

      const response = await requestDeleteObj(api_cor, OBJ_TYPE.CONV, conv_id, company_id);
      expect(response.status).toBe(200);

      const ResponseDeleteApiKey = await requestDeleteObj(api_cor, OBJ_TYPE.USER, newApiKeyId, company_id);
      expect(ResponseDeleteApiKey.status).toBe(200);
    },
  );
});
