import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithObj, createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import { User } from '../../../infrastructure/model/User';
import { axiosInstance } from '../../../application/api/AxiosClient';
import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';
import { requestConfirm, requestDeleteObj, requestCreateObj, requestListConv } from '../../../application/api/ApiObj';

describe('SyncApi Task (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: number;
  let newConv1: string | number;
  let newConv2: string | number;
  let nodeLogic: string;
  let nodeStart: string;
  let nodeFinal: string;
  let companyId: any;
  let newNodeApi: string;
  let newApi: ApiKeyClient;
  let ApiKey2: string | number;
  let user: User;
  let cookie: string;

  beforeAll(async () => {
    user = await application.getAuthorizedUser({ superadmin: true });
    cookie = user.cookieUser;
  });

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    companyId = apikey.companies[0].id;

    const responseConv = await requestCreateObj(api, OBJ_TYPE.CONV, companyId, `${OBJ_TYPE.CONV}_${Date.now()}`);
    newConv = responseConv.body.ops[0].obj_id;

    const responseList = await requestListConv(api, newConv, companyId);
    nodeLogic = responseList.body.ops[0].list[1].obj_id;
    nodeStart = responseList.body.ops[0].list[0].obj_id;
    nodeFinal = responseList.body.ops[0].list[2].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeLogic,
        company_id: companyId,
        conv_id: newConv,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'POST',
            url: '{{__callback_url}}',
            extra: { a: '{{bookstore}}' },
            extra_type: { a: 'array' },
            max_threads: 5,
            err_node_id: '',
            extra_headers: {},
            send_sys: false,
            cert_pem: '',
            content_type: 'application/json',
          },
          { node_title: 'final', to_node_id: nodeFinal, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );

    const responseNewNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id: companyId,
        title: 'api_sync',
        conv_id: newConv,
        obj_type: 0,
        version: 22,
      }),
    );
    newNodeApi = responseNewNode.body.ops[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeStart,
        company_id: companyId,
        conv_id: newConv,
        title: 'start',
        description: '',
        obj_type: 1,
        logics: [{ to_node_id: newNodeApi, type: 'go' }],
        semaphors: [],
        version: 22,
      }),
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: newNodeApi,
        company_id: companyId,
        conv_id: newConv,
        title: 'api',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'GET',
            url: 'https://www.w3schools.com/php/books.xml',
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: '',
            extra_headers: {},
            send_sys: false,
            cert_pem: '',
            content_type: 'text/xml',
          },
          { to_node_id: nodeLogic, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );

    const responseCommit = await requestConfirm(api, newConv, companyId);
    expect(responseCommit.status).toBe(200);

    const CreateKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id: companyId,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(200);
    const user = CreateKeyResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newApi = application.getApiKeyClient(newApiKey);
    ApiKey2 = +newApiKey.id;
  });

  test.each([
    [null, 'Wrong object reference. Validation error'],
    [true, 'json: cannot unmarshal bool into Go struct field CreateTaskOp.ops.conv_id of type int'],
    ['', 'strconv.Atoi: parsing "": invalid syntax'],
    ['test', 'strconv.Atoi: parsing "test": invalid syntax'],
    [{}, 'json: cannot unmarshal object into Go struct field CreateTaskOp.ops.conv_id of type int'],
    [[], 'json: cannot unmarshal array into Go struct field CreateTaskOp.ops.conv_id of type int'],
    [0, `Wrong object reference. Validation error`],

    [undefined, `Wrong object reference. Validation error`],
  ])(`shouldn't create task syncApi with invalid conv_id '%s'`, async (param, reason) => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: param,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(response.status).toBe(400);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, 'Wrong object reference. Validation error'],
    [true, 'json: cannot unmarshal bool into Go struct field CreateTaskOp.ops.obj_alias of type string'],
    ['', 'Wrong object reference. Validation error'],
    [{}, 'json: cannot unmarshal object into Go struct field CreateTaskOp.ops.obj_alias of type string'],
    [[], 'json: cannot unmarshal array into Go struct field CreateTaskOp.ops.obj_alias of type string'],
    [0, `json: cannot unmarshal number into Go struct field CreateTaskOp.ops.obj_alias of type string`],
    [1, `json: cannot unmarshal number into Go struct field CreateTaskOp.ops.obj_alias of type string`],
    [-1, `json: cannot unmarshal number into Go struct field CreateTaskOp.ops.obj_alias of type string`],
    [undefined, `Wrong object reference. Validation error`],
  ])(`shouldn't create task syncApi with invalid obj_alias'%s'`, async (param, reason) => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        obj_alias: param,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(response.status).toBe(400);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, 'json: cannot unmarshal bool into Go struct field CreateTaskOp.ops.company_id of type string'],

    [{}, 'json: cannot unmarshal object into Go struct field CreateTaskOp.ops.company_id of type string'],
    [[], 'json: cannot unmarshal array into Go struct field CreateTaskOp.ops.company_id of type string'],
    [0, `json: cannot unmarshal number into Go struct field CreateTaskOp.ops.company_id of type string`],
    [1, `json: cannot unmarshal number into Go struct field CreateTaskOp.ops.company_id of type string`],
    [-1, `json: cannot unmarshal number into Go struct field CreateTaskOp.ops.company_id of type string`],
  ])(`shouldn't create task syncApi with invalid company_id '%s'`, async (param, reason) => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: param,
        conv_id: newConv,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(response.status).toBe(400);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, 'json: cannot unmarshal bool into Go struct field CreateTaskOp.ops.ref of type string'],
    [{}, 'json: cannot unmarshal object into Go struct field CreateTaskOp.ops.ref of type string'],
    [[], 'json: cannot unmarshal array into Go struct field CreateTaskOp.ops.ref of type string'],
    [0, `json: cannot unmarshal number into Go struct field CreateTaskOp.ops.ref of type string`],
    [1, `json: cannot unmarshal number into Go struct field CreateTaskOp.ops.ref of type string`],
    [-1, `json: cannot unmarshal number into Go struct field CreateTaskOp.ops.ref of type string`],
  ])(`shouldn't create task syncApi with invalid ref '%s'`, async (param, reason) => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: param,
      }),
    );
    expect(response.status).toBe(400);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, 'error', `data is nil`],
    [
      true,
      'error',
      `json: cannot unmarshal bool into Go struct field CreateTaskOp.ops.data of type map[string]interface {}`,
    ],
    [
      '',
      'error',
      `json: cannot unmarshal string into Go struct field CreateTaskOp.ops.data of type map[string]interface {}`,
    ],
    [
      'test',
      'error',
      `json: cannot unmarshal string into Go struct field CreateTaskOp.ops.data of type map[string]interface {}`,
    ],
    [
      0,
      'error',
      `json: cannot unmarshal number into Go struct field CreateTaskOp.ops.data of type map[string]interface {}`,
    ],
    [
      1,
      'error',
      `json: cannot unmarshal number into Go struct field CreateTaskOp.ops.data of type map[string]interface {}`,
    ],
    [
      -1,
      'error',
      `json: cannot unmarshal number into Go struct field CreateTaskOp.ops.data of type map[string]interface {}`,
    ],
    [
      ['test'],
      'error',
      `json: cannot unmarshal array into Go struct field CreateTaskOp.ops.data of type map[string]interface {}`,
    ],
    [
      [1],
      'error',
      `json: cannot unmarshal array into Go struct field CreateTaskOp.ops.data of type map[string]interface {}`,
    ],
    [
      [null],
      'error',
      `json: cannot unmarshal array into Go struct field CreateTaskOp.ops.data of type map[string]interface {}`,
    ],
    [
      [true],
      'error',
      `json: cannot unmarshal array into Go struct field CreateTaskOp.ops.data of type map[string]interface {}`,
    ],
    [
      [],
      'error',
      `json: cannot unmarshal array into Go struct field CreateTaskOp.ops.data of type map[string]interface {}`,
    ],
  ])(`shouldn't create task syncApi with invalid data'%s'`, async (param, proc, reason) => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        data: param,
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(response.status).toBe(400);
    expect(response.body.ops[0].proc).toEqual(proc);
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([
    [undefined, 'error', `data is nil`],
    [
      [{}],
      'error',
      'json: cannot unmarshal array into Go struct field CreateTaskOp.ops.data of type map[string]interface {}',
    ],
    [
      [],
      'error',
      'json: cannot unmarshal array into Go struct field CreateTaskOp.ops.data of type map[string]interface {}',
    ],
  ])(`shouldn't create task syncApi with invalid data'%s'`, async (param, proc, reason) => {
    const response = await api.requestSyncApi({
      path: 'api/1/json',
      body: {
        timeout: 10,
        ops: [
          {
            type: 'create',
            obj: 'task',
            company_id: companyId,
            conv_id: newConv,
            data: param,
            ref: `ref_${Date.now()}`,
          },
        ],
      },
    });
    expect(response.status).toBe(400);
    expect(response.body.ops[0].proc).toEqual(proc);
    expect(response.body.ops[0].description).toContain(reason);
  });

  test(`shouldn't create task sync_api - access_denied`, async () => {
    const responseTask = await newApi.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(400);

    expect(responseTask.body.ops[0].description).toEqual(`user: ${ApiKey2}, conv_id: ${newConv}`);
  });

  test.skip(`shouldn't create task sync_api - user limit`, async () => {
    const data = {
      type: 'set',
      obj: 'limits',
      obj_id: ApiKey2,
      obj_type: 'user',
      company_id: companyId,
      user_id: ApiKey2,
      limits: [{ type: 'rps', value: 0, edit: true }],
      force: false,
    };

    await axiosInstance({
      method: 'POST',
      url: `${ConfigurationManager.getConfiguration().getSuperadminUrl()}superadmin/api/1/json`,
      data,
      headers: {
        Origin: `${ConfigurationManager.getConfiguration().getSuperadminUrl()}`,
        Cookie: cookie,
      },
    });

    const responseConv = await requestCreateObj(newApi, OBJ_TYPE.CONV, companyId, `${OBJ_TYPE.CONV}_${Date.now()}`);
    newConv1 = responseConv.body.ops[0].obj_id;

    const responseTask = await newApi.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv1,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(400);
    expect(responseTask.body.ops[0].proc).toEqual('error');
    expect(responseTask.body.ops[0].description).toEqual('too many requests, you exceeded user limit 0/sec');
  });

  test(`shouldn't create task sync_api - conveyor_is_not_active`, async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV,
        company_id: companyId,
        obj_id: newConv,
        status: 'paused',
      }),
    );
    const responseTask = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(400);

    expect(responseTask.body.ops[0].description).toEqual('conveyor is not active');
  });

  test(`shouldn't create task sync_api - conveyor_not_found`, async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, companyId);
    expect(response.status).toBe(200);

    const responseTask = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(400);

    expect(responseTask.body.ops[0].description).toEqual('conveyor not found');
  });

  test(`shouldn't create task sync_api - Timeout for create task`, async () => {
    const responseConv = await requestCreateObj(api, OBJ_TYPE.CONV, companyId, `${OBJ_TYPE.CONV}_${Date.now()}`);
    newConv1 = responseConv.body.ops[0].obj_id;

    const responseTask = await api.requestSyncApi(
      createRequestWithObj({
        timeout: 10,
        ops: [
          {
            type: 'create',
            obj: 'task',
            company_id: companyId,
            conv_id: newConv1,
            data: {
              test: '{{body.ops[0].data.a}}',
            },
            ref: 'ref_1688046624216',
          },
        ],
      }),
    );

    expect(responseTask.status).toBe(504);
    expect(responseTask.body.ops[0].proc).toEqual('error');
    expect(responseTask.body.ops[0].description).toEqual('System timeout exceeded');
  });

  test(`shouldn't create task sync_api - not_unical_ref`, async () => {
    const responseConv = await requestCreateObj(
      api,
      OBJ_TYPE.CONV,
      companyId,
      `${OBJ_TYPE.CONV}_${Date.now()}`,
      0,
      `state`,
    );
    newConv2 = responseConv.body.ops[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv2,
        data: { a: '1' },
        ref: `test`,
      }),
    );

    const responseTask = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        keys: { conv_id: newConv2, data: { test: '{{body.ops[0].data.a}}' }, ref: `test` },
      }),
    );
    expect(responseTask.status).toBe(400);

    expect(responseTask.body.ops[0].description).toEqual('not unical reference');
  });

  test(`shouldn't create task syncApi with invalid body`, async () => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        op: [
          { type: 'create', conv_id: newConv, obj: 'task', action: 'user', company_id: companyId, data: {}, ref: '1' },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual('Invalid obj or type');
  });

  test(`shouldn't create task syncApi with Bad signature`, async () => {
    const newApiKey = {
      key: `5381`,
      secret: `5381`,
      companies: [],
      title: '',
      id: `5381`,
    } as ApiKey;
    newApi = application.getApiKeyClient(newApiKey);
    const responseTask = await newApi.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(400);
    expect(responseTask.body.ops[0].proc).toEqual('error');
    expect(responseTask.body.ops[0].description).toEqual(`Bad signature`);
  });

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);

    const response = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, companyId);
    expect(response.status).toBe(200);

    const response2 = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv1, companyId);
    expect(response2.status).toBe(200);

    const response3 = await requestDeleteObj(api, OBJ_TYPE.USER, ApiKey2, companyId);
    expect(response3.status).toBe(200);
  });
});
