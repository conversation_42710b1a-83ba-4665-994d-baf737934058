import { request } from '../../../application/api/ApiGWUserClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import { axiosInstance } from '../../../application/api/AxiosClient';
import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';
import { ApiUserClient } from '../../../application/api/ApiUserClient';
import { User } from '../../../infrastructure/model/User';

describe('ApiGW Import (positive)', () => {
  let apikey: ApiKey;
  let api_user: ApiUserClient;
  let user: User;
  let endpoint_id: number;
  let endpoint_id_2: number;
  let endpoint_id_3: number;
  let company_id: any;
  let api_id: number;
  let api_login: string | number;
  let path_patch: string;
  let host: string;
  let final_node_ID: string | number;
  let conv_id: number;
  let protocol: string;
  let domain: string;
  let project_short_name: string;
  let project_id: number;
  let stage_id: number;
  let stage_id_2: number;
  let stage_id_3: number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    user = await application.getAuthorizedUser({ company: {} }, 0);
    api_user = await application.getApiUserClient(user);
    company_id = apikey.companies[0].id;
    const url = ConfigurationManager.getConfiguration().getApiGWUrl();
    domain = new URL(url).hostname;
    const parsedUrl = new URL(url);
    protocol = parsedUrl.protocol;
    project_short_name = `project-${Date.now()}`;

    const CreateKeyResponse = await api_user.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(200);
    const user_api = CreateKeyResponse.body;
    const newApiKey = {
      key: `${user_api.ops[0].users[0].logins[0].obj_id}`,
      secret: user_api.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user_api.ops[0].users[0].obj_id}`,
    } as ApiKey;
    api_id = +newApiKey.id;
    api_login = +newApiKey.key;

    const responseProject = await api_user.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: project_short_name,
        short_name: project_short_name,
        description: 'test',
        stages: [
          {
            title: `develop`,
            immutable: false,
          },
          {
            title: `develop2`,
            immutable: false,
          },
          {
            title: `develop3`,
            immutable: false,
          },
        ],
        status: 'active',
      }),
    );
    project_id = responseProject.body.ops[0].obj_id;
    stage_id = responseProject.body.ops[0].stages[0];
    stage_id_2 = responseProject.body.ops[0].stages[1];
    stage_id_3 = responseProject.body.ops[0].stages[2];

    const responseConv = await api_user.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        project_id,
        stage_id,
        title: `Api_Call`,
        conv_type: 'process',
        status: 'actived',
      }),
    );
    conv_id = responseConv.body.ops[0].obj_id;

    const responseList = await api_user.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
        project_id,
        stage_id,
      }),
    );
    expect(responseList.status).toBe(200);
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseLinkStage = await api_user.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.PROJECT,
        obj_id: project_id,
        company_id,
        obj_to: 'user',
        obj_to_id: api_id,
        is_need_to_notify: false,
        privs: [
          { type: 'create', list_obj: ['all'] },
          { type: 'modify', list_obj: ['all'] },
          { type: 'delete', list_obj: ['all'] },
          { type: 'view', list_obj: ['all'] },
        ],
      }),
    );
    expect(responseLinkStage.status).toBe(200);
  });

  test(`import endpoint`, async () => {
    const response = await request('post', `apis/import/${company_id}/${project_id}`, {
      apis: [
        {
          project_id,
          workspace_id: company_id,
          stage_short_name: 'develop',
          stage_id,
          project_short_name,
          description: '',
          id: 'f04gvomxv8',
          host: `f04gvomxv8.${domain}`,
          name: 'auto_test',
          paths: [
            {
              description: '',
              process_id: conv_id,
              api_id: 'f04gvomxv8',
              url: `https://f04gvomxv8.${domain}/put/path`,
              method: 'PUT',
              path: '/put/path',
              timeout: 35,
              paused: false,
              proxy_headers: false,
              proxy_raw_body: false,
              async: false,
            },
            {
              description: 'modify',
              process_id: conv_id,
              public: {
                api_login,
              },
              api_id: `f04gvomxv8`,
              url: `https://f04gvomxv8.${domain}/patch/path`,
              method: 'PATCH',
              path: '/patch/path',
              timeout: 5,
              updated_at: 1697543476,
              paused: false,
              proxy_headers: true,
              proxy_raw_body: true,
              async: true,
            },
          ],
          updated_at: 1697543478,
          created_at: 1697543467,
        },
        {
          project_id,
          workspace_id: company_id,
          stage_short_name: 'develop2',
          stage_id: stage_id_2,
          project_short_name,
          description: '',
          id: '777test',
          host: `777test.${domain}`,
          name: 'auto_test',
          paths: [
            {
              description: '',
              process_id: conv_id,
              api_id: '777test',
              url: `https://777test.${domain}/put/path`,
              method: 'PUT',
              path: '/put/path',
              timeout: 35,
              paused: false,
              proxy_headers: false,
              proxy_raw_body: false,
              async: false,
            },
            {
              description: 'modify',
              process_id: conv_id,
              public: {
                api_login,
              },
              api_id: `777test`,
              url: `https://777test.${domain}/patch/path`,
              method: 'PATCH',
              path: '/patch/path',
              timeout: 5,
              updated_at: 1697543476,
              paused: true,
              proxy_headers: true,
              proxy_raw_body: true,
              async: true,
            },
          ],
          updated_at: 1697543478,
          created_at: 1697543467,
        },
      ],
    });
    expect(response.status).toBe(200);
    expect(response.body).toBeObject();
  });

  test(`get endpoint stage`, async () => {
    const response = await request('get', `apis/${company_id}/${project_id}/${stage_id}`);
    expect(response.status).toBe(200);
    endpoint_id = response.body.api.id;
    host = response.body.api.host;

    const response2 = await request('get', `apis/${company_id}/${project_id}/${stage_id_2}`);
    expect(response2.status).toBe(200);
    endpoint_id_2 = response2.body.api.id;
  });

  test(`list paths`, async () => {
    const response = await request('get', `apis/${endpoint_id}/paths`);
    expect(response.status).toBe(200);
    path_patch = response.body[1].path;
  });

  test(`list paths stage2`, async () => {
    const response = await request('get', `apis/${endpoint_id_2}/paths`);
    expect(response.status).toBe(200);
    expect(response.body[0].path).toEqual('/put/path');
    expect(response.body[1].path).toEqual('/patch/path');
  });

  test('create request apiGW after import patch', async () => {
    await new Promise(r => setTimeout(r, 2000));
    const response = await axiosInstance({
      method: 'PATCH',
      url: `${protocol}//${host}${path_patch}`,
    });
    expect(response.status).toBe(204);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await api_user.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID,
        company_id,
        conv_id,
        limit: 1,
      }),
    );
    expect(responseList.status).toBe(200);

    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path_patch);
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('PATCH');
    expect(responseList.body.ops[0].list[0].data.__request.host).toBeString();
  });

  test(`merge endpoint with new param (modify)`, async () => {
    const response = await request('post', `apis/import/merge/${company_id}/${project_id}`, {
      apis: [
        {
          project_id,
          workspace_id: company_id,
          stage_short_name: 'develop',
          stage_id,
          project_short_name,
          description: '',
          id: 'f04gvomxv',
          host: `f04gvomxv.${domain}`,
          name: 'auto_test',
          paths: [
            {
              description: '',
              process_id: conv_id,
              api_id: 'f04gvomxv',
              url: `https://f04gvomxv.${domain}/put/path`,
              method: 'PUT',
              path: '/put/path',
              timeout: 5,
              paused: false,
              proxy_headers: false,
              proxy_raw_body: false,
              async: false,
            },
            {
              description: 'modify',
              process_id: conv_id,
              public: {
                api_login,
              },
              api_id: `f04gvomxv`,
              url: `https://f04gvomxv.${domain}/post/path`,
              method: 'POST',
              path: '/post/path',
              timeout: 15,
              updated_at: 1697543476,
              paused: false,
              proxy_headers: false,
              proxy_raw_body: false,
              async: true,
            },
            {
              description: '',
              process_id: conv_id,
              public: {
                api_login,
              },
              api_id: `f04gvomxv`,
              url: `https://f04gvomxv.${domain}/get/path`,
              method: 'GET',
              path: '/get/path',
              timeout: 15,
              updated_at: 1697543476,
              paused: false,
              proxy_headers: false,
              proxy_raw_body: false,
              async: true,
            },
          ],
          updated_at: 1697543478,
          created_at: 1697543467,
        },
      ],
    });
    expect(response.status).toBe(200);
    expect(response.body).toBeObject();
  });

  test(`list paths stage after merge`, async () => {
    const response = await request('get', `apis/${endpoint_id}/paths`);
    expect(response.status).toBe(200);
    expect(response.body[0].path).toEqual('/put/path');
    expect(response.body[1].path).toEqual('/post/path');
    expect(response.body[2].path).toEqual('/patch/path');
    expect(response.body[3].path).toEqual('/get/path');
  });

  test(`list paths stage2 after merge`, async () => {
    const response = await request('get', `apis/${endpoint_id_2}/paths`);
    expect(response.status).toBe(200);
    expect(response.body[0].path).toEqual('/put/path');
    expect(response.body[1].path).toEqual('/patch/path');
  });

  test('create request apiGW after merge', async () => {
    const responseHost = await request('get', `apis/${company_id}/${project_id}/${stage_id}`);
    expect(responseHost.status).toBe(200);
    endpoint_id = responseHost.body.api.id;
    host = responseHost.body.api.host;

    const responsePath = await request('get', `apis/${endpoint_id}/paths`);
    expect(responsePath.status).toBe(200);
    path_patch = responsePath.body[1].path;

    await new Promise(r => setTimeout(r, 2000));
    const response = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host}${path_patch}`,
    });
    expect(response.status).toBe(204);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await api_user.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID,
        company_id,
        conv_id,
        limit: 1,
      }),
    );
    expect(responseList.status).toBe(200);

    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path_patch);
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('POST');
    expect(responseList.body.ops[0].list[0].data.__request.host).toBeString();
  });

  test(`merge endpoint in empty stage3 (create)`, async () => {
    const response = await request('post', `apis/import/merge/${company_id}/${project_id}`, {
      apis: [
        {
          project_id,
          workspace_id: company_id,
          stage_short_name: 'develop3',
          stage_id: stage_id_3,
          project_short_name,
          description: '',
          id: 'f04gvomxv8',
          host: `f04gvomxv8.${domain}`,
          name: 'auto_test',
          paths: [
            {
              description: '',
              process_id: conv_id,
              api_id: 'f04gvomxv8',
              url: `https://f04gvomxv8.${domain}/get/path`,
              method: 'GET',
              path: '/get/path',
              timeout: 35,
              paused: false,
              proxy_headers: false,
              proxy_raw_body: false,
              async: false,
            },
            {
              description: 'modify',
              process_id: conv_id,
              public: {
                api_login,
              },
              api_id: `f04gvomxv8`,
              url: `https://f04gvomxv8.${domain}/post/path`,
              method: 'POST',
              path: '/post/path',
              timeout: 5,
              updated_at: 1697543476,
              paused: false,
              proxy_headers: true,
              proxy_raw_body: true,
              async: true,
            },
            {
              description: '',
              process_id: conv_id,
              api_id: '777test',
              url: `https://777test.${domain}/put/path`,
              method: 'PUT',
              path: '/put/path',
              timeout: 35,
              paused: false,
              proxy_headers: false,
              proxy_raw_body: false,
              async: false,
            },
          ],
          updated_at: 1697543478,
          created_at: 1697543467,
        },
      ],
    });
    expect(response.status).toBe(200);
    expect(response.body).toBeObject();
  });

  test(`list paths stage3 after merge`, async () => {
    const response2 = await request('get', `apis/${company_id}/${project_id}/${stage_id_3}`);
    expect(response2.status).toBe(200);
    endpoint_id_3 = response2.body.api.id;

    const response = await request('get', `apis/${endpoint_id_3}/paths`);
    expect(response.status).toBe(200);
    expect(response.body[0].path).toEqual('/put/path');
    expect(response.body[1].path).toEqual('/post/path');
    expect(response.body[2].path).toEqual('/get/path');
  });

  test('create request apiGW after merge post', async () => {
    const responseHost = await request('get', `apis/${company_id}/${project_id}/${stage_id}`);
    expect(responseHost.status).toBe(200);
    endpoint_id = responseHost.body.api.id;
    host = responseHost.body.api.host;

    const responsePath = await request('get', `apis/${endpoint_id}/paths`);
    expect(responsePath.status).toBe(200);
    path_patch = responsePath.body[1].path;

    await new Promise(r => setTimeout(r, 2000));
    const response = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host}${path_patch}`,
      data: {
        param: 1234,
      },
    });
    expect(response.status).toBe(204);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await api_user.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID,
        company_id,
        conv_id,
        limit: 1,
      }),
    );
    expect(responseList.status).toBe(200);

    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path_patch);
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('POST');
    expect(responseList.body.ops[0].list[0].data.param).toEqual(1234);
  });

  test(`merge endpoint in stage3 with empty paths`, async () => {
    const response = await request('post', `apis/import/merge/${company_id}/${project_id}`, {
      apis: [
        {
          project_id,
          workspace_id: company_id,
          stage_short_name: 'develop3',
          stage_id: stage_id_3,
          project_short_name,
          description: '',
          id: 'f04gvomxv8',
          host: `f04gvomxv8.${domain}`,
          name: 'auto_test',
          paths: [],
          updated_at: 1697543478,
          created_at: 1697543467,
        },
      ],
    });
    expect(response.status).toBe(200);
    expect(response.body).toBeObject();
  });

  test(`list paths stage3 after merge with empty paths`, async () => {
    const response = await request('get', `apis/${endpoint_id_3}/paths`);
    expect(response.status).toBe(200);
    expect(response.body[0].path).toEqual('/put/path');
    expect(response.body[1].path).toEqual('/post/path');
    expect(response.body[2].path).toEqual('/get/path');
  });

  test(`merge endpoint in empty stage3 (modify)`, async () => {
    const response = await request('post', `apis/import/merge/${company_id}/${project_id}`, {
      apis: [
        {
          project_id,
          workspace_id: company_id,
          stage_short_name: 'develop3',
          stage_id: stage_id_3,
          project_short_name,
          description: '',
          id: 'f04gvomxv8',
          host: `f04gvomxv8.${domain}`,
          name: 'auto_test',
          paths: [
            {
              description: '',
              process_id: conv_id,
              api_id: 'f04gvomxv8',
              url: `https://f04gvomxv8.${domain}/delete/path`,
              method: 'DELETE',
              path: '/delete/path',
              timeout: 15,
              paused: true,
              proxy_headers: true,
              proxy_raw_body: true,
              async: true,
            },
          ],
          updated_at: 1697543478,
          created_at: 1697543467,
        },
      ],
    });
    expect(response.status).toBe(200);
    expect(response.body).toBeObject();
  });

  test(`list paths stage3 after modify`, async () => {
    const response = await request('get', `apis/${endpoint_id_3}/paths`);
    expect(response.status).toBe(200);
    expect(response.body[0].path).toEqual('/put/path');
    expect(response.body[1].path).toEqual('/post/path');
    expect(response.body[2].path).toEqual('/get/path');
    expect(response.body[3].path).toEqual('/delete/path');
  });

  test(`export endpoint`, async () => {
    const response2 = await request('get', `apis/${company_id}/${project_id}/${stage_id_3}`);
    expect(response2.status).toBe(200);
    endpoint_id_3 = response2.body.api.id;

    const response3 = await request('get', `apis/${company_id}/${project_id}/${stage_id_2}`);
    expect(response3.status).toBe(200);
    endpoint_id_2 = response3.body.api.id;

    const response4 = await request('get', `apis/${company_id}/${project_id}/${stage_id}`);
    expect(response4.status).toBe(200);
    endpoint_id = response4.body.api.id;

    const response = await request('get', `apis/export/${company_id}/${project_id}`);
    expect(response.status).toBe(200);
    expect(response.body.apis).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id })]));
    expect(response.body.apis).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_2 })]));
    expect(response.body.apis).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_3 })]));
  });

  test(`export endpoint after delete stage`, async () => {
    const responseProject = await api_user.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.STAGE,
        obj_id: stage_id_3,
        company_id,
        project_id,
      }),
    );
    expect(responseProject.status).toBe(200);

    await new Promise(r => setTimeout(r, 2000));

    const response = await request('get', `apis/export/${company_id}/${project_id}`);
    expect(response.status).toBe(200);
    expect(response.body.apis).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id })]));
    expect(response.body.apis).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_2 })]));
    expect(response.body.apis).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_3 })]));
  });

  test(`export endpoint after destroy stage`, async () => {
    const responseStage = await api_user.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.STAGE,
        obj_id: stage_id_3,
        company_id,
      }),
    );
    expect(responseStage.status).toBe(200);

    await new Promise(r => setTimeout(r, 2000));

    const response = await request('get', `apis/export/${company_id}/${project_id}`);
    expect(response.status).toBe(200);
    expect(response.body.apis).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id })]));
    expect(response.body.apis).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_2 })]));
    expect(response.body.apis).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_3 })]));
  });

  test(`delete endpoint`, async () => {
    const response = await request('delete', `apis/${endpoint_id}`, {});
    expect(response.status).toBe(204);

    const response2 = await request('delete', `apis/${endpoint_id_2}`, {});
    expect(response2.status).toBe(204);
  });

  afterAll(async () => {
    const ResponseDeleteApiKey = await api_user.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: api_id,
        company_id,
      }),
    );
    expect(ResponseDeleteApiKey.status).toBe(200);

    const responseProject = await api_user.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: project_id,
        company_id,
      }),
    );
    expect(responseProject.status).toBe(200);
  });
});
