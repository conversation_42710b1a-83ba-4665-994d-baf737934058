import { request } from '../../../application/api/ApiGWUserClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import { axiosInstance } from '../../../application/api/AxiosClient';
import { SchemaValidator } from '../../../application/api/SchemaValidator';
import LisTaskSchema from './schemas/listTaskSchema.json';
import { ApiUserClient } from '../../../application/api/ApiUserClient';
import { User } from '../../../infrastructure/model/User';
import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';

describe('ApiGWCor (positive)', () => {
  let api: ApiUserClient;
  let user: User;
  let endpoint_id: number;
  let endpoint_id_2: number;
  let company_id: any;
  let alias_id: string | number;
  let path: string;
  let path_id: number;
  let path_id_2: number;
  let host: string;
  let host_2: string;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let conv_id: number;
  let newApiKeyId: string | number;
  let newApiKeyLogin: number;
  let protocol: string;
  let url_host: string;
  let project_short_name: string;
  let stage_short_name: string;
  let alias_short_name: string;
  let project_id: number;
  let stage_id: number;
  let stage_id_2: number;

  const requestList = async (obj_id: any, conv_id: number, company_id: any, limit = 1): Promise<any> => {
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id,
        company_id,
        conv_id,
        limit,
      }),
    );
    expect(responseList.status).toBe(200);
    return responseList;
  };

  beforeAll(
    async (): Promise<void> => {
      user = await application.getAuthorizedUser({ company: {} }, 0);
      api = await application.getApiUserClient(user);
      company_id = user.companies[0].id;
      const parsedUrl = new URL(`${ConfigurationManager.getConfiguration().getApiGWUrl()}`);
      protocol = parsedUrl.protocol;
      url_host = parsedUrl.host;
      project_short_name = `project-${Date.now()}`;
      stage_short_name = `stage-${Date.now()}`;
      alias_short_name = `alias-${Date.now()}-alias`;

      const CreateKeyResponse = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.USER,
          company_id,
          title: 'API',
          logins: [{ type: 'api' }],
        }),
      );
      expect(CreateKeyResponse.status).toBe(200);
      const user_api = CreateKeyResponse.body;
      const newApiKey = {
        key: `${user_api.ops[0].users[0].logins[0].obj_id}`,
        secret: user_api.ops[0].users[0].logins[0].key,
        companies: [],
        title: '',
        id: `${user_api.ops[0].users[0].obj_id}`,
      } as ApiKey;
      newApiKeyId = +newApiKey.id;
      newApiKeyLogin = +newApiKey.key;

      const responseProject = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.PROJECT,
          company_id,
          title: project_short_name,
          short_name: project_short_name,
          description: 'test',
          stages: [
            {
              title: stage_short_name,
              immutable: false,
            },
            {
              title: `develop`,
              immutable: false,
            },
          ],
          status: 'active',
        }),
      );
      project_id = responseProject.body.ops[0].obj_id;
      stage_id = responseProject.body.ops[0].stages[0];
      stage_id_2 = responseProject.body.ops[0].stages[1];

      const responseConv = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.CONV,
          company_id,
          project_id,
          stage_id,
          title: `Api_Call`,
          conv_type: 'process',
        }),
      );
      conv_id = responseConv.body.ops[0].obj_id;

      const responseAlias = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          short_name: alias_short_name,
          description: `aliasDesk-${Date.now()}`,
          title: `aliasTitle-${Date.now()}`,
          project_id,
          stage_id,
        }),
      );
      expect(responseAlias.status).toBe(200);
      alias_id = responseAlias.body.ops[0].obj_id;

      const responseLinkAlias = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.ALIAS,
          obj_id: alias_id,
          company_id,
          obj_to_id: conv_id,
          obj_to_type: 'conv',
          link: true,
          project_id,
          stage_id,
        }),
      );
      expect(responseLinkAlias.status).toBe(200);

      const responseList = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.CONV,
          obj_id: conv_id,
          company_id,
        }),
      );
      expect(responseList.status).toBe(200);
      process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
      final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

      const responseCreateNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id,
          title: 'ApiGW',
          description: '',
          obj_type: 0,
          logics: [
            {
              type: 'api',
              format: '',
              method: 'POST',
              url: '{{__callback_url}}',
              extra: { proxy: '{{code}}' },
              extra_type: { proxy: 'string' },
              max_threads: 5,
              err_node_id: '',
              extra_headers: { ['content-type']: 'application/json; charset=utf-8' },
              debug_info: false,
              send_sys: true,
              rfc_format: true,
              cert_pem: '',
              content_type: 'application/json',
              version: 2,
              response: {
                header: '{{header}}',
                body: '{{body}}',
              },
              response_type: {
                header: 'object',
                body: 'object',
              },
            },
            { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseCreateNode.status).toBe(200);

      const responseCommit = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CONFIRM,
          obj: OBJ_TYPE.COMMIT,
          company_id,
          conv_id,
          version: 22,
        }),
      );
      expect(responseCommit.status).toBe(200);

      const responseLink = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.STAGE,
          obj_id: stage_id,
          company_id,
          obj_to: 'user',
          obj_to_id: newApiKeyId,
          is_need_to_notify: false,
          privs: [
            { type: 'create', list_obj: ['all'] },
            { type: 'modify', list_obj: ['all'] },
            { type: 'delete', list_obj: ['all'] },
            { type: 'view', list_obj: ['all'] },
          ],
        }),
      );
      expect(responseLink.status).toBe(200);

      const response = await request('post', `apis`, {
        name: `auto_test`,
        description: '',
        workspace_id: company_id,
        stage_id,
        project_id,
      });
      expect(response.status).toBe(200);
      endpoint_id = response.body.api.id;
      host = response.body.api.host;

      const response2 = await request('post', `apis`, { name: `auto_test_2`, description: '' });
      expect(response2.status).toBe(200);
      endpoint_id_2 = response2.body.api.id;
      host_2 = response2.body.api.host;

      const responsePath = await request('post', `apis/${endpoint_id}/paths`, {
        path: '/{hubID}/hubs/view',
        description: '',
        method: 'GET',
        timeout: 29,
        async: false,
        proxy_headers: false,
        proxy_raw_body: false,
        public: { api_login: newApiKeyLogin },
        alias_id,
        alias_short_name,
        paused: false,
      });
      expect(responsePath.status).toBe(200);
      path = responsePath.body.path.path;
      path_id = responsePath.body.path.id;

      const responsePath2 = await request('post', `apis/${endpoint_id_2}/paths`, {
        path: '/hubs/{hubID}/view',
        description: '',
        method: 'POST',
        timeout: 29,
        async: false,
        proxy_headers: false,
        proxy_raw_body: false,
        public: { api_login: newApiKeyLogin },
        process_id: conv_id,
        paused: false,
      });
      expect(responsePath2.status).toBe(200);
      path_id_2 = responsePath2.body.path.id;

      const responseLink2 = await request('post', `apis/${company_id}/${project_id}/${stage_id_2}`, {
        apiId: endpoint_id_2,
      });
      expect(responseLink2.status).toBe(200);
    },
  );

  test('create request apiGW method GET with id alias and path=/{hubID}/hubs/view', async (): Promise<void> => {
    await new Promise(r => setTimeout(r, 1000));
    const response = await axiosInstance({
      method: 'GET',
      url: `${protocol}//${host}${path}`,
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(final_node_ID, conv_id, company_id);

    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(path);
    expect(responseList.body.ops[0].list[0].data.__request.path_params.hubID).toEqual('{hubID}');
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('GET');
    SchemaValidator.validate(LisTaskSchema, responseList.body);
  });

  test('create request apiGW method POST with id process and path=/hubs/{hubID}/view', async (): Promise<void> => {
    const response = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host_2}/hubs/test12345/view`,
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1200));
    const responseList = await requestList(final_node_ID, conv_id, company_id);

    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual('/hubs/test12345/view');
    expect(responseList.body.ops[0].list[0].data.__request.path_params.hubID).toEqual('test12345');
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('POST');
    SchemaValidator.validate(LisTaskSchema, responseList.body);
  });

  test(`create request apiGW method PUT with id alias and path=/hubs/view/{hubID}`, async (): Promise<void> => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/hubs/view/{hubID}',
      description: '',
      method: 'PUT',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      alias_id,
      alias_short_name,
    });
    expect(responseModify.status).toBe(200);

    await new Promise(r => setTimeout(r, 1000));
    const response = await axiosInstance({
      method: 'PUT',
      url: `${protocol}//${host}/hubs/view/test12345`,
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual('/hubs/view/test12345');
    expect(responseList.body.ops[0].list[0].data.__request.path_params.hubID).toEqual('test12345');
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('PUT');
  });

  test.skip(`create request apiGW method PUT with stageShortName`, async (): Promise<void> => {
    const response = await axiosInstance({
      method: 'PUT',
      url: `${protocol}//${stage_short_name}-${project_short_name}-${company_id}.${url_host}/hubs/view/test12345`,
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual('/hubs/view/test12345');
    expect(responseList.body.ops[0].list[0].data.__request.path_params.hubID).toEqual('test12345');
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('PUT');
  });

  test(`create request apiGW method PATCH with id process and path=/hubs/{hubID}/{view}`, async (): Promise<void> => {
    const responseModify = await request('put', `apis/${endpoint_id_2}/paths/${path_id_2}`, {
      path: '/hubs/{hubID}/{view}',
      description: '',
      method: 'PATCH',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
    });
    expect(responseModify.status).toBe(200);

    await new Promise(r => setTimeout(r, 1000));
    const response = await axiosInstance({
      method: 'PATCH',
      url: `${protocol}//${host_2}/hubs/test12345/{}[]""!@#$%^&*()_+`,
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual('/hubs/test12345/{}[]""!@');
    expect(responseList.body.ops[0].list[0].data.__request.path_params.hubID).toEqual('test12345');
    expect(responseList.body.ops[0].list[0].data.__request.path_params.view).toEqual('{}[]""!@');
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('PATCH');
  });

  test.skip(`create request apiGW method PATCH with stageShortName`, async (): Promise<void> => {
    const response = await axiosInstance({
      method: 'PATCH',
      url: `${protocol}//${host_2}/hubs/test12345/{}[]""!@#$%^&*()_+`,
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual('/hubs/test12345/{}[]""!@');
    expect(responseList.body.ops[0].list[0].data.__request.path_params.hubID).toEqual('test12345');
    expect(responseList.body.ops[0].list[0].data.__request.path_params.view).toEqual('{}[]""!@');
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('PATCH');
  });

  test(`create request apiGW method DELETE with id alias and path=/hubs/view/*`, async (): Promise<void> => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/hubs/view/*',
      description: '',
      method: 'DELETE',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      alias_id,
      alias_short_name,
    });
    expect(responseModify.status).toBe(200);

    await new Promise(r => setTimeout(r, 1000));
    const response = await axiosInstance({
      method: 'DELETE',
      url: `${protocol}//${host}/hubs/view/test/1234/new123`,
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual('/hubs/view/test/1234/new123');
    expect(responseList.body.ops[0].list[0].data.__request.path_params).toEqual({ '*': 'test/1234/new123' });
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('DELETE');
  });

  test(`create request apiGW method DELETE with id process and path=/articles/files/{file}.{ext}`, async (): Promise<
    void
  > => {
    const responseModify = await request('put', `apis/${endpoint_id_2}/paths/${path_id_2}`, {
      path: '/articles/files/{file}.{ext}',
      description: '',
      method: 'DELETE',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
    });
    expect(responseModify.status).toBe(200);

    await new Promise(r => setTimeout(r, 1000));
    const response = await axiosInstance({
      method: 'DELETE',
      url: `${protocol}//${host_2}/articles/files/test.pdf`,
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual('/articles/files/test.pdf');
    expect(responseList.body.ops[0].list[0].data.__request.path_params.ext).toEqual('pdf');
    expect(responseList.body.ops[0].list[0].data.__request.path_params.file).toEqual('test');
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('DELETE');
  });

  test(`create request apiGW method GET with id alias and path=/articles/{file}.{ext}/files`, async (): Promise<
    void
  > => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/articles/{file}.{ext}/files',
      description: '',
      method: 'GET',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      alias_id,
      alias_short_name,
    });
    expect(responseModify.status).toBe(200);

    await new Promise(r => setTimeout(r, 1000));
    const response = await axiosInstance({
      method: 'GET',
      url: `${protocol}//${host}/articles/test.pdf/files`,
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual('/articles/test.pdf/files');
    expect(responseList.body.ops[0].list[0].data.__request.path_params.ext).toEqual('pdf');
    expect(responseList.body.ops[0].list[0].data.__request.path_params.file).toEqual('test');
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('GET');
  });

  test(`create request apiGW method POST with id process and path=/articles/{id:^[1-9]+}-{aux}`, async (): Promise<
    void
  > => {
    const responseModify = await request('put', `apis/${endpoint_id_2}/paths/${path_id_2}`, {
      path: '/articles/{id:^[1-9]+}-{aux}',
      description: '',
      method: 'POST',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
    });
    expect(responseModify.status).toBe(200);

    await new Promise(r => setTimeout(r, 1000));
    const response = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host_2}/articles/123-13123`,
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual('/articles/123-13123');
    expect(responseList.body.ops[0].list[0].data.__request.path_params.aux).toEqual('13123');
    expect(responseList.body.ops[0].list[0].data.__request.path_params.id).toEqual('123');
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('POST');

    const response2 = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host_2}/articles/000123-13123`,
    });
    expect(response2.status).toBe(404);
    expect(response2.data.description).toEqual('path not found');
  });

  test(`create request apiGW method POST with id process and path=/articles/{rid:^[0-9]{5,6}}/new`, async (): Promise<
    void
  > => {
    const responseModify = await request('put', `apis/${endpoint_id_2}/paths/${path_id_2}`, {
      path: '/articles/{rid:^[0-9]{5,6}}',
      description: '',
      method: 'POST',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
    });
    expect(responseModify.status).toBe(200);

    await new Promise(r => setTimeout(r, 1000));
    const response = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host_2}/articles/12313`,
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual('/articles/12313');
    expect(responseList.body.ops[0].list[0].data.__request.path_params.rid).toEqual('12313');
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('POST');

    const response2 = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host_2}/articles/0231345345`,
    });
    expect(response2.status).toBe(404);
    expect(response2.data.description).toEqual('path not found');
  });

  test(`create request apiGW method PUT with id alias and path=/article/slug/{{month}}/-/{{day}}/{{year}}`, async (): Promise<
    void
  > => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/article/slug/{{month}}/-/{{day}}/{{year}}',
      description: '',
      method: 'PUT',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      alias_id,
      alias_short_name,
    });
    expect(responseModify.status).toBe(200);

    await new Promise(r => setTimeout(r, 1000));
    const response = await axiosInstance({
      method: 'PUT',
      url: `${protocol}//${host}/article/slug/sept/-/12/1975`,
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual('/article/slug/sept/-/12/1975');
    expect(responseList.body.ops[0].list[0].data.__request.path_params).toEqual({
      '{day}': '12',
      '{month}': 'sept',
      '{year}': '1975',
    });
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('PUT');
  });

  test(`create request apiGW method POST with id process and path=/article/{id}//related`, async (): Promise<void> => {
    const responseModify = await request('put', `apis/${endpoint_id_2}/paths/${path_id_2}`, {
      path: '/article/{id}//related',
      description: '',
      method: 'POST',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
    });
    expect(responseModify.status).toBe(200);

    await new Promise(r => setTimeout(r, 1000));
    const response = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host_2}/article/7564856748//related`,
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual('/article/7564856748//related');
    expect(responseList.body.ops[0].list[0].data.__request.path_params.id).toEqual('7564856748');
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('POST');
  });

  test(`create request apiGW method PUT with id alias and path=/articles/{iidd}!sup`, async (): Promise<void> => {
    const responseModify = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/articles/{iidd}!sup',
      description: '',
      method: 'PUT',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      alias_id,
      alias_short_name,
    });
    expect(responseModify.status).toBe(200);

    await new Promise(r => setTimeout(r, 1200));
    const response = await axiosInstance({
      method: 'PUT',
      url: `${protocol}//${host}/articles/5678!sup`,
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual('/articles/5678!sup');
    expect(responseList.body.ops[0].list[0].data.__request.path_params.iidd).toEqual('5678');
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('PUT');
  });

  test(`create request apiGW method POST with id process and path=/articles/{id}:{op}`, async (): Promise<void> => {
    const responseModify = await request('put', `apis/${endpoint_id_2}/paths/${path_id_2}`, {
      path: '/articles/{id}:{op}',
      description: '',
      method: 'POST',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: newApiKeyLogin },
      process_id: conv_id,
    });
    expect(responseModify.status).toBe(200);

    await new Promise(r => setTimeout(r, 1000));
    const response = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host_2}/articles/123:test`,
    });
    expect(response.status).toBe(200);
    expect(response.data.sys.conv_id).toEqual(conv_id);

    await new Promise(r => setTimeout(r, 1000));
    const responseList = await requestList(final_node_ID, conv_id, company_id);
    expect(responseList.body.ops[0].list[0].data.__request.path).toEqual('/articles/123:test');
    expect(responseList.body.ops[0].list[0].data.__request.path_params.id).toEqual('123');
    expect(responseList.body.ops[0].list[0].data.__request.path_params.op).toEqual('test');
    expect(responseList.body.ops[0].list[0].data.__request.method).toEqual('POST');

    const response2 = await axiosInstance({
      method: 'POST',
      url: `${protocol}//${host_2}/articles/123test`,
    });
    expect(response2.status).toBe(404);
    expect(response2.data.description).toEqual('path not found');
  });

  afterAll(
    async (): Promise<void> => {
      const response = await request('delete', `apis/${endpoint_id}`, {});
      expect(response.status).toBe(204);

      const response2 = await request('delete', `apis/${endpoint_id_2}`, {});
      expect(response2.status).toBe(204);

      const responseProject = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.PROJECT,
          obj_id: project_id,
          company_id,
        }),
      );
      expect(responseProject.status).toBe(200);

      const ResponseDeleteApiKey = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.USER,
          obj_id: newApiKeyId,
          company_id,
        }),
      );
      expect(ResponseDeleteApiKey.status).toBe(200);
    },
  );
});
