import { requestUser1, requestUser2 } from '../../../../application/api/ApiGWUserClient';
import { axiosInstance } from '../../../../application/api/AxiosClient';
import { application } from '../../../../application/Application';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';
import { ApiUserClient } from '../../../../application/api/ApiUserClient';
import { User } from '../../../../infrastructure/model/User';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';

describe('Rights check apiGW if project or stage sharing on view+create+modify+delete', (): void => {
  let api2: ApiUserClient;
  let user: User;
  let user2: User;
  let endpoint_id: number;
  let endpoint_id_2: number;
  let endpoint_id_3: number;
  let endpoint_id_4: number;
  let endpoint_id_5: number;
  let user_id: number;
  let cookieUser: string;
  let path_id: number;
  let path_id_2: number;
  let path_id_3: number;
  let workspace_id: any;
  let project_short_name: string;
  let stage_short_name: string;
  let project_id: number;
  let stage_id: number;
  let stage_id_3: number;
  let stage_id_4: number;
  let stage_id_5: number;

  const createRequestLink = async (obj_id: number, privs: any): Promise<void> => {
    const responseLink = await api2.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.STAGE,
        obj_id,
        company_id: workspace_id,
        obj_to: 'user',
        obj_to_id: user_id,
        is_need_to_notify: false,
        privs,
      }),
    );
    expect(responseLink.status).toBe(200);
  };

  beforeAll(
    async (): Promise<void> => {
      user = await application.getAuthorizedUser({ company: {} }, 0);
      cookieUser = user.cookieUser;

      user2 = await application.getAuthorizedUser({ company: {} }, 1);
      api2 = await application.getApiUserClient(user2);
      workspace_id = user.companies[0].id;

      const parsedUrl = new URL(`${ConfigurationManager.getConfiguration().getApiGWUrl()}`);
      project_short_name = `project-${Date.now()}`;
      stage_short_name = `stage-${Date.now()}`;

      const repsonseMe = await axiosInstance({
        method: 'GET',
        url: `${ConfigurationManager.getConfiguration().getApiUrl()}auth/me`,
        data: {},
        headers: {
          Cookie: cookieUser,
          origin: parsedUrl,
        },
      });
      user_id = repsonseMe.data.user_id;

      const responseProject = await api2.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.PROJECT,
          company_id: workspace_id,
          title: project_short_name,
          short_name: project_short_name,
          description: 'test',
          stages: [
            {
              title: stage_short_name,
              immutable: false,
            },
            {
              title: `develop`,
              immutable: false,
            },
            {
              title: `develop2`,
              immutable: false,
            },
            {
              title: `develop3`,
              immutable: false,
            },
            {
              title: `develop4`,
              immutable: false,
            },
          ],
          status: 'active',
        }),
      );
      project_id = responseProject.body.ops[0].obj_id;
      stage_id = responseProject.body.ops[0].stages[0];
      stage_id_3 = responseProject.body.ops[0].stages[2];
      stage_id_4 = responseProject.body.ops[0].stages[3];
      stage_id_5 = responseProject.body.ops[0].stages[4];

      await createRequestLink(stage_id, [
        { type: 'view', list_obj: ['all'] },
        { type: 'create', list_obj: ['all'] },
        { type: 'modify', list_obj: ['all'] },
        { type: 'delete', list_obj: ['all'] },
      ]);

      await createRequestLink(stage_id_3, [
        { type: 'view', list_obj: ['all'] },
        { type: 'create', list_obj: ['all'] },
        { type: 'modify', list_obj: ['all'] },
        { type: 'delete', list_obj: ['all'] },
      ]);

      await createRequestLink(stage_id_4, [
        { type: 'view', list_obj: ['all'] },
        { type: 'create', list_obj: ['all'] },
        { type: 'modify', list_obj: ['all'] },
        { type: 'delete', list_obj: ['all'] },
      ]);

      await createRequestLink(stage_id_5, [
        { type: 'view', list_obj: ['all'] },
        { type: 'create', list_obj: ['all'] },
        { type: 'modify', list_obj: ['all'] },
        { type: 'delete', list_obj: ['all'] },
      ]);

      const response = await requestUser2('post', `apis`, { name: `auto_test`, description: '' });
      expect(response.status).toBe(200);
      endpoint_id = response.body.api.id;

      const responsePath = await requestUser2('post', `apis/${endpoint_id}/paths`, {
        path: '/delete/path',
        description: '',
        method: 'DELETE',
        timeout: 29,
        async: true,
        proxy_headers: true,
        proxy_raw_body: true,
        public: { api_login: 777 },
        alias_id: 1,
        alias_short_name: 'test',
        paused: false,
      });
      expect(responsePath.status).toBe(200);
      path_id = responsePath.body.path.id;

      const response2 = await requestUser2('post', `apis`, {
        name: `auto_test_2`,
        description: '',
        workspace_id,
        stage_id,
        project_id,
      });
      expect(response2.status).toBe(200);
      endpoint_id_2 = response2.body.api.id;

      const responsePath2 = await requestUser2('post', `apis/${endpoint_id_2}/paths`, {
        path: '/delete/path',
        description: '',
        method: 'DELETE',
        timeout: 29,
        async: true,
        proxy_headers: true,
        proxy_raw_body: true,
        public: { api_login: 777 },
        alias_id: 1,
        alias_short_name: 'test',
        paused: false,
      });
      expect(responsePath2.status).toBe(200);
      path_id_2 = responsePath2.body.path.id;

      const response3 = await requestUser1('post', `apis`, { name: `auto_test_4`, description: '' });
      expect(response3.status).toBe(200);
      endpoint_id_4 = response3.body.api.id;

      const response4 = await requestUser2('post', `apis`, { name: `auto_test_5`, description: '' });
      expect(response4.status).toBe(200);
      endpoint_id_5 = response4.body.api.id;
    },
  );

  test(`create endpoint if rights on the stage 'view+create++modify+delete' - 200`, async (): Promise<void> => {
    const response = await requestUser1('post', `apis`, {
      name: `auto_test_3`,
      description: '',
      project_id,
      stage_id: stage_id_3,
      workspace_id,
    });
    expect(response.status).toBe(200);
    endpoint_id_3 = response.body.api.id;
  });

  test(`list endpoint if there's an shared stage on the user - 200`, async (): Promise<void> => {
    const response = await requestUser1('get', `apis`);
    expect(response.status).toBe(200);
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id })]));
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_2 })]));
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_5 })]));
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_3 })]));
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_4 })]));
  });

  test(`get endpoints if it is not attached to the project - 403`, async (): Promise<void> => {
    const response = await requestUser1('get', `apis/${endpoint_id}`);
    expect(response.status).toBe(403);
  });

  test(`get endpoint if rights on the stage 'view+create+modify+delete' - 200`, async (): Promise<void> => {
    const response = await requestUser1('get', `apis/${workspace_id}/${project_id}/${stage_id}`);
    expect(response.status).toBe(200);
    expect(response.body.api.name).toEqual(`auto_test_2`);
    expect(response.body.api.id).toEqual(endpoint_id_2);
  });

  test(`get my endpoint if rights on the stage 'view+create+modify+delete' - 200`, async (): Promise<void> => {
    const response = await requestUser1('get', `apis/${workspace_id}/${project_id}/${stage_id_3}`);
    expect(response.status).toBe(200);
    expect(response.body.api.name).toEqual(`auto_test_3`);
    expect(response.body.api.id).toEqual(endpoint_id_3);
  });

  test(`get endpoint unlinked - 200(only my endpoins)`, async (): Promise<void> => {
    const response = await requestUser1('get', `apis/unlinked`);
    expect(response.status).toBe(200);
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id })]));
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_2 })]));
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_3 })]));
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_4 })]));
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_5 })]));
  });

  test(`link endpoint if rights on the stage 'view+create+modify+delete' - 200`, async (): Promise<void> => {
    const response = await requestUser1('post', `apis/${workspace_id}/${project_id}/${stage_id_4}`, {
      apiId: endpoint_id_5,
    });
    expect(response.status).toBe(200);
    expect(response.body.api.id).toEqual(endpoint_id_5);
  });

  test(`link my endpoint if rights on the stage 'view+create+modify+delete' - 200`, async (): Promise<void> => {
    const response = await requestUser1('post', `apis/${workspace_id}/${project_id}/${stage_id_5}`, {
      apiId: endpoint_id_4,
    });
    expect(response.status).toBe(200);
    expect(response.body.api.id).toEqual(endpoint_id_4);
  });

  test(`create paths if rights on the stage 'view+create+modify+delete' - 200`, async (): Promise<void> => {
    const response = await requestUser1('post', `apis/${endpoint_id_2}/paths`, {
      path: '/post/path',
      description: '',
      method: 'DELETE',
      timeout: 29,
      async: true,
      proxy_headers: true,
      proxy_raw_body: true,
      public: { api_login: 777 },
      alias_id: 1,
      alias_short_name: 'test',
      paused: false,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.api_id).toBe(endpoint_id_2);
  });

  test(`create paths in my endpoints if rights on the stage 'view+create+modify+delete' - 200`, async (): Promise<
    void
  > => {
    const response = await requestUser1('post', `apis/${endpoint_id_4}/paths`, {
      path: '/put/path',
      description: '',
      method: 'PUT',
      timeout: 29,
      async: true,
      proxy_headers: true,
      proxy_raw_body: true,
      public: { api_login: 777 },
      alias_id: 1,
      alias_short_name: 'test',
      paused: false,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.api_id).toBe(endpoint_id_4);
    expect(response.status).toBe(200);
    path_id_3 = response.body.path.id;
  });

  test(`list paths endpoint in the staging - 200`, async (): Promise<void> => {
    const response = await requestUser1('get', `apis/${endpoint_id_2}/paths`);
    expect(response.status).toBe(200);
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: path_id })]));
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ id: path_id_2 })]));
  });

  test(`list paths in my endpoint in the staging - 200`, async (): Promise<void> => {
    const response = await requestUser1('get', `apis/${endpoint_id_4}/paths`);
    expect(response.status).toBe(200);
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ id: path_id_3 })]));
  });

  test(`get path endpoint in the staging - 200`, async (): Promise<void> => {
    const response = await requestUser1('get', `apis/${endpoint_id_2}/paths/${path_id_2}`);
    expect(response.status).toBe(200);
    expect(response.body.path.method).toBe(`DELETE`);
    expect(response.body.path.api_id).toBe(endpoint_id_2);
    expect(response.body.path.id).toBe(path_id_2);
  });

  test(`get path in my endpoint in the staging - 200`, async (): Promise<void> => {
    const response = await requestUser1('get', `apis/${endpoint_id_4}/paths/${path_id_3}`);
    expect(response.status).toBe(200);
    expect(response.body.path.method).toBe(`PUT`);
    expect(response.body.path.api_id).toBe(endpoint_id_4);
    expect(response.body.path.id).toBe(path_id_3);
  });

  test(`modify paths if endpoint in the staging - 200`, async (): Promise<void> => {
    const response = await requestUser1('put', `apis/${endpoint_id_2}/paths/${path_id_2}`, {
      path: '/delete/path',
      description: '',
      method: 'DELETE',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      process_id: 555,
      paused: false,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.id).toBe(path_id_2);
  });

  test(`modify paths in my endpoint in the staging - 200`, async (): Promise<void> => {
    const response = await requestUser1('put', `apis/${endpoint_id_4}/paths/${path_id_3}`, {
      path: '/modify/path',
      description: '',
      method: 'DELETE',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      process_id: 555,
      paused: false,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.id).toBe(path_id_3);
  });

  test(`delete path if endpoint in the staging - 204`, async (): Promise<void> => {
    const response = await requestUser1('delete', `apis/${endpoint_id_2}/paths/${path_id_2}`, {});
    expect(response.status).toBe(204);
  });

  test(`delete path in my endpoint in the staging - 204`, async (): Promise<void> => {
    const response = await requestUser1('delete', `apis/${endpoint_id_4}/paths/${path_id_3}`, {});
    expect(response.status).toBe(204);
  });

  test(`delete endpoint if endpoint in the staging - 204`, async (): Promise<void> => {
    const response = await requestUser1('delete', `apis/${endpoint_id_2}`, {});
    expect(response.status).toBe(204);
  });

  test(`delete endpoint in my endpoint in the staging - 204`, async (): Promise<void> => {
    const response = await requestUser1('delete', `apis/${endpoint_id_4}`, {});
    expect(response.status).toBe(204);
  });

  test(`delete endpoint 3/5 in the staging if I owner project - 204`, async (): Promise<void> => {
    const response = await requestUser2('delete', `apis/${endpoint_id_3}`, {});
    expect(response.status).toBe(204);

    const response3 = await requestUser2('delete', `apis/${endpoint_id_5}`, {});
    expect(response3.status).toBe(204);
  });

  afterAll(
    async (): Promise<void> => {
      const responseProject = await api2.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.PROJECT,
          obj_id: project_id,
          company_id: workspace_id,
        }),
      );
      expect(responseProject.status).toBe(200);

      const response = await requestUser2('delete', `apis/${endpoint_id}`, {});
      expect(response.status).toBe(204);
    },
  );
});
