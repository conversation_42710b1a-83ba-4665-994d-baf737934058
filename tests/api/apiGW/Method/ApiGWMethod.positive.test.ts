import { request } from '../../../../application/api/ApiGWUserClient';
import { SchemaValidator } from '../../../../application/api/SchemaValidator';
import createSchema from '.././schemas/createEndpointSchema.json';
import listSchema from '.././schemas/listEndpointSchema.json';
import getSchema from '.././schemas/getEndpointSchema.json';
import createPathSchema from '.././schemas/createPathSchema.json';
import listPathSchema from '.././schemas/listPathSchema.json';
import modifyPathSchema from '.././schemas/modifyPathSchema.json';
import getPathSchema from '.././schemas/getPathSchema.json';
import exportSchema from '.././schemas/exportSchema.json';
import { application } from '../../../../application/Application';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';
import { ApiUserClient } from '../../../../application/api/ApiUserClient';
import { User } from '../../../../infrastructure/model/User';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';

describe('ApiGWMethod (positive)', () => {
  let api: ApiUserClient;
  let user: User;
  let endpoint_id: number;
  let endpoint_id_2: number;
  let endpoint_id_3: number;
  let endpoint_id_5: number;
  let path: string;
  let path_2: string;
  let path_3: string;
  let path_4: string;
  let path_id: number;
  let path_id_2: number;
  let path_id_3: number;
  let path_id_4: number;
  let host: string;
  let external_id: string;
  let workspace_id: any;
  let project_short_name: string;
  let stage_short_name: string;
  let project_id: number;
  let project_id_2: number;
  let stage_id: number;
  let stage_id_2: number;
  let stage_id_3: number;
  let stage_id_4: number;
  let stage_id_5: number;
  let protocol: string;
  let domain: string;
  let conv_id: number;
  let api_login: number;

  beforeAll(async () => {
    external_id = `auto_test2_${Date.now()}`;
    user = await application.getAuthorizedUser({ company: {} }, 0);
    api = await application.getApiUserClient(user);
    workspace_id = user.companies[0].id;
    const parsedUrl = new URL(`${ConfigurationManager.getConfiguration().getApiGWUrl()}`);
    protocol = parsedUrl.protocol;
    project_short_name = `project-${Date.now()}`;
    stage_short_name = `stage-${Date.now()}`;
    const url = ConfigurationManager.getConfiguration().getApiGWUrl();
    domain = new URL(url).hostname;
    api_login = 1234;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id: workspace_id,
        title: project_short_name,
        short_name: project_short_name,
        description: 'test',
        stages: [
          {
            title: stage_short_name,
            immutable: false,
          },
          {
            title: `develop`,
            immutable: false,
          },
          {
            title: `develop2`,
            immutable: false,
          },
          {
            title: `develop3`,
            immutable: false,
          },
        ],
        status: 'active',
      }),
    );
    project_id = responseProject.body.ops[0].obj_id;
    stage_id = responseProject.body.ops[0].stages[0];
    stage_id_2 = responseProject.body.ops[0].stages[1];
    stage_id_3 = responseProject.body.ops[0].stages[2];
    stage_id_4 = responseProject.body.ops[0].stages[3];

    const responseProject2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id: workspace_id,
        title: `project2_${project_short_name}`,
        short_name: `project2${project_short_name}`,
        description: 'test',
        stages: [
          {
            title: stage_short_name,
            immutable: false,
          },
          {
            title: `develop`,
            immutable: false,
          },
          {
            title: `develop2`,
            immutable: false,
          },
          {
            title: `develop3`,
            immutable: false,
          },
        ],
        status: 'active',
      }),
    );
    project_id_2 = responseProject2.body.ops[0].obj_id;
    stage_id_5 = responseProject2.body.ops[0].stages[0];

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id: workspace_id,
        title: `Conv`,
        stage_id: stage_id_5,
      }),
    );
    conv_id = responseConv.body.ops[0].obj_id;
  });

  test(`get config`, async () => {
    const response = await request('get', `config`);
    expect(response.status).toBe(200);
    expect(response.body.config.corezoid_http_host).toBeString();
    expect(response.body.config.single_account_http_host).toBeString();
  });

  test(`get ping`, async () => {
    const response = await request('get', `ping`);
    expect(response.status).toBe(200);
    expect(response.body).toEqual('OK');
  });

  test(`get auth login`, async () => {
    const response = await request('get', `auth/login`);
    expect(response.status).toBe(200);
    expect(response.body.redirect_uri).toBeString();
  });

  test(`create endpoint`, async () => {
    const response = await request('post', `apis`, { name: `auto_test`, description: '' });
    expect(response.status).toBe(200);
    endpoint_id = response.body.api.id;
    host = response.body.api.host;
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`create endpoint2`, async () => {
    const response = await request('post', `apis`, {
      owner_id: 12312,
      name: external_id,
      description: 'test2_description',
      external_id,
    });
    expect(response.status).toBe(200);
    endpoint_id_2 = response.body.api.id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`get endpoint unlinked if only unlinked endpoint`, async () => {
    const response = await request('get', `apis/unlinked`);
    expect(response.status).toBe(200);
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id })]));
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_2 })]));
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`create endpoint3 with project`, async () => {
    const response = await request('post', `apis`, {
      name: `auto_test3`,
      description: 'test2_description',
      workspace_id,
      project_id,
      stage_id,
    });
    expect(response.status).toBe(200);
    endpoint_id_3 = response.body.api.id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`create endpoint4 duplicate in project`, async () => {
    const response = await request('post', `apis`, {
      name: `auto_test4`,
      description: '',
      workspace_id,
      project_id,
      stage_id,
      project_short_name,
      stage_short_name,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual(`error`);
    expect(response.body.description).toEqual(
      `pq: duplicate key value violates unique constraint \"apis_workspace_id_project_id_stage_id\"`,
    );
  });

  test(`create endpoint5 with project`, async () => {
    const response = await request('post', `apis`, {
      name: `auto_test5`,
      description: '',
      workspace_id,
      project_id,
      stage_id: stage_id_2,
    });
    expect(response.status).toBe(200);
    endpoint_id_5 = response.body.api.id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`get endpoint unlinked if created unlinked and link endpoint's`, async () => {
    const response = await request('get', `apis/unlinked`);
    expect(response.status).toBe(200);
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id })]));
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_2 })]));
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_3 })]));
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_5 })]));
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`list endpoint`, async () => {
    const response = await request('get', `apis`);
    expect(response.status).toBe(200);
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id })]));
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_2 })]));
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_3 })]));
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_5 })]));
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ external_id: external_id })]));
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`list external_id endpoint`, async () => {
    const response = await request('get', `apis/?external_id=${external_id}`);
    expect(response.status).toBe(200);
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id })]));
    expect(response.body[0].external_id).toEqual(external_id);
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`list project_id endpoint`, async () => {
    const response = await request('get', `apis/?project_id=${project_id}`);
    expect(response.status).toBe(200);
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id })]));
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_3 })]));
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_5 })]));
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`list stage_id endpoint`, async () => {
    const response = await request('get', `apis/?stage_id=${stage_id}`);
    expect(response.status).toBe(200);
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_2 })]));
    expect(response.body[0].id).toEqual(endpoint_id_3);
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`list workspace_id endpoint`, async () => {
    const response = await request('get', `apis/?workspace_id=${workspace_id}`);
    expect(response.status).toBe(200);
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_2 })]));
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_3 })]));
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_5 })]));
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`get endpoint`, async () => {
    const response = await request('get', `apis/${endpoint_id}`);
    expect(response.status).toBe(200);
    expect(response.body.api.name).toEqual(`auto_test`);
    expect(response.body.api.description).toEqual(``);
    expect(response.body.api.external_id).not.toEqual('');
    SchemaValidator.validate(getSchema, response.body);
  });

  test(`get endpoint2`, async () => {
    const response = await request('get', `apis/${endpoint_id_2}`);
    expect(response.status).toBe(200);
    expect(response.body.api.name).toEqual(external_id);
    expect(response.body.api.description).toEqual(`test2_description`);
    expect(response.body.api.external_id).toEqual(external_id);
    SchemaValidator.validate(getSchema, response.body);
  });

  test(`get endpoint stage`, async () => {
    const response = await request('get', `apis/${workspace_id}/${project_id}/${stage_id}`);
    expect(response.status).toBe(200);
    expect(response.body.api.name).toEqual(`auto_test3`);
    expect(response.body.api.id).toEqual(endpoint_id_3);
    SchemaValidator.validate(getSchema, response.body);
  });

  test(`create paths for get method public`, async () => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path',
      description: 'test_description',
      method: 'GET',
      timeout: 29,
      async: true,
      proxy_headers: true,
      proxy_raw_body: true,
      public: { api_login: 777 },
      process_id: 3,
      paused: true,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.api_id).toBe(endpoint_id);
    path = response.body.path.path;
    path_id = response.body.path.id;
    SchemaValidator.validate(createPathSchema, response.body);
  });

  test(`create paths for post method not public`, async () => {
    await new Promise(r => setTimeout(r, 1000));
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/post/path',
      description: '',
      method: 'POST',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      process_id: 4,
      paused: false,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.api_id).toBe(endpoint_id);
    path_2 = response.body.path.path;
    path_id_2 = response.body.path.id;
    SchemaValidator.validate(createPathSchema, response.body);
  });

  test(`create paths for put method for list`, async () => {
    await new Promise(r => setTimeout(r, 1000));
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/put/param',
      description: '',
      method: 'PUT',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      process_id: 4,
      paused: false,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.api_id).toBe(endpoint_id);
    path_3 = response.body.path.path;
    path_id_3 = response.body.path.id;
    SchemaValidator.validate(createPathSchema, response.body);
  });

  test(`create paths for delete method with alias`, async () => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/delete/path',
      description: '',
      method: 'DELETE',
      timeout: 29,
      async: true,
      proxy_headers: true,
      proxy_raw_body: true,
      public: { api_login: 777 },
      alias_id: 1,
      alias_short_name: 'tes',
      paused: false,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.api_id).toBe(endpoint_id);
    path_4 = response.body.path.path;
    path_id_4 = response.body.path.id;
    SchemaValidator.validate(createPathSchema, response.body);
  });

  test(`list paths`, async () => {
    const response = await request('get', `apis/${endpoint_id}/paths`);
    expect(response.status).toBe(200);
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ path: path })]));
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ path: path_2 })]));
    expect((response.body as Array<any>).find(item => item.path === path_3).process_id).toEqual(4);
    expect((response.body as Array<any>).find(item => item.path === path_4).alias_id).toEqual(1);
    expect((response.body as Array<any>).find(item => item.path === path_3).alias_id).not.toBeNumber();
    expect((response.body as Array<any>).find(item => item.path === path_4).process_id).not.toBeNumber();
    expect(response.body).toEqual(
      expect.arrayContaining([expect.objectContaining({ url: `${protocol}//${host}${path}` })]),
    );
    expect(response.body).toEqual(
      expect.arrayContaining([expect.objectContaining({ url: `${protocol}//${host}${path_2}` })]),
    );
    expect(response.body).toEqual(
      expect.arrayContaining([expect.objectContaining({ url: `${protocol}//${host}${path_3}` })]),
    );
    SchemaValidator.validate(listPathSchema, response.body);
  });

  test(`list paths sort direction=asc`, async () => {
    const response = await request(
      'get',
      `apis/${endpoint_id}/paths`,
      {},
      {
        path_fragment: 'path',
        sort: 'path',
        direction: 'asc',
      },
    );
    expect(response.status).toBe(200);
    expect(response.body[0].path).toBe(path_4);
    expect(response.body[1].path).toBe(path);
    expect(response.body[2].path).toBe(path_2);
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ path: path_3 })]));
    SchemaValidator.validate(listPathSchema, response.body);
  });

  test(`list paths sort direction=desc`, async () => {
    const response = await request(
      'get',
      `apis/${endpoint_id}/paths`,
      {},
      {
        path_fragment: 'path',
        sort: 'path',
        direction: 'desc',
      },
    );
    expect(response.status).toBe(200);
    expect(response.body[1].path).toBe(path);
    expect(response.body[0].path).toBe(path_2);
    expect(response.body[2].path).toBe(path_4);
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ path: path_3 })]));
    SchemaValidator.validate(listPathSchema, response.body);
  });

  test(`list paths sort withou path_fragment`, async () => {
    const response = await request(
      'get',
      `apis/${endpoint_id}/paths`,
      {},
      {
        sort: 'path',
        direction: 'desc',
      },
    );
    expect(response.status).toBe(200);
    expect(response.body[2].path).toBe(path);
    expect(response.body[1].path).toBe(path_2);
    expect(response.body[0].path).toBe(path_3);
    expect(response.body[3].path).toBe(path_4);
    SchemaValidator.validate(listPathSchema, response.body);
  });

  test(`list paths sort withou path_fragment and sort=updated_at`, async () => {
    const response = await request(
      'get',
      `apis/${endpoint_id}/paths`,
      {},
      {
        sort: 'updated_at',
        direction: 'desc',
      },
    );
    expect(response.status).toBe(200);
    expect(response.body[0].path).toBe(path_4);
    expect(response.body[3].path).toBe(path);
    expect(response.body[2].path).toBe(path_2);
    expect(response.body[1].path).toBe(path_3);
    SchemaValidator.validate(listPathSchema, response.body);
  });

  test(`modify paths get->put method public->not public`, async () => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/put/path',
      description: '',
      method: 'PUT',
      timeout: 35,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      process_id: 1234,
      paused: false,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.id).toBe(path_id);
    path = response.body.path.path;
    SchemaValidator.validate(modifyPathSchema, response.body);
  });

  test(`modify paths post->put method not public->public`, async () => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id_2}`, {
      path: '/patch/path',
      description: 'modify',
      method: 'PATCH',
      timeout: 5,
      async: true,
      proxy_headers: true,
      proxy_raw_body: true,
      process_id: 55,
      public: { api_login: 123 },
      paused: true,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.id).toBe(path_id_2);
    path_2 = response.body.path.path;
    SchemaValidator.validate(modifyPathSchema, response.body);
  });

  test(`modify paths to alias_id`, async () => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id_3}`, {
      path: '/put/param',
      description: '',
      method: 'PUT',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      alias_id: 2,
      alias_short_name: 'alias',
      paused: false,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.id).toBe(path_id_3);
    expect(response.body.path.alias_id).toBe(2);
    expect(response.body.path.process_id).not.toBeNumber();
    path_3 = response.body.path.path;
    SchemaValidator.validate(modifyPathSchema, response.body);
  });

  test(`get path`, async () => {
    const response = await request('get', `apis/${endpoint_id}/paths/${path_id}`);
    expect(response.status).toBe(200);
    expect(response.body.path.path).toBe(`/put/path`);
    expect(response.body.path.url).toBe(`${protocol}//${host}/put/path`);
    expect(response.body.path.method).toBe(`PUT`);
    expect(response.body.path.timeout).toBe(35);
    expect(response.body.path.id).toBe(path_id);
    expect(response.body.path.process_id).toBe(1234);
    expect(response.body.path.paused).toBe(false);
    expect(response.body.path.proxy_headers).toBe(false);
    expect(response.body.path.proxy_raw_body).toBe(false);
    expect(response.body.path.async).toBe(false);
    SchemaValidator.validate(getPathSchema, response.body);
  });

  test(`get path path_2`, async () => {
    const response = await request('get', `apis/${endpoint_id}/paths/${path_id_2}`);
    expect(response.status).toBe(200);
    expect(response.body.path.public.api_login).toBe(123);
    expect(response.body.path.path).toBe(`/patch/path`);
    expect(response.body.path.url).toBe(`${protocol}//${host}/patch/path`);
    expect(response.body.path.method).toBe(`PATCH`);
    expect(response.body.path.timeout).toBe(5);
    expect(response.body.path.id).toBe(path_id_2);
    expect(response.body.path.process_id).toBe(55);
    expect(response.body.path.alias_id).not.toBeNumber();
    expect(response.body.path.paused).toBe(true);
    expect(response.body.path.proxy_headers).toBe(true);
    expect(response.body.path.proxy_raw_body).toBe(true);
    expect(response.body.path.async).toBe(true);
    SchemaValidator.validate(getPathSchema, response.body);
  });

  test(`get path path_4`, async () => {
    const response = await request('get', `apis/${endpoint_id}/paths/${path_id_4}`);
    expect(response.status).toBe(200);
    expect(response.body.path.public.api_login).toBe(777);
    expect(response.body.path.path).toBe(`/delete/path`);
    expect(response.body.path.url).toBe(`${protocol}//${host}/delete/path`);
    expect(response.body.path.method).toBe(`DELETE`);
    expect(response.body.path.timeout).toBe(29);
    expect(response.body.path.id).toBe(path_id_4);
    expect(response.body.path.process_id).not.toBeNumber();
    expect(response.body.path.alias_id).toBe(1);
    expect(response.body.path.alias_short_name).toBe('tes');
    expect(response.body.path.paused).toBe(false);
    expect(response.body.path.proxy_headers).toBe(true);
    expect(response.body.path.proxy_raw_body).toBe(true);
    expect(response.body.path.async).toBe(true);
    SchemaValidator.validate(getPathSchema, response.body);
  });

  test(`modify paths to process_id`, async () => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id_4}`, {
      path: '/delete/path',
      description: '',
      method: 'DELETE',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      process_id: 555,
      paused: false,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.id).toBe(path_id_4);
    expect(response.body.path.process_id).toBe(555);
    expect(response.body.path.alias_id).not.toBeNumber();
    path_4 = response.body.path.path;
    SchemaValidator.validate(modifyPathSchema, response.body);
  });

  test(`create paths with spaces`, async () => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path/{spaces}\t\n ',
      description: 'test_description',
      method: 'GET',
      timeout: 29,
      async: true,
      proxy_headers: true,
      proxy_raw_body: true,
      public: { api_login: 777 },
      process_id: 3,
      paused: true,
    });
    expect(response.status).toBe(200);
    expect(response.body.path.api_id).toBe(endpoint_id);
    expect(response.body.path.path).toBe('/get/path/{spaces}');
    SchemaValidator.validate(createPathSchema, response.body);
  });

  test(`link endpoint/endpoint2 to stage`, async () => {
    const response = await request('post', `apis/${workspace_id}/${project_id}/${stage_id_3}`, {
      apiId: endpoint_id,
    });
    expect(response.status).toBe(200);
    expect(response.body.api.id).toEqual(endpoint_id);

    const response2 = await request('post', `apis/${workspace_id}/${project_id}/${stage_id_4}`, {
      apiId: endpoint_id_2,
    });
    expect(response2.status).toBe(200);
    expect(response2.body.api.id).toEqual(endpoint_id_2);
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`get endpoint unlinked if all endpoint's link`, async () => {
    const response = await request('get', `apis/unlinked`);
    expect(response.status).toBe(200);
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id })]));
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_2 })]));
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_3 })]));
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_5 })]));
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`export endpoint`, async () => {
    const response = await request('get', `apis/export/${workspace_id}/${project_id}`);
    expect(response.status).toBe(200);
    expect(response.body.apis).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id })]));
    expect(response.body.apis).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_2 })]));
    expect(response.body.apis).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_3 })]));
    expect(response.body.apis).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_5 })]));
    expect(response.body.apis[2].paths).toEqual(expect.arrayContaining([expect.objectContaining({ path: path })]));
    expect(response.body.apis[2].paths).toEqual(expect.arrayContaining([expect.objectContaining({ path: path_2 })]));
    expect(response.body.apis[2].paths).toEqual(expect.arrayContaining([expect.objectContaining({ path: path_3 })]));
    SchemaValidator.validate(exportSchema, response.body);
  });

  test(`delete path`, async () => {
    const response = await request('delete', `apis/${endpoint_id}/paths/${path_id}`, {});
    expect(response.status).toBe(204);
  });

  test(`list paths after delete`, async () => {
    const response = await request('get', `apis/${endpoint_id}/paths`);
    expect(response.status).toBe(200);
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ path: path })]));
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ path: path_2 })]));
    SchemaValidator.validate(listPathSchema, response.body);
  });

  test(`delete endpoint/3/4`, async () => {
    const response = await request('delete', `apis/${endpoint_id}`, {});
    expect(response.status).toBe(204);

    const response3 = await request('delete', `apis/${endpoint_id_3}`, {});
    expect(response3.status).toBe(204);

    const response4 = await request('delete', `apis/${endpoint_id_5}`, {});
    expect(response4.status).toBe(204);
  });

  test(`list endpoint after delete`, async () => {
    const response = await request('get', `apis`);
    expect(response.status).toBe(200);
    expect(response.body).not.toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id })]));
    expect(response.body).toEqual(expect.arrayContaining([expect.objectContaining({ id: endpoint_id_2 })]));
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`delete endpoint2`, async () => {
    const response = await request('delete', `apis/${endpoint_id_2}`, {});
    expect(response.status).toBe(204);
  });

  test(`create endpoint3 in project after delete endpoint3`, async () => {
    const response = await request('post', `apis`, {
      name: `auto_test3`,
      description: '',
      workspace_id,
      project_id,
      stage_id,
    });
    expect(response.status).toBe(200);
    endpoint_id_3 = response.body.api.id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`delete endpoint3 after recreating`, async () => {
    const response = await request('delete', `apis/${endpoint_id_3}`, {});
    expect(response.status).toBe(204);
  });

  test(`merge endpoint in empty stage`, async () => {
    const response = await request('post', `apis/import/merge/${workspace_id}/${project_id}`, {
      apis: [
        {
          project_id,
          workspace_id: workspace_id,
          stage_short_name: 'develop',
          stage_id,
          project_short_name,
          description: '',
          id: 'f04gvomxv8',
          host: `f04gvomxv8.${domain}`,
          name: 'auto_test',
          paths: [
            {
              description: '',
              process_id: conv_id,
              api_id: 'f04gvomxv8',
              url: `https://f04gvomxv8.${domain}/put/path`,
              method: 'PUT',
              path: '/put/path',
              timeout: 35,
              paused: false,
              proxy_headers: false,
              proxy_raw_body: false,
              async: false,
            },
            {
              description: 'modify',
              process_id: conv_id,
              public: {
                api_login,
              },
              api_id: `f04gvomxv8`,
              url: `https://f04gvomxv8.${domain}/patch/path`,
              method: 'PATCH',
              path: '/patch/path',
              timeout: 5,
              updated_at: 1697543476,
              paused: false,
              proxy_headers: true,
              proxy_raw_body: true,
              async: true,
            },
          ],
          updated_at: 1697543478,
          created_at: 1697543467,
        },
        {
          project_id,
          workspace_id: workspace_id,
          stage_short_name: 'develop2',
          stage_id: stage_id_2,
          project_short_name,
          description: '',
          id: '777test',
          host: `777test.${domain}`,
          name: 'auto_test',
          paths: [
            {
              description: '',
              process_id: conv_id,
              api_id: '777test',
              url: `https://777test.${domain}/put/path`,
              method: 'PUT',
              path: '/put/path',
              timeout: 35,
              paused: false,
              proxy_headers: false,
              proxy_raw_body: false,
              async: false,
            },
            {
              description: 'modify',
              process_id: conv_id,
              public: {
                api_login,
              },
              api_id: `777test`,
              url: `https://777test.${domain}/patch/path`,
              method: 'PATCH',
              path: '/patch/path',
              timeout: 5,
              updated_at: 1697543476,
              paused: true,
              proxy_headers: true,
              proxy_raw_body: true,
              async: true,
            },
          ],
          updated_at: 1697543478,
          created_at: 1697543467,
        },
      ],
    });
    expect(response.status).toBe(200);
    expect(response.body).toBeObject();
  });

  afterAll(async () => {
    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: project_id,
        company_id: workspace_id,
      }),
    );
    expect(responseProject.status).toBe(200);

    const responseProject2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: project_id_2,
        company_id: workspace_id,
      }),
    );
    expect(responseProject2.status).toBe(200);
  });
});
