import { request } from '../../../../application/api/ApiGWUserClient';
import faker from 'faker';
import { application } from '../../../../application/Application';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';
import { ApiUserClient } from '../../../../application/api/ApiUserClient';
import { User } from '../../../../infrastructure/model/User';

describe('ApiGWMethod (negative)', () => {
  let api: ApiUserClient;
  let user: User;
  let endpoint_id: number;
  let endpoint_id_2: number;
  let path_id: number;
  let workspace_id: any;
  let project_short_name: string;
  let stage_short_name: string;
  let project_id: number;
  let stage_id: number;
  let stage_id_2: number;

  beforeAll(async () => {
    user = await application.getAuthorizedUser({ company: {} });
    api = await application.getApiUserClient(user);
    workspace_id = user.companies[0].id;
    project_short_name = `project-${Date.now()}`;
    stage_short_name = `stage-${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id: workspace_id,
        title: project_short_name,
        short_name: project_short_name,
        description: 'test',
        stages: [
          {
            title: stage_short_name,
            immutable: false,
          },
          {
            title: `develop`,
            immutable: false,
          },
        ],
        status: 'active',
      }),
    );
    project_id = responseProject.body.ops[0].obj_id;
    stage_id = responseProject.body.ops[0].stages[0];
    stage_id_2 = responseProject.body.ops[0].stages[1];

    const response = await request('post', `apis`, {
      name: `auto_test`,
      description: '',
      workspace_id,
      project_id,
      stage_id,
    });
    expect(response.status).toBe(200);
    endpoint_id = response.body.api.id;

    const response2 = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path',
      description: 'test_description',
      method: 'GET',
      timeout: 29,
      async: true,
      proxy_headers: true,
      proxy_raw_body: true,
      public: { api_login: 777 },
      process_id: 3,
      paused: true,
    });
    expect(response2.status).toBe(200);
    expect(response2.body.path.api_id).toBe(endpoint_id);
    path_id = response2.body.path.id;

    const response3 = await request('post', `apis`, {
      name: `auto_test`,
      description: '',
    });
    expect(response3.status).toBe(200);
    endpoint_id_2 = response3.body.api.id;
  });

  test(`get endpoint api not found`, async () => {
    const response = await request('get', `apis/${workspace_id}/${project_id}/${stage_id_2}`);
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual(`error`);
    expect(response.body.description).toEqual(`api not found`);
  });

  test(`get endpoint non-existing project/stage`, async () => {
    const response = await request('get', `apis/${workspace_id}/1234/1234`);
    expect(response.status).toBe(403);
  });

  test(`link endpoint if the endpoint is already attached to another stage`, async () => {
    const response = await request('post', `apis/${workspace_id}/${project_id}/${stage_id_2}`, {
      apiId: endpoint_id,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual(`error`);
    expect(response.body.description).toEqual(`api already linked`);
  });

  test(`link endpoint if there's another endpoint already attached to the stage`, async () => {
    const response = await request('post', `apis/${workspace_id}/${project_id}/${stage_id}`, {
      apiId: endpoint_id_2,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual(`error`);
    expect(response.body.description).toEqual(
      `pq: duplicate key value violates unique constraint \"apis_workspace_id_project_id_stage_id\"`,
    );
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field CreateAPIRequest.name of type string`],
    [[], `json: cannot unmarshal array into Go struct field CreateAPIRequest.name of type string`],
    [{}, `json: cannot unmarshal object into Go struct field CreateAPIRequest.name of type string`],
    [0, `json: cannot unmarshal number into Go struct field CreateAPIRequest.name of type string`],
    [-1, `json: cannot unmarshal number into Go struct field CreateAPIRequest.name of type string`],
    [1, `json: cannot unmarshal number into Go struct field CreateAPIRequest.name of type string`],
    [undefined, `Key: 'CreateAPIRequest.name' Error:Field validation for 'name' failed on the 'required' tag`],
    [null, `Key: 'CreateAPIRequest.name' Error:Field validation for 'name' failed on the 'required' tag`],
    [
      faker.random.alphaNumeric(2000),
      `Key: 'CreateAPIRequest.name' Error:Field validation for 'name' failed on the 'max' tag`,
    ],
  ])(`shouldn't create endpoint with invalid name '%s'`, async (name, reason) => {
    const response = await request('post', `apis`, { name, description: '' });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field CreateAPIRequest.description of type string`],
    [[], `json: cannot unmarshal array into Go struct field CreateAPIRequest.description of type string`],
    [{}, `json: cannot unmarshal object into Go struct field CreateAPIRequest.description of type string`],
    [0, `json: cannot unmarshal number into Go struct field CreateAPIRequest.description of type string`],
    [-1, `json: cannot unmarshal number into Go struct field CreateAPIRequest.description of type string`],
    [1, `json: cannot unmarshal number into Go struct field CreateAPIRequest.description of type string`],
    [
      faker.random.alphaNumeric(6000),
      `Key: 'CreateAPIRequest.description' Error:Field validation for 'description' failed on the 'max' tag`,
    ],
  ])(`shouldn't create endpoint with invalid description '%s'`, async (description, reason) => {
    const response = await request('post', `apis`, { name: 'test', description });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field CreateAPIRequest.external_id of type string`],
    [[], `json: cannot unmarshal array into Go struct field CreateAPIRequest.external_id of type string`],
    [{}, `json: cannot unmarshal object into Go struct field CreateAPIRequest.external_id of type string`],
    [0, `json: cannot unmarshal number into Go struct field CreateAPIRequest.external_id of type string`],
    [-1, `json: cannot unmarshal number into Go struct field CreateAPIRequest.external_id of type string`],
    [1, `json: cannot unmarshal number into Go struct field CreateAPIRequest.external_id of type string`],
    [
      faker.random.alphaNumeric(3000),
      `Key: 'CreateAPIRequest.description' Error:Field validation for 'description' failed on the 'max' tag`,
    ],
  ])(`shouldn't create endpoint with invalid external_id '%s'`, async (external_id, reason) => {
    const response = await request('post', `apis`, { name: 'test', external_id });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field CreateAPIRequest.project_id of type int`],
    [[], `json: cannot unmarshal array into Go struct field CreateAPIRequest.project_id of type int`],
    [{}, `json: cannot unmarshal object into Go struct field CreateAPIRequest.project_id of type int`],
    [
      null,
      `Key: 'CreateAPIRequest.project_id' Error:Field validation for 'project_id' failed on the 'required_with' tag`,
    ],
    [
      undefined,
      `Key: 'CreateAPIRequest.project_id' Error:Field validation for 'project_id' failed on the 'required_with' tag`,
    ],
    [-1, `Key: 'CreateAPIRequest.project_id' Error:Field validation for 'project_id' failed on the 'min' tag`],
    ['', `json: cannot unmarshal string into Go struct field CreateAPIRequest.project_id of type int`],
    ['test', `json: cannot unmarshal string into Go struct field CreateAPIRequest.project_id of type int`],
  ])(`shouldn't create endpoint with invalid project_id '%s'`, async (project_id, reason) => {
    const response = await request('post', `apis`, {
      name: 'test',
      description: '',
      workspace_id,
      project_id,
      stage_id,
      project_short_name,
      stage_short_name,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field CreateAPIRequest.stage_id of type int`],
    [[], `json: cannot unmarshal array into Go struct field CreateAPIRequest.stage_id of type int`],
    [{}, `json: cannot unmarshal object into Go struct field CreateAPIRequest.stage_id of type int`],
    [null, `Key: 'CreateAPIRequest.stage_id' Error:Field validation for 'stage_id' failed on the 'required_with' tag`],
    [
      undefined,
      `Key: 'CreateAPIRequest.stage_id' Error:Field validation for 'stage_id' failed on the 'required_with' tag`,
    ],
    [-1, `Key: 'CreateAPIRequest.stage_id' Error:Field validation for 'stage_id' failed on the 'min' tag`],
    ['', `json: cannot unmarshal string into Go struct field CreateAPIRequest.stage_id of type int`],
    ['test', `json: cannot unmarshal string into Go struct field CreateAPIRequest.stage_id of type int`],
  ])(`shouldn't create endpoint with invalid stage_id '%s'`, async (stage_id, reason) => {
    const response = await request('post', `apis`, {
      name: 'test',
      description: '',
      workspace_id,
      project_id,
      stage_id,
      project_short_name,
      stage_short_name,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field CreateAPIRequest.workspace_id of type string`],
    [[], `json: cannot unmarshal array into Go struct field CreateAPIRequest.workspace_id of type string`],
    [{}, `json: cannot unmarshal object into Go struct field CreateAPIRequest.workspace_id of type string`],
    [
      null,
      `Key: 'CreateAPIRequest.workspace_id' Error:Field validation for 'workspace_id' failed on the 'required_with' tag`,
    ],
    [
      undefined,
      `Key: 'CreateAPIRequest.workspace_id' Error:Field validation for 'workspace_id' failed on the 'required_with' tag`,
    ],
    [-1, `json: cannot unmarshal number into Go struct field CreateAPIRequest.workspace_id of type string`],
    [0, `json: cannot unmarshal number into Go struct field CreateAPIRequest.workspace_id of type string`],
  ])(`shouldn't create endpoint with invalid workspace_id '%s'`, async (workspace_id, reason) => {
    const response = await request('post', `apis`, {
      name: 'test',
      description: '',
      workspace_id,
      project_id,
      stage_id,
      project_short_name,
      stage_short_name,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each(['put', 'delete', 'patch'])(`shouldn't create endpoint with other method '%s'`, async method => {
    const response = await request(method, `apis`, { name: 'test', description: 'method' });
    expect(response.status).toBe(405);
  });

  test.each([
    [true, `apiId true not found`],
    [-1, `apiId -1 not found`],
    ['patch', `apiId patch not found`],
  ])(`shouldn't list endpoint with invalid endpoint_id '%s'`, async (endpoint_id, reason) => {
    const response = await request('get', `apis/${endpoint_id}`);
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each(['put', 'post', 'patch'])(`shouldn't list endpoint with other method '%s'`, async method => {
    const response = await request(method, `apis/${endpoint_id}`);
    expect(response.status).toBe(405);
  });

  test.each(['put', 'delete', 'patch'])(`shouldn't get endpoint stage with other method '%s'`, async method => {
    const response = await request(method, `apis/${workspace_id}/${project_id}/${stage_id}`);
    expect(response.status).toBe(405);
  });

  test.each([
    [true, `apiId true not found`],
    [-1, `apiId -1 not found`],
    ['patch', `apiId patch not found`],
    [{}, `apiId [object%20Object] not found`],
  ])(`shouldn't delete endpoint with invalid endpoint_id '%s'`, async (endpoint_id, reason) => {
    const response = await request('delete', `apis/${endpoint_id}`);
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field LinkAPIRequest.apiId of type string`],
    [[], `json: cannot unmarshal array into Go struct field LinkAPIRequest.apiId of type string`],
    [{}, `json: cannot unmarshal object into Go struct field LinkAPIRequest.apiId of type string`],
    [null, `Key: 'LinkAPIRequest.apiId' Error:Field validation for 'apiId' failed on the 'required' tag`],
    [undefined, `Key: 'LinkAPIRequest.apiId' Error:Field validation for 'apiId' failed on the 'required' tag`],
    [-1, `json: cannot unmarshal number into Go struct field LinkAPIRequest.apiId of type string`],
    ['', `Key: 'LinkAPIRequest.apiId' Error:Field validation for 'apiId' failed on the 'required' tag`],
  ])(`shouldn't link to stage with invalid endpoint_id '%s'`, async (apiId, reason) => {
    const response = await request('post', `apis/${workspace_id}/${project_id}/${stage_id_2}`, {
      apiId,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each(['put', 'post', 'patch'])(`shouldn't get endpoint unlinked with other method '%s'`, async method => {
    const response = await request(method, `apis/unlinked`);
    expect(response.status).toBe(405);
  });

  test.each([-1, true, 'patch'])(`shouldn't create paths with invalid endpoint_id '%s'`, async endpoint_id => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path',
      description: 'test_description',
      method: 'GET',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: 777 },
      process_id: 3,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(`apiId ${endpoint_id} not found`);
  });

  test(`shouldn't create paths with alias_id/alias_short_name/process_id`, async () => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/delete/path',
      description: '',
      method: 'DELETE',
      timeout: 29,
      async: true,
      proxy_headers: true,
      proxy_raw_body: true,
      public: { api_login: 777 },
      alias_id: 1,
      alias_short_name: 'test',
      process_id: 4,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(
      `Key: 'CreatePathRequest.process_id' Error:Field validation for 'process_id' failed on the 'excluded_with' tag\nKey: 'CreatePathRequest.alias_id' Error:Field validation for 'alias_id' failed on the 'excluded_with' tag\nKey: 'CreatePathRequest.alias_short_name' Error:Field validation for 'alias_short_name' failed on the 'excluded_with' tag`,
    );
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field CreatePathRequest.path of type string`],
    [`get/path`, `Key: 'CreatePathRequest.path' Error:Field validation for 'path' failed on the 'startswith' tag`],
    [[], `json: cannot unmarshal array into Go struct field CreatePathRequest.path of type string`],
    [{}, `json: cannot unmarshal object into Go struct field CreatePathRequest.path of type string`],
    [0, `json: cannot unmarshal number into Go struct field CreatePathRequest.path of type string`],
    [-1, `json: cannot unmarshal number into Go struct field CreatePathRequest.path of type string`],
    [1, `json: cannot unmarshal number into Go struct field CreatePathRequest.path of type string`],
    [undefined, `Key: 'CreatePathRequest.path' Error:Field validation for 'path' failed on the 'required' tag`],
    [null, `Key: 'CreatePathRequest.path' Error:Field validation for 'path' failed on the 'required' tag`],
  ])(`shouldn't create paths with invalid path '%s'`, async (path, reason) => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path,
      description: 'test_description',
      method: 'GET',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: 777 },
      process_id: 3,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field CreatePathRequest.description of type string`],
    [[], `json: cannot unmarshal array into Go struct field CreatePathRequest.description of type string`],
    [{}, `json: cannot unmarshal object into Go struct field CreatePathRequest.description of type string`],
    [0, `json: cannot unmarshal number into Go struct field CreatePathRequest.description of type string`],
    [-1, `json: cannot unmarshal number into Go struct field CreatePathRequest.description of type string`],
    [1, `json: cannot unmarshal number into Go struct field CreatePathRequest.description of type string`],
    [
      faker.random.alphaNumeric(6000),
      `Key: 'CreatePathRequest.description' Error:Field validation for 'description' failed on the 'max' tag`,
    ],
  ])(`shouldn't create paths with invalid description '%s'`, async (description, reason) => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path',
      description,
      method: 'GET',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: 777 },
      process_id: 3,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field CreatePathRequest.alias_short_name of type string`],
    [[], `json: cannot unmarshal array into Go struct field CreatePathRequest.alias_short_name of type string`],
    [
      null,
      `Key: 'CreatePathRequest.alias_short_name' Error:Field validation for 'alias_short_name' failed on the 'required_with' tag`,
    ],
    [
      undefined,
      `Key: 'CreatePathRequest.alias_short_name' Error:Field validation for 'alias_short_name' failed on the 'required_with' tag`,
    ],
    [{}, `json: cannot unmarshal object into Go struct field CreatePathRequest.alias_short_name of type string`],
    [0, `json: cannot unmarshal number into Go struct field CreatePathRequest.alias_short_name of type string`],
    [-1, `json: cannot unmarshal number into Go struct field CreatePathRequest.alias_short_name of type string`],
    [
      '',
      `Key: 'CreatePathRequest.alias_short_name' Error:Field validation for 'alias_short_name' failed on the 'alphaNumericLower' tag`,
    ],
    [
      't',
      `Key: 'CreatePathRequest.alias_short_name' Error:Field validation for 'alias_short_name' failed on the 'min' tag`,
    ],
    [
      faker.random.alphaNumeric(128),
      `Key: 'CreatePathRequest.alias_short_name' Error:Field validation for 'alias_short_name' failed on the 'max' tag`,
    ],
  ])(`shouldn't create paths with invalid alias_short_name '%s'`, async (alias_short_name, reason) => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path1234',
      description: '',
      method: 'GET',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: 777 },
      alias_id: 1,
      alias_short_name,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field CreatePathRequest.alias_id of type int`],
    [`get/path`, `json: cannot unmarshal string into Go struct field CreatePathRequest.alias_id of type int`],
    [[], `json: cannot unmarshal array into Go struct field CreatePathRequest.alias_id of type int`],
    [{}, `json: cannot unmarshal object into Go struct field CreatePathRequest.alias_id of type int`],
    ['', `json: cannot unmarshal string into Go struct field CreatePathRequest.alias_id of type int`],
    [-1, `Key: 'CreatePathRequest.alias_id' Error:Field validation for 'alias_id' failed on the 'min' tag`],
    [
      undefined,
      `Key: 'CreatePathRequest.process_id' Error:Field validation for 'process_id' failed on the 'required_without' tag\nKey: 'CreatePathRequest.alias_id' Error:Field validation for 'alias_id' failed on the 'required_without' tag`,
    ],
    [
      null,
      `Key: 'CreatePathRequest.process_id' Error:Field validation for 'process_id' failed on the 'required_without' tag\nKey: 'CreatePathRequest.alias_id' Error:Field validation for 'alias_id' failed on the 'required_without' tag`,
    ],
  ])(`shouldn't create paths with invalid alias_id '%s'`, async (alias_id, reason) => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path',
      description: 'test',
      method: 'POST',
      timeout: 12,
      async: false,
      proxy_headers: true,
      proxy_raw_body: false,
      public: { api_login: 777 },
      alias_id,
      alias_short_name: 'test',
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field CreatePathRequest.method of type string`],
    [`get/path`, `Key: 'CreatePathRequest.method' Error:Field validation for 'method' failed on the 'oneof' tag`],
    [[], `json: cannot unmarshal array into Go struct field CreatePathRequest.method of type string`],
    [{}, `json: cannot unmarshal object into Go struct field CreatePathRequest.method of type string`],
    [0, `json: cannot unmarshal number into Go struct field CreatePathRequest.method of type string`],
    [-1, `json: cannot unmarshal number into Go struct field CreatePathRequest.method of type string`],
    [1, `json: cannot unmarshal number into Go struct field CreatePathRequest.method of type string`],
    [undefined, `Key: 'CreatePathRequest.method' Error:Field validation for 'method' failed on the 'required' tag`],
  ])(`shouldn't create paths with invalid method '%s'`, async (method, reason) => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path',
      description: 'test',
      method,
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: 777 },
      process_id: 3,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field CreatePathRequest.timeout of type int`],
    [`get/path`, `json: cannot unmarshal string into Go struct field CreatePathRequest.timeout of type int`],
    [[], `json: cannot unmarshal array into Go struct field CreatePathRequest.timeout of type int`],
    [{}, `json: cannot unmarshal object into Go struct field CreatePathRequest.timeout of type int`],
    [0, `Key: 'CreatePathRequest.timeout' Error:Field validation for 'timeout' failed on the 'min' tag`],
    [700, `Key: 'CreatePathRequest.timeout' Error:Field validation for 'timeout' failed on the 'max' tag`],
    [-1, `Key: 'CreatePathRequest.timeout' Error:Field validation for 'timeout' failed on the 'min' tag`],
  ])(`shouldn't create paths with invalid timeout '%s'`, async (timeout, reason) => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path',
      description: 'test',
      method: 'POST',
      timeout,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: 777 },
      process_id: 3,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [`get/path`, `json: cannot unmarshal string into Go struct field CreatePathRequest.async of type bool`],
    [[], `json: cannot unmarshal array into Go struct field CreatePathRequest.async of type bool`],
    [{}, `json: cannot unmarshal object into Go struct field CreatePathRequest.async of type bool`],
    [0, `json: cannot unmarshal number into Go struct field CreatePathRequest.async of type bool`],
    [-1, `json: cannot unmarshal number into Go struct field CreatePathRequest.async of type bool`],
    [1, `json: cannot unmarshal number into Go struct field CreatePathRequest.async of type bool`],
  ])(`shouldn't create paths with invalid async '%s'`, async (async, reason) => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path',
      description: 'test',
      method: 'POST',
      timeout: 12,
      async,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: 777 },
      process_id: 3,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [`get/path`, `json: cannot unmarshal string into Go struct field CreatePathRequest.proxy_headers of type bool`],
    [[], `json: cannot unmarshal array into Go struct field CreatePathRequest.proxy_headers of type bool`],
    [{}, `json: cannot unmarshal object into Go struct field CreatePathRequest.proxy_headers of type bool`],
    [0, `json: cannot unmarshal number into Go struct field CreatePathRequest.proxy_headers of type bool`],
    [-1, `json: cannot unmarshal number into Go struct field CreatePathRequest.proxy_headers of type bool`],
    [1, `json: cannot unmarshal number into Go struct field CreatePathRequest.proxy_headers of type bool`],
  ])(`shouldn't create paths with invalid proxy_headers '%s'`, async (proxy_headers, reason) => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path',
      description: 'test',
      method: 'POST',
      timeout: 12,
      async: false,
      proxy_headers,
      proxy_raw_body: false,
      public: { api_login: 777 },
      process_id: 3,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [`get/path`, `json: cannot unmarshal string into Go struct field CreatePathRequest.proxy_raw_body of type bool`],
    [[], `json: cannot unmarshal array into Go struct field CreatePathRequest.proxy_raw_body of type bool`],
    [{}, `json: cannot unmarshal object into Go struct field CreatePathRequest.proxy_raw_body of type bool`],
    [0, `json: cannot unmarshal number into Go struct field CreatePathRequest.proxy_raw_body of type bool`],
    [-1, `json: cannot unmarshal number into Go struct field CreatePathRequest.proxy_raw_body of type bool`],
    [1, `json: cannot unmarshal number into Go struct field CreatePathRequest.proxy_raw_body of type bool`],
  ])(`shouldn't create paths with invalid proxy_raw_body '%s'`, async (proxy_raw_body, reason) => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path',
      description: 'test',
      method: 'POST',
      timeout: 12,
      async: false,
      proxy_headers: true,
      proxy_raw_body,
      public: { api_login: 777 },
      process_id: 3,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [`get/path`, `json: cannot unmarshal string into Go struct field CreatePathRequest.paused of type bool`],
    [[], `json: cannot unmarshal array into Go struct field CreatePathRequest.paused of type bool`],
    [{}, `json: cannot unmarshal object into Go struct field CreatePathRequest.paused of type bool`],
    [0, `json: cannot unmarshal number into Go struct field CreatePathRequest.paused of type bool`],
    [-1, `json: cannot unmarshal number into Go struct field CreatePathRequest.paused of type bool`],
    [1, `json: cannot unmarshal number into Go struct field CreatePathRequest.paused of type bool`],
  ])(`shouldn't create paths with invalid paused '%s'`, async (paused, reason) => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path',
      description: 'test',
      method: 'POST',
      timeout: 12,
      async: false,
      proxy_headers: true,
      proxy_raw_body: false,
      public: { api_login: 777 },
      process_id: 3,
      paused,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field CreatePathRequest.process_id of type int`],
    [`get/path`, `json: cannot unmarshal string into Go struct field CreatePathRequest.process_id of type int`],
    [[], `json: cannot unmarshal array into Go struct field CreatePathRequest.process_id of type int`],
    [{}, `json: cannot unmarshal object into Go struct field CreatePathRequest.process_id of type int`],
    [-1, `Key: 'CreatePathRequest.process_id' Error:Field validation for 'process_id' failed on the 'min' tag`],
    [
      undefined,
      `Key: 'CreatePathRequest.process_id' Error:Field validation for 'process_id' failed on the 'required_without' tag\nKey: 'CreatePathRequest.alias_id' Error:Field validation for 'alias_id' failed on the 'required_without' tag`,
    ],
    [
      null,
      `Key: 'CreatePathRequest.process_id' Error:Field validation for 'process_id' failed on the 'required_without' tag\nKey: 'CreatePathRequest.alias_id' Error:Field validation for 'alias_id' failed on the 'required_without' tag`,
    ],
  ])(`shouldn't create paths with invalid process_id '%s'`, async (process_id, reason) => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path',
      description: 'test',
      method: 'POST',
      timeout: 12,
      async: false,
      proxy_headers: true,
      proxy_raw_body: false,
      public: { api_login: 777 },
      process_id,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field CreatePathRequestPublic.public.api_login of type int`],
    [
      `get/path`,
      `json: cannot unmarshal string into Go struct field CreatePathRequestPublic.public.api_login of type int`,
    ],
    [[], `json: cannot unmarshal array into Go struct field CreatePathRequestPublic.public.api_login of type int`],
    [{}, `json: cannot unmarshal object into Go struct field CreatePathRequestPublic.public.api_login of type int`],
    [-1, `Key: 'CreatePathRequest.public.api_login' Error:Field validation for 'api_login' failed on the 'min' tag`],
  ])(`shouldn't create paths with invalid api_login '%s'`, async (api_login, reason) => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path',
      description: 'test',
      method: 'POST',
      timeout: 12,
      async: false,
      proxy_headers: true,
      proxy_raw_body: false,
      public: { api_login },
      process_id: 567,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [
      true,
      `json: cannot unmarshal bool into Go struct field CreatePathRequest.public of type models.CreatePathRequestPublic`,
    ],
    [
      `get/path`,
      `json: cannot unmarshal string into Go struct field CreatePathRequest.public of type models.CreatePathRequestPublic`,
    ],
    [
      [],
      `json: cannot unmarshal array into Go struct field CreatePathRequest.public of type models.CreatePathRequestPublic`,
    ],
    [
      0,
      `json: cannot unmarshal number into Go struct field CreatePathRequest.public of type models.CreatePathRequestPublic`,
    ],
    [
      -1,
      `json: cannot unmarshal number into Go struct field CreatePathRequest.public of type models.CreatePathRequestPublic`,
    ],
    [
      1,
      `json: cannot unmarshal number into Go struct field CreatePathRequest.public of type models.CreatePathRequestPublic`,
    ],
  ])(`shouldn't create paths with invalid public '%s'`, async (public_test, reason) => {
    const response = await request('post', `apis/${endpoint_id}/paths`, {
      path: '/get/path',
      description: 'test',
      method: 'POST',
      timeout: 12,
      async: false,
      proxy_headers: true,
      proxy_raw_body: false,
      public: public_test,
      process_id: 567,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test(`shouldn't edit paths with alias_id/alias_short_name/process_id`, async () => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/delete/path',
      description: '',
      method: 'DELETE',
      timeout: 29,
      async: true,
      proxy_headers: true,
      proxy_raw_body: true,
      public: { api_login: 777 },
      alias_id: 2,
      alias_short_name: 'test',
      process_id: 44,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(
      `Key: 'EditPathRequest.process_id' Error:Field validation for 'process_id' failed on the 'excluded_with' tag\nKey: 'EditPathRequest.alias_id' Error:Field validation for 'alias_id' failed on the 'excluded_with' tag\nKey: 'EditPathRequest.alias_short_name' Error:Field validation for 'alias_short_name' failed on the 'excluded_with' tag`,
    );
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field EditPathRequest.path of type string`],
    [`get/path`, `Key: 'EditPathRequest.path' Error:Field validation for 'path' failed on the 'startswith' tag`],
    [
      `/hubs/*/view`,
      `chi: wildcard '*' must be the last value in a route. trim trailing text or use a '{param}' instead`,
    ],
    [[], `json: cannot unmarshal array into Go struct field EditPathRequest.path of type string`],
    [{}, `json: cannot unmarshal object into Go struct field EditPathRequest.path of type string`],
    [0, `json: cannot unmarshal number into Go struct field EditPathRequest.path of type string`],
    [-1, `json: cannot unmarshal number into Go struct field EditPathRequest.path of type string`],
    [1, `json: cannot unmarshal number into Go struct field EditPathRequest.path of type string`],
    [undefined, `Key: 'EditPathRequest.path' Error:Field validation for 'path' failed on the 'required' tag`],
    [null, `Key: 'EditPathRequest.path' Error:Field validation for 'path' failed on the 'required' tag`],
  ])(`shouldn't edit paths with invalid path '%s'`, async (path, reason) => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path,
      description: 'test_description',
      method: 'GET',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: 777 },
      process_id: 3,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field EditPathRequest.description of type string`],
    [[], `json: cannot unmarshal array into Go struct field EditPathRequest.description of type string`],
    [{}, `json: cannot unmarshal object into Go struct field EditPathRequest.description of type string`],
    [0, `json: cannot unmarshal number into Go struct field EditPathRequest.description of type string`],
    [-1, `json: cannot unmarshal number into Go struct field EditPathRequest.description of type string`],
    [1, `json: cannot unmarshal number into Go struct field EditPathRequest.description of type string`],
    [
      faker.random.alphaNumeric(6000),
      `Key: 'EditPathRequest.description' Error:Field validation for 'description' failed on the 'max' tag`,
    ],
  ])(`shouldn't edit paths with invalid description '%s'`, async (description, reason) => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description,
      method: 'GET',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: 777 },
      process_id: 3,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field EditPathRequest.alias_id of type int`],
    [`get/path`, `json: cannot unmarshal string into Go struct field EditPathRequest.alias_id of type int`],
    [[], `json: cannot unmarshal array into Go struct field EditPathRequest.alias_id of type int`],
    [{}, `json: cannot unmarshal object into Go struct field EditPathRequest.alias_id of type int`],
    ['', `json: cannot unmarshal string into Go struct field EditPathRequest.alias_id of type int`],
    [-1, `Key: 'EditPathRequest.alias_id' Error:Field validation for 'alias_id' failed on the 'min' tag`],
    [
      undefined,
      `Key: 'EditPathRequest.process_id' Error:Field validation for 'process_id' failed on the 'required_without' tag\nKey: 'EditPathRequest.alias_id' Error:Field validation for 'alias_id' failed on the 'required_without' tag`,
    ],
    [
      null,
      `Key: 'EditPathRequest.process_id' Error:Field validation for 'process_id' failed on the 'required_without' tag\nKey: 'EditPathRequest.alias_id' Error:Field validation for 'alias_id' failed on the 'required_without' tag`,
    ],
  ])(`shouldn't edit paths with invalid alias_id '%s'`, async (alias_id, reason) => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: 'test',
      method: 'POST',
      timeout: 12,
      async: false,
      proxy_headers: true,
      proxy_raw_body: false,
      public: { api_login: 777 },
      alias_id,
      alias_short_name: 'test',
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field EditPathRequest.alias_short_name of type string`],
    [[], `json: cannot unmarshal array into Go struct field EditPathRequest.alias_short_name of type string`],
    [
      null,
      `Key: 'EditPathRequest.alias_short_name' Error:Field validation for 'alias_short_name' failed on the 'required_with' tag`,
    ],
    [
      undefined,
      `Key: 'EditPathRequest.alias_short_name' Error:Field validation for 'alias_short_name' failed on the 'required_with' tag`,
    ],
    [{}, `json: cannot unmarshal object into Go struct field EditPathRequest.alias_short_name of type string`],
    [0, `json: cannot unmarshal number into Go struct field EditPathRequest.alias_short_name of type string`],
    [-1, `json: cannot unmarshal number into Go struct field EditPathRequest.alias_short_name of type string`],
    [
      '',
      `Key: 'EditPathRequest.alias_short_name' Error:Field validation for 'alias_short_name' failed on the 'alphaNumericLower' tag`,
    ],
    [
      't',
      `Key: 'EditPathRequest.alias_short_name' Error:Field validation for 'alias_short_name' failed on the 'min' tag`,
    ],
    [
      faker.random.alphaNumeric(128),
      `Key: 'EditPathRequest.alias_short_name' Error:Field validation for 'alias_short_name' failed on the 'max' tag`,
    ],
    [
      'tе',
      `Key: 'EditPathRequest.alias_short_name' Error:Field validation for 'alias_short_name' failed on the 'alphaNumericLower' tag`,
    ],
  ])(`shouldn't edit paths with invalid alias_short_name '%s'`, async (alias_short_name, reason) => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path1234',
      description: '',
      method: 'GET',
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: 777 },
      alias_id: 1,
      alias_short_name,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field EditPathRequest.method of type string`],
    [`get/path`, `Key: 'EditPathRequest.method' Error:Field validation for 'method' failed on the 'oneof' tag`],
    [[], `json: cannot unmarshal array into Go struct field EditPathRequest.method of type string`],
    [{}, `json: cannot unmarshal object into Go struct field EditPathRequest.method of type string`],
    [0, `json: cannot unmarshal number into Go struct field EditPathRequest.method of type string`],
    [-1, `json: cannot unmarshal number into Go struct field EditPathRequest.method of type string`],
    [1, `json: cannot unmarshal number into Go struct field EditPathRequest.method of type string`],
    [undefined, `Key: 'EditPathRequest.method' Error:Field validation for 'method' failed on the 'required' tag`],
  ])(`shouldn't edit paths with invalid method '%s'`, async (method, reason) => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: 'test',
      method,
      timeout: 29,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: 777 },
      process_id: 3,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field EditPathRequest.timeout of type int`],
    [`get/path`, `json: cannot unmarshal string into Go struct field EditPathRequest.timeout of type int`],
    [[], `json: cannot unmarshal array into Go struct field EditPathRequest.timeout of type int`],
    [{}, `json: cannot unmarshal object into Go struct field EditPathRequest.timeout of type int`],
    [0, `Key: 'EditPathRequest.timeout' Error:Field validation for 'timeout' failed on the 'min' tag`],
    [700, `Key: 'EditPathRequest.timeout' Error:Field validation for 'timeout' failed on the 'max' tag`],
    [-1, `Key: 'EditPathRequest.timeout' Error:Field validation for 'timeout' failed on the 'min' tag`],
  ])(`shouldn't edit paths with invalid timeout '%s'`, async (timeout, reason) => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: 'test',
      method: 'POST',
      timeout,
      async: false,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: 777 },
      process_id: 3,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [`get/path`, `json: cannot unmarshal string into Go struct field EditPathRequest.async of type bool`],
    [[], `json: cannot unmarshal array into Go struct field EditPathRequest.async of type bool`],
    [{}, `json: cannot unmarshal object into Go struct field EditPathRequest.async of type bool`],
    [0, `json: cannot unmarshal number into Go struct field EditPathRequest.async of type bool`],
    [-1, `json: cannot unmarshal number into Go struct field EditPathRequest.async of type bool`],
    [1, `json: cannot unmarshal number into Go struct field EditPathRequest.async of type bool`],
  ])(`shouldn't edit paths with invalid async '%s'`, async (async, reason) => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: 'test',
      method: 'POST',
      timeout: 12,
      async,
      proxy_headers: false,
      proxy_raw_body: false,
      public: { api_login: 777 },
      process_id: 3,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [`get/path`, `json: cannot unmarshal string into Go struct field EditPathRequest.proxy_headers of type bool`],
    [[], `json: cannot unmarshal array into Go struct field EditPathRequest.proxy_headers of type bool`],
    [{}, `json: cannot unmarshal object into Go struct field EditPathRequest.proxy_headers of type bool`],
    [0, `json: cannot unmarshal number into Go struct field EditPathRequest.proxy_headers of type bool`],
    [-1, `json: cannot unmarshal number into Go struct field EditPathRequest.proxy_headers of type bool`],
    [1, `json: cannot unmarshal number into Go struct field EditPathRequest.proxy_headers of type bool`],
  ])(`shouldn't edit paths with invalid proxy_headers '%s'`, async (proxy_headers, reason) => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: 'test',
      method: 'POST',
      timeout: 12,
      async: false,
      proxy_headers,
      proxy_raw_body: false,
      public: { api_login: 777 },
      process_id: 3,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [`get/path`, `json: cannot unmarshal string into Go struct field EditPathRequest.proxy_raw_body of type bool`],
    [[], `json: cannot unmarshal array into Go struct field EditPathRequest.proxy_raw_body of type bool`],
    [{}, `json: cannot unmarshal object into Go struct field EditPathRequest.proxy_raw_body of type bool`],
    [0, `json: cannot unmarshal number into Go struct field EditPathRequest.proxy_raw_body of type bool`],
    [-1, `json: cannot unmarshal number into Go struct field EditPathRequest.proxy_raw_body of type bool`],
    [1, `json: cannot unmarshal number into Go struct field EditPathRequest.proxy_raw_body of type bool`],
  ])(`shouldn't edit paths with invalid proxy_raw_body '%s'`, async (proxy_raw_body, reason) => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: 'test',
      method: 'POST',
      timeout: 12,
      async: false,
      proxy_headers: true,
      proxy_raw_body,
      public: { api_login: 777 },
      process_id: 3,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [`get/path`, `json: cannot unmarshal string into Go struct field EditPathRequest.paused of type bool`],
    [[], `json: cannot unmarshal array into Go struct field EditPathRequest.paused of type bool`],
    [{}, `json: cannot unmarshal object into Go struct field EditPathRequest.paused of type bool`],
    [0, `json: cannot unmarshal number into Go struct field EditPathRequest.paused of type bool`],
    [-1, `json: cannot unmarshal number into Go struct field EditPathRequest.paused of type bool`],
    [1, `json: cannot unmarshal number into Go struct field EditPathRequest.paused of type bool`],
  ])(`shouldn't edit paths with invalid paused '%s'`, async (paused, reason) => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: 'test',
      method: 'POST',
      timeout: 12,
      async: false,
      proxy_headers: true,
      proxy_raw_body: false,
      public: { api_login: 777 },
      process_id: 3,
      paused,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field EditPathRequest.process_id of type int`],
    [`get/path`, `json: cannot unmarshal string into Go struct field EditPathRequest.process_id of type int`],
    [[], `json: cannot unmarshal array into Go struct field EditPathRequest.process_id of type int`],
    [{}, `json: cannot unmarshal object into Go struct field EditPathRequest.process_id of type int`],
    [-1, `Key: 'EditPathRequest.process_id' Error:Field validation for 'process_id' failed on the 'min' tag`],
    [
      undefined,
      `Key: 'EditPathRequest.process_id' Error:Field validation for 'process_id' failed on the 'required_without' tag\nKey: 'EditPathRequest.alias_id' Error:Field validation for 'alias_id' failed on the 'required_without' tag`,
    ],
    [
      null,
      `Key: 'EditPathRequest.process_id' Error:Field validation for 'process_id' failed on the 'required_without' tag\nKey: 'EditPathRequest.alias_id' Error:Field validation for 'alias_id' failed on the 'required_without' tag`,
    ],
  ])(`shouldn't edit paths with invalid process_id '%s'`, async (process_id, reason) => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: 'test',
      method: 'POST',
      timeout: 12,
      async: false,
      proxy_headers: true,
      proxy_raw_body: false,
      public: { api_login: 777 },
      process_id,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [true, `json: cannot unmarshal bool into Go struct field EditPathRequestPublic.public.api_login of type int`],
    [
      `get/path`,
      `json: cannot unmarshal string into Go struct field EditPathRequestPublic.public.api_login of type int`,
    ],
    [[], `json: cannot unmarshal array into Go struct field EditPathRequestPublic.public.api_login of type int`],
    [{}, `json: cannot unmarshal object into Go struct field EditPathRequestPublic.public.api_login of type int`],
    [-1, `Key: 'EditPathRequest.public.api_login' Error:Field validation for 'api_login' failed on the 'min' tag`],
  ])(`shouldn't edit paths with invalid api_login '%s'`, async (api_login, reason) => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: 'test',
      method: 'POST',
      timeout: 12,
      async: false,
      proxy_headers: true,
      proxy_raw_body: false,
      public: { api_login },
      process_id: 567,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [
      true,
      `json: cannot unmarshal bool into Go struct field EditPathRequest.public of type models.EditPathRequestPublic`,
    ],
    [
      `get/path`,
      `json: cannot unmarshal string into Go struct field EditPathRequest.public of type models.EditPathRequestPublic`,
    ],
    [
      [],
      `json: cannot unmarshal array into Go struct field EditPathRequest.public of type models.EditPathRequestPublic`,
    ],
    [
      0,
      `json: cannot unmarshal number into Go struct field EditPathRequest.public of type models.EditPathRequestPublic`,
    ],
    [
      -1,
      `json: cannot unmarshal number into Go struct field EditPathRequest.public of type models.EditPathRequestPublic`,
    ],
    [
      1,
      `json: cannot unmarshal number into Go struct field EditPathRequest.public of type models.EditPathRequestPublic`,
    ],
  ])(`shouldn't edit paths with invalid public '%s'`, async (public_test, reason) => {
    const response = await request('put', `apis/${endpoint_id}/paths/${path_id}`, {
      path: '/get/path',
      description: 'test',
      method: 'POST',
      timeout: 12,
      async: false,
      proxy_headers: true,
      proxy_raw_body: false,
      public: public_test,
      process_id: 567,
      paused: false,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each(['put', 'delete', 'patch'])(`shouldn't export endpoint with other method '%s'`, async method => {
    const response = await request(method, `apis/export/${workspace_id}/${project_id}`);
    expect(response.status).toBe(405);
  });

  test(`shouldn't export endpoint with project_id=undefined`, async () => {
    const response = await request('get', `apis/export/${workspace_id}`);
    expect(response.status).toBe(404);
  });

  test(`shouldn't export endpoint with workspace_id=undefined`, async () => {
    const response = await request('get', `apis/export/${project_id}`);
    expect(response.status).toBe(404);
  });

  test(`shouldn't export endpoint with workspace_id&project_id=undefined`, async () => {
    const response = await request('get', `apis/export`);
    expect(response.status).toBe(400);
    expect(response.body.description).toEqual(`apiId export not found`);
  });

  test(`shouldn't import endpoint with invalid data`, async () => {
    const response = await request('post', `apis/import/${workspace_id}/${project_id}`, { 213123: 22342 });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(`there are no apis to import`);
  });

  test.each(['put', 'delete', 'patch'])(`shouldn't import endpoint with other method '%s'`, async method => {
    const response = await request(method, `apis/import/${workspace_id}/${project_id}`);
    expect(response.status).toBe(405);
  });

  test(`shouldn't import endpoint with project_id=undefined`, async () => {
    const response = await request('get', `apis/import/${workspace_id}`);
    expect(response.status).toBe(404);
  });

  test(`shouldn't import endpoint with workspace_id=undefined`, async () => {
    const response = await request('get', `apis/import/${project_id}`);
    expect(response.status).toBe(404);
  });

  test(`shouldn't import endpoint with workspace_id&project_id=undefined`, async () => {
    const response = await request('get', `apis/import`);
    expect(response.status).toBe(400);
    expect(response.body.description).toEqual(`apiId import not found`);
  });

  test(`shouldn't merge endpoint with invalid data`, async () => {
    const response = await request('post', `apis/import/merge/${workspace_id}/${project_id}`, { 213123: 22342 });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(`there are no apis to import`);
  });

  test.each(['put', 'delete', 'patch'])(`shouldn't merge endpoint with other method '%s'`, async method => {
    const response = await request(method, `apis/import/merge/${workspace_id}/${project_id}`);
    expect(response.status).toBe(405);
  });

  test(`shouldn't merge endpoint with project_id=undefined`, async () => {
    const response = await request('get', `apis/import/merge/${workspace_id}`);
    expect(response.status).toBe(400);
  });

  test(`shouldn't merge endpoint with workspace_id=undefined`, async () => {
    const response = await request('get', `apis/import/merge/${project_id}`);
    expect(response.status).toBe(400);
  });

  test(`shouldn't merge endpoint with workspace_id&project_id=undefined`, async () => {
    const response = await request('get', `apis/import/merge`);
    expect(response.status).toBe(404);
  });

  test(`shouldn't merge endpoint with invalid data - empty apis`, async () => {
    const response = await request('post', `apis/import/merge/${workspace_id}/${project_id}`, {
      apis: [],
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(`there are no apis to import`);
  });

  test.each([-1, true, 'patch'])(`shouldn't get path with invalid path_id '%s'`, async path_id => {
    const response = await request('delete', `apis/${endpoint_id}/paths/${path_id}`);
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(`path id ${path_id} not found`);
  });

  test.each([-1, true, 'patch'])(`shouldn't delete path with invalid endpoint_id '%s'`, async path_id => {
    const response = await request('delete', `apis/${endpoint_id}/paths/${path_id}`);
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toEqual(`path id ${path_id} not found`);
  });

  afterAll(async () => {
    const response = await request('delete', `apis/${endpoint_id}`, {});
    expect(response.status).toBe(204);

    const response2 = await request('delete', `apis/${endpoint_id_2}`, {});
    expect(response2.status).toBe(204);

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: project_id,
        company_id: workspace_id,
      }),
    );
    expect(responseProject.status).toBe(200);
  });
});
