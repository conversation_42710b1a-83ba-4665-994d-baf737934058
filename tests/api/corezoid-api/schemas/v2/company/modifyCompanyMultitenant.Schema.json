{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "company_id", "name", "description", "auth_providers", "status", "new_status", "old_status"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "company_id": {"type": "string"}, "migrated_count": {"type": "integer"}, "name": {"type": "string"}, "description": {"type": "string"}, "auth_providers": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}, "status": {"type": "string"}, "new_status": {"type": "string"}, "old_status": {"type": "string"}}}, "uniqueItems": true}}}