{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["company_id", "title", "description", "status", "owner_user_id", "create_time", "childs", "is_owner", "is_admin", "auth_providers", "user_status"], "additionalProperties": false, "properties": {"company_id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": ["string", "null"]}, "status": {"type": "string"}, "owner_user_id": {"type": "integer"}, "create_time": {"type": "integer"}, "childs": {"type": "integer"}, "is_owner": {"type": "boolean"}, "is_admin": {"type": "boolean"}, "auth_providers": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "name", "active"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "active": {"type": "boolean"}}}, "uniqueItems": true}, "user_status": {"type": "string"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}