{"$schema": "http://json-schema.org/draft-07/schema", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"type": "string", "const": "ok"}, "ops": {"type": "array", "additionalItems": true, "items": {"anyOf": [{"type": "object", "required": ["id", "proc", "obj", "list"], "properties": {"id": {"type": "string"}, "proc": {"type": "string", "const": "ok"}, "obj": {"type": "string", "const": "company_users"}, "list": {"type": "array", "additionalItems": true, "items": {"anyOf": [{"type": "object", "required": ["obj_id", "title", "logins"], "properties": {"obj_id": {"type": "integer"}, "title": {"type": "string"}, "logins": {"type": "array", "additionalItems": true, "items": {"anyOf": [{"type": "object", "required": ["key", "obj_id", "type", "login"], "properties": {"key": {"type": "string"}, "obj_id": {"type": "integer"}, "type": {"type": "string", "const": "api"}, "login": {"type": "string"}}, "additionalProperties": true}]}}}, "additionalProperties": true}]}}}, "additionalProperties": true}]}}}, "additionalProperties": true}