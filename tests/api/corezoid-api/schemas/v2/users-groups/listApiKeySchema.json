{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "const": "ok"}, "ops": {"$id": "#/properties/ops", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "required": ["id", "proc", "obj", "list"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string"}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "const": "ok"}, "obj": {"$id": "#/properties/ops/items/anyOf/0/properties/obj", "type": "string"}, "list": {"$id": "#/properties/ops/items/anyOf/0/properties/list", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0", "type": "object", "required": ["obj_type", "obj_id", "size", "title", "type", "create_time", "owner_id", "owner_name", "is_owner"], "properties": {"obj_type": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/obj_type", "type": "string"}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/obj_id", "type": "integer"}, "size": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/size", "type": "integer"}, "title": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/title", "type": "string"}, "type": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/type", "type": "string"}, "create_time": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/create_time", "type": "integer"}, "owner_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/owner_id", "type": "integer"}, "owner_name": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/owner_name", "type": "string"}, "is_owner": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/is_owner", "type": "boolean"}}, "additionalProperties": false}, {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1", "type": "object", "required": ["obj_type", "obj_id", "title", "owner_id", "logins"], "properties": {"obj_type": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/obj_type", "type": "string"}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/obj_id", "type": "integer"}, "title": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/title", "type": "string"}, "owner_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/owner_id", "type": "integer"}, "logins": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/logins", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/logins/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/logins/items/anyOf/0", "type": "object", "required": ["key", "obj_id", "type", "login"], "properties": {"key": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/logins/items/anyOf/0/properties/key", "type": "string"}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/logins/items/anyOf/0/properties/obj_id", "type": "integer"}, "type": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/logins/items/anyOf/0/properties/type", "type": "string"}, "login": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/logins/items/anyOf/0/properties/login", "type": "string"}}, "additionalProperties": false}]}}}, "additionalProperties": false}, {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/2", "type": "object", "required": ["obj_type", "obj_id", "title", "logins", "status", "is_superadmin", "groups"], "properties": {"obj_type": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/2/properties/obj_type", "type": "string"}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/2/properties/obj_id", "type": "integer"}, "title": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/2/properties/title", "type": "string"}, "logins": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/2/properties/logins", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/2/properties/logins/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/2/properties/logins/items/anyOf/0", "type": "object", "required": ["obj_id", "type", "login"], "properties": {"obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/2/properties/logins/items/anyOf/0/properties/obj_id", "type": "integer"}, "type": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/2/properties/logins/items/anyOf/0/properties/type", "type": "string"}, "login": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/2/properties/logins/items/anyOf/0/properties/login", "type": "string"}}, "additionalProperties": false}, {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/2/properties/logins/items/anyOf/1", "type": "object", "required": ["key", "obj_id", "type", "login"], "properties": {"key": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/2/properties/logins/items/anyOf/1/properties/key", "type": "string"}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/2/properties/logins/items/anyOf/1/properties/obj_id", "type": "integer"}, "type": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/2/properties/logins/items/anyOf/1/properties/type", "type": "string"}, "login": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/2/properties/logins/items/anyOf/1/properties/login", "type": "string"}}, "additionalProperties": false}]}}, "status": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/2/properties/status", "type": "string"}, "is_superadmin": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/2/properties/is_superadmin", "type": "boolean"}, "groups": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/2/properties/groups", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/2/properties/groups/items"}}}, "additionalProperties": false}]}}}, "additionalProperties": false}]}}}, "additionalProperties": false}