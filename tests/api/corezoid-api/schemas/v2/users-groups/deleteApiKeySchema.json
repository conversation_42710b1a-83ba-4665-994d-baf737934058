{"$schema": "http://json-schema.org/draft-07/schema", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"type": "string", "const": "ok"}, "ops": {"type": "array", "additionalItems": true, "items": {"anyOf": [{"type": "object", "required": ["id", "proc", "obj_id", "company_id"], "properties": {"id": {"type": "string"}, "proc": {"type": "string", "const": "ok"}, "obj_id": {"type": "integer"}, "company_id": {"type": ["string", "null"]}}, "additionalProperties": true}]}}}, "additionalProperties": true}