{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "const": "ok"}, "ops": {"$id": "#/properties/ops", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "required": ["id", "proc", "obj", "logins", "obj_id"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string"}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "const": "ok"}, "obj": {"$id": "#/properties/ops/items/anyOf/0/properties/obj", "type": "string", "const": "user"}, "logins": {"$id": "#/properties/ops/items/anyOf/0/properties/logins", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/logins/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/logins/items/anyOf/0", "type": "object", "required": ["type", "login", "key", "obj_id"], "properties": {"type": {"$id": "#/properties/ops/items/anyOf/0/properties/logins/items/anyOf/0/properties/type", "type": "string", "const": "api"}, "login": {"$id": "#/properties/ops/items/anyOf/0/properties/logins/items/anyOf/0/properties/login", "type": "string"}, "key": {"$id": "#/properties/ops/items/anyOf/0/properties/logins/items/anyOf/0/properties/key", "type": "string"}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/logins/items/anyOf/0/properties/obj_id", "type": "integer"}}, "additionalProperties": false}]}}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/obj_id", "type": "integer"}}, "additionalProperties": false}]}}}, "additionalProperties": false}