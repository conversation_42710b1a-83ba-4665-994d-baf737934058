{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "create_time", "user_id", "user_name", "login", "company_id", "obj", "type", "obj_type", "obj_id", "obj_name", "obj_to", "obj_to_id", "obj_to_name", "request", "response"], "additionalProperties": false, "properties": {"id": {"type": "integer"}, "create_time": {"type": "integer"}, "user_id": {"type": "integer"}, "user_name": {"type": "string"}, "login": {"type": "string"}, "company_id": {"type": "null"}, "obj": {"type": "string"}, "type": {"type": "string"}, "obj_type": {"type": "string"}, "obj_id": {"type": "integer"}, "obj_name": {"type": ["null", "string"]}, "obj_to": {"type": "null"}, "obj_to_id": {"type": "null"}, "obj_to_name": {"type": "null"}, "request": {"type": "string"}, "response": {"type": "string"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}