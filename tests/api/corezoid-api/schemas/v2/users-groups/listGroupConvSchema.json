{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "obj_id", "proc", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "integer"}, "proc": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj_id", "title", "description", "status"], "additionalProperties": false, "properties": {"obj_id": {"type": "integer"}, "title": {"type": "string"}, "description": {"type": "string"}, "status": {"type": "string"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}