{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "title", "login", "key", "obj_id"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "title": {"type": "string"}, "login": {"type": "integer"}, "key": {"type": "string"}, "obj_id": {"type": "integer"}}}, "uniqueItems": true}}}