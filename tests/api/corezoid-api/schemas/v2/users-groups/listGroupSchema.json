{"type": "object", "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "items": [{"type": "object", "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "list": {"type": "array", "items": [{"type": "object", "properties": {"obj_id": {"type": "integer"}, "size": {"type": "integer"}, "title": {"type": "string"}, "type": {"type": "string"}, "create_time": {"type": "integer"}, "owner_id": {"type": "integer"}, "owner_name": {"type": "string"}, "is_owner": {"type": "boolean"}}, "required": ["obj_id", "size", "title", "type", "create_time", "owner_id", "owner_name", "is_owner"]}, {"type": "object", "properties": {"obj_id": {"type": "integer"}, "size": {"type": "integer"}, "title": {"type": "string"}, "type": {"type": "string"}, "create_time": {"type": "integer"}, "owner_id": {"type": "integer"}, "owner_name": {"type": "string"}, "is_owner": {"type": "boolean"}}, "required": ["obj_id", "size", "title", "type", "create_time", "owner_id", "owner_name", "is_owner"]}]}}, "required": ["id", "proc", "obj", "list"]}]}}, "required": ["request_proc", "ops"]}