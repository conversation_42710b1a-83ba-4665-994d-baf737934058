{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "owner_id", "owner_name", "proc", "obj", "obj_id", "title", "logins"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "owner_id": {"type": "integer"}, "owner_name": {"type": "string"}, "groups": {"type": "array"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "integer"}, "title": {"type": "string"}, "logins": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["key", "obj_id", "type", "login"], "additionalProperties": false, "properties": {"key": {"type": "string"}, "obj_id": {"type": "integer"}, "type": {"type": "string"}, "login": {"type": "string"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}