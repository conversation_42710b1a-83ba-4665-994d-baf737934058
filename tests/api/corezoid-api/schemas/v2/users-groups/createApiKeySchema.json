{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "proc", "users"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "proc": {"type": "string"}, "users": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["created", "obj_id", "title", "logins"], "additionalProperties": false, "properties": {"created": {"type": "boolean"}, "obj_id": {"type": "integer"}, "title": {"type": "string"}, "logins": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["type", "key", "obj_id"], "additionalProperties": false, "properties": {"type": {"type": "string"}, "key": {"type": "string"}, "obj_id": {"type": "integer"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}, "uniqueItems": true}}}