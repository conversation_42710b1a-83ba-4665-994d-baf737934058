{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "obj_id", "owner_id", "owner_name", "proc", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "integer"}, "owner_id": {"type": "integer"}, "owner_name": {"type": "string"}, "proc": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj", "obj_id", "title", "logins"], "additionalProperties": false, "properties": {"obj": {"type": "string"}, "obj_id": {"type": "integer"}, "title": {"type": "string"}, "logins": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["type", "login", "key", "obj_id"], "additionalProperties": false, "properties": {"type": {"type": "string"}, "login": {"type": "string"}, "key": {"type": "string"}, "obj_id": {"type": "integer"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}, "uniqueItems": true}}}