{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "dashboard_url", "folder_url", "webhooks_url", "result", "description"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "dashboard_url": {"type": "string"}, "folder_url": {"type": "string"}, "webhooks_url": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["channel", "url"], "additionalProperties": false, "properties": {"channel": {"type": "string"}, "url": {"type": "string"}}}, "uniqueItems": true}, "result": {"type": "string"}, "description": {"type": "string"}}}, "uniqueItems": true}}}