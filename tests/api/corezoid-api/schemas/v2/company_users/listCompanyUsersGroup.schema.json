{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj_id", "size", "title", "create_time", "owner_id", "owner_name", "is_owner"], "additionalProperties": false, "properties": {"obj_id": {"type": "integer"}, "size": {"type": "integer"}, "title": {"type": "string"}, "type": {"type": "string"}, "create_time": {"type": "integer"}, "owner_id": {"type": "integer"}, "owner_name": {"type": "string"}, "is_owner": {"type": "boolean"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}