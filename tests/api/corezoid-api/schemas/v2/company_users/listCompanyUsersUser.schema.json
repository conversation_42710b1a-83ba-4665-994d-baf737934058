{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj_id", "title", "logins", "status", "is_login_locked", "is_superadmin", "groups"], "additionalProperties": false, "properties": {"obj_id": {"type": "integer"}, "title": {"type": "string"}, "logins": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj_id", "type", "login"], "additionalProperties": false, "properties": {"key": {"type": "string"}, "obj_id": {"type": "integer"}, "type": {"type": "string"}, "login": {"type": "string"}}}, "uniqueItems": true}, "status": {"type": "string"}, "is_login_locked": {"type": "boolean"}, "is_superadmin": {"type": "boolean"}, "groups": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "name"], "additionalProperties": false, "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}, "uniqueItems": true}}}