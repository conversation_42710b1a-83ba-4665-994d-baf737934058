{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["result", "id", "key", "value", "description", "obj_type", "is_required", "vsn", "tags", "allowed"], "additionalProperties": false, "properties": {"result": {"type": "string"}, "id": {"type": "integer"}, "key": {"type": "string"}, "value": {"type": ["integer", "boolean"]}, "description": {"type": "string"}, "obj_type": {"type": "string"}, "is_required": {"type": "boolean"}, "vsn": {"type": "integer"}, "tags": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "tag"], "additionalProperties": false, "properties": {"id": {"type": "integer"}, "tag": {"type": "string"}}}, "uniqueItems": true}, "allowed": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": true}}}