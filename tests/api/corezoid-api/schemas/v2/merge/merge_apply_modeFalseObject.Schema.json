{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj", "proc", "diff"], "additionalProperties": false, "properties": {"obj": {"type": "string"}, "proc": {"type": "string"}, "diff": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["type", "obj", "obj_id", "title", "description", "status", "params", "ref_mask", "conv_type", "scheme", "uuid", "folder_id"], "additionalProperties": false, "properties": {"type": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "integer"}, "title": {"type": "string"}, "description": {"type": "string"}, "status": {"type": "string"}, "params": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": false}, "ref_mask": {"type": "boolean"}, "conv_type": {"type": "string"}, "scheme": {"type": "object", "required": ["nodes", "web_settings"], "additionalProperties": false, "properties": {"nodes": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["logics", "semaphors", "obj_id", "position", "version", "obj_type", "title", "description", "uuid", "extra", "options"], "additionalProperties": false, "properties": {"logics": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["to_node_id", "type"], "additionalProperties": false, "properties": {"to_node_id": {"type": "string"}, "type": {"type": "string"}}}, "uniqueItems": false}, "semaphors": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": false}, "obj_id": {"type": "string"}, "position": {"type": "array", "additionalItems": true, "items": {"type": "integer"}, "uniqueItems": false}, "version": {"type": "integer"}, "obj_type": {"type": "integer"}, "title": {"type": "string"}, "description": {"type": "string"}, "uuid": {"type": "string"}, "extra": {"type": "null"}, "options": {"type": "null"}}}, "uniqueItems": false}, "web_settings": {"type": "array", "additionalItems": true, "items": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": false}, "uniqueItems": false}}}, "uuid": {"type": "string"}, "folder_id": {"type": "integer"}}}, "uniqueItems": false}}}, "uniqueItems": false}}}