{"type": "object", "required": ["result", "box_solution", "type_logins", "auth_providers", "web_settings", "backend_settings", "auth_single_account", "first_day_of_week", "multitenancy", "components", "max_versions_per_projects", "max_login_attempts_strategy", "user_downloads", "database_active_list", "companies_manager", "billing_manager"], "additionalProperties": false, "properties": {"result": {"type": "string"}, "box_solution": {"type": "boolean"}, "type_logins": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}, "auth_providers": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": true}, "web_settings": {"type": "object", "required": ["feedback_url", "env", "host", "path", "sender", "<PERSON><PERSON>a", "stripe", "whitelist", "ui", "password_policy", "widgets"], "additionalProperties": false, "properties": {"feedback_url": {"type": "string"}, "env": {"type": "string"}, "host": {"type": "object", "required": ["site", "doc", "ws", "webhook", "auth", "single_account", "openapi"], "additionalProperties": false, "properties": {"site": {"type": "string"}, "doc": {"type": "string"}, "ws": {"type": "string"}, "webhook": {"type": "string"}, "auth": {"type": "string"}, "single_account": {"type": "string"}, "openapi": {"type": "string"}}}, "widgets": {"type": "object", "required": ["editor_ai"], "additionalProperties": false, "properties": {"editor_ai": {"type": "object", "required": ["enabled", "src", "actor_id", "workspace_whitelist"], "additionalProperties": false, "properties": {"enabled": {"type": "boolean"}, "src": {"type": "string"}, "actor_id": {"type": "string"}, "workspace_whitelist": {"type": ["array", "string"], "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}}}}}, "path": {"type": "object", "required": ["api", "upload", "download", "ws", "webhook", "auth", "base_path", "compare", "superadmin", "merge", "doc"], "additionalProperties": false, "properties": {"api": {"type": "string"}, "upload": {"type": "string"}, "download": {"type": "string"}, "ws": {"type": "string"}, "webhook": {"type": "string"}, "auth": {"type": "string"}, "base_path": {"type": "string"}, "compare": {"type": "string"}, "superadmin": {"type": "string"}, "merge": {"type": "string"}, "doc": {"type": "object", "required": ["index", "introduction", "bot_platform", "task_export", "mask_values", "openapi_auth"], "additionalProperties": false, "properties": {"index": {"type": "string"}, "introduction": {"type": "string"}, "bot_platform": {"type": "string"}, "task_export": {"type": "string"}, "mask_values": {"type": "string"}, "openapi_auth": {"type": "string"}}}}}, "sender": {"type": "object", "required": ["host", "path"], "additionalProperties": false, "properties": {"host": {"type": "string"}, "path": {"type": "object", "required": ["embed", "builder"], "additionalProperties": false, "properties": {"embed": {"type": "string"}, "builder": {"type": "string"}}}}}, "captcha": {"type": "object", "required": ["provider", "key", "disabled"], "additionalProperties": false, "properties": {"provider": {"type": "string"}, "key": {"type": "string"}, "disabled": {"type": "boolean"}}}, "stripe": {"type": "object", "required": ["key", "client_id"], "additionalProperties": false, "properties": {"key": {"type": "string"}, "client_id": {"type": "string"}}}, "whitelist": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}, "ui": {"type": "object", "required": ["company", "bot_platform", "old_editor", "health", "send_invite", "search", "billing", "default_company", "show_welcome_notify", "tab_name", "features", "api_call", "save_archive_tasks", "enabled_copilot"], "additionalProperties": false, "properties": {"company": {"type": "boolean"}, "bot_platform": {"type": "boolean"}, "old_editor": {"type": "boolean"}, "health": {"type": "boolean"}, "send_invite": {"type": "boolean"}, "search": {"type": "boolean"}, "billing": {"type": "boolean"}, "default_company": {"type": "string"}, "show_welcome_notify": {"type": "boolean"}, "tab_name": {"type": "string"}, "features": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}, "api_call": {"type": "object", "required": ["send_sys", "default_max_thread"], "additionalProperties": false, "properties": {"send_sys": {"type": "boolean"}, "default_max_thread": {"type": "integer"}}}, "save_archive_tasks": {"type": "boolean"}, "enabled_copilot": {"type": "boolean"}}}, "password_policy": {"type": "object", "required": ["min_length", "max_length", "lowercase", "uppercase", "number", "special"], "additionalProperties": false, "properties": {"min_length": {"type": "integer"}, "max_length": {"type": "integer"}, "lowercase": {"type": "boolean"}, "uppercase": {"type": "boolean"}, "number": {"type": "boolean"}, "special": {"type": "boolean"}}}}}, "backend_settings": {"type": "object", "required": ["code_engine"], "additionalProperties": false, "properties": {"code_engine": {"type": "object", "required": ["js", "erl"], "additionalProperties": false, "properties": {"js": {"type": "string"}, "erl": {"type": "string"}}}}}, "auth_single_account": {"type": "boolean"}, "first_day_of_week": {"type": "string"}, "multitenancy": {"type": "boolean"}, "components": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}, "max_versions_per_projects": {"type": "integer"}, "max_login_attempts_strategy": {"type": "string"}, "user_downloads": {"type": "object", "required": ["max_throughput"], "additionalProperties": false, "properties": {"max_throughput": {"type": "integer"}}}, "database_active_list": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}, "companies_manager": {"type": "string"}, "billing_manager": {"type": "string"}}}