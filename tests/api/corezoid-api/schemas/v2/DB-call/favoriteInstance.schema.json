{"type": "object", "default": {}, "title": "Root Schema", "required": ["request_proc", "ops"], "properties": {"request_proc": {"type": "string", "default": "", "title": "The request_proc Schema", "examples": ["ok"]}, "ops": {"type": "array", "default": [], "title": "The ops Schema", "items": {"type": "object", "default": {}, "title": "A Schema", "required": ["id", "proc", "obj_id", "obj"], "properties": {"id": {"type": "string", "default": "", "title": "The id Schema", "examples": [""]}, "proc": {"type": "string", "default": "", "title": "The proc Schema", "examples": ["ok"]}, "obj_id": {"type": "integer", "default": 0, "title": "The obj_id Schema", "examples": [63856]}, "obj": {"type": "string", "default": "", "title": "The obj Schema", "examples": ["instance"]}}, "examples": [{"id": "", "proc": "ok", "obj_id": 63856, "obj": "instance"}]}, "examples": [[{"id": "", "proc": "ok", "obj_id": 63856, "obj": "instance"}]]}}, "examples": [{"request_proc": "ok", "ops": [{"id": "", "proc": "ok", "obj_id": 63856, "obj": "instance"}]}]}