{"type": "object", "default": {}, "title": "Root Schema", "required": ["request_proc", "ops"], "properties": {"request_proc": {"type": "string", "default": "", "title": "The request_proc Schema", "examples": ["ok"]}, "ops": {"type": "array", "default": [], "title": "The ops Schema", "items": {"type": "object", "default": {}, "title": "A Schema", "required": ["id", "proc", "obj", "obj_id", "title", "description", "create_time", "change_time", "instance_type", "status", "company_id", "data", "folder_id", "owner_name", "owner_login", "parent_obj_id", "parent_obj_type"], "properties": {"id": {"type": "string", "default": "", "title": "The id Schema", "examples": [""]}, "proc": {"type": "string", "default": "", "title": "The proc Schema", "examples": ["ok"]}, "obj": {"type": "string", "default": "", "title": "The obj Schema", "examples": ["instance"]}, "obj_id": {"type": "integer", "default": 0, "title": "The obj_id Schema", "examples": [63856]}, "title": {"type": "string", "default": "", "title": "The title <PERSON><PERSON>a", "examples": ["Modify"]}, "description": {"type": "string", "default": "", "title": "The description Schema", "examples": ["test"]}, "create_time": {"type": "integer", "default": 0, "title": "The create_time Schema", "examples": [1699537953]}, "change_time": {"type": "integer", "default": 0, "title": "The change_time Schema", "examples": [1699537953]}, "instance_type": {"type": "string", "default": "", "title": "The instance_type Schema", "examples": ["db_call"]}, "status": {"type": "string", "default": "", "title": "The status Schema", "examples": ["active"]}, "company_id": {"type": "string", "default": "", "title": "The company_id Schema", "examples": ["i738314881"]}, "data": {"type": "object", "default": {}, "title": "The data Schema", "required": ["driver", "host", "port", "username", "password", "database", "timeoutMs", "ssl"], "properties": {"driver": {"type": "string", "default": "", "title": "The driver <PERSON><PERSON><PERSON>", "examples": ["postgres"]}, "host": {"type": "string", "default": "", "title": "The host <PERSON><PERSON><PERSON>", "examples": ["COR-6938.middleware.loc"]}, "port": {"type": "integer", "default": 0, "title": "The port Schema", "examples": [5432]}, "username": {"type": "string", "default": "", "title": "The username <PERSON><PERSON>a", "examples": ["postgres"]}, "password": {"type": "string", "default": "", "title": "The password Schema", "examples": ["pass"]}, "database": {"type": "string", "default": "", "title": "The database Schema", "examples": ["dbcall"]}, "timeoutMs": {"type": "integer", "default": 0, "title": "The timeoutMs Schema", "examples": [30000]}, "ssl": {"type": "boolean", "default": false, "title": "The ssl Schema", "examples": [true]}}, "examples": [{"driver": "postgres", "host": "COR-6938.middleware.loc", "port": 5432, "username": "postgres", "password": "pass", "database": "dbcall", "timeoutMs": 30000, "ssl": true}]}, "folder_id": {"type": "integer", "default": 0, "title": "The folder_id Schema", "examples": [572300]}, "owner_name": {"type": "string", "default": "", "title": "The owner_name <PERSON><PERSON><PERSON>", "examples": ["ApiKeyForTest"]}, "owner_login": {"type": "string", "default": "", "title": "The owner_login <PERSON><PERSON>a", "examples": ["undefined"]}, "parent_obj_id": {"type": "integer", "default": 0, "title": "The parent_obj_id <PERSON>a", "examples": [572300]}, "parent_obj_type": {"type": "string", "default": "", "title": "The parent_obj_type Schema", "examples": ["folder"]}}, "examples": [{"id": "", "proc": "ok", "obj": "instance", "obj_id": 63856, "title": "Modify", "description": "test", "create_time": 1699537953, "change_time": 1699537953, "instance_type": "db_call", "status": "active", "company_id": "i738314881", "data": {"driver": "postgres", "host": "COR-6938.middleware.loc", "port": 5432, "username": "postgres", "password": "pass", "database": "dbcall", "timeoutMs": 30000, "ssl": true}, "folder_id": 572300, "owner_name": "ApiKeyForTest", "owner_login": "undefined", "parent_obj_id": 572300, "parent_obj_type": "folder"}]}, "examples": [[{"id": "", "proc": "ok", "obj": "instance", "obj_id": 63856, "title": "Modify", "description": "test", "create_time": 1699537953, "change_time": 1699537953, "instance_type": "db_call", "status": "active", "company_id": "i738314881", "data": {"driver": "postgres", "host": "COR-6938.middleware.loc", "port": 5432, "username": "postgres", "password": "pass", "database": "dbcall", "timeoutMs": 30000, "ssl": true}, "folder_id": 572300, "owner_name": "ApiKeyForTest", "owner_login": "undefined", "parent_obj_id": 572300, "parent_obj_type": "folder"}]]}}, "examples": [{"request_proc": "ok", "ops": [{"id": "", "proc": "ok", "obj": "instance", "obj_id": 63856, "title": "Modify", "description": "test", "create_time": 1699537953, "change_time": 1699537953, "instance_type": "db_call", "status": "active", "company_id": "i738314881", "data": {"driver": "postgres", "host": "COR-6938.middleware.loc", "port": 5432, "username": "postgres", "password": "pass", "database": "dbcall", "timeoutMs": 30000, "ssl": true}, "folder_id": 572300, "owner_name": "ApiKeyForTest", "owner_login": "undefined", "parent_obj_id": 572300, "parent_obj_type": "folder"}]}]}