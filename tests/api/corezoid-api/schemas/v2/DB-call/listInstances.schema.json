{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "instances"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "instances": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj_id", "title", "description", "create_time", "change_time", "owner_id", "user_id", "instance_type", "status", "company_id", "data"], "additionalProperties": false, "properties": {"obj_id": {"type": "integer"}, "title": {"type": "string"}, "description": {"type": "string"}, "create_time": {"type": "integer"}, "change_time": {"type": "integer"}, "owner_id": {"type": "integer"}, "user_id": {"type": "integer"}, "instance_type": {"type": "string"}, "status": {"type": "integer"}, "company_id": {"type": "string"}, "data": {"type": "object", "required": ["driver", "host", "port", "username", "password", "database", "timeoutMs", "ssl"], "additionalProperties": false, "properties": {"driver": {"type": "string"}, "host": {"type": "string"}, "port": {"type": "integer"}, "username": {"type": "string"}, "password": {"type": "string"}, "database": {"type": "string"}, "timeoutMs": {"type": "integer"}, "ssl": {"type": "boolean"}}}}}, "uniqueItems": true}}}, "uniqueItems": true}}}