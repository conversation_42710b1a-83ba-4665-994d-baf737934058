{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "obj_id", "favorite", "list_obj", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "integer"}, "favorite": {"type": "boolean"}, "list_obj": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj_id", "obj", "title", "privs"], "additionalProperties": false, "properties": {"obj_id": {"type": "integer"}, "obj": {"type": "string"}, "title": {"type": "string"}, "obj_type": {"type": "string"}, "privs": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["type", "list_obj"], "additionalProperties": false, "properties": {"type": {"type": "string"}, "list_obj": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}}}, "uniqueItems": true}, "obj_status": {"type": "string"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}