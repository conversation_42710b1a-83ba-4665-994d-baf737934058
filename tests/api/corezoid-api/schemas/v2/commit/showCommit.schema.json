{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj_id", "obj_type", "title", "description", "logics", "semaphors", "position", "extra", "status", "conv_id", "user_id", "create_time"], "additionalProperties": false, "properties": {"obj_id": {"type": "string"}, "obj_type": {"type": "integer"}, "title": {"type": "string"}, "description": {"type": "string"}, "logics": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": true}, "semaphors": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["type", "value", "dimension", "to_node_id"], "additionalProperties": false, "properties": {"type": {"type": "string"}, "value": {"type": "integer"}, "dimension": {"type": "string"}, "to_node_id": {"type": "string"}}}, "uniqueItems": true}, "position": {"type": "array", "additionalItems": true, "items": {"type": "integer"}, "uniqueItems": false}, "extra": {"type": "null"}, "status": {"type": "integer"}, "conv_id": {"type": "integer"}, "user_id": {"type": "integer"}, "create_time": {"type": "integer"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}