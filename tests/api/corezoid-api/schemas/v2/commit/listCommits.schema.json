{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["conv_id", "version", "create_time", "user_id"], "additionalProperties": false, "properties": {"conv_id": {"type": "integer"}, "version": {"type": "integer"}, "create_time": {"type": "integer"}, "user_id": {"type": "integer"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}