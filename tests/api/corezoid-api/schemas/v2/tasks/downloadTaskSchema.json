{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string"}, "ops": {"$id": "#/properties/ops", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "required": ["download_url", "statistics_id", "obj", "proc"], "properties": {"download_url": {"$id": "#/properties/ops/items/anyOf/0/properties/download_url", "type": "string"}, "statistics_id": {"$id": "#/properties/ops/items/anyOf/0/properties/statistics_id", "type": "string"}, "obj": {"$id": "#/properties/ops/items/anyOf/0/properties/obj", "type": "string"}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string"}}, "additionalProperties": false}]}}}, "additionalProperties": false}