{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "const": "ok"}, "ops": {"$id": "#/properties/ops", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "required": ["id", "proc", "obj", "obj_id", "task_id", "ref", "status", "user_id", "create_time", "change_time", "node_id", "node_prev_id", "data"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string", "const": ""}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "const": "ok"}, "obj": {"$id": "#/properties/ops/items/anyOf/0/properties/obj", "type": "string", "const": "task"}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/obj_id", "type": "string"}, "task_id": {"$id": "#/properties/ops/items/anyOf/0/properties/task_id", "type": "string"}, "ref": {"$id": "#/properties/ops/items/anyOf/0/properties/ref", "type": "string"}, "status": {"$id": "#/properties/ops/items/anyOf/0/properties/status", "type": "string"}, "user_id": {"$id": "#/properties/ops/items/anyOf/0/properties/user_id", "type": "integer"}, "create_time": {"$id": "#/properties/ops/items/anyOf/0/properties/create_time", "type": "integer"}, "change_time": {"$id": "#/properties/ops/items/anyOf/0/properties/change_time", "type": "integer"}, "node_id": {"$id": "#/properties/ops/items/anyOf/0/properties/node_id", "type": "string"}, "node_prev_id": {"$id": "#/properties/ops/items/anyOf/0/properties/node_prev_id", "type": "string"}, "data": {"$id": "#/properties/ops/items/anyOf/0/properties/data", "type": "object"}}, "additionalProperties": false}]}}}, "additionalProperties": false}