{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "const": "ok"}, "ops": {"$id": "#/properties/ops", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "required": ["id", "proc", "obj_id", "ref", "list"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string", "const": ""}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "const": "ok"}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/obj_id", "type": "string"}, "ref": {"$id": "#/properties/ops/items/anyOf/0/properties/ref", "type": "string"}, "list": {"$id": "#/properties/ops/items/anyOf/0/properties/list", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0", "type": "object", "required": ["history_id", "conv_id", "node_id", "node_prev_id", "user_id", "user_name", "create_time_ms", "status", "data"], "properties": {"history_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/history_id", "type": "integer"}, "conv_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/conv_id", "type": "integer"}, "node_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/node_id", "type": "string"}, "node_prev_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/node_prev_id"}, "user_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/user_id", "type": "integer"}, "user_name": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/user_name", "type": "string"}, "create_time_ms": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/create_time_ms", "type": "integer"}, "status": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/status", "type": "string"}, "data": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/data"}}, "additionalProperties": false}]}}}, "additionalProperties": false}]}}}, "additionalProperties": false}