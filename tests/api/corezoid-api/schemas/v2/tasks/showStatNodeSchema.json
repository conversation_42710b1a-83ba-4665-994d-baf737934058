{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string"}, "ops": {"$id": "#/properties/ops", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "required": ["id", "proc", "obj", "node_id", "title", "group", "conv_id", "dashboard_id", "data"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string"}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string"}, "obj": {"$id": "#/properties/ops/items/anyOf/0/properties/obj", "type": "string"}, "node_id": {"$id": "#/properties/ops/items/anyOf/0/properties/node_id", "type": "string"}, "title": {"$id": "#/properties/ops/items/anyOf/0/properties/title", "type": "string"}, "group": {"$id": "#/properties/ops/items/anyOf/0/properties/group", "type": "string"}, "conv_id": {"$id": "#/properties/ops/items/anyOf/0/properties/conv_id", "type": "integer"}, "dashboard_id": {"$id": "#/properties/ops/items/anyOf/0/properties/dashboard_id", "type": "string"}, "data": {"$id": "#/properties/ops/items/anyOf/0/properties/data", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/data/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/data/items/anyOf/0", "type": "object", "required": ["date", "in", "out"], "properties": {"date": {"$id": "#/properties/ops/items/anyOf/0/properties/data/items/anyOf/0/properties/date", "type": "string"}, "in": {"$id": "#/properties/ops/items/anyOf/0/properties/data/items/anyOf/0/properties/in", "type": "integer"}, "out": {"$id": "#/properties/ops/items/anyOf/0/properties/data/items/anyOf/0/properties/out", "type": "integer"}}, "additionalProperties": false}]}}}, "additionalProperties": false}]}}}, "additionalProperties": false}