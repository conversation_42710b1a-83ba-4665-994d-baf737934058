{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "obj_id", "prev_conv_id", "prev_conv_name", "prev_node_id", "prev_node_name", "conv_id", "conv_name", "node_id", "node_name", "data", "branches", "logic", "history"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "string"}, "prev_conv_id": {"type": "integer"}, "prev_conv_name": {"type": "string"}, "prev_node_id": {"type": "string"}, "prev_node_name": {"type": "string"}, "conv_id": {"type": "integer"}, "conv_name": {"type": "string"}, "node_id": {"type": "string"}, "node_name": {"type": "string"}, "data": {"type": "object"}, "branches": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}, "logic": {"type": "string"}, "history": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": true}}}, "uniqueItems": true}}}