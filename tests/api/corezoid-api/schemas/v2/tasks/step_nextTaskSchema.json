{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "const": "ok"}, "ops": {"$id": "#/properties/ops", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "required": ["id", "proc", "obj", "obj_id", "prev_conv_id", "prev_conv_name", "prev_node_id", "prev_node_name", "conv_id", "conv_name", "node_id", "node_name", "data", "prev_logic", "prev_semaphor", "branches", "logic", "history"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string"}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "const": "ok"}, "obj": {"$id": "#/properties/ops/items/anyOf/0/properties/obj", "type": "string"}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/obj_id", "type": "string"}, "prev_conv_id": {"$id": "#/properties/ops/items/anyOf/0/properties/prev_conv_id", "type": "integer"}, "prev_conv_name": {"$id": "#/properties/ops/items/anyOf/0/properties/prev_conv_name", "type": "string"}, "prev_node_id": {"$id": "#/properties/ops/items/anyOf/0/properties/prev_node_id", "type": "string"}, "prev_node_name": {"$id": "#/properties/ops/items/anyOf/0/properties/prev_node_name", "type": "string"}, "conv_id": {"$id": "#/properties/ops/items/anyOf/0/properties/conv_id", "type": "integer"}, "conv_name": {"$id": "#/properties/ops/items/anyOf/0/properties/conv_name", "type": "string"}, "node_id": {"$id": "#/properties/ops/items/anyOf/0/properties/node_id", "type": "string"}, "node_name": {"$id": "#/properties/ops/items/anyOf/0/properties/node_name", "type": "string"}, "data": {"$id": "#/properties/ops/items/anyOf/0/properties/data", "type": "object"}, "prev_logic": {"$id": "#/properties/ops/items/anyOf/0/properties/prev_logic", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/prev_logic/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/prev_logic/items/anyOf/0", "type": "object", "required": ["proc", "sys", "type", "to_conv_id", "to_node_id"], "properties": {"proc": {"$id": "#/properties/ops/items/anyOf/0/properties/prev_logic/items/anyOf/0/properties/proc", "type": "string"}, "sys": {"$id": "#/properties/ops/items/anyOf/0/properties/prev_logic/items/anyOf/0/properties/sys", "type": "object"}, "type": {"$id": "#/properties/ops/items/anyOf/0/properties/prev_logic/items/anyOf/0/properties/type", "type": "string"}, "to_conv_id": {"$id": "#/properties/ops/items/anyOf/0/properties/prev_logic/items/anyOf/0/properties/to_conv_id", "type": "string"}, "to_node_id": {"$id": "#/properties/ops/items/anyOf/0/properties/prev_logic/items/anyOf/0/properties/to_node_id", "type": "string"}}, "additionalProperties": false}]}}, "prev_semaphor": {"$id": "#/properties/ops/items/anyOf/0/properties/prev_semaphor", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/prev_semaphor/items"}}, "branches": {"$id": "#/properties/ops/items/anyOf/0/properties/branches", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/branches/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/branches/items/anyOf/0", "type": "string"}]}}, "logic": {"$id": "#/properties/ops/items/anyOf/0/properties/logic", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/logic/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/logic/items/anyOf/0", "type": "object", "required": ["type", "obj_id_path"], "properties": {"type": {"$id": "#/properties/ops/items/anyOf/0/properties/logic/items/anyOf/0/properties/type", "type": "string"}, "obj_id_path": {"$id": "#/properties/ops/items/anyOf/0/properties/logic/items/anyOf/0/properties/obj_id_path", "type": "string"}}, "additionalProperties": false}, {"$id": "#/properties/ops/items/anyOf/0/properties/logic/items/anyOf/1", "type": "object", "required": ["to_node_id", "type"], "properties": {"to_node_id": {"$id": "#/properties/ops/items/anyOf/0/properties/logic/items/anyOf/1/properties/to_node_id", "type": "string"}, "type": {"$id": "#/properties/ops/items/anyOf/0/properties/logic/items/anyOf/1/properties/type", "type": "string"}}, "additionalProperties": false}]}}, "history": {"$id": "#/properties/ops/items/anyOf/0/properties/history", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/history/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/history/items/anyOf/0", "type": "object", "required": ["time", "prev_conv_id", "conv_id", "prev_node_id", "node_id", "status", "data"], "properties": {"time": {"$id": "#/properties/ops/items/anyOf/0/properties/history/items/anyOf/0/properties/time", "type": "integer"}, "prev_conv_id": {"$id": "#/properties/ops/items/anyOf/0/properties/history/items/anyOf/0/properties/prev_conv_id", "type": "integer"}, "conv_id": {"$id": "#/properties/ops/items/anyOf/0/properties/history/items/anyOf/0/properties/conv_id", "type": "integer"}, "prev_node_id": {"$id": "#/properties/ops/items/anyOf/0/properties/history/items/anyOf/0/properties/prev_node_id"}, "node_id": {"$id": "#/properties/ops/items/anyOf/0/properties/history/items/anyOf/0/properties/node_id", "type": "string"}, "status": {"$id": "#/properties/ops/items/anyOf/0/properties/history/items/anyOf/0/properties/status", "type": "integer"}, "data": {"$id": "#/properties/ops/items/anyOf/0/properties/history/items/anyOf/0/properties/data"}}, "additionalProperties": false}]}}}, "additionalProperties": false}]}}}, "additionalProperties": false}