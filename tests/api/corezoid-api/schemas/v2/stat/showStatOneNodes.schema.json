{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "node_id", "title", "group", "conv_id", "dashboard_id", "data"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "node_id": {"type": "string"}, "title": {"type": "string"}, "group": {"type": "string"}, "conv_id": {"type": "integer"}, "dashboard_id": {"type": "string"}, "data": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["date", "in", "out"], "additionalProperties": false, "properties": {"date": {"type": ["string", "integer"]}, "in": {"type": "integer"}, "out": {"type": "integer"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}