{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj_id", "proc", "obj", "fingerprints"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj_id": {"type": "integer"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "fingerprints": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["algo", "value"], "additionalProperties": false, "properties": {"algo": {"type": "string"}, "value": {"type": "string"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}