{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "obj_id", "fingerprints"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "integer"}, "fingerprints": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["algo", "value"], "additionalProperties": false, "properties": {"algo": {"type": "string"}, "value": {"type": "string"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}