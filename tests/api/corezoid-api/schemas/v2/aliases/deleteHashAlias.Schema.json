{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "title": "The root schema", "description": "The root schema comprises the entire JSON document.", "default": {}, "const": {"request_proc": "ok", "ops": [{"id": "", "type": "delete", "obj": "alias_callback_hash", "proc": "ok"}]}, "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "title": "The request_proc schema", "description": "An explanation about the purpose of this instance.", "default": "", "const": "ok"}, "ops": {"$id": "#/properties/ops", "type": "array", "title": "The ops schema", "description": "An explanation about the purpose of this instance.", "default": [], "const": [{"id": "", "type": "delete", "obj": "alias_callback_hash", "proc": "ok"}], "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "title": "The first anyOf schema", "description": "An explanation about the purpose of this instance.", "default": {}, "const": {"id": "", "type": "delete", "obj": "alias_callback_hash", "proc": "ok"}, "required": ["id", "type", "obj", "proc"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string", "title": "The id schema", "description": "An explanation about the purpose of this instance.", "default": "", "const": ""}, "type": {"$id": "#/properties/ops/items/anyOf/0/properties/type", "type": "string", "title": "The type schema", "description": "An explanation about the purpose of this instance.", "default": "", "const": "delete"}, "obj": {"$id": "#/properties/ops/items/anyOf/0/properties/obj", "type": "string", "title": "The obj schema", "description": "An explanation about the purpose of this instance.", "default": "", "const": "alias_callback_hash"}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "title": "The proc schema", "description": "An explanation about the purpose of this instance.", "default": "", "const": "ok"}}, "additionalProperties": true}]}}}, "additionalProperties": true}