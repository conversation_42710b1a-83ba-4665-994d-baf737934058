{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "title": "The root schema", "description": "The root schema comprises the entire JSON document.", "default": {}, "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "title": "The request_proc schema", "description": "An explanation about the purpose of this instance.", "default": ""}, "ops": {"$id": "#/properties/ops", "type": "array", "title": "The ops schema", "description": "An explanation about the purpose of this instance.", "default": [], "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "title": "The first anyOf schema", "description": "An explanation about the purpose of this instance.", "default": {}, "required": ["id", "proc", "callback_hash"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string", "title": "The id schema", "description": "An explanation about the purpose of this instance.", "default": ""}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "title": "The proc schema", "description": "An explanation about the purpose of this instance.", "default": ""}, "callback_hash": {"$id": "#/properties/ops/items/anyOf/0/properties/callback_hash", "type": "string", "title": "The callback_hash schema", "description": "An explanation about the purpose of this instance.", "default": ""}}, "additionalProperties": true}]}}}, "additionalProperties": true}