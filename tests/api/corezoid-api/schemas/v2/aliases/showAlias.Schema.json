{"type": "object", "default": {}, "title": "Root Schema", "required": ["request_proc", "ops"], "properties": {"request_proc": {"type": "string", "default": "", "title": "The request_proc Schema", "examples": ["ok"]}, "ops": {"type": "array", "default": [], "title": "The ops Schema", "items": {"type": "object", "default": {}, "title": "A Schema", "required": ["id", "proc", "obj", "is_favorite", "is_owner", "owner_id", "owner_name", "owner_login", "obj_id", "obj_type", "title", "description", "short_name", "company_id", "project_id", "project_title", "project_short_name", "stage_id", "stage_title", "stage_short_name", "obj_to_id", "obj_to_type", "create_time", "change_time", "uuid", "privs"], "properties": {"id": {"type": "string", "default": "", "title": "The id Schema", "examples": [""]}, "proc": {"type": "string", "default": "", "title": "The proc Schema", "examples": ["ok"]}, "obj": {"type": "string", "default": "", "title": "The obj Schema", "examples": ["alias"]}, "is_favorite": {"type": "boolean", "default": false, "title": "The is_favorite <PERSON><PERSON><PERSON>", "examples": [false]}, "is_owner": {"type": "boolean", "default": false, "title": "The is_owner Sc<PERSON>a", "examples": [true]}, "owner_id": {"type": "integer", "default": 0, "title": "The owner_id <PERSON><PERSON>", "examples": [21396]}, "owner_name": {"type": "string", "default": "", "title": "The owner_name <PERSON><PERSON><PERSON>", "examples": ["ApiKeyForTest"]}, "owner_login": {"type": "string", "default": "", "title": "The owner_login <PERSON><PERSON>a", "examples": ["612f2acd56167c519a000d03"]}, "obj_id": {"type": "integer", "default": 0, "title": "The obj_id Schema", "examples": [76894]}, "obj_type": {"type": "string", "default": "", "title": "The obj_type Schema", "examples": ["alias"]}, "title": {"type": "string", "default": "", "title": "The title <PERSON><PERSON>a", "examples": ["aliasTitle-1686816034004"]}, "description": {"type": ["string", "null"], "default": "", "title": "The description Schema", "examples": ["aliasDesk-1686816034004"]}, "short_name": {"type": "string", "default": "", "title": "The short_name <PERSON><PERSON><PERSON>", "examples": ["alias-1686816034004"]}, "company_id": {"type": "string", "default": "", "title": "The company_id Schema", "examples": ["i738314881"]}, "project_id": {"type": "integer", "default": 0, "title": "The project_id Schema", "examples": [0]}, "project_title": {"type": "null", "default": null, "title": "The project_title Schema", "examples": [null]}, "project_short_name": {"type": "null", "default": null, "title": "The project_short_name <PERSON><PERSON><PERSON>", "examples": [null]}, "stage_id": {"type": "integer", "default": 0, "title": "The stage_id Schema", "examples": [0]}, "stage_title": {"type": "null", "default": null, "title": "The stage_title Sc<PERSON>a", "examples": [null]}, "stage_short_name": {"type": "null", "default": null, "title": "The stage_short_name <PERSON><PERSON><PERSON>", "examples": [null]}, "obj_to_id": {"type": ["string", "integer", "null"], "default": null, "title": "The obj_to_id Schema", "examples": [null]}, "obj_to_type": {"type": ["string", "null"], "default": null, "title": "The obj_to_type Schema", "examples": [null]}, "create_time": {"type": "integer", "default": 0, "title": "The create_time Schema", "examples": [1686816034]}, "change_time": {"type": "integer", "default": 0, "title": "The change_time Schema", "examples": [1686816034]}, "uuid": {"type": "string", "default": "", "title": "The uuid Schema", "examples": ["fa4ec691-6739-4bd9-aa3e-16ce38eddea5"]}, "privs": {"type": "array", "default": [], "title": "The privs Schema", "items": {"type": "object", "title": "A Schema", "required": ["type", "list_obj"], "properties": {"type": {"type": "string", "title": "The type Schema", "examples": ["create", "view", "modify", "delete"]}, "list_obj": {"type": "array", "title": "The list_obj <PERSON><PERSON><PERSON>", "items": {"type": "string", "title": "A Schema", "examples": ["all"]}, "examples": [["all"], ["all"], ["all"], ["all"]]}}, "examples": [{"type": "create", "list_obj": ["all"]}, {"type": "view", "list_obj": ["all"]}, {"type": "modify", "list_obj": ["all"]}, {"type": "delete", "list_obj": ["all"]}]}, "examples": [[{"type": "create", "list_obj": ["all"]}, {"type": "view", "list_obj": ["all"]}, {"type": "modify", "list_obj": ["all"]}, {"type": "delete", "list_obj": ["all"]}]]}}, "examples": [{"id": "", "proc": "ok", "obj": "alias", "is_favorite": false, "is_owner": true, "owner_id": 21396, "owner_name": "ApiKeyForTest", "owner_login": "612f2acd56167c519a000d03", "obj_id": 76894, "obj_type": "alias", "title": "aliasTitle-1686816034004", "description": "aliasDesk-1686816034004", "short_name": "alias-1686816034004", "company_id": "i738314881", "project_id": 0, "project_title": null, "project_short_name": null, "stage_id": 0, "stage_title": null, "stage_short_name": null, "obj_to_id": null, "obj_to_type": null, "create_time": 1686816034, "change_time": 1686816034, "uuid": "fa4ec691-6739-4bd9-aa3e-16ce38eddea5", "privs": [{"type": "create", "list_obj": ["all"]}, {"type": "view", "list_obj": ["all"]}, {"type": "modify", "list_obj": ["all"]}, {"type": "delete", "list_obj": ["all"]}]}]}, "examples": [[{"id": "", "proc": "ok", "obj": "alias", "is_favorite": false, "is_owner": true, "owner_id": 21396, "owner_name": "ApiKeyForTest", "owner_login": "612f2acd56167c519a000d03", "obj_id": 76894, "obj_type": "alias", "title": "aliasTitle-1686816034004", "description": "aliasDesk-1686816034004", "short_name": "alias-1686816034004", "company_id": "i738314881", "project_id": 0, "project_title": null, "project_short_name": null, "stage_id": 0, "stage_title": null, "stage_short_name": null, "obj_to_id": null, "obj_to_type": null, "create_time": 1686816034, "change_time": 1686816034, "uuid": "fa4ec691-6739-4bd9-aa3e-16ce38eddea5", "privs": [{"type": "create", "list_obj": ["all"]}, {"type": "view", "list_obj": ["all"]}, {"type": "modify", "list_obj": ["all"]}, {"type": "delete", "list_obj": ["all"]}]}]]}}, "examples": [{"request_proc": "ok", "ops": [{"id": "", "proc": "ok", "obj": "alias", "is_favorite": false, "is_owner": true, "owner_id": 21396, "owner_name": "ApiKeyForTest", "owner_login": "612f2acd56167c519a000d03", "obj_id": 76894, "obj_type": "alias", "title": "aliasTitle-1686816034004", "description": "aliasDesk-1686816034004", "short_name": "alias-1686816034004", "company_id": "i738314881", "project_id": 0, "project_title": null, "project_short_name": null, "stage_id": 0, "stage_title": null, "stage_short_name": null, "obj_to_id": null, "obj_to_type": null, "create_time": 1686816034, "change_time": 1686816034, "uuid": "fa4ec691-6739-4bd9-aa3e-16ce38eddea5", "privs": [{"type": "create", "list_obj": ["all"]}, {"type": "view", "list_obj": ["all"]}, {"type": "modify", "list_obj": ["all"]}, {"type": "delete", "list_obj": ["all"]}]}]}]}