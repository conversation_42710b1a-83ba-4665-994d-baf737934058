{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "obj_id", "list_obj", "proc", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "integer"}, "list_obj": {"type": "string"}, "proc": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj", "obj_id", "title", "privs"], "additionalProperties": false, "properties": {"obj": {"type": "string"}, "obj_id": {"type": "integer"}, "login_id": {"type": "integer"}, "title": {"type": "string"}, "privs": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["type", "list_obj"], "additionalProperties": false, "properties": {"type": {"type": "string"}, "list_obj": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}}}, "uniqueItems": true}, "login": {"type": "string"}, "obj_type": {"type": "string"}, "obj_status": {"type": "string"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}