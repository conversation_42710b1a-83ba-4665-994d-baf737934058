{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "obj_id", "proc", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "integer"}, "proc": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["is_shared", "favorite", "privs", "size", "is_owner", "owner_id", "owner_name", "owner_login", "obj_id", "obj_type", "create_time", "change_time", "status", "title", "description", "conv_type", "company_id", "project_id", "stage_id", "owner_group_id", "is_deployed", "parent_id", "version"], "additionalProperties": false, "properties": {"is_shared": {"type": "boolean"}, "favorite": {"type": "boolean"}, "privs": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["type", "list_obj"], "additionalProperties": false, "properties": {"type": {"type": "string"}, "list_obj": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}}}, "uniqueItems": true}, "size": {"type": "integer"}, "is_owner": {"type": "boolean"}, "owner_id": {"type": "integer"}, "owner_name": {"type": "string"}, "owner_login": {"type": "string"}, "obj_id": {"type": "integer"}, "obj_type": {"type": "string"}, "create_time": {"type": "integer"}, "change_time": {"type": "integer"}, "status": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "conv_type": {"type": "string"}, "company_id": {"type": "string"}, "project_id": {"type": "integer"}, "stage_id": {"type": "integer"}, "owner_group_id": {"type": "integer"}, "is_deployed": {"type": "boolean"}, "parent_id": {"type": "integer"}, "version": {"type": "integer"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}