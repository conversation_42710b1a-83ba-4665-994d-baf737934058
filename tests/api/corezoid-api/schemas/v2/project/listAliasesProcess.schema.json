{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "const": "ok"}, "ops": {"$id": "#/properties/ops", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "required": ["id", "proc", "list"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string"}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "const": "ok"}, "list": {"$id": "#/properties/ops/items/anyOf/0/properties/list", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0", "type": "object", "required": ["is_favorite", "obj_to_title", "is_owner", "owner_id", "owner_name", "owner_login", "obj_id", "obj_type", "title", "description", "short_name", "company_id", "project_id", "stage_id", "obj_to_id", "obj_to_type", "create_time", "change_time", "uuid", "stage_title", "project_title", "stage_short_name", "project_short_name", "privs"], "properties": {"is_favorite": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/is_favorite", "type": "boolean"}, "obj_to_title": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/obj_to_title", "type": "string"}, "is_owner": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/is_owner", "type": "boolean"}, "owner_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/owner_id", "type": "integer"}, "owner_name": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/owner_name", "type": "string"}, "owner_login": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/owner_login", "type": "string"}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/obj_id", "type": "integer"}, "obj_type": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/obj_type", "type": "string"}, "title": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/title", "type": "string"}, "description": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/description", "type": "string"}, "short_name": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/short_name", "type": "string"}, "company_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/company_id", "type": "string"}, "project_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/project_id", "type": "integer"}, "stage_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/stage_id", "type": "integer"}, "obj_to_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/obj_to_id", "type": "integer"}, "obj_to_type": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/obj_to_type", "type": "string"}, "create_time": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/create_time", "type": "integer"}, "change_time": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/change_time", "type": "integer"}, "uuid": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/uuid", "type": "string"}, "stage_title": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/stage_title", "type": "string"}, "project_title": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/project_title", "type": "string"}, "stage_short_name": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/stage_short_name", "type": "string"}, "project_short_name": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/project_short_name", "type": "string"}, "privs": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/privs", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/privs/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/privs/items/anyOf/0", "type": "object", "required": ["type", "list_obj"], "properties": {"type": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/privs/items/anyOf/0/properties/type", "type": "string"}, "list_obj": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/privs/items/anyOf/0/properties/list_obj", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/privs/items/anyOf/0/properties/list_obj/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/privs/items/anyOf/0/properties/list_obj/items/anyOf/0", "type": "string"}]}}}, "additionalProperties": false}]}}}, "additionalProperties": false}]}}}, "additionalProperties": false}]}}}, "additionalProperties": false}