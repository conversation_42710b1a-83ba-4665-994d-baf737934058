{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "proc", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "proc": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["is_owner", "owner_id", "owner_name", "owner_login", "obj_id", "obj_type", "project_id", "project_title", "stage_id", "stage_title", "vsn", "title", "changelog", "link", "create_time", "change_time", "user_id", "status", "file_format_vsn", "privs"], "additionalProperties": false, "properties": {"is_owner": {"type": "boolean"}, "owner_id": {"type": "integer"}, "owner_name": {"type": "string"}, "owner_login": {"type": "string"}, "obj_id": {"type": "integer"}, "obj_type": {"type": "string"}, "project_id": {"type": "integer"}, "project_title": {"type": "string"}, "stage_id": {"type": "integer"}, "stage_title": {"type": "string"}, "vsn": {"type": "string"}, "title": {"type": "null"}, "changelog": {"type": "string"}, "link": {"type": "string"}, "create_time": {"type": "integer"}, "change_time": {"type": "integer"}, "user_id": {"type": "integer"}, "status": {"type": "integer"}, "file_format_vsn": {"type": "integer"}, "privs": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["type", "list_obj"], "additionalProperties": false, "properties": {"type": {"type": "string"}, "list_obj": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}}}, "uniqueItems": true}}}, "uniqueItems": true}}}, "uniqueItems": true}}}