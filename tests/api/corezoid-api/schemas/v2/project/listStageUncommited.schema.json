{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "obj_id", "list_obj", "proc", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "integer"}, "list_obj": {"type": "string"}, "proc": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["is_owner", "owner_id", "owner_name", "owner_login", "obj_id", "obj_type", "conv_type", "title", "folder_id", "change_time"], "additionalProperties": false, "properties": {"is_owner": {"type": "boolean"}, "owner_id": {"type": "integer"}, "owner_name": {"type": "string"}, "owner_login": {"type": "string"}, "obj_id": {"type": "integer"}, "obj_type": {"type": "string"}, "conv_type": {"type": "string"}, "title": {"type": "string"}, "folder_id": {"type": "integer"}, "change_time": {"type": "integer"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}