{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj_id", "title", "company_id", "privs", "favorite", "owner_id", "owner_name", "owner_login"], "additionalProperties": false, "properties": {"obj_id": {"type": "integer"}, "title": {"type": "string"}, "company_id": {"type": "string"}, "privs": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["type", "list_obj"], "additionalProperties": false, "properties": {"type": {"type": "string"}, "list_obj": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}}}, "uniqueItems": true}, "favorite": {"type": "boolean"}, "owner_id": {"type": "integer"}, "owner_name": {"type": "string"}, "owner_login": {"type": "string"}, "type": {"type": "string"}, "description": {"type": ["string", "null"]}, "short_name": {"type": "string"}, "immutable": {"type": "boolean"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}