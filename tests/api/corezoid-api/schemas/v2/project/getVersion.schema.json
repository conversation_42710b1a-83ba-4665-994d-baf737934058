{"type": "object", "default": {}, "title": "Root Schema", "required": ["request_proc", "ops"], "properties": {"request_proc": {"type": "string", "default": "", "title": "The request_proc Schema", "examples": ["ok"]}, "ops": {"type": "array", "default": [], "title": "The ops Schema", "items": {"type": "object", "default": {}, "title": "A Schema", "required": ["id", "obj", "proc", "is_owner", "owner_id", "owner_name", "owner_login", "obj_id", "obj_type", "project_id", "project_title", "stage_id", "stage_title", "vsn", "title", "changelog", "link", "create_time", "change_time", "user_id", "status", "file_format_vsn"], "properties": {"id": {"type": "string", "default": "", "title": "The id Schema", "examples": [""]}, "obj": {"type": "string", "default": "", "title": "The obj Schema", "examples": ["version"]}, "proc": {"type": "string", "default": "", "title": "The proc Schema", "examples": ["ok"]}, "is_owner": {"type": "boolean", "default": false, "title": "The is_owner Sc<PERSON>a", "examples": [true]}, "owner_id": {"type": "integer", "default": 0, "title": "The owner_id <PERSON><PERSON>", "examples": [60184]}, "owner_name": {"type": "string", "default": "", "title": "The owner_name <PERSON><PERSON><PERSON>", "examples": ["forApiTest"]}, "owner_login": {"type": "string", "default": "", "title": "The owner_login <PERSON><PERSON>a", "examples": ["655cbcae56167c312501b853"]}, "obj_id": {"type": "integer", "default": 0, "title": "The obj_id Schema", "examples": [24936]}, "obj_type": {"type": "string", "default": "", "title": "The obj_type Schema", "examples": ["version"]}, "project_id": {"type": "integer", "default": 0, "title": "The project_id Schema", "examples": [597204]}, "project_title": {"type": "string", "default": "", "title": "The project_title Schema", "examples": ["Project_1702901181024"]}, "stage_id": {"type": "integer", "default": 0, "title": "The stage_id Schema", "examples": [597205]}, "stage_title": {"type": "string", "default": "", "title": "The stage_title Sc<PERSON>a", "examples": ["Stage_1702901181263"]}, "vsn": {"type": "string", "default": "", "title": "The vsn Schema", "examples": ["1702901182042"]}, "title": {"type": "null", "default": null, "title": "The title <PERSON><PERSON>a", "examples": [null]}, "changelog": {"type": "string", "default": "", "title": "The changelog Schema", "examples": ["modify"]}, "link": {"type": "string", "default": "", "title": "The link Schema", "examples": ["version_597205_1702901182042_1702901182396.zip"]}, "create_time": {"type": "integer", "default": 0, "title": "The create_time Schema", "examples": [1702901182]}, "change_time": {"type": "integer", "default": 0, "title": "The change_time Schema", "examples": [1702901182]}, "user_id": {"type": "integer", "default": 0, "title": "The user_id Schema", "examples": [60184]}, "status": {"type": "integer", "default": 0, "title": "The status Schema", "examples": [1]}, "file_format_vsn": {"type": "integer", "default": 0, "title": "The file_format_vsn Schema", "examples": [1]}}, "examples": [{"id": "", "obj": "version", "proc": "ok", "is_owner": true, "owner_id": 60184, "owner_name": "forApiTest", "owner_login": "655cbcae56167c312501b853", "obj_id": 24936, "obj_type": "version", "project_id": 597204, "project_title": "Project_1702901181024", "stage_id": 597205, "stage_title": "Stage_1702901181263", "vsn": "1702901182042", "title": null, "changelog": "modify", "link": "version_597205_1702901182042_1702901182396.zip", "create_time": 1702901182, "change_time": 1702901182, "user_id": 60184, "status": 1, "file_format_vsn": 1}]}, "examples": [[{"id": "", "obj": "version", "proc": "ok", "is_owner": true, "owner_id": 60184, "owner_name": "forApiTest", "owner_login": "655cbcae56167c312501b853", "obj_id": 24936, "obj_type": "version", "project_id": 597204, "project_title": "Project_1702901181024", "stage_id": 597205, "stage_title": "Stage_1702901181263", "vsn": "1702901182042", "title": null, "changelog": "modify", "link": "version_597205_1702901182042_1702901182396.zip", "create_time": 1702901182, "change_time": 1702901182, "user_id": 60184, "status": 1, "file_format_vsn": 1}]]}}, "examples": [{"request_proc": "ok", "ops": [{"id": "", "obj": "version", "proc": "ok", "is_owner": true, "owner_id": 60184, "owner_name": "forApiTest", "owner_login": "655cbcae56167c312501b853", "obj_id": 24936, "obj_type": "version", "project_id": 597204, "project_title": "Project_1702901181024", "stage_id": 597205, "stage_title": "Stage_1702901181263", "vsn": "1702901182042", "title": null, "changelog": "modify", "link": "version_597205_1702901182042_1702901182396.zip", "create_time": 1702901182, "change_time": 1702901182, "user_id": 60184, "status": 1, "file_format_vsn": 1}]}]}