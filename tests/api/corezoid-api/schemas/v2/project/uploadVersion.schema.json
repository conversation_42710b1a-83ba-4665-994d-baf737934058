{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj", "proc", "stage_to_id"], "additionalProperties": false, "properties": {"obj": {"type": "string"}, "proc": {"type": "string"}, "stage_to_id": {"type": "integer"}}}, "uniqueItems": true}}}