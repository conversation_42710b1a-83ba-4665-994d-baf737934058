{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "obj_id", "obj_short_name", "stages", "parent_obj_id", "parent_obj_type"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "integer"}, "obj_short_name": {"type": "string"}, "stages": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["title", "obj_id", "obj_short_name"], "additionalProperties": false, "properties": {"title": {"type": "string"}, "obj_id": {"type": "integer"}, "obj_short_name": {"type": "string"}}}, "uniqueItems": true}, "parent_obj_id": {"type": "integer"}, "parent_obj_type": {"type": "string"}}}, "uniqueItems": true}}}