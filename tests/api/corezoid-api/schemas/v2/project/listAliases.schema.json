{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["is_favorite", "is_owner", "owner_id", "owner_name", "owner_login", "obj_id", "obj_type", "title", "description", "short_name", "company_id", "project_id", "project_title", "project_short_name", "stage_id", "stage_title", "stage_short_name", "obj_to_id", "obj_to_type", "create_time", "change_time", "uuid", "privs"], "additionalProperties": false, "properties": {"is_favorite": {"type": "boolean"}, "is_owner": {"type": "boolean"}, "owner_id": {"type": "integer"}, "owner_name": {"type": "string"}, "owner_login": {"type": "string"}, "obj_id": {"type": "integer"}, "obj_type": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "short_name": {"type": "string"}, "company_id": {"type": "string"}, "project_id": {"type": "integer"}, "project_title": {"type": "string"}, "project_short_name": {"type": "string"}, "stage_id": {"type": "integer"}, "stage_title": {"type": "string"}, "stage_short_name": {"type": "string"}, "obj_to_id": {"type": ["null", "integer"]}, "obj_to_type": {"type": ["null", "string"]}, "create_time": {"type": "integer"}, "change_time": {"type": "integer"}, "uuid": {"type": "string"}, "privs": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["type", "list_obj"], "additionalProperties": false, "properties": {"type": {"type": "string"}, "list_obj": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}}}, "uniqueItems": true}, "obj_to_title": {"type": "string"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}