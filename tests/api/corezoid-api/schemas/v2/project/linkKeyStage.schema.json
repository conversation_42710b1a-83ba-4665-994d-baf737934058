{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "const": "ok"}, "ops": {"$id": "#/properties/ops", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "required": ["id", "proc", "obj_type", "obj_id", "obj_to_type", "obj_to_id", "obj_to_title", "action_type"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string", "const": ""}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "const": "ok"}, "obj_type": {"$id": "#/properties/ops/items/anyOf/0/properties/obj_type", "type": "string", "const": "stage"}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/obj_id", "type": "integer"}, "obj_to_type": {"$id": "#/properties/ops/items/anyOf/0/properties/obj_to_type", "type": "string", "const": "user"}, "obj_to_id": {"$id": "#/properties/ops/items/anyOf/0/properties/obj_to_id", "type": "integer"}, "obj_to_title": {"$id": "#/properties/ops/items/anyOf/0/properties/obj_to_title", "type": "string"}, "action_type": {"$id": "#/properties/ops/items/anyOf/0/properties/action_type", "type": "string", "const": "link"}}, "additionalProperties": false}]}}}, "additionalProperties": false}