{"type": "object", "default": {}, "title": "Root Schema", "required": ["request_proc", "ops"], "properties": {"request_proc": {"type": "string", "default": "", "title": "The request_proc Schema", "examples": ["ok"]}, "ops": {"type": "array", "default": [], "title": "The ops Schema", "items": {"type": "object", "default": {}, "title": "A Schema", "required": ["id", "proc", "obj", "description"], "properties": {"id": {"type": "string", "default": "", "title": "The id Schema", "examples": [""]}, "proc": {"type": "string", "default": "", "title": "The proc Schema", "examples": ["ok"]}, "obj": {"type": "string", "default": "", "title": "The obj Schema", "examples": ["api_code"]}, "description": {"type": "string", "default": "", "title": "The description Schema", "examples": [""]}}, "examples": [{"id": "", "proc": "ok", "obj": "api_code", "description": ""}]}, "examples": [[{"id": "", "proc": "ok", "obj": "api_code", "description": ""}]]}}, "examples": [{"request_proc": "ok", "ops": [{"id": "", "proc": "ok", "obj": "api_code", "description": ""}]}]}