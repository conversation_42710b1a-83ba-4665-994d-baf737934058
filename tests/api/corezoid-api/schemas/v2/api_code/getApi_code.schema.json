{"type": "object", "default": {}, "title": "Root Schema", "required": ["request_proc", "ops"], "properties": {"request_proc": {"type": "string", "default": "", "title": "The request_proc Schema", "examples": ["ok"]}, "ops": {"type": "array", "default": [], "title": "The ops Schema", "items": {"type": "object", "default": {}, "title": "A Schema", "required": ["id", "proc", "obj", "lang", "src"], "properties": {"id": {"type": "string", "default": "", "title": "The id Schema", "examples": [""]}, "proc": {"type": "string", "default": "", "title": "The proc Schema", "examples": ["ok"]}, "obj": {"type": "string", "default": "", "title": "The obj Schema", "examples": ["api_code"]}, "lang": {"type": "string", "default": "", "title": "The lang Schema", "examples": ["js"]}, "src": {"type": "string", "default": "", "title": "The src Schema", "examples": ["var b = 123456789;data.a = b;"]}}, "examples": [{"id": "", "proc": "ok", "obj": "api_code", "lang": "js", "src": "var b = 123456789;data.a = b;"}]}, "examples": [[{"id": "", "proc": "ok", "obj": "api_code", "lang": "js", "src": "var b = 123456789;data.a = b;"}]]}}, "examples": [{"request_proc": "ok", "ops": [{"id": "", "proc": "ok", "obj": "api_code", "lang": "js", "src": "var b = 123456789;data.a = b;"}]}]}