{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["proc", "status", "scheme", "errors"], "additionalProperties": false, "properties": {"proc": {"type": "string"}, "status": {"type": "string"}, "scheme": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": true}, "errors": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj_id", "obj", "title", "errors"], "additionalProperties": false, "properties": {"obj_id": {"type": "integer"}, "obj": {"type": "string"}, "title": {"type": "string"}, "errors": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}}}, "uniqueItems": true}}}, "uniqueItems": true}}}