{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["proc", "status", "scheme", "errors"], "additionalProperties": false, "properties": {"proc": {"type": "string"}, "status": {"type": "string"}, "scheme": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["old_obj_id", "obj_id", "obj_type", "title"], "additionalProperties": false, "properties": {"old_obj_id": {"type": "integer"}, "obj_id": {"type": "integer"}, "obj_type": {"type": "string"}, "title": {"type": "string"}}}, "uniqueItems": true}, "errors": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj_id", "obj", "title", "count", "destinations"], "additionalProperties": false, "properties": {"obj_id": {"type": "integer"}, "obj": {"type": "string"}, "title": {"type": "string"}, "count": {"type": "integer"}, "destinations": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["conv_type", "obj_id", "obj", "title", "count", "destinations"], "additionalProperties": false, "properties": {"conv_type": {"type": "string"}, "obj_id": {"type": "integer"}, "obj": {"type": "string"}, "title": {"type": "string"}, "count": {"type": "integer"}, "destinations": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj_id", "obj", "title", "count", "errors"], "additionalProperties": false, "properties": {"obj_id": {"type": "string"}, "obj": {"type": "string"}, "title": {"type": "string"}, "count": {"type": "integer"}, "errors": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}}}, "uniqueItems": true}}}, "uniqueItems": true}}}, "uniqueItems": true}}}, "uniqueItems": true}}}