{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["proc", "status", "scheme"], "additionalProperties": false, "properties": {"proc": {"type": "string"}, "status": {"type": "string"}, "scheme": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["old_obj_id", "obj_id", "obj_type", "title"], "additionalProperties": false, "properties": {"old_obj_id": {"type": "integer"}, "obj_id": {"type": "integer"}, "obj_type": {"type": "string"}, "title": {"type": "string"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}