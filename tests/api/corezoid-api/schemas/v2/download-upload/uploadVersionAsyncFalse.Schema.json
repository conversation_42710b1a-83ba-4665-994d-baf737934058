{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj", "proc", "stage_to_id"], "additionalProperties": false, "properties": {"obj": {"type": "string"}, "proc": {"type": "string"}, "stage_to_id": {"type": "integer"}}}, "uniqueItems": true}}}