{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "const": "ok"}, "ops": {"$id": "#/properties/ops", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "required": ["id", "proc", "errors"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string"}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "const": "error"}, "errors": {"$id": "#/properties/ops/items/anyOf/0/properties/errors", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/errors/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/errors/items/anyOf/0", "type": "object", "required": ["obj_id", "obj_type", "description"], "properties": {"obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/errors/items/anyOf/0/properties/obj_id", "type": "integer"}, "obj_type": {"$id": "#/properties/ops/items/anyOf/0/properties/errors/items/anyOf/0/properties/obj_type", "type": "string"}, "description": {"$id": "#/properties/ops/items/anyOf/0/properties/errors/items/anyOf/0/properties/description", "type": "string"}}, "additionalProperties": false}]}}}, "additionalProperties": false}]}}}, "additionalProperties": false}