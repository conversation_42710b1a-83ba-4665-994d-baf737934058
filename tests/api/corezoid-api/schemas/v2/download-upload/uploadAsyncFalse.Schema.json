{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj", "proc", "scheme"], "additionalProperties": false, "properties": {"obj": {"type": "string"}, "proc": {"type": "string"}, "scheme": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj_id", "obj_type", "title", "description", "old_obj_id", "old_parent_id"], "additionalProperties": false, "properties": {"obj_id": {"type": "integer"}, "obj_type": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "old_obj_id": {"type": "integer"}, "old_parent_id": {"type": "integer"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}