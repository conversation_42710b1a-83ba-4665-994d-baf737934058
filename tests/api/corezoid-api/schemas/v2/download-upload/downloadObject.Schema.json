{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "const": "ok"}, "ops": {"$id": "#/properties/ops", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "required": ["proc", "obj", "download_url", "statistics_id"], "properties": {"proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "const": "ok"}, "obj": {"$id": "#/properties/ops/items/anyOf/0/properties/obj", "type": "string", "const": "obj_scheme"}, "download_url": {"$id": "#/properties/ops/items/anyOf/0/properties/download_url", "type": "string"}, "statistics_id": {"$id": "#/properties/ops/items/anyOf/0/properties/statistics_id", "type": "string"}}, "additionalProperties": false}]}}}, "additionalProperties": false}