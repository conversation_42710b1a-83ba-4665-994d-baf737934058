{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "proc", "data"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "proc": {"type": "string"}, "data": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj_id", "company_id", "obj", "obj_type", "ok", "err", "all", "progress"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj_id": {"type": "integer"}, "company_id": {"type": "string"}, "obj": {"type": "string", "enum": ["obj_scheme", "csv_upload"]}, "obj_type": {"type": "string", "enum": ["project", "stage", "version", "folder", "conv", "dashboard", "instance"]}, "ok": {"type": "integer"}, "err": {"type": "integer"}, "all": {"type": "integer"}, "progress": {"type": "integer"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}