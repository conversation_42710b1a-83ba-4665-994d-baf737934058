{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj", "proc", "ok_count", "err_count", "all_count"], "additionalProperties": false, "properties": {"obj": {"type": "string"}, "proc": {"type": "string"}, "ok_count": {"type": "integer"}, "err_count": {"type": "integer"}, "all_count": {"type": "integer"}}}, "uniqueItems": true}}}