{"type": "object", "required": ["result", "data", "meta"], "additionalProperties": false, "properties": {"result": {"type": "string"}, "data": {"type": "object", "required": ["account_license_module", "issuer", "created_time", "company_id", "cluster_id", "multi_tenancy", "max_active_procs", "max_rps", "max_rpm", "max_scpm", "max_task_size_process", "max_task_size_state_diagram", "min_timer", "max_users", "max_storage_size", "time_to_expire", "time_to_start", "is_allow", "pub_key"], "additionalProperties": false, "properties": {"account_license_module": {"type": "boolean"}, "issuer": {"type": "string"}, "created_time": {"type": "string"}, "company_id": {"type": "string"}, "cluster_id": {"type": "string"}, "multi_tenancy": {"type": "boolean"}, "max_active_procs": {"type": "string"}, "max_rps": {"type": "string"}, "max_rpm": {"type": "string"}, "max_scpm": {"type": "string"}, "max_task_size_process": {"type": "string"}, "max_task_size_state_diagram": {"type": "integer"}, "min_timer": {"type": "integer"}, "max_users": {"type": "string"}, "max_storage_size": {"type": "string"}, "time_to_expire": {"type": "integer"}, "time_to_start": {"type": "integer"}, "is_allow": {"type": "boolean"}, "pub_key": {"type": "string"}}}, "meta": {"type": "object", "required": ["account_license_module"], "additionalProperties": false, "properties": {"account_license_module": {"type": "string"}}}}}