{"type": "object", "required": ["create_time", "status", "download_count", "pub_key", "company_name", "cluster_id", "start_time", "expire_time", "max_active_procs", "max_rps", "multi_tenancy"], "additionalProperties": false, "properties": {"create_time": {"type": "integer"}, "status": {"type": "string"}, "download_count": {"type": "integer"}, "pub_key": {"type": "string"}, "company_name": {"type": "string"}, "cluster_id": {"type": "string"}, "start_time": {"type": "integer"}, "expire_time": {"type": "integer"}, "max_active_procs": {"type": "integer"}, "max_rps": {"type": "integer"}, "multi_tenancy": {"type": "boolean"}}}