{"type": "object", "required": ["result", "data", "meta"], "additionalProperties": false, "properties": {"result": {"type": "string"}, "data": {"type": "object", "required": ["pub_key", "multi_tenancy", "company_id", "cluster_id", "is_allow", "time_to_start", "max_task_size_state_diagram", "max_scpm", "max_rpm", "max_storage_size", "created_time", "account_license_module", "max_active_procs", "max_task_size_process", "max_users", "time_to_expire", "max_rps", "min_timer", "issuer"], "additionalProperties": false, "properties": {"pub_key": {"type": "string"}, "multi_tenancy": {"type": "boolean"}, "company_id": {"type": "string"}, "cluster_id": {"type": "string"}, "is_allow": {"type": "boolean"}, "time_to_start": {"type": "integer"}, "max_task_size_state_diagram": {"type": "string"}, "max_scpm": {"type": "string"}, "max_rpm": {"type": "string"}, "max_storage_size": {"type": "string"}, "created_time": {"type": "string"}, "account_license_module": {"type": "boolean"}, "max_active_procs": {"type": "string"}, "max_task_size_process": {"type": "string"}, "max_users": {"type": "string"}, "time_to_expire": {"type": "integer"}, "max_rps": {"type": "string"}, "min_timer": {"type": ["integer", "string"]}, "issuer": {"type": "string"}}}, "meta": {"type": "object", "required": ["account_license_module"], "additionalProperties": false, "properties": {"account_license_module": {"type": "string"}}}}}