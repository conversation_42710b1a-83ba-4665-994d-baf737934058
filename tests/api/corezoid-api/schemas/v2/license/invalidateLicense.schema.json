{"type": "object", "required": ["result", "list"], "additionalProperties": false, "properties": {"result": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["proc", "component", "ip"], "additionalProperties": false, "properties": {"proc": {"type": "string"}, "reason": {"type": "string"}, "component": {"type": "string"}, "ip": {"type": "string"}, "vsn": {"type": "string"}, "start_time": {"type": "integer"}}}, "uniqueItems": true}}}