{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "obj_type", "version"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "obj_type": {"type": "string"}, "version": {"type": "integer"}, "description": {"type": "string"}}}, "uniqueItems": true}}}