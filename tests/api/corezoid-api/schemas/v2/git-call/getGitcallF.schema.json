{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "title": "The root schema", "description": "The root schema comprises the entire JSON document.", "default": {}, "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "title": "The request_proc schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["ok"]}, "ops": {"$id": "#/properties/ops", "type": "array", "title": "The ops schema", "description": "An explanation about the purpose of this instance.", "default": [], "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "title": "The first anyOf schema", "description": "An explanation about the purpose of this instance.", "default": {}, "required": ["id", "proc", "obj", "obj_type", "version", "lang", "code", "repo", "commit", "script"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string", "title": "The id schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": [""]}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "title": "The proc schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["ok"]}, "obj": {"$id": "#/properties/ops/items/anyOf/0/properties/obj", "type": "string", "title": "The obj schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["git_call"]}, "obj_type": {"$id": "#/properties/ops/items/anyOf/0/properties/obj_type", "type": "string", "title": "The obj_type schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["function"]}, "version": {"$id": "#/properties/ops/items/anyOf/0/properties/version", "type": "integer", "title": "The version schema", "description": "An explanation about the purpose of this instance.", "default": 0}, "lang": {"$id": "#/properties/ops/items/anyOf/0/properties/lang", "type": "string", "title": "The lang schema", "description": "An explanation about the purpose of this instance.", "default": ""}, "code": {"$id": "#/properties/ops/items/anyOf/0/properties/code", "type": "string", "title": "The code schema", "description": "An explanation about the purpose of this instance.", "default": ""}, "repo": {"$id": "#/properties/ops/items/anyOf/0/properties/repo", "type": "string", "title": "The repo schema", "description": "An explanation about the purpose of this instance.", "default": ""}, "commit": {"$id": "#/properties/ops/items/anyOf/0/properties/commit", "type": "string", "title": "The commit schema", "description": "An explanation about the purpose of this instance.", "default": ""}, "script": {"$id": "#/properties/ops/items/anyOf/0/properties/script", "type": "string", "title": "The script schema", "description": "An explanation about the purpose of this instance.", "default": ""}}, "additionalProperties": true}]}}}, "additionalProperties": true}