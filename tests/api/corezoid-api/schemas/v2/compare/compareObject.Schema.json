{"type": "object", "default": {}, "title": "Root Schema", "required": ["request_proc", "ops"], "properties": {"request_proc": {"type": "string", "default": "", "title": "The request_proc Schema", "examples": ["ok"]}, "ops": {"type": "array", "default": [], "title": "The ops Schema", "items": {"type": "object", "default": {}, "title": "A Schema", "required": ["proc", "obj", "list"], "properties": {"proc": {"type": "string", "default": "", "title": "The proc Schema", "examples": ["ok"]}, "obj": {"type": "string", "default": "", "title": "The obj Schema", "examples": ["obj_scheme"]}, "list": {"type": "array", "default": [], "title": "The list Schema", "items": {"type": "object", "title": "A Schema", "required": ["parent_id", "obj_id", "title", "obj_type", "__num_stat", "__status"], "properties": {"parent_id": {"type": "integer", "title": "The parent_id Schema", "examples": [458594]}, "obj_id": {"type": "integer", "title": "The obj_id Schema", "examples": [1183747, 70438]}, "title": {"type": "string", "title": "The title <PERSON><PERSON>a", "examples": ["conv_1683629605465", "aliasTitle-1683629605582"]}, "obj_type": {"type": "string", "title": "The obj_type Schema", "examples": ["conv", "alias"]}, "__num_stat": {"type": "object", "title": "The __num_stat Schema", "required": ["added", "changed", "deleted"], "properties": {"added": {"type": "integer", "title": "The added Schema", "examples": [1]}, "changed": {"type": "integer", "title": "The changed Schema", "examples": [0]}, "deleted": {"type": "integer", "title": "The deleted Schema", "examples": [0]}}, "examples": [{"added": 1, "changed": 0, "deleted": 0}, {"added": 1, "changed": 0, "deleted": 0}]}, "__status": {"type": "string", "title": "The __status Schema", "examples": ["added"]}}, "examples": [{"parent_id": 458594, "obj_id": 1183747, "title": "conv_1683629605465", "obj_type": "conv", "__num_stat": {"added": 1, "changed": 0, "deleted": 0}, "__status": "added"}, {"parent_id": 458594, "obj_id": 70438, "title": "aliasTitle-1683629605582", "obj_type": "alias", "__num_stat": {"added": 1, "changed": 0, "deleted": 0}, "__status": "added"}]}, "examples": [[{"parent_id": 458594, "obj_id": 1183747, "title": "conv_1683629605465", "obj_type": "conv", "__num_stat": {"added": 1, "changed": 0, "deleted": 0}, "__status": "added"}, {"parent_id": 458594, "obj_id": 70438, "title": "aliasTitle-1683629605582", "obj_type": "alias", "__num_stat": {"added": 1, "changed": 0, "deleted": 0}, "__status": "added"}]]}}, "examples": [{"proc": "ok", "obj": "obj_scheme", "list": [{"parent_id": 458594, "obj_id": 1183747, "title": "conv_1683629605465", "obj_type": "conv", "__num_stat": {"added": 1, "changed": 0, "deleted": 0}, "__status": "added"}, {"parent_id": 458594, "obj_id": 70438, "title": "aliasTitle-1683629605582", "obj_type": "alias", "__num_stat": {"added": 1, "changed": 0, "deleted": 0}, "__status": "added"}]}]}, "examples": [[{"proc": "ok", "obj": "obj_scheme", "list": [{"parent_id": 458594, "obj_id": 1183747, "title": "conv_1683629605465", "obj_type": "conv", "__num_stat": {"added": 1, "changed": 0, "deleted": 0}, "__status": "added"}, {"parent_id": 458594, "obj_id": 70438, "title": "aliasTitle-1683629605582", "obj_type": "alias", "__num_stat": {"added": 1, "changed": 0, "deleted": 0}, "__status": "added"}]}]]}}, "examples": [{"request_proc": "ok", "ops": [{"proc": "ok", "obj": "obj_scheme", "list": [{"parent_id": 458594, "obj_id": 1183747, "title": "conv_1683629605465", "obj_type": "conv", "__num_stat": {"added": 1, "changed": 0, "deleted": 0}, "__status": "added"}, {"parent_id": 458594, "obj_id": 70438, "title": "aliasTitle-1683629605582", "obj_type": "alias", "__num_stat": {"added": 1, "changed": 0, "deleted": 0}, "__status": "added"}]}]}]}