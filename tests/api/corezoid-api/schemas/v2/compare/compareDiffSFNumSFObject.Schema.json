{"type": "object", "default": {}, "title": "Root Schema", "required": ["request_proc", "ops"], "properties": {"request_proc": {"type": "string", "default": "", "title": "The request_proc Schema", "examples": ["ok"]}, "ops": {"type": "array", "default": [], "title": "The ops Schema", "items": {"type": "object", "default": {}, "title": "A Schema", "required": ["proc", "obj", "list"], "properties": {"proc": {"type": "string", "default": "", "title": "The proc Schema", "examples": ["ok"]}, "obj": {"type": "string", "default": "", "title": "The obj Schema", "examples": ["obj_scheme"]}, "list": {"type": "array", "default": [], "title": "The list Schema", "items": {"type": "object", "title": "A Schema", "required": ["parent_id", "obj_id", "title", "obj_type", "__added", "description", "uuid"], "properties": {"parent_id": {"type": "integer", "title": "The parent_id Schema", "examples": [459144]}, "obj_id": {"type": "integer", "title": "The obj_id Schema", "examples": [1184942, 70550, 459146]}, "title": {"type": "string", "title": "The title <PERSON><PERSON>a", "examples": ["conv_1683714308979", "aliasTitle-1683714309181", "folder_1683714309091"]}, "obj_type": {"type": "string", "title": "The obj_type Schema", "examples": ["conv", "alias", "folder"]}, "__added": {"type": "object", "title": "The __added <PERSON><PERSON><PERSON>", "required": ["obj_type", "obj_id", "parent_id", "title", "description", "uuid"], "properties": {"obj_type": {"type": "integer", "title": "The obj_type Schema", "examples": [1, 9, 0]}, "obj_id": {"type": "integer", "title": "The obj_id Schema", "examples": [1184942, 70550, 459146]}, "parent_id": {"type": "integer", "title": "The parent_id Schema", "examples": [459145]}, "title": {"type": "string", "title": "The title <PERSON><PERSON>a", "examples": ["conv_1683714308979", "aliasTitle-1683714309181", "folder_1683714309091"]}, "description": {"type": "string", "title": "The description Schema", "examples": ["", "aliasDesk-1683714309181"]}, "status": {"type": "string", "default": "", "title": "The status Schema", "examples": ["active"]}, "params": {"type": "array", "default": [], "title": "The params Schema", "items": {}, "examples": [[]]}, "ref_mask": {"type": "boolean", "default": false, "title": "The ref_mask Schema", "examples": [true]}, "conv_type": {"type": "string", "default": "", "title": "The conv_type Schema", "examples": ["process"]}, "scheme": {"type": "object", "default": {}, "title": "The scheme Schema", "required": ["nodes", "web_settings"], "properties": {"nodes": {"type": "array", "default": [], "title": "The nodes Schema", "items": {"type": "object", "title": "A Schema", "required": ["id", "obj_type", "condition", "title", "description", "x", "y", "uuid", "extra", "options"], "properties": {"id": {"type": "string", "title": "The id Schema", "examples": ["645b7105094bab040f000007", "645b7105094bab040f000008", "645b7105094bab040f000009"]}, "obj_type": {"type": "integer", "title": "The obj_type Schema", "examples": [1, 0, 2]}, "condition": {"type": "object", "title": "The condition Schema", "required": ["logics", "semaphors"], "properties": {"logics": {"type": "array", "title": "The logics Schema", "items": {"type": "object", "title": "A Schema", "required": ["to_node_id", "type"], "properties": {"to_node_id": {"type": "string", "title": "The to_node_id Schema", "examples": ["645b7105094bab040f000008", "645b7105094bab040f000009"]}, "type": {"type": "string", "title": "The type Schema", "examples": ["go"]}}, "examples": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}, {"to_node_id": "645b7105094bab040f000009", "type": "go"}]}, "examples": [[{"to_node_id": "645b7105094bab040f000008", "type": "go"}], [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], []]}, "semaphors": {"type": "array", "title": "The semaphors Schema", "items": {}, "examples": [[], [], []]}}, "examples": [{"logics": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}], "semaphors": []}, {"logics": [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], "semaphors": []}, {"logics": [], "semaphors": []}]}, "title": {"type": "string", "title": "The title <PERSON><PERSON>a", "examples": ["start", "process", "final"]}, "description": {"type": "string", "title": "The description Schema", "examples": [""]}, "x": {"type": "integer", "title": "The x Schema", "examples": [0]}, "y": {"type": "integer", "title": "The y Schema", "examples": [0]}, "uuid": {"type": "string", "title": "The uuid Schema", "examples": ["49f6888f-10b8-414b-9d03-8354424c260d", "58ef0169-0458-4eb6-8e7c-7fe190ec0dbb", "013cc84b-aeb4-4aee-b292-1f6707a1ada0"]}, "extra": {"type": "null", "title": "The extra Schema", "examples": [null]}, "options": {"type": "null", "title": "The options Schema", "examples": [null]}}, "examples": [{"id": "645b7105094bab040f000007", "obj_type": 1, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}], "semaphors": []}, "title": "start", "description": "", "x": 0, "y": 0, "uuid": "49f6888f-10b8-414b-9d03-8354424c260d", "extra": null, "options": null}, {"id": "645b7105094bab040f000008", "obj_type": 0, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], "semaphors": []}, "title": "process", "description": "", "x": 0, "y": 0, "uuid": "58ef0169-0458-4eb6-8e7c-7fe190ec0dbb", "extra": null, "options": null}, {"id": "645b7105094bab040f000009", "obj_type": 2, "condition": {"logics": [], "semaphors": []}, "title": "final", "description": "", "x": 0, "y": 0, "uuid": "013cc84b-aeb4-4aee-b292-1f6707a1ada0", "extra": null, "options": null}]}, "examples": [[{"id": "645b7105094bab040f000007", "obj_type": 1, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}], "semaphors": []}, "title": "start", "description": "", "x": 0, "y": 0, "uuid": "49f6888f-10b8-414b-9d03-8354424c260d", "extra": null, "options": null}, {"id": "645b7105094bab040f000008", "obj_type": 0, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], "semaphors": []}, "title": "process", "description": "", "x": 0, "y": 0, "uuid": "58ef0169-0458-4eb6-8e7c-7fe190ec0dbb", "extra": null, "options": null}, {"id": "645b7105094bab040f000009", "obj_type": 2, "condition": {"logics": [], "semaphors": []}, "title": "final", "description": "", "x": 0, "y": 0, "uuid": "013cc84b-aeb4-4aee-b292-1f6707a1ada0", "extra": null, "options": null}]]}, "web_settings": {"type": "array", "default": [], "title": "The web_settings Schema", "items": {"type": "array", "title": "A Schema", "items": {}, "examples": [[], []]}, "examples": [[[], []]]}}, "examples": [{"nodes": [{"id": "645b7105094bab040f000007", "obj_type": 1, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}], "semaphors": []}, "title": "start", "description": "", "x": 0, "y": 0, "uuid": "49f6888f-10b8-414b-9d03-8354424c260d", "extra": null, "options": null}, {"id": "645b7105094bab040f000008", "obj_type": 0, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], "semaphors": []}, "title": "process", "description": "", "x": 0, "y": 0, "uuid": "58ef0169-0458-4eb6-8e7c-7fe190ec0dbb", "extra": null, "options": null}, {"id": "645b7105094bab040f000009", "obj_type": 2, "condition": {"logics": [], "semaphors": []}, "title": "final", "description": "", "x": 0, "y": 0, "uuid": "013cc84b-aeb4-4aee-b292-1f6707a1ada0", "extra": null, "options": null}], "web_settings": [[], []]}]}, "uuid": {"type": "string", "title": "The uuid Schema", "examples": ["2c7317f3-9617-41a5-b144-6bf125afccd6", "39dae0dc-c236-45cf-af27-abff3791c247", "ca582679-097f-4d37-b139-a221eeab6cea"]}, "obj_to_title": {"type": "string", "default": "", "title": "The obj_to_title Schema", "examples": ["conv_1683714308979"]}, "short_name": {"type": "string", "default": "", "title": "The short_name <PERSON><PERSON><PERSON>", "examples": ["alias-1683714309181"]}, "obj_to_id": {"type": "integer", "default": 0, "title": "The obj_to_id Schema", "examples": [1184942]}, "obj_to_type": {"type": "string", "default": "", "title": "The obj_to_type Schema", "examples": ["conv"]}}, "examples": [{"obj_type": 1, "obj_id": 1184942, "parent_id": 459145, "title": "conv_1683714308979", "description": "", "status": "active", "params": [], "ref_mask": true, "conv_type": "process", "scheme": {"nodes": [{"id": "645b7105094bab040f000007", "obj_type": 1, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}], "semaphors": []}, "title": "start", "description": "", "x": 0, "y": 0, "uuid": "49f6888f-10b8-414b-9d03-8354424c260d", "extra": null, "options": null}, {"id": "645b7105094bab040f000008", "obj_type": 0, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], "semaphors": []}, "title": "process", "description": "", "x": 0, "y": 0, "uuid": "58ef0169-0458-4eb6-8e7c-7fe190ec0dbb", "extra": null, "options": null}, {"id": "645b7105094bab040f000009", "obj_type": 2, "condition": {"logics": [], "semaphors": []}, "title": "final", "description": "", "x": 0, "y": 0, "uuid": "013cc84b-aeb4-4aee-b292-1f6707a1ada0", "extra": null, "options": null}], "web_settings": [[], []]}, "uuid": "2c7317f3-9617-41a5-b144-6bf125afccd6"}, {"obj_type": 9, "parent_id": 459145, "obj_to_title": "conv_1683714308979", "obj_id": 70550, "title": "aliasTitle-1683714309181", "description": "aliasDesk-1683714309181", "short_name": "alias-1683714309181", "obj_to_id": 1184942, "obj_to_type": "conv", "uuid": "39dae0dc-c236-45cf-af27-abff3791c247"}, {"obj_type": 0, "obj_id": 459146, "parent_id": 459145, "title": "folder_1683714309091", "description": "", "uuid": "ca582679-097f-4d37-b139-a221eeab6cea"}]}, "description": {"type": "string", "title": "The description Schema", "examples": ["", "aliasDesk-1683714309181"]}, "status": {"type": "string", "default": "", "title": "The status Schema", "examples": ["active"]}, "params": {"type": "array", "default": [], "title": "The params Schema", "items": {}, "examples": [[]]}, "ref_mask": {"type": "boolean", "default": false, "title": "The ref_mask Schema", "examples": [true]}, "conv_type": {"type": "string", "default": "", "title": "The conv_type Schema", "examples": ["process"]}, "scheme": {"type": "object", "default": {}, "title": "The scheme Schema", "required": ["nodes", "web_settings"], "properties": {"nodes": {"type": "array", "default": [], "title": "The nodes Schema", "items": {"type": "object", "title": "A Schema", "required": ["id", "obj_type", "condition", "title", "description", "x", "y", "uuid", "extra", "options"], "properties": {"id": {"type": "string", "title": "The id Schema", "examples": ["645b7105094bab040f000007", "645b7105094bab040f000008", "645b7105094bab040f000009"]}, "obj_type": {"type": "integer", "title": "The obj_type Schema", "examples": [1, 0, 2]}, "condition": {"type": "object", "title": "The condition Schema", "required": ["logics", "semaphors"], "properties": {"logics": {"type": "array", "title": "The logics Schema", "items": {"type": "object", "title": "A Schema", "required": ["to_node_id", "type"], "properties": {"to_node_id": {"type": "string", "title": "The to_node_id Schema", "examples": ["645b7105094bab040f000008", "645b7105094bab040f000009"]}, "type": {"type": "string", "title": "The type Schema", "examples": ["go"]}}, "examples": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}, {"to_node_id": "645b7105094bab040f000009", "type": "go"}]}, "examples": [[{"to_node_id": "645b7105094bab040f000008", "type": "go"}], [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], []]}, "semaphors": {"type": "array", "title": "The semaphors Schema", "items": {}, "examples": [[], [], []]}}, "examples": [{"logics": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}], "semaphors": []}, {"logics": [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], "semaphors": []}, {"logics": [], "semaphors": []}]}, "title": {"type": "string", "title": "The title <PERSON><PERSON>a", "examples": ["start", "process", "final"]}, "description": {"type": "string", "title": "The description Schema", "examples": [""]}, "x": {"type": "integer", "title": "The x Schema", "examples": [0]}, "y": {"type": "integer", "title": "The y Schema", "examples": [0]}, "uuid": {"type": "string", "title": "The uuid Schema", "examples": ["49f6888f-10b8-414b-9d03-8354424c260d", "58ef0169-0458-4eb6-8e7c-7fe190ec0dbb", "013cc84b-aeb4-4aee-b292-1f6707a1ada0"]}, "extra": {"type": "null", "title": "The extra Schema", "examples": [null]}, "options": {"type": "null", "title": "The options Schema", "examples": [null]}}, "examples": [{"id": "645b7105094bab040f000007", "obj_type": 1, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}], "semaphors": []}, "title": "start", "description": "", "x": 0, "y": 0, "uuid": "49f6888f-10b8-414b-9d03-8354424c260d", "extra": null, "options": null}, {"id": "645b7105094bab040f000008", "obj_type": 0, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], "semaphors": []}, "title": "process", "description": "", "x": 0, "y": 0, "uuid": "58ef0169-0458-4eb6-8e7c-7fe190ec0dbb", "extra": null, "options": null}, {"id": "645b7105094bab040f000009", "obj_type": 2, "condition": {"logics": [], "semaphors": []}, "title": "final", "description": "", "x": 0, "y": 0, "uuid": "013cc84b-aeb4-4aee-b292-1f6707a1ada0", "extra": null, "options": null}]}, "examples": [[{"id": "645b7105094bab040f000007", "obj_type": 1, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}], "semaphors": []}, "title": "start", "description": "", "x": 0, "y": 0, "uuid": "49f6888f-10b8-414b-9d03-8354424c260d", "extra": null, "options": null}, {"id": "645b7105094bab040f000008", "obj_type": 0, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], "semaphors": []}, "title": "process", "description": "", "x": 0, "y": 0, "uuid": "58ef0169-0458-4eb6-8e7c-7fe190ec0dbb", "extra": null, "options": null}, {"id": "645b7105094bab040f000009", "obj_type": 2, "condition": {"logics": [], "semaphors": []}, "title": "final", "description": "", "x": 0, "y": 0, "uuid": "013cc84b-aeb4-4aee-b292-1f6707a1ada0", "extra": null, "options": null}]]}, "web_settings": {"type": "array", "default": [], "title": "The web_settings Schema", "items": {"type": "array", "title": "A Schema", "items": {}, "examples": [[], []]}, "examples": [[[], []]]}}, "examples": [{"nodes": [{"id": "645b7105094bab040f000007", "obj_type": 1, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}], "semaphors": []}, "title": "start", "description": "", "x": 0, "y": 0, "uuid": "49f6888f-10b8-414b-9d03-8354424c260d", "extra": null, "options": null}, {"id": "645b7105094bab040f000008", "obj_type": 0, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], "semaphors": []}, "title": "process", "description": "", "x": 0, "y": 0, "uuid": "58ef0169-0458-4eb6-8e7c-7fe190ec0dbb", "extra": null, "options": null}, {"id": "645b7105094bab040f000009", "obj_type": 2, "condition": {"logics": [], "semaphors": []}, "title": "final", "description": "", "x": 0, "y": 0, "uuid": "013cc84b-aeb4-4aee-b292-1f6707a1ada0", "extra": null, "options": null}], "web_settings": [[], []]}]}, "uuid": {"type": "string", "title": "The uuid Schema", "examples": ["2c7317f3-9617-41a5-b144-6bf125afccd6", "39dae0dc-c236-45cf-af27-abff3791c247", "ca582679-097f-4d37-b139-a221eeab6cea"]}, "obj_to_title": {"type": "string", "default": "", "title": "The obj_to_title Schema", "examples": ["conv_1683714308979"]}, "short_name": {"type": "string", "default": "", "title": "The short_name <PERSON><PERSON><PERSON>", "examples": ["alias-1683714309181"]}, "obj_to_id": {"type": "integer", "default": 0, "title": "The obj_to_id Schema", "examples": [1184942]}, "obj_to_type": {"type": "string", "default": "", "title": "The obj_to_type Schema", "examples": ["conv"]}}, "examples": [{"parent_id": 459144, "obj_id": 1184942, "title": "conv_1683714308979", "obj_type": "conv", "__added": {"obj_type": 1, "obj_id": 1184942, "parent_id": 459145, "title": "conv_1683714308979", "description": "", "status": "active", "params": [], "ref_mask": true, "conv_type": "process", "scheme": {"nodes": [{"id": "645b7105094bab040f000007", "obj_type": 1, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}], "semaphors": []}, "title": "start", "description": "", "x": 0, "y": 0, "uuid": "49f6888f-10b8-414b-9d03-8354424c260d", "extra": null, "options": null}, {"id": "645b7105094bab040f000008", "obj_type": 0, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], "semaphors": []}, "title": "process", "description": "", "x": 0, "y": 0, "uuid": "58ef0169-0458-4eb6-8e7c-7fe190ec0dbb", "extra": null, "options": null}, {"id": "645b7105094bab040f000009", "obj_type": 2, "condition": {"logics": [], "semaphors": []}, "title": "final", "description": "", "x": 0, "y": 0, "uuid": "013cc84b-aeb4-4aee-b292-1f6707a1ada0", "extra": null, "options": null}], "web_settings": [[], []]}, "uuid": "2c7317f3-9617-41a5-b144-6bf125afccd6"}, "description": "", "status": "active", "params": [], "ref_mask": true, "conv_type": "process", "scheme": {"nodes": [{"id": "645b7105094bab040f000007", "obj_type": 1, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}], "semaphors": []}, "title": "start", "description": "", "x": 0, "y": 0, "uuid": "49f6888f-10b8-414b-9d03-8354424c260d", "extra": null, "options": null}, {"id": "645b7105094bab040f000008", "obj_type": 0, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], "semaphors": []}, "title": "process", "description": "", "x": 0, "y": 0, "uuid": "58ef0169-0458-4eb6-8e7c-7fe190ec0dbb", "extra": null, "options": null}, {"id": "645b7105094bab040f000009", "obj_type": 2, "condition": {"logics": [], "semaphors": []}, "title": "final", "description": "", "x": 0, "y": 0, "uuid": "013cc84b-aeb4-4aee-b292-1f6707a1ada0", "extra": null, "options": null}], "web_settings": [[], []]}, "uuid": "2c7317f3-9617-41a5-b144-6bf125afccd6"}, {"parent_id": 459144, "obj_id": 70550, "title": "aliasTitle-1683714309181", "obj_type": "alias", "__added": {"obj_type": 9, "parent_id": 459145, "obj_to_title": "conv_1683714308979", "obj_id": 70550, "title": "aliasTitle-1683714309181", "description": "aliasDesk-1683714309181", "short_name": "alias-1683714309181", "obj_to_id": 1184942, "obj_to_type": "conv", "uuid": "39dae0dc-c236-45cf-af27-abff3791c247"}, "obj_to_title": "conv_1683714308979", "description": "aliasDesk-1683714309181", "short_name": "alias-1683714309181", "obj_to_id": 1184942, "obj_to_type": "conv", "uuid": "39dae0dc-c236-45cf-af27-abff3791c247"}, {"parent_id": 459144, "obj_id": 459146, "title": "folder_1683714309091", "obj_type": "folder", "__added": {"obj_type": 0, "obj_id": 459146, "parent_id": 459145, "title": "folder_1683714309091", "description": "", "uuid": "ca582679-097f-4d37-b139-a221eeab6cea"}, "description": "", "uuid": "ca582679-097f-4d37-b139-a221eeab6cea"}]}, "examples": [[{"parent_id": 459144, "obj_id": 1184942, "title": "conv_1683714308979", "obj_type": "conv", "__added": {"obj_type": 1, "obj_id": 1184942, "parent_id": 459145, "title": "conv_1683714308979", "description": "", "status": "active", "params": [], "ref_mask": true, "conv_type": "process", "scheme": {"nodes": [{"id": "645b7105094bab040f000007", "obj_type": 1, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}], "semaphors": []}, "title": "start", "description": "", "x": 0, "y": 0, "uuid": "49f6888f-10b8-414b-9d03-8354424c260d", "extra": null, "options": null}, {"id": "645b7105094bab040f000008", "obj_type": 0, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], "semaphors": []}, "title": "process", "description": "", "x": 0, "y": 0, "uuid": "58ef0169-0458-4eb6-8e7c-7fe190ec0dbb", "extra": null, "options": null}, {"id": "645b7105094bab040f000009", "obj_type": 2, "condition": {"logics": [], "semaphors": []}, "title": "final", "description": "", "x": 0, "y": 0, "uuid": "013cc84b-aeb4-4aee-b292-1f6707a1ada0", "extra": null, "options": null}], "web_settings": [[], []]}, "uuid": "2c7317f3-9617-41a5-b144-6bf125afccd6"}, "description": "", "status": "active", "params": [], "ref_mask": true, "conv_type": "process", "scheme": {"nodes": [{"id": "645b7105094bab040f000007", "obj_type": 1, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}], "semaphors": []}, "title": "start", "description": "", "x": 0, "y": 0, "uuid": "49f6888f-10b8-414b-9d03-8354424c260d", "extra": null, "options": null}, {"id": "645b7105094bab040f000008", "obj_type": 0, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], "semaphors": []}, "title": "process", "description": "", "x": 0, "y": 0, "uuid": "58ef0169-0458-4eb6-8e7c-7fe190ec0dbb", "extra": null, "options": null}, {"id": "645b7105094bab040f000009", "obj_type": 2, "condition": {"logics": [], "semaphors": []}, "title": "final", "description": "", "x": 0, "y": 0, "uuid": "013cc84b-aeb4-4aee-b292-1f6707a1ada0", "extra": null, "options": null}], "web_settings": [[], []]}, "uuid": "2c7317f3-9617-41a5-b144-6bf125afccd6"}, {"parent_id": 459144, "obj_id": 70550, "title": "aliasTitle-1683714309181", "obj_type": "alias", "__added": {"obj_type": 9, "parent_id": 459145, "obj_to_title": "conv_1683714308979", "obj_id": 70550, "title": "aliasTitle-1683714309181", "description": "aliasDesk-1683714309181", "short_name": "alias-1683714309181", "obj_to_id": 1184942, "obj_to_type": "conv", "uuid": "39dae0dc-c236-45cf-af27-abff3791c247"}, "obj_to_title": "conv_1683714308979", "description": "aliasDesk-1683714309181", "short_name": "alias-1683714309181", "obj_to_id": 1184942, "obj_to_type": "conv", "uuid": "39dae0dc-c236-45cf-af27-abff3791c247"}, {"parent_id": 459144, "obj_id": 459146, "title": "folder_1683714309091", "obj_type": "folder", "__added": {"obj_type": 0, "obj_id": 459146, "parent_id": 459145, "title": "folder_1683714309091", "description": "", "uuid": "ca582679-097f-4d37-b139-a221eeab6cea"}, "description": "", "uuid": "ca582679-097f-4d37-b139-a221eeab6cea"}]]}}, "examples": [{"proc": "ok", "obj": "obj_scheme", "list": [{"parent_id": 459144, "obj_id": 1184942, "title": "conv_1683714308979", "obj_type": "conv", "__added": {"obj_type": 1, "obj_id": 1184942, "parent_id": 459145, "title": "conv_1683714308979", "description": "", "status": "active", "params": [], "ref_mask": true, "conv_type": "process", "scheme": {"nodes": [{"id": "645b7105094bab040f000007", "obj_type": 1, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}], "semaphors": []}, "title": "start", "description": "", "x": 0, "y": 0, "uuid": "49f6888f-10b8-414b-9d03-8354424c260d", "extra": null, "options": null}, {"id": "645b7105094bab040f000008", "obj_type": 0, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], "semaphors": []}, "title": "process", "description": "", "x": 0, "y": 0, "uuid": "58ef0169-0458-4eb6-8e7c-7fe190ec0dbb", "extra": null, "options": null}, {"id": "645b7105094bab040f000009", "obj_type": 2, "condition": {"logics": [], "semaphors": []}, "title": "final", "description": "", "x": 0, "y": 0, "uuid": "013cc84b-aeb4-4aee-b292-1f6707a1ada0", "extra": null, "options": null}], "web_settings": [[], []]}, "uuid": "2c7317f3-9617-41a5-b144-6bf125afccd6"}, "description": "", "status": "active", "params": [], "ref_mask": true, "conv_type": "process", "scheme": {"nodes": [{"id": "645b7105094bab040f000007", "obj_type": 1, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}], "semaphors": []}, "title": "start", "description": "", "x": 0, "y": 0, "uuid": "49f6888f-10b8-414b-9d03-8354424c260d", "extra": null, "options": null}, {"id": "645b7105094bab040f000008", "obj_type": 0, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], "semaphors": []}, "title": "process", "description": "", "x": 0, "y": 0, "uuid": "58ef0169-0458-4eb6-8e7c-7fe190ec0dbb", "extra": null, "options": null}, {"id": "645b7105094bab040f000009", "obj_type": 2, "condition": {"logics": [], "semaphors": []}, "title": "final", "description": "", "x": 0, "y": 0, "uuid": "013cc84b-aeb4-4aee-b292-1f6707a1ada0", "extra": null, "options": null}], "web_settings": [[], []]}, "uuid": "2c7317f3-9617-41a5-b144-6bf125afccd6"}, {"parent_id": 459144, "obj_id": 70550, "title": "aliasTitle-1683714309181", "obj_type": "alias", "__added": {"obj_type": 9, "parent_id": 459145, "obj_to_title": "conv_1683714308979", "obj_id": 70550, "title": "aliasTitle-1683714309181", "description": "aliasDesk-1683714309181", "short_name": "alias-1683714309181", "obj_to_id": 1184942, "obj_to_type": "conv", "uuid": "39dae0dc-c236-45cf-af27-abff3791c247"}, "obj_to_title": "conv_1683714308979", "description": "aliasDesk-1683714309181", "short_name": "alias-1683714309181", "obj_to_id": 1184942, "obj_to_type": "conv", "uuid": "39dae0dc-c236-45cf-af27-abff3791c247"}, {"parent_id": 459144, "obj_id": 459146, "title": "folder_1683714309091", "obj_type": "folder", "__added": {"obj_type": 0, "obj_id": 459146, "parent_id": 459145, "title": "folder_1683714309091", "description": "", "uuid": "ca582679-097f-4d37-b139-a221eeab6cea"}, "description": "", "uuid": "ca582679-097f-4d37-b139-a221eeab6cea"}]}]}, "examples": [[{"proc": "ok", "obj": "obj_scheme", "list": [{"parent_id": 459144, "obj_id": 1184942, "title": "conv_1683714308979", "obj_type": "conv", "__added": {"obj_type": 1, "obj_id": 1184942, "parent_id": 459145, "title": "conv_1683714308979", "description": "", "status": "active", "params": [], "ref_mask": true, "conv_type": "process", "scheme": {"nodes": [{"id": "645b7105094bab040f000007", "obj_type": 1, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}], "semaphors": []}, "title": "start", "description": "", "x": 0, "y": 0, "uuid": "49f6888f-10b8-414b-9d03-8354424c260d", "extra": null, "options": null}, {"id": "645b7105094bab040f000008", "obj_type": 0, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], "semaphors": []}, "title": "process", "description": "", "x": 0, "y": 0, "uuid": "58ef0169-0458-4eb6-8e7c-7fe190ec0dbb", "extra": null, "options": null}, {"id": "645b7105094bab040f000009", "obj_type": 2, "condition": {"logics": [], "semaphors": []}, "title": "final", "description": "", "x": 0, "y": 0, "uuid": "013cc84b-aeb4-4aee-b292-1f6707a1ada0", "extra": null, "options": null}], "web_settings": [[], []]}, "uuid": "2c7317f3-9617-41a5-b144-6bf125afccd6"}, "description": "", "status": "active", "params": [], "ref_mask": true, "conv_type": "process", "scheme": {"nodes": [{"id": "645b7105094bab040f000007", "obj_type": 1, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}], "semaphors": []}, "title": "start", "description": "", "x": 0, "y": 0, "uuid": "49f6888f-10b8-414b-9d03-8354424c260d", "extra": null, "options": null}, {"id": "645b7105094bab040f000008", "obj_type": 0, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], "semaphors": []}, "title": "process", "description": "", "x": 0, "y": 0, "uuid": "58ef0169-0458-4eb6-8e7c-7fe190ec0dbb", "extra": null, "options": null}, {"id": "645b7105094bab040f000009", "obj_type": 2, "condition": {"logics": [], "semaphors": []}, "title": "final", "description": "", "x": 0, "y": 0, "uuid": "013cc84b-aeb4-4aee-b292-1f6707a1ada0", "extra": null, "options": null}], "web_settings": [[], []]}, "uuid": "2c7317f3-9617-41a5-b144-6bf125afccd6"}, {"parent_id": 459144, "obj_id": 70550, "title": "aliasTitle-1683714309181", "obj_type": "alias", "__added": {"obj_type": 9, "parent_id": 459145, "obj_to_title": "conv_1683714308979", "obj_id": 70550, "title": "aliasTitle-1683714309181", "description": "aliasDesk-1683714309181", "short_name": "alias-1683714309181", "obj_to_id": 1184942, "obj_to_type": "conv", "uuid": "39dae0dc-c236-45cf-af27-abff3791c247"}, "obj_to_title": "conv_1683714308979", "description": "aliasDesk-1683714309181", "short_name": "alias-1683714309181", "obj_to_id": 1184942, "obj_to_type": "conv", "uuid": "39dae0dc-c236-45cf-af27-abff3791c247"}, {"parent_id": 459144, "obj_id": 459146, "title": "folder_1683714309091", "obj_type": "folder", "__added": {"obj_type": 0, "obj_id": 459146, "parent_id": 459145, "title": "folder_1683714309091", "description": "", "uuid": "ca582679-097f-4d37-b139-a221eeab6cea"}, "description": "", "uuid": "ca582679-097f-4d37-b139-a221eeab6cea"}]}]]}}, "examples": [{"request_proc": "ok", "ops": [{"proc": "ok", "obj": "obj_scheme", "list": [{"parent_id": 459144, "obj_id": 1184942, "title": "conv_1683714308979", "obj_type": "conv", "__added": {"obj_type": 1, "obj_id": 1184942, "parent_id": 459145, "title": "conv_1683714308979", "description": "", "status": "active", "params": [], "ref_mask": true, "conv_type": "process", "scheme": {"nodes": [{"id": "645b7105094bab040f000007", "obj_type": 1, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}], "semaphors": []}, "title": "start", "description": "", "x": 0, "y": 0, "uuid": "49f6888f-10b8-414b-9d03-8354424c260d", "extra": null, "options": null}, {"id": "645b7105094bab040f000008", "obj_type": 0, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], "semaphors": []}, "title": "process", "description": "", "x": 0, "y": 0, "uuid": "58ef0169-0458-4eb6-8e7c-7fe190ec0dbb", "extra": null, "options": null}, {"id": "645b7105094bab040f000009", "obj_type": 2, "condition": {"logics": [], "semaphors": []}, "title": "final", "description": "", "x": 0, "y": 0, "uuid": "013cc84b-aeb4-4aee-b292-1f6707a1ada0", "extra": null, "options": null}], "web_settings": [[], []]}, "uuid": "2c7317f3-9617-41a5-b144-6bf125afccd6"}, "description": "", "status": "active", "params": [], "ref_mask": true, "conv_type": "process", "scheme": {"nodes": [{"id": "645b7105094bab040f000007", "obj_type": 1, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000008", "type": "go"}], "semaphors": []}, "title": "start", "description": "", "x": 0, "y": 0, "uuid": "49f6888f-10b8-414b-9d03-8354424c260d", "extra": null, "options": null}, {"id": "645b7105094bab040f000008", "obj_type": 0, "condition": {"logics": [{"to_node_id": "645b7105094bab040f000009", "type": "go"}], "semaphors": []}, "title": "process", "description": "", "x": 0, "y": 0, "uuid": "58ef0169-0458-4eb6-8e7c-7fe190ec0dbb", "extra": null, "options": null}, {"id": "645b7105094bab040f000009", "obj_type": 2, "condition": {"logics": [], "semaphors": []}, "title": "final", "description": "", "x": 0, "y": 0, "uuid": "013cc84b-aeb4-4aee-b292-1f6707a1ada0", "extra": null, "options": null}], "web_settings": [[], []]}, "uuid": "2c7317f3-9617-41a5-b144-6bf125afccd6"}, {"parent_id": 459144, "obj_id": 70550, "title": "aliasTitle-1683714309181", "obj_type": "alias", "__added": {"obj_type": 9, "parent_id": 459145, "obj_to_title": "conv_1683714308979", "obj_id": 70550, "title": "aliasTitle-1683714309181", "description": "aliasDesk-1683714309181", "short_name": "alias-1683714309181", "obj_to_id": 1184942, "obj_to_type": "conv", "uuid": "39dae0dc-c236-45cf-af27-abff3791c247"}, "obj_to_title": "conv_1683714308979", "description": "aliasDesk-1683714309181", "short_name": "alias-1683714309181", "obj_to_id": 1184942, "obj_to_type": "conv", "uuid": "39dae0dc-c236-45cf-af27-abff3791c247"}, {"parent_id": 459144, "obj_id": 459146, "title": "folder_1683714309091", "obj_type": "folder", "__added": {"obj_type": 0, "obj_id": 459146, "parent_id": 459145, "title": "folder_1683714309091", "description": "", "uuid": "ca582679-097f-4d37-b139-a221eeab6cea"}, "description": "", "uuid": "ca582679-097f-4d37-b139-a221eeab6cea"}]}]}]}