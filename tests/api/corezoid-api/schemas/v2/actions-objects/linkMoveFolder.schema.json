{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj_type", "obj_id", "obj_to_type", "obj_to_id", "obj_to_title", "action_type", "from_folder", "to_folder", "from_folder_name", "to_folder_name", "user_id", "user_name"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj_type": {"type": "string"}, "obj_id": {"type": "integer"}, "obj_to_type": {"type": "string"}, "obj_to_id": {"type": "integer"}, "obj_to_title": {"type": "string"}, "action_type": {"type": "string"}, "from_folder": {"type": "integer"}, "to_folder": {"type": "integer"}, "from_folder_name": {"type": "string"}, "to_folder_name": {"type": "string"}, "user_id": {"type": "integer"}, "user_name": {"type": "string"}}}, "uniqueItems": true}}}