{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "obj_id", "proc", "parent_folder_id", "parent_folder_title", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "integer"}, "proc": {"type": "string"}, "parent_folder_id": {"type": "integer"}, "parent_folder_title": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["is_owner", "owner_name", "owner_id", "owner_login", "obj_id", "obj_type", "create_time", "change_time", "title", "description", "project_id", "project_title", "stage_id", "stage_title"], "additionalProperties": false, "properties": {"is_owner": {"type": "boolean"}, "owner_name": {"type": "string"}, "owner_id": {"type": "integer"}, "owner_login": {"type": "string"}, "obj_id": {"type": "integer"}, "obj_type": {"type": "string"}, "create_time": {"type": "integer"}, "change_time": {"type": "integer"}, "title": {"type": "string"}, "description": {"type": "string"}, "project_id": {"type": "integer"}, "project_title": {"type": "string"}, "stage_id": {"type": "integer"}, "stage_title": {"type": "string"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}