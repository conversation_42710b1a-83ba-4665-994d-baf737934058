{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "proc", "scheme"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "proc": {"type": "string"}, "scheme": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj_type", "obj_id", "parent_id", "title", "description", "uuid"], "additionalProperties": false, "properties": {"obj_type": {"type": "integer"}, "obj_id": {"type": "integer"}, "parent_id": {"type": "integer"}, "title": {"type": "string"}, "description": {"type": "string"}, "status": {"type": "string"}, "params": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": true}, "ref_mask": {"type": "boolean"}, "conv_type": {"type": "string"}, "scheme": {"type": "object", "required": ["nodes", "web_settings"], "additionalProperties": false, "properties": {"nodes": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": true}, "web_settings": {"type": "array", "additionalItems": true, "items": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": true}}}}, "uuid": {"type": "string"}, "lists": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": true}, "charts_order": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": true}, "grid": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": true}, "time_range": {"type": "object", "required": ["timezone_offset", "select"], "additionalProperties": false, "properties": {"timezone_offset": {"type": "integer"}, "select": {"type": "string"}}}, "charts_list": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": true}}}, "uniqueItems": true}}}, "uniqueItems": true}}}