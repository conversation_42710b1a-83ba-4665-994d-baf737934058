{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "create_time", "user_id", "user_name", "login", "company_id", "obj", "type", "obj_type", "obj_id", "obj_name", "obj_to", "obj_to_id", "obj_to_name", "request", "response"], "additionalProperties": false, "properties": {"id": {"type": "integer"}, "create_time": {"type": "integer"}, "user_id": {"type": "integer"}, "user_name": {"type": ["string", "null"]}, "login": {"type": ["string", "null"]}, "company_id": {"type": "string"}, "obj": {"type": "string"}, "type": {"type": "string"}, "obj_type": {"type": ["string", "null"]}, "obj_id": {"type": ["integer", "null"]}, "obj_name": {"type": ["string", "null"]}, "obj_to": {"type": ["string", "null"]}, "obj_to_id": {"type": ["integer", "null"]}, "obj_to_name": {"type": ["string", "null"]}, "request": {"type": "string"}, "response": {"type": "string"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}