{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "const": "ok"}, "ops": {"$id": "#/properties/ops", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "required": ["id", "proc", "obj", "conv_id", "data"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string"}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "const": "ok"}, "obj": {"$id": "#/properties/ops/items/anyOf/0/properties/obj", "type": "string", "const": "sys_stat"}, "conv_id": {"$id": "#/properties/ops/items/anyOf/0/properties/conv_id", "type": "string", "const": "undefined"}, "data": {"$id": "#/properties/ops/items/anyOf/0/properties/data", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/data/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/data/items/anyOf/0", "type": "object", "required": ["conv_id", "conv_name", "owner_id", "owner_name", "date", "opers", "tacts", "company_id", "folder_id", "traff"], "properties": {"conv_id": {"$id": "#/properties/ops/items/anyOf/0/properties/data/items/anyOf/0/properties/conv_id", "type": "integer"}, "conv_name": {"$id": "#/properties/ops/items/anyOf/0/properties/data/items/anyOf/0/properties/conv_name", "type": "string"}, "owner_id": {"$id": "#/properties/ops/items/anyOf/0/properties/data/items/anyOf/0/properties/owner_id", "type": "integer"}, "owner_name": {"$id": "#/properties/ops/items/anyOf/0/properties/data/items/anyOf/0/properties/owner_name", "type": "string"}, "date": {"$id": "#/properties/ops/items/anyOf/0/properties/data/items/anyOf/0/properties/date", "type": "string"}, "opers": {"$id": "#/properties/ops/items/anyOf/0/properties/data/items/anyOf/0/properties/opers", "type": "integer"}, "tacts": {"$id": "#/properties/ops/items/anyOf/0/properties/data/items/anyOf/0/properties/tacts", "type": "integer"}, "company_id": {"$id": "#/properties/ops/items/anyOf/0/properties/data/items/anyOf/0/properties/company_id", "type": ["string", "null"]}, "folder_id": {"$id": "#/properties/ops/items/anyOf/0/properties/data/items/anyOf/0/properties/folder_id", "type": "integer"}, "traff": {"$id": "#/properties/ops/items/anyOf/0/properties/data/items/anyOf/0/properties/traff", "type": "number"}}, "additionalProperties": false}]}}}, "additionalProperties": false}]}}}, "additionalProperties": false}