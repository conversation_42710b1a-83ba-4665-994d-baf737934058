{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "conv_id", "data"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "conv_id": {"type": "integer"}, "data": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["conv_id", "conv_name", "owner_id", "owner_name", "date", "opers", "tacts", "folder_id", "traff"], "additionalProperties": false, "properties": {"conv_id": {"type": "integer"}, "conv_name": {"type": "string"}, "owner_id": {"type": "integer"}, "owner_name": {"type": "string"}, "date": {"type": "string"}, "opers": {"type": "integer"}, "tacts": {"type": "integer"}, "folder_id": {"type": "integer"}, "traff": {"type": "integer"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}