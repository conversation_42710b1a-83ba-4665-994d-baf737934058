{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "const": "ok"}, "ops": {"$id": "#/properties/ops", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "required": ["id", "proc", "list"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string"}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "const": "ok"}, "list": {"$id": "#/properties/ops/items/anyOf/0/properties/list", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0", "type": "object", "required": ["opers", "date"], "properties": {"opers": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/opers", "type": "integer"}, "date": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/date", "type": "string"}}, "additionalProperties": false}]}}}, "additionalProperties": false}]}}}, "additionalProperties": false}