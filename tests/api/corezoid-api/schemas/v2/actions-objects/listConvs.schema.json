{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj_id", "obj_type", "create_time", "change_time", "status", "is_owner", "owner_name", "owner_id", "title", "description", "escalation_conv", "conv_type", "project_id", "stage_id", "project_title", "stage_title"], "additionalProperties": false, "properties": {"obj_id": {"type": "integer"}, "obj_type": {"type": "string"}, "create_time": {"type": "integer"}, "change_time": {"type": "integer"}, "status": {"type": "string"}, "is_owner": {"type": "boolean"}, "owner_name": {"type": "string"}, "owner_id": {"type": "integer"}, "title": {"type": "string"}, "description": {"type": "string"}, "escalation_conv": {"type": "string"}, "conv_type": {"type": "string"}, "project_id": {"type": "integer"}, "stage_id": {"type": "integer"}, "project_title": {"type": ["null", "string"]}, "stage_title": {"type": ["null", "string"]}}}, "uniqueItems": true}}}, "uniqueItems": true}}}