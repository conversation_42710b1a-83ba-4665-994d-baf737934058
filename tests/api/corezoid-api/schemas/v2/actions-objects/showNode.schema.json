{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "obj_id", "title", "description", "logics", "semaphors", "count", "obj_type"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "string"}, "title": {"type": "string"}, "description": {}, "logics": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": true}, "semaphors": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": true}, "count": {"type": "integer"}, "obj_type": {"type": "integer"}}}, "uniqueItems": true}}}