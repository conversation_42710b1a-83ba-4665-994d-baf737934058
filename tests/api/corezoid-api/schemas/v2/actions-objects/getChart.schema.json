{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "name", "description", "obj_type", "series", "obj_id", "dashboard_id", "company_id", "privs"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "obj_type": {"type": "string"}, "series": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": [], "additionalProperties": false, "properties": {"title": {"type": "string"}, "conv_id": {"type": "integer"}, "node_id": {"type": "string"}, "type_icon": {"type": "string"}, "type_title": {"type": "string"}, "type": {"type": "string"}}}, "uniqueItems": true}, "obj_id": {"type": "string"}, "dashboard_id": {"type": "integer"}, "company_id": {"type": ["string", "null"]}, "privs": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["type", "list_obj"], "additionalProperties": false, "properties": {"type": {"type": "string"}, "list_obj": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}}}, "uniqueItems": true}}}, "uniqueItems": true}}}