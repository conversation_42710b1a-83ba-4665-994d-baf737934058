{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "obj_id", "title", "description", "create_time", "change_time", "time_range", "favorite", "privs", "company_id", "chart_list", "folder_id", "folder_name", "owner_id", "owner_login", "owner_name", "immutable", "parent_obj_id", "parent_obj_type"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "integer"}, "title": {"type": "string"}, "description": {"type": "string"}, "create_time": {"type": "integer"}, "change_time": {"type": "integer"}, "time_range": {"type": "object", "required": ["timezone_offset", "select"], "additionalProperties": false, "properties": {"timezone_offset": {"type": "integer"}, "select": {"type": "string"}, "start": {"type": "integer"}, "stop": {"type": "integer"}}}, "favorite": {"type": "boolean"}, "privs": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["type", "list_obj"], "additionalProperties": false, "properties": {"type": {"type": "string"}, "list_obj": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}}}, "uniqueItems": true}, "company_id": {"type": ["string", "null"]}, "chart_list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["name", "obj_id", "type"], "additionalProperties": false, "properties": {"name": {"type": "string"}, "obj_id": {"type": "string"}, "type": {"type": "string"}}}, "uniqueItems": true}, "folder_id": {"type": "integer"}, "folder_name": {"type": "string"}, "owner_id": {"type": "integer"}, "owner_login": {"type": "string"}, "owner_name": {"type": "string"}, "immutable": {"type": "boolean"}, "grid": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": [], "additionalProperties": false, "properties": {"obj_id": {"type": "string"}, "x": {"type": "integer"}, "y": {"type": "integer"}, "width": {"type": "integer"}, "height": {"type": "integer"}}}, "uniqueItems": true}, "parent_obj_id": {"type": "integer"}, "parent_obj_type": {"type": "string"}}}, "uniqueItems": true}}}