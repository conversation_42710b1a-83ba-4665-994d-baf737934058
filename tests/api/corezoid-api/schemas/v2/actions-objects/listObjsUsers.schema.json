{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "proc", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "proc": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj_type", "obj_id", "create_time", "status", "type", "owner_id", "logins", "companies", "title"], "additionalProperties": false, "properties": {"obj_type": {"type": "string"}, "obj_id": {"type": "integer"}, "create_time": {"type": "string"}, "status": {"type": "integer"}, "type": {"type": "string"}, "owner_id": {"type": "integer"}, "logins": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["login", "obj_id", "type"], "additionalProperties": false, "properties": {"login": {"type": "string"}, "obj_id": {"type": "integer"}, "type": {"type": "string"}}}, "uniqueItems": true}, "companies": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}, "title": {"type": "string"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}