{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "const": "ok"}, "ops": {"$id": "#/properties/ops", "type": "array", "uniqueItems": true, "additionalItems": false, "items": {"$id": "#/properties/ops/items", "type": "object", "required": ["id", "proc", "obj", "obj_id", "folder_id", "hash"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string", "const": ""}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "const": "ok"}, "obj": {"$id": "#/properties/ops/items/anyOf/0/properties/obj", "type": "string", "const": "conv"}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/obj_id", "type": "integer"}, "folder_id": {"$id": "#/properties/ops/items/anyOf/0/properties/folder_id", "type": "integer"}, "hash": {"$id": "#/properties/ops/items/anyOf/0/properties/hash", "type": "string"}}, "additionalProperties": false}}}, "additionalProperties": false}