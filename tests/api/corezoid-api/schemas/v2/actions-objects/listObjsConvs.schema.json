{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "proc", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "proc": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj_type", "obj_id", "folder_id", "create_time", "change_time", "status", "conv_type", "is_owner", "owner_name", "owner_login", "owner_id", "title", "description", "company_id"], "additionalProperties": false, "properties": {"obj_type": {"type": "string"}, "obj_id": {"type": "integer"}, "folder_id": {"type": "integer"}, "create_time": {"type": "integer"}, "change_time": {"type": "integer"}, "status": {"type": "integer"}, "conv_type": {"type": "string"}, "is_owner": {"type": "boolean"}, "owner_name": {"type": "string"}, "owner_login": {"type": "string"}, "owner_id": {"type": "integer"}, "title": {"type": "string"}, "description": {"type": "string"}, "company_id": {"type": ["string", "null"]}}}, "uniqueItems": true}}}, "uniqueItems": true}}}