{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "obj_id", "proc", "parent_folder_id", "parent_folder_title", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "integer"}, "proc": {"type": "string"}, "parent_folder_id": {"type": "integer"}, "parent_folder_title": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["is_shared", "favorite", "privs", "size", "is_owner", "owner_id", "owner_name", "owner_login", "obj_id", "obj_type", "create_time", "change_time", "title", "description", "company_id", "project_id", "stage_id", "owner_group_id", "parent_id"], "additionalProperties": false, "properties": {"is_shared": {"type": "boolean"}, "favorite": {"type": "boolean"}, "privs": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["type", "list_obj"], "additionalProperties": false, "properties": {"type": {"type": "string"}, "list_obj": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}}}, "uniqueItems": true}, "size": {"type": "integer"}, "is_owner": {"type": "boolean"}, "owner_id": {"type": "integer"}, "owner_name": {"type": "string"}, "owner_login": {"type": "string"}, "obj_id": {"type": "integer"}, "obj_type": {"type": "string"}, "instance_type": {"type": "string"}, "create_time": {"type": "integer"}, "change_time": {"type": "integer"}, "title": {"type": "string"}, "description": {"type": "string"}, "company_id": {"type": ["string", "null"]}, "project_id": {"type": "integer"}, "stage_id": {"type": "integer"}, "owner_group_id": {"type": "integer"}, "version": {"type": "null"}, "parent_id": {"type": "integer"}, "undeployed": {"type": "integer"}, "childs": {"type": "integer"}, "status": {"type": "string"}, "conv_type": {"type": "string"}, "is_deployed": {"type": "boolean"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}