{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "const": "ok"}, "ops": {"$id": "#/properties/ops", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "required": ["id", "obj", "obj_id", "project_id", "stage_id", "immutable", "privs", "status", "title", "description", "conv_type", "is_owner", "owner_name", "owner_id", "owner_login", "proc", "list", "escalation_conv", "shared", "folder_id", "folder_name", "change_time", "create_time", "tacts", "favorite", "company_id", "commits", "size"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string"}, "obj": {"$id": "#/properties/ops/items/anyOf/0/properties/obj", "type": "string", "const": "conv"}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/obj_id", "type": "integer"}, "project_id": {"$id": "#/properties/ops/items/anyOf/0/properties/project_id", "type": "integer"}, "stage_id": {"$id": "#/properties/ops/items/anyOf/0/properties/stage_id", "type": "integer"}, "immutable": {"$id": "#/properties/ops/items/anyOf/0/properties/immutable", "type": "boolean"}, "privs": {"$id": "#/properties/ops/items/anyOf/0/properties/privs", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/privs/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/privs/items/anyOf/0", "type": "object", "required": ["type", "list_obj"], "properties": {"type": {"$id": "#/properties/ops/items/anyOf/0/properties/privs/items/anyOf/0/properties/type", "type": "string"}, "list_obj": {"$id": "#/properties/ops/items/anyOf/0/properties/privs/items/anyOf/0/properties/list_obj", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/privs/items/anyOf/0/properties/list_obj/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/privs/items/anyOf/0/properties/list_obj/items/anyOf/0", "type": "string"}]}}}, "additionalProperties": false}]}}, "status": {"$id": "#/properties/ops/items/anyOf/0/properties/status", "type": "string"}, "title": {"$id": "#/properties/ops/items/anyOf/0/properties/title", "type": "string"}, "description": {"$id": "#/properties/ops/items/anyOf/0/properties/description", "type": "string"}, "conv_type": {"$id": "#/properties/ops/items/anyOf/0/properties/conv_type", "type": "string"}, "is_owner": {"$id": "#/properties/ops/items/anyOf/0/properties/is_owner", "type": "boolean"}, "owner_name": {"$id": "#/properties/ops/items/anyOf/0/properties/owner_name", "type": "string"}, "owner_id": {"$id": "#/properties/ops/items/anyOf/0/properties/owner_id", "type": "integer"}, "owner_login": {"$id": "#/properties/ops/items/anyOf/0/properties/owner_login", "type": "string"}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "const": "ok"}, "list": {"$id": "#/properties/ops/items/anyOf/0/properties/list", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items"}}, "escalation_conv": {"$id": "#/properties/ops/items/anyOf/0/properties/escalation_conv", "type": "string"}, "shared": {"$id": "#/properties/ops/items/anyOf/0/properties/shared", "type": "boolean"}, "folder_id": {"$id": "#/properties/ops/items/anyOf/0/properties/folder_id", "type": "integer"}, "folder_name": {"$id": "#/properties/ops/items/anyOf/0/properties/folder_name", "type": "string"}, "change_time": {"$id": "#/properties/ops/items/anyOf/0/properties/change_time", "type": "integer"}, "create_time": {"$id": "#/properties/ops/items/anyOf/0/properties/create_time", "type": "integer"}, "tacts": {"$id": "#/properties/ops/items/anyOf/0/properties/tacts", "type": "integer"}, "favorite": {"$id": "#/properties/ops/items/anyOf/0/properties/favorite", "type": "boolean"}, "company_id": {"$id": "#/properties/ops/items/anyOf/0/properties/company_id", "type": "string"}, "commits": {"$id": "#/properties/ops/items/anyOf/0/properties/commits", "type": "object", "required": [], "additionalProperties": false}, "size": {"$id": "#/properties/ops/items/anyOf/0/properties/size", "type": "integer"}}, "additionalProperties": false}]}}}, "additionalProperties": false}