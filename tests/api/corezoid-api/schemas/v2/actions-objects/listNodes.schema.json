{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["conv_id", "description", "id", "title", "type", "status"], "additionalProperties": false, "properties": {"conv_id": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "string"}, "title": {"type": "string"}, "type": {"type": "integer"}, "status": {"type": "integer"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}