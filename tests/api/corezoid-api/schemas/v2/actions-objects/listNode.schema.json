{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "obj_id", "count", "proc", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "string"}, "count": {"type": "integer"}, "proc": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": true}}}, "uniqueItems": true}}}