{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "obj_id", "list_obj", "proc", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "integer"}, "list_obj": {"type": "string"}, "proc": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj_id", "obj", "title", "privs"], "additionalProperties": false, "properties": {"obj_id": {"type": "integer"}, "obj": {"type": "string"}, "title": {"type": "string"}, "obj_type": {"type": "string"}, "obj_status": {"type": "string"}, "privs": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["type", "list_obj"], "additionalProperties": false, "properties": {"type": {"type": "string"}, "list_obj": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}}}, "uniqueItems": true}, "login_id": {"type": "integer"}, "login": {"type": "string"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}