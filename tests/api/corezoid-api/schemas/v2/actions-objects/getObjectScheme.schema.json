{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "project_id", "stage_id", "company_id", "owner_id", "parent_id", "owner_title", "privs"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "project_id": {"type": "integer"}, "stage_id": {"type": "integer"}, "company_id": {"type": "string"}, "owner_id": {"type": "integer"}, "parent_id": {"type": "integer"}, "owner_title": {"type": "string"}, "privs": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["type", "list_obj"], "additionalProperties": false, "properties": {"type": {"type": "string"}, "list_obj": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}}}, "uniqueItems": true}}}, "uniqueItems": true}}}