{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "obj_id", "project_id", "stage_id", "immutable", "privs", "status", "title", "description", "conv_type", "is_owner", "owner_name", "owner_id", "owner_login", "proc", "list", "escalation_conv", "shared", "folder_id", "folder_name", "change_time", "create_time", "tacts", "favorite", "commits", "size"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "integer"}, "project_id": {"type": "integer"}, "stage_id": {"type": "integer"}, "immutable": {"type": "boolean"}, "privs": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["type", "list_obj"], "additionalProperties": false, "properties": {"type": {"type": "string"}, "list_obj": {"type": "array", "additionalItems": true, "items": {"type": "string"}, "uniqueItems": true}}}, "uniqueItems": true}, "status": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "conv_type": {"type": "string"}, "is_owner": {"type": "boolean"}, "owner_name": {"type": "string"}, "owner_id": {"type": "integer"}, "owner_login": {"type": "string"}, "proc": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": [], "additionalProperties": false, "properties": {"obj": {"type": "string"}, "obj_id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "logics": {"type": "array", "additionalItems": false, "items": {"type": "object", "required": [], "additionalProperties": false, "properties": {"to_node_id": {"type": "string"}, "type": {"type": "string"}, "obj_id_path": {"type": "string"}, "format": {"type": "string"}, "content_type": {"type": "string"}, "method": {"type": "string"}, "url": {"type": "string"}, "extra": {"type": "object", "required": [], "additionalProperties": false, "properties": {"ukrainian": {"type": "string"}, "cityId": {"type": "string"}, "keyWords": {"type": "string"}}}, "extra_type": {"type": "object", "required": [], "additionalProperties": false, "properties": {"ukrainian": {"type": "string"}, "cityId": {"type": "string"}, "keyWords": {"type": "string"}}}, "extra_headers": {"type": "object", "required": [], "additionalProperties": false, "properties": {"x-api-key": {"type": "string"}}}, "cert_pem": {"type": "string"}, "max_threads": {"type": "integer"}, "send_sys": {"type": "boolean"}, "err_node_id": {"type": "string"}, "is_migrate": {"type": "boolean"}, "debug_info": {"type": "boolean"}}}, "uniqueItems": true}, "semaphors": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": true}, "count": {"type": "integer"}, "obj_type": {"type": "integer"}, "position": {"type": ["null", "array"], "additionalItems": true, "items": {"type": "integer"}, "uniqueItems": true}, "extra": {"type": "null"}, "options": {"type": "null"}}}, "uniqueItems": true}, "escalation_conv": {"type": "string"}, "shared": {"type": "boolean"}, "folder_id": {"type": "integer"}, "folder_name": {"type": "string"}, "change_time": {"type": "integer"}, "create_time": {"type": "integer"}, "tacts": {"type": "integer"}, "favorite": {"type": "boolean"}, "company_id": {"type": ["string", "null"]}, "commits": {"type": "object", "required": [], "additionalProperties": false, "properties": {}}, "size": {"type": "integer"}, "blocked_reason": {"type": "string"}, "last_confirmed_version": {"type": "integer"}}}, "uniqueItems": true}}}