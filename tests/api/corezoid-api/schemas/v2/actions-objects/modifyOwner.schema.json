{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "owner_id", "owner_login", "owner_name"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "owner_id": {"type": "integer"}, "owner_login": {"type": "string"}, "owner_name": {"type": "string"}}}, "uniqueItems": true}}}