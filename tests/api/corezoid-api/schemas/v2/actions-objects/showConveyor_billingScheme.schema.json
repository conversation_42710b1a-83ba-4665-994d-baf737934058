{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "data", "start", "end"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "data": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["conv_id", "project_id", "stage_id", "company_id", "conv_name", "owner_account_id", "owner_id", "owner_type", "opers", "tacts", "traff", "timestamp"], "additionalProperties": false, "properties": {"conv_id": {"type": "integer"}, "project_id": {"type": ["null", "integer"]}, "stage_id": {"type": ["null", "integer"]}, "company_id": {"type": ["null", "string"]}, "conv_name": {"type": "string"}, "owner_account_id": {"type": "integer"}, "owner_id": {"type": "integer"}, "owner_type": {"type": "string"}, "opers": {"type": "integer"}, "tacts": {"type": "integer"}, "traff": {"type": "integer"}, "timestamp": {"type": "integer"}}}, "uniqueItems": true}, "start": {"type": "integer"}, "end": {"type": "integer"}}}, "uniqueItems": true}}}