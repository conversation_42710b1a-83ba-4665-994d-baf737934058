{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "request_proc", "ops"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["type", "obj", "obj_id", "proc", "list"], "additionalProperties": false, "properties": {"type": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "integer"}, "proc": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj", "count"], "additionalProperties": false, "properties": {"obj": {"type": "string"}, "count": {"type": "integer"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}, "uniqueItems": true}}}