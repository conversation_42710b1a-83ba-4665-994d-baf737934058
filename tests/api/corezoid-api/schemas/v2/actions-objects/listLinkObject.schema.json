{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "const": "ok"}, "ops": {"$id": "#/properties/ops", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "required": ["id", "obj", "obj_id", "list_obj", "proc", "list"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string"}, "obj": {"$id": "#/properties/ops/items/anyOf/0/properties/obj", "type": "string"}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/obj_id", "type": "integer"}, "list_obj": {"$id": "#/properties/ops/items/anyOf/0/properties/list_obj", "type": "string", "const": "group"}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "const": "ok"}, "list": {"$id": "#/properties/ops/items/anyOf/0/properties/list", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0", "type": "object", "required": ["obj", "obj_id", "login_id", "title", "privs", "login"], "properties": {"obj": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/obj", "type": "string", "const": "user"}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/obj_id", "type": "integer"}, "login_id": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/login_id", "type": "integer"}, "title": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/title", "type": "string"}, "privs": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/privs", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/privs/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/privs/items/anyOf/0", "type": "object", "required": ["type", "list_obj"], "properties": {"type": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/privs/items/anyOf/0/properties/type", "type": "string"}, "list_obj": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/privs/items/anyOf/0/properties/list_obj", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/privs/items/anyOf/0/properties/list_obj/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/privs/items/anyOf/0/properties/list_obj/items/anyOf/0", "type": "string"}]}}}, "additionalProperties": false}]}}, "login": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/0/properties/login", "type": "string"}}, "additionalProperties": false}, {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1", "type": "object", "required": ["obj", "title", "privs", "is_invite", "invite_hash"], "properties": {"obj": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/obj", "type": "string", "const": "user"}, "title": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/title", "type": "string"}, "privs": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/privs", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/privs/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/privs/items/anyOf/0", "type": "object", "required": ["type", "list_obj"], "properties": {"type": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/privs/items/anyOf/0/properties/type", "type": "string"}, "list_obj": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/privs/items/anyOf/0/properties/list_obj", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/privs/items/anyOf/0/properties/list_obj/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/privs/items/anyOf/0/properties/list_obj/items/anyOf/0", "type": "string", "const": "all"}]}}}, "additionalProperties": false}]}}, "is_invite": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/is_invite", "type": "boolean"}, "invite_hash": {"$id": "#/properties/ops/items/anyOf/0/properties/list/items/anyOf/1/properties/invite_hash", "type": "string"}}, "additionalProperties": false}]}}}, "additionalProperties": false}]}}}, "additionalProperties": false}