{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "default": {}, "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "default": "", "const": "ok"}, "ops": {"$id": "#/properties/ops", "type": "array", "default": [], "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "default": {}, "required": ["id", "proc", "type", "obj", "dashboard_id", "obj_id"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string", "default": ""}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "default": "", "const": "ok"}, "type": {"$id": "#/properties/ops/items/anyOf/0/properties/type", "type": "string", "default": "", "const": "create"}, "obj": {"$id": "#/properties/ops/items/anyOf/0/properties/obj", "type": "string", "default": "", "const": "chart"}, "dashboard_id": {"$id": "#/properties/ops/items/anyOf/0/properties/dashboard_id", "type": "integer", "default": 0}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/obj_id", "type": "string", "default": ""}}, "additionalProperties": false}]}}}, "additionalProperties": false}