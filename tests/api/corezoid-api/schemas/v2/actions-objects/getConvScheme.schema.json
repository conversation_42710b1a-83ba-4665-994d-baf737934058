{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj", "proc", "scheme"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj": {"type": "string"}, "proc": {"type": "string"}, "scheme": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj_type", "obj_id", "parent_id", "title", "description", "status", "params", "ref_mask", "conv_type", "scheme", "uuid"], "additionalProperties": false, "properties": {"obj_type": {"type": "integer"}, "obj_id": {"type": "integer"}, "parent_id": {"type": "integer"}, "title": {"type": "string"}, "description": {"type": "string"}, "status": {"type": "string"}, "params": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": true}, "ref_mask": {"type": "boolean"}, "conv_type": {"type": "string"}, "scheme": {"type": "object", "required": ["nodes", "web_settings"], "additionalProperties": false, "properties": {"nodes": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "obj_type", "condition", "title", "description", "x", "y", "uuid", "extra", "options"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "obj_type": {"type": "integer"}, "condition": {"type": "object", "required": ["logics", "semaphors"], "additionalProperties": false, "properties": {"logics": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["to_node_id", "type"], "additionalProperties": false, "properties": {"to_node_id": {"type": "string"}, "type": {"type": "string"}}}, "uniqueItems": true}, "semaphors": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["type", "value", "dimension", "to_node_id"], "additionalProperties": false, "properties": {"type": {"type": "string"}, "value": {"type": "integer"}, "dimension": {"type": "string"}, "to_node_id": {"type": "string"}}}, "uniqueItems": true}}}, "title": {"type": "string"}, "description": {"type": "string"}, "x": {"type": "integer"}, "y": {"type": "integer"}, "uuid": {"type": "string"}, "extra": {"type": "null"}, "options": {"type": "null"}}}, "uniqueItems": true}, "web_settings": {"type": "array", "additionalItems": true, "items": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": true}}}}, "uuid": {"type": "string"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}