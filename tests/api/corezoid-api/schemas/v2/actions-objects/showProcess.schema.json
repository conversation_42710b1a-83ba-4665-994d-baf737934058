{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "obj_id", "title", "status", "size", "owner_name", "owner_login", "create_time", "change_time", "company_id", "project_id", "stage_id", "project_title", "stage_title", "project_short_name", "stage_short_name", "immutable", "privs", "parent_obj_id", "parent_obj_type"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "obj_id": {"type": "integer"}, "title": {"type": "string"}, "status": {"type": "string"}, "size": {"type": "integer"}, "owner_name": {"type": "string"}, "owner_login": {"type": "string"}, "create_time": {"type": "integer"}, "change_time": {"type": "integer"}, "company_id": {"type": ["string", "null"]}, "project_id": {"type": "integer"}, "stage_id": {"type": "integer"}, "project_title": {"type": ["string", "null"]}, "stage_title": {"type": ["string", "null"]}, "project_short_name": {"type": ["string", "null"]}, "stage_short_name": {"type": ["string", "null"]}, "immutable": {"type": "boolean"}, "privs": {"type": "array", "additionalItems": true, "items": {}, "uniqueItems": true}, "parent_obj_id": {"type": "integer"}, "parent_obj_type": {"type": "string"}}}, "uniqueItems": true}}}