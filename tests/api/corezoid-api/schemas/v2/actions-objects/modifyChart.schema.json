{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "default": {}, "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "default": "", "const": "ok"}, "ops": {"$id": "#/properties/ops", "type": "array", "default": [], "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "default": {}, "required": ["id", "proc", "obj", "chart_id", "obj_id"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string", "default": "", "const": ""}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "default": "", "const": "ok"}, "obj": {"$id": "#/properties/ops/items/anyOf/0/properties/obj", "type": "string", "default": "", "const": "chart"}, "chart_id": {"$id": "#/properties/ops/items/anyOf/0/properties/chart_id", "type": "string", "default": ""}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/obj_id", "type": "integer", "default": 0}}, "additionalProperties": false}]}}}, "additionalProperties": false}