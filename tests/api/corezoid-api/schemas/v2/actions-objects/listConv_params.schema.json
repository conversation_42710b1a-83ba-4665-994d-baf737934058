{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "const": "ok"}, "ops": {"$id": "#/properties/ops", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0", "type": "object", "required": ["id", "proc", "obj", "obj_id", "params", "ref_mask"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string", "const": ""}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "const": "ok"}, "obj": {"$id": "#/properties/ops/items/anyOf/0/properties/obj", "type": "string", "const": "conv_params"}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/obj_id", "type": "integer"}, "params": {"$id": "#/properties/ops/items/anyOf/0/properties/params", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/params/items", "anyOf": [{"$id": "#/properties/ops/items/anyOf/0/properties/params/items/anyOf/0", "type": "object", "required": ["name", "type", "descr", "flags", "regex", "regex_error_text"], "properties": {"name": {"$id": "#/properties/ops/items/anyOf/0/properties/params/items/anyOf/0/properties/name", "type": "string"}, "type": {"$id": "#/properties/ops/items/anyOf/0/properties/params/items/anyOf/0/properties/type", "type": "string"}, "descr": {"$id": "#/properties/ops/items/anyOf/0/properties/params/items/anyOf/0/properties/descr"}, "flags": {"$id": "#/properties/ops/items/anyOf/0/properties/params/items/anyOf/0/properties/flags", "type": "array", "uniqueItems": true, "additionalItems": true, "items": {"$id": "#/properties/ops/items/anyOf/0/properties/params/items/anyOf/0/properties/flags/items"}}, "regex": {"$id": "#/properties/ops/items/anyOf/0/properties/params/items/anyOf/0/properties/regex", "type": "string"}, "regex_error_text": {"$id": "#/properties/ops/items/anyOf/0/properties/params/items/anyOf/0/properties/regex_error_text", "type": "string"}}, "additionalProperties": false}]}}, "ref_mask": {"$id": "#/properties/ops/items/anyOf/0/properties/ref_mask", "type": "boolean"}}, "additionalProperties": false}]}}}, "additionalProperties": false}