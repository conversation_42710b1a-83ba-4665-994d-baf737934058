{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "list"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "list": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj_id", "obj_type", "company_id", "title", "create_time", "change_time", "owner_id"], "additionalProperties": false, "properties": {"obj_id": {"type": "integer"}, "obj_type": {"type": "string", "const": "dashboard"}, "company_id": {"type": ["string", "null"]}, "title": {"type": "string"}, "create_time": {"type": "integer"}, "change_time": {"type": "integer"}, "owner_id": {"type": "integer"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}