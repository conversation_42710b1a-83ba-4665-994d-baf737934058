{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"$id": "#/properties/request_proc", "type": "string", "const": "ok"}, "ops": {"$id": "#/properties/ops", "type": "array", "uniqueItems": true, "additionalItems": false, "items": {"$id": "#/properties/ops/items", "type": "object", "required": ["id", "proc", "obj", "obj_id", "new_status", "old_status", "is_changed"], "properties": {"id": {"$id": "#/properties/ops/items/anyOf/0/properties/id", "type": "string", "const": ""}, "proc": {"$id": "#/properties/ops/items/anyOf/0/properties/proc", "type": "string", "const": "ok"}, "obj": {"$id": "#/properties/ops/items/anyOf/0/properties/obj", "type": "string"}, "obj_id": {"$id": "#/properties/ops/items/anyOf/0/properties/obj_id", "type": "integer"}, "new_status": {"$id": "#/properties/ops/items/anyOf/0/properties/new_status", "type": "integer"}, "old_status": {"$id": "#/properties/ops/items/anyOf/0/properties/old_status", "type": "integer"}, "is_changed": {"$id": "#/properties/ops/items/anyOf/0/properties/is_changed", "type": "boolean"}}, "additionalProperties": false}}}, "additionalProperties": false}