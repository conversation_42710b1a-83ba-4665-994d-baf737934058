{"$schema": "http://json-schema.org/draft-07/schema", "type": "object", "required": ["request_proc", "ops"], "properties": {"request_proc": {"type": "string", "const": "ok"}, "ops": {"type": "array", "additionalItems": true, "items": {"anyOf": [{"type": "object", "required": ["id", "proc", "obj", "ref", "obj_id"], "properties": {"id": {"type": "string"}, "proc": {"type": "string", "const": "ok"}, "obj": {"type": "string", "const": "task"}, "ref": {"type": "string"}, "obj_id": {"type": "string"}}, "additionalProperties": true}]}}}, "additionalProperties": true}