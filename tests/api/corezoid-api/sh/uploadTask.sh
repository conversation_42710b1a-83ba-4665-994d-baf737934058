#!/bin/sh

# Before you can start using Corezoid API you need to create an API key and get an authorization login and secret key.
# Create file with this script
# Check if file allowed for executing as program
# Allow executing as a program - chmod +x ./../conveyor_api_multipart/download_sh
# Set your env variables
# {{API_KEY_LOGIN}}, {{API_KEY_SECRET}}, {{API_URL}},
# {{COMPANY_ID}}, {{FOLDER_ID}}, {{ASYNC}},
# {{FILE_DIR_PATH}}, {{FILE_NAME}}, {{CONTENT_TYPE}},
# {{TMP_FILE_PATH}}
# https://doc.corezoid.com/docs/protocol-description-v2
# Manage api_keys - https://admin.corezoid.com/users/api_key

#API_KEY_LOGIN="22059"                                                            # authorization login of your API key (for example {{API_KEY_LOGIN}} = 32
#API_KEY_SECRET="1PCA8D7iABVTpdM7KUW92XFufbMd1Wivdxmrve1w1KDwUvahtW"              # a secret key of your API key
#API_URL="https://admin-pre.corezoid.com/api/2/upload"                            # URL to download_sh endpoint (for example {{API_URL}} = https://admin.corezoid.com/api/2/upload

#COMPANY_ID="i738314881"                                                          # company id (for examole {{COMPANY_ID}} = i814004545)
# FOLDER_ID="0"                                                                   # foder id for upload objects (for example {{FOLDER_ID}} = 0)
# ASYNC="false"                                                                   # possible values: true | false
FILE_DIR_PATH="./tests/api/corezoid-api/objects-csv/"                             # Path to local file dir (for example {{FILE_DIR_PATH}} = /home)
# FILE_NAME="folder_for_test_upload.zip"                                          # File name (for example {{FILE_NAME}} = test.json)
CONTENT_TYPE="text/csv"                                                           # possible values: application/json | application/zip
TMP_FILE_PATH="./gen_signature"                                                   # path to tmp file

TIMESTAMP=$(printf $(($(date +%s))))
FILE_PATH=${FILE_DIR_PATH}/${FILE_NAME}
BODY="Content-Disposition: form-data; name=\"type\"\r\ncreateContent-Disposition: form-data; name=\"obj\"\r\ntaskContent-Disposition: form-data; name=\"conv_id\"\r\n${CONV_ID}Content-Disposition: form-data; name=\"async\"\r\n${ASYNC}Content-Disposition: form-data; name=\"divider\"\r\n${DIVIDER}Content-Disposition: form-data; name=\"encoding\"\r\n${ENCODING}Content-Disposition: form-data; name=\"names_in_first_row\"\r\n${NAMES_IN_FIRST_ROW}Content-Disposition: form-data; name=\"company_id\"\r\n${COMPANY_ID}Content-Disposition: form-data; name=\"reference_col_name\"\r\n${REFERENCE_COL_NAME}Content-Disposition: form-data; name=\"cols_convert_to\"\r\n${COLS_CONVERT_TO}Content-Disposition: form-data; name=\"extra_sys_ip\"\r\n${EXTRA_SYS_IP}Content-Disposition: form-data; name=\"extra_sys_filename\"\r\n${EXTRA_SYS_FILENAME}Content-Disposition: form-data; name=\"from_file\"; filename=\"${FILE_NAME}\"\r\nContent-Type: ${CONTENT_TYPE}\r\n"

printf "${TIMESTAMP}${API_KEY_SECRET}${BODY}" >> $TMP_FILE_PATH
cat $FILE_PATH >> $TMP_FILE_PATH
printf "$API_KEY_SECRET" >> $TMP_FILE_PATH
SIGNATURE=$(openssl dgst -sha1 $TMP_FILE_PATH | awk '{printf $NF}')
rm $TMP_FILE_PATH

REQ=$(
  curl  --insecure --silent -XPOST "${API_URL}/${API_KEY_LOGIN}/${TIMESTAMP}/${SIGNATURE}" \
        --header "Content-Type: multipart/form-data" \
        --form "type=create" \
        --form "obj=task" \
        --form "conv_id=${CONV_ID}" \
        --form "async=${ASYNC}" \
        --form "divider=${DIVIDER}" \
        --form "encoding=${ENCODING}" \
        --form "names_in_first_row=${NAMES_IN_FIRST_ROW}" \
        --form "company_id=${COMPANY_ID}" \
        --form "reference_col_name=${REFERENCE_COL_NAME}" \
        --form "cols_convert_to=${COLS_CONVERT_TO}" \
        --form "extra_sys_ip=${EXTRA_SYS_IP}" \
        --form "extra_sys_filename=${EXTRA_SYS_FILENAME}" \
        --form "from_file=@${FILE_PATH};type=${CONTENT_TYPE}"
)

# printf "divider=${DIVIDER}" log
printf "Result: ${REQ}"