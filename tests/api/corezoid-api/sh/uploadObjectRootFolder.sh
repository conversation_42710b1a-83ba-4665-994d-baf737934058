#!/bin/sh
FILE_DIR_PATH="./tests/api/corezoid-api/objects-zip/"                           # Path to local file dir (for example {{FILE_DIR_PATH}} = /home)
CONTENT_TYPE="application/zip"                                                  # possible values: application/json | application/zip
TMP_FILE_PATH="./gen_signature"                                                 # path to tmp file

TIMESTAMP=$(printf $(($(date +%s))))
FILE_PATH=${FILE_DIR_PATH}/${FILE_NAME}
BODY="Content-Disposition: form-data; name=\"type\"\r\ncreateContent-Disposition: form-data; name=\"obj\"\r\nobj_schemeContent-Disposition: form-data; name=\"obj_to_id\"\r\n0Content-Disposition: form-data; name=\"async\"\r\n${ASYNC}Content-Disposition: form-data; name=\"rewrite_alias\"\r\nfalseContent-Disposition: form-data; name=\"with_alias\"\r\ntrueContent-Disposition: form-data; name=\"validate_scheme\"\r\n${VALIDATE_SCHEME}Content-Disposition: form-data; name=\"company_id\"\r\n${COMPANY_ID}Content-Disposition: form-data; name=\"scheme\"; filename=\"${FILE_NAME}\"\r\nContent-Type: ${CONTENT_TYPE}\r\n"

printf "${TIMESTAMP}${API_KEY_SECRET}${BODY}" >> $TMP_FILE_PATH
cat $FILE_PATH >> $TMP_FILE_PATH
printf "$API_KEY_SECRET" >> $TMP_FILE_PATH
SIGNATURE=$(openssl dgst -sha1 $TMP_FILE_PATH | awk '{printf $NF}')
rm $TMP_FILE_PATH

REQ=$(
  curl  --insecure --silent -XPOST "${API_URL}/${API_KEY_LOGIN}/${TIMESTAMP}/${SIGNATURE}" \
        --header "Content-Type: multipart/form-data" \
        --form "type=create" \
        --form "obj=obj_scheme" \
        --form "obj_to_id=0" \
        --form "async=${ASYNC}" \
        --form "rewrite_alias=${REWRITE_ALIAS}" \
        --form "with_alias=${WITH_ALIAS}" \
        --form "validate_scheme=${VALIDATE_SCHEME}" \
        --form "company_id=${COMPANY_ID}" \
        --form "scheme=@${FILE_PATH};type=${CONTENT_TYPE}"
)

printf "Result: ${REQ}"