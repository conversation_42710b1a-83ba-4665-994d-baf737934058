import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  companyTestCases,
  arrayTestCases,
  securityTestCases,
  maxLength,
  minLength,
} from '../../../../../negativeCases';

describe('Chart dashboard (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newDashboard: string | number;
  let newChart: string | number;
  let company_id: any;
  let valuesToSkip: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: 0,
        obj_type: 0,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    newDashboard = response.body.ops[0].obj_id;
    const responseChart = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHART,
        company_id,

        name: `newDashboardChart`,
        dashboard_id: newDashboard,
        series: [],
        description: 'newDashboardChart',
        obj_type: 'table',
      }),
    );
    newChart = responseChart.body.ops[0].obj_id;
  });

  test.each(
    [...integerTestCases, ...undefinedTestCase, ...securityTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create chart in dashboard with invalid dashboard_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHART,
        company_id,

        name: `newDashboardChart`,
        dashboard_id: input,
        series: [],
        description: 'newDashboardChart',
        obj_type: 'table',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...securityTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create chart in dashboard with invalid obj_type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHART,
        company_id,

        name: 'test',
        dashboard_id: newDashboard,
        series: [],
        obj_type: input,
      }),
    );

    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...maxLength, ...minLength].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create chart in dashboard with invalid name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHART,
        company_id,

        name: input,
        dashboard_id: newDashboard,
        series: [],
        description: 'newDashboardChart',
        obj_type: 'table',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...arrayTestCases, ...undefinedTestCase, ...securityTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create chart in dashboard with invalid series '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHART,
        company_id,

        name: 'test',
        dashboard_id: newDashboard,
        series: input,
        description: 'newDashboardChart',
        obj_type: 'table',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't create chart in dashboard with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.CHART,
          company_id: input,

          name: 'test',
          dashboard_id: newDashboard,
          series: [],
          description: 'newDashboardChart',
          obj_type: 'table',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0, -1, ''];

  test.each(
    integerTestCases
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create chart in dashboard with invalid version '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHART,
        company_id,

        name: 'test',
        dashboard_id: newDashboard,
        series: [],
        version: input,
        obj_type: 'table',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(integerTestCases.filter(({ input }) => input !== null).map(({ input, errors }) => [input, errors]))(
    `shouldn't create chart in dashboard with invalid uuid '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.CHART,
          company_id,

          name: 'test',
          dashboard_id: newDashboard,
          series: [],
          uuid: input,
          obj_type: 'table',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't get chart in dashboard with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.GET,
          obj: OBJ_TYPE.CHART,
          company_id,
          obj_id: input,

          dashboard_id: newDashboard,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(companyTestCases.map(({ input, errors }) => [input, errors]))(
    `shouldn't get chart in dashboard with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.GET,
          obj: OBJ_TYPE.CHART,
          company_id: input,
          obj_id: newChart,

          dashboard_id: newDashboard,
        }),
      );
      expect(response.status).toBe(200);
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't get chart in dashboard with invalid dashboard_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.GET,
          obj: OBJ_TYPE.CHART,
          company_id,
          obj_id: newChart,

          dashboard_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify chart in dashboard with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.CHART,
          company_id,
          obj_id: input,

          dashboard_id: newDashboard,
          name: 'modifyDashboardChart',
          description: 'modifyDashboardChart',
          series: [],
          obj_type: 'column',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify chart in dashboard with invalid dashboard_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.CHART,
          company_id,
          obj_id: newChart,

          dashboard_id: input,
          name: 'modifyDashboardChart',
          description: 'modifyDashboardChart',
          series: [],
          obj_type: 'column',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...minLength, ...maxLength].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify chart in dashboard with invalid name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CHART,
        company_id,
        obj_id: newChart,

        dashboard_id: newDashboard,
        name: input,
        description: 'modifyDashboardChart',
        series: [],
        obj_type: 'column',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...arrayTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify chart in dashboard with invalid series '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.CHART,
          company_id,
          obj_id: newChart,

          dashboard_id: newDashboard,
          name: 'test',
          description: 'modifyDashboardChart',
          series: input,
          obj_type: 'column',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify chart in dashboard with invalid obj_type '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.CHART,
          company_id,
          obj_id: newChart,

          dashboard_id: newDashboard,
          name: 'test',
          description: 'modifyDashboardChart',
          series: [],
          obj_type: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(companyTestCases.map(({ input, errors }) => [input, errors]))(
    `shouldn't modify chart in dashboard with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.CHART,
          company_id: input,
          obj_id: newChart,

          dashboard_id: newDashboard,
          name: 'test',
          description: 'modifyDashboardChart',
          series: [],
          obj_type: 'table',
        }),
      );
      expect(response.status).toBe(200);
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...minLength, ...maxLength].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify chart in dashboard with invalid name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CHART,
        company_id,
        obj_id: newChart,

        dashboard_id: newDashboard,
        name: input,
        description: 'modifyDashboardChart',
        series: [],
        obj_type: 'table',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [null];

  test.each(
    stringTestCases
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify chart in dashboard with invalid description '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CHART,
        company_id,
        obj_id: newChart,

        dashboard_id: newDashboard,
        name: 'new',
        description: input,
        series: [],
        obj_type: 'table',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0, -1, ''];

  test.each(
    integerTestCases
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify chart in dashboard with invalid version '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CHART,
        company_id,
        obj_id: newChart,

        dashboard_id: newDashboard,
        name: 'test',
        description: 'modifyDashboardChart',
        series: [],
        obj_type: 'table',
        version: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.skip.each(companyTestCases.map(({ input, errors }) => [input, errors]))(
    `shouldn't modify chart in dashboard with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.CHART,
          company_id: input,
          obj_id: newChart,

          dashboard_id: newDashboard,
        }),
      );
      expect(response.status).toBe(200);
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't delete chart in dashboard with invalid dashboard_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.CHART,
          company_id,
          obj_id: newChart,

          dashboard_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't delete chart in dashboard with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.CHART,
          company_id,
          obj_id: input,

          dashboard_id: newDashboard,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0, -1, ''];

  test.each(
    integerTestCases
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't delete chart in dashboard with invalid version '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CHART,
        obj_id: newChart,
        company_id,

        dashboard_id: newDashboard,
        version: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDashboard,
        company_id,
      }),
    );
  });
});
