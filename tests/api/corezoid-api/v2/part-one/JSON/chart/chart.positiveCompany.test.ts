import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createChartSchema from '../../../../schemas/v2/actions-objects/createChart.schema.json';
import getChartSchema from '../../../../schemas/v2/actions-objects/getChart.schema.json';
import showSchema from '../../../../schemas/v2/actions-objects/showDashboardWithGrid.schema.json';
import modifySchema from '../../../../schemas/v2/actions-objects/modifyDashboard.schema.json';
import modifyChartSchema from '../../../../schemas/v2/actions-objects/modifyChart.schema.json';
import deleteChartSchema from '../../../../schemas/v2/actions-objects/deleteChart.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { v4 as uuidv4 } from 'uuid';
import { requestListConv, requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';

describe('Chart dashboard (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newDashboard: string | number;
  let newDashboardChart: string | number;
  let newDashboardChart2: string | number;
  let company_id: any;
  const randInt = Date.now();
  const uuid = uuidv4();
  let conv_id: number;
  let final_node_ID: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseConv = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Conv_${randInt}`, 0);
    conv_id = responseConv.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_id, company_id);
    expect(responseList.status).toBe(200);
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: 0,
        obj_type: 0,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    newDashboard = response.body.ops[0].obj_id;
  });

  test('should create chart in dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHART,
        company_id,

        name: `newDashboardChart`,
        dashboard_id: newDashboard,
        series: [],
        obj_type: 'table',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].dashboard_id).toBe(newDashboard);
    expect(response.body.ops[0].type).toBe('create');
    expect(response.body.ops[0].obj).toBe('chart');
    newDashboardChart = response.body.ops[0].obj_id;
    SchemaValidator.validate(createChartSchema, response.body);
  });

  test('should create chart in dashboard with description', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHART,
        company_id,

        name: `newDashboardChart`,
        dashboard_id: newDashboard,
        series: [],
        description: 'newDashboardChart',
        obj_type: 'funnel',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].dashboard_id).toBe(newDashboard);
    newDashboardChart2 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createChartSchema, response.body);
  });

  test('should create chart in dashboard with sort', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHART,
        company_id,

        name: `newDashboardChart`,
        dashboard_id: newDashboard,
        series: [],
        description: 'newDashboardChart',
        obj_type: 'column',
        sort: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].dashboard_id).toBe(newDashboard);
    SchemaValidator.validate(createChartSchema, response.body);
  });

  test('should create chart in dashboard with version', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHART,
        company_id,

        name: `newDashboardChart`,
        dashboard_id: newDashboard,
        series: [],
        description: 'newDashboardChart',
        obj_type: 'pie',
        sort: false,
        version: randInt,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].dashboard_id).toBe(newDashboard);
    SchemaValidator.validate(createChartSchema, response.body);
  });

  test('should create chart in dashboard with uuid', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHART,
        company_id,

        name: `newDashboardChart`,
        dashboard_id: newDashboard,
        series: [],
        description: 'newDashboardChart',
        obj_type: 'pie',
        sort: false,
        version: randInt,
        uuid: `${uuid}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].dashboard_id).toBe(newDashboard);
    SchemaValidator.validate(createChartSchema, response.body);
  });

  test('should get chart in dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CHART,
        company_id,
        obj_id: newDashboardChart,

        dashboard_id: newDashboard,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].dashboard_id).toBe(newDashboard);
    expect(response.body.ops[0].obj_type).toBe('table');
    expect(response.body.ops[0].name).toBe('newDashboardChart');
    expect(response.body.ops[0].privs).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ list_obj: ['all'], type: 'delete' }),
        { list_obj: ['all'], type: 'create' },
        { list_obj: ['all'], type: 'view' },
        { list_obj: ['all'], type: 'modify' },
      ]),
    );
    SchemaValidator.validate(getChartSchema, response.body);
  });

  test('should modify dashboard with chart addad grid', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        title: `titleModifyDashboard`,
        time_range: {
          select: 'online',
          timezone_offset: -180,
        },
        description: 'descriptionModifyDashboard',
        grid: [
          {
            obj_id: newDashboardChart,
            x: 0,
            y: 0,
            width: 4,
            height: 4,
          },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(modifySchema, response.body);
  });

  test('should modify chart in required param', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CHART,
        company_id,
        obj_id: newDashboardChart,

        name: `modifyDashboardChart`,
        dashboard_id: newDashboard,
        series: [],
        obj_type: 'column',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('chart');
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    expect(response.body.ops[0].chart_id).toBe(newDashboardChart);
    SchemaValidator.validate(modifyChartSchema, response.body);
  });

  test('should get chart in dashboard after modify', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CHART,
        company_id,
        obj_id: newDashboardChart,

        dashboard_id: newDashboard,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].dashboard_id).toBe(newDashboard);
    expect(response.body.ops[0].obj_type).toBe('column');
    expect(response.body.ops[0].name).toBe('modifyDashboardChart');
    expect(response.body.ops[0].privs).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ list_obj: ['all'], type: 'delete' }),
        { list_obj: ['all'], type: 'create' },
        { list_obj: ['all'], type: 'view' },
        { list_obj: ['all'], type: 'modify' },
      ]),
    );
    SchemaValidator.validate(getChartSchema, response.body);
  });

  test('should modify chart in all param', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CHART,
        company_id,
        obj_id: newDashboardChart,

        name: `modifyDashboardChart`,
        dashboard_id: newDashboard,
        series: [
          {
            conv_id,
            node_id: final_node_ID,
            title: 'Start',
            type_icon: 'start',
            type_title: 'Start node',
            type: 'node',
          },
        ],
        description: 'modifyDashboardChart',
        obj_type: 'pie',
        sort: false,
        version: randInt,
        uuid,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('chart');
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    expect(response.body.ops[0].chart_id).toBe(newDashboardChart);
    SchemaValidator.validate(modifyChartSchema, response.body);

    const responseGet = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CHART,
        company_id,
        obj_id: newDashboardChart,

        dashboard_id: newDashboard,
      }),
    );
    expect(responseGet.status).toBe(200);
    expect(responseGet.body.ops[0].dashboard_id).toBe(newDashboard);
    expect(responseGet.body.ops[0].obj_type).toBe('pie');
    expect(responseGet.body.ops[0].name).toBe('modifyDashboardChart');
    expect(responseGet.body.ops[0].description).toBe('modifyDashboardChart');
    expect(responseGet.body.ops[0].series[0].conv_id).toBe(conv_id);
    expect(responseGet.body.ops[0].series[0].node_id).toBe(final_node_ID);
    SchemaValidator.validate(getChartSchema, responseGet.body);
  });

  test('should show dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    expect(response.body.ops[0].title).toBe('titleModifyDashboard');
    expect(response.body.ops[0].description).toBe('descriptionModifyDashboard');
    expect(response.body.ops[0].favorite).toBe(false);
    expect(response.body.ops[0].folder_id).toBe(0);
    expect(response.body.ops[0].privs).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ list_obj: ['all'], type: 'delete' }),
        { list_obj: ['all'], type: 'create' },
        { list_obj: ['all'], type: 'view' },
        { list_obj: ['all'], type: 'modify' },
      ]),
    );
    expect(response.body.ops[0].chart_list[0].obj_id).toBe(newDashboardChart);
    expect(response.body.ops[0].chart_list[0].name).toBe('modifyDashboardChart');
    expect(response.body.ops[0].chart_list[0].type).toBe('pie');
    expect(response.body.ops[0].time_range.select).toBe('online');
    expect(response.body.ops[0].time_range.timezone_offset).toBe(-180);
    SchemaValidator.validate(showSchema, response.body);
  });

  test('should delete chart in dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CHART,
        company_id,
        obj_id: newDashboardChart,

        dashboard_id: newDashboard,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].dashboard_id).toBe(newDashboard);
    expect(response.body.ops[0].obj_id).toBe(newDashboardChart);
    SchemaValidator.validate(deleteChartSchema, response.body);
  });

  test('should delete chart in dashboard with version', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CHART,
        company_id,
        obj_id: newDashboardChart2,

        dashboard_id: newDashboard,
        version: randInt,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].dashboard_id).toBe(newDashboard);
    expect(response.body.ops[0].obj_id).toBe(newDashboardChart2);
    SchemaValidator.validate(deleteChartSchema, response.body);
  });

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);

    const responseDashboard = await requestDeleteObj(api, OBJ_TYPE.DASHBOARD, newDashboard, company_id);
    expect(responseDashboard.status).toBe(200);

    const responseConv = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(responseConv.status).toBe(200);
  });
});
