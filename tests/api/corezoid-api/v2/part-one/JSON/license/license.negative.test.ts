import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import faker from 'faker';
import {
  integerTestCases,
  stringTestCases,
  stringNotValidTestCases,
  undefinedTestCase,
  arrayTestCases,
  boolTestCases,
} from '../../../../../negativeCases';

describe('License (negative)', () => {
  let api_s: ApiKeyClient;
  let apikey_s: ApiKey;
  let obj_id: number;
  let valuesToSkip: any;

  beforeAll(async () => {
    apikey_s = await application.getApiKeySuper();
    api_s = application.getApiKeyClient(apikey_s);

    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'list',
      obj: 'licenses',
      limit: 1,
      offset: 0,
    });
    expect(response.status).toBe(200);
    expect(response.body[0].status).toBe('active');
    obj_id = response.body[0].obj_id;
  });

  test.each([...stringTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't get instances with invalid obj_type '%s'`,
    async (input, errors) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'get',
        obj: 'license',
        obj_type: input,
      });
      expect(response.status).toBe(400);
      expect(response.body.result).toEqual('error');
      const error = response.body.description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([undefined])(`shouldn't list licenses with invalid obj_type '%s'`, async obj_type => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'get',
      obj: 'license',
      obj_type,
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toBe('error');
    expect(response.body.message).toContain(`Key 'obj_type' is required`);
  });

  valuesToSkip = [0];
  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't list instances with invalid limit '%s'`, async (limit, errors) => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'list',
      obj: 'licenses',
      limit,
      offset: 0,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    const error = response.body.description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([
    [
      101,
      `{\"description\":\"Value is not valid. Value's limit is more than maximum allowed: 100\",\"key\":\"limit\",\"value\":101}`,
    ],
    [undefined, `Key 'limit' is required`],
  ])(`shouldn't list licenses with invalid limit '%s'`, async (limit, res) => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'list',
      obj: 'licenses',
      limit,
      offset: 0,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toBe('error');
    expect(response.body.description).toContain(res);
  });

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't list instances with invalid offset '%s'`, async (offset, errors) => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'list',
      obj: 'licenses',
      limit: 1,
      offset,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    const error = response.body.description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([
    [
      101,
      `{\"description\":\"Value is not valid. Value's limit is more than maximum allowed: 100\",\"key\":\"offset\",\"value\":101}`,
    ],
    [undefined, `Key 'offset' is required`],
  ])(`shouldn't list licenses with invalid offset '%s'`, async (offset, res) => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'list',
      obj: 'licenses',
      limit: 1,
      offset,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toBe('error');
    expect(response.body.description).toContain(res);
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't view license with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api_s.requestSuperadmin('license/api/2', {
        type: 'view',
        obj: 'license',
        obj_id: input,
      });
      expect(response.status).toBe(400);
      expect(response.body.proc).toEqual('error');
      const error = response.body.description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't delete license with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api_s.requestSuperadmin('license/api/2', {
        type: 'delete',
        obj: 'license',
        obj_id: input,
      });
      expect(response.status).toBe(400);
      expect(response.body.proc).toEqual('error');
      const error = response.body.description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't download license with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api_s.requestSuperadmin('license/api/2', {
        type: 'download',
        obj: 'license',
        obj_id: input,
        format: 'base64',
      });
      expect(response.status).toBe(400);
      expect(response.body.proc).toEqual('error');
      const error = response.body.description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't download license with invalid format '%s'`,
    async (input, errors) => {
      const response = await api_s.requestSuperadmin('license/api/2', {
        type: 'download',
        obj: 'license',
        obj_id,
        format: input,
      });
      expect(response.status).toBe(400);
      expect(response.body.proc).toEqual('error');
      const error = response.body.description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.skip.each([...stringTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't upload license with invalid content '%s'`,
    async (input, errors) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'upload',
        obj: 'license',
        content: input,
      });
      expect(response.status).toBe(200);
      const error = response.body.description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't create license with invalid company_name '%s'`,
    async (input, errors) => {
      const response = await api_s.requestSuperadmin('license/api/2', {
        type: 'create',
        obj: 'license',
        company_name: input,
        cluster_id: 'PRE',
        start_time: **********,
        expire_time: **********,
        max_active_procs: 0,
        max_rps: 0,
        multi_tenancy: false,
        min_timer: 2,
        max_task_size_process: 0,
        max_users: '0',
        max_storage_size: '0',
        extra_attributes: [
          {
            name: 'account_license_module',
            value: true,
          },
        ],
        max_task_size_state_diagram: 35840,
      });
      expect(response.status).toBe(400);
      expect(response.body.proc).toEqual('error');
      const error = response.body.description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test(`shouldn't create license with invalid company_name - maxL'%s'`, async () => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'create',
      obj: 'license',
      company_name: faker.random.alphaNumeric(256),
      cluster_id: 'PRE',
      start_time: **********,
      expire_time: **********,
      max_active_procs: 0,
      max_rps: 0,
      multi_tenancy: false,
      min_timer: 2,
      max_task_size_process: 0,
      max_users: '0',
      max_storage_size: '0',
      extra_attributes: [
        {
          name: 'account_license_module',
          value: true,
        },
      ],
      max_task_size_state_diagram: 35840,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toContain(
      `Value is not valid. Value's byte_size is more than maximum allowed: 255`,
    );
  });

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't create license with invalid cluster_id '%s'`,
    async (input, errors) => {
      const response = await api_s.requestSuperadmin('license/api/2', {
        type: 'create',
        obj: 'license',
        company_name: 'testCorezoid',
        cluster_id: input,
        start_time: **********,
        expire_time: **********,
        max_active_procs: 0,
        max_rps: 0,
        multi_tenancy: false,
        min_timer: 2,
        max_task_size_process: 0,
        max_users: '0',
        max_storage_size: '0',
        extra_attributes: [
          {
            name: 'account_license_module',
            value: true,
          },
        ],
        max_task_size_state_diagram: 35840,
      });
      expect(response.status).toBe(400);
      expect(response.body.proc).toEqual('error');
      const error = response.body.description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test(`shouldn't create license with invalid cluster_id - maxL'%s'`, async () => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'create',
      obj: 'license',
      company_name: 'test',
      cluster_id: faker.random.alphaNumeric(256),
      start_time: **********,
      expire_time: **********,
      max_active_procs: 0,
      max_rps: 0,
      multi_tenancy: false,
      min_timer: 2,
      max_task_size_process: 0,
      max_users: '0',
      max_storage_size: '0',
      extra_attributes: [
        {
          name: 'account_license_module',
          value: true,
        },
      ],
      max_task_size_state_diagram: 35840,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toContain(
      `Value is not valid. Value's byte_size is more than maximum allowed: 255`,
    );
  });

  valuesToSkip = [0];
  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create license with invalid start_time '%s'`, async (input, errors) => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'create',
      obj: 'license',
      company_name: 'testCorezoid',
      cluster_id: 'test',
      start_time: input,
      expire_time: **********,
      max_active_procs: 0,
      max_rps: 0,
      multi_tenancy: false,
      min_timer: 2,
      max_task_size_process: 0,
      max_users: '0',
      max_storage_size: '0',
      extra_attributes: [
        {
          name: 'account_license_module',
          value: true,
        },
      ],
      max_task_size_state_diagram: 35840,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    const error = response.body.description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0];
  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create license with invalid expire_time '%s'`, async (input, errors) => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'create',
      obj: 'license',
      company_name: 'testCorezoid',
      cluster_id: 'test',
      start_time: **********,
      expire_time: input,
      max_active_procs: 0,
      max_rps: 0,
      multi_tenancy: false,
      min_timer: 2,
      max_task_size_process: 0,
      max_users: '0',
      max_storage_size: '0',
      extra_attributes: [
        {
          name: 'account_license_module',
          value: true,
        },
      ],
      max_task_size_state_diagram: 35840,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    const error = response.body.description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0];
  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create license with invalid max_active_procs '%s'`, async (input, errors) => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'create',
      obj: 'license',
      company_name: 'testCorezoid',
      cluster_id: 'test',
      start_time: **********,
      expire_time: **********,
      max_active_procs: input,
      max_rps: 0,
      multi_tenancy: false,
      min_timer: 2,
      max_task_size_process: 0,
      max_users: '0',
      max_storage_size: '0',
      extra_attributes: [
        {
          name: 'account_license_module',
          value: true,
        },
      ],
      max_task_size_state_diagram: 35840,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    const error = response.body.description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([faker.random.alphaNumeric(256), ****************************************])(
    `shouldn't create license with invalid max_active_procs maxL'%s'`,
    async input => {
      const response = await api_s.requestSuperadmin('license/api/2', {
        type: 'create',
        obj: 'license',
        company_name: 'testCorezoid',
        cluster_id: 'test',
        start_time: **********,
        expire_time: **********,
        max_active_procs: input,
        max_rps: 0,
        multi_tenancy: false,
        min_timer: 2,
        max_task_size_process: 0,
        max_users: '0',
        max_storage_size: '0',
        extra_attributes: [
          {
            name: 'account_license_module',
            value: true,
          },
        ],
        max_task_size_state_diagram: 35840,
      });
      expect(response.status).toBe(400);
      expect(response.body.proc).toEqual('error');
      expect(response.body.description).toContain(
        `{\"description\":\"Value is not valid\",\"key\":\"max_active_procs\"`,
      );
    },
  );

  valuesToSkip = [0];
  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create license with invalid max_rps '%s'`, async (input, errors) => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'create',
      obj: 'license',
      company_name: 'testCorezoid',
      cluster_id: 'test',
      start_time: **********,
      expire_time: **********,
      max_active_procs: 0,
      max_rps: input,
      multi_tenancy: false,
      min_timer: 2,
      max_task_size_process: 0,
      max_users: '0',
      max_storage_size: '0',
      extra_attributes: [
        {
          name: 'account_license_module',
          value: true,
        },
      ],
      max_task_size_state_diagram: 35840,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    const error = response.body.description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([faker.random.alphaNumeric(256), ****************************************])(
    `shouldn't create license with invalid max_rps maxL'%s'`,
    async input => {
      const response = await api_s.requestSuperadmin('license/api/2', {
        type: 'create',
        obj: 'license',
        company_name: 'testCorezoid',
        cluster_id: 'test',
        start_time: **********,
        expire_time: **********,
        max_active_procs: 0,
        max_rps: input,
        multi_tenancy: false,
        min_timer: 2,
        max_task_size_process: 0,
        max_users: '0',
        max_storage_size: '0',
        extra_attributes: [
          {
            name: 'account_license_module',
            value: true,
          },
        ],
        max_task_size_state_diagram: 35840,
      });
      expect(response.status).toBe(400);
      expect(response.body.proc).toEqual('error');
      expect(response.body.description).toContain(`{\"description\":\"Value is not valid\",\"key\":\"max_rps\"`);
    },
  );

  valuesToSkip = [];
  test.each(
    [...boolTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create license with invalid multi_tenancy '%s'`, async (input, errors) => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'create',
      obj: 'license',
      company_name: 'testCorezoid',
      cluster_id: 'test',
      start_time: **********,
      expire_time: **********,
      max_active_procs: 0,
      max_rps: 0,
      multi_tenancy: input,
      min_timer: 2,
      max_task_size_process: 0,
      max_users: '0',
      max_storage_size: '0',
      extra_attributes: [
        {
          name: 'account_license_module',
          value: true,
        },
      ],
      max_task_size_state_diagram: 35840,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    const error = response.body.description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0];
  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create license with invalid min_timer '%s'`, async (input, errors) => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'create',
      obj: 'license',
      company_name: 'testCorezoid',
      cluster_id: 'test',
      start_time: **********,
      expire_time: **********,
      max_active_procs: 0,
      max_rps: 0,
      multi_tenancy: false,
      min_timer: input,
      max_task_size_process: 0,
      max_users: '0',
      max_storage_size: '0',
      extra_attributes: [
        {
          name: 'account_license_module',
          value: true,
        },
      ],
      max_task_size_state_diagram: 35840,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    const error = response.body.description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([faker.random.alphaNumeric(256), ****************************************])(
    `shouldn't create license with invalid max_rps maxL'%s'`,
    async input => {
      const response = await api_s.requestSuperadmin('license/api/2', {
        type: 'create',
        obj: 'license',
        company_name: 'testCorezoid',
        cluster_id: 'test',
        start_time: **********,
        expire_time: **********,
        max_active_procs: 0,
        max_rps: 0,
        multi_tenancy: false,
        min_timer: input,
        max_task_size_process: 0,
        max_users: '0',
        max_storage_size: '0',
        extra_attributes: [
          {
            name: 'account_license_module',
            value: true,
          },
        ],
        max_task_size_state_diagram: 35840,
      });
      expect(response.status).toBe(400);
      expect(response.body.proc).toEqual('error');
      expect(response.body.description).toContain(`{\"description\":\"Value is not valid\",\"key\":\"min_timer\"`);
    },
  );

  valuesToSkip = [0];
  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create license with invalid max_task_size_process '%s'`, async (input, errors) => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'create',
      obj: 'license',
      company_name: 'testCorezoid',
      cluster_id: 'test',
      start_time: **********,
      expire_time: **********,
      max_active_procs: 0,
      max_rps: 0,
      multi_tenancy: false,
      min_timer: 1,
      max_task_size_process: input,
      max_users: '0',
      max_storage_size: '0',
      extra_attributes: [
        {
          name: 'account_license_module',
          value: true,
        },
      ],
      max_task_size_state_diagram: 35840,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    const error = response.body.description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([faker.random.alphaNumeric(256), ****************************************, 1023])(
    `shouldn't create license with invalid max_task_size_process maxL'%s'`,
    async input => {
      const response = await api_s.requestSuperadmin('license/api/2', {
        type: 'create',
        obj: 'license',
        company_name: 'testCorezoid',
        cluster_id: 'test',
        start_time: **********,
        expire_time: **********,
        max_active_procs: 0,
        max_rps: 0,
        multi_tenancy: false,
        min_timer: 1,
        max_task_size_process: input,
        max_users: '0',
        max_storage_size: '0',
        extra_attributes: [
          {
            name: 'account_license_module',
            value: true,
          },
        ],
        max_task_size_state_diagram: 35840,
      });
      expect(response.status).toBe(400);
      expect(response.body.proc).toEqual('error');
      expect(response.body.description).toContain(
        `{\"description\":\"Value is not valid\",\"key\":\"max_task_size_process\"`,
      );
    },
  );

  valuesToSkip = [0];
  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create license with invalid max_users '%s'`, async (input, errors) => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'create',
      obj: 'license',
      company_name: 'testCorezoid',
      cluster_id: 'test',
      start_time: **********,
      expire_time: **********,
      max_active_procs: 0,
      max_rps: 0,
      multi_tenancy: false,
      min_timer: 1,
      max_task_size_process: 0,
      max_users: input,
      max_storage_size: '0',
      extra_attributes: [
        {
          name: 'account_license_module',
          value: true,
        },
      ],
      max_task_size_state_diagram: 35840,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    const error = response.body.description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([faker.random.alphaNumeric(256), ****************************************])(
    `shouldn't create license with invalid max_users maxL'%s'`,
    async input => {
      const response = await api_s.requestSuperadmin('license/api/2', {
        type: 'create',
        obj: 'license',
        company_name: 'testCorezoid',
        cluster_id: 'test',
        start_time: **********,
        expire_time: **********,
        max_active_procs: 0,
        max_rps: 0,
        multi_tenancy: false,
        min_timer: 1,
        max_task_size_process: 0,
        max_users: input,
        max_storage_size: '0',
        extra_attributes: [
          {
            name: 'account_license_module',
            value: true,
          },
        ],
        max_task_size_state_diagram: 35840,
      });
      expect(response.status).toBe(400);
      expect(response.body.proc).toEqual('error');
      expect(response.body.description).toContain(`{\"description\":\"Value is not valid\",\"key\":\"max_users\"`);
    },
  );

  valuesToSkip = [0];
  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create license with invalid max_storage_size '%s'`, async (input, errors) => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'create',
      obj: 'license',
      company_name: 'testCorezoid',
      cluster_id: 'test',
      start_time: **********,
      expire_time: **********,
      max_active_procs: 0,
      max_rps: 0,
      multi_tenancy: false,
      min_timer: 1,
      max_task_size_process: 0,
      max_users: 0,
      max_storage_size: input,
      extra_attributes: [
        {
          name: 'account_license_module',
          value: true,
        },
      ],
      max_task_size_state_diagram: 35840,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    const error = response.body.description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([faker.random.alphaNumeric(256), ****************************************])(
    `shouldn't create license with invalid max_storage_size maxL'%s'`,
    async input => {
      const response = await api_s.requestSuperadmin('license/api/2', {
        type: 'create',
        obj: 'license',
        company_name: 'testCorezoid',
        cluster_id: 'test',
        start_time: **********,
        expire_time: **********,
        max_active_procs: 0,
        max_rps: 0,
        multi_tenancy: false,
        min_timer: 1,
        max_task_size_process: 0,
        max_users: 0,
        max_storage_size: input,
        extra_attributes: [
          {
            name: 'account_license_module',
            value: true,
          },
        ],
        max_task_size_state_diagram: 35840,
      });
      expect(response.status).toBe(400);
      expect(response.body.proc).toEqual('error');
      expect(response.body.description).toContain(
        `{\"description\":\"Value is not valid\",\"key\":\"max_storage_size\"`,
      );
    },
  );

  valuesToSkip = [0];
  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create license with invalid max_task_size_state_diagram '%s'`, async (input, errors) => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'create',
      obj: 'license',
      company_name: 'testCorezoid',
      cluster_id: 'test',
      start_time: **********,
      expire_time: **********,
      max_active_procs: 0,
      max_rps: 0,
      multi_tenancy: false,
      min_timer: 1,
      max_task_size_process: 0,
      max_users: 0,
      max_storage_size: 0,
      extra_attributes: [
        {
          name: 'account_license_module',
          value: true,
        },
      ],
      max_task_size_state_diagram: input,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    const error = response.body.description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([faker.random.alphaNumeric(256), ****************************************, 1023])(
    `shouldn't create license with invalid max_storage_size maxL'%s'`,
    async input => {
      const response = await api_s.requestSuperadmin('license/api/2', {
        type: 'create',
        obj: 'license',
        company_name: 'testCorezoid',
        cluster_id: 'test',
        start_time: **********,
        expire_time: **********,
        max_active_procs: 0,
        max_rps: 0,
        multi_tenancy: false,
        min_timer: 1,
        max_task_size_process: 0,
        max_users: 0,
        max_storage_size: 0,
        extra_attributes: [
          {
            name: 'account_license_module',
            value: true,
          },
        ],
        max_task_size_state_diagram: input,
      });
      expect(response.status).toBe(400);
      expect(response.body.proc).toEqual('error');
      expect(response.body.description).toContain(
        `{\"description\":\"Value is not valid\",\"key\":\"max_task_size_state_diagram\"`,
      );
    },
  );

  valuesToSkip = [];
  test.each(
    [...boolTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create license with invalid extra_attributes.value '%s'`, async (input, errors) => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'create',
      obj: 'license',
      company_name: 'testCorezoid',
      cluster_id: 'test',
      start_time: **********,
      expire_time: **********,
      max_active_procs: 0,
      max_rps: 0,
      multi_tenancy: false,
      min_timer: 1,
      max_task_size_process: 0,
      max_users: 0,
      max_storage_size: 0,
      extra_attributes: [
        {
          name: 'account_license_module',
          value: input,
        },
      ],
      max_task_size_state_diagram: 0,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    const error = response.body.description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [];
  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create license with invalid extra_attributes.name '%s'`, async (input, errors) => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'create',
      obj: 'license',
      company_name: 'testCorezoid',
      cluster_id: 'test',
      start_time: **********,
      expire_time: **********,
      max_active_procs: 0,
      max_rps: 0,
      multi_tenancy: false,
      min_timer: 1,
      max_task_size_process: 0,
      max_users: 0,
      max_storage_size: 0,
      extra_attributes: [
        {
          name: input,
          value: 'input',
        },
      ],
      max_task_size_state_diagram: 0,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    const error = response.body.description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test(`shouldn't create license with invalid extra_attributes.name - maxL'%s'`, async () => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'create',
      obj: 'license',
      company_name: 'test',
      cluster_id: 'test',
      start_time: **********,
      expire_time: **********,
      max_active_procs: 0,
      max_rps: 0,
      multi_tenancy: false,
      min_timer: 2,
      max_task_size_process: 0,
      max_users: 0,
      max_storage_size: 0,
      extra_attributes: [
        {
          name: faker.random.alphaNumeric(256),
          value: true,
        },
      ],
      max_task_size_state_diagram: 0,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    expect(response.body.description).toContain(
      `Value is not valid. Value's byte_size is more than maximum allowed: 255`,
    );
  });

  valuesToSkip = [];
  test.each(
    [...arrayTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create license with invalid extra_attributes '%s'`, async (input, errors) => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'create',
      obj: 'license',
      company_name: 'testCorezoid',
      cluster_id: 'test',
      start_time: **********,
      expire_time: **********,
      max_active_procs: 0,
      max_rps: 0,
      multi_tenancy: false,
      min_timer: 1,
      max_task_size_process: 0,
      max_users: 0,
      max_storage_size: 0,
      extra_attributes: input,
      max_task_size_state_diagram: 0,
    });
    expect(response.status).toBe(400);
    expect(response.body.proc).toEqual('error');
    const error = response.body.description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });
});
