import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import * as data from './license.json';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import listSchema from '../../../../schemas/v2/license/listLicense.schema.json';
import showSchema from '../../../../schemas/v2/license/showLicense.schema.json';
import viewSchema from '../../../../schemas/v2/license/viewLicense.schema.json';
import uploadSchema from '../../../../schemas/v2/license/uploadLicense.schema.json';
import declineSchema from '../../../../schemas/v2/license/declineLicense.schema.json';
import getSchema from '../../../../schemas/v2/license/getLicense.schema.json';
import invalidateSchema from '../../../../schemas/v2/license/invalidateLicense.schema.json';
import createSchema from '../../../../schemas/v2/license/createLicense.schema.json';
import deleteSchema from '../../../../schemas/v2/license/deleteLicense.schema.json';
import downloadSchema from '../../../../schemas/v2/license/downloadLicense.schema.json';

describe('Licence (positive)', () => {
  let api_s: ApiKeyClient;
  let apikey_s: ApiKey;
  let obj_id: number;

  beforeAll(async () => {
    apikey_s = await application.getApiKeySuper();
    api_s = application.getApiKeyClient(apikey_s);
  });

  test(`Get license (file)`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'get',
      obj: 'license',
      obj_type: 'file',
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    expect(response.body.data.pub_key).toBeString();
    SchemaValidator.validate(getSchema, response.body);
  });

  test(`Show license`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'show',
      obj: 'license',
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toBe('error');
    expect(response.body.message).toBe('License not valid');
  });

  test(`Upload license (content "test")`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'upload',
      obj: 'license',
      content: 'test',
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    SchemaValidator.validate(uploadSchema, response.body);
  });

  test(`Accept license - 'License not valid'`, async () => {
    await new Promise(r => setTimeout(r, 5000));
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'accept',
      obj: 'license',
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toBe('error');
    expect(response.body.message).toBe('License not valid');
  });

  test(`Upload license (valid content)`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'upload',
      obj: 'license',
      content: data.content,
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    SchemaValidator.validate(uploadSchema, response.body);

    const responseShow = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'show',
      obj: 'license',
    });
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.result).toBe('ok');
    expect(responseShow.body.data.multi_tenancy).toBe(false);
    SchemaValidator.validate(showSchema, responseShow.body);

    const responseDecline = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'decline',
      obj: 'license',
    });
    expect(responseDecline.status).toBe(200);
    expect(responseDecline.body.result).toBe('ok');
  });

  test(`Invalidate license`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'invalidate',
      obj: 'license',
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    expect(response.body.list[0].proc).toBe('error');
    expect(response.body.list[0].reason).toBe('no_response');
    SchemaValidator.validate(invalidateSchema, response.body);
  });

  test(`Decline license`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'decline',
      obj: 'license',
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    SchemaValidator.validate(declineSchema, response.body);
  });

  test(`List licenses`, async () => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'list',
      obj: 'licenses',
      limit: 1,
      offset: 0,
    });
    expect(response.status).toBe(200);
    expect(response.body[0].status).toBe('active');
    obj_id = response.body[0].obj_id;
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`View license`, async () => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'view',
      obj: 'license',
      obj_id,
    });
    expect(response.status).toBe(200);
    expect(response.body.status).toBe('active');
    expect(response.body.multi_tenancy).toBe(false);
    SchemaValidator.validate(viewSchema, response.body);
  });

  test(`сreate license (only requared parameter)`, async () => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'create',
      obj: 'license',
      company_name: 'Middleware',
      cluster_id: 'PRE',
      start_time: **********,
      expire_time: **********,
    });
    expect(response.status).toBe(200);
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`сreate license`, async () => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'create',
      obj: 'license',
      company_name: 'Middleware',
      cluster_id: 'PRE',
      start_time: **********,
      expire_time: **********,
      max_active_procs: 0,
      max_rps: 0,
      multi_tenancy: false,
      min_timer: 2,
      max_task_size_process: 0,
      max_users: '0',
      max_storage_size: '0',
      extra_attributes: [
        {
          name: 'account_license_module',
          value: true,
        },
      ],
      max_task_size_state_diagram: 35840,
    });
    obj_id = response.body.obj_id;
    expect(response.status).toBe(200);
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`download license`, async () => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'download',
      obj: 'license',
      obj_id,
      format: 'base64',
    });
    expect(response.status).toBe(200);
    expect(response.body.content).toBeString;
    SchemaValidator.validate(downloadSchema, response.body);
  });

  test(`delete license`, async () => {
    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'delete',
      obj: 'license',
      obj_id,
    });
    expect(response.status).toBe(200);
    expect(response.body.proc).toBe('ok');
    SchemaValidator.validate(deleteSchema, response.body);
  });
});
