import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import faker from 'faker';

describe('Convs (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let short_name_project: string;
  let project_id: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    short_name_project = `project-${Date.now()}`;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: short_name_project,
        description: 'test',
        stages: [{ title: 'develop', immutable: false }],
        status: 'active',
      }),
    );
    project_id = response.body.ops[0].obj_id;
  });

  test.each([
    [0, `Value is not valid`],
    [true, `Value is not valid`],
    [1234, `Value is not valid`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
  ])(`should list convs with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [1234, `Value '1234' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 1`],
    [faker.random.alphaNumeric(151), `Value is not valid. Value's byte_size is more than maximum allowed: 150`],
  ])(`should list convs with invalid pattern '%s'`, async (pattern, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        pattern,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [0, `Value is not valid. Value's limit is less than minimum allowed: 1`],
    [101, `Value is not valid. Value's limit is more than maximum allowed: 100`],
    [true, `Value is not valid`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
    ['', `Value is not valid`],
    [null, `Value is not valid`],
    [faker.random.alphaNumeric(101), `Value is not valid`],
  ])(`should list convs with invalid limit '%s'`, async (limit, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        limit,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [0, `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>]\">>`],
    [
      true,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>]\">>`,
    ],
    [
      1234,
      `Value '<<\"1234\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>]\">>`,
    ],
    [
      [],
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>]\">>`,
    ],
    [
      null,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>]\">>`,
    ],
    [
      {},
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>]\">>`,
    ],
    ['', `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"process\\\">>,<<\\\"state\\\">>]\">>`],
  ])(`should list convs with invalid obj_type '%s'`, async (obj_type, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        obj_type,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value is not valid`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
    ['', `Value is not valid`],
    [null, `Value is not valid`],
    [faker.random.alphaNumeric(101), `Value is not valid`],
  ])(`should list convs with invalid owner_id '%s'`, async (owner_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        owner_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      0,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>]\">>`,
    ],
    [
      true,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>]\">>`,
    ],
    [
      1234,
      `Value '<<\"1234\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>]\">>`,
    ],
    [
      [],
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>]\">>`,
    ],
    [
      null,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>]\">>`,
    ],
    [
      {},
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>]\">>`,
    ],
    [
      '',
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>]\">>`,
    ],
  ])(`should list convs with invalid location '%s'`, async (location, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        location,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value 'true' is not valid project_id or Key 'project_short_name' is required`],
    [[], `Value '' is not valid project_id or Key 'project_short_name' is required`],
    [{}, `Value '{[]}' is not valid project_id or Key 'project_short_name' is required`],
    ['', `Value '' is not valid project_id or Key 'project_short_name' is required`],
    [null, `Value 'null' is not valid project_id or Key 'project_short_name' is required`],
  ])(`should list convs with invalid project_id '%s'`, async (project_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        project_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value 'true' is not valid stage_id or Key 'stage_short_name' is required`],
    [[], `Value '' is not valid stage_id or Key 'stage_short_name' is required`],
    [{}, `Value '{[]}' is not valid stage_id or Key 'stage_short_name' is required`],
    ['', `Value '' is not valid stage_id or Key 'stage_short_name' is required`],
    [null, `Value 'null' is not valid stage_id or Key 'stage_short_name' is required`],
  ])(`should list convs with invalid stage_id '%s'`, async (stage_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        stage_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.skip.each([
    [true, `Value 'true' is not valid stage_id or Key 'stage_short_name' is required`],
    [[], `Value '' is not valid stage_id or Key 'stage_short_name' is required`],
    [{}, `Value '{[]}' is not valid stage_id or Key 'stage_short_name' is required`],
    ['', `Value '' is not valid stage_id or Key 'stage_short_name' is required`],
    [null, `Value 'null' is not valid stage_id or Key 'stage_short_name' is required`],
  ])(`should list convs with invalid stage_short_name '%s'`, async (stage_short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  afterAll(async () => {
    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: project_id,
        company_id,
      }),
    );
    expect(responseProject.status).toBe(200);
  });
});
