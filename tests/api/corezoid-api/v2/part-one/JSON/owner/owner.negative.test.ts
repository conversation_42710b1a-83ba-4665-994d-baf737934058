import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  maxLength,
  stringNotValidTestCases,
  boolTestCases,
} from '../../../../../negativeCases';

describe('Modify Owner (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let apiS: ApiKeyClient;
  let apiSuper: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let newApiKeyCompany: string | number;
  let newConv: string | number;
  let company_id: any;
  let stage_short_name: string;
  let project_short_name: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    apiSuper = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apiSuper);
    company_id = apikey.companies[0].id;
    stage_short_name = `stage-${Date.now()}`;
    project_short_name = `project-${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
        project_short_name: `test`,
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;

    const responseConvProject = await requestCreateObj(
      api,
      OBJ_TYPE.CONV,
      company_id,
      `Conv_${Date.now()}`,
      newStage,
      'process',
      newProject,
      newStage,
    );
    newConv = responseConvProject.body.ops[0].obj_id;

    const responseApiKeyCompany = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'APICompany',
        logins: [{ type: 'api' }],
      }),
    );
    newApiKeyCompany = responseApiKeyCompany.body.ops[0].users[0].obj_id;
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify owner with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.OWNER,
          obj_id: input,
          obj_type: 'conv',
          obj_to_id: newApiKeyCompany,
          save_src_privs: false,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify owner with invalid obj_type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newConv,
        obj_type: input,
        obj_to_id: newApiKeyCompany,
        save_src_privs: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify owner with invalid obj_to_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.OWNER,
          obj_id: newConv,
          obj_type: 'conv',
          obj_to_id: input,
          save_src_privs: false,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...boolTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify owner with invalid save_src_privs '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.OWNER,
          obj_id: newConv,
          obj_type: 'conv',
          obj_to_id: newApiKeyCompany,
          save_src_privs: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase, ...maxLength].map(({ input, errors }) => [
      input,
      errors,
    ]),
  )(`shouldn't modify owner with invalid obj_to_login '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newConv,
        obj_type: 'conv',
        obj_to_type: 'api',
        obj_to_login: input,
        save_src_privs: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify owner with invalid obj_to_type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newConv,
        obj_type: 'conv',
        obj_to_type: input,
        obj_to_login: '<EMAIL>',
        save_src_privs: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  afterAll(async () => {
    const responseProject = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(responseProject.status).toBe(200);

    const responseKeyCompany = await requestDeleteObj(apiS, OBJ_TYPE.USER, newApiKeyCompany, company_id);
    expect(responseKeyCompany.status).toBe(200);
  });
});
