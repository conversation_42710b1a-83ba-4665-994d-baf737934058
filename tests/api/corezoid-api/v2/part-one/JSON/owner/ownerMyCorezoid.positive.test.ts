import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import modifySchema from '../../../../schemas/v2/actions-objects/modifyOwner.schema.json';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';

describe('Modify Owner (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let apiS: ApiKeyClient;
  let apiSuper: ApiKey;
  let newConvFolder: string | number;
  let newFolder: string | number;
  let newDash: string | number;
  let newGroup: string | number;
  let newGroup1: string | number;
  let newInst: string | number;
  let newInst1: string | number;
  let newApiKeyCompany: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    apiSuper = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apiSuper);

    const responseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,

        title: `Folder_${Date.now()}`,
      }),
    );
    newFolder = responseFolder.body.ops[0].obj_id;

    const responseConvFolder = await requestCreateObj(
      api,
      OBJ_TYPE.CONV,
      null,
      `Conv_${Date.now()}`,
      newFolder,
      'process',
    );
    newConvFolder = responseConvFolder.body.ops[0].obj_id;

    const responseDash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        title: `Dash_${Date.now()}`,
        folder_id: 0,
      }),
    );
    newDash = responseDash.body.ops[0].obj_id;

    const responseGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,

        title: `Group_${Date.now()}`,
        obj_type: 'admins',
      }),
    );
    newGroup = responseGroup.body.ops[0].obj_id;

    const responseGroup1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,

        title: `Group_${Date.now()}`,
        obj_type: 'admins',
      }),
    );
    newGroup1 = responseGroup1.body.ops[0].obj_id;

    const responseInst = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: `db_call`,
        folder_id: newFolder,
        title: `Instance_${Date.now()}`,
        data: {
          driver: `postgres`,
          host: `COR-6938.middleware.loc`,
          port: `5432`,
          username: `postgres`,
          password: `pass`,
          ssl: false,
          database: `dbcall`,
          timeoutMs: 30000,
        },
      }),
    );
    expect(responseInst.status).toBe(200);
    expect(responseInst.body.ops[0].obj).toBe('instance');
    newInst = responseInst.body.ops[0].obj_id;

    const responseInst1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: `db_call`,
        folder_id: 0,
        title: `Instance_${Date.now()}`,
        data: {
          driver: `postgres`,
          host: `COR-6938.middleware.loc`,
          port: `5432`,
          username: `postgres`,
          password: `pass`,
          ssl: false,
          database: `dbcall`,
          timeoutMs: 30000,
        },
      }),
    );
    expect(responseInst1.status).toBe(200);
    expect(responseInst1.body.ops[0].obj).toBe('instance');
    newInst1 = responseInst1.body.ops[0].obj_id;

    const responseApiKeyCompany = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        title: 'APICompany',
        logins: [{ type: 'api' }],
      }),
    );
    newApiKeyCompany = responseApiKeyCompany.body.ops[0].users[0].obj_id;
  });

  test(`should modify Owner conv`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newConvFolder,
        obj_type: 'conv',
        obj_to_id: newApiKeyCompany,
        save_src_privs: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(modifySchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        limit: 10,
        sort: 'date',
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toBe('ok');
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConvFolder })]),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newConvFolder).owner_id).toEqual(
      newApiKeyCompany,
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newInst).owner_id).not.toEqual(
      newApiKeyCompany,
    );
  });

  test(`should modify Owner folder (save_src_privs:true)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newFolder,
        obj_type: 'folder',
        obj_to_id: newApiKeyCompany,
        save_src_privs: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(modifySchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        limit: 10,
        sort: 'date',
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toBe('ok');
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConvFolder })]),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newConvFolder).owner_id).toEqual(
      newApiKeyCompany,
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newInst).owner_id).toEqual(
      newApiKeyCompany,
    );

    const responseGetFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: `${newFolder}`,

        obj_type: 'folder',
      }),
    );
    expect(responseGetFolder.status).toBe(200);
    expect(responseGetFolder.body.ops[0].proc).toBe('ok');
    expect(responseGetFolder.body.ops[0].owner_id).toBe(newApiKeyCompany);
  });

  test(`should modify Owner dashboard (save_src_privs:false)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newDash,
        obj_type: 'dashboard',
        obj_to_id: newApiKeyCompany,
        save_src_privs: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(modifySchema, response.body);

    const responseGetDash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: `${newDash}`,

        obj_type: 'dashboard',
      }),
    );
    expect(responseGetDash.status).toBe(200);
    expect(responseGetDash.body.ops[0].proc).toBe('error');
    expect(responseGetDash.body.ops[0].description).toBe('Access denied');

    const responseGetDashS = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: `${newDash}`,

        obj_type: 'dashboard',
      }),
    );
    expect(responseGetDashS.status).toBe(200);
    expect(responseGetDashS.body.ops[0].proc).toBe('ok');
    expect(responseGetDashS.body.ops[0].owner_id).toBe(newApiKeyCompany);
  });

  test(`should modify Owner instance (save_src_privs:false)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newInst1,
        obj_type: 'instance',
        obj_to_id: newApiKeyCompany,
        save_src_privs: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(modifySchema, response.body);

    const responseGetDash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: `${newInst1}`,

        obj_type: 'instance',
      }),
    );
    expect(responseGetDash.status).toBe(200);
    expect(responseGetDash.body.ops[0].proc).toBe('error');
    expect(responseGetDash.body.ops[0].description).toBe('Access denied');

    const responseGetDashS = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: `${newInst1}`,

        obj_type: 'instance',
      }),
    );
    expect(responseGetDashS.status).toBe(200);
    expect(responseGetDashS.body.ops[0].proc).toBe('ok');
    expect(responseGetDashS.body.ops[0].owner_id).toBe(newApiKeyCompany);
  });

  test(`should modify Owner group (save_src_privs:false)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newGroup,
        obj_type: 'group',
        obj_to_id: newApiKeyCompany,
        save_src_privs: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(modifySchema, response.body);

    const responseGetGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: `${newGroup}`,

        obj_type: 'group',
      }),
    );
    expect(responseGetGroup.status).toBe(200);
    expect(responseGetGroup.body.ops[0].proc).toBe('error');
    expect(responseGetGroup.body.ops[0].description).toBe('Access denied');

    const responseGetGroupS = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: `${newGroup}`,

        obj_type: 'group',
      }),
    );
    expect(responseGetGroupS.status).toBe(200);
    expect(responseGetGroupS.body.ops[0].proc).toBe('ok');
    expect(responseGetGroupS.body.ops[0].owner_id).toBe(newApiKeyCompany);
  });

  test(`should modify Owner group (save_src_privs:true)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newGroup1,
        obj_type: 'group',
        obj_to_id: newApiKeyCompany,
        save_src_privs: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(modifySchema, response.body);

    const responseGetGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: `${newGroup1}`,

        obj_type: 'group',
      }),
    );
    expect(responseGetGroup.status).toBe(200);
    expect(responseGetGroup.body.ops[0].proc).toBe('ok');
    expect(responseGetGroup.body.ops[0].owner_id).toBe(newApiKeyCompany);
  });

  afterAll(async () => {
    const responseFolder = await requestDeleteObj(apiS, OBJ_TYPE.FOLDER, newFolder, null);
    expect(responseFolder.status).toBe(200);

    const responseDash = await requestDeleteObj(apiS, OBJ_TYPE.DASHBOARD, newDash, null);
    expect(responseDash.status).toBe(200);

    const responseInst = await requestDeleteObj(apiS, OBJ_TYPE.INSTANCE, newInst1, null);
    expect(responseInst.status).toBe(200);

    const responseKeyCompany = await requestDeleteObj(apiS, OBJ_TYPE.USER, newApiKeyCompany, null);
    expect(responseKeyCompany.status).toBe(200);

    const responseGroup = await requestDeleteObj(apiS, OBJ_TYPE.GROUP, newGroup, null);
    expect(responseGroup.status).toBe(200);

    const responseGroup1 = await requestDeleteObj(apiS, OBJ_TYPE.GROUP, newGroup1, null);
    expect(responseGroup1.status).toBe(200);
  });
});
