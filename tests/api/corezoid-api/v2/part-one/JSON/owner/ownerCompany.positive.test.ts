import { debug } from '../../../../../../../support/utils/logger';
import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import modifySchema from '../../../../schemas/v2/actions-objects/modifyOwner.schema.json';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';
import { makeRequestWithRetries, MyApiResponse } from '../../../../../../../utils/requestRetries';

describe('Modify Owner (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let apiS: ApiKeyClient;
  let apiSuper: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let newStage1: string | number;
  let newConv: string | number;
  let newConvProject: string | number;
  let newFolder: string | number;
  let newFolderProject: string | number;
  let newDash: string | number;
  let newGroup: string | number;
  let newGroup1: string | number;
  let newAlias: string | number;
  let newAliasProject: string | number;
  let newInst: string | number;
  let newInst1: string | number;
  let newApiKeyCompany: string | number;
  let newApiKeyLogin: string;
  let company_id: any;
  let project_short_name: string;
  let stage_short_name: string;
  let stage_short_name1: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    apiSuper = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apiSuper);
    company_id = apikey.companies[0].id;
    project_short_name = `project-${Date.now()}`;
    stage_short_name = `stage-${Date.now()}`;
    stage_short_name1 = `stage1-${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;

    const responseStage1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage1_${Date.now()}`,
        short_name: stage_short_name1,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage1 = responseStage1.body.ops[0].obj_id;

    const responseConvProject = await requestCreateObj(
      api,
      OBJ_TYPE.CONV,
      company_id,
      `Conv_${Date.now()}`,
      newStage,
      'process',
      newProject,
      newStage,
    );
    newConvProject = responseConvProject.body.ops[0].obj_id;

    const responseFolderProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,

        title: `Folder_${Date.now()}`,
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    newFolderProject = responseFolderProject.body.ops[0].obj_id;

    const responseDash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        project_id: newProject,
        stage_id: newStage,
        title: `Dash_${Date.now()}`,
      }),
    );
    newDash = responseDash.body.ops[0].obj_id;

    const responseAliasProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        short_name: `${Date.now()}`,
        title: 'Alias2',
        description: 'test',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    newAliasProject = responseAliasProject.body.ops[0].obj_id;

    const responseGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,

        title: `Group_${Date.now()}`,
        obj_type: 'admins',
      }),
    );
    newGroup = responseGroup.body.ops[0].obj_id;

    const responseGroup1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,

        title: `Group_${Date.now()}`,
        obj_type: 'admins',
      }),
    );
    newGroup1 = responseGroup1.body.ops[0].obj_id;

    const responseVersion = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn: `${Date.now()}`,
        stage_id: newStage,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    expect(responseVersion.status).toBe(200);
    expect(responseVersion.body.ops[0].obj).toBe('version');

    const responseInst = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        instance_type: `db_call`,
        folder_id: newFolderProject,
        title: `Instance_${Date.now()}`,
        data: {
          driver: `postgres`,
          host: `COR-6938.middleware.loc`,
          port: `5432`,
          username: `postgres`,
          password: `pass`,
          ssl: false,
          database: `dbcall`,
          timeoutMs: 30000,
        },
      }),
    );
    expect(responseInst.status).toBe(200);
    expect(responseInst.body.ops[0].obj).toBe('instance');
    newInst = responseInst.body.ops[0].obj_id;

    const responseInst1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        instance_type: `db_call`,
        folder_id: newFolderProject,
        title: `Instance_${Date.now()}`,
        data: {
          driver: `postgres`,
          host: `COR-6938.middleware.loc`,
          port: `5432`,
          username: `postgres`,
          password: `pass`,
          ssl: false,
          database: `dbcall`,
          timeoutMs: 30000,
        },
      }),
    );
    expect(responseInst1.status).toBe(200);
    expect(responseInst1.body.ops[0].obj).toBe('instance');
    newInst1 = responseInst1.body.ops[0].obj_id;

    const responseApiKeyCompany = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'APICompany',
        logins: [{ type: 'api' }],
      }),
    );
    newApiKeyCompany = responseApiKeyCompany.body.ops[0].users[0].obj_id;

    const responseConv = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Conv_${Date.now()}`, 0, 'process');
    newConv = responseConv.body.ops[0].obj_id;

    const responseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,

        title: `Folder_${Date.now()}`,
        folder_id: 0,
      }),
    );
    newFolder = responseFolder.body.ops[0].obj_id;

    const responseAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        short_name: `${Date.now()}-1`,
        title: 'Alias2',
        description: 'test',
      }),
    );
    newAlias = responseAlias.body.ops[0].obj_id;
  });

  test(`should modify Owner conv in stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newConvProject,
        obj_type: 'conv',
        obj_to_id: newApiKeyCompany,
        save_src_privs: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(modifySchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStage,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toBe('ok');
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConvProject })]),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newConvProject).owner_id).toEqual(
      newApiKeyCompany,
    );
  });

  test(`should modify Owner instanse in stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newInst1,
        obj_type: 'instance',
        obj_to_id: newApiKeyCompany,
        save_src_privs: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(modifySchema, response.body);

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolderProject,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.ops[0].proc).toBe('ok');
    expect(responseListFolder.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInst1 })]),
    );
    expect(
      (responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === newInst).owner_id,
    ).not.toEqual(newApiKeyCompany);
    expect((responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === newInst1).owner_id).toEqual(
      newApiKeyCompany,
    );
  });

  test(`should modify Owner folder in stage (instance in folder)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newFolderProject,
        obj_type: 'folder',
        obj_to_id: newApiKeyCompany,
        save_src_privs: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(modifySchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStage,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toBe('ok');
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolderProject })]),
    );
    expect(
      (responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolderProject).owner_id,
    ).toEqual(newApiKeyCompany);

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolderProject,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.ops[0].proc).toBe('ok');
    expect(responseListFolder.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInst })]),
    );
    expect((responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === newInst).owner_id).toEqual(
      newApiKeyCompany,
    );
    expect((responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === newInst1).owner_id).toEqual(
      newApiKeyCompany,
    );
  });

  test(`should modify Owner dashboard in stage `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newDash,
        obj_type: 'dashboard',
        obj_to_id: newApiKeyCompany,
        save_src_privs: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(modifySchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStage,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toBe('ok');
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDash })]),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newDash).owner_id).toEqual(
      newApiKeyCompany,
    );
  });

  test(`should modify Owner alias in stage `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newAliasProject,
        obj_type: 'alias',
        obj_to_id: newApiKeyCompany,
        save_src_privs: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(modifySchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toBe('ok');
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newAliasProject })]),
    );
    expect(
      (responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newAliasProject).owner_id,
    ).toEqual(newApiKeyCompany);
  });

  test(`should modify Owner group (save_src_privs:true)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newGroup,
        company_id,
        obj_type: 'group',
        obj_to_id: newApiKeyCompany,
        save_src_privs: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(modifySchema, response.body);

    const responseGetGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        company_id,
        obj_id: `${newGroup}`,

        obj_type: 'group',
      }),
    );
    expect(responseGetGroup.status).toBe(200);
    debug(JSON.stringify(responseGetGroup.body));
    expect(responseGetGroup.body.ops[0].proc).toBe('ok');
    expect(responseGetGroup.body.ops[0].owner_id).toBe(newApiKeyCompany);
  });

  test(`should modify Owner group (save_src_privs:false)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newGroup1,
        company_id,
        obj_type: 'group',
        obj_to_id: newApiKeyCompany,
        save_src_privs: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(modifySchema, response.body);

    const responseGetGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: `${newGroup1}`,
        company_id,

        obj_type: 'group',
      }),
    );
    expect(responseGetGroup.status).toBe(200);
    debug(JSON.stringify(responseGetGroup.body));
    expect(responseGetGroup.body.ops[0].proc).toBe('error');
    expect(responseGetGroup.body.ops[0].description).toBe('Access denied');

    const responseGetGroupS = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        company_id,
        obj_id: `${newGroup1}`,

        obj_type: 'group',
      }),
    );
    expect(responseGetGroupS.status).toBe(200);
    debug(JSON.stringify(responseGetGroupS.body));
    expect(responseGetGroupS.body.ops[0].proc).toBe('ok');
    expect(responseGetGroupS.body.ops[0].owner_id).toBe(newApiKeyCompany);
  });

  test(`should modify Owner stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newStage,
        obj_type: 'stage',
        obj_to_id: newApiKeyCompany,
        save_src_privs: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(modifySchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toBe('ok');
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newStage })]),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newStage).owner_id).toEqual(
      newApiKeyCompany,
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newStage1).owner_id).not.toEqual(
      newApiKeyCompany,
    );
  });

  test(`should modify Owner project (save_src_privs:true)`, async () => {
    const responseMod = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newProject,
        obj_type: 'project',
        obj_to_id: newApiKeyCompany,
        save_src_privs: true,
      }),
    );
    expect(responseMod.status).toBe(200);
    expect(responseMod.body.ops[0].proc).toBe('ok');
    expect(responseMod.body.ops[0].owner_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(modifySchema, responseMod.body);

    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        response.body.ops[0].list.some((item: any) => item.obj_id === newStage) &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === newStage)?.owner_id ===
          newApiKeyCompany &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === newStage1)?.owner_id === newApiKeyCompany
      );
    }

    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.PROJECT,
          obj_id: newProject,
          company_id,
        }),
      );
    }, checkConditions);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test(`should list projects after save_src_privs:true`, async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        response.body.ops[0].list.some((item: any) => item.obj_id === newProject) &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === newProject)?.owner_id ===
          newApiKeyCompany
      );
    }
    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.PROJECTS,
          company_id,
          sort: 'date',
          order: 'asc',
          limit: 10,
        }),
      );
    }, checkConditions);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test(`should modify Owner alias in company (save_src_privs:false)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newAlias,
        obj_type: 'alias',
        obj_to_id: newApiKeyCompany,
        save_src_privs: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(modifySchema, response.body);

    const responseGet = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: `${newAlias}`,

        obj_type: 'alias',
      }),
    );
    expect(responseGet.status).toBe(200);
    expect(responseGet.body.ops[0].proc).toBe('error');
    expect(responseGet.body.ops[0].description).toBe('Access denied');

    const responseGetS = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: `${newAlias}`,

        obj_type: 'alias',
      }),
    );
    expect(responseGetS.status).toBe(200);
    expect(responseGetS.body.ops[0].proc).toBe('ok');
    expect(responseGetS.body.ops[0].owner_id).toBe(newApiKeyCompany);
  });

  test(`should modify Owner conv in company (save_src_privs:true)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newConv,
        obj_type: 'conv',
        obj_to_id: newApiKeyCompany,
        save_src_privs: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(modifySchema, response.body);
    newApiKeyLogin = response.body.ops[0].owner_login;

    const responseGet = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: `${newConv}`,

        obj_type: 'conv',
      }),
    );
    expect(responseGet.status).toBe(200);
    expect(responseGet.body.ops[0].proc).toBe('ok');
    expect(responseGet.body.ops[0].owner_id).toBe(newApiKeyCompany);
  });

  test(`should modify Owner folder in company (save_src_privs:true) (obj_to_type/obj_to_login)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newFolder,
        obj_type: 'folder',
        save_src_privs: true,
        obj_to_type: 'api',
        obj_to_login: newApiKeyLogin,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(modifySchema, response.body);

    const responseGet = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: `${newFolder}`,

        obj_type: 'folder',
      }),
    );
    expect(responseGet.status).toBe(200);
    expect(responseGet.body.ops[0].proc).toBe('ok');
    expect(responseGet.body.ops[0].owner_id).toBe(newApiKeyCompany);
  });

  afterAll(async () => {
    const responseProject = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(responseProject.status).toBe(200);

    const responseConv = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, company_id);
    expect(responseConv.status).toBe(200);

    const responseFolder = await requestDeleteObj(api, OBJ_TYPE.FOLDER, newFolder, company_id);
    expect(responseFolder.status).toBe(200);

    const responseKeyCompany = await requestDeleteObj(api, OBJ_TYPE.USER, newApiKeyCompany, company_id);
    expect(responseKeyCompany.status).toBe(200);

    const responseGroup = await requestDeleteObj(apiS, OBJ_TYPE.GROUP, newGroup, company_id);
    expect(responseGroup.status).toBe(200);

    const responseGroup1 = await requestDeleteObj(apiS, OBJ_TYPE.GROUP, newGroup1, company_id);
    expect(responseGroup1.status).toBe(200);
  });
});
