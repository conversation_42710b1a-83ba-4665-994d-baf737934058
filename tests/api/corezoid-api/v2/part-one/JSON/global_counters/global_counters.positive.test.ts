import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import showSchema from '../../../../schemas/v2/actions-objects/showGlobal_counters.schema.json';

describe('Global_counters (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
  });

  test('should show global_counters - user', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.GLOBAL_COUNTERS,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].ops[0].proc).toBe('ok');
    SchemaValidator.validate(showSchema, response.body);
  });

  test('should show global_counters - superadmin', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.GLOBAL_COUNTERS,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].ops[0].proc).toBe('ok');
    SchemaValidator.validate(showSchema, response.body);
  });
});
