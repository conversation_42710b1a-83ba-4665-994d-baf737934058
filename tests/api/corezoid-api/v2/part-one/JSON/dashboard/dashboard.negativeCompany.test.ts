import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import faker from 'faker';

describe('Dashboard (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newDashboard: string | number;
  let company_id: any;
  let ApiKey2: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: 0,
        obj_type: 0,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    newDashboard = response.body.ops[0].obj_id;
    const createKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(createKeyResponse.status).toBe(200);
    const user = createKeyResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    ApiKey2 = +newApiKey.id;
  });

  test.each([0, 1234, null, true])(`shouldn't create dashboard with invalid title '%s'`, async title => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title,
        folder_id: 0,
        obj_type: 0,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(`Value '${title}' is not valid. Type of value is not 'binary'`);
    expect(response.body.ops[0].key).toEqual('title');
    expect(response.body.ops[0].value).toEqual(title);
  });

  test(`shouldn't create dashboard with invalid title long_string`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: faker.random.alphaNumeric(256),
        folder_id: 0,
        obj_type: 0,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(
      `Value is not valid. Value's byte_size is more than maximum allowed: 255`,
    );
  });

  test.each([0, 1234, true])(`shouldn't create dashboard with invalid description '%s'`, async description => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: 0,
        obj_type: 0,
        description,
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(
      `Value '${description}' is not valid. Type of value is not 'binary' or Value '<<\"${description}\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    );
  });

  test(`shouldn't create dashboard with invalid description long_string`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: 0,
        obj_type: 0,
        description: faker.random.alphaNumeric(2001),
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(
      `Value is not valid. Value's byte_size is more than maximum allowed: 2000 or Value`,
    );
  });

  test.each([
    [null, 'Value is not valid'],
    ['test', 'Value is not valid'],
    [true, 'Value is not valid'],
    ['', 'Value is not valid'],
    ['1', `Object's company ID does not match company ID in the request`],
    ['1234', `Object folder with id 1234 does not exist`],
    [1234, `Object folder with id 1234 does not exist`],
  ])(`shouldn't create dashboard with invalid folder_id '%s'`, async (folder_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id,
        obj_type: 0,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([0, 1234, null, true, 'test'])(
    `shouldn't create dashboard with invalid time_range '%s'`,
    async time_range => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.DASHBOARD,
          company_id,

          title: `Dashboard_${Date.now()}`,
          folder_id: 0,
          obj_type: 0,
          description: 'test',
          time_range,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(
        `Value '${time_range}' is not valid. Type of value is not 'list'`,
      );
    },
  );

  test.each([
    [
      null,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"online\\\">>,<<\\\"last10minute\\\">>,<<\\\"today\\\">>,<<\\\"lastHour\\\">>,<<\\\"last24hour\\\">>,\\n <<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previous_hour\\\">>,<<\\\"previous_day\\\">>,\\n <<\\\"previous_week\\\">>,<<\\\"previous_month\\\">>]\">> or Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"fromTo\\\">>]\">>`,
    ],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"online\\\">>,<<\\\"last10minute\\\">>,<<\\\"today\\\">>,<<\\\"lastHour\\\">>,<<\\\"last24hour\\\">>,\\n <<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previous_hour\\\">>,<<\\\"previous_day\\\">>,\\n <<\\\"previous_week\\\">>,<<\\\"previous_month\\\">>]\">> or Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"fromTo\\\">>]\">>`,
    ],
    [
      true,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"online\\\">>,<<\\\"last10minute\\\">>,<<\\\"today\\\">>,<<\\\"lastHour\\\">>,<<\\\"last24hour\\\">>,\\n <<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previous_hour\\\">>,<<\\\"previous_day\\\">>,\\n <<\\\"previous_week\\\">>,<<\\\"previous_month\\\">>]\">> or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"fromTo\\\">>]\">>`,
    ],
    [
      0,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"online\\\">>,<<\\\"last10minute\\\">>,<<\\\"today\\\">>,<<\\\"lastHour\\\">>,<<\\\"last24hour\\\">>,\\n <<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previous_hour\\\">>,<<\\\"previous_day\\\">>,\\n <<\\\"previous_week\\\">>,<<\\\"previous_month\\\">>]\">> or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"fromTo\\\">>]\">>`,
    ],
    [
      1234,
      `Value '<<\"1234\">>' is not valid. Value is not in allowed list <<\"[<<\\\"online\\\">>,<<\\\"last10minute\\\">>,<<\\\"today\\\">>,<<\\\"lastHour\\\">>,<<\\\"last24hour\\\">>,\\n <<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previous_hour\\\">>,<<\\\"previous_day\\\">>,\\n <<\\\"previous_week\\\">>,<<\\\"previous_month\\\">>]\">> or Value '<<\"1234\">>' is not valid. Value is not in allowed list <<\"[<<\\\"fromTo\\\">>]\">>`,
    ],
  ])(`shouldn't create dashboard with invalid select '%s'`, async (select, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: 0,
        obj_type: 0,
        description: 'test',
        time_range: {
          select,
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test(`shouldn't create dashboard without time_range parameters`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: 0,
        obj_type: 0,
        description: 'test',
        time_range: {
          select: undefined,
          start: undefined,
          stop: undefined,
          timezone_offset: undefined,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(`Value '{[]}' is not valid. Type of value is not 'list'`);
  });

  test.each([null, true, 'test'])(
    `shouldn't create dashboard with invalid timezone_offset '%s'`,
    async timezone_offset => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.DASHBOARD,
          company_id,

          title: `Dashboard_${Date.now()}`,
          folder_id: 0,
          obj_type: 0,
          description: 'test',
          time_range: {
            select: 'online',
            start: 1597847260,
            stop: 1597847260,
            timezone_offset,
          },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(`Value is not valid`);
    },
  );

  test.each([
    [0, 'Value is not valid'],
    [1234, 'Value is not valid'],
    [true, 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    ['test', 'Company test does not exists'],
    ['i883227690', 'User not in company'],
    [faker.random.alphaNumeric(37), `Value is not valid`],
  ])(`shouldn't create dashboard with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: 0,
        obj_type: 0,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([
    [null, 'Value is not valid'],
    [true, 'Value is not valid'],
    ['test', 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [0, 'Object dashboard with id 0 does not exist'],
    [1234, 'Object dashboard with id 1234 does not exist'],
  ])(`shouldn't modify dashboard with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id,

        title: `Dashboard_${Date.now()}`,
        description: 'test',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, 'Value is not valid'],
    [1234, 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    ['test', 'Company test does not exists'],
    [faker.random.alphaNumeric(37), `Value is not valid`],
  ])(`shouldn't modify dashboard with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        title: `Dashboard_${Date.now()}`,
        description: 'test',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [1234, `Value '1234' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [faker.random.alphaNumeric(256), `Value is not valid. Value's byte_size is more than maximum allowed: 255`],
  ])(`shouldn't modify dashboard with invalid title '%s'`, async (title, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        title,
        description: 'test',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      true,
      `Value 'true' is not valid. Type of value is not 'binary' or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [
      1234,
      `Value '1234' is not valid. Type of value is not 'binary' or Value '<<\"1234\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [
      0,
      `Value '0' is not valid. Type of value is not 'binary' or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [
      faker.random.alphaNumeric(2001),
      `Value is not valid. Value's byte_size is more than maximum allowed: 2000 or Value`,
    ],
  ])(`shouldn't modify dashboard with invalid description '%s'`, async (description, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        title: 'test',
        description,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([
    [null, 'Value is not valid'],
    [true, 'Value is not valid'],
    ['test', 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [0, `Object dashboard with id 0 does not exist`],
    [1234, `Object dashboard with id 1234 does not exist`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
  ])(`shouldn't favorite dashboard with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id,

        favorite: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, 'Value is not valid'],
    ['test', `Company test does not exists`],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [1234, `Value is not valid`],
    [-1, `Value is not valid`],
    [faker.random.alphaNumeric(37), `Value is not valid`],
  ])(`shouldn't favorite dashboard with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        favorite: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([0, 1234, 'test', [], {}, -1])(
    `shouldn't favorite dashboard with invalid favorite '%s'`,
    async favorite => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.FAVORITE,
          obj: OBJ_TYPE.DASHBOARD,
          company_id,
          obj_id: newDashboard,

          favorite,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(`Value is not valid`);
    },
  );

  test(`shouldn't favorite dashboard with invalid favorite false (object unstarred)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        favorite: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(`Already unstarred object`);
  });

  test(`shouldn't favorite dashboard with invalid favorite true (object starred)`, async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        favorite: true,
      }),
    );
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        favorite: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(`Already starred object`);
  });

  test.each([
    [true, 'Value is not valid'],
    ['test', `Value is not valid`],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [null, 'Value is not valid'],
    [1234, `Object dashboard with id 1234 does not exist`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [0, `Object dashboard with id 0 does not exist`],
  ])(`shouldn't show dashboard with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, 'Value is not valid'],
    ['test', `Company test does not exists`],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [1234, 'Value is not valid'],
    [-1, `Value is not valid`],
    [faker.random.alphaNumeric(37), `Value is not valid`],
  ])(`shouldn't show dashboard with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([
    [true, 'Value is not valid'],
    ['test', `Value is not valid`],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [null, 'Value is not valid'],
    [1234, `Object dashboard with id 1234 does not exist`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [0, `Object dashboard with id 0 does not exist`],
  ])(`shouldn't list dashboard with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id,
        list_obj: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`],
    ['test', `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`],
    [[], `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`],
    [{}, `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`],
    [null, `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`],
    [1234, `Value '<<\"1234\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`],
    [-1, `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`],
    [0, `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>]\">>`],
  ])(`shouldn't list dashboard with invalid list_obj '%s'`, async (list_obj, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
        list_obj,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value is not valid`],
    ['test', `Company test does not exists`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
    [1234, `Value is not valid`],
    [-1, `Value is not valid`],
    [0, `Value is not valid`],
    [faker.random.alphaNumeric(37), `Value is not valid`],
  ])(`shouldn't list dashboard with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
        list_obj: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([
    [true, 'Value is not valid'],
    ['test', `Value is not valid`],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [null, 'Value is not valid'],
    [1234, `Object dashboard with id 1234 does not exist`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [0, `Object dashboard with id 0 does not exist`],
  ])(`shouldn't link dashboard with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id,
        obj_to: 'user',
        obj_to_id: ApiKey2,
        privs: [{ type: 'create', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, 'Value is not valid'],
    ['test', `Object's company ID does not match company ID in the request`],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [null, `Object's company ID does not match company ID in the request`],
    [1234, `Value is not valid`],
    [-1, `Value is not valid`],
    [0, `Value is not valid`],
    [faker.random.alphaNumeric(37), `Value is not valid`],
  ])(`shouldn't link dashboard with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
        obj_to: 'user',
        obj_to_id: ApiKey2,
        privs: [{ type: 'create', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([
    [
      true,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
    ],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
    ],
    [
      [],
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
    ],
    [
      {},
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
    ],
    [
      null,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
    ],
    [
      1234,
      `Value '<<\"1234\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"1234\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value '<<\"1234\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
    ],
    [
      -1,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
    ],
    [
      0,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"folder\\\">>,<<\\\"group\\\">>,<<\\\"user\\\">>]\">>`,
    ],
  ])(`shouldn't link dashboard with invalid obj_to '%s'`, async (obj_to, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
        obj_to,
        obj_to_id: ApiKey2,
        privs: [{ type: 'create', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      true,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid`,
    ],
    [
      'test',
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid`,
    ],
    [
      [],
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid`,
    ],
    [
      {},
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid`,
    ],
    [
      null,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid`,
    ],
    [1234, `Link failed: user is not in the same company, what and specified dashboard.`],
    [
      -1,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid. Value's limit is less than minimum allowed: 0`,
    ],
    [0, `Object user with id 0 does not exist`],
  ])(`shouldn't link dashboard with invalid obj_to_id '%s'`, async (obj_to_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
        obj_to: 'user',
        obj_to_id,
        privs: [{ type: 'create', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `privs are not valid`],
    ['test', `privs are not valid`],
    [[], `privs are not valid`],
    [{}, `privs are not valid`],
    [null, `privs are not valid`],
    [1234, `privs are not valid`],
    [-1, `privs are not valid`],
    [0, `privs are not valid`],
  ])(`shouldn't link dashboard with invalid type '%s'`, async (type, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
        obj_to: 'user',
        obj_to_id: ApiKey2,
        privs: [{ type, list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `privs are not valid`],
    ['test', `privs are not valid`],
    [[], `list_obj does not coincide with one of the expected values [all]`],
    [{}, `privs are not valid`],
    [null, `privs are not valid`],
    [1234, `privs are not valid`],
    [-1, `privs are not valid`],
    [0, `privs are not valid`],
  ])(`shouldn't link dashboard with invalid list_obj '%s'`, async (list_obj, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
        obj_to: 'user',
        obj_to_id: ApiKey2,
        privs: [{ type: 'create', list_obj }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, 'Value is not valid'],
    [null, 'Value is not valid'],
    ['test', 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [1234, `object not found`],
    [0, `object not found`],
    [1, `object not found`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
  ])(`shouldn't delete dashboard with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, 'Value is not valid'],
    ['test', 'Company test does not exists'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [1234, `Value is not valid`],
    [1, `Value is not valid`],
    [-1, `Value is not valid`],
  ])(`shouldn't delete dashboard with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, 'Value is not valid'],
    [null, 'Value is not valid'],
    ['test', 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [1234, `Object dashboard with id 1234 does not exist`],
    [0, `Object dashboard with id 0 does not exist`],
    [1, `Object dashboard with id 1 does not exist`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
  ])(`shouldn't restore dashboard with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [1234, `Value is not valid`],
    [1, `Value is not valid`],
    [-1, `Value is not valid`],
  ])(`shouldn't restore dashboard with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test(`shouldn't restore dashboard with invalid company_id 'test'`, async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
      }),
    );
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id: 'test',
        obj_id: newDashboard,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(`Company test does not exists`);
  });

  test.each([
    [true, 'Value is not valid'],
    [null, 'Value is not valid'],
    ['test', 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [1234, `Object dashboard with id 1234 does not exist`],
    [0, `Object dashboard with id 0 does not exist`],
    [1, `Object dashboard with id 1 does not exist`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
  ])(`shouldn't destroy dashboard with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([true, {}, [], 1234, 1, -1])(
    `shouldn't destroy dashboard with invalid company_id '%s'`,
    async company_id => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DESTROY,
          obj: OBJ_TYPE.DASHBOARD,
          company_id,
          obj_id: newDashboard,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual('Value is not valid');
    },
  );

  test(`shouldn't destroy dashboard with invalid company_id 'test'`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.DASHBOARD,
        company_id: 'test',
        obj_id: newDashboard,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(`Company test does not exists`);
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
      }),
    );
  });

  test.each([
    [true, `obj_id has not valid type, expected types [integer, binary_integer]`],
    [null, `obj_id has not valid type, expected types [integer, binary_integer]`],
    ['test', `obj_id has not valid type, expected types [integer, binary_integer]`],
    [[], `obj_id has not valid type, expected types [integer, binary_integer]`],
    [-1, `Object dashboard with id -1 does not exist`],
    [0, `Object dashboard with id 0 does not exist`],
    [1234, `Object dashboard with id 1234 does not exist`],
    [undefined, `obj_id field is missing`],
  ])(`shouldn't list_history dashboard with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,
        obj_id,

        obj_type: 'dashboard',
        limit: 10,
        offset: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([-1, 1234, true, [], {}, 'test'])(
    `shouldn't list_history dashboard with invalid obj_type '%s'`,
    async obj_type => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.HISTORY,
          company_id,
          obj_id: newDashboard,

          obj_type,
          limit: 10,
          offset: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(
        `obj_type does not coincide with one of the expected values [stage, project, config, alias, instance, dashboard, conv, folder, version]`,
      );
    },
  );

  test.each([
    [true, 'limit has not valid type, expected types [non_neg_integer]'],
    ['test', 'limit has not valid type, expected types [non_neg_integer]'],
    [[], 'limit has not valid type, expected types [non_neg_integer]'],
    [{}, 'limit has not valid type, expected types [non_neg_integer]'],
    [-1, 'limit has not valid type, expected types [non_neg_integer]'],
    [undefined, 'limit field is missing'],
  ])(`shouldn't list_history dashboard with invalid limit '%s'`, async (limit, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,
        obj_id: newDashboard,

        obj_type: 'dashboard',
        limit,
        offset: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, 'offset has not valid type, expected types [non_neg_integer]'],
    ['test', 'offset has not valid type, expected types [non_neg_integer]'],
    [[], 'offset has not valid type, expected types [non_neg_integer]'],
    [{}, 'offset has not valid type, expected types [non_neg_integer]'],
    [-1, 'offset has not valid type, expected types [non_neg_integer]'],
    [undefined, 'offset field is missing'],
  ])(`shouldn't list_history dashboard with invalid offset '%s'`, async (offset, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,
        obj_id: newDashboard,

        obj_type: 'dashboard',
        limit: 10,
        offset,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([null, true, 'test', [], {}])(`shouldn't copy dashboard with invalid obj_id '%s'`, async obj_id => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        company_id,
        obj_id,

        obj_type: 'dashboard',
        title: 'copyDash',
        ignore_errors: true,
        async: false,
        folder_id: 0,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(`Value is not valid`);
    expect(response.body.ops[0].errors.key).toEqual(`obj_id`);
    expect(response.body.ops[0].errors.value).toEqual(obj_id);
  });

  test.each([
    [0, `dashboard does not exists`],
    [1234, `dashboard does not exists`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [undefined, `Key 'obj_id' is required`],
  ])(`shouldn't copy dashboard with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        company_id,
        obj_id,

        obj_type: 'dashboard',
        title: 'copyDash',
        ignore_errors: true,
        async: false,
        folder_id: 0,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"config\\\">>,\\n <<\\\"project\\\">>,<<\\\"stage\\\">>]\">>`,
    ],
  ])(`shouldn't copy dashboard with invalid obj_type '%s'`, async (obj_type, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        company_id,
        obj_id: newDashboard,

        obj_type,
        title: 'copyDash',
        ignore_errors: true,
        async: false,
        folder_id: 0,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(reason);
    expect(response.body.ops[0].errors.key).toEqual(`obj_type`);
    expect(response.body.ops[0].errors.value).toEqual(obj_type);
  });

  test.each([
    ['test', `Value is not valid or Key 'obj_to_id' is required`],
    [{}, `Value is not valid or Key 'obj_to_id' is required`],
    [[], `Value is not valid or Key 'obj_to_id' is required`],
    [null, `Value is not valid or Key 'obj_to_id' is required`],
    [true, `Value is not valid or Key 'obj_to_id' is required`],
    [1, `access denied`],
    [1234, `folder does not exists`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [undefined, `Key 'folder_id' is required or Key 'obj_to_id' is required`],
  ])(`shouldn't copy dashboard with invalid folder_id '%s'`, async (folder_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        company_id,
        obj_id: newDashboard,

        obj_type: 'dashboard',
        title: 'copyDash',
        ignore_errors: true,
        async: false,
        folder_id,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test(`shouldn't copy dashboard without obj_type`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        company_id,
        obj_id: newDashboard,

        obj_type: undefined,
        title: 'copyDash',
        ignore_errors: true,
        async: false,
        folder_id: 0,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(`Key 'obj_type' is required`);
  });

  test.each([
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't copy dashboard with invalid title '%s'`, async (title, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        company_id,
        obj_id: newDashboard,

        obj_type: 'dashboard',
        title,
        ignore_errors: true,
        async: false,
        folder_id: 0,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(reason);
    expect(response.body.ops[0].errors.key).toEqual(`title`);
    expect(response.body.ops[0].errors.value).toEqual(title);
  });

  test(`shouldn't copy dashboard with long title`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        company_id,
        obj_id: newDashboard,

        obj_type: 'dashboard',
        title: faker.random.alphaNumeric(256),
        ignore_errors: true,
        async: false,
        folder_id: 0,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(
      `Value is not valid. Value's byte_size is more than maximum allowed: 255`,
    );
    expect(response.body.ops[0].errors.key).toEqual(`title`);
  });

  test(`shouldn't copy dashboard without title`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        company_id,
        obj_id: newDashboard,

        obj_type: 'dashboard',
        title: undefined,
        ignore_errors: true,
        async: false,
        folder_id: 0,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(`Key 'title' is required`);
  });

  test.each([0, 123, null, 'test', {}, []])(
    `shouldn't copy dashboard with invalid ignore_errors '%s'`,
    async ignore_errors => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.OBJ_COPY,
          company_id,
          obj_id: newDashboard,

          obj_type: 'dashboard',
          title: 'test',
          ignore_errors,
          async: false,
          folder_id: 0,
          from_company_id: company_id,
          to_company_id: company_id,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].errors.description).toEqual(`Value is not valid`);
      expect(response.body.ops[0].errors.key).toEqual(`ignore_errors`);
      expect(response.body.ops[0].errors.value).toEqual(ignore_errors);
    },
  );

  test.each([0, 123, null, 'test', {}, []])(`shouldn't copy dashboard with invalid async '%s'`, async async => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        company_id,
        obj_id: newDashboard,

        obj_type: 'dashboard',
        title: 'test',
        ignore_errors: true,
        async,
        folder_id: 0,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(`Value is not valid`);
    expect(response.body.ops[0].errors.key).toEqual(`async`);
    expect(response.body.ops[0].errors.value).toEqual(async);
  });

  test.each([0, 123, true, {}, []])(`shouldn't copy dashboard with invalid from_company_id '%s'`, async id => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        company_id,
        obj_id: newDashboard,

        obj_type: 'dashboard',
        title: 'test',
        ignore_errors: true,
        async: false,
        folder_id: 0,
        from_company_id: id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(`Value is not valid`);
    expect(response.body.ops[0].errors.key).toEqual(`from_company_id`);
    expect(response.body.ops[0].errors.value).toEqual(id);
  });

  test.each([0, 123, true, {}, []])(`shouldn't copy dashboard with invalid to_company_id '%s'`, async id => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        company_id,
        obj_id: newDashboard,

        obj_type: 'dashboard',
        title: 'test',
        ignore_errors: true,
        async: false,
        folder_id: 0,
        from_company_id: company_id,
        to_company_id: id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(`Value is not valid`);
    expect(response.body.ops[0].errors.key).toEqual(`to_company_id`);
    expect(response.body.ops[0].errors.value).toEqual(id);
  });

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDashboard,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
        company_id,
      }),
    );
  });
});
