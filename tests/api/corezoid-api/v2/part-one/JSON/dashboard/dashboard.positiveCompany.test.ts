import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createSchema from '../../../../schemas/v2/actions-objects/createDashboard.schema.json';
import deleteSchema from '../../../../schemas/v2/actions-objects/deleteObject.schema.json';
import destroySchema from '../../../../schemas/v2/actions-objects/destroyObject.schema.json';
import restoreSchema from '../../../../schemas/v2/actions-objects/restoreObject.schema.json';
import modifySchema from '../../../../schemas/v2/actions-objects/modifyDashboard.schema.json';
import favoriteSchema from '../../../../schemas/v2/actions-objects/favoriteObject.schema.json';
import showSchema from '../../../../schemas/v2/actions-objects/showDashboard.schema.json';
import copySync from '../../../../schemas/v2/actions-objects/copyObjectSync.schema.json';
import copyAsync from '../../../../schemas/v2/actions-objects/copyObjectAsync.schema.json';
import linkSchema from '../../../../schemas/v2/actions-objects/linkMoveFolder.schema.json';
import linkKeySchema from '../../../../schemas/v2/actions-objects/linkKeyDashboard.schema.json';
import listSchema from '../../../../schemas/v2/actions-objects/listDashboard.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { makeRequestWithRetries, MyApiResponse } from '../../../../../../../utils/requestRetries';

describe('Dashboard (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newDashboard: string | number;
  let newFolder: string | number;
  let copyDashboard: string | number;
  let copyDashboardInFolder: string | number;
  let copyDashboardInCompany: string | number;
  let company_id: any;
  let ApiKey2: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        title: `Folder_${Date.now()}`,
        folder_id: 0,
        obj_type: 0,
        description: 'test',
      }),
    );
    newFolder = responseFolder.body.ops[0].obj_id;

    const createKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(createKeyResponse.status).toBe(200);
    const user = createKeyResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    ApiKey2 = +newApiKey.id;
  });

  test('should create dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: 0,
        obj_type: 0,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    expect(response.status).toBe(200);
    newDashboard = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should create dashboard without parameters', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: undefined,
        folder_id: undefined,
        obj_type: undefined,
        description: undefined,
        time_range: undefined,
      }),
    );
    expect(response.status).toBe(200);
    newDashboard = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should find dashboard in list_folder after create', async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        response.body.ops[0].list.some((item: any) => item.obj_id === newDashboard) &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === newDashboard)?.is_owner === true
      );
    }
    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.FOLDER,
          obj_id: 0,
          company_id,
        }),
      );
    }, checkConditions);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test('should modify dashboard', async () => {
    const responseMod = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        time_range: { select: 'online', timezone_offset: -180 },
        title: 'titleModifyDashboard',
        description: 'descriptionModifyDashboard',
      }),
    );
    expect(responseMod.status).toBe(200);
    expect(responseMod.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(modifySchema, responseMod.body);
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === newDashboard)?.title ===
          'titleModifyDashboard'
      );
    }
    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.FOLDER,
          obj_id: 0,
          company_id,
        }),
      );
    }, checkConditions);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test('should favorite dashboard true', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDashboard,
        favorite: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(favoriteSchema, response.body);
    await new Promise(r => setTimeout(r, 2000));
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
      }),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newDashboard)?.favorite).toEqual(
      true,
    );
  });

  test('should favorite dashboard false', async () => {
    const responseFavorite = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
        favorite: false,
      }),
    );
    expect(responseFavorite.status).toBe(200);
    expect(responseFavorite.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(favoriteSchema, responseFavorite.body);
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === newDashboard)?.favorite === false
      );
    }
    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.FOLDER,
          obj_id: 0,
          company_id,
        }),
      );
    }, checkConditions);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test('should show dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    expect(response.body.ops[0].title).toBe('titleModifyDashboard');
    expect(response.body.ops[0].description).toBe('descriptionModifyDashboard');
    expect(response.body.ops[0].favorite).toBe(false);
    expect(response.body.ops[0].folder_id).toBe(0);
    expect(response.body.ops[0].privs).toBeArray();
    expect(response.body.ops[0].chart_list).toBeArray();
    expect(response.body.ops[0].chart_list).toBeArray();
    SchemaValidator.validate(showSchema, response.body);
  });

  test('should link key to dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
        obj_to: 'user',
        obj_to_id: ApiKey2,
        privs: [{ type: 'create', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toBe('dashboard');
    expect(response.body.ops[0].obj_to_type).toBe('user');
    expect(response.body.ops[0].action_type).toBe('link');
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(linkKeySchema, response.body);
  });

  test('should list dashboard list_obj:group', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDashboard,
        list_obj: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('dashboard');
    expect(response.body.ops[0].list_obj).toBe('group');
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should copy dashboard sync', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newDashboard,

        obj_type: 'dashboard',
        folder_id: 0,
        title: 'copyDash',
        ignore_errors: true,
        async: false,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].scheme[0].old_obj_id).toBe(newDashboard);
    expect(response.body.ops[0].scheme[0].obj_type).toBe('dashboard');
    expect(response.body.ops[0].scheme[0].title).toBe('copyDash');
    copyDashboard = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copySync, response.body);
  });

  test('should find dashboard in list_folder after copy', async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        response.body.ops[0].list.some((item: any) => item.obj_id === copyDashboard) &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === copyDashboard)?.is_owner === true
      );
    }
    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.FOLDER,
          obj_id: 0,
          company_id,
        }),
      );
    }, checkConditions);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test('should copy dashboard async', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newDashboard,

        obj_type: 'dashboard',
        folder_id: 0,
        title: 'copyDash',
        ignore_errors: true,
        async: true,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('obj_copy');
    SchemaValidator.validate(copyAsync, response.body);
  });

  test('should copy dashboard sync in folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newDashboard,

        obj_type: 'dashboard',
        folder_id: newFolder,
        title: 'copyDash',
        ignore_errors: true,
        async: false,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    copyDashboardInFolder = response.body.ops[0].scheme[0].obj_id;
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('obj_copy');
    SchemaValidator.validate(copySync, response.body);
  });

  test('should find dashboard in folder list_folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newFolder,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: copyDashboardInFolder })]),
    );
    expect(
      (response.body.ops[0].list as Array<any>).find(item => item.obj_id === copyDashboardInFolder)?.is_owner,
    ).toEqual(true);
  });

  test('should copy dashboard sync in company null', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newDashboard,

        obj_type: 'dashboard',
        folder_id: 0,
        title: 'copyDash',
        ignore_errors: true,
        async: false,
        from_company_id: company_id,
        to_company_id: null,
      }),
    );
    copyDashboardInCompany = response.body.ops[0].scheme[0].obj_id;
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('obj_copy');
    SchemaValidator.validate(copySync, response.body);
  });

  test('should find dashboard in folder list_folder company_id null', async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        response.body.ops[0].list.some((item: any) => item.obj_id === copyDashboardInCompany) &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === copyDashboardInCompany)?.is_owner ===
          true
      );
    }
    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.FOLDER,
          obj_id: 0,
          company_id: null,
        }),
      );
    }, checkConditions);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test('should delete dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should find dashboard in list_folder filter_deleted', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
        filter: 'deleted',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDashboard })]),
    );
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newDashboard)?.is_owner).toEqual(
      true,
    );
  });

  test('should not find dashboard in list_folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newDashboard)).toBeEmpty();
  });

  test('should restore dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(restoreSchema, response.body);
  });

  test('should find dashboard in list_folder after restore', async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        response.body.ops[0].list.some((item: any) => item.obj_id === newDashboard) &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === newDashboard)?.is_owner === true
      );
    }
    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.FOLDER,
          obj_id: 0,
          company_id,
        }),
      );
    }, checkConditions);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test('should not find dashboard in list_folder filter_deleted', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
        filter: 'deleted',
      }),
    );
    expect(response.status).toBe(200);
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newDashboard)).toBeEmpty();
  });

  test('should list_history dashboard', async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        response.body.ops[0].list.length > 0 &&
        response.body.ops[0].list[0].obj === 'dashboard'
      );
    }
    const response = await makeRequestWithRetries(
      async () => {
        return await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.HISTORY,
            company_id,
            obj_id: newDashboard,
            obj_type: 'dashboard',
            limit: 10,
            offset: 0,
          }),
        );
      },
      checkConditions,
      {
        maxRetries: 150,
      },
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test('should link_folder dashboard to folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newDashboard,
        obj_type: 'dashboard',
        parent_id: 0,
        folder_id: newFolder,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    expect(response.body.ops[0].obj_type).toBe('dashboard');
    expect(response.body.ops[0].action_type).toBe('move');
    expect(response.body.ops[0].to_folder).toBe(newFolder);
    expect(response.body.ops[0].from_folder).toBe(0);
    SchemaValidator.validate(linkSchema, response.body);
  });

  test('should not find dashboard in list_folder 0', async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return response.status === 200 && response.body.ops[0].list.some((item: any) => item.obj_id !== newDashboard);
    }
    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.FOLDER,
          obj_id: 0,
          company_id,
        }),
      );
    }, checkConditions);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test('should find dashboard in list_folder newFolder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newFolder,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDashboard })]),
    );
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newDashboard)?.is_owner).toEqual(
      true,
    );
  });

  test('should link_folder dashboard to 0', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newDashboard,
        obj_type: 'dashboard',
        parent_id: newFolder,
        folder_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    expect(response.body.ops[0].obj_type).toBe('dashboard');
    expect(response.body.ops[0].action_type).toBe('move');
    expect(response.body.ops[0].to_folder).toBe(0);
    expect(response.body.ops[0].from_folder).toBe(newFolder);
    SchemaValidator.validate(linkSchema, response.body);
  });

  test('should not find dashboard in list_folder newFolder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newFolder,
      }),
    );
    expect(response.status).toBe(200);
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newDashboard)).toBeEmpty();
  });

  test('should find dashboard in list_folder 0', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    const dashboardItem = (response.body.ops[0].list as Array<any>).find(item => item.obj_id === newDashboard);
    expect(dashboardItem).toBeTruthy();
    expect(dashboardItem?.is_owner).toEqual(true);
  });

  test('should destroy dashboard', async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
      }),
    );
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(destroySchema, response.body);
  });

  test('should not find dashboard in list_folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newDashboard)).toBeEmpty();
  });

  test('should not find dashboard in list_folder filter_deleted', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
        filter: 'deleted',
      }),
    );
    expect(response.status).toBe(200);
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newDashboard)).toBeEmpty();
  });

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
        company_id,
      }),
    );
  });
});
