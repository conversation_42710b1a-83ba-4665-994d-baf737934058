import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import faker from 'faker';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  companyTestCases,
  objectTestCases,
  securityTestCases,
  maxLength,
  arrayTestCases,
  stringNotValidTestCases,
  boolTestCases,
} from '../../../../../negativeCases';

describe('Dashboard (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newDashboard: string | number;
  let company_id: any;
  let ApiKey2: string | number;
  let newProject: string | number;
  let newStage: string | number;
  let valuesToSkip: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: `project${Date.now()}`,
        description: 'test',
        stages: ['production'],
        status: 'active',
      }),
    );
    expect(responseProject.status).toBe(200);
    newProject = responseProject.body.ops[0].obj_id;
    newStage = responseProject.body.ops[0].stages[0];

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: newStage,
        stage_id: newStage,
        project_id: newProject,
        obj_type: 0,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    newDashboard = response.body.ops[0].obj_id;
    const createKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(createKeyResponse.status).toBe(200);
    const user = createKeyResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    ApiKey2 = +newApiKey.id;
  });

  test.each([...stringTestCases, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't create dashboard with invalid title '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.DASHBOARD,
          company_id,

          title: input,
          folder_id: newStage,
          stage_id: newStage,
          project_id: newProject,
          obj_type: 0,
          description: 'test',
          time_range: {
            select: 'online',
            start: 1597847260,
            stop: 1597847260,
            timezone_offset: -180,
          },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [null];

  test.each(
    stringTestCases
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create dashboard invalid description '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: newStage,
        stage_id: newStage,
        project_id: newProject,
        obj_type: 0,
        description: input,
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test(`shouldn't create dashboard with invalid description long_string`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: newStage,
        stage_id: newStage,
        project_id: newProject,
        obj_type: 0,
        description: faker.random.alphaNumeric(2001),
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(
      `Value is not valid. Value's byte_size is more than maximum allowed: 2000 or Value`,
    );
  });

  test.each([...integerTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't create dashboard with invalid folder_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.DASHBOARD,
          company_id,

          title: `Dashboard_${Date.now()}`,
          folder_id: input,
          stage_id: newStage,
          project_id: newProject,
          obj_type: 0,
          description: 'test',
          time_range: {
            select: 'online',
            start: 1597847260,
            stop: 1597847260,
            timezone_offset: -180,
          },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...objectTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't create dashboard with invalid time_range '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.DASHBOARD,
          company_id,

          title: `Dashboard_${Date.now()}`,
          folder_id: newStage,
          stage_id: newStage,
          project_id: newProject,
          obj_type: 0,
          description: 'test',
          time_range: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't create dashboard with invalid select '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.DASHBOARD,
          company_id,

          title: `Dashboard_${Date.now()}`,
          folder_id: newStage,
          stage_id: newStage,
          project_id: newProject,
          obj_type: 0,
          description: 'test',
          time_range: {
            select: input,
            start: 1597847260,
            stop: 1597847260,
            timezone_offset: -180,
          },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0, -1];

  test.each(
    [...integerTestCases, ...securityTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create dashboard with invalid timezone_offset '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: newStage,
        stage_id: newStage,
        project_id: newProject,
        obj_type: 0,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: input,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't create dashboard with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.DASHBOARD,
          company_id: input,

          title: `Dashboard_${Date.now()}`,
          folder_id: newStage,
          stage_id: newStage,
          project_id: newProject,
          obj_type: 0,
          description: 'test',
          time_range: {
            select: 'online',
            start: 1597847260,
            stop: 1597847260,
            timezone_offset: -180,
          },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0, -1];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create dashboard with invalid stage_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: newStage,
        stage_id: input,
        project_id: newProject,
        obj_type: 0,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0, -1];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create dashboard with invalid project_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: newStage,
        stage_id: newStage,
        project_id: input,
        obj_type: 0,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...stringTestCases, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify dashboard with invalid title '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.DASHBOARD,
          obj_id: newDashboard,

          title: input,
          description: 'test',
          time_range: {
            select: 'online',
            start: 1597847260,
            stop: 1597847260,
            timezone_offset: -180,
          },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [null];

  test.each(
    stringTestCases
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify dashboard invalid description '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDashboard,

        title: `Dashboard_${Date.now()}`,
        description: input,
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test(`shouldn't modify dashboard with invalid description long_string`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDashboard,

        title: `Dashboard_${Date.now()}`,
        description: faker.random.alphaNumeric(2001),
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(
      `Value is not valid. Value's byte_size is more than maximum allowed: 2000 or Value`,
    );
  });

  test.each([...integerTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify dashboard with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.DASHBOARD,
          obj_id: input,

          title: `Dashboard_${Date.now()}`,
          description: 'test',
          time_range: {
            select: 'online',
            start: 1597847260,
            stop: 1597847260,
            timezone_offset: -180,
          },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...objectTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify dashboard with invalid time_range '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.DASHBOARD,
          obj_id: newDashboard,

          title: `Dashboard_${Date.now()}`,
          description: 'test',
          time_range: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify dashboard with invalid select '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.DASHBOARD,
          obj_id: newDashboard,

          title: `Dashboard_${Date.now()}`,
          description: 'test',
          time_range: {
            select: input,
            start: 1597847260,
            stop: 1597847260,
            timezone_offset: -180,
          },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0, -1];

  test.each(
    [...integerTestCases, ...securityTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify dashboard with invalid timezone_offset '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDashboard,

        title: `Dashboard_${Date.now()}`,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: input,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...arrayTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify dashboard with invalid grid '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.DASHBOARD,
          obj_id: newDashboard,

          title: `Dashboard_${Date.now()}`,
          description: 'test',
          time_range: {
            select: 'online',
            start: 1597847260,
            stop: 1597847260,
            timezone_offset: -180,
          },
          grid: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify dashboard with invalid grid.obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.DASHBOARD,
          obj_id: newDashboard,

          title: `Dashboard_${Date.now()}`,
          description: 'test',
          time_range: {
            select: 'online',
            start: 1597847260,
            stop: 1597847260,
            timezone_offset: -180,
          },
          grid: [{ obj_id: input, x: 0, y: 0, width: 4, height: 4 }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test(`shouldn't modify dashboard with invalid grid.obj_id long_string`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDashboard,

        title: `Dashboard_${Date.now()}`,
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
        grid: [{ obj_id: faker.random.alphaNumeric(25), x: 0, y: 0, width: 4, height: 4 }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(
      `Value is not valid. Value's byte_size is more than maximum allowed: 24 or Value `,
    );
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify dashboard with invalid grid.x '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.DASHBOARD,
          obj_id: newDashboard,

          title: `Dashboard_${Date.now()}`,
          description: 'test',
          time_range: {
            select: 'online',
            start: 1597847260,
            stop: 1597847260,
            timezone_offset: -180,
          },
          grid: [{ obj_id: `103065`, x: input, y: 0, width: 4, height: 4 }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify dashboard with invalid grid.y '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.DASHBOARD,
          obj_id: newDashboard,

          title: `Dashboard_${Date.now()}`,
          description: 'test',
          time_range: {
            select: 'online',
            start: 1597847260,
            stop: 1597847260,
            timezone_offset: -180,
          },
          grid: [{ obj_id: `103065`, x: 0, y: input, width: 4, height: 4 }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify dashboard with invalid grid.width '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.DASHBOARD,
          obj_id: newDashboard,

          title: `Dashboard_${Date.now()}`,
          description: 'test',
          time_range: {
            select: 'online',
            start: 1597847260,
            stop: 1597847260,
            timezone_offset: -180,
          },
          grid: [{ obj_id: `103065`, x: 0, y: 0, width: input, height: 4 }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify dashboard with invalid grid.height '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.DASHBOARD,
          obj_id: newDashboard,

          title: `Dashboard_${Date.now()}`,
          description: 'test',
          time_range: {
            select: 'online',
            start: 1597847260,
            stop: 1597847260,
            timezone_offset: -180,
          },
          grid: [{ obj_id: `103065`, x: 0, y: 0, width: 4, height: input }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't show dashboard with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.DASHBOARD,
          company_id,
          obj_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't show dashboard with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.DASHBOARD,
          company_id: input,
          obj_id: newDashboard,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't list dashboard with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.DASHBOARD,
          company_id,
          obj_id: input,
          list_obj: 'group',
          stage_id: newStage,
          project_id: newStage,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't list dashboard with invalid list_obj '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,
        list_obj: input,
        stage_id: newStage,
        project_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't list dashboard with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.DASHBOARD,
          company_id: input,
          obj_id: newDashboard,
          list_obj: 'group',
          stage_id: newStage,
          project_id: newStage,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't link dashboard with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.DASHBOARD,
          company_id,
          obj_id: input,

          obj_to: 'user',
          obj_to_id: ApiKey2,
          privs: [{ type: 'create', list_obj: ['all'] }],
          stage_id: newStage,
          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't link dashboard with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.DASHBOARD,
          company_id: input,
          obj_id: newDashboard,

          obj_to: 'user',
          obj_to_id: ApiKey2,
          privs: [{ type: 'create', list_obj: ['all'] }],
          stage_id: newStage,
          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...arrayTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't link dashboard with invalid privs '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.DASHBOARD,
          company_id,
          obj_id: newDashboard,

          obj_to: 'user',
          obj_to_id: ApiKey2,
          privs: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't link dashboard with invalid privs.type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        obj_to: 'user',
        obj_to_id: ApiKey2,
        privs: [{ type: input, list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...arrayTestCases, ...undefinedTestCase, ...securityTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't link dashboard with invalid privs.list_obj '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        obj_to: 'user',
        obj_to_id: ApiKey2,
        privs: [{ type: 'create', list_obj: input }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...boolTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't link dashboard with invalid is_need_to_notify '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.DASHBOARD,
          company_id,
          obj_id: newDashboard,

          obj_to: 'user',
          obj_to_id: ApiKey2,
          is_need_to_notify: input,
          privs: [{ type: 'create', list_obj: ['all'] }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...securityTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't link dashboard with invalid obj_to '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        obj_to: input,
        obj_to_id: ApiKey2,
        is_need_to_notify: true,
        privs: [{ type: 'create', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...boolTestCases, ...securityTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't link dashboard with invalid obj_to_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.DASHBOARD,
          company_id,
          obj_id: newDashboard,

          obj_to: 'user',
          obj_to_id: input,
          is_need_to_notify: true,
          privs: [{ type: 'create', list_obj: ['all'] }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't favorite dashboard with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.FAVORITE,
          obj: OBJ_TYPE.DASHBOARD,
          obj_id: input,

          favorite: true,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...boolTestCases, ...undefinedTestCase, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't favorite dashboard with invalid favorite '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.FAVORITE,
          obj: OBJ_TYPE.DASHBOARD,
          obj_id: newDashboard,

          favorite: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't delete dashboard with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.DASHBOARD,
          obj_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't restore dashboard with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.RESTORE,
          obj: OBJ_TYPE.DASHBOARD,
          obj_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't destroy dashboard with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DESTROY,
          obj: OBJ_TYPE.DASHBOARD,
          obj_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDashboard,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
        company_id,
      }),
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: `${newProject}`,
        company_id,
      }),
    );
  });
});
