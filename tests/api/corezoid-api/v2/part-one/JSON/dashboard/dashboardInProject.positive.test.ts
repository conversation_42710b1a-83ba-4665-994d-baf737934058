import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import createSchema from '../../../../schemas/v2/actions-objects/createDashboard.schema.json';
import deleteSchema from '../../../../schemas/v2/actions-objects/deleteObject.schema.json';
import destroySchema from '../../../../schemas/v2/actions-objects/destroyObject.schema.json';
import restoreSchema from '../../../../schemas/v2/actions-objects/restoreObject.schema.json';
import modifySchema from '../../../../schemas/v2/actions-objects/modifyDashboard.schema.json';
import favoriteSchema from '../../../../schemas/v2/actions-objects/favoriteObject.schema.json';
import showSchema from '../../../../schemas/v2/actions-objects/showDashboard.schema.json';
import copySync from '../../../../schemas/v2/actions-objects/copyObjectSync.schema.json';
import linkSchema from '../../../../schemas/v2/actions-objects/linkMoveFolder.schema.json';
import linkKeySchema from '../../../../schemas/v2/actions-objects/linkKeyDashboard.schema.json';
import listSchema from '../../../../schemas/v2/actions-objects/listDashboard.schema.json';
import { makeRequestWithRetries, MyApiResponse } from '../../../../../../../utils/requestRetries';

describe('Dashboard in project (positive)', () => {
  let api: ApiKeyClient;
  let newApi: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let company_id: any;
  let ApiKey2: string | number;
  let newFolder: string | number;
  let newDashboard: string | number;
  let newDashboard1: string | number;
  let copyDashboard: string | number;
  let chart_id: string;
  let group_id: number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,

        title: `key_${Date.now()}`,
        logins: [{ type: 'api' }],
      }),
    );
    const user = responseKey.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newApi = application.getApiKeyClient(newApiKey);
    ApiKey2 = +newApiKey.id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: `project${Date.now()}`,
        description: 'test',
        stages: ['production'],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;
    newStage = response.body.ops[0].stages[0];

    const responseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,

        title: `Folder_${Date.now()}`,
        folder_id: newStage,
        obj_type: 0,
        description: 'test',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    newFolder = responseFolder.body.ops[0].obj_id;

    const responseGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,

        obj_type: `admins`,
        title: 'Group',
      }),
    );
    group_id = responseGroup.body.ops[0].obj_id;

    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
        company_id,

        group_id,
        level: 1,
      }),
    );
    expect(responseLink.status).toBe(200);
  });

  test('should create dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: newStage,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    newDashboard = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should create dashboard without parameters', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: undefined,
        folder_id: undefined,
        obj_type: undefined,
        description: undefined,
        time_range: undefined,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    newDashboard1 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should create dashboard with description=null', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: newStage,
        description: null,
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: `${Date.now()}`,
          timezone_offset: -120,
        },
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    newDashboard = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should find dashboard in list_folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newStage,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDashboard })]),
    );
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newDashboard)?.is_owner).toEqual(
      true,
    );
  });

  test('should create chart', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHART,

        obj_type: 'table',
        series: [],
        dashboard_id: newDashboard,
        name: 'chart',
      }),
    );
    expect(response.status).toBe(200);
    chart_id = response.body.ops[0].obj_id;
  });

  test('should modify dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        time_range: { select: 'online', timezone_offset: -180 },
        title: 'titleModifyDashboard',
        description: 'descriptionModifyDashboard',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(modifySchema, response.body);
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newStage,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newDashboard)?.title).toEqual(
      'titleModifyDashboard',
    );
    expect(
      (responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newDashboard)?.description,
    ).toEqual('descriptionModifyDashboard');
  });

  test.each([
    `online`,
    `last10minute`,
    `today`,
    `lastHour`,
    `last24hour`,
    `lastWeek`,
    `lastMonth`,
    `previous_hour`,
    `previous_day`,
    `previous_week`,
    `previous_month`,
  ])(`should modify dashboard with select '%s'`, async select => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        time_range: { select, timezone_offset: -180 },
        title: 'titleModifyDashboard',
        description: 'descriptionModifyDashboard',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(modifySchema, response.body);

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].obj_id).toBe(newDashboard);
    expect(responseShow.body.ops[0].time_range.select).toBe(select);
  });

  test('should modify dashboard with required param', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDashboard,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(modifySchema, response.body);
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newStage,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newDashboard)?.title).toEqual(
      'titleModifyDashboard',
    );
  });

  test('should modify dashboard with grid', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        time_range: { select: 'fromTo', timezone_offset: -180, start: 1656075346, stop: 1656075346 },
        grid: [{ obj_id: chart_id, x: 0, y: 0, width: 4, height: 4 }],
        description: null,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(modifySchema, response.body);
  });

  test('should favorite dashboard true', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDashboard,

        favorite: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(favoriteSchema, response.body);
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newStage,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newDashboard)?.favorite).toEqual(
      true,
    );
  });

  test('should favorite dashboard false', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        favorite: false,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(favoriteSchema, response.body);
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newStage,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newDashboard)?.favorite).toEqual(
      false,
    );
  });

  test('should show dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDashboard,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    expect(response.body.ops[0].title).toBe('titleModifyDashboard');
    expect(response.body.ops[0].description).toBe('descriptionModifyDashboard');
    expect(response.body.ops[0].favorite).toBe(false);
    expect(response.body.ops[0].folder_id).toBe(newStage);
    expect(response.body.ops[0].privs).toBeArray();
    expect(response.body.ops[0].chart_list).toBeArray();
    expect(response.body.ops[0].chart_list).toBeArray();
    expect(response.body.ops[0].time_range.timezone_offset).toBe(-180);
    expect(response.body.ops[0].time_range.select).toBe(`fromTo`);
    expect(response.body.ops[0].grid[0].obj_id).toBe(chart_id);
    expect(response.body.ops[0].grid[0].x).toBe(0);
    expect(response.body.ops[0].grid[0].y).toBe(0);
    expect(response.body.ops[0].grid[0].width).toBe(4);
    expect(response.body.ops[0].grid[0].height).toBe(4);
    SchemaValidator.validate(showSchema, response.body);
  });

  test('should link key to dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        is_need_to_notify: true,
        obj_to: 'user',
        obj_to_id: ApiKey2,
        privs: [{ type: 'create', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toBe('dashboard');
    expect(response.body.ops[0].obj_to_type).toBe('user');
    expect(response.body.ops[0].action_type).toBe('link');
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(linkKeySchema, response.body);

    const responseShow = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDashboard,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].obj_id).toBe(newDashboard);
  });

  test('should link group to dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard1,

        is_need_to_notify: false,
        obj_to: 'group',
        obj_to_id: group_id,
        privs: [
          { type: 'create', list_obj: ['all'] },
          { type: 'modify', list_obj: ['all'] },
          { type: 'delete', list_obj: ['all'] },
          { type: 'view', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toBe('dashboard');
    expect(response.body.ops[0].obj_to_type).toBe('group');
    expect(response.body.ops[0].action_type).toBe('link');
    expect(response.body.ops[0].obj_id).toBe(newDashboard1);
    SchemaValidator.validate(linkKeySchema, response.body);
  });

  test('should unlink key to dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        is_need_to_notify: true,
        obj_to: 'user',
        obj_to_id: ApiKey2,
        privs: [],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toBe('dashboard');
    expect(response.body.ops[0].obj_to_type).toBe('user');
    expect(response.body.ops[0].action_type).toBe('unlink');
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(linkKeySchema, response.body);

    const responseShow = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDashboard,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].description).toBe(`Access denied`);
  });

  test('should list dashboard list_obj:group', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDashboard,
        list_obj: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('dashboard');
    expect(response.body.ops[0].list_obj).toBe('group');
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should copy dashboard sync', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newDashboard,

        obj_type: 'dashboard',
        folder_id: 0,
        title: 'copyDash',
        ignore_errors: true,
        async: false,
        from_company_id: company_id,
        to_company_id: company_id,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].scheme[0].old_obj_id).toBe(newDashboard);
    expect(response.body.ops[0].scheme[0].obj_type).toBe('dashboard');
    expect(response.body.ops[0].scheme[0].title).toBe('copyDash');
    copyDashboard = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copySync, response.body);
  });

  test('should delete dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDashboard,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should find dashboard in list_folder filter_deleted', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
        filter: 'deleted',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDashboard })]),
    );
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newDashboard)?.is_owner).toEqual(
      true,
    );
  });

  test('should not find dashboard in list_folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newStage,

        roject_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newDashboard)).toBeEmpty();
  });

  test('should restore dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDashboard,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(restoreSchema, response.body);
  });

  test('should find dashboard in list_folder', async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return response.status === 200 && response.body.ops[0].list.some((item: any) => item.obj_id === newDashboard);
    }
    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.FOLDER,
          company_id,
          obj_id: newStage,
          roject_id: newProject,
          stage_id: newStage,
        }),
      );
    }, checkConditions);
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newDashboard)?.is_owner).toEqual(
      true,
    );
  });

  test('should not find dashboard in list_folder filter_deleted', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
        filter: 'deleted',
      }),
    );
    expect(response.status).toBe(200);
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newDashboard)).toBeEmpty();
  });

  test('should link_folder dashboard to folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newDashboard,

        obj_type: 'dashboard',
        parent_id: newStage,
        folder_id: newFolder,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    expect(response.body.ops[0].obj_type).toBe('dashboard');
    expect(response.body.ops[0].action_type).toBe('move');
    expect(response.body.ops[0].to_folder).toBe(newFolder);
    expect(response.body.ops[0].from_folder).toBe(newStage);
    SchemaValidator.validate(linkSchema, response.body);
  });

  test('should not find dashboard in list_folder 0', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newDashboard)).toBeEmpty();
  });

  test('should find dashboard in list_folder newFolder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newFolder,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDashboard })]),
    );
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newDashboard)?.is_owner).toEqual(
      true,
    );
  });

  test('should destroy dashboard', async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        obj_id: newDashboard,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDashboard,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newDashboard);
    SchemaValidator.validate(destroySchema, response.body);
  });

  test('should not find dashboard in list_folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newDashboard)).toBeEmpty();
  });

  test('should not find dashboard in list_folder filter_deleted', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
        filter: 'deleted',
      }),
    );
    expect(response.status).toBe(200);
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newDashboard)).toBeEmpty();
  });

  afterAll(async () => {
    const responseKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
        company_id,

        group_id: 0,
        level: '',
      }),
    );
    expect(responseKey.status).toBe(200);

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: `${newProject}`,
        company_id,
      }),
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: copyDashboard,
        company_id,
      }),
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDashboard1,
        company_id,
      }),
    );

    const ResponseDeleteGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        obj_id: group_id,
        company_id,
      }),
    );
    expect(ResponseDeleteGroup.status).toBe(200);
  });
});
