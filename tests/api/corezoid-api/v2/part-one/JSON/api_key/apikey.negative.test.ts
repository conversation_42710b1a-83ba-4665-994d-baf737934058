import { application } from '../../../../../../../application/Application';
import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('API Key (negative)', () => {
  let api: ApiKeyClient;
  let company_id: any;

  beforeAll(async () => {
    const apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
  });

  test.each([1, 1234567890, 0, true])(`shouldn't create api key with invalid 'company_id': %s`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id: param,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual('Value is not valid');
    expect(response.body.ops[0].key).toEqual('company_id');
    expect(response.body.ops[0].value).toEqual(param);
  });

  test.each([
    ['test', 'You do not have permission to perform this action'],
    ['i000000000', 'You do not have permission to perform this action'],
  ])(`shouldn't create api key with invalid company_id '%s'`, async (param, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id: param,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([null])(`shouldn't create api key with invalid title %s`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: param,
        logins: [{ type: 'api' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(`Value 'null' is not valid. Type of value is not 'binary'`);
    expect(response.body.ops[0].key).toEqual('title');
    expect(response.body.ops[0].value).toEqual(param);
  });

  test(`shouldn't create api key with invalid title undefined`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        title: undefined,
        logins: [{ type: 'api' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(`Key 'title' is required`);
  });

  test.each([1234, 0, true, 'group', null])(`shouldn't create api key with invalid 'type': %s`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        title: 'API',
        logins: [{ type: param }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual('Bad login type or login is required');
  });

  test(`shouldn't create api key with invalid type undefined`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        title: 'API',
        logins: [{ type: undefined }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual('Bad login type or login is required');
  });

  test(`shouldn't create api key with invalid type 'corezoid'`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        title: 'API',
        logins: [{ type: 'corezoid' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual('Bad login type or login is required');
  });

  test(`shouldn't delete api key with invalid obj_id 0`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(`User 0 not found.`);
  });

  test.each([
    [true, 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [null, `Value is not valid`],
    ['test', `Value is not valid`],
    [undefined, `Key 'obj_id' is required`],
  ])(`shouldn't show api_key with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.API_KEY,
        obj_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([true, 'test', null])(`shouldn't delete api key with invalid obj_id %s`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: param,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(`Value is not valid`);
    expect(response.body.ops[0].key).toEqual('obj_id');
    expect(response.body.ops[0].value).toEqual(param);
  });

  test(`shouldn't delete api key with invalid obj_id 'undefined'`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: undefined,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(`Key 'obj_id' is required`);
  });
});
