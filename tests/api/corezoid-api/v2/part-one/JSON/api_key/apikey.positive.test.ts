import { application } from '../../../../../../../application/Application';
import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createSchema from '../../../../schemas/v2/users-groups/createApiKeySchema.json';
import showSchema from '../../../../schemas/v2/users-groups/showApiKeysSchema.json';
import deleteSchema from '../../../../schemas/v2/users-groups/deleteApiKeySchema.json';
import showInConvSchema from '../../../../schemas/v2/users-groups/showApiKeysInConvSchema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';

let conv_id: number;

describe('API Key (positive)', () => {
  let api: ApiKeyClient;
  let newApiKey: string | number;
  let company_id: any;

  beforeAll(async () => {
    const apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = null;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Logic_API_Copy_Create`);
    conv_id = response.body.ops[0].obj_id;
  });

  test('should create api key', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('user');
    newApiKey = response.body.ops[0].users[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should get list of api keys', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        filter: 'api_key',
        sort: 'title',
        order: 'asc',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('company_users');
    SchemaValidator.validate(showSchema, response.body);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newApiKey })]));
  });

  test('should link api keys', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        obj_to: 'user',
        obj_to_id: newApiKey,
        privs: [{ type: 'create', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj_to_id).toBe(newApiKey);
  });

  test('should show api keys', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.API_KEY,
        obj_id: conv_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj_id).toBe(newApiKey);
    SchemaValidator.validate(showInConvSchema, response.body);
  });

  test('should delete existing api key', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: newApiKey,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(deleteSchema, response.body);
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(response.status).toBe(200);
  });
});
