import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import listSchema from '../../../../schemas/v2/actions-objects/listFolderBranch.schema.json';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';

describe('Folder_branch (positive)', () => {
  let api: ApiKeyClient;
  let apiS: ApiKeyClient;
  let newApi: ApiKeyClient;
  let apikey: ApiKey;
  let apikeyS: ApiKey;
  let obj_idKey: any;
  let newProject: string | number;
  let newStage: string | number;
  let company_id: any;
  let folder_id: number;
  let folder_id_2: number;
  let folder_id_3: number;
  let folder_id_4: number;
  let folder_id_5: number;
  let project_short_name: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = `project${Date.now()}`;

    apikeyS = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apikeyS);

    const responseUser = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'APInewOwner',
        logins: [{ type: 'api' }],
      }),
    );
    expect(responseUser.status).toBe(200);
    expect(responseUser.body.ops[0].proc).toBe('ok');
    const userOw = responseUser.body;
    const newApikey = {
      key: `${userOw.ops[0].users[0].logins[0].obj_id}`,
      secret: userOw.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${userOw.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newApi = application.getApiKeyClient(newApikey);
    obj_idKey = +newApikey.id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: ['production'],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;
    newStage = response.body.ops[0].stages[0];

    const responseFolderInCompany = await requestCreateObj(api, OBJ_TYPE.FOLDER, company_id, `Folder_${Date.now()}`);
    folder_id = responseFolderInCompany.body.ops[0].obj_id;

    const responseFolderInStage = await requestCreateObj(
      api,
      OBJ_TYPE.FOLDER,
      company_id,
      `Folder_${Date.now()}`,
      newStage,
    );
    folder_id_2 = responseFolderInStage.body.ops[0].obj_id;

    const responseFolder3InStage = await requestCreateObj(
      api,
      OBJ_TYPE.FOLDER,
      company_id,
      `Folder_${Date.now()}`,
      newStage,
    );
    folder_id_3 = responseFolder3InStage.body.ops[0].obj_id;

    const responseFolder4InStage = await requestCreateObj(
      api,
      OBJ_TYPE.FOLDER,
      company_id,
      `Folder_${Date.now()}`,
      newStage,
    );
    folder_id_4 = responseFolder4InStage.body.ops[0].obj_id;

    const responseFolder5InCompany = await requestCreateObj(
      newApi,
      OBJ_TYPE.FOLDER,
      company_id,
      `Folder_${Date.now()}`,
    );
    folder_id_5 = responseFolder5InCompany.body.ops[0].obj_id;
  });

  test('should list folder branch with required param', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER_BRANCH,
        obj_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder branch in company', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER_BRANCH,
        obj_id: 0,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: folder_id })]));
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder branch in stage', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER_BRANCH,
        obj_id: newStage,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: folder_id_2 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: folder_id_3 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: folder_id_4 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder branch in company with other owner_id', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER_BRANCH,
        obj_id: 0,
        company_id,

        owner_id: obj_idKey,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: folder_id_2 })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: folder_id_3 })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: folder_id_4 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: folder_id_5 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder branch in company, - req from superadmin', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER_BRANCH,
        obj_id: newStage,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: folder_id_2 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: folder_id_3 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: folder_id_4 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  afterAll(async () => {
    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(responseProject.status).toBe(200);

    const responseFolder = await requestDeleteObj(api, OBJ_TYPE.FOLDER, folder_id, company_id);
    expect(responseFolder.status).toBe(200);

    const responseFolder2 = await requestDeleteObj(newApi, OBJ_TYPE.FOLDER, folder_id_5, company_id);
    expect(responseFolder2.status).toBe(200);

    const responseUser = await requestDeleteObj(apiS, OBJ_TYPE.USER, obj_idKey, company_id);
    expect(responseUser.status).toBe(200);
  });
});
