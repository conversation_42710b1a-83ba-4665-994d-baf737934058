import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { integerTestCases, undefinedTestCase, companyTestCases, securityTestCases } from '../../../../../negativeCases';

describe('Folder_branch (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let valuesToSkip: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
  });

  test.each([...companyTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't list folder branch with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.FOLDER_BRANCH,
          obj_id: 0,
          company_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0];

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't list folder branch with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER_BRANCH,
        obj_id: input,
        company_id,

        owner_id: 123,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't list folder branch with invalid owner_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER_BRANCH,
        obj_id: 0,
        company_id,

        owner_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });
});
