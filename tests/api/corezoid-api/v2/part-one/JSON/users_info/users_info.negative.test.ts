import { application } from '../../../../../../../application/Application';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { User } from '../../../../../../../infrastructure/model/User';
import { integerTestCases, stringTestCases, undefinedTestCase } from '../../../../../negativeCases';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { ApiUserClient } from '../../../../../../../application/api/ApiUserClient';
import { createAuthUser, Method } from '../../../../../../../utils/request';

describe('Used Traff (negative)', () => {
  const valuesToSkip: any = [0, -1];
  let user: User;
  let cookieSUser: any;
  let host: string;
  let user_id: number | string;
  let apiUser: ApiUserClient;

  beforeAll(async () => {
    await ConfigurationManager.getConfiguration().initialize();
    const config = ConfigurationManager.getConfiguration();
    user = await application.getAuthorizedUser({ company: {} }, 1);
    cookieSUser = createAuthUser(user.cookieUser, 'cookie');
    host = config.getApiUrl();
    apiUser = await application.getApiUserClient(user);

    const responselist = await apiUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        sort: 'title',
        order: 'asc',
        filter: 'user',
      }),
    );
    expect(responselist.status).toBe(200);
    user_id = responselist.body.ops[0].list[0].obj_id;
  });

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't show user_info with invalid obj_id '%s'`, async (input, errors) => {
    const response = await cookieSUser.request({
      method: Method.POST,
      url: `${host}system/users_info`,
      data: { obj_type: 'user', obj_id: input },
    });
    expect(response.status).toBe(200);
    const error = response.data;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([-1, 0])(`shouldn't show user_info with invalid obj_id`, async input => {
    const response = await cookieSUser.request({
      method: Method.POST,
      url: `${host}system/users_info`,
      data: { obj_type: 'user', obj_id: input },
    });
    expect(response.status).toBe(200);
    expect(response.data.account_id).toContain(`not_found`);
    expect(response.data.corezoid_id).toContain(`not_found`);
  });

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't show user_info with invalid obj_type '%s'`,
    async (input, errors) => {
      const response = await cookieSUser.request({
        method: Method.POST,
        url: `${host}system/users_info`,
        data: { obj_type: input, obj_id: user_id },
      });
      expect(response.status).toBe(200);
      const error = response.data.description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't show user_info with invalid obj_type '%s'`,
    async (input, errors) => {
      const response = await cookieSUser.request({
        method: Method.POST,
        url: `${host}system/users_info`,
        data: { obj_type: input, obj_id: user_id },
      });
      expect(response.status).toBe(200);
      const error = response.data;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([undefined, '', null])(`shouldn't show user_info with invalid data '%s'`, async input => {
    const response = await cookieSUser.request({
      method: Method.POST,
      url: `${host}system/users_info`,
      data: input,
    });
    expect(response.status).toBe(400);
    expect(response.data).toContain(`Missing body`);
  });

  test.each([{}, []])(`shouldn't show user_info with invalid data '%s'`, async input => {
    const response = await cookieSUser.request({
      method: Method.POST,
      url: `${host}system/users_info`,
      data: input,
    });
    expect(response.status).toBe(200);
    expect(response.data).toContain(`Incorrect body`);
  });

  test.each(['test'])(`shouldn't show user_info with invalid data '%s'`, async input => {
    const response = await cookieSUser.request({
      method: Method.POST,
      url: `${host}system/users_info`,
      data: input,
    });
    expect(response.status).toBe(200);
    expect(response.data).toContain(`Invalid JSON`);
  });
});
