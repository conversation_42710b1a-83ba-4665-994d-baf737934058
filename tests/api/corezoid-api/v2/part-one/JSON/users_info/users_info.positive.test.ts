import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { User } from '../../../../../../../infrastructure/model/User';
import { application } from '../../../../../../../application/Application';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import users_infoScheme from '../../../../schemas/v2/users_info/users_infoSchema.json';
import users_infoLoginScheme from '../../../../schemas/v2/users_info/users_infoLoginSchema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { ApiUserClient } from '../../../../../../../application/api/ApiUserClient';
import { createAuthUser, Method } from '../../../../../../../utils/request';

describe('User_status (positive)', () => {
  let host: string;
  let userS: User;
  let cookieSUser: any;
  let user: User;
  let cookieUser: any;
  let user_id: string | number;
  let login: string;
  let apiUser: ApiUserClient;

  beforeAll(async () => {
    await ConfigurationManager.getConfiguration().initialize();
    const config = ConfigurationManager.getConfiguration();
    userS = await application.getAuthorizedUser({ company: {} }, 1);
    cookieSUser = createAuthUser(userS.cookieUser, 'cookie');
    host = config.getApiUrl();
    apiUser = await application.getApiUserClient(userS);

    const responselist = await apiUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        sort: 'title',
        order: 'asc',
        filter: 'user',
      }),
    );
    expect(responselist.status).toBe(200);
    user_id = responselist.body.ops[0].list[0].obj_id;
    login = responselist.body.ops[0].list[0].logins[0].login;
  });

  test('should create requests for users_info by user_id', async () => {
    const response = await cookieSUser.request({
      method: Method.POST,
      url: `${host}system/users_info`,
      data: { obj_type: 'user', obj_id: user_id },
    });
    expect(response.status).toBe(200);
    expect(response.data.corezoid_id).toBe(user_id);
    expect(response.data.login).toBe(login);
    SchemaValidator.validate(users_infoScheme, response.data);
  });

  test('should create requests for users_info by login', async () => {
    const response = await cookieSUser.request({
      method: Method.POST,
      url: `${host}system/users_info`,
      data: { obj_type: 'login', obj_id: login },
    });
    expect(response.status).toBe(200);
    expect(response.data.corezoid_id).toBe(user_id);
    expect(response.data.obj_id).toBe(login);
    SchemaValidator.validate(users_infoLoginScheme, response.data);
  });

  test('should create requests for users_info (have not superadmin privs)', async () => {
    user = await application.getAuthorizedUser({ company: {} }, 2);
    cookieUser = createAuthUser(user.cookieUser, 'cookie');

    const response = await cookieUser.request({
      method: Method.POST,
      url: `${host}system/users_info`,
      data: { obj_type: 'login', obj_id: login },
    });
    expect(response.status).toBe(200);
    expect(response.data).toContain(`you haven't superadmin privs`);
  });
});
