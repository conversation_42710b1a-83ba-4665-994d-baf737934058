import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import faker from 'faker';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  maxLength,
  stringNotValidTestCases,
  minLength,
  securityTestCases,
  companyTestCases,
  boolTestCases,
} from '../../../../../negativeCases';

describe('Aliases (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let valuesToSkip: any;
  let newProject: string | number;
  let newStage: string | number;
  let newDash: string | number;
  let newConv: string | number;
  let newAlias: string | number;
  let company_id: any;
  let stage_short_name: string;
  let project_short_name: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    stage_short_name = `stage-${Date.now()}`;
    project_short_name = `project-${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
        project_short_name: `test`,
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `Conv_${Date.now()}`,
        conv_type: 'state',
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    newConv = responseConv.body.ops[0].obj_id;

    const responseDash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        project_id: newProject,
        stage_id: newStage,
        title: `Dash_${Date.now()}`,
      }),
    );
    newDash = responseDash.body.ops[0].obj_id;

    const responseAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: `modify`,
        title: 'Alias2',
        description: 'test',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    newAlias = responseAlias.body.ops[0].obj_id;

    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
        obj_to_id: newConv,
        obj_to_type: `conv`,
        link: true,
        project_id: newProject,
      }),
    );
    expect(responseLink.status).toBe(200);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        obj_alias: `modify`,
        ref: `123`,
        data: { a: '1' },
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    expect(responseTask.status).toBe(200);
  });

  test.each([...companyTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't create alias with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ALIAS,
          company_id: input,
          short_name: 'test',
          description: 'test',
          title: 'test',
          project_id: newProject,
          stage_id: newStage,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create alias with invalid project_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: 'test',
        description: 'test',
        title: 'test',
        project_id: input,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create alias with invalid stage_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: 'test',
        description: 'test',
        title: 'test',
        project_id: newProject,
        stage_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...maxLength, ...minLength].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create alias with invalid title '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: 'test',
        description: 'test',
        title: input,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...maxLength, ...minLength].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create alias with invalid short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: input,
        description: 'test',
        title: 'test',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...maxLength, ...minLength].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create alias with invalid short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: input,
        description: 'test',
        title: 'test',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [null];

  test.each(
    [...stringTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create alias with invalid description '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: 'test',
        description: input,
        title: 'test',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test(`shouldn't create alias with max description '%s'`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: 'test',
        description: faker.random.alphaNumeric(2001),
        title: 'test',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(
      `Value is not valid. Value's byte_size is more than maximum allowed: 2000`,
    );
  });

  valuesToSkip = [null];

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create alias with invalid obj_to_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: 'test',
        description: 'test',
        title: 'test',
        project_id: newProject,
        stage_id: newStage,
        obj_to_id: input,
        obj_to_type: 'conv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...stringTestCases, ...undefinedTestCase, ...minLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't create alias with invalid project_short_name '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          short_name: 'test',
          description: 'test',
          title: 'test',
          project_short_name: input,
          stage_short_name,
          obj_to_id: 1,
          obj_to_type: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase, ...minLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't create alias with invalid stage_short_name '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          short_name: 'test',
          description: 'test',
          title: 'test',
          project_short_name,
          stage_short_name: input,
          obj_to_id: 1,
          obj_to_type: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't show alias with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.ALIAS,
          company_id: input,
          obj_id: newAlias,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't show alias with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          obj_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify alias with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          obj_id: input,
          short_name: `aliasmodify-${Date.now()}`,
          description: `aliasDeskModify-${Date.now()}`,
          title: `aliasTitleModify-${Date.now()}`,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify alias with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.ALIAS,
          company_id: input,
          obj_id: newAlias,
          short_name: `aliasmodify-${Date.now()}`,
          description: `aliasDeskModify-${Date.now()}`,
          title: `aliasTitleModify-${Date.now()}`,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...securityTestCases, ...minLength].map(({ input, errors }) => [
      input,
      errors,
    ]),
  )(`shouldn't modify alias with invalid short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newAlias,
        short_name: input,
        description: `aliasDeskModify-${Date.now()}`,
        title: `aliasTitleModify-${Date.now()}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [null];

  test.each(
    [...stringTestCases, ...minLength, ...maxLength]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify alias with invalid title '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newAlias,
        short_name: `alias-${Date.now()}`,
        description: `aliasDeskModify-${Date.now()}`,
        title: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [null];

  test.each(
    [...stringTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify alias with invalid description '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newAlias,
        short_name: `alias-${Date.now()}`,
        description: input,
        title: `alias-${Date.now()}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test(`shouldn't modify alias with max description '%s'`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newAlias,
        short_name: 'test',
        description: faker.random.alphaNumeric(2001),
        title: 'test',
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(
      `Value is not valid. Value's byte_size is more than maximum allowed: 2000`,
    );
  });

  test.each([...companyTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't favorite alias with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.FAVORITE,
          obj: OBJ_TYPE.ALIAS,
          company_id: input,
          obj_id: newAlias,
          favorite: true,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0];

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't favorite alias with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: input,
        favorite: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...boolTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't favorite alias with invalid favorite '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.FAVORITE,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          obj_id: newAlias,
          favorite: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't favorite link with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.ALIAS,
          company_id: input,
          obj_id: newAlias,
          link: true,
          obj_to_id: newDash,
          obj_to_type: 'dashboard',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't link alias with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          obj_id: input,
          link: true,
          obj_to_id: newDash,
          obj_to_type: 'dashboard',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't link alias with invalid obj_to_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          obj_id: newAlias,
          link: true,
          obj_to_id: input,
          obj_to_type: 'dashboard',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't link alias with invalid obj_to_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newAlias,
        link: true,
        obj_to_id: newDash,
        obj_to_type: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...boolTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't link alias with invalid link '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          obj_id: newAlias,
          link: input,
          obj_to_id: newDash,
          obj_to_type: 'dashboard',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't upsert with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.UPSERT,
          obj: OBJ_TYPE.ALIAS,
          company_id: input,
          obj_id: newAlias,
          short_name: 'test',
          project_id: newProject,
          stage_id: newStage,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't upsert alias with invalid short_name '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.UPSERT,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          obj_id: newAlias,
          short_name: input,
          project_id: newProject,
          stage_id: newStage,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [null];

  test.each(
    [...stringTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't upsert alias with invalid description '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.UPSERT,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newAlias,
        short_name: 'test',
        description: input,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test(`shouldn't upsert alias with max description '%s'`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.UPSERT,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newAlias,
        short_name: 'test',
        description: faker.random.alphaNumeric(2001),
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(
      `Value is not valid. Value's byte_size is more than maximum allowed: 2000`,
    );
  });

  valuesToSkip = [null];

  test.each(
    [...stringTestCases, ...maxLength]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't upsert alias with invalid title '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.UPSERT,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newConv,
        short_name: 'test',
        description: 'test',
        title: input,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [null];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't upsert alias with invalid obj_to_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.UPSERT,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newConv,
        short_name: 'test',
        description: 'test',
        title: 'test',
        obj_to_id: input,
        obj_to_type: 'conv',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't upsert alias with invalid project_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.UPSERT,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newConv,
        short_name: 'test',
        description: 'test',
        title: 'test',
        project_id: input,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't upsert alias with invalid stage_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.UPSERT,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newConv,
        short_name: 'test',
        description: 'test',
        title: 'test',
        project_id: newProject,
        stage_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...maxLength]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't upsert alias with invalid stage_short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.UPSERT,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newConv,
        short_name: 'test',
        description: 'test',
        title: 'test',
        project_short_name,
        stage_short_name: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [null];

  test.each(
    [...stringTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't upsert alias with invalid project_short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.UPSERT,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newConv,
        short_name: 'test',
        description: 'test',
        title: 'test',
        project_short_name: input,
        stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [1234, `Value '1234' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'company_id' is required`],
  ])(`shouldn't list aliases with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid project_id or Key 'project_short_name' is required`],
    ['test', `Value 'test' is not valid project_id or Key 'project_short_name' is required`],
    [true, `Value 'true' is not valid project_id or Key 'project_short_name' is required`],
    [{}, `Value '{[]}' is not valid project_id or Key 'project_short_name' is required`],
    [[], `Value '' is not valid project_id or Key 'project_short_name' is required`],
    ['', `Value '' is not valid project_id or Key 'project_short_name' is required`],
  ])(`shouldn't list aliases with invalid project_id '%s'`, async (project_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Stage not found`],
    ['test', `Project is not found`],
    [true, `Stage not found`],
    [{}, `Stage not found`],
    [[], `Stage not found`],
    ['', `Stage not found`],
    [undefined, `Stage not found`],
  ])(`shouldn't list aliases with invalid project_short_name '%s'`, async (project_short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_short_name,
        stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid stage_id or Key 'stage_short_name' is required`],
    ['test', `Value 'test' is not valid stage_id or Key 'stage_short_name' is required`],
    [true, `Value 'true' is not valid stage_id or Key 'stage_short_name' is required`],
    [{}, `Value '{[]}' is not valid stage_id or Key 'stage_short_name' is required`],
    [[], `Value '' is not valid stage_id or Key 'stage_short_name' is required`],
    ['', `Value '' is not valid stage_id or Key 'stage_short_name' is required`],
    [1, `Object stage with id 1 does not exist`],
    [-1, `Object stage with id -1 does not exist`],
    [1234, `Object stage with id 1234 does not exist`],
  ])(`shouldn't list aliases with invalid stage_id '%s'`, async (stage_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id: newProject,
        stage_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([['test', `Stage not found`]])(
    `shouldn't list aliases with invalid stage_short_name '%s'`,
    async (stage_short_name, reason) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.ALIASES,
          company_id,
          project_short_name,
          stage_short_name,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [1234, `Value '1234' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't GET hash with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: `alias`,
        alias_id: newAlias,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [1, `Alias not found`],
    [1234, `Alias not found`],
    [undefined, `Key 'alias_id' is required`],
  ])(`shouldn't GET hash alias with invalid alias_id (description) '%s'`, async (alias_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: `alias`,
        alias_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value is not valid`],
    ['test', `Value is not valid`],
    [true, `Value is not valid`],
    [{}, `Value is not valid`],
    [[], `Value is not valid`],
    ['', `Value is not valid`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
  ])(`shouldn't GET hash alias with invalid alias_id (message) '%s'`, async (alias_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: `alias`,
        alias_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [1234, `Value '1234' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't delete hash with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: `alias`,
        alias_id: newAlias,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [1, `Alias not found`],
    [1234, `Alias not found`],
    [undefined, `Key 'alias_id' is required`],
  ])(`shouldn't delete hash alias with invalid alias_id (description) '%s'`, async (alias_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: `alias`,
        alias_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value is not valid`],
    ['test', `Value is not valid`],
    [true, `Value is not valid`],
    [{}, `Value is not valid`],
    [[], `Value is not valid`],
    ['', `Value is not valid`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
  ])(`shouldn't delete hash alias with invalid alias_id (message) '%s'`, async (alias_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: `alias`,
        alias_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [1234, `Value '1234' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't modify hash with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: `alias`,
        alias_id: newAlias,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [1, `Alias not found`],
    [1234, `Alias not found`],
    [undefined, `Key 'alias_id' is required`],
  ])(`shouldn't modify hash alias with invalid alias_id (description) '%s'`, async (alias_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: `alias`,
        alias_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value is not valid`],
    ['test', `Value is not valid`],
    [true, `Value is not valid`],
    [{}, `Value is not valid`],
    [[], `Value is not valid`],
    ['', `Value is not valid`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
  ])(`shouldn't modify hash alias with invalid alias_id (message) '%s'`, async (alias_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: `alias`,
        alias_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [1234, `Value '1234' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'company_id' is required`],
  ])(`shouldn't list aliases for a process with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        conv_id: newConv,
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value is not valid`],
    [{}, `Value is not valid`],
    [[], `Value is not valid`],
    ['', `Value is not valid`],
    [1, `user has no rights`],
    [-1, `Object conv with id -1 does not exist`],
    [0, `Object conv with id 0 does not exist`],
  ])(`shouldn't list aliases for a process with invalid conv_id '%s'`, async (conv_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        conv_id,
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value 'true' is not valid stage_id or Key 'stage_short_name' is required`],
    [{}, `Value '{[]}' is not valid stage_id or Key 'stage_short_name' is required`],
    [[], `Value '' is not valid stage_id or Key 'stage_short_name' is required`],
    ['', `Value '' is not valid stage_id or Key 'stage_short_name' is required`],
    [1, `Object stage with id 1 does not exist`],
    [-1, `Object stage with id -1 does not exist`],
    [1234, `Object stage with id 1234 does not exist`],
  ])(`shouldn't list aliases for a process with invalid stage_id '%s'`, async (stage_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        conv_id: newConv,
        stage_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([['test', `Stage not found`]])(
    `shouldn't list aliases for a process with invalid stage_short_name '%s'`,
    async (stage_short_name, reason) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.ALIASES,
          company_id,
          conv_id: newConv,
          stage_short_name,
          project_short_name,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([
    [true, `Value 'true' is not valid project_id or Key 'project_short_name' is required`],
    [{}, `Value '{[]}' is not valid project_id or Key 'project_short_name' is required`],
    [[], `Value '' is not valid project_id or Key 'project_short_name' is required`],
    ['', `Value '' is not valid project_id or Key 'project_short_name' is required`],
    [-1, `Value '-1' is not valid project_id or Key 'project_short_name' is required`],
  ])(`shouldn't list aliases for a process with invalid project_id '%s'`, async (project_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        conv_id: newConv,
        stage_id: newStage,
        project_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Stage not found`],
    ['test', `Project is not found`],
    [{}, `Stage not found`],
    [[], `Stage not found`],
    ['', `Stage not found`],
    [-1, `Stage not found`],
    [undefined, `Stage not found`],
  ])(
    `shouldn't list aliases for a process with invalid project_short_name '%s'`,
    async (project_short_name, reason) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.ALIASES,
          company_id,
          conv_id: newConv,
          stage_short_name,
          project_short_name,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([
    [true, `Value is not valid`],
    [{}, `Value is not valid`],
    [[], `Value is not valid`],
    ['', `Value is not valid`],
    [1, `Value is not valid`],
    [-1, `Value is not valid`],
    [0, `Value is not valid`],
    [1234, `Value is not valid`],
    [undefined, `Object alias not found`],
  ])(`shouldn't create task by Alias with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        obj_alias: `modify`,
        ref: `ref_${Date.now()}`,
        data: { a: '1' },
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Project is not found`],
    [{}, `Project is not found`],
    [[], `Project is not found`],
    ['', `Project is not found`],
    [1, `Project is not found`],
    [-1, `Project is not found`],
    [0, `Project is not found`],
    [1234, `Project is not found`],
    [undefined, `Project is not found`],
  ])(`shouldn't create task by Alias with invalid project_id '%s'`, async (project_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        obj_alias: `modify`,
        ref: `ref_${Date.now()}`,
        data: { a: '1' },
        stage_id: newStage,
        project_id,
        project_short_name: `test`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Stage not found`],
    ['test', `Project is not found`],
    [{}, `Stage not found`],
    [[], `Stage not found`],
    ['', `Stage not found`],
    [1, `Stage not found`],
    [-1, `Stage not found`],
    [0, `Stage not found`],
    [1234, `Stage not found`],
    [undefined, `Stage not found`],
  ])(`shouldn't create task by Alias with invalid project_short_name '%s'`, async (project_short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        obj_alias: `modify`,
        ref: `ref_${Date.now()}`,
        data: { a: '1' },
        stage_short_name,
        project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value 'true' is not valid stage_id or Key 'stage_short_name' is required`],
    [{}, `Value '{[]}' is not valid stage_id or Key 'stage_short_name' is required`],
    [[], `Value '' is not valid stage_id or Key 'stage_short_name' is required`],
    ['', `Value '' is not valid stage_id or Key 'stage_short_name' is required`],
    [1, `Object alias not found`],
    [-1, `Object alias not found`],
    [0, `Object alias not found`],
    [1234, `Object alias not found`],
    [undefined, `Object alias not found`],
  ])(`shouldn't create task by Alias with invalid stage_id '%s'`, async (stage_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        obj_alias: `modify`,
        ref: `ref_${Date.now()}`,
        data: { a: '1' },
        stage_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Object alias not found`],
    ['test', `Stage not found`],
    [{}, `Object alias not found`],
    [[], `Object alias not found`],
    ['', `Object alias not found`],
    [1, `Object alias not found`],
    [-1, `Object alias not found`],
    [0, `Object alias not found`],
    [1234, `Object alias not found`],
    [undefined, `Object alias not found`],
  ])(`shouldn't create task by Alias with invalid stage_short_name '%s'`, async (stage_short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        obj_alias: `modify`,
        ref: `ref_${Date.now()}`,
        data: { a: '1' },
        stage_short_name,
        project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Wrong object reference. Validation error`],
    [{}, `Wrong object reference. Validation error`],
    [[], `Wrong object reference. Validation error`],
    ['', `Wrong object reference. Validation error`],
    [1, `Wrong object reference. Validation error`],
    [-1, `Wrong object reference. Validation error`],
    [0, `Wrong object reference. Validation error`],
    [1234, `Wrong object reference. Validation error`],
    [undefined, `Wrong object reference. Validation error`],
  ])(`shouldn't create task by Alias with invalid obj_alias '%s'`, async (obj_alias, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        obj_alias,
        ref: `ref_${Date.now()}`,
        data: { a: '1' },
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test(`shouldn't create task by Alias with not unical ref '%s'`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        obj_alias: `modify`,
        ref: `123`,
        data: { a: '1' },
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(`not unical reference`);
  });

  test.each([true, {}, [], '', 1234, faker.random.alphaNumeric(256)])(
    `shouldn't create task by Alias with invalid ref '%s'`,
    async ref => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          company_id,
          obj_alias: `modify`,
          ref,
          data: { a: '1' },
          stage_id: newStage,
          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(`Value is not valid`);
    },
  );

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't delete alias with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.ALIAS,
          company_id: input,
          obj_id: newAlias,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't delete alias with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          obj_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test('should delete alias before destroy', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
      }),
    );
    expect(response.status).toBe(200);
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't destroy alias with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DESTROY,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          obj_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
