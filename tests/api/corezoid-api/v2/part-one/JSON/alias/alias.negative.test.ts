import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import faker from 'faker';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  maxLength,
  stringNotValidTestCases,
  minLength,
  securityTestCases,
  companyTestCases,
  boolTestCases,
} from '../../../../../negativeCases';

describe('Aliases (negative)', (): void => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let valuesToSkip: any;
  let newConv: string | number;
  let company_id: any;
  let newAlias: string;

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;

      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.CONV,
          company_id,
          folder_id: 0,
          title: `Process_${Date.now()}`,
          create_mode: 'without_nodes',
        }),
      );
      newConv = response.body.ops[0].obj_id;

      const responseAlias = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          short_name: `alias-${Date.now()}`,
          description: `aliasDesk-${Date.now()}`,
          title: `aliasTitle-${Date.now()}`,
        }),
      );
      newAlias = responseAlias.body.ops[0].obj_id;
    },
  );

  test.each([...companyTestCases, ...undefinedTestCase, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't create alias with invalid company_id '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ALIAS,
          company_id: input,
          short_name: 'test',
          description: 'test',
          title: 'test',
          project_id: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create alias with invalid project_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: 'test',
        description: 'test',
        title: 'test',
        project_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...maxLength, ...minLength].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create alias with invalid title '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: 'test',
        description: 'test',
        title: input,
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...maxLength, ...minLength].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create alias with invalid short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: input,
        description: 'test',
        title: 'test',
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [null];

  test.each(
    [...stringTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create alias with invalid description '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: 'test',
        description: input,
        title: 'test',
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test(`shouldn't create alias with max description '%s'`, async (): Promise<void> => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: 'test',
        description: faker.random.alphaNumeric(2001),
        title: 'test',
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(
      `Value is not valid. Value's byte_size is more than maximum allowed: 2000`,
    );
  });

  valuesToSkip = [null];

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create alias with invalid obj_to_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: 'test',
        description: 'test',
        title: 'test',
        project_id: 0,
        obj_to_id: input,
        obj_to_type: 'conv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...stringTestCases, ...undefinedTestCase, ...minLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't create alias with invalid obj_to_type '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          short_name: 'test',
          description: 'test',
          title: 'test',
          project_id: 0,
          obj_to_id: 1,
          obj_to_type: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't show alias with invalid company_id '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.ALIAS,
          company_id: input,
          obj_id: newAlias,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't show alias with invalid obj_id '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          obj_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't favorite alias with invalid company_id '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.FAVORITE,
          obj: OBJ_TYPE.ALIAS,
          company_id: input,
          obj_id: newAlias,
          favorite: true,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0];

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't favorite alias with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: input,
        favorite: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...boolTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't favorite alias with invalid favorite '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.FAVORITE,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          obj_id: newAlias,
          favorite: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify alias with invalid obj_id '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          obj_id: input,
          short_name: `aliasmodify-${Date.now()}`,
          description: `aliasDeskModify-${Date.now()}`,
          title: `aliasTitleModify-${Date.now()}`,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify alias with invalid company_id '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.ALIAS,
          company_id: input,
          obj_id: newAlias,
          short_name: `aliasmodify-${Date.now()}`,
          description: `aliasDeskModify-${Date.now()}`,
          title: `aliasTitleModify-${Date.now()}`,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [
      ...stringTestCases,
      ...undefinedTestCase,
      ...securityTestCases,
      ...minLength,
      ...maxLength,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify alias with invalid short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newAlias,
        short_name: input,
        description: `aliasDeskModify-${Date.now()}`,
        title: `aliasTitleModify-${Date.now()}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [null];

  test.each(
    [...stringTestCases, ...minLength, ...maxLength]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify alias with invalid title '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newAlias,
        short_name: `alias-${Date.now()}`,
        description: `aliasDeskModify-${Date.now()}`,
        title: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [null];

  test.each(
    [...stringTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify alias with invalid description '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newAlias,
        short_name: `alias-${Date.now()}`,
        description: input,
        title: `alias-${Date.now()}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test(`shouldn't modify alias with max description '%s'`, async (): Promise<void> => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newAlias,
        short_name: 'test',
        description: faker.random.alphaNumeric(2001),
        title: 'test',
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(
      `Value is not valid. Value's byte_size is more than maximum allowed: 2000`,
    );
  });

  test.each([...companyTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't link with invalid company_id '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.ALIAS,
          company_id: input,
          obj_id: newAlias,
          link: true,
          obj_to_id: newConv,
          obj_to_type: 'conv',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't link alias with invalid obj_id '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          obj_id: input,
          link: true,
          obj_to_id: newConv,
          obj_to_type: 'conv',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't link alias with invalid obj_to_id '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          obj_id: newAlias,
          link: true,
          obj_to_id: input,
          obj_to_type: 'conv',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't link alias with invalid obj_to_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newAlias,
        link: true,
        obj_to_id: newConv,
        obj_to_type: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...boolTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't link alias with invalid link '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          obj_id: newAlias,
          link: input,
          obj_to_id: newConv,
          obj_to_type: 'conv',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't upsert with invalid company_id '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.UPSERT,
          obj: OBJ_TYPE.ALIAS,
          company_id: input,
          obj_id: newAlias,
          short_name: 'test',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't upsert alias with invalid short_name '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.UPSERT,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          obj_id: newConv,
          short_name: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [null];

  test.each(
    [...stringTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't upsert alias with invalid description '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.UPSERT,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newConv,
        short_name: 'test',
        description: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test(`shouldn't upsert alias with invalid description max '%s'`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.UPSERT,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newAlias,
        short_name: 'test',
        description: faker.random.alphaNumeric(2001),
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(
      `Value is not valid. Value's byte_size is more than maximum allowed: 2000`,
    );
  });

  valuesToSkip = [null];

  test.each(
    [...stringTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't upsert alias with invalid title '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.UPSERT,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newConv,
        short_name: 'test',
        description: 'test',
        title: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [null];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't upsert alias with invalid obj_to_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.UPSERT,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newConv,
        short_name: 'test',
        description: 'test',
        title: 'test',
        obj_to_id: input,
        obj_to_type: 'conv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    ['test', `User not in company`],
    ['i12345678', `User not in company`],
    [undefined, `Key 'company_id' is required`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [123, `Value '123' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't list aliases with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        obj_id: newAlias,
        company_id,
        conv_id: newConv,
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value is not valid`],
    [null, `Value is not valid`],
    ['test', `Value is not valid`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
    [0, `Object conv with id 0 does not exist`],
    [-1, `Object conv with id -1 does not exist`],
    [123, `Object conv with id 123 does not exist`],
    [1, `user has no rights`],
  ])(`shouldn't list aliases with invalid obj_id '%s'`, async (conv_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        conv_id,
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value 'true' is not valid project_id or Key 'project_short_name' is required`],
    [null, `Value 'null' is not valid project_id or Key 'project_short_name' is required`],
    ['test', `Value 'test' is not valid project_id or Key 'project_short_name' is required`],
    [[], `Value '' is not valid project_id or Key 'project_short_name' is required`],
    [{}, `Value '{[]}' is not valid project_id or Key 'project_short_name' is required`],
  ])(`shouldn't list aliases with invalid project_id '%s'`, async (project_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        conv_id: newConv,
        project_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [123, `Value '123' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't get alias hash with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: 'alias',
        alias_id: newAlias,
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', `Alias is not in company`],
    ['i12345678', `Alias is not in company`],
  ])(`shouldn't get alias hash with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: 'alias',
        alias_id: newAlias,
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value is not valid`],
    [null, `Value is not valid`],
    ['test', `Value is not valid`],
    ['i12345678', `Value is not valid`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
  ])(`shouldn't get alias hash with invalid alias_id '%s'`, async (alias_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: 'alias',
        alias_id,
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [undefined, `Key 'alias_id' is required`],
    [0, `Alias not found`],
    [123, `Alias not found`],
    [1, `Alias not found`],
  ])(`shouldn't get alias hash with invalid alias_id '%s'`, async (alias_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: 'alias',
        alias_id,
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([true, null, 'test', [], {}, undefined, 0, 1, -1])(
    `shouldn't get alias hash with invalid obj_type '%s'`,
    async (obj_type): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.GET,
          obj: OBJ_TYPE.CALLBACK_HASH,
          company_id,
          obj_type,
          alias_id: newAlias,
          project_id: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(`Key 'conv_id' is required`);
    },
  );

  test.each([
    [true, `Value 'true' is not valid project_id or Key 'project_short_name' is required`],
    [null, `Value 'null' is not valid project_id or Key 'project_short_name' is required`],
    [[], `Value '' is not valid project_id or Key 'project_short_name' is required`],
    [{}, `Value '{[]}' is not valid project_id or Key 'project_short_name' is required`],
    ['test', `Value 'test' is not valid project_id or Key 'project_short_name' is required`],
  ])(
    `shouldn't get alias hash with invalid project_id '%s'`,
    async (project_id, reason): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.GET,
          obj: OBJ_TYPE.CALLBACK_HASH,
          company_id,
          obj_type: 'alias',
          alias_id: newAlias,
          project_id,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [123, `Value '123' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
  ])(
    `shouldn't delete alias hash with invalid company_id '%s'`,
    async (company_id, reason): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.CALLBACK_HASH,
          company_id,
          obj_type: 'alias',
          alias_id: newAlias,
          project_id: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([
    ['test', `Alias is not in company`],
    ['i12345678', `Alias is not in company`],
  ])(
    `shouldn't delete alias hash with invalid company_id '%s'`,
    async (company_id, reason): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.CALLBACK_HASH,
          company_id,
          obj_type: 'alias',
          alias_id: newAlias,
          project_id: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([
    [true, `Value is not valid`],
    [null, `Value is not valid`],
    ['test', `Value is not valid`],
    ['i12345678', `Value is not valid`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
  ])(
    `shouldn't delete alias hash with invalid alias_id '%s'`,
    async (alias_id, reason): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.CALLBACK_HASH,
          company_id,
          obj_type: 'alias',
          alias_id,
          project_id: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([
    [undefined, `Key 'alias_id' is required`],
    [0, `Alias not found`],
    [123, `Alias not found`],
    [1, `Alias not found`],
  ])(
    `shouldn't delete alias hash with invalid alias_id '%s'`,
    async (alias_id, reason): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.CALLBACK_HASH,
          company_id,
          obj_type: 'alias',
          alias_id,
          project_id: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([true, null, 'test', [], {}, undefined, 0, 1, -1])(
    `shouldn't delete alias hash with invalid obj_type '%s'`,
    async (obj_type): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.CALLBACK_HASH,
          company_id,
          obj_type,
          alias_id: newAlias,
          project_id: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(`Key 'conv_id' is required`);
    },
  );

  test.each([
    [true, `Value is not valid`],
    [null, `Value is not valid`],
    ['test', `Value is not valid`],
    ['i12345678', `Value is not valid`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
  ])(
    `shouldn't modify alias hash with invalid alias_id '%s'`,
    async (alias_id, reason): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.CALLBACK_HASH,
          company_id,
          obj_type: 'alias',
          alias_id,
          project_id: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([
    [undefined, `Key 'alias_id' is required`],
    [0, `Alias not found`],
    [123, `Alias not found`],
    [1, `Alias not found`],
  ])(
    `shouldn't modify alias hash with invalid alias_id '%s'`,
    async (alias_id, reason): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.CALLBACK_HASH,
          company_id,
          obj_type: 'alias',
          alias_id,
          project_id: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([true, null, 'test', [], {}, undefined, 0, 1, -1])(
    `shouldn't modify alias hash with invalid obj_type '%s'`,
    async (obj_type): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.CALLBACK_HASH,
          company_id,
          obj_type,
          alias_id: newAlias,
          project_id: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(`Key 'conv_id' is required`);
    },
  );

  test.each([
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [123, `Value '123' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
  ])(
    `shouldn't modify alias hash with invalid company_id '%s'`,
    async (company_id, reason): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.CALLBACK_HASH,
          company_id,
          obj_type: 'alias',
          alias_id: newAlias,
          project_id: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([
    ['test', `Alias is not in company`],
    ['i12345678', `Alias is not in company`],
  ])(
    `shouldn't modify alias hash with invalid company_id '%s'`,
    async (company_id, reason): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.CALLBACK_HASH,
          company_id,
          obj_type: 'alias',
          alias_id: newAlias,
          project_id: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't delete alias with invalid company_id '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.ALIAS,
          company_id: input,
          obj_id: newAlias,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't delete alias with invalid obj_id '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          obj_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test(`shouldn't destroy alias before deleted`, async (): Promise<void> => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].description).toContain(`Object alias with id ${newAlias} does not exist`);
  });

  test('should delete alias (only required param)', async (): Promise<void> => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
      }),
    );
    expect(response.status).toBe(200);
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't destroy alias with invalid obj_id '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DESTROY,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          obj_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  afterAll(
    async (): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.CONV,
          obj_id: newConv,
          company_id,
        }),
      );
      expect(response.status).toBe(200);

      const responseAlias = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.ALIAS,
          obj_id: newAlias,
          company_id,
        }),
      );
      expect(responseAlias.status).toBe(200);
    },
  );
});
