import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createSchema from '../../../../schemas/v2/aliases/createAlias.Schema.json';
import deleteSchema from '../../../../schemas/v2/aliases/deleteAlias.Schema.json';
import linkSchema from '../../../../schemas/v2/aliases/linkAlias.Schema.json';
import showSchema from '../../../../schemas/v2/aliases/showAlias.Schema.json';
import listSchema from '../../../../schemas/v2/aliases/listAliases.Schema.json';
import favoriteSchema from '../../../../schemas/v2/aliases/favoriteAliases.Schema.json';
import getHashSchema from '../../../../schemas/v2/aliases/getHashAlias.Schema.json';
import deleteHashSchema from '../../../../schemas/v2/aliases/deleteHashAlias.Schema.json';
import modifyHashSchema from '../../../../schemas/v2/aliases/modifyHashAlias.Schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { axiosInstance } from '../../../../../../../application/api/AxiosClient';
import { addMsg } from 'jest-html-reporters/helper';

describe('Aliases (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: string | number;
  let newDash: string | number;
  let newAlias: string;
  let newAlias1: string;
  let newAliasUpsert1: string;
  let newAliasUpsert2: string;
  let aliasShortName: string;
  let short_name: string;
  let company_id: any;
  let aliasHash: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    short_name = `alias-${Date.now()}`;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: 0,
        title: `Process_${Date.now()}`,
        conv_type: 'process',
      }),
    );
    newConv = response.body.ops[0].obj_id;

    const responseDash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        folder_id: 0,
        title: `Dash_${Date.now()}`,
      }),
    );
    newDash = responseDash.body.ops[0].obj_id;
  });

  test('should create alias (only required parameters)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name,
        title: `aliasTitle-${Date.now()}`,
      }),
    );
    expect(response.status).toBe(200);
    newAlias = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[0].obj_type).toBe('alias');
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newAlias })]),
    );
  });

  test('should create alias all parameters', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: `alias1-${Date.now()}`,
        title: `aliasTitle1-${Date.now()}`,
        description: `aliasDesc1-${Date.now()}`,
        project_id: 0,
        obj_to_id: newConv,
        obj_to_type: 'conv',
      }),
    );
    expect(response.status).toBe(200);
    newAlias1 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newAlias1 })]),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newAlias1).is_owner).toEqual(
      true,
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newAlias1).obj_to_type).toEqual(
      'conv',
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newAlias1).obj_to_id).toEqual(
      newConv,
    );
  });

  test('should show alias namber', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].is_owner).toEqual(true);
    expect(response.body.ops[0].is_favorite).toEqual(false);
    expect(response.body.ops[0].obj_to_id).toEqual(null);
    expect(response.body.ops[0].privs).toBeArrayOfSize(4);
    SchemaValidator.validate(showSchema, response.body);
  });

  test('should modify alias string (only required parameter)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAlias}`,
        short_name: `aliasmodify1-${Date.now()}`,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should modify alias number', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
        short_name: `aliasmodify-${Date.now()}`,
        description: `aliasDeskModify-${Date.now()}`,
        title: `aliasTitleModify-${Date.now()}`,
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should show alias string after_modify', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAlias}`,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].is_owner).toEqual(true);
    expect(response.body.ops[0].is_favorite).toEqual(false);
    expect(response.body.ops[0].obj_to_id).toEqual(null);
    expect(response.body.ops[0].short_name).toContain('aliasmodify');
    expect(response.body.ops[0].title).toContain('aliasTitleModify');
    expect(response.body.ops[0].description).toContain('aliasDeskModify');
    expect(response.body.ops[0].privs).toBeArrayOfSize(4);
    SchemaValidator.validate(showSchema, response.body);
    aliasShortName = response.body.ops[0].short_name;
  });

  test('should favorite alias number', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
        favorite: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toEqual('alias');
    expect(response.body.ops[0].obj_id).toEqual(newAlias);
    SchemaValidator.validate(favoriteSchema, response.body);

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].is_owner).toEqual(true);
    expect(responseShow.body.ops[0].is_favorite).toEqual(true);
  });

  test('should favorite alias string (only required parameters)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAlias}`,
        company_id,
        favorite: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toEqual('alias');
    expect(response.body.ops[0].obj_id).toEqual(newAlias);
    SchemaValidator.validate(favoriteSchema, response.body);

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].is_owner).toEqual(true);
    expect(responseShow.body.ops[0].is_favorite).toEqual(false);
  });

  test('should link alias number to dash number', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
        obj_to_id: newDash,
        obj_to_type: 'dashboard',
        link: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toEqual(newAlias);
    SchemaValidator.validate(linkSchema, response.body);

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAlias}`,
        company_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].obj_to_id).toEqual(newDash);
    SchemaValidator.validate(showSchema, responseShow.body);
  });

  test('should link alias number to conv number', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
        obj_to_id: newConv,
        obj_to_type: 'conv',
        link: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toEqual(newAlias);
    SchemaValidator.validate(linkSchema, response.body);
  });

  test('should show alias string after_link', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAlias}`,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].is_owner).toEqual(true);
    expect(response.body.ops[0].obj_to_id).toEqual(newConv);
    expect(response.body.ops[0].privs).toBeArrayOfSize(4);
    SchemaValidator.validate(showSchema, response.body);
  });

  test('should list aliases', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newAlias })]));
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newAlias).is_owner).toEqual(true);
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newAlias).obj_to_type).toEqual(
      'conv',
    );
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newAlias).obj_to_id).toEqual(newConv);
  });

  test('should list aliases by conv_id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        conv_id: newConv,
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newAlias })]));
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newAlias).is_owner).toEqual(true);
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newAlias).obj_to_type).toEqual(
      'conv',
    );
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newAlias).obj_to_id).toEqual(newConv);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should get callback_hash by alias', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: 'alias',
        alias_id: newAlias,
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].callback_hash).not.toEqual('');
    aliasHash = response.body.ops[0].callback_hash;
    SchemaValidator.validate(getHashSchema, response.body);
  });

  test('shouldn create empty task by direct url by alias', async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/@${aliasShortName}/0/${company_id}/${aliasHash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: {},
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(200);
    expect(response.data.ops.proc).toEqual('ok');
    expect(response.data.ops.obj).toEqual('task');
    expect(response.data.ops.ref).toEqual(null);
    expect(response.data.ops.obj_id).not.toEqual('');
  });

  test(`shouldn't create task data:[] by direct url by alias`, async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/@${aliasShortName}/0/${company_id}/${aliasHash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: [],
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(400);
    expect(response.data.ops[0].proc).toEqual('error');
    expect(response.data.ops[0].description).toEqual('Incorrect body');
  });

  test(`shouldn't create task data:[{'test':'1'}] by direct url by alias`, async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/@${aliasShortName}/0/${company_id}/${aliasHash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: [{ test: '1' }],
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(200);
    expect(response.data.ops.proc).toEqual('error');
    expect(response.data.ops.event).toEqual({ conv_id: newConv, data: [{ test: '1' }], obj: 'task' });
  });

  test(`shouldn't create task data:[{}] by direct url by alias`, async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/@${aliasShortName}/0/${company_id}/${aliasHash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: [{}],
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(200);
    expect(response.data.ops.proc).toEqual('not_correct_task_data');
    expect(response.data.ops.description).toEqual('incorrect task data');
  });

  test('should create task by direct url by alias', async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/@${aliasShortName}/0/${company_id}/${aliasHash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { test: 1 },
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(200);
    expect(response.data.ops.proc).toEqual('ok');
    expect(response.data.ops.obj).toEqual('task');
    expect(response.data.ops.obj_id).not.toEqual('');
  });

  test('should delete callback_hash by alias', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: 'alias',
        alias_id: newAlias,
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(deleteHashSchema, response.body);
  });

  test('should not create task by direct url by alias after delete hash', async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/@${aliasShortName}/0/${company_id}/${aliasHash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { test: 1 },
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(400);
    expect(response.data.ops[0].proc).toEqual('error');
    expect(response.data.ops[0].description).toEqual('Wrong alias hash');
  });

  test('should get callback_hash by alias after delete', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: 'alias',
        alias_id: newAlias,
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].callback_hash).toEqual('');
    SchemaValidator.validate(getHashSchema, response.body);
  });

  test('should modify callback_hash by alias', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: 'alias',
        alias_id: newAlias,
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].callback_hash).not.toEqual('');
    aliasHash = response.body.ops[0].callback_hash;
    SchemaValidator.validate(modifyHashSchema, response.body);
  });

  test('should unlink alias string to conv string', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAlias}`,
        company_id,
        obj_to_id: `${newConv}`,
        obj_to_type: 'conv',
        link: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toEqual(newAlias);
    SchemaValidator.validate(linkSchema, response.body);
  });

  test('should list aliases after unlink conv', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newAlias })]));
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newAlias).is_owner).toEqual(true);
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newAlias).obj_to_type).toEqual(null);
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newAlias).obj_to_id).toEqual(null);
  });

  test('should upsert alias number (only required parameters)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.UPSERT,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name,
      }),
    );
    expect(response.status).toBe(200);
    newAliasUpsert1 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAliasUpsert1}`,
        company_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].is_owner).toEqual(true);
    expect(responseShow.body.ops[0].obj_to_id).toEqual(null);
    expect(responseShow.body.ops[0].privs).toBeArrayOfSize(4);
  });

  test('should upsert alias string all parameters', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.UPSERT,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
        short_name,
        title: 'newusert',
        description: 'newusert',
        obj_to_id: newConv,
        obj_to_type: 'conv',
      }),
    );
    expect(response.status).toBe(200);
    newAliasUpsert2 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAliasUpsert2}`,
        company_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].is_owner).toEqual(true);
    expect(responseShow.body.ops[0].obj_to_id).toEqual(newConv);
    expect(responseShow.body.ops[0].privs).toBeArrayOfSize(4);
  });

  test('should delete alias after upsert1', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAliasUpsert1}`,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test.skip('should delete alias after upsert2', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAliasUpsert2}`,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should delete alias number', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should delete alias string (only required param)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAlias1}`,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should destroy alias number', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should destroy alias string (only required param)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAlias1}`,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
    expect(response.status).toBe(200);

    const responseDash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDash,
        company_id,
      }),
    );
    expect(responseDash.status).toBe(200);
  });
});
