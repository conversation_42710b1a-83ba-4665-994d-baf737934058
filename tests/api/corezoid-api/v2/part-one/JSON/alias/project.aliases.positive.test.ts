import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createSchema from '../../../../schemas/v2/project/createAlias.schema.json';
import listSchema from '../../../../schemas/v2/project/listAliases.schema.json';
import deleteSchema from '../../../../schemas/v2/project/deleteAlias.schema.json';
import modifySchema from '../../../../schemas/v2/project/modifyAlias.schema.json';
import showSchema from '../../../../schemas/v2/project/showAlias.schema.json';
import linkSchema from '../../../../schemas/v2/project/linkAlias.schema.json';
import favoriteSchema from '../../../../schemas/v2/aliases/favoriteAliases.Schema.json';
import getWebhookSchema from '../../../../schemas/v2/project/getWebhookAlias.schema.json';
import deleteWebhookSchema from '../../../../schemas/v2/project/deleteWebhookAlias.schema.json';
import modifyWebhookSchema from '../../../../schemas/v2/project/modifyWebhookAlias.schema.json';
import listAliasesProcessSchema from '../../../../schemas/v2/project/listAliasesProcess.schema.json';
import createTaskSchema from '../../../../schemas/v2/tasks/createTaskSchema.json';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { axiosInstance } from '../../../../../../../application/api/AxiosClient';
import { addMsg } from 'jest-html-reporters/helper';

describe('Aliases (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let newConv: string | number;
  let newDash: string | number;
  let newAlias: string | number;
  let newAlias1: string | number;
  let newAliasUpsert1: string | number;
  let newAliasUpsert2: string | number;
  let newAliasUpsert3: string | number;
  let newAlias2: string | number;
  let company_id: any;
  let taskRef: string | number;
  let taskObjId: string | number;
  let aliasHash: string;
  let taskObjId2: string | number;
  let short_name: string;
  let project_short_name: string;
  let stage_short_name: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = `project-${Date.now()}`;
    stage_short_name = `stage-${Date.now()}`;
    short_name = `alias-${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `Conv_${Date.now()}`,
        conv_type: 'process',
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    newConv = responseConv.body.ops[0].obj_id;

    const responseDash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        project_id: newProject,
        stage_id: newStage,
        title: `Dash_${Date.now()}`,
      }),
    );
    newDash = responseDash.body.ops[0].obj_id;

    const responseAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: `${Date.now()}`,
        title: 'Alias2',
        description: 'test',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    newAlias2 = responseAlias.body.ops[0].obj_id;
  });

  test(`should create Alias (only required param)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name,
        title: 'Alias',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('alias');
    newAlias = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[0].obj_type).toBe('alias');
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newAlias })]),
    );
  });

  test(`should create Alias (all param - project_short_name/stage_short_name)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: `${Date.now()}`,
        title: 'Alias',
        description: 'test',
        project_short_name,
        stage_short_name,
        obj_to_id: newConv,
        obj_to_type: 'conv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('alias');
    newAlias1 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[0].obj_type).toBe('alias');
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newAlias1 })]),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newAlias1).is_owner).toEqual(
      true,
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newAlias1).obj_to_type).toEqual(
      'conv',
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newAlias1).obj_to_id).toEqual(
      newConv,
    );
  });

  test(`should show Alias string after create`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAlias}`,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].title).toBe('Alias');
    expect(response.body.ops[0].description).toBe(null);
    expect(response.body.ops[0].short_name).toContain(short_name);
    SchemaValidator.validate(showSchema, response.body);
  });

  test(`should modify Alias string (only required parameter)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAlias}`,
        short_name: `modify1`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('alias');
    SchemaValidator.validate(modifySchema, response.body);
  });

  test(`should modify Alias number`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
        title: `newName`,
        description: `modify`,
        short_name: `modify`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('alias');
    SchemaValidator.validate(modifySchema, response.body);
  });

  test(`should show Alias number after modify`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].title).toBe('newName');
    expect(response.body.ops[0].description).toBe('modify');
    expect(response.body.ops[0].short_name).toContain('modify');
    SchemaValidator.validate(showSchema, response.body);
  });

  test(`should list Aliases`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].obj_type).toBe('alias');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newAlias })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newAlias1 })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newAlias2 })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ title: 'Alias2' })]));
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should favorite alias number', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
        favorite: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toEqual('alias');
    expect(response.body.ops[0].obj_id).toEqual(newAlias);
    SchemaValidator.validate(favoriteSchema, response.body);

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].is_owner).toEqual(true);
    expect(responseShow.body.ops[0].is_favorite).toEqual(true);
  });

  test('should favorite alias string (only required parameters)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAlias}`,
        company_id,
        favorite: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toEqual('alias');
    expect(response.body.ops[0].obj_id).toEqual(newAlias);
    SchemaValidator.validate(favoriteSchema, response.body);

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].is_owner).toEqual(true);
    expect(responseShow.body.ops[0].is_favorite).toEqual(false);
  });

  test('should link alias number to dash number', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
        obj_to_id: newDash,
        obj_to_type: 'dashboard',
        link: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toEqual(newAlias);
    SchemaValidator.validate(linkSchema, response.body);

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAlias}`,
        company_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].obj_to_id).toEqual(newDash);
    SchemaValidator.validate(showSchema, responseShow.body);
  });

  test(`should link Alias number to conv number`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
        obj_to_id: newConv,
        obj_to_type: `conv`,
        link: true,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newAlias);
    SchemaValidator.validate(linkSchema, response.body);
  });

  test(`should show Alias number after link`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_to_id).toBe(newConv);
    SchemaValidator.validate(showSchema, response.body);
  });

  test(`should create task by Alias`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        obj_alias: `modify`,
        ref: `ref_${Date.now()}`,
        data: { a: '1' },
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    taskRef = response.body.ops[0].ref;
    taskObjId = response.body.ops[0].obj_id;
    SchemaValidator.validate(createTaskSchema, response.body);

    await new Promise(r => setTimeout(r, 1000));
    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: newConv,
        ref: taskRef,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].data).toEqual({ a: '1' });
    expect(responseShow.body.ops[0].ref).toEqual(taskRef);
    expect(responseShow.body.ops[0].obj_id).toEqual(taskObjId);
  });

  test(`should create task by Alias with id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        obj_alias: `modify`,
        ref: `ref_${Date.now()}`,
        data: { a: '1' },
        stage_id: newStage,
        project_id: newProject,
        id: `test123`,
      }),
    );
    expect(response.status).toBe(200);
    taskRef = response.body.ops[0].ref;
    taskObjId = response.body.ops[0].obj_id;
    expect(response.body.ops[0].id).toEqual('test123');
  });

  test(`should Get Webhook Alias`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: 'alias',
        alias_id: newAlias,
      }),
    );
    expect(response.status).toBe(200);
    aliasHash = response.body.ops[0].callback_hash;
    SchemaValidator.validate(getWebhookSchema, response.body);
  });

  test(`should create task by direct url by alias`, async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/@modify/${newProject}/${newStage}/${company_id}/${aliasHash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { test: 1 },
    });
    expect(response.status).toBe(200);
    expect(response.data.ops.proc).toEqual('ok');
    expect(response.data.ops.obj).toEqual('task');
    taskObjId2 = response.data.ops.obj_id;
    expect(response.data.ops.obj_id).not.toEqual('');
  });

  test(`should show task after create task across Webhook Alias`, async () => {
    await new Promise(r => setTimeout(r, 1000));
    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: newConv,
        obj_id: taskObjId2,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].data).toEqual({ test: 1 });
    expect(responseShow.body.ops[0].obj_id).toEqual(taskObjId2);
  });

  test(`should create task by direct url by alias by project/stage_short_name`, async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/@modify/${project_short_name}/${stage_short_name}/${company_id}/${aliasHash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { test: 1 },
    });
    expect(response.status).toBe(200);
    expect(response.data.ops.proc).toEqual('ok');
    expect(response.data.ops.obj).toEqual('task');
    taskObjId2 = response.data.ops.obj_id;
    expect(response.data.ops.obj_id).not.toEqual('');

    await new Promise(r => setTimeout(r, 1000));
    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: newConv,
        obj_id: taskObjId2,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].data).toEqual({ test: 1 });
    expect(responseShow.body.ops[0].obj_id).toEqual(taskObjId2);
  });

  test('should create empty task by direct url by alias', async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/@modify/${project_short_name}/${stage_short_name}/${company_id}/${aliasHash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: {},
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(200);
    expect(response.data.ops.proc).toEqual('ok');
    expect(response.data.ops.obj).toEqual('task');
    expect(response.data.ops.ref).toEqual(null);
    expect(response.data.ops.obj_id).not.toEqual('');
  });

  test(`shouldn't create task data:[] by direct url by alias`, async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/@modify/${project_short_name}/${stage_short_name}/${company_id}/${aliasHash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: [],
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(400);
    expect(response.data.ops[0].proc).toEqual('error');
    expect(response.data.ops[0].description).toEqual('Incorrect body');
  });

  test(`should't create task data:[] by direct url by alias`, async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/@modify/${project_short_name}/${stage_short_name}/${company_id}/${aliasHash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: [{}],
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(200);
    expect(response.data.ops.proc).toEqual('not_correct_task_data');
    expect(response.data.ops.description).toEqual('incorrect task data');
  });

  test(`shouldn't create task data:[] by direct url by alias`, async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/@modify/${project_short_name}/${stage_short_name}/${company_id}/${aliasHash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: [{ test: '1' }],
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(200);
    expect(response.data.ops.proc).toEqual('error');
    expect(response.data.ops.event).toEqual({ conv_id: newConv, data: [{ test: '1' }], obj: 'task' });
  });

  test(`should delete Webhook Alias`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: 'alias',
        alias_id: newAlias,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('alias_callback_hash');
    SchemaValidator.validate(deleteWebhookSchema, response.body);
  });

  test(`should modify Webhook Alias`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: 'alias',
        alias_id: newAlias,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(modifyWebhookSchema, response.body);
  });

  test(`should Show Stage Aliases for a Process`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        conv_id: newConv,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].obj_type).toBe('alias');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newAlias })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ title: 'newName' })]));
    SchemaValidator.validate(listAliasesProcessSchema, response.body);
  });

  test('should unlink alias string to conv string', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAlias}`,
        company_id,
        obj_to_id: `${newConv}`,
        obj_to_type: 'conv',
        link: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toEqual(newAlias);
    SchemaValidator.validate(linkSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newAlias })]),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newAlias).is_owner).toEqual(true);
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newAlias).obj_to_type).toEqual(
      null,
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newAlias).obj_to_id).toEqual(
      null,
    );
  });

  test('should upsert alias number (only required parameters)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.UPSERT,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
        short_name,
      }),
    );
    expect(response.status).toBe(200);
    newAliasUpsert1 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAliasUpsert1}`,
        company_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].is_owner).toEqual(true);
    expect(responseShow.body.ops[0].obj_to_id).toEqual(null);
    expect(responseShow.body.ops[0].privs).toBeArrayOfSize(4);
  });

  test('should upsert alias string all parameters', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.UPSERT,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
        short_name,
        title: 'newusert',
        description: 'newusert',
        obj_to_id: newConv,
        obj_to_type: 'conv',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    newAliasUpsert2 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAliasUpsert2}`,
        company_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].is_owner).toEqual(true);
    expect(responseShow.body.ops[0].obj_to_id).toEqual(newConv);
    expect(responseShow.body.ops[0].privs).toBeArrayOfSize(4);
  });

  test('should upsert alias string all parameters', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.UPSERT,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
        short_name,
        title: 'newusert',
        description: 'newusert',
        obj_to_id: newConv,
        obj_to_type: 'conv',
        project_short_name,
        stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    newAliasUpsert3 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAliasUpsert3}`,
        company_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].is_owner).toEqual(true);
    expect(responseShow.body.ops[0].obj_to_id).toEqual(newConv);
    expect(responseShow.body.ops[0].privs).toBeArrayOfSize(4);
  });

  test('should delete alias after upsert1', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAliasUpsert1}`,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should delete alias after upsert2', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAliasUpsert2}`,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test.skip('should delete alias after upsert3', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAliasUpsert3}`,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test(`should delete Alias number`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias2,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newAlias2);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test(`should delete Alias string (only required parameter)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAlias1}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newAlias1);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test(`should destroy Alias number`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias2,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newAlias2);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test(`should destroy Alias string (only required parameter)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.ALIAS,
        obj_id: `${newAlias1}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newAlias1);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
