import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  maxLength,
  securityTestCases,
  stringNotValidTestCases,
} from '../../../../../negativeCases';

describe('Channel (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: number;
  let company_id: any;
  let tokenViber: string;
  let tokenTelegram: string;
  const valuesToSkip: any = [0];

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    (tokenViber = '47507eb35367d40c-2487bb76e2ec831a-5b242f9eaf064aaf'),
      (tokenTelegram = '**********************************************');

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Process_${Date.now()}`);
    newConv = response.body.ops[0].obj_id;
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create channel with invalid obj_type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHANNEL,
        company_id,
        folder_id: 0,
        conv_id: newConv,
        obj_type: input,
        token: tokenViber,
        env: 'editor',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...securityTestCases, ...maxLength].map(({ input, errors }) => [
      input,
      errors,
    ]),
  )(`shouldn't create channel with invalid token '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHANNEL,
        company_id,
        folder_id: 0,
        conv_id: newConv,
        obj_type: 'viber',
        token: input,
        env: 'front',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't create channel with invalid conv_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.CHANNEL,
          company_id,
          folder_id: 0,
          conv_id: input,
          obj_type: 'viber',
          token: tokenViber,
          env: 'front',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create channel with invalid folder_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHANNEL,
        company_id,
        folder_id: input,
        conv_id: newConv,
        obj_type: 'viber',
        token: tokenViber,
        env: 'front',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create channel with invalid callback_hash '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHANNEL,
        company_id,
        callback_hash: input,
        obj_type: 'telegram',
        token: tokenTelegram,
        env: 'front',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify channel with invalid obj_type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CHANNEL,
        company_id,
        obj_id: '1',
        obj_type: input,
        result: 'ok',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify channel with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.CHANNEL,
          company_id,
          obj_id: input,
          obj_type: 'viber',
          result: 'ok',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [
      ...stringTestCases,
      ...stringNotValidTestCases,
      ...undefinedTestCase,
      ...securityTestCases,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify channel with invalid result '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CHANNEL,
        company_id,
        obj_id: '1',
        obj_type: 'viber',
        result: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify channel with invalid description '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.CHANNEL,
          company_id,
          obj_id: '1',
          obj_type: 'viber',
          result: 'error',
          description: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  afterAll(async () => {
    const responseConv = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, company_id);
    expect(responseConv.status).toBe(200);
  });
});
