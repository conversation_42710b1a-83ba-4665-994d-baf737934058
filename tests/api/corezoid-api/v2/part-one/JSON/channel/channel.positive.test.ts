import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import modifySchema from '../../../../schemas/v2/channel/modifyChannel.schema.json';
import createSchema from '../../../../schemas/v2/channel/createChannel.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj, requestListConv } from '../../../../../../../application/api/ApiObj';

describe('Channel (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: number;
  let company_id: any;
  let hash: string;
  let tokenViber: string;
  let tokenTelegram: string;
  let tokenFacebook: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    (tokenViber = '47507eb35367d40c-2487bb76e2ec831a-5b242f9eaf064aaf'),
      (tokenTelegram = '**********************************************'),
      (tokenFacebook = 'asjdljkljl1j3lu21ydh,jasndkl');

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Process_${Date.now()}`);
    newConv = response.body.ops[0].obj_id;
    hash = response.body.ops[0].hash;

    const responseList = await requestListConv(api, newConv, company_id);
    expect(responseList.status).toBe(200);
  });

  test('should create channel (obj_type: viber)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHANNEL,
        company_id,
        folder_id: 0,
        conv_id: newConv,
        obj_type: 'viber',
        token: tokenViber,
        env: 'editor',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('channel');
    expect(response.body.ops[0].obj_type).toBe('viber');
    SchemaValidator.validate(createSchema, response.body);

    const responseList = await requestListConv(api, newConv, company_id);
    expect(responseList.status).toBe(200);
    expect(
      (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'Set tokens').logics[0].extra,
    ).toEqual({ viber_token: tokenViber });
  });

  test('should create channel (obj_type: telegram)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHANNEL,
        company_id,
        obj_type: 'telegram',
        token: tokenTelegram,
        env: 'editor',
        callback_hash: hash,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('channel');
    expect(response.body.ops[0].obj_type).toBe('telegram');
    SchemaValidator.validate(createSchema, response.body);

    const responseList = await requestListConv(api, newConv, company_id);
    expect(responseList.status).toBe(200);
    expect(
      (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'Set tokens').logics[0].extra
        .telegram_token,
    ).toEqual(tokenTelegram);
  });

  test('should create channel (obj_type: fbmessenger)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHANNEL,
        company_id,
        folder_id: 0,
        conv_id: newConv,
        obj_type: 'fbmessenger',
        token: tokenFacebook,
        env: 'front',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('channel');
    expect(response.body.ops[0].obj_type).toBe('fbmessenger');
    SchemaValidator.validate(createSchema, response.body);

    const responseList = await requestListConv(api, newConv, company_id);
    expect(responseList.status).toBe(200);
    expect(
      (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'Set tokens').logics[0].extra
        .fb_access_token,
    ).toEqual(tokenFacebook);
  });

  test('should create channel (obj_type: slack)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CHANNEL,
        company_id,
        folder_id: 0,
        conv_id: newConv,
        obj_type: 'slack',
        env: 'front',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('channel');
    expect(response.body.ops[0].obj_type).toBe('slack');
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should modify channel (obj_type: telegram - result: ok)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CHANNEL,
        company_id,
        obj_id: '1',
        result: 'ok',
        obj_type: 'telegram',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('channel');
    SchemaValidator.validate(modifySchema, response.body);
  });

  test('should modify channel (obj_type: telegram - result: error)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CHANNEL,
        company_id,
        obj_id: '1',
        result: 'error',
        description: 'test',
        obj_type: 'telegram',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('channel');
    SchemaValidator.validate(modifySchema, response.body);
  });

  afterAll(async () => {
    const responseConv = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, company_id);
    expect(responseConv.status).toBe(200);
  });
});
