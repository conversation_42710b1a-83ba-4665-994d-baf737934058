import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createTaskSchema from '../../../../schemas/v2/tasks/createTaskSchema.json';
import showTaskSchema from '../../../../schemas/v2/tasks/showTaskSchema.json';
import stepNextTaskSchema from '../../../../schemas/v2/tasks/step_nextTaskSchema.json';
import stepPrevTaskSchema from '../../../../schemas/v2/tasks/step_prevTaskSchema.json';
import stepGoToTaskSchema from '../../../../schemas/v2/tasks/step_gotoTaskSchema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('Debug Task (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newSD: string | number;
  let taskObjId: string | number;
  let taskRef: string | number;
  let companyId: any;
  let start_node_ID: string;
  let activeuser_node_ID: string;
  let newuser_node_ID: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    companyId = apikey.companies[0].id;

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id: companyId,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: 0,
        conv_type: 'state',
      }),
    );
    newSD = responseConv.body.ops[0].obj_id;

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newSD,
        company_id: companyId,
      }),
    );
    newuser_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'New user').obj_id;
    activeuser_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'Active user')
      .obj_id;
    start_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'start').obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV,
        company_id: companyId,
        status: 'debug',
        obj_id: newSD,
      }),
    );
  });

  test('should CREATE task in debug', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newSD,
        data: { a: '1' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(response.status).toBe(200);
    taskRef = response.body.ops[0].ref;
    taskObjId = response.body.ops[0].obj_id;
    SchemaValidator.validate(createTaskSchema, response.body);
  });

  test('should NEXT_STEP task in debug', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STEP_NEXT,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        obj_id: taskObjId,
        conv_id: newSD,
        branch: 'logic',
        data: { a: '2' },
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(stepNextTaskSchema, response.body);
    expect(response.body.ops[0].obj_id).toBe(taskObjId);
    expect(response.body.ops[0].conv_id).toEqual(newSD);
    expect(response.body.ops[0].data).toEqual({ a: '2' });
    expect(response.body.ops[0].branches).toEqual(['timer', 'logic']);
    expect(response.body.ops[0].logic[0]).toEqual({ type: 'api_callback', obj_id_path: '' });
    expect(response.body.ops[0].history[0].prev_node_id).toEqual(null);

    await new Promise(r => setTimeout(r, 1000));
    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjId,
        conv_id: newSD,
      }),
    );
    expect(responseShow.status).toBe(200);
    SchemaValidator.validate(showTaskSchema, responseShow.body);
    expect(responseShow.body.ops[0].data).toEqual({ a: '2' });
    expect(responseShow.body.ops[0].ref).toEqual(taskRef);
    expect(responseShow.body.ops[0].obj_id).toEqual(taskObjId);
  });

  test('should STEP_PREV task in debug', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STEP_PREV,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        obj_id: taskObjId,
        conv_id: newSD,
        branch: 'logic',
        data: { a: '2' },
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(stepPrevTaskSchema, response.body);
    expect(response.body.ops[0].obj_id).toBe(taskObjId);
    expect(response.body.ops[0].conv_id).toEqual(newSD);
    expect(response.body.ops[0].data).toEqual({ a: '2' });
    expect(response.body.ops[0].branches).toEqual(['logic']);
    expect(response.body.ops[0].logic[0].type).toEqual('go');
    expect(response.body.ops[0].history[1].prev_node_id).toEqual(start_node_ID);

    await new Promise(r => setTimeout(r, 1000));
    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjId,
        conv_id: newSD,
      }),
    );
    expect(responseShow.status).toBe(200);
    SchemaValidator.validate(showTaskSchema, responseShow.body);
    expect(responseShow.body.ops[0].data).toEqual({ a: '2' });
    expect(responseShow.body.ops[0].ref).toEqual(taskRef);
    expect(responseShow.body.ops[0].obj_id).toEqual(taskObjId);
  });

  test('should NEXT_STEP task in debug add param', async () => {
    const responseN = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STEP_NEXT,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjId,
        company_id: companyId,
        conv_id: newSD,
        data: { a: '3', b: 3 },
        branch: 'logic',
      }),
    );
    expect(responseN.status).toBe(200);
    SchemaValidator.validate(stepNextTaskSchema, responseN.body);
    expect(responseN.body.ops[0].obj_id).toBe(taskObjId);
    expect(responseN.body.ops[0].conv_id).toEqual(newSD);
    expect(responseN.body.ops[0].data).toEqual({ a: '3', b: 3 });
    expect(responseN.body.ops[0].branches).toEqual(['timer', 'logic']);
    expect(responseN.body.ops[0].logic[0]).toEqual({ type: 'api_callback', obj_id_path: '' });

    await new Promise(r => setTimeout(r, 1000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjId,
        conv_id: newSD,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(showTaskSchema, response.body);
    expect(response.body.ops[0].data).toEqual({ a: '3', b: 3 });
    expect(response.body.ops[0].ref).toEqual(taskRef);
    expect(response.body.ops[0].obj_id).toEqual(taskObjId);
  });

  test('should STEP_PREV task in debug add param', async () => {
    const responseN = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STEP_PREV,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjId,
        company_id: companyId,
        conv_id: newSD,
        data: { a: '3', b: 5 },
        branch: 'logic',
      }),
    );
    expect(responseN.status).toBe(200);
    SchemaValidator.validate(stepPrevTaskSchema, responseN.body);
    expect(responseN.body.ops[0].obj_id).toBe(taskObjId);
    expect(responseN.body.ops[0].conv_id).toEqual(newSD);
    expect(responseN.body.ops[0].data).toEqual({ a: '3', b: 5 });
    expect(responseN.body.ops[0].branches).toEqual(['logic']);
    expect(responseN.body.ops[0].logic[0].type).toEqual('go');
    expect(responseN.body.ops[0].prev_node_id).toEqual(newuser_node_ID);

    await new Promise(r => setTimeout(r, 1000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjId,
        conv_id: newSD,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(showTaskSchema, response.body);
    expect(response.body.ops[0].data).toEqual({ a: '3', b: 5 });
    expect(response.body.ops[0].ref).toEqual(taskRef);
    expect(response.body.ops[0].obj_id).toEqual(taskObjId);
  });

  test('should NEXT_STEP task in debug without branch, conv_id number', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STEP_NEXT,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjId,
        conv_id: newSD,
        data: { c: 1 },
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(stepNextTaskSchema, response.body);
    expect(response.body.ops[0].obj_id).toBe(taskObjId);
    expect(response.body.ops[0].conv_id).toEqual(newSD);
    expect(response.body.ops[0].data).toEqual({ c: 1 });
    expect(response.body.ops[0].branches).toEqual(['timer', 'logic']);
    expect(response.body.ops[0].logic[0]).toEqual({ type: 'api_callback', obj_id_path: '' });
  });

  test('should NEXT_STEP task in debug without branch, conv_id string', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STEP_NEXT,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjId,
        conv_id: `${newSD}`,
        data: { c: 1 },
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(stepNextTaskSchema, response.body);
    expect(response.body.ops[0].obj_id).toBe(taskObjId);
    expect(response.body.ops[0].conv_id).toEqual(newSD);
    expect(response.body.ops[0].data).toEqual({ c: 1 });
    expect(response.body.ops[0].branches).toEqual(['timer', 'logic']);
    expect(response.body.ops[0].logic[0]).toEqual({ type: 'api_callback', obj_id_path: '' });
  });

  test('should STEP_PREV task in debug without branch, conv_id string', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STEP_PREV,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjId,
        conv_id: `${newSD}`,
        data: { c: 1 },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(taskObjId);
    expect(response.body.ops[0].conv_id).toEqual(newSD);
    expect(response.body.ops[0].data).toEqual({ c: 1 });
    expect(response.body.ops[0].branches).toEqual(['timer', 'logic']);
    expect(response.body.ops[0].logic[0]).toEqual({ type: 'api_callback', obj_id_path: '' });
    expect(response.body.ops[0].prev_node_id).toEqual(activeuser_node_ID);
  });

  test('should STEP_PREV task in debug with data {}', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STEP_PREV,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjId,
        conv_id: `${newSD}`,
        data: [],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(taskObjId);
    expect(response.body.ops[0].conv_id).toEqual(newSD);
    expect(response.body.ops[0].data).toEqual({});
    expect(response.body.ops[0].branches).toEqual(['timer', 'logic']);
    expect(response.body.ops[0].logic[0]).toEqual({ type: 'api_callback', obj_id_path: '' });
    expect(response.body.ops[0].prev_node_id).toEqual(newuser_node_ID);
  });

  test('should STEP_GOTO task in debug', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STEP_GOTO,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        obj_id: taskObjId,
        conv_id: newSD,
        branch: 'logic',
        data: { a: '2' },
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(stepGoToTaskSchema, response.body);
    expect(response.body.ops[0].obj_id).toBe(taskObjId);
    expect(response.body.ops[0].conv_id).toEqual(newSD);
    expect(response.body.ops[0].data).toEqual({ a: '2' });
    expect(response.body.ops[0].branches).toEqual(['timer', 'logic']);
    expect(response.body.ops[0].history).toEqual([]);
  });

  test('should STEP_GOTO task in debug add parameter', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STEP_GOTO,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        obj_id: taskObjId,
        conv_id: newSD,
        branch: 'logic',
        data: { a: '4', b: '12' },
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(stepGoToTaskSchema, response.body);
    expect(response.body.ops[0].obj_id).toBe(taskObjId);
    expect(response.body.ops[0].conv_id).toEqual(newSD);
    expect(response.body.ops[0].data).toEqual({ a: '4', b: '12' });
    expect(response.body.ops[0].branches).toEqual(['timer', 'logic']);
    expect(response.body.ops[0].history).toEqual([]);
  });

  test('should STEP_GOTO task in debug without branch/company and conv_id string', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STEP_GOTO,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjId,
        conv_id: `${newSD}`,
        data: { a: '4', b: '12' },
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(stepGoToTaskSchema, response.body);
    expect(response.body.ops[0].obj_id).toBe(taskObjId);
    expect(response.body.ops[0].conv_id).toEqual(newSD);
    expect(response.body.ops[0].data).toEqual({ a: '4', b: '12' });
    expect(response.body.ops[0].branches).toEqual(['timer', 'logic']);
    expect(response.body.ops[0].history).toEqual([]);
  });

  test('should STEP_GOTO task in debug with data {}', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STEP_GOTO,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjId,
        conv_id: `${newSD}`,
        data: [],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(taskObjId);
    expect(response.body.ops[0].conv_id).toEqual(newSD);
    expect(response.body.ops[0].data).toEqual({});
    expect(response.body.ops[0].branches).toEqual(['timer', 'logic']);
    expect(response.body.ops[0].history).toEqual([]);
  });

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newSD,
        company_id: companyId,
      }),
    );
  });
});
