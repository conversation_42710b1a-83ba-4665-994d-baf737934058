import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createTaskSchema from '../../../../schemas/v2/tasks/createTaskSchema.json';
import showTaskSchema from '../../../../schemas/v2/tasks/showTaskSchema.json';
import modifyTaskSchema from '../../../../schemas/v2/tasks/modifyTaskSchema.json';
import deleteTaskSchema from '../../../../schemas/v2/tasks/deleteTaskSchema.json';
import listTaskHostorySchema from '../../../../schemas/v2/tasks/listTaskHistorySchema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import {
  requestList,
  requestCreateObj,
  requestListConv,
  requestShow,
} from '../../../../../../../application/api/ApiObj';

describe('Task (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: number;
  let newSD: number;
  let newAlias: string | number;
  let short_nameAlias: string;
  let taskObjId1: string | number;
  let taskRef1: string | number;
  let taskObjId2: string | number;
  let taskRef2: string | number;
  let taskObjIdSD: string | number;
  let taskObjIdSD2: string | number;
  let taskRefSD: string | number;
  let nodeWithoutTask: string;
  let nodeTask: string;
  let companyId: any;
  let final_node_ID: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    companyId = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, companyId, `SetParameter`);
    newConv = response.body.ops[0].obj_id;

    const responseListConv = await requestListConv(api, newConv, companyId);
    expect(responseListConv.status).toBe(200);
    final_node_ID = (responseListConv.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseSD = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id: companyId,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: 0,
        conv_type: 'state',
      }),
    );
    newSD = responseSD.body.ops[0].obj_id;

    const responseListSD = await requestListConv(api, newSD, companyId);
    expect(responseListSD.status).toBe(200);
    nodeTask = responseListSD.body.ops[0].list[2].obj_id;
    nodeWithoutTask = responseListSD.body.ops[0].list[0].obj_id;

    const responseAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id: companyId,
        title: `${OBJ_TYPE.ALIAS}_${Date.now()}`,
        short_name: `alias-${Date.now()}`,
      }),
    );
    newAlias = responseAlias.body.ops[0].obj_id;

    const responseShowAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.ALIAS,
        company_id: companyId,
        obj_id: newAlias,
      }),
    );
    short_nameAlias = responseShowAlias.body.ops[0].short_name;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id: companyId,
        link: true,
        obj_to_type: 'conv',
        obj_to_id: newSD,
      }),
    );
  });

  test('should create task1 conv_id number and id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        action: 'user',
        data: { a: '1' },
        ref: `ref_${Date.now()}`,
        id: 'test123',
      }),
    );
    expect(response.status).toBe(200);
    taskRef1 = response.body.ops[0].ref;
    taskObjId1 = response.body.ops[0].obj_id;
    expect(response.body.ops[0].id).toEqual('test123');
  });

  test('should create task2 conv_id string', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        action: 'user',
        data: { b: 1 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(response.status).toBe(200);
    taskRef2 = response.body.ops[0].ref;
    taskObjId2 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createTaskSchema, response.body);
  });

  test('should show task1 ref', async () => {
    await new Promise(r => setTimeout(r, 3000));

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        ref: taskRef1,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(showTaskSchema, response.body);
    expect(response.body.ops[0].data).toEqual({ a: '1' });
    expect(response.body.ops[0].ref).toEqual(taskRef1);
    expect(response.body.ops[0].obj_id).toEqual(taskObjId1);
  });

  test('should show task2 obj_id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjId2,
        company_id: companyId,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data).toEqual({ b: 1 });
    expect(response.body.ops[0].ref).toEqual(taskRef2);
    expect(response.body.ops[0].obj_id).toEqual(taskObjId2);
    SchemaValidator.validate(showTaskSchema, response.body);
  });

  test('should show task2 with id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjId2,
        company_id: companyId,
        conv_id: newConv,
        id: 'test123',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data).toEqual({ b: 1 });
    expect(response.body.ops[0].ref).toEqual(taskRef2);
    expect(response.body.ops[0].obj_id).toEqual(taskObjId2);
    expect(response.body.ops[0].id).toEqual('test123');
  });

  test('should show task1 ref higher priority', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjId2,
        company_id: companyId,
        conv_id: newConv,
        ref: taskRef1,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(showTaskSchema, response.body);
    expect(response.body.ops[0].data).toEqual({ a: '1' });
    expect(response.body.ops[0].ref).toEqual(taskRef1);
    expect(response.body.ops[0].obj_id).toEqual(taskObjId1);
  });

  test('should create task SD by alias', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        obj_alias: short_nameAlias,
        data: { c: '1' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(response.status).toBe(200);
    taskRefSD = response.body.ops[0].ref;
    taskObjIdSD = response.body.ops[0].obj_id;
    SchemaValidator.validate(createTaskSchema, response.body);
  });

  test('should create task SD by alias with id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        obj_alias: short_nameAlias,
        data: { c: '1' },
        ref: `ref_${Date.now()}`,
        id: 'test123',
      }),
    );
    expect(response.status).toBe(200);
    taskRefSD = response.body.ops[0].ref;
    taskObjIdSD = response.body.ops[0].obj_id;
    expect(response.body.ops[0].id).toEqual('test123');
  });

  test('should create task2 SD by alias', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        obj_alias: short_nameAlias,
        data: { x: '1' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(response.status).toBe(200);
    taskObjIdSD2 = response.body.ops[0].obj_id;
  });

  test('should show task SD by obj_id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjIdSD,
        conv_id: newSD,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data).toEqual({ c: '1' });
    expect(response.body.ops[0].ref).toEqual(taskRefSD);
    expect(response.body.ops[0].obj_id).toEqual(taskObjIdSD);
    SchemaValidator.validate(showTaskSchema, response.body);
  });

  test('should modify task SD by ref', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.TASK,
        conv_id: newSD,
        ref: taskRefSD,
        data: { c: '2' },
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(modifyTaskSchema, response.body);
  });

  test('should show task SD by obj_id', async () => {
    await new Promise(r => setTimeout(r, 2000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjIdSD,
        conv_id: newSD,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(showTaskSchema, response.body);
    expect(response.body.ops[0].data).toEqual({ c: '2' });
    expect(response.body.ops[0].ref).toEqual(taskRefSD);
    expect(response.body.ops[0].obj_id).toEqual(taskObjIdSD);
  });

  test('should modify task SD by obj_id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjIdSD,
        company_id: companyId,
        conv_id: newSD,
        data: { c: 3, d: '1' },
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(modifyTaskSchema, response.body);
  });

  test('should modify task SD by ref higher priority', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjIdSD2,
        conv_id: newSD,
        ref: taskRefSD,
        data: { x: 5 },
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(modifyTaskSchema, response.body);
  });

  test('should show task SD by ref higher priority', async () => {
    await new Promise(r => setTimeout(r, 2000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjIdSD,
        conv_id: newSD,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(showTaskSchema, response.body);
    expect(response.body.ops[0].data).toEqual({ c: 3, d: '1', x: 5 });
    expect(response.body.ops[0].ref).toEqual(taskRefSD);
    expect(response.body.ops[0].obj_id).toEqual(taskObjIdSD);
  });

  test('should modify task SD with id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjIdSD,
        company_id: companyId,
        conv_id: newSD,
        data: { c: 3, d: '1' },
        id: 'test123',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].id).toEqual('test123');
  });

  test('should show task SD by obj_id', async () => {
    await new Promise(r => setTimeout(r, 2000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjIdSD,
        conv_id: newSD,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(showTaskSchema, response.body);
    expect(response.body.ops[0].data).toEqual({ c: 3, d: '1', x: 5 });
    expect(response.body.ops[0].ref).toEqual(taskRefSD);
    expect(response.body.ops[0].obj_id).toEqual(taskObjIdSD);
  });

  test('should not delete task SD by obj_id when task in other node', async () => {
    await new Promise(r => setTimeout(r, 2000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjIdSD,
        node_id: nodeWithoutTask,
        conv_id: newSD,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual('task not found');
  });

  test('should show task SD by obj_id after delete task when task in other node', async () => {
    await new Promise(r => setTimeout(r, 2000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjIdSD,
        conv_id: newSD,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(showTaskSchema, response.body);
    expect(response.body.ops[0].data).toEqual({ c: 3, d: '1', x: 5 });
    expect(response.body.ops[0].ref).toEqual(taskRefSD);
    expect(response.body.ops[0].obj_id).toEqual(taskObjIdSD);
  });

  test('should delete task SD by obj_id', async () => {
    const responseList = await requestList(api, nodeTask, newSD, companyId, 10);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].count).toBe(1);
    expect(responseList.body.ops[0].list).toBeArrayOfSize(1);

    await new Promise(r => setTimeout(r, 2000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjIdSD,
        node_id: nodeTask,
        conv_id: newSD,
        id: 'test123',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].id).toEqual('test123');
    SchemaValidator.validate(deleteTaskSchema, response.body);
  });

  test('should not show task SD by obj_id after delete task', async () => {
    await new Promise(r => setTimeout(r, 2000));
    const responseShowTask2 = await requestShow(api, OBJ_TYPE.TASK, taskObjIdSD, { conv_id: newSD });
    expect(responseShowTask2.body.ops[0].proc).toEqual('error');
    expect(responseShowTask2.body.ops[0].description).toEqual('task not found');

    const responseList = await requestList(api, nodeTask, newSD, companyId, 10);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].count).toBe(0);
    expect(responseList.body.ops[0].list).toBeArrayOfSize(0);
  });

  test('should delete task in final node by obj_id', async () => {
    const responseListTask = await requestList(api, final_node_ID, newConv, companyId, 10);
    expect(responseListTask.status).toBe(200);
    expect(responseListTask.body.ops[0].count).toBe(2);
    expect(responseListTask.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ data: { a: '1' } })]),
    );
    expect(responseListTask.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ data: { b: 1 } })]),
    );

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.TASK,
        obj_id: taskObjId1,
        node_id: final_node_ID,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(deleteTaskSchema, response.body);

    await new Promise(r => setTimeout(r, 3000));

    const responseShowTask2 = await requestShow(api, OBJ_TYPE.TASK, taskObjId1, { conv_id: newConv });
    expect(responseShowTask2.body.ops[0].proc).toEqual('error');
    expect(responseShowTask2.body.ops[0].description).toEqual('task not found');

    const responseList = await requestList(api, final_node_ID, newConv, companyId, 10);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].count).toBe(1);
    expect(responseList.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ data: { a: '1' } })]),
    );
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ data: { b: 1 } })]),
    );
  });

  test('should create task with the same ref=ref', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        action: 'user',
        data: { task: 'one' },
        ref: `ref`,
      }),
    );
    expect(response.status).toBe(200);

    await new Promise(r => setTimeout(r, 1000));

    const response2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        action: 'user',
        data: { task: 'two' },
        ref: `ref`,
      }),
    );
    expect(response2.status).toBe(200);
  });

  test('should list task_history by obj_id', async () => {
    await new Promise(r => setTimeout(r, 2000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.TASK_HISTORY,
        obj_id: taskObjIdSD,
        company_id: companyId,
        conv_id: newSD,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(listTaskHostorySchema, response.body);
    expect(response.body.ops[0].obj_id).toEqual(taskObjIdSD);
    expect(response.body.ops[0].list[0].conv_id).toEqual(newSD);
  });

  test('should list task_history with id', async () => {
    await new Promise(r => setTimeout(r, 2000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.TASK_HISTORY,
        obj_id: taskObjIdSD,
        company_id: companyId,
        conv_id: newSD,
        id: 'test123',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].id).toEqual('test123');
    expect(response.body.ops[0].obj_id).toEqual(taskObjIdSD);
    expect(response.body.ops[0].list[0].conv_id).toEqual(newSD);
  });

  test('should show task by ref=ref', async () => {
    await new Promise(r => setTimeout(r, 1000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: newConv,
        ref: `ref`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].ref).toEqual('ref');
    expect(response.body.ops[0].data).toEqual({ task: 'two' });
  });

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id: companyId,
      }),
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newSD,
        company_id: companyId,
      }),
    );
  });
});
