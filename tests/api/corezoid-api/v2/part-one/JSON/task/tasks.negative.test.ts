import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj } from '../../../../../../../application/api/ApiObj';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  maxLength,
  stringNotValidTestCases,
  minLength,
  objectTestCases,
  securityTestCases,
  companyTestCases,
} from '../../../../../negativeCases';

describe('Task (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: string | number;
  let taskRef1: string | number;
  let taskObjId1: string | number;
  let newAlias: string | number;
  let nodeTask: string;
  let companyId: any;
  let valuesToSkip: any;
  let newProject: number;
  let project_short_name: string;
  let newStage: number;
  let stage_short_name: string;
  let alias_short_name: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    companyId = apikey.companies[0].id;
    project_short_name = `project_${Date.now()}`;
    stage_short_name = `stage_${Date.now()}`;
    alias_short_name = `alias_${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id: companyId,
        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id: companyId,
        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;

    const responseSD = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id: companyId,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: 0,
        conv_type: 'state',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    newConv = responseSD.body.ops[0].obj_id;

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id: companyId,
      }),
    );
    nodeTask = responseList.body.ops[0].list[1].obj_id;

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        ction: 'user',
        data: { a: '1' },
        ref: `ref_${Date.now()}`,
      }),
    );
    taskRef1 = responseTask.body.ops[0].ref;
    taskObjId1 = responseTask.body.ops[0].obj_id;

    const responseAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id: companyId,
        title: `${OBJ_TYPE.ALIAS}_${Date.now()}`,
        short_name: alias_short_name,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    newAlias = responseAlias.body.ops[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id: companyId,
        link: true,
        obj_to_type: 'conv',
        obj_to_id: newConv,
      }),
    );
  });

  valuesToSkip = [0, -1];

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't create task in conv with invalid conv_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: input,
        action: 'user',
        data: { a: '1' },
        ref: `ref_${Date.now()}`,
        id: 'test123',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([0, -1])(`shouldn't shouldn't create task in conv with invalid conv_id '%s'`, async input => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: input,
        action: 'user',
        data: { a: '1' },
        ref: `ref_${Date.now()}`,
        id: 'test123',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('conveyor_not_found');
    expect(response.body.ops[0].description).toEqual(`conveyor not found`);
  });

  valuesToSkip = [null];

  test.each(
    [...stringTestCases, ...maxLength, ...minLength]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't create task in conv with invalid ref '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        action: 'user',
        data: { a: '1' },
        ref: input,
        id: 'test123',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...objectTestCases, ...maxLength, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't create task in conv with invalid data '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          company_id: companyId,
          conv_id: newConv,
          action: 'user',
          data: input,
          ref: `ref_${Date.now()}`,
          id: '123',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...maxLength, ...undefinedTestCase, ...securityTestCases].map(({ input, errors }) => [
      input,
      errors,
    ]),
  )(`shouldn't shouldn't create task in conv with invalid obj_alias '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        obj_alias: input,
        action: 'user',
        data: { a: '1' },
        ref: `ref_${Date.now()}`,
        id: 'test123',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't create task in conv with invalid project_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,

        conv_id: newConv,
        action: 'user',
        data: { a: '1' },
        ref: `ref_${Date.now()}`,
        id: '123',
        project_id: input,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0, -1];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't create task in conv with invalid stage_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,

        conv_id: newConv,
        action: 'user',
        data: { a: '1' },
        ref: `ref_${Date.now()}`,
        id: '123',
        project_id: newProject,
        stage_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases, ...maxLength].map(({ input, errors }) => [
      input,
      errors,
    ]),
  )(`shouldn't shouldn't create task in conv with invalid stage_short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,

        obj_alias: alias_short_name,
        action: 'user',
        data: { a: '1' },
        ref: `ref_${Date.now()}`,
        id: '123',
        project_short_name,
        stage_short_name: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases, ...maxLength].map(({ input, errors }) => [
      input,
      errors,
    ]),
  )(`shouldn't shouldn't create task in conv with invalid project_short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,

        obj_alias: alias_short_name,
        action: 'user',
        data: { a: '1' },
        ref: `ref_${Date.now()}`,
        id: '123',
        project_short_name: input,
        stage_short_name: stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't show task in conv with invalid conv_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.TASK,
          company_id: companyId,

          conv_id: input,
          obj_id: taskObjId1,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't show task in conv with invalid ref '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.TASK,
          company_id: companyId,

          conv_id: newConv,
          ref: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't show task in conv with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.TASK,
          company_id: companyId,

          conv_id: newConv,
          obj_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't show task in conv with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.TASK,
          company_id: input,

          conv_id: input,
          obj_id: taskObjId1,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0, -1];

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't modify task in conv with invalid conv_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,

        conv_id: input,
        ref: taskRef1,
        data: { a: 1 },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([0, -1])(`shouldn't shouldn't modify task in conv with invalid conv_id '%s'`, async input => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,

        conv_id: input,
        ref: taskRef1,
        data: { a: 1 },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('conveyor_not_found');
    expect(response.body.ops[0].description).toEqual(`conveyor not found`);
  });

  valuesToSkip = [null];

  test.each(
    [...stringTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't modify task in conv with invalid ref '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,

        conv_id: newConv,
        ref: input,
        data: { a: 1 },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([undefined, null, '', 'test'])(
    `shouldn't shouldn't modify task in conv with invalid ref '%s'`,
    async input => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.TASK,
          company_id: companyId,

          conv_id: newConv,
          ref: input,
          data: { a: 1 },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('not_found_task');
      expect(response.body.ops[0].description).toEqual(`not found task`);
    },
  );

  test.each([...stringTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't modify task in conv with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.TASK,
          company_id: companyId,

          conv_id: newConv,
          obj_id: input,
          data: { a: 1 },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([undefined])(`shouldn't shouldn't modify task in conv with invalid obj_id '%s'`, async input => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,

        conv_id: newConv,
        obj_id: input,
        data: { a: 1 },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('not_found_task');
    expect(response.body.ops[0].description).toEqual(`not found task`);
  });

  test.each([...objectTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't modify task in conv with invalid data '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.TASK,
          company_id: companyId,

          conv_id: newConv,
          obj_id: taskObjId1,
          data: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't modify task in conv with invalid obj_alias '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,

        obj_alias: input,
        ref: taskRef1,
        data: { a: 1 },
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't modify task in conv with invalid project_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.TASK,
          company_id: companyId,

          obj_alias: alias_short_name,
          ref: taskRef1,
          data: { a: 1 },
          project_id: input,
          stage_id: newStage,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't modify task in conv with invalid stage_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.TASK,
          company_id: companyId,

          obj_alias: alias_short_name,
          ref: taskRef1,
          data: { a: 1 },
          project_id: newProject,
          stage_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't modify task in conv with invalid stage_short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,

        obj_alias: alias_short_name,
        ref: taskRef1,
        data: { a: 1 },
        project_short_name: project_short_name,
        stage_short_name: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't modify task in conv with invalid project_short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,

        obj_alias: alias_short_name,
        ref: taskRef1,
        data: { a: 1 },
        project_short_name: input,
        stage_short_name: stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't delete task in conv with invalid conv_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.TASK,
          company_id: companyId,

          conv_id: input,
          obj_id: taskObjId1,
          node_id: nodeTask,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...maxLength, ...stringNotValidTestCases].map(({ input, errors }) => [
      input,
      errors,
    ]),
  )(`shouldn't shouldn't delete task in conv with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,

        conv_id: newConv,
        obj_id: input,
        node_id: nodeTask,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...maxLength, ...stringNotValidTestCases].map(({ input, errors }) => [
      input,
      errors,
    ]),
  )(`shouldn't shouldn't delete task in conv with invalid node_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,

        conv_id: newConv,
        obj_id: nodeTask,
        node_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't delete task in conv with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.TASK,
          company_id: input,

          conv_id: newConv,
          obj_id: nodeTask,
          node_id: taskObjId1,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);

    const responseConv = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, companyId);
    expect(responseConv.status).toBe(200);
  });
});
