import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  stringNotValidTestCases,
  objectTestCases,
} from '../../../../../negativeCases';

describe('Debug Task (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newSD: string | number;
  let taskObjId: string | number;
  let companyId: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    companyId = apikey.companies[0].id;

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id: companyId,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: 0,
        conv_type: 'state',
      }),
    );
    newSD = responseConv.body.ops[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV,
        company_id: companyId,
        status: 'debug',
        obj_id: newSD,
      }),
    );

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newSD,
        data: { a: '1' },
        ref: `ref_${Date.now()}`,
      }),
    );
    taskObjId = responseTask.body.ops[0].obj_id;
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't STEP_NEXT task in conv with invalid conv_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.STEP_NEXT,
          obj: OBJ_TYPE.TASK,
          obj_id: taskObjId,
          company_id: companyId,
          data: { a: '2' },
          branch: 'logic',
          conv_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't STEP_NEXT task in conv with invalid obj_id '%s'`,
    async (input, errors) => {
      void errors;
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.STEP_NEXT,
          obj: OBJ_TYPE.TASK,
          obj_id: input,
          company_id: companyId,
          data: { a: '2' },
          branch: 'logic',
          conv_id: newSD,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('ref_or_obj_id_is_not_string_format');
    },
  );

  test.each([...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't STEP_NEXT task in conv with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.STEP_NEXT,
          obj: OBJ_TYPE.TASK,
          obj_id: input,
          company_id: companyId,
          data: { a: '2' },
          branch: 'logic',
          conv_id: newSD,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...undefinedTestCase, ...objectTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't STEP_NEXT task in conv with invalid data '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.STEP_NEXT,
          obj: OBJ_TYPE.TASK,
          obj_id: taskObjId,
          company_id: companyId,
          data: input,
          branch: 'logic',
          conv_id: newSD,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't STEP_PREV task in conv with invalid conv_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.STEP_PREV,
          obj: OBJ_TYPE.TASK,
          obj_id: taskObjId,
          company_id: companyId,
          data: { a: '2' },
          branch: 'logic',
          conv_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't STEP_PREV task in conv with invalid obj_id '%s'`,
    async (input, errors) => {
      void errors;
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.STEP_PREV,
          obj: OBJ_TYPE.TASK,
          obj_id: input,
          company_id: companyId,
          data: { a: '2' },
          branch: 'logic',
          conv_id: newSD,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('ref_or_obj_id_is_not_string_format');
    },
  );

  test.each([...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't STEP_PREV task in conv with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.STEP_PREV,
          obj: OBJ_TYPE.TASK,
          obj_id: input,
          company_id: companyId,
          data: { a: '2' },
          branch: 'logic',
          conv_id: newSD,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...undefinedTestCase, ...objectTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't STEP_PREV task in conv with invalid data '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.STEP_PREV,
          obj: OBJ_TYPE.TASK,
          obj_id: taskObjId,
          company_id: companyId,
          data: input,
          branch: 'logic',
          conv_id: newSD,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't STEP_GOTO task in conv with invalid conv_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.STEP_GOTO,
          obj: OBJ_TYPE.TASK,
          obj_id: taskObjId,
          company_id: companyId,
          data: { a: '2' },
          branch: 'logic',
          conv_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't STEP_GOTO task in conv with invalid obj_id '%s'`,
    async (input, errors) => {
      void errors;
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.STEP_GOTO,
          obj: OBJ_TYPE.TASK,
          obj_id: input,
          company_id: companyId,
          data: { a: '2' },
          branch: 'logic',
          conv_id: newSD,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('ref_or_obj_id_is_not_string_format');
    },
  );

  test.each([...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't STEP_GOTO task in conv with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.STEP_GOTO,
          obj: OBJ_TYPE.TASK,
          obj_id: input,
          company_id: companyId,
          data: { a: '2' },
          branch: 'logic',
          conv_id: newSD,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...undefinedTestCase, ...objectTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't STEP_GOTO task in conv with invalid data '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.STEP_GOTO,
          obj: OBJ_TYPE.TASK,
          obj_id: taskObjId,
          company_id: companyId,
          data: input,
          branch: 'logic',
          conv_id: newSD,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newSD,
        company_id: companyId,
      }),
    );
  });
});
