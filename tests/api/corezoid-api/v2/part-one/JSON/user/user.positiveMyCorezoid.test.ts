import { application } from '../../../../../../../application/Application';
import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createSchema from '../../../../schemas/v2/users-groups/createApiKeySchema.json';
import modifyUserScheme from '../../../../schemas/v2/users-groups/modifyUserSchema.json';
import showUserScheme from '../../../../schemas/v2/users-groups/showUserSchema.json';
import linkUserScheme from '../../../../schemas/v2/users-groups/linkUserSchema.json';
import deleteSchema from '../../../../schemas/v2/users-groups/deleteApiKeySchema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj } from '../../../../../../../application/api/ApiObj';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';

describe('user (positive)', () => {
  let api: ApiKeyClient;
  let apiSuper: ApiKeyClient;
  let newApiKeyMyCorezoid: string | number;
  let company_id: any;
  let titleForMyCorezoid: any;
  let apikey: ApiKey;
  let apikeySuper: ApiKey;
  let groupObjId: ApiKey;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    apikeySuper = await application.getApiKeySuper();
    apiSuper = application.getApiKeyClient(apikeySuper);
    company_id = apikey.companies[0].id;
    titleForMyCorezoid = `title-${Date.now()}`;
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        obj_type: 'admins',
        title: titleForMyCorezoid,
      }),
    );
    expect(response.status).toBe(200);
    groupObjId = response.body.ops[0].obj_id;
  });

  test('should create user in MyCorezoid', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        title: titleForMyCorezoid,
        logins: [{ type: 'api' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('user');
    expect(response.body.ops[0].users[0].created).toBe(true);
    expect(response.body.ops[0].users[0].title).toBe(titleForMyCorezoid);
    newApiKeyMyCorezoid = response.body.ops[0].users[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should modify user in Company', async () => {
    titleForMyCorezoid = 'new' + titleForMyCorezoid;
    const response1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: newApiKeyMyCorezoid,
        group_id: groupObjId,
        level: 1,
      }),
    );
    expect(response1.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER,
        obj_id: newApiKeyMyCorezoid,
        title: titleForMyCorezoid,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('user');
    expect(response.body.ops[0].title).toBe(titleForMyCorezoid);
    SchemaValidator.validate(modifyUserScheme, response.body);

    const response3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: newApiKeyMyCorezoid,
        group_id: groupObjId,
        level: 2,
      }),
    );
    expect(response3.status).toBe(200);
  });

  test('should show user in MyCorezoid', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        obj_id: newApiKeyMyCorezoid,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('user');
    expect(response.body.ops[0].obj_id).toBe(newApiKeyMyCorezoid);
    expect(response.body.ops[0].title).toBe(titleForMyCorezoid);

    SchemaValidator.validate(showUserScheme, response.body);
  });

  test('should link API key to group in MyCorezoid', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: newApiKeyMyCorezoid,
        group_id: groupObjId,
        level: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('user');
    expect(response.body.ops[0].obj_id).toBe(newApiKeyMyCorezoid);
    SchemaValidator.validate(linkUserScheme, response.body);
  });

  test('should unlink API key to group with int value !=1 in MyCorezoid', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: newApiKeyMyCorezoid,
        group_id: groupObjId,
        level: 2,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('user');
    expect(response.body.ops[0].obj_id).toBe(newApiKeyMyCorezoid);
    SchemaValidator.validate(linkUserScheme, response.body);
  });

  test('should unlink API key to group with value level = undefined in MyCorezoid', async () => {
    const response1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: newApiKeyMyCorezoid,
        group_id: groupObjId,
        level: 1,
      }),
    );
    expect(response1.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: newApiKeyMyCorezoid,
        group_id: groupObjId,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('user');
    expect(response.body.ops[0].obj_id).toBe(newApiKeyMyCorezoid);
    SchemaValidator.validate(linkUserScheme, response.body);
  });

  test('should unlink API key to group with value level = null in MyCorezoid', async () => {
    const response1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: newApiKeyMyCorezoid,
        group_id: groupObjId,
        level: 1,
      }),
    );
    expect(response1.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: newApiKeyMyCorezoid,
        group_id: groupObjId,
        level: null,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('user');
    expect(response.body.ops[0].obj_id).toBe(newApiKeyMyCorezoid);
    SchemaValidator.validate(linkUserScheme, response.body);
  });

  test('should delete user in MyCorezoid', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: newApiKeyMyCorezoid,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_id).toBe(newApiKeyMyCorezoid);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should create user in MyCorezoid in group', async () => {
    titleForMyCorezoid = `title-${Date.now()}`;
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,

        title: titleForMyCorezoid,
        logins: [{ type: 'api' }],
        group_id: groupObjId,
        send_invite_if_user_not_exists: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('user');
    expect(response.body.ops[0].users[0].created).toBe(true);
    expect(response.body.ops[0].users[0].title).toBe(titleForMyCorezoid);
    newApiKeyMyCorezoid = response.body.ops[0].users[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should show users groups in Company with obj_type = company (for superadmin)', async () => {
    const response = await apiSuper.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        obj_id: newApiKeyMyCorezoid,
        obj_type: 'company',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('user');
    expect(response.body.ops[0].obj_id).toBe(newApiKeyMyCorezoid);
    expect(response.body.ops[0].title).toBe(titleForMyCorezoid);
    expect(response.body.ops[0].groups[0].id).toBe(groupObjId);
    SchemaValidator.validate(showUserScheme, response.body);
  });

  afterAll(async () => {
    const responseCompany = await requestDeleteObj(api, OBJ_TYPE.USER, newApiKeyMyCorezoid, null);
    expect(responseCompany.status).toBe(200);
    const responseGroup = await requestDeleteObj(api, OBJ_TYPE.GROUP, groupObjId, company_id);
    expect(responseGroup.status).toBe(200);
  });
});
