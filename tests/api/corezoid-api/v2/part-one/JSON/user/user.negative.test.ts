import { application } from '../../../../../../../application/Application';
import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import faker from 'faker';

describe('user (negative)', () => {
  let api: ApiKeyClient;
  let company_id: any;
  let titleForCompany: any;
  let apikey: ApiKey;
  let randInt: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    titleForCompany = `title-${Date.now()}`;
    randInt = Date.now();
  });

  test.each([1, 1234567890, 0, true, {}, []])(
    `should't create user in Company with invalid company_id`,
    async param => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.USER,
          company_id: param,
          title: titleForCompany,
          logins: [{ type: 'api' }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.request_proc).toBe('ok');
      expect(response.body.ops[0].proc).toBe('error');
      expect(response.body.ops[0].description).toEqual('Value is not valid');
      expect(response.body.ops[0].key).toEqual('company_id');
      expect(response.body.ops[0].value).toEqual(param);
    },
  );

  test.each([
    ['test', 'You do not have permission to perform this action'],
    ['i000000000', 'You do not have permission to perform this action'],
  ])(`shouldn't create user with invalid company_id '%s'`, async (param, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id: param,
        title: titleForCompany,
        logins: [{ type: 'api' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [1234, `Value '1234' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [faker.random.alphaNumeric(256), `Value is not valid. Value's byte_size is more than maximum allowed: 255`],
  ])(`shouldn't create user with invalid title '%s'`, async (param, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: param,
        logins: [{ type: 'api' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
    expect(response.body.ops[0].key).toEqual('title');
    expect(response.body.ops[0].value).toEqual(param);
  });

  test(`shouldn't create user with invalid title 'undefined'`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: undefined,
        logins: [{ type: 'api' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(`Key 'title' is required`);
  });

  test.each([
    [null, `Value is not valid`],
    [true, `Value is not valid`],
    ['test', `Value is not valid`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
  ])(`shouldn't create user with invalid group_id '%s'`, async (param, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: titleForCompany,
        logins: [{ type: 'api' }],
        group_id: param,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
    expect(response.body.ops[0].key).toEqual('group_id');
  });

  test(`shouldn't create user with non exist group`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: titleForCompany,
        logins: [{ type: 'api' }],
        group_id: randInt,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(`Object group with id ${randInt} does not exist`);
  });

  test.each([1234, 0, true, 'test', null, undefined, 'corezoid'])(
    `shouldn't create user with invalid 'type' '%s'`,
    async param => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.USER,
          company_id,
          title: 'API',
          logins: [{ type: param }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.request_proc).toBe('ok');
      expect(response.body.ops[0].proc).toBe('error');
      expect(response.body.ops[0].description).toEqual('Bad login type or login is required');
    },
  );

  test.each([1, 1234567890, 0, true, {}, [], null])(
    `should't modify user in Company with invalid company_id`,
    async param => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.USER,
          obj_id: randInt,
          company_id: param,
          title: titleForCompany,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.request_proc).toBe('ok');
      expect(response.body.ops[0].proc).toBe('error');
      expect(response.body.ops[0].description).toEqual('user is not found');
    },
  );
  test.each([
    [null, `title has not valid type, expected types [binary]`],
    [1234, `title has not valid type, expected types [binary]`],
    [true, `title has not valid type, expected types [binary]`],
    [-1, `title has not valid type, expected types [binary]`],
    [0, `title has not valid type, expected types [binary]`],
    [[], `title has not valid type, expected types [binary]`],
    [{}, `title has not valid type, expected types [binary]`],
    [faker.random.alphaNumeric(256), `title more than maximum allowed size (range) 255`],
  ])(`shouldn't modify user with invalid title '%s'`, async (param, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER,
        obj_id: randInt,
        company_id,
        title: param,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test(`shouldn't modify user with invalid title 'undefined'`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER,
        obj_id: randInt,
        company_id,
        title: undefined,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual('title field is missing');
  });

  test.each([true, null, {}, []])(`shouldn't modify user with invalid 'obj_id'`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER,
        obj_id: param,
        company_id,
        title: 'API',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual('obj_id has not valid type, expected types [integer, binary]');
  });

  test(`shouldn't modify user with invalid obj_id 'undefined'`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER,
        obj_id: undefined,
        company_id,
        title: 'API',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual('obj_id field is missing');
  });

  test.each([
    [true, 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [null, `Value is not valid`],
    ['test', `Value is not valid`],
  ])(`shouldn't show user with invalid obj_id`, async (invalidObjId, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        company_id,
        obj_id: invalidObjId,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test(`shouldn't show user with non exist obj_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        company_id,
        obj_id: randInt,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(`User ${randInt} not found.`);
  });

  test.each([1, 1234567890, 0, true])(`should't show user in Company with invalid company_id`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        company_id: param,
        obj_id: randInt,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual('Value is not valid');
    expect(response.body.ops[0].key).toEqual('company_id');
    expect(response.body.ops[0].value).toEqual(param);
  });

  test.each([
    [true, 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [null, `Value is not valid`],
    ['test', `Value is not valid`],
    [undefined, `Key 'obj_id' is required`],
  ])(`shouldn't link user to group with invalid obj_id`, async (invalidObjId, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        company_id,
        obj_id: invalidObjId,
        group_id: randInt,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test(`shouldn't link user to group with non exist obj_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        company_id,
        obj_id: randInt,
        group_id: randInt,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual('User not in company');
  });

  test.each([1, 1234567890, 0, true])(`should't link user in Company with invalid company_id`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        company_id: param,
        obj_id: randInt,
        group_id: randInt,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual('Value is not valid');
    expect(response.body.ops[0].key).toEqual('company_id');
    expect(response.body.ops[0].value).toEqual(param);
  });

  test.each([
    [true, 'User not in company'],
    [[], 'User not in company'],
    [{}, 'User not in company'],
    ['test', `User not in company`],
  ])(`shouldn't link user to group with invalid level`, async (invalidLevel, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        company_id,
        obj_id: randInt,
        group_id: randInt,
        level: invalidLevel,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [null, `Value is not valid`],
    ['test', `Value is not valid`],
  ])(`shouldn't delete user to group with invalid obj_id`, async (invalidObjId, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        company_id,
        obj_id: invalidObjId,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test(`shouldn't delete user to group with non exist obj_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        company_id,
        obj_id: randInt,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(`User ${randInt} not found.`);
  });

  test.each([1, 1234567890, 0, true])(`should't delete user in Company with invalid company_id`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        company_id: param,
        obj_id: randInt,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual('Value is not valid');
    expect(response.body.ops[0].key).toEqual('company_id');
    expect(response.body.ops[0].value).toEqual(param);
  });

  test.each([
    [123, `Value '123' is not valid. Type of value is not 'boolean'`],
    ['test', `Value 'test' is not valid. Type of value is not 'boolean'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'boolean'`],
    [[], `Value '[]' is not valid. Type of value is not 'boolean'`],
    [null, `Value 'null' is not valid. Type of value is not 'boolean'`],
  ])(`shouldn't create user with invalid send_invite_if_user_not_exists`, async (param, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: titleForCompany,
        logins: [{ type: 'api' }],
        send_invite_if_user_not_exists: param,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].key).toEqual('send_invite_if_user_not_exists');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [123, `Value '123' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    ['test', `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"company\\\">>]\">>`],
  ])(`shouldn't show user with invalid obj_type`, async (invalidObjType, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        company_id,
        obj_id: randInt,
        obj_type: invalidObjType,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });
});
