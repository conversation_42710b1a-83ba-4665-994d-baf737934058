import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import modifySchema from '../../../../schemas/v2/actions-objects/modifyConv_params.schema.json';
import listSchema from '../../../../schemas/v2/actions-objects/listConv_params.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('Conv_params (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: string | number;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        folder_id: 0,
        obj_type: 0,
        conv_type: 'process',
        status: 'active',
        title: `Process_${Date.now()}`,
        create_mode: 'without_nodes',
      }),
    );
    newConv = response.body.ops[0].obj_id;
  });

  test('should list conv_params = []', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV_PARAMS,
        obj_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should modify conv_params with required param', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV_PARAMS,
        obj_id: newConv,

        params: [
          { name: 'amount', descr: '', type: 'string', flags: [], regex: '', regex_error_text: '' },
          { name: 'a', descr: 'a', type: '', flags: ['required', 'input'], regex: '', regex_error_text: '' },
        ],
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(modifySchema, response.body);
  });

  test('should modify conv_params', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV_PARAMS,
        obj_id: newConv,
        company_id,

        ref_mask: false,
        params: [
          { name: 'amount', descr: '', type: 'string', flags: [], regex: '', regex_error_text: '' },
          { name: 'a', descr: 'a', type: 'number', flags: ['required', 'input'], regex: '', regex_error_text: '' },
        ],
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(modifySchema, response.body);
  });

  test('should list conv_params after added parameter', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV_PARAMS,
        obj_id: newConv,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(listSchema, response.body);
    expect(response.body.ops[0].params[0].name).toEqual('amount');
    expect(response.body.ops[0].params[0].flags).toEqual([]);
    expect(response.body.ops[0].params[0].type).toEqual('string');
    expect(response.body.ops[0].params[1].name).toEqual('a');
    expect(response.body.ops[0].params[1].descr).toEqual('a');
    expect(response.body.ops[0].params[1].type).toEqual('number');
    expect(response.body.ops[0].params[1].flags).toEqual(['required', 'input']);
    expect(response.body.ops[0].ref_mask).toEqual(false);
  });

  test('should modify conv_params_2', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV_PARAMS,
        obj_id: newConv,
        company_id,

        ref_mask: true,
        params: [
          { name: 'amount', descr: '', type: 'string', flags: [], regex: '', regex_error_text: '' },
          {
            name: 'a',
            descr: 'a',
            type: 'number',
            flags: ['required', 'input', 'auto-clear'],
            regex: '',
            regex_error_text: '',
          },
          {
            name: 'test',
            descr: 'test',
            type: 'boolean',
            flags: ['required', 'output', 'auto-clear'],
            regex: '',
            regex_error_text: '',
          },
        ],
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(modifySchema, response.body);
  });

  test('should list conv_params after added parameter', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV_PARAMS,
        obj_id: newConv,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(listSchema, response.body);
    expect(response.body.ops[0].params[0].name).toEqual('amount');
    expect(response.body.ops[0].params[0].flags).toEqual([]);
    expect(response.body.ops[0].params[0].type).toEqual('string');
    expect(response.body.ops[0].params[1].name).toEqual('a');
    expect(response.body.ops[0].params[1].type).toEqual('number');
    expect(response.body.ops[0].params[1].flags).toEqual(['required', 'input', 'auto-clear']);
    expect(response.body.ops[0].params[2].name).toEqual('test');
    expect(response.body.ops[0].params[2].type).toEqual('boolean');
    expect(response.body.ops[0].params[2].flags).toEqual(['required', 'output', 'auto-clear']);
    expect(response.body.ops[0].ref_mask).toEqual(true);
  });

  test('should modify conv_params_3', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV_PARAMS,
        obj_id: newConv,
        company_id,

        ref_mask: true,
        params: [
          { name: 'amount', descr: '', type: 'string', flags: [], regex: '', regex_error_text: '' },
          {
            name: 'a',
            descr: 'a',
            type: 'number',
            flags: ['required', 'input', 'auto-clear'],
            regex: 'test',
            regex_error_text: 'error',
          },
          {
            name: 'test',
            descr: 'test',
            type: 'boolean',
            flags: ['output', 'auto-clear'],
            regex: '',
            regex_error_text: '',
          },
        ],
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(modifySchema, response.body);
  });

  test('should list conv_params after added parameter', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV_PARAMS,
        obj_id: newConv,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(listSchema, response.body);
    expect(response.body.ops[0].params[0].name).toEqual('amount');
    expect(response.body.ops[0].params[0].flags).toEqual([]);
    expect(response.body.ops[0].params[0].type).toEqual('string');
    expect(response.body.ops[0].params[1].name).toEqual('a');
    expect(response.body.ops[0].params[1].type).toEqual('number');
    expect(response.body.ops[0].params[1].regex).toEqual('test');
    expect(response.body.ops[0].params[1].regex_error_text).toEqual('error');
    expect(response.body.ops[0].params[1].flags).toEqual(['required', 'input', 'auto-clear']);
    expect(response.body.ops[0].params[2].name).toEqual('test');
    expect(response.body.ops[0].params[2].type).toEqual('boolean');
    expect(response.body.ops[0].params[2].flags).toEqual(['output', 'auto-clear']);
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
