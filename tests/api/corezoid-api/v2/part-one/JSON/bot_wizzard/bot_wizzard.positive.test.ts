import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { application } from '../../../../../../../application/Application';
import { User } from '../../../../../../../infrastructure/model/User';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createSchema from '../../../../schemas/v2/bot_wizzard/createBot.schema.json';
import checkSchema from '../../../../schemas/v2/bot_wizzard/checkBot.schema.json';
import modifySchema from '../../../../schemas/v2/bot_wizzard/modifyBot.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { ApiUserClient } from '../../../../../../../application/api/ApiUserClient';
import { createAuthUser, Method } from '../../../../../../../utils/request';

describe('Bot_wizzard (positive)', () => {
  let newProject: string | number;
  let newStage: string | number;
  let obj_id: string;
  let company_id: any;
  let user: User;
  let cookieUser: any;
  let hostSS: string;
  let apiUser: ApiUserClient;

  beforeAll(async () => {
    const config = ConfigurationManager.getConfiguration();
    hostSS = config.getSSUrl();

    user = await application.getAuthorizedUser({}, 0);
    cookieUser = createAuthUser(user.cookieUser, 'cookie');
    apiUser = await application.getApiUserClient(user);

    const responseCompany = await apiUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,
        name: 'test',
        description: 'company',
        site: 'corezoid.com',
      }),
    );
    expect(responseCompany.status).toBe(200);
    expect(responseCompany.body.ops[0].proc).toBe('ok');
    company_id = responseCompany.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 20000));

    const responseProject = await apiUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        title: `Project_${Date.now()}`,
        short_name: `project-${Date.now()}`,
        description: 'test',
        stages: [],
        status: 'active',
        company_id,
      }),
    );
    expect(responseProject.status).toBe(200);
    expect(responseProject.body.ops[0].proc).toBe('ok');
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await apiUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        title: `Stage_${Date.now()}`,
        short_name: `stage-${Date.now()}`,
        description: 'test',
        project_id: newProject,
        company_id,
      }),
    );
    expect(responseStage.status).toBe(200);
    expect(responseStage.body.ops[0].proc).toBe('ok');
    newStage = responseStage.body.ops[0].obj_id;
  });

  test(`should create Bot_wizzard (async: true)`, async () => {
    await new Promise(r => setTimeout(r, 20000));
    const response = await apiUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.BOT_WIZZARD,
        lang: `en`,
        version: 2,
        async: true,
        project_id: newProject,
        stage_id: newStage,
        messengers: [{ channel: 'telegram', key: '**********************************************' }],
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('bot_wizzard');
    obj_id = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`should check Bot_wizzard `, async () => {
    await new Promise(r => setTimeout(r, 40000));
    const response = await apiUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.BOT_WIZZARD,
        obj_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('bot_wizzard');
    expect(response.body.ops[0].result).toBe('ok');
    SchemaValidator.validate(checkSchema, response.body);

    const responseList = await apiUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStage,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[0].obj_type).toBe('folder');
    expect(responseList.body.ops[0].list[0].title).toBe('Communications Orchestrator');
  });

  test(`should modify Bot_wizzard `, async () => {
    const response = await apiUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.BOT_WIZZARD,
        obj_id,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('bot_wizzard');
    SchemaValidator.validate(modifySchema, response.body);
  });

  test.skip(`should create Bot_wizzard (async: false)`, async () => {
    const response = await apiUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.BOT_WIZZARD,
        lang: `en`,
        version: 2,
        async: false,
        project_id: newProject,
        stage_id: newStage,
        messengers: [{ channel: 'abc', key: '8847e1d5-fb05-4700-86b9-f1a9291f2ce1' }],
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('bot_wizzard');
    SchemaValidator.validate(checkSchema, response.body);
  });

  test(`should create Bot_wizzard (only required)`, async () => {
    const response = await apiUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.BOT_WIZZARD,
        version: 2,
        messengers: [{ channel: 'viber', key: '47507eb35367d40c-2487bb76e2ec831a-5b242f9eaf064aaf' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('bot_wizzard');
  });

  afterAll(async () => {
    const responseStage = await apiUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(responseStage.status).toBe(200);

    const response = await cookieUser.request({
      method: Method.DELETE,
      url: `${hostSS}face/api/1/workspaces/${company_id}`,
    });
    expect(response.status).toBe(200);
  });
});
