import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  maxLength,
  securityTestCases,
  stringNotValidTestCases,
  boolTestCases,
  companyTestCases,
  arrayTestCases,
} from '../../../../../negativeCases';

describe('Bot_wizzard (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let company_id: any;
  let project_short_name: string;
  let stage_short_name: string;
  let valuesToSkip: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = `project-${Date.now()}`;
    stage_short_name = `stage-${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;
  });

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't create bot_wizzard with invalid lang '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.BOT_WIZZARD,

          lang: input,
          version: 2,
          async: true,
          project_id: newProject,
          stage_id: newStage,
          messengers: [{ channel: 'telegram', key: '**********************************************' }],
          company_id,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0, -1];

  test.each(
    [...integerTestCases, ...securityTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create bot_wizzard with invalid version '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.BOT_WIZZARD,

        lang: 'en',
        version: input,
        async: true,
        project_id: newProject,
        stage_id: newStage,
        messengers: [{ channel: 'telegram', key: '**********************************************' }],
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...boolTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't create bot_wizzard with invalid async '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.BOT_WIZZARD,

          lang: 'en',
          version: 2,
          async: input,
          project_id: newProject,
          stage_id: newStage,
          messengers: [{ channel: 'telegram', key: '**********************************************' }],
          company_id,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0];

  test.each(
    [...integerTestCases, ...securityTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create bot_wizzard with invalid project_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.BOT_WIZZARD,

        lang: 'en',
        version: 2,
        async: true,
        project_id: input,
        stage_id: newStage,
        messengers: [{ channel: 'telegram', key: '**********************************************' }],
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0, -1];

  test.each(
    [...integerTestCases, ...securityTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create bot_wizzard with invalid stage_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.BOT_WIZZARD,

        lang: 'en',
        version: 2,
        async: true,
        project_id: newProject,
        stage_id: input,
        messengers: [{ channel: 'telegram', key: '**********************************************' }],
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = ['te'];

  test.each(
    [...companyTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create bot_wizzard with invalid compan_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.BOT_WIZZARD,

        lang: 'en',
        version: 2,
        async: true,
        project_id: newProject,
        stage_id: newStage,
        messengers: [{ channel: 'telegram', key: '**********************************************' }],
        company_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...arrayTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't create bot_wizzard with invalid messengers '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.BOT_WIZZARD,

          lang: 'en',
          version: 2,
          async: true,
          project_id: newProject,
          stage_id: newStage,
          messengers: input,
          company_id,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [
      ...stringTestCases,
      ...stringNotValidTestCases,
      ...securityTestCases,
      ...undefinedTestCase,
      ...maxLength,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't check bot_wizzard with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.BOT_WIZZARD,
        obj_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify bot_wizzard with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.BOT_WIZZARD,
          obj_id: input,
          company_id,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
