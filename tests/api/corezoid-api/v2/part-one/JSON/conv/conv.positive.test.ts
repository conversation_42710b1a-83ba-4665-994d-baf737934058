import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createSchema from '../../../../schemas/v2/actions-objects/createProcess.schema.json';
import deleteSchema from '../../../../schemas/v2/actions-objects/deleteObject.schema.json';
import destroySchema from '../../../../schemas/v2/actions-objects/destroyObject.schema.json';
import modifySchema from '../../../../schemas/v2/actions-objects/modifyProcess.schema.json';
import restoreSchema from '../../../../schemas/v2/actions-objects/restoreObject.schema.json';
import favoriteSchema from '../../../../schemas/v2/actions-objects/favoriteObject.schema.json';
import showSchema from '../../../../schemas/v2/actions-objects/showProcess.schema.json';
import linkSchema from '../../../../schemas/v2/actions-objects/linkMoveFolder.schema.json';
import linkObjSchema from '../../../../schemas/v2/actions-objects/linkObject.schema.json';
import listConv from '../../../../schemas/v2/actions-objects/listConv.schema.json';
import copySync from '../../../../schemas/v2/actions-objects/copyConvSync.schema.json';
import copyAsync from '../../../../schemas/v2/actions-objects/copyObjectAsync.schema.json';
import listConvsSchema from '../../../../schemas/v2/actions-objects/listConvs.schema.json';
import listConvsGroupSchema from '../../../../schemas/v2/actions-objects/listConvListObjGroup.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { makeRequestWithRetries, MyApiResponse } from '../../../../../../../utils/requestRetries';

describe.each(['Process', 'State'])('%s (positive)', data => {
  let api: ApiKeyClient;
  let apiS: ApiKeyClient;
  let apikey: ApiKey;
  let apikeyS: ApiKey;
  let apiKey2: string | number;
  let newConv: string | number;
  let newConv2: string | number;
  let newConv3: string | number;
  let newConv4: string | number;
  let newConv5: string | number;
  let newConv6: string | number;
  let newConv7: string | number;
  let newFolder: string | number;
  let copyConvSync: string | number;
  let copyConvSyncMyCorezoid: string | number;
  let company_id: any;
  let conv_title: string;
  let owner: string | number;
  let project_id: string | number;
  let stage_id: string | number;
  let group_id: number;
  let short_name_project: string;
  const type = data.toLocaleLowerCase();

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    apikeyS = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apikeyS);
    company_id = apikey.companies[0].id;
    conv_title = `Process_${Date.now()}`;
    owner = +apikey.id;
    short_name_project = `project-${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: short_name_project,
        description: 'test',
        stages: [
          { title: 'develop', immutable: false },
          { title: 'develop2', immutable: false },
        ],
        status: 'active',
      }),
    );
    project_id = responseProject.body.ops[0].obj_id;
    stage_id = responseProject.body.ops[0].stages[0];

    const CreateKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(200);
    const user = CreateKeyResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    apiKey2 = +newApiKey.id;

    const responseGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,

        obj_type: `admins`,
        title: 'Group',
      }),
    );
    group_id = responseGroup.body.ops[0].obj_id;

    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: apiKey2,
        company_id,

        group_id,
        level: 1,
      }),
    );
    expect(responseLink.status).toBe(200);
  });

  test(`should create ${type}`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        folder_id: 0,
        title: conv_title,
        status: 'active',
        obj_type: 0,
        description: 'test',
        create_mode: 'without_nodes',
        conv_type: type,
      }),
    );
    expect(response.status).toBe(200);
    newConv = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`should list ${type} with required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newConv);
    expect(response.body.ops[0].title).toBe(conv_title);
    expect(response.body.ops[0].description).toBe('test');
    SchemaValidator.validate(listConv, response.body);
  });

  test(`should create ${type} without parameters`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        folder_id: undefined,
        title: undefined,
        status: undefined,
        obj_type: undefined,
        description: undefined,
        create_mode: undefined,
        conv_type: undefined,
      }),
    );
    expect(response.status).toBe(200);
    newConv2 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`should create ${type} my corezoid`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id: null,

        folder_id: 0,
        title: conv_title,
        status: 'active',
        obj_type: 0,
        create_mode: 'without_nodes',
        conv_type: type,
      }),
    );
    expect(response.status).toBe(200);
    newConv3 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`should show ${type} with company`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newConv);
    SchemaValidator.validate(showSchema, response.body);
  });

  test(`should show ${type} without company`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv3,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newConv3);
    SchemaValidator.validate(showSchema, response.body);
  });

  test(`should link ${type}`, async () => {
    const responseCreateFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: 0,
        title: `ForLink`,
        status: 'active',
        obj_type: 0,
      }),
    );
    newFolder = responseCreateFolder.body.ops[0].obj_id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newConv,
        company_id,
        folder_id: newFolder,
        parent_id: 0,
        obj_type: 'conv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_type).toBe('conv');
    expect(response.body.ops[0].obj_id).toBe(newConv);
    expect(response.body.ops[0].obj_to_id).toBe(newFolder);
    expect(response.body.ops[0].obj_to_title).toBe('ForLink');
    expect(response.body.ops[0].from_folder).toBe(0);
    expect(response.body.ops[0].to_folder).toBe(newFolder);
    expect(response.body.ops[0].to_folder_name).toBe('ForLink');
    SchemaValidator.validate(linkSchema, response.body);
  });

  test(`should create ${type} in folder`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        folder_id: newFolder,
        title: conv_title,
        status: 'active',
        obj_type: 0,
        create_mode: 'without_nodes',
        conv_type: type,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`should create ${type} in folder with status`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        folder_id: newFolder,
        title: conv_title,
        status: 'paused',
        obj_type: 0,
        create_mode: 'without_nodes',
        conv_type: type,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(createSchema, response.body);

    const responseDebug = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        folder_id: newFolder,
        title: conv_title,
        status: 'debug',
        obj_type: 0,
        create_mode: 'without_nodes',
        conv_type: type,
      }),
    );
    expect(responseDebug.status).toBe(200);
    SchemaValidator.validate(createSchema, responseDebug.body);

    const responseBlocked = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        folder_id: newFolder,
        title: conv_title,
        status: 'blocked',
        obj_type: 0,
        create_mode: 'without_nodes',
        conv_type: type,
      }),
    );
    expect(responseBlocked.status).toBe(200);
    SchemaValidator.validate(createSchema, responseBlocked.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ status: 'active' })]),
    );
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ status: 'paused' })]),
    );
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ status: 'debug' })]),
    );
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ status: 'blocked' })]),
    );
  });

  test(`should create ${type} in stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        title: conv_title,
        status: 'active',
        obj_type: 0,
        conv_type: type,
        project_id,
        stage_id,
      }),
    );
    expect(response.status).toBe(200);
    newConv4 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const response2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        title: conv_title,
        status: 'active',
        obj_type: 0,
        create_mode: 'without_nodes',
        conv_type: type,
        project_short_name: short_name_project,
        stage_short_name: 'develop',
      }),
    );
    expect(response2.status).toBe(200);
    newConv5 = response2.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response2.body);

    const response3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        title: conv_title,
        status: 'active',
        obj_type: 0,
        create_mode: 'without_nodes',
        conv_type: type,
        project_short_name: short_name_project,
        stage_id,
      }),
    );
    expect(response3.status).toBe(200);
    newConv6 = response3.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response3.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: stage_id,
        company_id,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv4 })]),
    );
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv5 })]),
    );
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv6 })]),
    );
  });

  test(`should modify ${type} in stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv4,
        company_id,

        title: 'modify',
        status: 'debug',
        description: 'modify',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].new_status).toBe(5);
    expect(response.body.ops[0].old_status).toBe(1);
    SchemaValidator.validate(modifySchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: stage_id,
        company_id,
      }),
    );
    expect(responseList.status).toBe(200);
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newConv4).title).toEqual(
      'modify',
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newConv4).description).toEqual(
      'modify',
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newConv4).status).toEqual(
      'debug',
    );
  });

  test(`should modify ${type} in my corezoid`, async () => {
    const responseMod = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv3,
        company_id: null,

        title: 'modify',
        status: 'paused',
        description: 'modify',
      }),
    );
    expect(responseMod.status).toBe(200);
    expect(responseMod.body.ops[0].new_status).toBe(2);
    expect(responseMod.body.ops[0].old_status).toBe(1);
    expect(responseMod.body.ops[0].is_changed).toBe(true);
    SchemaValidator.validate(modifySchema, responseMod.body);

    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === newConv3)?.title === 'modify' &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === newConv3)?.description === 'modify' &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === newConv3)?.status === 'paused'
      );
    }

    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.FOLDER,
          obj_id: 0,
          company_id: null,
        }),
      );
    }, checkConditions);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test(`should modify ${type} with required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv4,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].new_status).toBe(5);
    expect(response.body.ops[0].old_status).toBe(5);
    expect(response.body.ops[0].is_changed).toBe(false);
    SchemaValidator.validate(modifySchema, response.body);
  });

  test(`should modify ${type} blocked reason`, async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv4,
        company_id,
        title: 'modify',
        status: 'blocked',
        description: 'modify',
        blocked_reason: 'test_blocked',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].new_status).toBe(4);
    expect(response.body.ops[0].old_status).toBe(5);
    expect(response.body.ops[0].is_changed).toBe(true);
    SchemaValidator.validate(modifySchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv4,
        company_id,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].status).toBe('blocked');
  });

  test(`should list ${type} with company`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv2,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newConv2);
    expect(response.body.ops[0].title).toBe('');
    expect(response.body.ops[0].description).toBe('');
    SchemaValidator.validate(listConv, response.body);
  });

  test(`should list ${type} my corezoid`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv3,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newConv3);
    SchemaValidator.validate(listConv, response.body);
  });

  test(`should list ${type} list_obj`, async () => {
    const responseConv = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        title: conv_title,
        status: 'active',
        obj_type: 0,
        create_mode: 'without_nodes',
        conv_type: type,
        project_short_name: short_name_project,
        stage_id,
      }),
    );
    expect(responseConv.status).toBe(200);
    newConv7 = responseConv.body.ops[0].obj_id;

    const responseLink = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv7,
        company_id,

        obj_to: 'user',
        obj_to_id: owner,
        privs: [{ type: 'create', list_obj: ['all'] }],
      }),
    );
    expect(responseLink.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv7,
        list_obj: 'all',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newConv7);
    expect(response.body.ops[0].title).toBe(conv_title);
    SchemaValidator.validate(listConv, response.body);

    const response2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv7,
        list_obj: 'group',
      }),
    );
    expect(response2.status).toBe(200);
    expect(response2.body.ops[0].obj_id).toBe(newConv7);
    expect(response2.body.ops[0].list[0].obj_id).toBe(owner);
    SchemaValidator.validate(listConvsGroupSchema, response2.body);
  });

  test(`should list ${type} in stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv4,
        project_id,
        stage_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newConv4);
    expect(response.body.ops[0].title).toBe('modify');
    expect(response.body.ops[0].description).toBe('modify');
    SchemaValidator.validate(listConv, response.body);

    const response2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv4,
        project_id,
        stage_short_name: 'develop',
      }),
    );
    expect(response2.status).toBe(200);
    expect(response2.body.ops[0].obj_id).toBe(newConv4);
    SchemaValidator.validate(listConv, response2.body);
  });

  test(`should show conv`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newConv);
    SchemaValidator.validate(showSchema, response.body);
  });

  test(`should show conv with company_id=null`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv3,
        company_id: null,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newConv3);
    SchemaValidator.validate(showSchema, response.body);
  });

  test(`should show ${type} in stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv4,
        project_id,
        stage_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newConv4);
    expect(response.body.ops[0].title).toBe('modify');
    SchemaValidator.validate(showSchema, response.body);

    const response2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv4,
        project_short_name: short_name_project,
        stage_short_name: 'develop',
      }),
    );
    expect(response2.status).toBe(200);
    expect(response2.body.ops[0].obj_id).toBe(newConv4);
    SchemaValidator.validate(showSchema, response2.body);
  });

  test(`should find link ${type} in list_folder`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]));
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newConv).is_owner).toEqual(true);
  });

  test(`should link ${type} back`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newConv,
        company_id,
        folder_id: 0,
        parent_id: newFolder,
        obj_type: 'conv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_type).toBe('conv');
    expect(response.body.ops[0].obj_id).toBe(newConv);
    expect(response.body.ops[0].obj_to_id).toBe(0);
    expect(response.body.ops[0].obj_to_title).toBe('Root folder');
    expect(response.body.ops[0].from_folder).toBe(newFolder);
    expect(response.body.ops[0].to_folder).toBe(0);
    expect(response.body.ops[0].to_folder_name).toBe('Root folder');
    SchemaValidator.validate(linkSchema, response.body);
  });

  test(`should find link ${type} in list_folder after back`, async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return response.status === 200 && response.body.ops[0].list.some((item: any) => item.obj_id === newConv);
    }
    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.FOLDER,
          obj_id: 0,
          company_id,
        }),
      );
    }, checkConditions);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test(`should link ${type} all privs`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv2,
        company_id,

        obj_to: 'user',
        obj_to_id: apiKey2,
        privs: [
          { type: 'create', list_obj: ['all'] },
          { type: 'modify', list_obj: ['all'] },
          { type: 'delete', list_obj: ['all'] },
          { type: 'view', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_to_id).toBe(apiKey2);
    expect(response.body.ops[0].obj_to_type).toBe('user');
    SchemaValidator.validate(linkObjSchema, response.body);
  });

  test(`should link ${type} view privs`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv3,
        company_id: null,
        obj_to: 'user',
        obj_to_id: apiKey2,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_to_id).toBe(apiKey2);
    expect(response.body.ops[0].obj_to_type).toBe('user');
    SchemaValidator.validate(linkObjSchema, response.body);
  });

  test(`should link ${type} in stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv4,
        company_id,

        project_id,
        stage_id,
        obj_to: 'user',
        obj_to_id: apiKey2,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newConv4);
    expect(response.body.ops[0].obj_to_id).toBe(apiKey2);
    SchemaValidator.validate(linkObjSchema, response.body);

    const response2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv4,
        company_id,

        project_short_name: short_name_project,
        stage_short_name: 'develop',
        obj_to: 'user',
        obj_to_id: apiKey2,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
        ],
      }),
    );
    expect(response2.status).toBe(200);
    expect(response2.body.ops[0].obj_id).toBe(newConv4);
    expect(response2.body.ops[0].obj_to_id).toBe(apiKey2);
    SchemaValidator.validate(linkObjSchema, response2.body);
  });

  test(`should link ${type} to group`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv2,
        company_id,

        obj_to: 'group',
        obj_to_id: group_id,
        is_need_to_notify: false,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_to_id).toBe(group_id);
    expect(response.body.ops[0].obj_to_type).toBe('group');
    SchemaValidator.validate(linkObjSchema, response.body);
  });

  test(`should unlink ${type}`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv3,
        company_id: null,
        obj_to: 'user',
        obj_to_id: apiKey2,
        privs: [],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_to_id).toBe(apiKey2);
    expect(response.body.ops[0].obj_to_type).toBe('user');
    expect(response.body.ops[0].action_type).toBe('unlink');
    SchemaValidator.validate(linkObjSchema, response.body);
  });

  test(`should create invite ${type}`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        company_id,
        login: '<EMAIL>',
        login_type: 'google',
        link_to_obj: 'conv',
        link_to_obj_id: newConv2,
        is_need_to_notify: true,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('invite');
  });

  test('should copy conv sync', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newConv,

        obj_type: 'conv',
        folder_id: 0,
        title: 'copyConvSync',
        ignore_errors: true,
        async: false,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].scheme[0].old_obj_id).toBe(newConv);
    expect(response.body.ops[0].scheme[0].obj_type).toBe('conv');
    expect(response.body.ops[0].scheme[0].title).toBe('copyConvSync');
    copyConvSync = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copySync, response.body);
  });

  test('should copy conv async', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newConv,

        obj_type: 'conv',
        folder_id: 0,
        title: 'copyConvSync',
        ignore_errors: true,
        async: true,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('obj_copy');
    SchemaValidator.validate(copyAsync, response.body);
  });

  test('should find copy conv in list_folder and after delete/destroy', async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return response.status === 200 && response.body.ops[0].list.some((item: any) => item.obj_id === copyConvSync);
    }
    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.FOLDER,
          obj_id: 0,
          company_id,
        }),
      );
    }, checkConditions);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');

    const deleteresponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: copyConvSync,
        company_id,
      }),
    );
    expect(deleteresponse.status).toBe(200);
    expect(deleteresponse.body.ops[0].obj_id).toBe(copyConvSync);
    const destroyresponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.CONV,
        obj_id: copyConvSync,
        company_id,
      }),
    );
    expect(destroyresponse.status).toBe(200);
    expect(destroyresponse.body.ops[0].obj_id).toBe(copyConvSync);
  });

  test('should copy conv sync in other folder in company', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newConv,

        obj_type: 'conv',
        folder_id: newFolder,
        title: 'copyConvSync',
        ignore_errors: true,
        async: false,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].scheme[0].old_obj_id).toBe(newConv);
    expect(response.body.ops[0].scheme[0].obj_type).toBe('conv');
    expect(response.body.ops[0].scheme[0].title).toBe('copyConvSync');
    copyConvSync = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copySync, response.body);
  });

  test('should find copy conv sync in other folder in company', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: copyConvSync })]),
    );
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === copyConvSync).is_owner).toEqual(true);
  });

  test('should copy conv sync with company in my corezoid', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newConv,

        obj_type: 'conv',
        folder_id: 0,
        title: 'copyConvSync',
        ignore_errors: true,
        async: false,
        from_company_id: company_id,
        to_company_id: null,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].scheme[0].old_obj_id).toBe(newConv);
    expect(response.body.ops[0].scheme[0].obj_type).toBe('conv');
    expect(response.body.ops[0].scheme[0].title).toBe('copyConvSync');
    copyConvSync = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copySync, response.body);
  });

  test('should copy conv sync in my corezoid', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: copyConvSync,

        obj_type: 'conv',
        folder_id: 0,
        title: 'copyConvSyncMyCorezoid',
        ignore_errors: true,
        async: false,
        from_company_id: null,
        to_company_id: null,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].scheme[0].old_obj_id).toBe(copyConvSync);
    expect(response.body.ops[0].scheme[0].obj_type).toBe('conv');
    expect(response.body.ops[0].scheme[0].title).toBe('copyConvSyncMyCorezoid');
    copyConvSyncMyCorezoid = response.body.ops[0].scheme[0].obj_id;
  });

  test('should copy conv sync with my corezoid in company', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: copyConvSync,

        obj_type: 'conv',
        folder_id: newFolder,
        title: 'newcopyConvSync',
        ignore_errors: true,
        async: false,
        from_company_id: null,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].scheme[0].old_obj_id).toBe(copyConvSync);
    expect(response.body.ops[0].scheme[0].obj_type).toBe('conv');
    expect(response.body.ops[0].scheme[0].title).toBe('newcopyConvSync');
    SchemaValidator.validate(copySync, response.body);
  });

  test('should find copy conv in my corezoid and after delete/destroy', async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === copyConvSync, copyConvSyncMyCorezoid)
          ?.is_owner === true &&
        response.body.ops[0].list.some((item: any) => item.obj_id === copyConvSync) &&
        response.body.ops[0].list.some((item: any) => item.obj_id === copyConvSyncMyCorezoid)
      );
    }
    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.FOLDER,
          obj_id: 0,
          company_id: null,
        }),
      );
    }, checkConditions);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');

    const deleteresponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: copyConvSync,
        company_id: null,
      }),
    );
    expect(deleteresponse.status).toBe(200);
    SchemaValidator.validate(deleteSchema, deleteresponse.body);
    expect(deleteresponse.body.ops[0].obj_id).toBe(copyConvSync);
    const deleteresponse2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: copyConvSyncMyCorezoid,
      }),
    );
    expect(deleteresponse2.status).toBe(200);
    expect(deleteresponse2.body.ops[0].obj_id).toBe(copyConvSyncMyCorezoid);
    const destroyresponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.CONV,
        obj_id: copyConvSync,
        company_id: null,
      }),
    );
    expect(destroyresponse.status).toBe(200);
    expect(destroyresponse.body.ops[0].obj_id).toBe(copyConvSync);
    const destroyresponse2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.CONV,
        obj_id: copyConvSyncMyCorezoid,
        company_id: null,
      }),
    );
    expect(destroyresponse2.status).toBe(200);
    expect(destroyresponse2.body.ops[0].obj_id).toBe(copyConvSyncMyCorezoid);
    const deleteresponse3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(deleteresponse3.status).toBe(200);
    expect(deleteresponse3.body.ops[0].obj_id).toBe(newFolder);
  });

  test(`should add ${type} to favorite`, async () => {
    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
        favorite: true,
      }),
    );
    expect(responseConv.status).toBe(200);
    SchemaValidator.validate(favoriteSchema, responseConv.body);

    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === newConv)?.favorite === true
      );
    }

    const response = await makeRequestWithRetries(
      async () => {
        return await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.FOLDER,
            obj_id: 0,
            company_id,
          }),
        );
      },
      checkConditions,
      10,
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test(`should remove ${type} from favorite`, async () => {
    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        favorite: false,
      }),
    );
    expect(responseConv.status).toBe(200);
    SchemaValidator.validate(favoriteSchema, responseConv.body);
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === newConv)?.favorite === false
      );
    }
    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.FOLDER,
          obj_id: 0,
          company_id,
        }),
      );
    }, checkConditions);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test(`should favorite ${type} with company_id=null`, async () => {
    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv3,
        company_id: null,
        favorite: true,
      }),
    );
    expect(responseConv.status).toBe(200);
    SchemaValidator.validate(favoriteSchema, responseConv.body);
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === newConv3)?.favorite === true
      );
    }
    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.FOLDER,
          obj_id: 0,
          company_id: null,
        }),
      );
    }, checkConditions);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test(`should favorite ${type} in stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv4,
        company_id,
        favorite: true,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(favoriteSchema, response.body);
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: stage_id,
        company_id,
      }),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newConv4)?.favorite).toEqual(
      true,
    );
  });

  test(`should modify ${type}`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
        description: 'new description',
        title: `Process_${Date.now()}`,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(modifySchema, response.body);
  });

  test(`should find ${type} in list_folder`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_type: 'conv' })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]));
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newConv)?.is_owner).toEqual(true);
  });

  test(`should list convs`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        obj_type: 'process',
        pattern: conv_title,
        owner_id: owner,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('convs');
    SchemaValidator.validate(listConvsSchema, response.body);
  });

  test(`should delete ${type}`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(deleteSchema, response.body);
    expect(response.body.ops[0].obj_id).toBe(newConv);
  });

  test(`should find ${type} in list_folder filter_deleted`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
        filter: 'deleted',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_type: 'conv' })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]));
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newConv)?.is_owner).toEqual(true);
  });

  test(`should not find ${type} in list_folder`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newConv)).toBeEmpty();
  });

  test(`should restore ${type}`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(restoreSchema, response.body);
    expect(response.body.ops[0].obj_id).toBe(newConv);
  });

  test(`should find ${type} in list_folder`, async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        response.body.ops[0].list.some((item: any) => item.obj_id === newConv) &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === newConv)?.is_owner === true
      );
    }
    const response = await makeRequestWithRetries(
      async () => {
        return await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.FOLDER,
            obj_id: 0,
            company_id,
          }),
        );
      },
      checkConditions,
      {
        maxRetries: 10,
      },
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test(`should not find ${type} in list_folder filter_deleted`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
        filter: 'deleted',
      }),
    );
    expect(response.status).toBe(200);
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newConv)).toBeEmpty();
  });

  test(`should destroy ${type}`, async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newConv);
    SchemaValidator.validate(destroySchema, response.body);
  });

  test(`should not find ${type} in list_folder`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newConv)).toBeEmpty();
  });

  test(`should not find ${type} in list_folder filter_deleted`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
        filter: 'deleted',
      }),
    );
    expect(response.status).toBe(200);
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newConv)).toBeEmpty();
  });

  test(`should delete ${type} for restore tests`, async () => {
    const ResponseDeleteConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv4,
        company_id,
      }),
    );
    expect(ResponseDeleteConv.status).toBe(200);

    const ResponseDeleteConv2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv3,
        company_id: null,
      }),
    );
    expect(ResponseDeleteConv2.status).toBe(200);
  });

  test(`should restore ${type} in stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv4,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].parent_folder_id).toBe(stage_id);
    SchemaValidator.validate(restoreSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: stage_id,
        company_id,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv4 })]),
    );
  });

  test(`should restore ${type} in my corezoid`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv3,
        company_id: null,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].parent_folder_id).toBe(0);
    SchemaValidator.validate(restoreSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id: null,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv3 })]),
    );
  });

  test(`should delete ${type} for destroy tests`, async () => {
    const ResponseDeleteConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv4,
        company_id,
      }),
    );
    expect(ResponseDeleteConv.status).toBe(200);
  });

  test(`should destroy ${type} in stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv4,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(destroySchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
        filter: 'deleted',
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv4 })]),
    );
  });

  afterAll(async () => {
    const ResponseDeleteConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv2,
        company_id,
      }),
    );
    expect(ResponseDeleteConv.status).toBe(200);

    const ResponseDeleteConv2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv3,
        company_id: null,
      }),
    );
    expect(ResponseDeleteConv2.status).toBe(200);

    const ResponseDeleteProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: project_id,
        company_id,
      }),
    );
    expect(ResponseDeleteProject.status).toBe(200);

    const ResponseDeleteApiKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: apiKey2,
        company_id,
      }),
    );
    expect(ResponseDeleteApiKey.status).toBe(200);

    const ResponseDeleteGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        obj_id: group_id,
        company_id,
      }),
    );
    expect(ResponseDeleteGroup.status).toBe(200);
  });
});
