import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObjNew, requestRestoreObj } from '../../../../../../../application/api/ApiObj';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import listSchema from '../../../../schemas/v2/DB-call/listInstances.schema.json';

describe('Instances (positive)', () => {
  let api: ApiKeyClient;
  let newApi: ApiKeyClient;
  let apikey: ApiKey;
  let key_obj_id: string | number;
  let newFolder: string | number;
  let newProject: string | number;
  let newStage: string | number;
  let newProjectKey: string | number;
  let newStageKey: string | number;
  let project_short_name: string;
  let project_short_nameKey: string;
  let stage_short_name: string;
  let newInstanceFolders: string | number;
  let newInstanceFolders2: string | number;
  let newInstanceStage: string | number;
  let newInstanceStage2: string | number;
  let newInstanceMyC: string | number;
  let newInstanceKey: string | number;
  let newInstanceKey2: string | number;
  let company_id: any;
  let db_host: string;
  let db_nameP: string;
  let db_userP: string;
  let db_passP: string;
  let db_nameMyS: string;
  let db_userMyS: string;
  let db_passMyS: string;
  let db_nameMsS: string;
  let db_userMsS: string;
  let db_passMsS: string;
  let postgres_instance_name_mc: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key_obj_id = apikey.id;
    project_short_name = `project1-${Date.now()}`;
    project_short_nameKey = `projectkey-${Date.now()}`;
    stage_short_name = `stage1-${Date.now()}`;
    db_host = '**********';
    db_nameP = 'test';
    db_userP = 'test_user';
    db_passP = '3sha1cheeH4uoziej1u';
    db_nameMyS = 'COR6783';
    db_userMyS = 'root';
    db_passMyS = 'she2kojiJo';
    db_nameMsS = 'SUPPORT1230';
    db_userMsS = 'sa';
    db_passMsS = 'wah3Jushai';
    postgres_instance_name_mc = `Postgres_instance_mc${Date.now()}`;

    const CreateKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(200);
    const user_api = CreateKeyResponse.body;
    const newApiKey = {
      key: `${user_api.ops[0].users[0].logins[0].obj_id}`,
      secret: user_api.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user_api.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newApi = application.getApiKeyClient(newApiKey);

    const responseCreateDB = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        instance_type: `db_call`,
        folder_id: 0,
        title: `Postgres_instance_folder_key${Date.now()}`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_userP,
          password: db_passP,
          ssl: false,
          database: db_nameP,
          timeoutMs: 30000,
        },
      }),
    );
    expect(responseCreateDB.status).toBe(200);
    expect(responseCreateDB.body.ops[0].obj).toBe('instance');
    newInstanceKey = responseCreateDB.body.ops[0].obj_id;

    const responseLink = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstanceKey,
        company_id,

        obj_to: 'user',
        obj_to_id: key_obj_id,
        is_need_to_notify: false,
        privs: [
          { type: 'create', list_obj: ['all'] },
          { type: 'modify', list_obj: ['all'] },
          { type: 'delete', list_obj: ['all'] },
          { type: 'view', list_obj: ['all'] },
        ],
      }),
    );
    expect(responseLink.status).toBe(200);

    const responseProjectKey = await requestCreateObjNew(
      newApi,
      OBJ_TYPE.PROJECT,
      company_id,
      `Project1_${Date.now()}`,
      {
        short_name: project_short_nameKey,
        description: 'test',
        stages: [],
      },
    );
    newProjectKey = responseProjectKey.body.ops[0].obj_id;

    const responseStageKey = await requestCreateObjNew(newApi, OBJ_TYPE.STAGE, company_id, `Stage1_${Date.now()}`, {
      short_name: stage_short_name,
      description: 'test',
      project_id: newProjectKey,
    });
    newStageKey = responseStageKey.body.ops[0].obj_id;

    const responseCreateDB2 = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        instance_type: `db_call`,
        folder_id: newStageKey,
        stage_id: newStageKey,
        project_id: newProjectKey,
        title: `Postgres_instance_folder_key${Date.now()}`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_userP,
          password: db_passP,
          ssl: false,
          database: db_nameP,
          timeoutMs: 30000,
        },
      }),
    );
    expect(responseCreateDB2.status).toBe(200);
    expect(responseCreateDB2.body.ops[0].obj).toBe('instance');
    newInstanceKey2 = responseCreateDB2.body.ops[0].obj_id;

    const responseLinkProject = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProjectKey,
        company_id,

        obj_to: 'user',
        obj_to_id: key_obj_id,
        is_need_to_notify: false,
        privs: [
          { type: 'create', list_obj: ['all'] },
          { type: 'modify', list_obj: ['all'] },
          { type: 'delete', list_obj: ['all'] },
          { type: 'view', list_obj: ['all'] },
        ],
      }),
    );
    expect(responseLinkProject.status).toBe(200);

    const responseFolder = await requestCreateObjNew(api, OBJ_TYPE.FOLDER, company_id, `Folder_${Date.now()}`, {
      folder_id: 0,
    });
    newFolder = responseFolder.body.ops[0].obj_id;

    const responseProject = await requestCreateObjNew(api, OBJ_TYPE.PROJECT, company_id, `Project1_${Date.now()}`, {
      short_name: project_short_name,
      description: 'test',
      stages: [],
    });
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await requestCreateObjNew(api, OBJ_TYPE.STAGE, company_id, `Stage1_${Date.now()}`, {
      short_name: stage_short_name,
      description: 'test',
      project_id: newProject,
    });
    newStage = responseStage.body.ops[0].obj_id;

    const responseFoldersDB = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        instance_type: `db_call`,
        folder_id: newFolder,
        title: `Postgres_instance_folder${Date.now()}`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_userP,
          password: db_passP,
          ssl: false,
          database: db_nameP,
          timeoutMs: 30000,
        },
      }),
    );
    expect(responseFoldersDB.status).toBe(200);
    expect(responseFoldersDB.body.ops[0].obj).toBe('instance');
    newInstanceFolders = responseFoldersDB.body.ops[0].obj_id;

    const responseFoldersDB2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        instance_type: `db_call`,
        folder_id: newFolder,
        title: `Instance_mysql_folder${Date.now()}`,
        description: `test`,
        data: {
          driver: `mysql`,
          host: db_host,
          port: `3306`,
          username: db_userMyS,
          password: db_passMyS,
          ssl: false,
          database: db_nameMyS,
          timeoutMs: 30000,
        },
      }),
    );
    expect(responseFoldersDB2.status).toBe(200);
    expect(responseFoldersDB2.body.ops[0].obj).toBe('instance');
    newInstanceFolders2 = responseFoldersDB2.body.ops[0].obj_id;

    const responseStageDB = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        instance_type: `db_call`,
        stage_id: newStage,
        project_id: newProject,
        title: `Instance_mssql_project${Date.now()}`,
        description: `test`,
        data: {
          driver: `mssql`,
          host: db_host,
          port: `1433`,
          username: db_userMsS,
          password: db_passMsS,
          ssl: false,
          database: db_nameMsS,
          timeoutMs: 30000,
        },
      }),
    );
    expect(responseStageDB.status).toBe(200);
    expect(responseStageDB.body.ops[0].obj).toBe('instance');
    newInstanceStage = responseStageDB.body.ops[0].obj_id;

    const responseStageDB2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        instance_type: `db_call`,
        stage_id: newStage,
        project_id: newProject,
        title: `Postgres_instance_project${Date.now()}`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_userP,
          password: db_passP,
          ssl: false,
          database: db_nameP,
          timeoutMs: 30000,
        },
      }),
    );
    expect(responseStageDB2.status).toBe(200);
    expect(responseStageDB2.body.ops[0].obj).toBe('instance');
    newInstanceStage2 = responseStageDB2.body.ops[0].obj_id;

    const responseMyCorezoidDB = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id: null,

        instance_type: `db_call`,
        title: postgres_instance_name_mc,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_userP,
          password: db_passP,
          ssl: false,
          database: db_nameP,
          timeoutMs: 30000,
        },
      }),
    );
    expect(responseMyCorezoidDB.status).toBe(200);
    expect(responseMyCorezoidDB.body.ops[0].obj).toBe('instance');
    newInstanceMyC = responseMyCorezoidDB.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));
  });

  test(`should list instances Folders by name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,
        instance_type: 'db_call',
        pattern: 'postgres_instance',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceFolders })]),
    );
    expect(response.body.ops[0].instances).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceStage2 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should list instances Folders by id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,
        instance_type: 'db_call',
        pattern: `${newInstanceFolders2}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceFolders2 })]),
    );
    expect(response.body.ops[0].instances).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceStage })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should list instances Folders by id after delete`, async () => {
    const responseDelInstance = await requestDeleteObj(api, OBJ_TYPE.INSTANCE, newInstanceFolders2, null);
    expect(responseDelInstance.status).toBe(200);
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,
        instance_type: 'db_call',
        pattern: `${newInstanceFolders2}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceFolders2 })]),
    );
  });

  test(`should list instances Folders by id after restore`, async () => {
    const responseRestoreInstance = await requestRestoreObj(api, OBJ_TYPE.INSTANCE, newInstanceFolders2, null);
    expect(responseRestoreInstance.status).toBe(200);
    await new Promise(r => setTimeout(r, 1000));

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,
        instance_type: 'db_call',
        pattern: `${newInstanceFolders2}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceFolders2 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should list instances stage by name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,
        instance_type: 'db_call',
        pattern: 'Postgres_instance',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceStage2 })]),
    );
    expect(response.body.ops[0].instances).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceFolders })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should list instances stage by id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,
        instance_type: 'db_call',
        pattern: `${newInstanceStage}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceStage })]),
    );
    expect(response.body.ops[0].instances).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceFolders2 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should list instances stage by short_name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,

        instance_type: 'db_call',
        pattern: 'Postgres_instance',
        project_short_name,
        stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceStage2 })]),
    );
    expect(response.body.ops[0].instances).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceFolders })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should list instances stage without pattern`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,
        instance_type: 'db_call',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceStage })]),
    );
    expect(response.body.ops[0].instances).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceStage2 })]),
    );
    expect(response.body.ops[0].instances).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceFolders2 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should list instances all in Folders with limit without pattern`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,
        instance_type: 'db_call',
        limit: 500,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceFolders2 })]),
    );
    expect(response.body.ops[0].instances).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceFolders })]),
    );
    expect(response.body.ops[0].instances).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceStage })]),
    );
    expect(response.body.ops[0].instances).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceStage2 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should list instances stage by id after delete`, async () => {
    const responseDeleteInstance = await requestDeleteObj(api, OBJ_TYPE.INSTANCE, newInstanceStage, null);
    expect(responseDeleteInstance.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,
        instance_type: 'db_call',
        pattern: `${newInstanceStage}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceStage })]),
    );
  });

  test(`should list instances stage by id after restore`, async () => {
    const responseRestoreInstance = await requestRestoreObj(api, OBJ_TYPE.INSTANCE, newInstanceStage, null);
    expect(responseRestoreInstance.status).toBe(200);
    await new Promise(r => setTimeout(r, 1000));

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,
        instance_type: 'db_call',
        pattern: `${newInstanceStage}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceStage })]),
    );
  });

  test(`should list instances all my corezoid with pattern title`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        pattern: postgres_instance_name_mc,
        instance_type: 'db_call',
        limit: 20,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceMyC })]),
    );
    expect(response.body.ops[0].instances).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceFolders })]),
    );
  });

  test(`should list instances all my corezoid with pattern id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        pattern: `${newInstanceMyC}`,
        instance_type: 'db_call',
        limit: 20,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceMyC })]),
    );
    expect(response.body.ops[0].instances).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceFolders })]),
    );
  });

  test(`should list instances all my corezoid without pattern`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        instance_type: 'db_call',
        limit: 20,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceMyC })]),
    );
    expect(response.body.ops[0].instances).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceFolders })]),
    );
  });

  test(`shouldn't list instances all my corezoid with pattern id after delete`, async () => {
    const responseDelInstance = await requestDeleteObj(api, OBJ_TYPE.INSTANCE, newInstanceMyC, null);
    expect(responseDelInstance.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        pattern: `${newInstanceMyC}`,
        instance_type: 'db_call',
        limit: 20,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceMyC })]),
    );
  });

  test(`should list instances all my corezoid with pattern id after restore`, async () => {
    const responseRestoreInstance = await requestRestoreObj(api, OBJ_TYPE.INSTANCE, newInstanceMyC, null);
    expect(responseRestoreInstance.status).toBe(200);
    await new Promise(r => setTimeout(r, 1000));

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        pattern: `${newInstanceMyC}`,
        instance_type: 'db_call',
        limit: 20,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceMyC })]),
    );
  });

  test(`should list shared instances in Folders by id `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,
        instance_type: 'db_call',
        pattern: `${newInstanceKey}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceKey })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`shouldn't list unshared instances in Folders by id `, async () => {
    const responseLink = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstanceKey,
        company_id,

        obj_to: 'user',
        obj_to_id: key_obj_id,
        is_need_to_notify: false,
        privs: [],
      }),
    );
    expect(responseLink.status).toBe(200);
    await new Promise(r => setTimeout(r, 2000));

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,
        instance_type: 'db_call',
        pattern: `${newInstanceKey}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceKey })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should list instances in shared Project by id `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,

        instance_type: 'db_call',
        pattern: `${newInstanceKey2}`,
        project_id: newProjectKey,
        stage_id: newStageKey,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceKey2 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`shouldn't list instances in unshared Project by id `, async () => {
    const responseLinkProject = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProjectKey,
        company_id,

        obj_to: 'user',
        obj_to_id: key_obj_id,
        is_need_to_notify: false,
        privs: [],
      }),
    );
    expect(responseLinkProject.status).toBe(200);
    await new Promise(r => setTimeout(r, 2000));

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,

        instance_type: 'db_call',
        pattern: `${newInstanceKey2}`,
        project_id: newProjectKey,
        stage_id: newStageKey,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].instances).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstanceKey2 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  afterAll(async () => {
    const responseDelFolder = await requestDeleteObj(api, OBJ_TYPE.FOLDER, newFolder, company_id);
    expect(responseDelFolder.status).toBe(200);

    const responseDelProject = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(responseDelProject.status).toBe(200);

    const responseDelInstance = await requestDeleteObj(api, OBJ_TYPE.INSTANCE, newInstanceMyC, null);
    expect(responseDelInstance.status).toBe(200);

    const responseDelInstanceKey = await requestDeleteObj(newApi, OBJ_TYPE.INSTANCE, newInstanceKey, null);
    expect(responseDelInstanceKey.status).toBe(200);

    const responseDelProjectKey = await requestDeleteObj(newApi, OBJ_TYPE.PROJECT, newProjectKey, null);
    expect(responseDelProjectKey.status).toBe(200);
  });
});
