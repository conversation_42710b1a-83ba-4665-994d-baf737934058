import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { requestDeleteObj } from '../../../../../../../application/api/ApiObj';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import {
  integerTestCases,
  stringTestCases,
  stringNotValidTestCases,
  undefinedTestCase,
  companyTestCases,
  securityTestCases,
  maxLength,
} from '../../../../../negativeCases';

describe('Instances (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newInstance: string | number;
  let newStage: string | number;
  let newProject: string | number;
  let company_id: any;
  let valuesToSkip: any;
  let db_host: string;
  let db_name: string;
  let db_user: string;
  let db_pass: string;
  let short_nameP: string;
  let short_nameS: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    db_host = '**********';
    db_name = 'test';
    db_user = 'test_user';
    db_pass = '3sha1cheeH4uoziej1u';
    (short_nameP = `projectname-${Date.now()}`), (short_nameS = `stagename-${Date.now()}`);

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: `Project_${Date.now()}`,
        short_name: short_nameP,
      }),
    );
    expect(responseProject.status).toBe(200);
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        title: `Stage_${Date.now()}`,
        short_name: short_nameS,
        project_id: newProject,
      }),
    );
    expect(responseStage.status).toBe(200);
    newStage = responseStage.body.ops[0].obj_id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        project_id: newProject,
        stage_id: newStage,
        instance_type: `db_call`,
        folder_id: 0,
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    newInstance = response.body.ops[0].obj_id;
  });

  test.each(
    [
      ...stringTestCases,
      ...undefinedTestCase,
      ...stringNotValidTestCases,
      ...securityTestCases,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't list instances with invalid instance_type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,
        instance_type: input,
        pattern: `${newInstance}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = ['test'];
  test.each(
    [...companyTestCases, ...stringNotValidTestCases, ...securityTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't list instances with invalid company_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id: input,
        instance_type: 'db_call',
        pattern: `${newInstance}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = ['test'];
  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...maxLength]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't list instances with invalid pattern '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,
        instance_type: 'db_call',
        pattern: input,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0];
  test.each(
    [...integerTestCases, ...securityTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't list instances with invalid project_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,
        instance_type: 'db_call',
        pattern: 'test',
        project_id: input,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0, -1];
  test.each(
    [...integerTestCases, ...securityTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't list instances with invalid stage_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,
        instance_type: 'db_call',
        pattern: 'test',
        project_id: newProject,
        stage_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0];
  test.each(
    [...integerTestCases, ...securityTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't list instances with invalid limit '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,
        instance_type: 'db_call',
        pattern: 'test',
        project_id: newProject,
        stage_id: newStage,
        limit: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [''];
  test.each(
    [...stringNotValidTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't list instances with invalid stage_short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,
        instance_type: 'db_call',
        pattern: 'test',
        project_short_name: short_nameP,
        stage_short_name: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [''];
  test.each(
    [...stringNotValidTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't list instances with invalid project_short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCES,
        company_id,
        instance_type: 'db_call',
        pattern: 'test',
        project_short_name: input,
        stage_short_name: short_nameS,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  afterAll(async () => {
    const responseDelInstance = await requestDeleteObj(api, OBJ_TYPE.INSTANCE, newInstance, company_id);
    expect(responseDelInstance.status).toBe(200);
  });
});
