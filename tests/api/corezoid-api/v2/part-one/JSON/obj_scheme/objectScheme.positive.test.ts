import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import getDashboardSchema from '../../../../schemas/v2/actions-objects/getDashboardScheme.schema.json';
import getFolderSchema from '../../../../schemas/v2/actions-objects/getFolderScheme.schema.json';
import getConvSchema from '../../../../schemas/v2/actions-objects/getConvScheme.schema.json';
import checkSchema from '../../../../schemas/v2/actions-objects/checkObjScheme.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import {
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
} from '../../../../../../../application/api/ApiObj';

describe('Get object scheme in project (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newFolder: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let newConv: number;
  let newSD: number;
  let newDashboard: number;
  let newProject: number;
  let newStage: number;
  let short_name: string;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    const title = `Folder_${Date.now()}`;
    short_name = `project${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: short_name,
        short_name,
        description: 'test',
        stages: ['develop'],
        status: 'active',
      }),
    );
    expect(responseProject.status).toBe(200);
    newProject = responseProject.body.ops[0].obj_id;
    newStage = responseProject.body.ops[0].stages[0];

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: newStage,
        description: 'test',
        title,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    newFolder = response.body.ops[0].obj_id;

    const response2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: newFolder,
        description: 'test',
        title,
      }),
    );
    expect(response2.status).toBe(200);

    const response3 = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Delay`);
    newConv = response3.body.ops[0].obj_id;

    const responseList = await requestListConv(api, newConv, company_id);
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateQueue = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,

        conv_id: newConv,
        title: 'process',
        obj_type: 0,
        logics: [],
        semaphors: [{ type: 'time', value: 30, dimension: 'sec', to_node_id: final_node_ID }],
        version: 22,
      }),
    );
    expect(responseCreateQueue.status).toBe(200);

    const responseCommit = await requestConfirm(api, newConv, company_id);
    expect(responseCommit.status).toBe(200);

    const responseSD = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        folder_id: newFolder,
        title: `SD_${Date.now()}`,
        status: 'active',
        obj_type: 0,
        description: 'test',
        create_mode: 'without_nodes',
        conv_type: 'state',
      }),
    );
    expect(responseSD.status).toBe(200);
    newSD = responseSD.body.ops[0].obj_id;

    const response4 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: newFolder,
        obj_type: 0,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    expect(response4.status).toBe(200);
    newDashboard = response4.body.ops[0].obj_id;
  });

  test('should get folder scheme', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newFolder,
        obj_type: 'folder',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].scheme).toBeArray();
    expect(response.body.ops[0].scheme[1].obj_type).toEqual(0);
    SchemaValidator.validate(getFolderSchema, response.body);
  });

  test('should get conv scheme when uncommitted version', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newSD,
        obj_type: 'conv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(`The process with ID ${newSD} has an uncommitted version`);
  });

  test('should get dashboard scheme', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newDashboard,
        obj_type: 'dashboard',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].scheme).toBeArray();
    expect(response.body.ops[0].scheme[0].obj_type).toEqual(2);
    SchemaValidator.validate(getDashboardSchema, response.body);
  });

  test('should get conv scheme', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newConv,
        obj_type: 'conv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].scheme).toBeArray();
    expect(response.body.ops[0].scheme[0].obj_type).toEqual(1);
    SchemaValidator.validate(getConvSchema, response.body);
  });

  test('should check folder scheme', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newFolder,
        obj_type: 'folder',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].obj).toEqual('obj_scheme');
    SchemaValidator.validate(checkSchema, response.body);
  });

  test('should check conv scheme when uncommitted version', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newSD,
        obj_type: 'conv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(`The process with ID ${newSD} has an uncommitted version`);
  });

  test('should check dashboard scheme', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newDashboard,
        obj_type: 'dashboard',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].obj).toEqual('obj_scheme');
    SchemaValidator.validate(checkSchema, response.body);
  });

  test('should check conv scheme', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newConv,
        obj_type: 'conv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].obj).toEqual('obj_scheme');
    SchemaValidator.validate(checkSchema, response.body);
  });

  afterAll(async () => {
    const responseFolder = await requestDeleteObj(api, OBJ_TYPE.FOLDER, newFolder, company_id);
    expect(responseFolder.status).toBe(200);

    const responseProject = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(responseProject.status).toBe(200);
  });
});
