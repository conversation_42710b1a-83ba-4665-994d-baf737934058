import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj } from '../../../../../../../application/api/ApiObj';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  securityTestCases,
  stringNotValidTestCases,
} from '../../../../../negativeCases';

describe('Get object scheme (negative)', () => {
  let api: ApiKeyClient;
  let apikey: Api<PERSON>ey;
  let newFolder: number;
  let company_id: any;
  let valuesToSkip: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    const title = `Folder_${Date.now()}`;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: 0,
        title,
      }),
    );
    expect(response.status).toBe(200);
    newFolder = response.body.ops[0].obj_id;
  });

  valuesToSkip = [0];

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't get obj_scheme with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: input,
        company_id,
        obj_type: 'folder',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [
      ...stringTestCases,
      ...securityTestCases,
      ...stringNotValidTestCases,
      ...undefinedTestCase,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't get obj_scheme with invalid obj_type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newFolder,
        company_id,
        obj_type: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0];

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't check obj_scheme with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: input,
        company_id,
        obj_type: 'folder',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [
      ...stringTestCases,
      ...securityTestCases,
      ...stringNotValidTestCases,
      ...undefinedTestCase,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't check obj_scheme with invalid obj_type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newFolder,
        company_id,
        obj_type: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  afterAll(async () => {
    const responseFolder = await requestDeleteObj(api, OBJ_TYPE.FOLDER, newFolder, company_id);
    expect(responseFolder.status).toBe(200);
  });
});
