import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  companyTestCases,
  arrayTestCases,
  securityTestCases,
  boolTestCases,
  maxLength,
  minLength,
  stringNotValidTestCases,
} from '../../../../../negativeCases';

describe('Project (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let company_id: any;
  let newKeyLoginId: string | number;
  let project_short_name: string;
  let valuesToSkip: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = `project-${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: `stage-${Date.now()}`,
        description: 'test',
        project_id: newProject,
      }),
    );

    const responseKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,

        title: `key_${Date.now()}`,
        logins: [{ type: 'api' }],
      }),
    );
    newKeyLoginId = responseKey.body.ops[0].users[0].obj_id;
  });

  valuesToSkip = [];

  test.each(
    [...companyTestCases, ...securityTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create project with invalid company_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id: input,

        title: `Project_${Date.now()}`,
        short_name: `project${project_short_name}`,
        description: 'test',
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...maxLength, ...minLength].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create project with invalid title '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: input,
        short_name: project_short_name,
        description: 'test',
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [null];

  test.each(
    [...stringTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create project with invalid description '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: input,
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't create project with invalid description maxLength`,
    async (input, errors) => {
      void errors;
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.PROJECT,
          company_id,

          title: `Project_${Date.now()}`,
          short_name: project_short_name,
          description: input,
          status: 'active',
        }),
      );
      expect(response.status).toBe(200);

      expect(response.body.ops[0].description).toBeString();
    },
  );

  test.each(
    [
      ...stringTestCases,
      ...undefinedTestCase,
      ...maxLength,
      ...minLength,
      ...securityTestCases,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create project with invalid short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: 'title',
        short_name: input,
        description: 'test',
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...boolTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't create project with invalid update_not_uniq_short_name '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.PROJECT,
          company_id,

          title: 'title',
          short_name: `project-${Date.now()}`,
          description: 'test',
          status: 'active',
          update_not_uniq_short_name: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...arrayTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't create project with invalid stages '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.PROJECT,
          company_id,

          title: 'title',
          short_name: `project-${Date.now()}`,
          description: 'test',
          status: 'active',
          stages: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't create project with invalid stages title '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.PROJECT,
          company_id,

          title: 'title',
          short_name: `project-${Date.now()}`,
          description: 'test',
          status: 'active',
          stages: [{ title: input }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...boolTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't create project with invalid stages immutable '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.PROJECT,
          company_id,

          title: 'title',
          short_name: `project-${Date.now()}`,
          description: 'test',
          status: 'active',
          stages: [{ title: 'prod', immutable: input }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify project with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.PROJECT,
          obj_id: newProject,
          company_id: input,
          title: `ProjectModify`,
          short_name: `project-modify`,
          description: 'testModify',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify project with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.PROJECT,
          obj_id: input,
          company_id,
          title: `ProjectModify`,
          short_name: `project-modify`,
          description: 'testModify',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...maxLength, ...minLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify project with invalid title '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.PROJECT,
          obj_id: newProject,
          company_id,
          title: input,
          short_name: `project-modify`,
          description: 'testModify',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [null];

  test.each(
    [...stringTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify project with invalid description '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        title: 'newProjectTitle',
        short_name: `project-modify`,
        description: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify project with invalid description maxLength`,
    async (input, errors) => {
      void errors;
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.PROJECT,
          obj_id: newProject,
          company_id,
          title: 'newProjectTitle',
          short_name: `project-modify`,
          description: input,
        }),
      );
      expect(response.status).toBe(200);

      expect(response.body.ops[0].description).toBeString();
    },
  );

  test.each(
    [...stringTestCases, ...maxLength, ...minLength, ...securityTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify project with invalid short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        title: 'newProjectTitle',
        short_name: input,
        description: 'test',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't list projects with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.PROJECT,
          obj_id: newProject,
          company_id: input,
          sort: 'title',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...securityTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't list projects with invalid sort '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        sort: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't list projects with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.PROJECT,
          obj_id: input,
          company_id,
          sort: 'title',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...securityTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't list projects with invalid order '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        order: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't list projects with invalid obj_short_name '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.PROJECT,
          company_id,
          obj_short_name: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...securityTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't list projects with invalid list_obj '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        list_obj: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't show project with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.PROJECT,
          company_id: input,
          obj_short_name: 'project-modify',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't show project with invalid obj_short_name '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.PROJECT,
          company_id,
          obj_short_name: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't show project with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.PROJECT,
          obj_id: input,
          company_id,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0];

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't get_object_project with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        company_id,
        obj_id: input,
        obj_type: 'project',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't get_object_project with invalid obj_type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        company_id,
        obj_id: newProject,
        obj_type: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't list list_path_to_folder_project with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.PATH_TO_FOLDER,
          obj_id: newProject,
          company_id: input,
          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't list list_path_to_folder_project with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.PATH_TO_FOLDER,
          obj_id: input,
          company_id,
          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't list list_path_to_folder_project with invalid project_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PATH_TO_FOLDER,
        obj_id: newProject,
        company_id,
        project_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't link project with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.PROJECT,
          obj_id: newProject,
          company_id: input,
          obj_to: 'user',
          obj_to_id: newKeyLoginId,
          privs: [{ type: 'view', list_obj: ['all'] }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't link projects with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.PROJECT,
          obj_id: input,
          company_id,
          obj_to: 'user',
          obj_to_id: newKeyLoginId,
          privs: [{ type: 'view', list_obj: ['all'] }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [
      ...stringNotValidTestCases,
      ...undefinedTestCase,
      ...stringTestCases,
      ...securityTestCases,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't link projects with invalid obj_to '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        obj_to: input,
        obj_to_id: newKeyLoginId,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't link projects with invalid obj_short_name '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.PROJECT,
          company_id,

          obj_to: 'user',
          obj_short_name: input,
          obj_to_id: newKeyLoginId,
          privs: [{ type: 'view', list_obj: ['all'] }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...arrayTestCases, ...securityTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't link projects with invalid privs '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        obj_to: 'user',
        obj_to_id: newKeyLoginId,
        privs: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't link projects with invalid obj_to_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.PROJECT,
          obj_id: newProject,
          company_id,
          obj_to: 'user',
          obj_to_id: input,
          privs: [{ type: 'view', list_obj: ['all'] }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...boolTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't link projects with invalid is_need_to_notify '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.PROJECT,
          obj_id: newProject,
          company_id,

          obj_to: 'user',
          is_need_to_notify: input,
          obj_to_id: newKeyLoginId,
          privs: [{ type: 'view', list_obj: ['all'] }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't favorite projects with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.FAVORITE,
          obj: OBJ_TYPE.PROJECT,
          obj_id: input,
          company_id,
          favorite: false,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't favorite project with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.FAVORITE,
          obj: OBJ_TYPE.PROJECT,
          obj_id: newProject,
          company_id: input,
          favorite: false,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...boolTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't favorite project with invalid favorite '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.FAVORITE,
          obj: OBJ_TYPE.PROJECT,
          obj_id: newProject,
          company_id,
          favorite: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't favorite project with invalid obj_short_name '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.FAVORITE,
          obj: OBJ_TYPE.PROJECT,
          company_id,
          obj_short_name: input,
          favorite: true,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't delete project with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.PROJECT,
          obj_id: newProject,
          company_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't delete project with invalid obj_short_name '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.PROJECT,
          company_id,
          obj_short_name: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't delete project with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.PROJECT,
          obj_id: input,
          company_id,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't restore project with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.RESTORE,
          obj: OBJ_TYPE.PROJECT,
          obj_id: newProject,
          company_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't restore project with invalid obj_short_name '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.RESTORE,
          obj: OBJ_TYPE.PROJECT,
          company_id,
          obj_short_name: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't restore project with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.RESTORE,
          obj: OBJ_TYPE.PROJECT,
          obj_id: input,
          company_id,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't destroy project with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DESTROY,
          obj: OBJ_TYPE.PROJECT,
          obj_id: newProject,
          company_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't destroy project with invalid obj_short_name '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DESTROY,
          obj: OBJ_TYPE.PROJECT,
          company_id,
          obj_short_name: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't destroy project with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DESTROY,
          obj: OBJ_TYPE.PROJECT,
          obj_id: input,
          company_id,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
