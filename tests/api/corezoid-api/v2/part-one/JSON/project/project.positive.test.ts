import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import createProjectSchema from '../../../../schemas/v2/project/createProject.schema.json';
import linkKeyProjectSchema from '../../../../schemas/v2/project/linkKeyProject.schema.json';
import destroyProjectSchema from '../../../../schemas/v2/project/destroyProject.schema.json';
import deleteProjectSchema from '../../../../schemas/v2/project/deleteProject.schema.json';
import listProjectSchema from '../../../../schemas/v2/project/listProject.schema.json';
import showProjectSchema from '../../../../schemas/v2/project/showProject.schema.json';
import modifyProjectSchema from '../../../../schemas/v2/project/modifyProject.schema.json';
import listSchema from '../../../../schemas/v2/project/listStage.schema.json';
import pathToFolderProjectSchema from '../../../../schemas/v2/project/pathToFolderProject.schema.json';
import getObjectProjectSchema from '../../../../schemas/v2/project/getObjectProject.schema.json';
import favoriteSchema from '../../../../schemas/v2/project/favoriteProject.schema.json';
import { makeRequestWithRetries, MyApiResponse } from '../../../../../../../utils/requestRetries';

describe('Project (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newProject2: string | number;
  let newStage: string | number;
  let short_name: string;
  let company_id: any;
  let newKeyLoginId: string | number;
  let newKeyObjId: string | number;
  let group_id: number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    short_name = `project2${Date.now()}`;

    const responseKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,

        title: `key_${Date.now()}`,
        logins: [{ type: 'api' }],
      }),
    );
    newKeyLoginId = responseKey.body.ops[0].users[0].obj_id;
    newKeyObjId = responseKey.body.ops[0].users[0].obj_id;
  });

  test(`should create Project with required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: `project${Date.now()}`,
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;
    SchemaValidator.validate(createProjectSchema, response.body);

    const responseDelete = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: `${newProject}`,
        company_id,
      }),
    );
    expect(responseDelete.status).toBe(200);
  });

  test(`should create Project stage (array with string)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: `${short_name}1`,
        description: 'test',
        stages: ['production', 'develop', 'pre'],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;
    newStage = response.body.ops[0].stages[0];
    SchemaValidator.validate(createProjectSchema, response.body);

    const responseGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,

        obj_type: `admins`,
        title: 'Group',
      }),
    );
    group_id = responseGroup.body.ops[0].obj_id;

    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: newKeyObjId,
        company_id,

        group_id,
        level: 1,
      }),
    );
    expect(responseLink.status).toBe(200);
  });

  test(`should list_project`, async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return response.status === 200 && response.body.ops[0].list.some((item: any) => item.obj_id === newProject);
    }

    const response = await makeRequestWithRetries(
      async () => {
        return await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.PROJECTS,
            company_id,
            sort: 'title',
          }),
        );
      },
      checkConditions,
      10,
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    SchemaValidator.validate(listProjectSchema, response.body);
  });

  test(`should list_project stage with required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newStage })]));
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newStage)?.is_owner).toEqual(true);
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should list_project stage with obj_short_name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        obj_short_name: `${short_name}1`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newStage })]));
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newStage)?.is_owner).toEqual(true);
    expect(response.body.ops[0].list[0].short_name).toBe('develop');
    expect(response.body.ops[0].list[1].short_name).toBe('pre');
    expect(response.body.ops[0].list[2].short_name).toBe('production');
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should list_project sort:name `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        sort: 'name',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].short_name).toBe('develop');
    expect(response.body.ops[0].list[1].short_name).toBe('pre');
    expect(response.body.ops[0].list[2].short_name).toBe('production');
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should list_project sort:title `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        sort: 'title',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].short_name).toBe('develop');
    expect(response.body.ops[0].list[1].short_name).toBe('pre');
    expect(response.body.ops[0].list[2].short_name).toBe('production');
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should list_project sort:owner `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        sort: 'owner',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[1].short_name).toBe('develop');
    expect(response.body.ops[0].list[0].short_name).toBe('pre');
    expect(response.body.ops[0].list[2].short_name).toBe('production');
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should list_project sort:date `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        sort: 'date',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[1].short_name).toBe('develop');
    expect(response.body.ops[0].list[0].short_name).toBe('pre');
    expect(response.body.ops[0].list[2].short_name).toBe('production');
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should list_project sort:tacts `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        sort: 'tacts',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[1].short_name).toBe('develop');
    expect(response.body.ops[0].list[2].short_name).toBe('pre');
    expect(response.body.ops[0].list[0].short_name).toBe('production');
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should list_project order:asc `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        order: 'asc',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].short_name).toBe('develop');
    expect(response.body.ops[0].list[1].short_name).toBe('pre');
    expect(response.body.ops[0].list[2].short_name).toBe('production');
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should list_project order:desc `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        order: 'desc',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[2].short_name).toBe('develop');
    expect(response.body.ops[0].list[1].short_name).toBe('pre');
    expect(response.body.ops[0].list[0].short_name).toBe('production');
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should show_project (stages) with required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].stages).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newStage })]),
    );
    expect(response.body.ops[0].stages).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_short_name: 'develop' })]),
    );
    expect(response.body.ops[0].stages).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_short_name: 'pre' })]),
    );
    expect(response.body.ops[0].stages).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_short_name: 'production' })]),
    );
    SchemaValidator.validate(showProjectSchema, response.body);
  });

  test(`should show_project (stages) with company_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].stages).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newStage })]),
    );
    expect(response.body.ops[0].stages).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_short_name: 'production' })]),
    );
    expect(response.body.ops[0].stages).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_short_name: 'develop' })]),
    );
    expect(response.body.ops[0].stages).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_short_name: 'pre' })]),
    );
    SchemaValidator.validate(showProjectSchema, response.body);
  });

  test(`should show_project (stages) with obj_short_name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        obj_short_name: `${short_name}1`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].stages).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newStage })]),
    );
    expect(response.body.ops[0].stages).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_short_name: 'production' })]),
    );
    expect(response.body.ops[0].stages).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_short_name: 'develop' })]),
    );
    expect(response.body.ops[0].stages).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_short_name: 'pre' })]),
    );
    SchemaValidator.validate(showProjectSchema, response.body);
  });

  test(`should link_project with required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        obj_to: 'user',
        obj_to_id: newKeyLoginId,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(linkKeyProjectSchema, response.body);
  });

  test(`should list_path_to_folder_project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PATH_TO_FOLDER,
        obj_id: newProject,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(pathToFolderProjectSchema, response.body);
  });

  test(`should list_project list_obj:group after link`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        list_obj: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].obj_id).toBe(newKeyLoginId);
  });

  test(`should get_object_project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: newProject,
        company_id,
        obj_type: 'project',
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(getObjectProjectSchema, response.body);
  });

  test(`should modify Project with required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('project');
    SchemaValidator.validate(modifyProjectSchema, response.body);
  });

  test(`should link_project with obj_short_name and is_need_to_notify: false`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        obj_short_name: `${short_name}1`,
        is_need_to_notify: false,
        obj_to: 'user',
        obj_to_id: newKeyLoginId,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(linkKeyProjectSchema, response.body);

    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        list_obj: 'group',
      }),
    );
    expect(responseLink.status).toBe(200);
    expect(responseLink.body.ops[0].list[0].obj_id).toBe(newKeyLoginId);
    expect(responseLink.body.ops[0].list[0].privs).toEqual(
      expect.arrayContaining([expect.objectContaining({ type: 'view' })]),
    );
    expect(responseLink.body.ops[0].list[0].privs).toEqual(
      expect.arrayContaining([expect.objectContaining({ type: 'create' })]),
    );
  });

  test(`should add/remove project from favorite with required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        favorite: true,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(favoriteSchema, response.body);

    const responseRemove = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        favorite: false,
      }),
    );
    expect(responseRemove.status).toBe(200);
    SchemaValidator.validate(favoriteSchema, responseRemove.body);
  });

  test(`should add/remove project from favorite with company_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        favorite: true,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(favoriteSchema, response.body);

    const responseRemove = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        favorite: false,
      }),
    );
    expect(responseRemove.status).toBe(200);
    SchemaValidator.validate(favoriteSchema, responseRemove.body);
  });

  test(`should add/remove project from favorite with obj_short_name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        obj_short_name: `${short_name}1`,
        favorite: true,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(favoriteSchema, response.body);

    const responseRemove = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        obj_short_name: `${short_name}1`,
        favorite: false,
      }),
    );
    expect(responseRemove.status).toBe(200);
    SchemaValidator.validate(favoriteSchema, responseRemove.body);
  });

  test(`should modify Project`, async () => {
    short_name = `project-modify${Date.now()}`;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        title: short_name,
        short_name,
        description: 'testModify',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('project');
    SchemaValidator.validate(modifyProjectSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECTS,
        company_id,
        sort: 'title',
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[0].obj_type).toBe('project');
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newProject })]),
    );
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: short_name })]),
    );
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ short_name: short_name })]),
    );
  });

  test(`should link_project with group and is_need_to_notify: true`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,

        is_need_to_notify: true,
        obj_to: 'group',
        obj_to_id: group_id,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
          { type: 'modify', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(linkKeyProjectSchema, response.body);

    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        list_obj: 'group',
      }),
    );
    expect(responseLink.status).toBe(200);
    expect(responseLink.body.ops[0].list[0].obj_id).toBe(group_id);
    expect(responseLink.body.ops[0].list[0].privs).toEqual(
      expect.arrayContaining([expect.objectContaining({ type: 'view' })]),
    );
    expect(responseLink.body.ops[0].list[0].privs).toEqual(
      expect.arrayContaining([expect.objectContaining({ type: 'create' })]),
    );
    expect(responseLink.body.ops[0].list[0].privs).toEqual(
      expect.arrayContaining([expect.objectContaining({ type: 'modify' })]),
    );
  });

  test(`should delete project with obj_short_name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        obj_short_name: short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newProject);
    SchemaValidator.validate(deleteProjectSchema, response.body);
  });

  test(`should restore project with obj_short_name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        obj_short_name: short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newProject);
    expect(response.body.ops[0].obj).toBe('project');
    SchemaValidator.validate(deleteProjectSchema, response.body);
  });

  test(`should delete project with required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newProject);
    SchemaValidator.validate(deleteProjectSchema, response.body);
  });

  test(`should restore project with required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newProject);
    expect(response.body.ops[0].obj).toBe('project');
    SchemaValidator.validate(deleteProjectSchema, response.body);
  });

  test(`should delete project after restore`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newProject);
    SchemaValidator.validate(deleteProjectSchema, response.body);
  });

  test(`should restore project with company_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newProject);
    expect(response.body.ops[0].obj).toBe('project');
    SchemaValidator.validate(deleteProjectSchema, response.body);
  });

  test(`should delete project with company_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newProject);
    SchemaValidator.validate(deleteProjectSchema, response.body);
  });

  test(`should destroy project with obj_short_name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        obj_short_name: short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newProject);
    SchemaValidator.validate(destroyProjectSchema, response.body);
  });

  test(`should create Project stage (array with object/with immutable)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: `project${Date.now()}`,
        description: 'test',
        stages: [
          { title: 'production', immutable: true },
          { title: 'develop', immutable: false },
        ],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;
    SchemaValidator.validate(createProjectSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        project_id: newProject,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[0].obj_type).toBe('stage');
    expect(
      (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'develop').uniq_undeployed_versions,
    ).toEqual(0);
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.title === 'production').undeployed).toEqual(
      0,
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.title === 'develop').immutable).toEqual(
      false,
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.title === 'production').immutable).toEqual(
      true,
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: `${newProject}`,
        company_id,
      }),
    );
  });

  test(`should create Project stage (array with object/without immutable)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name,
        description: 'test',
        stages: [{ title: 'production' }, { title: 'develop' }],
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;
    SchemaValidator.validate(createProjectSchema, response.body);
  });

  test(`should create Project with same short_name`, async () => {
    const response2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name,
        description: 'test',
        stages: [{ title: 'production' }, { title: 'develop' }],
        update_not_uniq_short_name: false,
      }),
    );
    expect(response2.status).toBe(200);
    expect(response2.body.ops[0].description).toContain(`Unable to create project with short_name`);
  });

  test(`should create Project with update_not_uniq_short_name: true`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name,
        description: 'test',
        stages: [{ title: 'production' }, { title: 'develop' }],
        update_not_uniq_short_name: true,
      }),
    );
    expect(response.status).toBe(200);
    newProject2 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createProjectSchema, response.body);
  });

  test(`should list Project after create`, async () => {
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        project_id: newProject,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[0].obj_type).toBe('stage');
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.title === 'develop').immutable).toEqual(
      false,
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.title === 'production').immutable).toEqual(
      false,
    );

    const responseList2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject2,
        company_id,
        project_id: newProject2,
      }),
    );
    expect(responseList2.status).toBe(200);
    expect(responseList2.body.ops[0].list[0].obj_type).toBe('stage');
    expect((responseList2.body.ops[0].list as Array<any>).find(item => item.title === 'develop').immutable).toEqual(
      false,
    );
    expect((responseList2.body.ops[0].list as Array<any>).find(item => item.title === 'production').immutable).toEqual(
      false,
    );
  });

  test(`should destroy project with required param`, async () => {
    const responseDelete = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(responseDelete.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newProject);
    SchemaValidator.validate(destroyProjectSchema, response.body);
  });

  test(`should destroy project with company_id`, async () => {
    const responseDelete = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject2,
        company_id,
      }),
    );
    expect(responseDelete.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject2,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newProject2);
    SchemaValidator.validate(destroyProjectSchema, response.body);
  });

  afterAll(async () => {
    const responseKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: newKeyObjId,
        company_id,

        group_id: 0,
        level: '',
      }),
    );
    expect(responseKey.status).toBe(200);

    const ResponseDeleteGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        obj_id: group_id,
        company_id,
      }),
    );
    expect(ResponseDeleteGroup.status).toBe(200);
  });
});
