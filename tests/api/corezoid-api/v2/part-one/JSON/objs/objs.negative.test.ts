import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  maxLength,
  stringNotValidTestCases,
  boolTestCases,
} from '../../../../../negativeCases';

describe('Obj List (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  const valuesToSkip: any = [''];
  let newProject: string | number;
  let newStage: string | number;
  let company_id: any;
  let stage_short_name: string;
  let project_short_name: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    stage_short_name = `stage-${Date.now()}`;
    project_short_name = `project-${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
        project_short_name: `test`,
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        title: `Conv_${Date.now()}`,
        conv_type: 'state',
        stage_id: newStage,
        project_id: newProject,
      }),
    );
  });

  test.each([...stringTestCases, ...undefinedTestCase, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't list objs with invalid pattern '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.OBJS,
          pattern: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([1, -1, 0, 123, true, {}, []])(`should't list objs in Company with invalid company_id`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,
        pattern: 'conv',
        company_id: param,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual('Value is not valid');
  });

  test.each([...stringTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't list objs with invalid filter '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.OBJS,
          pattern: 'conv',
          filter: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't list objs with invalid limit '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.OBJS,
          pattern: 'conv',
          limit: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([1001])(`shouldn't list objs with invalid limit 1001`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,
        pattern: 'conv',
        limit: param,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(
      `Value is not valid. Value's limit is more than maximum allowed: 1000`,
    );
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't list objs with invalid pattern '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,
        pattern: 'test',
        filter: 'user',
        user_type: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...boolTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't list objs with invalid only_owner '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.OBJS,
          pattern: input,
          only_owner: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
