import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  objectTestCases,
  maxLength,
  companyTestCases,
  stringNotValidTestCases,
  securityTestCases,
  arrayTestCases,
  boolTestCases,
} from '../../../../../negativeCases';

describe('Node (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: string | number;
  let company_id: any;
  let newNode: string;
  let valuesToSkip: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Process_${Date.now()}`);
    newConv = response.body.ops[0].obj_id;

    const responseNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_type: 0,
        description: 'test',
        conv_id: newConv,
        title: `Node_${Date.now()}`,
        version: 1,
      }),
    );
    newNode = responseNode.body.ops[0].obj_id;
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create node in conv with invalid conv_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_type: 0,
        description: 'test',
        conv_id: input,
        title: `Node_${Date.now()}`,
        version: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create node in conv with invalid obj_alias '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_type: 0,
        description: 'test',
        obj_alias: input,
        title: `Node_${Date.now()}`,
        version: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't create node in conv with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.NODE,
          company_id: input,
          obj_type: 0,
          description: 'test',
          conv_id: newConv,
          title: `Node_${Date.now()}`,
          version: 1,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0];

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create node in conv with invalid obj_type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_type: input,
        description: 'test',
        conv_id: newConv,
        title: `Node_${Date.now()}`,
        version: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...stringTestCases, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't create node in conv with invalid title '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_type: 0,
          description: 'test',
          conv_id: newConv,
          title: input,
          version: 1,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [null];

  test.each(
    [...stringTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create node in conv with invalid description '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_type: 0,
        description: input,
        conv_id: newConv,
        title: `Node_${Date.now()}`,
        version: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't create node in conv with invalid description '%s'`,
    async (input, errors) => {
      void errors;
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_type: 0,
          description: input,
          conv_id: newConv,
          title: `Node_${Date.now()}`,
          version: 1,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toContain(
        `Value is not valid. Value's byte_size is more than maximum allowed: 2000`,
      );
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't create node in conv with invalid version '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_type: 0,
          description: '',
          conv_id: newConv,
          title: `Node_${Date.now()}`,
          version: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0];

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't reset node in conv with invalid conv_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESET,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,
        conv_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't reset node in conv with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESET,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: input,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't reset node in conv with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.RESET,
          obj: OBJ_TYPE.NODE,
          company_id: input,
          obj_id: newNode,
          conv_id: newConv,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't show node in conv with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.NODE,
          company_id: input,
          obj_id: newNode,
          conv_id: newConv,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't show node in conv with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: input,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't show node in conv with invalid conv_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,
        conv_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't list node in conv with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: input,

        conv_id: newConv,
        limit: 1,
        offset: 0,
        sort: 'ASC',
        filter: [{ name: 'ref', fun: 'eq', value: 'test' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't list node in conv with invalid conv_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,
        conv_id: input,
        limit: 1,
        offset: 0,
        sort: 'ASC',
        filter: [{ name: 'ref', fun: 'eq', value: 'test' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't list node in conv with invalid obj_alias '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,

        obj_alias: input,
        limit: 1,
        offset: 0,
        sort: 'ASC',
        filter: [{ name: 'ref', fun: 'eq', value: 'test' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't list node in conv with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.NODE,
          company_id: input,
          obj_id: newNode,

          conv_id: newConv,
          limit: 1,
          offset: 0,
          sort: 'ASC',
          filter: [{ name: 'ref', fun: 'eq', value: 'test' }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0];

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't list node in conv with invalid limit '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,

        conv_id: newConv,
        limit: input,
        offset: 0,
        sort: 'ASC',
        filter: [{ name: 'ref', fun: 'eq', value: 'test' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't list node in conv with invalid offset '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,

        conv_id: newConv,
        limit: 1,
        offset: input,
        sort: 'ASC',
        filter: [{ name: 'ref', fun: 'eq', value: 'test' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...stringTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't list node in conv with invalid sort '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          limit: 1,
          offset: 0,
          sort: input,
          filter: [{ name: 'ref', fun: 'eq', value: 'test' }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...arrayTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't list node in conv with invalid filter '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          limit: 1,
          offset: 0,
          sort: 'ASC',
          filters: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't list node in conv with invalid filters.name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,

        conv_id: newConv,
        limit: 1,
        offset: 0,
        sort: 'ASC',
        filters: [{ name: input, fun: 'eq', value: 'test' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't list node in conv with invalid filters.fun '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,

        conv_id: newConv,
        limit: 1,
        offset: 0,
        sort: 'ASC',
        filters: [{ name: 'ref', fun: input, value: 'test' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't list node in conv with invalid filters.value '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          limit: 1,
          offset: 0,
          sort: 'ASC',
          filters: [{ name: 'ref', fun: 'eq', value: input }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify node in conv with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: input,

        conv_id: newConv,
        logics: [],
        semaphors: [],
        obj_type: 0,
        position: [0, 0],
        extra: {},
        options: { save_task: true },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify node in conv with invalid conv_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,

        conv_id: input,
        logics: [],
        semaphors: [],
        obj_type: 0,
        position: [0, 0],
        extra: {},
        options: { save_task: true },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id: input,
          obj_id: newNode,

          conv_id: newConv,
          logics: [],
          semaphors: [],
          obj_type: 0,
          position: [0, 0],
          extra: {},
          options: { save_task: true },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...undefinedTestCase, ...arrayTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: input,
          semaphors: [],
          obj_type: 0,
          position: [0, 0],
          extra: {},
          options: { save_task: true },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...arrayTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics [] '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [input],
          semaphors: [],
          obj_type: 0,
          position: [0, 0],
          extra: {},
          options: { save_task: true },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...arrayTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid semaphors '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [],
          semaphors: input,
          obj_type: 0,
          position: [0, 0],
          extra: {},
          options: { save_task: true },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0];

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify node in conv with invalid obj_type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,

        conv_id: newConv,
        logics: [],
        semaphors: [],
        obj_type: input,
        position: [0, 0],
        extra: {},
        options: { save_task: true },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...arrayTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid position '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [],
          semaphors: [],
          obj_type: 0,
          position: input,
          extra: {},
          options: { save_task: true },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...objectTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid extra '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [],
          semaphors: [],
          obj_type: 0,
          position: [],
          extra: input,
          options: { save_task: true },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...objectTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid options '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,
          conv_id: newConv,
          logics: [],
          semaphors: [],
          obj_type: 0,
          position: [],
          extra: {},
          options: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...boolTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid options.save_task '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [],
          semaphors: [],
          obj_type: 0,
          position: [],
          extra: {},
          options: { save_task: input },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases, ...maxLength].map(({ input, errors }) => [
      input,
      errors,
    ]),
  )(`shouldn't modify node in conv with invalid semaphors.value '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,

        conv_id: newConv,
        logics: [],
        semaphors: [{ type: 'time', value: input, dimension: 'min', to_node_id: newNode, esc_node_id: newNode }],
        obj_type: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.skip.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases, ...maxLength].map(({ input, errors }) => [
      input,
      errors,
    ]),
  )(`shouldn't modify node in conv with invalid semaphors.to_node_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,

        conv_id: newConv,
        logics: [],
        semaphors: [{ type: 'time', value: 1, dimension: 'min', to_node_id: input, esc_node_id: newNode }],
        obj_type: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.skip.each(
    [...stringTestCases, ...stringNotValidTestCases, ...maxLength].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify node in conv with invalid semaphors.esc_node_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,

        conv_id: newConv,
        logics: [],
        semaphors: [{ type: 'time', value: 1, dimension: 'min', to_node_id: newConv, esc_node_id: input }],
        obj_type: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase, ...maxLength].map(({ input, errors }) => [
      input,
      errors,
    ]),
  )(`shouldn't modify node in conv with invalid logics.type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,

        conv_id: newConv,
        logics: [
          {
            to_node_id: null,
            type: input,
            conditions: [{ param: 'test', const: '1', fun: 'eq', cast: 'string' }],
            error: true,
          },
        ],
        obj_type: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.conditions.param '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [
            {
              to_node_id: null,
              type: 'go_if_const',
              conditions: [{ param: input, const: '1', fun: 'eq', cast: 'string' }],
              error: true,
            },
          ],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...undefinedTestCase, ...stringTestCases, ...maxLength, ...stringNotValidTestCases].map(({ input, errors }) => [
      input,
      errors,
    ]),
  )(`shouldn't modify node in conv with invalid logics.conditions.cast '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,

        conv_id: newConv,
        logics: [
          {
            to_node_id: null,
            type: 'go_if_const',
            conditions: [{ param: 'test', const: '1', fun: '', cast: input }],
            error: true,
          },
        ],
        obj_type: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [1];

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases, ...maxLength]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify node in conv with invalid logics.conv_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,

        conv_id: newConv,
        logics: [
          {
            to_node_id: null,
            err_node_id: null,
            type: 'api_copy',
            conv_id: input,
            convTitle: 2,
            send_parent_data: false,
            mode: 'create',
            data: { a: '1' },
            data_type: { a: 'string' },
            group: 'all',
            ref: '',
          },
        ],
        obj_type: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.err_node_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [
            {
              to_node_id: null,
              err_node_id: input,
              type: 'api_copy',
              conv_id: 1,
              convTitle: 2,
              send_parent_data: false,
              mode: 'create',
              data: { a: '1' },
              data_type: { a: 'string' },
              group: 'all',
              ref: '',
            },
          ],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [
      ...undefinedTestCase,
      ...stringTestCases,
      ...maxLength,
      ...securityTestCases,
      ...stringNotValidTestCases,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify node in conv with invalid logics.mode '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,

        conv_id: newConv,
        logics: [
          {
            to_node_id: null,
            err_node_id: null,
            type: 'api_copy',
            conv_id: 1,
            convTitle: 1,
            send_parent_data: false,
            mode: input,
            data: { a: '1' },
            data_type: { a: 'string' },
            group: 'all',
            ref: '',
          },
        ],
        obj_type: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.ref '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [
            {
              to_node_id: null,
              err_node_id: null,
              type: 'api_copy',
              conv_id: 1,
              convTitle: 1,
              send_parent_data: false,
              mode: 'create',
              data: { a: '1' },
              data_type: { a: 'string' },
              group: 'all',
              ref: input,
            },
          ],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...undefinedTestCase, ...objectTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.data '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [
            {
              to_node_id: null,
              err_node_id: null,
              type: 'api_copy',
              conv_id: 1,
              convTitle: 1,
              send_parent_data: false,
              mode: 'create',
              data: input,
              data_type: { a: 'string' },
              group: 'all',
              ref: '',
            },
          ],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...objectTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.data_type '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [
            {
              to_node_id: null,
              err_node_id: null,
              type: 'api_copy',
              conv_id: 1,
              convTitle: 1,
              send_parent_data: false,
              mode: 'create',
              data: { a: '1' },
              data_type: input,
              group: 'all',
              ref: '',
            },
          ],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...undefinedTestCase, ...arrayTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.extra '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [{ err_node_id: null, type: 'api_sum', extra: input }],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.extra.id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [{ err_node_id: null, type: 'api_sum', extra: [{ id: input, name: 'a', value: '1' }] }],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.extra.name '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [{ err_node_id: null, type: 'api_sum', extra: [{ id: '*********', name: input, value: '1' }] }],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.extra.value '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [{ err_node_id: null, type: 'api_sum', extra: [{ id: '*********', name: 'a', value: input }] }],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.extra.value '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [{ err_node_id: null, type: 'api_sum', extra: [{ id: '*********', name: 'a', value: input }] }],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...maxLength, ...stringTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.lang '%s'`,
    async input => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [{ err_node_id: null, type: 'api_code', lang: input, src: 'data.a = 123;' }],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toContain(
        'is not valid. Value is not in allowed list <<"[<<\\"erl\\">>,<<\\"js\\">>]">>',
      );
    },
  );

  test.each([...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.res_data '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [
            {
              err_node_id: null,
              type: 'api_rpc_reply',
              res_data: input,
              res_data_type: { a: 'string' },
              mode: 'key_value',
              throw_exception: false,
            },
          ],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...objectTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.res_data_type '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [
            {
              err_node_id: null,
              type: 'api_rpc_reply',
              res_data: { a: '1' },
              res_data_type: input,
              mode: 'key_value',
              throw_exception: false,
            },
          ],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...stringNotValidTestCases, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.res_data_type.a '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [
            {
              err_node_id: null,
              type: 'api_rpc_reply',
              res_data: { a: '1' },
              res_data_type: { a: input },
              mode: 'key_value',
              throw_exception: false,
            },
          ],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...maxLength, ...undefinedTestCase].map(({ input, errors }) => [
      input,
      errors,
    ]),
  )(`shouldn't modify node in conv with invalid logics.mode '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,

        conv_id: newConv,
        logics: [
          {
            err_node_id: null,
            type: 'api_rpc_reply',
            res_data: { a: 'a' },
            res_data_type: { a: 'string' },
            mode: input,
            throw_exception: false,
          },
        ],
        obj_type: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [1];

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify node in conv with invalid logics.conv_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,

        conv_id: newConv,
        logics: [{ err_node_id: null, type: 'api_rpc', extra: {}, extra_type: {}, group: '', conv_id: input }],
        obj_type: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...objectTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.extra_type '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [{ err_node_id: null, type: 'api_rpc', extra: {}, extra_type: input, group: '', conv_id: '1' }],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.format '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [
            {
              err_node_id: null,
              type: 'api',
              format: input,
              method: 'POST',
              url: 'https://test.com',
              extra: {},
              extra_type: {},
              max_threads: 101,
              debug_info: false,
              customize_response: false,
              response: { header: '{{header}}', body: '{{body}}' },
              response_type: { header: 'object', body: 'object' },
              extra_headers: { 'content-type': 'application/json;charset=utf-8' },
              send_sys: true,
              cert_pem: '',
              content_type: 'application/json',
              rfc_format: true,
              version: 2,
            },
          ],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.url '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [
            {
              err_node_id: null,
              type: 'api',
              format: '',
              method: 'POST',
              url: input,
              extra: {},
              extra_type: {},
              max_threads: 101,
              debug_info: false,
              customize_response: false,
              response: { header: '{{header}}', body: '{{body}}' },
              response_type: { header: 'object', body: 'object' },
              extra_headers: { 'content-type': 'application/json;charset=utf-8' },
              send_sys: true,
              cert_pem: '',
              content_type: 'application/json',
              rfc_format: true,
              version: 2,
            },
          ],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.skip.each([...stringTestCases, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.extra_type '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [
            {
              err_node_id: null,
              type: 'api',
              format: '',
              method: 'POST',
              url: 'https://test.com',
              extra: {},
              extra_type: input,
              max_threads: 101,
              debug_info: false,
              customize_response: false,
              response: { header: '{{header}}', body: '{{body}}' },
              response_type: { header: 'object', body: 'object' },
              extra_headers: { 'content-type': 'application/json;charset=utf-8' },
              send_sys: true,
              cert_pem: '',
              content_type: 'application/json',
              rfc_format: true,
              version: 2,
            },
          ],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [-1, 0, ''];

  test.each(
    [...integerTestCases, ...maxLength]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify node in conv with invalid logics.max_threads '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,

        conv_id: newConv,
        logics: [
          {
            err_node_id: null,
            type: 'api',
            format: '',
            method: 'POST',
            url: 'https://test.com',
            extra: {},
            extra_type: {},
            max_threads: input,
            debug_info: false,
            customize_response: false,
            response: { header: '{{header}}', body: '{{body}}' },
            response_type: { header: 'object', body: 'object' },
            extra_headers: { 'content-type': 'application/json;charset=utf-8' },
            send_sys: true,
            cert_pem: '',
            content_type: 'application/json',
            rfc_format: true,
            version: 2,
          },
        ],
        obj_type: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.skip.each([...boolTestCases, ...maxLength, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.response '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [
            {
              err_node_id: null,
              type: 'api',
              format: '',
              method: 'POST',
              url: 'https://test.com',
              extra: {},
              extra_type: {},
              max_threads: 100,
              debug_info: false,
              customize_response: false,
              response: input,
              response_type: { header: 'object', body: 'object' },
              extra_headers: { 'content-type': 'application/json;charset=utf-8' },
              send_sys: true,
              cert_pem: '',
              content_type: 'application/json',
              rfc_format: true,
              version: 2,
            },
          ],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...objectTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify node in conv with invalid logics.response_type '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          company_id,
          obj_id: newNode,

          conv_id: newConv,
          logics: [
            {
              err_node_id: null,
              type: 'api',
              format: '',
              method: 'POST',
              url: 'https://test.com',
              extra: {},
              extra_type: {},
              max_threads: 100,
              debug_info: false,
              customize_response: false,
              response: { header: '{{header}}', body: '{{body}}' },
              response_type: input,
              extra_headers: { 'content-type': 'application/json;charset=utf-8' },
              send_sys: true,
              cert_pem: '',
              content_type: 'application/json',
              rfc_format: true,
              version: 2,
            },
          ],
          obj_type: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't delete node in conv with invalid conv_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,
        conv_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't delete node in conv with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: input,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = ['te'];

  test.each(
    [...companyTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't delete node in conv with invalid company_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.NODE,
        company_id: input,
        obj_id: newNode,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't delete node in conv with invalid obj_alias '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: newNode,
        obj_alias: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  afterAll(async () => {
    const responseConv = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, company_id);
    expect(responseConv.status).toBe(200);
  });
});
