import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createSchema from '../../../../schemas/v2/actions-objects/createNode.schema.json';
import modifySchema from '../../../../schemas/v2/actions-objects/modifyNode.schema.json';
import deleteSchema from '../../../../schemas/v2/actions-objects/deleteNode.schema.json';
import showSchema from '../../../../schemas/v2/actions-objects/showNode.schema.json';
import listSchema from '../../../../schemas/v2/actions-objects/listNode.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('Node (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: string | number;
  let newAlias: string | number;
  let newNode: string;
  let newNode1: string;
  let newNode2: string;
  let newNodeA: string;
  let newNodeC: string;
  let newNodeE: string;
  let newNodeD: string;
  let newNodeF: string;
  let newNodeS: string;
  let company_id: any;
  let aliasShortName: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    aliasShortName = `alias-${Date.now()}`;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: 0,
        title: `Process_${Date.now()}`,
        create_mode: 'without_nodes',
      }),
    );
    newConv = response.body.ops[0].obj_id;

    const responseAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        short_name: aliasShortName,
        description: `aliasDesk-${Date.now()}`,
        title: `aliasTitle-${Date.now()}`,
        project_id: 0,
      }),
    );
    expect(responseAlias.status).toBe(200);
    newAlias = responseAlias.body.ops[0].obj_id;

    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,

        obj_to_id: newConv,
        obj_to_type: 'conv',
        link: true,
        project_id: 0,
      }),
    );
    expect(responseLink.status).toBe(200);
    expect(responseLink.body.ops[0].obj_id).toEqual(newAlias);
  });

  test('should create node obj_type 0', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_type: 0,
        description: 'test',
        conv_id: newConv,
        title: `Node_${Date.now()}`,
        version: 1,
      }),
    );
    expect(response.status).toBe(200);
    newNode = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should not reset node obj_type 0', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESET,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Counter reset is applicable only for final nodes');
  });

  test('should show node obj_type 0', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(showSchema, response.body);
  });

  test('should list node obj_type 0', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,
        conv_id: newConv,
        limit: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].count).toBe(0);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should delete node obj_type 0', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,
        conv_id: newConv,
        version: 1,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should create node obj_type 1 Start_node', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_type: 1,
        description: 'test',
        conv_id: newConv,
        title: `Node_${Date.now()}`,
        version: 1,
      }),
    );
    expect(response.status).toBe(200);
    newNode1 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should not reset node obj_type 1 Start_node', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESET,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode1,
        company_id,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Counter reset is applicable only for final nodes');
  });

  test('should modify node obj_type 1 Start_node', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode1,
        company_id,
        conv_id: newConv,
        logics: [],
        obj_type: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(modifySchema, response.body);
  });

  test('should show node obj_type 1 Start_node', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode1,
        company_id,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(showSchema, response.body);
  });

  test('should list node obj_type 1 Start_node', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode1,
        company_id,
        conv_id: newConv,
        limit: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].count).toBe(0);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should create node obj_type 2', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_type: 2,
        description: 'test',
        conv_id: newConv,
        title: `Node_${Date.now()}`,
        version: 1,
      }),
    );
    expect(response.status).toBe(200);
    newNode = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should reset node obj_type 2', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESET,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('node');
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should show node obj_type 2', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(showSchema, response.body);
  });

  test('should list node obj_type 2', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,
        conv_id: newConv,
        limit: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].count).toBe(0);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should delete node obj_type 2', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,
        conv_id: newConv,
        version: 1,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should create node obj_type 3', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_type: 3,
        description: 'test',
        conv_id: newConv,
        title: `Node_${Date.now()}`,
        version: 1,
      }),
    );
    expect(response.status).toBe(200);
    newNode = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should show node obj_type 3', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(showSchema, response.body);
  });

  test('should modify node obj_type 3', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,
        conv_id: newConv,
        logics: [],
        obj_type: 3,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(modifySchema, response.body);
  });

  test('should list node obj_type 3', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,
        conv_id: newConv,
        limit: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].count).toBe(0);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should not reset node obj_type 3', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESET,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Counter reset is applicable only for final nodes');
  });

  test('should delete node obj_type 3', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,
        conv_id: newConv,
        version: 1,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should create node by alias_short_name', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_type: 2,
        description: 'test',
        obj_alias: aliasShortName,
        title: `Node_${Date.now()}`,
        version: 1,
      }),
    );
    expect(response.status).toBe(200);
    newNode = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should list node by alias_short_name', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,
        obj_alias: aliasShortName,
        limit: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].count).toBe(0);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should delete node by alias_short_name', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,
        obj_alias: aliasShortName,
        version: 1,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should list node with task', async () => {
    const responseCreateFinal = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_type: 2,
        description: 'test',
        conv_id: newConv,
        title: `Node_${Date.now()}`,
        version: 1,
      }),
    );
    expect(responseCreateFinal.status).toBe(200);
    newNode2 = responseCreateFinal.body.ops[0].obj_id;

    const responseModifyNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode1,
        company_id,

        obj_type: 1,
        description: 'test',
        conv_id: newConv,
        title: `Start`,
        version: 1,
        logics: [{ type: 'go', to_node_id: newNode2 }],
        semaphors: [],
      }),
    );
    expect(responseModifyNode.status).toBe(200);

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        conv_id: newConv,
        version: 1,
      }),
    );
    expect(responseCommit.status).toBe(200);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: newConv,
        data: { a: 1 },
        ref: 'task1',
        action: 'user',
      }),
    );
    expect(responseTask.status).toBe(200);

    const responseTask2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: newConv,
        data: { a: 2 },
        ref: 'task2',
        action: 'user',
      }),
    );
    expect(responseTask2.status).toBe(200);

    const responseTask3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: newConv,
        data: { a: 3 },
        ref: 'task3',
        action: 'user',
      }),
    );
    expect(responseTask3.status).toBe(200);

    await new Promise(r => setTimeout(r, 6000));

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode2,
        company_id,
        conv_id: newConv,
        limit: 10,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].count).toBe(3);
    const taskData = (response.body.ops[0].list as Array<any>).find(item => item.ref === 'task1').data;
    expect(taskData).toEqual({ a: 1 });
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list node with task find with filter', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode2,
        company_id,

        conv_id: newConv,
        limit: 10,
        offset: 0,
        sort: 'ASC',
        filters: [{ name: 'ref', fun: 'eq', value: 'task1' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].count).toBe(3);
    expect(response.body.ops[0].list[0].data).toEqual({ a: 1 });
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should create node only required parameter', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        obj_type: 2,
        conv_id: newConv,
        version: 1,
      }),
    );
    expect(response.status).toBe(200);
    newNode = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should reset node only required parameter', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESET,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('node');
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should delete node only required parameter', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test(`should create node Api call/Condition/Delay/Final`, async () => {
    const responseApiCall = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,

        obj_type: 0,
        description: `test`,
        conv_id: newConv,
        title: `Api_call_${Date.now()}`,
        version: 1,
        id: `1`,
      }),
    );
    expect(responseApiCall.status).toBe(200);
    newNodeA = responseApiCall.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, responseApiCall.body);

    const responseEscalation = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,

        obj_type: 3,
        description: `test`,
        conv_id: newConv,
        title: `Escalation_node_for_API_Call_${Date.now()}`,
        version: 1,
        id: `2`,
      }),
    );
    expect(responseEscalation.status).toBe(200);
    SchemaValidator.validate(createSchema, responseEscalation.body);
    newNodeE = responseEscalation.body.ops[0].obj_id;

    const responseDelay = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,

        obj_type: 0,
        description: `test`,
        conv_id: newConv,
        title: `Delay_for_API_Call_${Date.now()}`,
        version: 1,
        id: `3`,
      }),
    );
    expect(responseDelay.status).toBe(200);
    SchemaValidator.validate(createSchema, responseDelay.body);
    newNodeD = responseDelay.body.ops[0].obj_id;

    const responseFinal = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,

        obj_type: 2,
        description: `test`,
        conv_id: newConv,
        title: `Final_for_API_Call_${Date.now()}`,
        version: 1,
        id: `4`,
      }),
    );
    expect(responseFinal.status).toBe(200);
    SchemaValidator.validate(createSchema, responseFinal.body);
    newNodeF = responseFinal.body.ops[0].obj_id;
  });

  test(`should modify node (logics sequence) to Api Call/Condition/Delay/Final logic`, async () => {
    const responseApiCall = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: newNodeA,

        conv_id: newConv,
        title: `Api_Call`,
        description: `test`,
        obj_type: 0,
        logics: [
          { type: `go`, to_node_id: newNodeF },
          {
            type: `api`,
            err_node_id: newNodeE,
            format: ``,
            method: `POST`,
            url: `https:\/\/test.com`,
            extra: { key1: `value1`, key2: `2222` },
            extra_type: { key1: `string`, key2: `number` },
            max_threads: 5,
            debug_info: false,
            customize_response: false,
            response: { header: `test`, body: `test` },
            response_type: { header: `object`, body: `object` },
            extra_headers: {},
            send_sys: true,
            cert_pem: ``,
            content_type: `application\/json`,
          },
        ],
        semaphors: [],
        position: [-1680, -100],
        extra: { modeForm: `expand`, icon: `` },
        version: 1,
      }),
    );
    expect(responseApiCall.status).toBe(200);
    SchemaValidator.validate(modifySchema, responseApiCall.body);
  });

  test(`should modify node to Api Call/Condition/Delay/Final logic`, async () => {
    const responseApiCall = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: newNodeA,

        conv_id: newConv,
        title: `Api_Call`,
        description: `test`,
        obj_type: 0,
        logics: [
          {
            type: `api`,
            err_node_id: newNodeE,
            format: ``,
            method: `POST`,
            url: `https:\/\/test.com`,
            extra: { key1: `value1`, key2: `2` },
            extra_type: { key1: `string`, key2: `number` },
            max_threads: 5,
            debug_info: false,
            customize_response: false,
            response: { header: `test`, body: `test` },
            response_type: { header: `object`, body: `object` },
            extra_headers: {},
            send_sys: true,
            cert_pem: ``,
            content_type: `application\/json`,
          },
          { type: `go`, to_node_id: newNodeF },
        ],
        semaphors: [],
        position: [-1680, -100],
        extra: { modeForm: `expand`, icon: `` },
        version: 1,
      }),
    );
    expect(responseApiCall.status).toBe(200);
    SchemaValidator.validate(modifySchema, responseApiCall.body);

    const responseEscalation = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: newNodeE,

        conv_id: newConv,
        title: `Escalation`,
        description: `test`,
        obj_type: 3,
        logics: [
          {
            type: `go_if_const`,
            to_node_id: newNodeD,
            conditions: [{ fun: `eq`, const: `hardware`, param: `__conveyor_api_return_type_error__`, cast: `string` }],
          },
          {
            type: `go_if_const`,
            to_node_id: newNodeD,
            conditions: [
              { fun: `eq`, const: `software`, param: `__conveyor_api_return_type_error__`, cast: `string` },
              { fun: `eq`, const: `api_bad_answer`, param: `__conveyor_api_return_type_tag__`, cast: `string` },
            ],
          },
          { type: `go`, to_node_id: newNodeF },
        ],
        semaphors: [],
        position: [-1428, -8],
        extra: { modeForm: `collapse`, icon: `` },
        version: 1,
      }),
    );
    expect(responseEscalation.status).toBe(200);
    SchemaValidator.validate(modifySchema, responseApiCall.body);

    const responseDelay = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: newNodeD,

        conv_id: newConv,
        title: `Delay`,
        description: `test`,
        obj_type: 0,
        logics: [],
        semaphors: [{ type: `time`, value: 30, dimension: `sec`, to_node_id: newNodeA }],
        position: [-1428, -100],
        extra: { modeForm: `collapse`, icon: `` },
        version: 1,
      }),
    );
    expect(responseDelay.status).toBe(200);
    SchemaValidator.validate(modifySchema, responseApiCall.body);

    const responseFinal = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: newNodeF,

        conv_id: newConv,
        title: `Final`,
        description: `test`,
        obj_type: 2,
        logics: [],
        semaphors: [],
        position: [-1296, 20],
        extra: { modeForm: `collapse`, icon: `error` },
        version: 1,
      }),
    );
    expect(responseFinal.status).toBe(200);
    SchemaValidator.validate(modifySchema, responseApiCall.body);
  });

  test('should show node after modify api_call logic', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.NODE,
        obj_id: newNodeA,
        company_id,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(showSchema, response.body);
  });

  test('should show node after confirm commit api_call logic', async () => {
    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        conv_id: newConv,
        version: 1,
      }),
    );
    expect(responseCommit.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.NODE,
        obj_id: newNodeA,
        company_id,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].logics[0].type).toBe('api');
    expect(response.body.ops[0].logics[0].extra).toEqual({ key1: 'value1', key2: '2' });
    expect(response.body.ops[0].logics[0].url).toBe('https://test.com');
    SchemaValidator.validate(showSchema, response.body);
  });

  test(`should modify the Condition logic (create/modify)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,

        obj_type: 0,
        description: `test`,
        conv_id: newConv,
        title: `Condition_${Date.now()}`,
        version: 1,
        id: `5`,
      }),
    );
    expect(response.status).toBe(200);
    newNodeC = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const responseFinal = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        obj_type: 2,
        description: `test`,
        conv_id: newConv,
        title: `Final`,
        version: 1,
        id: `6`,
      }),
    );
    expect(responseFinal.status).toBe(200);
    newNodeF = responseFinal.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, responseFinal.body);

    const responseCondition = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: newNodeC,

        conv_id: newConv,
        title: `Condition`,
        description: `test`,
        obj_type: 0,
        logics: [
          {
            to_node_id: newNodeF,
            type: `go_if_const`,
            id: `test`,
            conditions: [{ param: `test`, const: `test`, fun: `eq`, cast: `string` }],
          },
          { type: `go`, to_node_id: `` },
        ],
        semaphors: [],
        position: [1032, 280],
        extra: { modeForm: `expand`, icon: `` },
        version: 1,
      }),
    );
    expect(responseCondition.status).toBe(200);
    SchemaValidator.validate(modifySchema, responseCondition.body);
  });

  test(`should modify Sum logic (create/modify)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        obj_type: 0,
        description: `test`,
        conv_id: newConv,
        title: `Sum_${Date.now()}`,
        version: 1,
        id: `7`,
      }),
    );
    expect(response.status).toBe(200);
    newNodeS = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const responseSum = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: newNodeS,

        conv_id: newConv,
        title: `Sum`,
        description: `test`,
        obj_type: 0,
        logics: [
          { type: `api_sum`, extra: [{ id: `8`, name: `param`, value: `param` }] },
          { type: `go`, to_node_id: newNodeF },
        ],
        semaphors: [],
        position: [716, 188],
        extra: { modeForm: `expand`, icon: `` },
        version: 1,
      }),
    );
    expect(responseSum.status).toBe(200);
    SchemaValidator.validate(modifySchema, responseSum.body);
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
