import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('SyncApi Task (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: string | number;
  let newConv3: string | number;
  let newConv4: string | number;
  let newConv6: string | number;
  let newFolder: string | number;
  let newFolder1: string | number;
  let newFolder2: string | number;
  let newFolder3: string | number;
  let nodeLogic: string;
  let nodeFinal: string;
  let newProject: string | number;
  let newStage: string | number;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: 0,
        obj_type: 0,
        description: 'test',
        status: 'active',
        title: `Folder_${Date.now()}`,
      }),
    );
    newFolder = responseFolder.body.ops[0].obj_id;
    expect(responseFolder.status).toBe(200);

    const responseFolder1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,

        folder_id: newFolder,
        obj_type: 0,
        description: 'test',
        status: 'active',
        title: `Folder1_${Date.now()}`,
      }),
    );
    newFolder1 = responseFolder1.body.ops[0].obj_id;
    expect(responseFolder1.status).toBe(200);

    const responseFolder2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,

        folder_id: newFolder1,
        obj_type: 0,
        description: 'test',
        status: 'active',
        title: `Folder2_${Date.now()}`,
      }),
    );
    newFolder2 = responseFolder2.body.ops[0].obj_id;
    expect(responseFolder2.status).toBe(200);

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `${OBJ_TYPE.CONV}1_${Date.now()}`,
        folder_id: newFolder1,
      }),
    );
    newConv = responseConv.body.ops[0].obj_id;
    expect(responseConv.status).toBe(200);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
    nodeLogic = responseList.body.ops[0].list[1].obj_id;
    nodeFinal = responseList.body.ops[0].list[2].obj_id;
    expect(responseList.status).toBe(200);

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeLogic,
        company_id,
        conv_id: newConv,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'set_param',
            extra: {},
            extra_type: {},
            err_node_id: '',
          },
          { node_title: 'final', to_node_id: nodeFinal, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );

    const responseConv2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `${OBJ_TYPE.CONV}2_${Date.now()}`,
        folder_id: newFolder1,
      }),
    );
    expect(responseConv2.status).toBe(200);

    const responseConv3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `${OBJ_TYPE.CONV}3_${Date.now()}`,
        folder_id: newFolder2,
      }),
    );
    newConv3 = responseConv3.body.ops[0].obj_id;
    expect(responseConv3.status).toBe(200);

    const responseList3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv3,
        company_id,
      }),
    );
    nodeLogic = responseList3.body.ops[0].list[1].obj_id;
    nodeFinal = responseList3.body.ops[0].list[2].obj_id;
    expect(responseList3.status).toBe(200);

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeLogic,
        company_id,

        conv_id: newConv3,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'set_param',
            extra: {},
            extra_type: {},
            err_node_id: '',
          },
          { node_title: 'final', to_node_id: nodeFinal, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: `project${Date.now()}`,
        description: 'test',
        stages: ['production'],
        status: 'active',
      }),
    );
    expect(responseProject.status).toBe(200);
    newProject = responseProject.body.ops[0].obj_id;
    newStage = responseProject.body.ops[0].stages[0];

    const responseFolder3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,

        folder_id: newStage,
        obj_type: 0,
        project_id: newProject,
        stage_id: newStage,
        status: 'active',
        title: `Folder3_${Date.now()}`,
      }),
    );
    newFolder3 = responseFolder3.body.ops[0].obj_id;
    expect(responseFolder3.status).toBe(200);

    const responseConv4 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        title: `${OBJ_TYPE.CONV}4_${Date.now()}`,
        folder_id: newStage,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    newConv4 = responseConv4.body.ops[0].obj_id;
    expect(responseConv4.status).toBe(200);

    const responseList4 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv4,
        company_id,
      }),
    );
    nodeLogic = responseList4.body.ops[0].list[1].obj_id;
    nodeFinal = responseList4.body.ops[0].list[2].obj_id;
    expect(responseList4.status).toBe(200);

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeLogic,
        company_id,

        conv_id: newConv4,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'set_param',
            extra: {},
            extra_type: {},
            err_node_id: '',
          },
          { node_title: 'final', to_node_id: nodeFinal, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );

    const responseConv5 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        title: `${OBJ_TYPE.CONV}5_${Date.now()}`,
        folder_id: newFolder3,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseConv5.status).toBe(200);

    const responseConv6 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        title: `${OBJ_TYPE.CONV}6_${Date.now()}`,
        folder_id: newFolder3,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    newConv6 = responseConv6.body.ops[0].obj_id;
    expect(responseConv6.status).toBe(200);

    const responseList5 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv6,
        company_id,
      }),
    );
    nodeLogic = responseList5.body.ops[0].list[1].obj_id;
    nodeFinal = responseList5.body.ops[0].list[2].obj_id;
    expect(responseList5.status).toBe(200);

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeLogic,
        company_id,

        conv_id: newConv6,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'set_param',
            extra: {},
            extra_type: {},
            err_node_id: '',
          },
          { node_title: 'final', to_node_id: nodeFinal, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
  });

  test('should undeployed process in list folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        sort: 'date',
        order: 'asc',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].undeployed).toEqual(2);
  });

  test('should is_deployed process in list folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder1,
        company_id,
        sort: 'date',
        order: 'asc',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].undeployed).toEqual(1);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ is_deployed: true })]));
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ is_deployed: false })]),
    );
  });

  test('should undeployed process in list project', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECTS,
        company_id,
        sort: 'title',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ undeployed: 2 })]));
  });

  test('should undeployed process in list stage', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].undeployed).toEqual(2);
  });

  test('should undeployed process in list folder project', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStage,
        company_id,
        sort: 'date',
        order: 'asc',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].undeployed).toEqual(1);
  });

  test('should is_deployed process in list folder project', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder3,
        company_id,
        sort: 'date',
        order: 'asc',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ is_deployed: true })]));
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ is_deployed: false })]),
    );
  });

  afterAll(async () => {
    const responseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(responseFolder.status).toBe(200);

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(responseProject.status).toBe(200);
  });
});
