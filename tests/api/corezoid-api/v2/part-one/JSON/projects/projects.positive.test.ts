import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import listProjectSchema from '../../../../schemas/v2/project/listProject.schema.json';

describe('Projects (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newApi: ApiKeyClient;
  let apiKey2: string | number;
  let newProject: string | number;
  let newProject2: string | number;
  let newProject3: string | number;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const CreateKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(200);
    const user = CreateKeyResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newApi = application.getApiKeyClient(newApiKey);
    apiKey2 = +newApiKey.id;

    const responseProject2 = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `BProject_${Date.now()}`,
        short_name: `bproject${Date.now()}`,
      }),
    );
    expect(responseProject2.status).toBe(200);
    newProject2 = responseProject2.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 4000));

    const responseProject = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `AProject_${Date.now()}`,
        short_name: `aproject${Date.now()}`,
        description: `new`,
      }),
    );
    expect(responseProject.status).toBe(200);
    newProject = responseProject.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 4000));

    const responseProject3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `CProject_${Date.now()}`,
        short_name: `cproject${Date.now()}`,
      }),
    );
    expect(responseProject3.status).toBe(200);
    newProject3 = responseProject3.body.ops[0].obj_id;

    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject3,
        company_id,
        obj_to: 'user',
        obj_to_id: apiKey2,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(responseLink.status).toBe(200);
  });

  test(`should list projects with required param`, async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECTS,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[1].obj_id).toBe(newProject);
    expect(response.body.ops[0].list[0].obj_id).toBe(newProject2);
    expect(response.body.ops[0].list[2].obj_id).toBe(newProject3);
    SchemaValidator.validate(listProjectSchema, response.body);
  });

  test.each(['name', 'title', 'owner'])(`should list projects with sort:'%s'`, async sort => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECTS,
        company_id,
        sort,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].obj_id).toBe(newProject);
    expect(response.body.ops[0].list[1].obj_id).toBe(newProject2);
    expect(response.body.ops[0].list[2].obj_id).toBe(newProject3);
    SchemaValidator.validate(listProjectSchema, response.body);
  });

  test(`should list projects with sort:date`, async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECTS,
        company_id,
        sort: 'date',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[1].obj_id).toBe(newProject);
    expect(response.body.ops[0].list[0].obj_id).toBe(newProject2);
    expect(response.body.ops[0].list[2].obj_id).toBe(newProject3);
    SchemaValidator.validate(listProjectSchema, response.body);
  });

  test(`should list projects with order:asc`, async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECTS,
        company_id,
        order: 'asc',
        sort: 'date',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[1].obj_id).toBe(newProject);
    expect(response.body.ops[0].list[0].obj_id).toBe(newProject2);
    expect(response.body.ops[0].list[2].obj_id).toBe(newProject3);
    SchemaValidator.validate(listProjectSchema, response.body);
  });

  test(`should list projects with order:desc`, async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECTS,
        company_id,
        order: 'desc',
        sort: 'date',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[1].obj_id).toBe(newProject);
    expect(response.body.ops[0].list[2].obj_id).toBe(newProject2);
    expect(response.body.ops[0].list[0].obj_id).toBe(newProject3);
    SchemaValidator.validate(listProjectSchema, response.body);
  });

  afterAll(async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);

    const responseProject2 = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject2,
        company_id,
      }),
    );
    expect(responseProject2.status).toBe(200);

    const responseProject3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject3,
        company_id,
      }),
    );
    expect(responseProject3.status).toBe(200);

    const responseKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: apiKey2,
        company_id,

        group_id: 0,
        level: '',
      }),
    );
    expect(responseKey.status).toBe(200);
  });
});
