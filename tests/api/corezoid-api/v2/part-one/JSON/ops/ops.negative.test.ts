import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { arrayTestCases } from '../../../../../negativeCases';
import { createRequestWithObj } from '../../../../../../../utils/corezoidRequest';

describe('Ops (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
  });

  test.each([...arrayTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't create request with invalid ops as '%s'`,
    async input => {
      const response = await api.request(
        createRequestWithObj({
          ops: input,
        }),
      );
      expect(response.status).toBe(400);
      expect(response.body.request_proc).toEqual('ok');
      expect(response.body.ops[0].proc).toBe('error');
      expect(response.body.ops[0].description).toEqual('incorrect body, ops is not found');
    },
  );

  test(`should create request with ops []`, async () => {
    const response = await api.request(
      createRequestWithObj({
        ops: [],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops).toBeArrayOfSize(0);
  });

  test(`shouldn't create request with ops [{}]`, async () => {
    const response = await api.request(
      createRequestWithObj({
        ops: [{}],
      }),
    );
    expect(response.status).toBe(400);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual('Incorrect ops');
  });

  test(`shouldn't create request with invalid ops {}`, async () => {
    const response = await api.request(
      createRequestWithObj({
        ops: {
          conv_id: '2226679',
          type: 'show',
          obj: 'conf',
          obj_type: 'timer',
          company_id: 'afba7c15-246f-4ab2-9329-175ae9aeee89',
        },
      }),
    );
    expect(response.status).toBe(400);
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual('Incorrect ops');
  });
});
