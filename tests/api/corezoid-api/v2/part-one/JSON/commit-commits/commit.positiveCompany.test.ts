import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import confirmCommitSchema from '../../../../schemas/v2/commit/confirmCommit.schema.json';
import showCommitSchema from '../../../../schemas/v2/commit/showCommit.schema.json';
import listCommitsSchema from '../../../../schemas/v2/commit/listCommits.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('Confirm commit (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let newFolder: string | number;
  let newConv: string | number;
  let company_id: any;
  let nodeGo: string;
  let nodeFinal: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: `Project_${Date.now()}`,
        short_name: `projectsn-${Date.now()}`,
        stages: [],
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        project_id: newProject,
        title: `Stage_${Date.now()}`,
        short_name: `stagesn-${Date.now()}`,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;

    const responseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        title: `Folder_${Date.now()}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    newFolder = responseFolder.body.ops[0].obj_id;

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: newFolder,
        title: `Process_${Date.now()}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    newConv = responseConv.body.ops[0].obj_id;

    const responseListConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
      }),
    );
    nodeGo = responseListConv.body.ops[0].list[1].obj_id;
    nodeFinal = responseListConv.body.ops[0].list[2].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeGo,

        conv_id: newConv,
        obj_type: 0,
        logics: [],
        title: 'delay',
        semaphors: [
          {
            type: 'time',
            value: 60,
            dimension: 'sec',
            to_node_id: nodeFinal,
          },
        ],
        version: 1,
      }),
    );
  });

  test('should confirm commit obj_type conv', async () => {
    await new Promise(r => setTimeout(r, 10000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        obj_id: newConv,
        obj_type: 'conv',
        version: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].version).toBe(1);
    expect(response.body.ops[0].obj_id).toBe(newConv);
    SchemaValidator.validate(confirmCommitSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: newConv,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].commits).toBeEmpty;
    expect(
      (responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === nodeGo).semaphors[0],
    ).toStrictEqual({ type: 'time', value: 60, dimension: 'sec', to_node_id: nodeFinal });
  });

  test('should confirm commit obj_type folder', async () => {
    await new Promise(r => setTimeout(r, 10000));
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeGo,

        conv_id: newConv,
        obj_type: 0,
        logics: [],
        semaphors: [
          {
            type: 'time',
            value: 30,
            dimension: 'min',
            to_node_id: nodeFinal,
          },
        ],
        version: 2,
      }),
    );

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        obj_id: newFolder,
        obj_type: 'folder',
        version: 2,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].version).toBe(2);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(confirmCommitSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: newConv,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].commits).toBeEmpty;
    expect(
      (responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === nodeGo).semaphors[0],
    ).toStrictEqual({ type: 'time', value: 30, dimension: 'min', to_node_id: nodeFinal });
  });

  test('should confirm commit obj_type stage', async () => {
    await new Promise(r => setTimeout(r, 10000));
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeGo,

        conv_id: newConv,
        obj_type: 0,
        logics: [],
        semaphors: [
          {
            type: 'time',
            value: 1,
            dimension: 'min',
            to_node_id: nodeFinal,
          },
        ],
        version: 3,
      }),
    );

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        obj_id: newStage,
        obj_type: 'stage',
        version: 3,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].version).toBe(3);
    expect(response.body.ops[0].obj_id).toBe(newStage);
    SchemaValidator.validate(confirmCommitSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: newConv,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].commits).toBeEmpty;
    expect(
      (responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === nodeGo).semaphors[0],
    ).toStrictEqual({ type: 'time', value: 1, dimension: 'min', to_node_id: nodeFinal });
  });

  test('should confirm commit obj_type project', async () => {
    await new Promise(r => setTimeout(r, 5000));
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeGo,

        conv_id: newConv,
        obj_type: 0,
        logics: [],
        semaphors: [
          {
            type: 'time',
            value: 10,
            dimension: 'min',
            to_node_id: nodeFinal,
          },
        ],
        version: 4,
      }),
    );

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        obj_id: newProject,
        obj_type: 'project',
        version: 4,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].version).toBe(4);
    expect(response.body.ops[0].obj_id).toBe(newProject);
    SchemaValidator.validate(confirmCommitSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: newConv,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].commits).toBeEmpty;
    expect(
      (responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === nodeGo).semaphors[0],
    ).toStrictEqual({ type: 'time', value: 10, dimension: 'min', to_node_id: nodeFinal });
  });

  test('should confirm commit with conv_id', async () => {
    await new Promise(r => setTimeout(r, 5000));
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeGo,

        conv_id: newConv,
        obj_type: 0,
        logics: [],
        semaphors: [
          {
            type: 'time',
            value: 15,
            dimension: 'min',
            to_node_id: nodeFinal,
          },
        ],
        version: 5,
      }),
    );

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        conv_id: newConv,
        version: 5,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].version).toBe(5);
    expect(response.body.ops[0].obj_id).toBe(newConv);
    SchemaValidator.validate(confirmCommitSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: newConv,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].commits).toBeEmpty;
    expect(
      (responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === nodeGo).semaphors[0],
    ).toStrictEqual({ type: 'time', value: 15, dimension: 'min', to_node_id: nodeFinal });
  });

  test('should delete commit obj_type conv with conv_id', async () => {
    await new Promise(r => setTimeout(r, 5000));
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeFinal,
        conv_id: newConv,
        version: 5,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeGo,

        conv_id: newConv,
        obj_type: 0,
        logics: [],
        semaphors: [
          {
            type: 'time',
            value: 10,
            dimension: 'min',
          },
        ],
        version: 5,
      }),
    );

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.COMMIT,
        conv_id: newConv,
        version: 5,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].version).toBe(5);
    expect(response.body.ops[0].obj_id).toBe(newConv);
    SchemaValidator.validate(confirmCommitSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: newConv,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].commits).toBeEmpty;
    expect(responseList.body.ops[0].size).toEqual(3);
    expect(responseList.body.ops[0].last_confirmed_version).toEqual(5);
  });

  test('should delete commit obj_type folder', async () => {
    await new Promise(r => setTimeout(r, 5000));
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeFinal,
        conv_id: newConv,
        version: 5,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeGo,

        conv_id: newConv,
        obj_type: 0,
        logics: [],
        semaphors: [
          {
            type: 'time',
            value: 10,
            dimension: 'min',
          },
        ],
        version: 5,
      }),
    );

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        obj_id: newFolder,
        obj_type: 'folder',
        version: 5,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].version).toBe(5);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(confirmCommitSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: newConv,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].commits).toBeEmpty;
    expect(responseList.body.ops[0].size).toEqual(3);
    expect(responseList.body.ops[0].last_confirmed_version).toEqual(5);
  });

  test('should delete commit obj_type stage', async () => {
    await new Promise(r => setTimeout(r, 5000));
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeFinal,
        conv_id: newConv,
        version: 5,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeGo,

        conv_id: newConv,
        obj_type: 0,
        logics: [],
        semaphors: [
          {
            type: 'time',
            value: 10,
            dimension: 'min',
          },
        ],
        version: 5,
      }),
    );

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        obj_id: newStage,
        obj_type: 'stage',
        version: 5,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].version).toBe(5);
    expect(response.body.ops[0].obj_id).toBe(newStage);
    SchemaValidator.validate(confirmCommitSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: newConv,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].commits).toBeEmpty;
    expect(responseList.body.ops[0].size).toEqual(3);
    expect(responseList.body.ops[0].last_confirmed_version).toEqual(5);
  });

  test('should delete commit obj_type project', async () => {
    await new Promise(r => setTimeout(r, 5000));
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeFinal,
        conv_id: newConv,
        version: 6,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeGo,

        conv_id: newConv,
        obj_type: 0,
        logics: [],
        semaphors: [
          {
            type: 'time',
            value: 10,
            dimension: 'min',
          },
        ],
        version: 6,
      }),
    );

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        obj_id: newProject,
        obj_type: 'project',
        version: 6,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].version).toBe(6);
    expect(response.body.ops[0].obj_id).toBe(newProject);
    SchemaValidator.validate(confirmCommitSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: newConv,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].commits).toBeEmpty;
    expect(responseList.body.ops[0].size).toEqual(3);
    expect(responseList.body.ops[0].last_confirmed_version).toEqual(5);
  });

  test('should show commit obj_type conv', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        obj_id: newConv,
        obj_type: 'conv',
        version: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('commit');
    expect(response.body.ops[0].list[0].semaphors[0].type).toBe('time');
    expect(response.body.ops[0].list[0].conv_id).toBe(newConv);
    SchemaValidator.validate(showCommitSchema, response.body);
  });

  test('should show commit with conv_id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        conv_id: newConv,
        version: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('commit');
    expect(response.body.ops[0].list[0].semaphors[0].type).toBe('time');
    expect(response.body.ops[0].list[0].conv_id).toBe(newConv);
    SchemaValidator.validate(showCommitSchema, response.body);
  });

  test('should show commit obj_type folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        obj_id: newFolder,
        obj_type: 'folder',
        version: 2,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('commit');
    expect(response.body.ops[0].list[0].semaphors[0].type).toBe('time');
    expect(response.body.ops[0].list[0].conv_id).toBe(newConv);
    SchemaValidator.validate(showCommitSchema, response.body);
  });

  test('should show commit obj_type stage', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        obj_id: newStage,
        obj_type: 'stage',
        version: 3,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('commit');
    expect(response.body.ops[0].list[0].semaphors[0].type).toBe('time');
    expect(response.body.ops[0].list[0].conv_id).toBe(newConv);
    SchemaValidator.validate(showCommitSchema, response.body);
  });

  test('should show commit obj_type project', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        obj_id: newProject,
        obj_type: 'project',
        version: 4,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('commit');
    expect(response.body.ops[0].list[0].semaphors[0].type).toBe('time');
    expect(response.body.ops[0].list[0].conv_id).toBe(newConv);
    SchemaValidator.validate(showCommitSchema, response.body);
  });

  test('should list commits obj_type conv', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMMITS,
        obj_id: newConv,
        obj_type: 'conv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('commits');
    expect(response.body.ops[0].list[0].version).toEqual(5);
    expect(response.body.ops[0].list[4].version).toEqual(1);
    SchemaValidator.validate(listCommitsSchema, response.body);
  });

  test('should list commits obj_type conv with conv_id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMMITS,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('commits');
    expect(response.body.ops[0].list[0].version).toEqual(5);
    expect(response.body.ops[0].list[4].version).toEqual(1);
    SchemaValidator.validate(listCommitsSchema, response.body);
  });

  test('should list commits obj_type folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMMITS,
        company_id,
        obj_id: newFolder,
        obj_type: 'folder',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('commits');
    expect(response.body.ops[0].list[0].version).toEqual(5);
    expect(response.body.ops[0].list[4].version).toEqual(1);
    SchemaValidator.validate(listCommitsSchema, response.body);
  });

  test('should list commits obj_type stage', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMMITS,
        company_id,
        obj_id: newStage,
        obj_type: 'stage',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('commits');
    expect(response.body.ops[0].list[0].version).toEqual(5);
    expect(response.body.ops[0].list[4].version).toEqual(1);
    SchemaValidator.validate(listCommitsSchema, response.body);
  });

  test('should list commits obj_type project', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMMITS,
        company_id,
        obj_id: newProject,
        obj_type: 'project',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('commits');
    expect(response.body.ops[0].list[0].version).toEqual(5);
    expect(response.body.ops[0].list[4].version).toEqual(1);
    SchemaValidator.validate(listCommitsSchema, response.body);
  });

  test('should confirm commit obj_type project with errors', async () => {
    await new Promise(r => setTimeout(r, 5000));
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeGo,

        conv_id: newConv,
        obj_type: 0,
        logics: [],
        semaphors: [
          {
            type: 'time',
            value: null,
            dimension: 'min',
            to_node_id: null,
          },
        ],
        version: 4,
      }),
    );

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        obj_id: newProject,
        obj_type: 'project',
        version: 4,
      }),
    );
    expect(response.status).toBe(200);
    expect(
      response.body.ops[0].errors[0].destinations[0].destinations[0].destinations[0].destinations[0].errors[0],
    ).toBe(`Key 'value'. 'Wrong timer value null'`);
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
