import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { makeRequestWithRetries, MyApiResponse } from '../../../../../../../utils/requestRetries';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import showBillingSchema from '../../../../schemas/v2/actions-objects/showConveyor_billingScheme.schema.json';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';

describe('Show conveyor billing (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let apiS: ApiKeyClient;
  let apiSuper: ApiKey;
  let idkey: string | number;
  let owner_idKey: string | number;
  let newProject: string | number;
  let newStage: string | number;
  let newConvProject: string | number;
  let newConvCompany: string | number;
  let newConvMyC: string | number;
  let company_id: any;
  let project_short_name: string;
  let stage_short_name: string;
  let start: string | number;
  let end: string | number;
  let start1: string | number;
  let end1: string | number;
  let start2: string | number;
  let end2: string | number;

  beforeAll(async () => {
    apiSuper = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apiSuper);
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    idkey = apikey.id;
    company_id = apikey.companies[0].id;
    project_short_name = `project-${Date.now()}`;
    stage_short_name = `stage-${Date.now()}`;
    start = Math.round(+new Date() / 1000) - 100;
    end = start + 360;
    start1 = Math.round(+new Date() / 1000) - 4200;
    end1 = start1 + 3599;
    start2 = Math.round(+new Date() / 1000) - 1200;
    end2 = start2 + 599;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;

    const responseConvProject = await requestCreateObj(
      api,
      OBJ_TYPE.CONV,
      company_id,
      `Conv_${Date.now()}`,
      newStage,
      'process',
      newProject,
      newStage,
    );
    newConvProject = responseConvProject.body.ops[0].obj_id;

    const responseTask1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: newConvProject,
        action: 'user',
        data: { a: '1' },
        ref: `ref_123`,
      }),
    );
    expect(responseTask1.status).toBe(200);
    expect(responseTask1.body.ops[0].proc).toBe('ok');

    const responseConvMyC = await requestCreateObj(api, OBJ_TYPE.CONV, null, `Conv_${Date.now()}`, 0);
    newConvMyC = responseConvMyC.body.ops[0].obj_id;

    const responseTask2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: newConvMyC,
        action: 'user',
        data: { a: '1' },
        ref: `ref_123`,
      }),
    );
    expect(responseTask2.status).toBe(200);
    expect(responseTask2.body.ops[0].proc).toBe('ok');

    const responseConvCompany = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Conv_${Date.now()}`, 0);
    newConvCompany = responseConvCompany.body.ops[0].obj_id;

    const responseTask3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: newConvCompany,
        action: 'user',
        data: { a: '1' },
        ref: `ref_123`,
      }),
    );
    expect(responseTask3.status).toBe(200);
    expect(responseTask3.body.ops[0].proc).toBe('ok');

    const listCompanyUser = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        company_id,
        obj_id: idkey,
      }),
    );
    expect(listCompanyUser.status).toBe(200);
    expect(listCompanyUser.body.ops[0].proc).toBe('ok');
    owner_idKey = listCompanyUser.body.ops[0].owner_id;
  });

  test.skip(`should show conveyor_billing (conv in project/stage)`, async () => {
    await new Promise(r => setTimeout(r, 20000));
    function checkConditions(response: MyApiResponse): boolean {
      return response.status === 200 && response.body.ops[0].data.some((item: any) => item.conv_id === newConvProject);
    }
    const response = await makeRequestWithRetries(
      async () => {
        return await apiS.request(
          createRequestWithOps({
            type: REQUEST_TYPE.SHOW,
            obj: OBJ_TYPE.CONVEYOR_BILLING,

            start: `${start}`,
            end: `${end}`,
          }),
        );
      },
      checkConditions,
      700,
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('conveyor_billing');
    expect(response.body.ops[0].data).toEqual(
      expect.arrayContaining([expect.objectContaining({ conv_id: newConvProject })]),
    );
    expect((response.body.ops[0].data as Array<any>).find(item => item.conv_id === newConvProject).tacts).toEqual(3);
    expect((response.body.ops[0].data as Array<any>).find(item => item.conv_id === newConvProject).owner_type).toEqual(
      'key',
    );
    expect((response.body.ops[0].data as Array<any>).find(item => item.conv_id === newConvProject).owner_id).toEqual(
      owner_idKey,
    );
    expect(
      (response.body.ops[0].data as Array<any>).find(item => item.conv_id === newConvProject).owner_account_id,
    ).toEqual(owner_idKey);
    expect((response.body.ops[0].data as Array<any>).find(item => item.conv_id === newConvProject).company_id).toEqual(
      company_id,
    );
    SchemaValidator.validate(showBillingSchema, response.body);
  });

  test.skip(`should show conveyor_billing (conv in my corezoid)`, async () => {
    await new Promise(r => setTimeout(r, 10000));
    function checkConditions(response: MyApiResponse): boolean {
      return response.status === 200 && response.body.ops[0].data.some((item: any) => item.conv_id === newConvMyC);
    }
    const response = await makeRequestWithRetries(
      async () => {
        return await apiS.request(
          createRequestWithOps({
            type: REQUEST_TYPE.SHOW,
            obj: OBJ_TYPE.CONVEYOR_BILLING,

            start: `${start}`,
            end: `${end}`,
          }),
        );
      },
      checkConditions,
      700,
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('conveyor_billing');
    expect(response.body.ops[0].data).toEqual(
      expect.arrayContaining([expect.objectContaining({ conv_id: newConvMyC })]),
    );
    expect((response.body.ops[0].data as Array<any>).find(item => item.conv_id === newConvMyC).tacts).toEqual(3);
    expect((response.body.ops[0].data as Array<any>).find(item => item.conv_id === newConvMyC).owner_type).toEqual(
      'key',
    );
    expect((response.body.ops[0].data as Array<any>).find(item => item.conv_id === newConvMyC).owner_id).toEqual(
      owner_idKey,
    );
    expect(
      (response.body.ops[0].data as Array<any>).find(item => item.conv_id === newConvMyC).owner_account_id,
    ).toEqual(owner_idKey);
    expect((response.body.ops[0].data as Array<any>).find(item => item.conv_id === newConvMyC).company_id).toEqual(
      null,
    );
    SchemaValidator.validate(showBillingSchema, response.body);
  });

  test.skip(`should show conveyor_billing (conv in company)`, async () => {
    await new Promise(r => setTimeout(r, 10000));
    function checkConditions(response: MyApiResponse): boolean {
      return response.status === 200 && response.body.ops[0].data.some((item: any) => item.conv_id === newConvCompany);
    }
    const response = await makeRequestWithRetries(
      async () => {
        return await apiS.request(
          createRequestWithOps({
            type: REQUEST_TYPE.SHOW,
            obj: OBJ_TYPE.CONVEYOR_BILLING,

            start: `${start}`,
            end: `${end}`,
          }),
        );
      },
      checkConditions,
      700,
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('conveyor_billing');
    expect(response.body.ops[0].data).toEqual(
      expect.arrayContaining([expect.objectContaining({ conv_id: newConvCompany })]),
    );
    expect((response.body.ops[0].data as Array<any>).find(item => item.conv_id === newConvCompany).tacts).toEqual(3);
    expect((response.body.ops[0].data as Array<any>).find(item => item.conv_id === newConvCompany).owner_type).toEqual(
      'key',
    );
    expect((response.body.ops[0].data as Array<any>).find(item => item.conv_id === newConvCompany).owner_id).toEqual(
      owner_idKey,
    );
    expect(
      (response.body.ops[0].data as Array<any>).find(item => item.conv_id === newConvCompany).owner_account_id,
    ).toEqual(owner_idKey);
    expect((response.body.ops[0].data as Array<any>).find(item => item.conv_id === newConvCompany).company_id).toEqual(
      company_id,
    );
    SchemaValidator.validate(showBillingSchema, response.body);
  });

  test(`Show CONVEYOR_BILLING (3599sec)`, async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONVEYOR_BILLING,

        start: `${start1}`,
        end: `${end1}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].obj).toBe('conveyor_billing');
    expect(response.body.ops[0].data[0].conv_id).toBeNumber;
  });

  test(`Show CONVEYOR_BILLING (599sec)`, async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONVEYOR_BILLING,

        start: `${start2}`,
        end: `${end2}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].obj).toBe('conveyor_billing');
    expect(response.body.ops[0].data[0].conv_id).toBeNumber;
  });

  afterAll(async () => {
    const responseConvMyCorezoid = await requestDeleteObj(api, OBJ_TYPE.CONV, newConvMyC, null);
    expect(responseConvMyCorezoid.status).toBe(200);

    const responseConvCompany = await requestDeleteObj(api, OBJ_TYPE.CONV, newConvCompany, company_id);
    expect(responseConvCompany.status).toBe(200);

    const responseProject = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(responseProject.status).toBe(200);
  });
});
