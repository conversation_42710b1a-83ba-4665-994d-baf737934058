import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { integerTestCases, undefinedTestCase, maxLength } from '../../../../../negativeCases';
import { debug } from '../../../../../../../support/utils/logger';

describe('Show conveyor billing (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let apiS: ApiKeyClient;
  let apiSuper: ApiKey;
  let valuesToSkip: any;
  let start: string | number;
  let end: string | number;
  let start1: string | number;
  let end1: string | number;

  beforeAll(async () => {
    apiSuper = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apiSuper);
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    start = Math.round(+new Date() / 1000) - 100;
    end = start + 360;
    start1 = Math.round(+new Date() / 1000) - 4200;
    end1 = start1 + 3602;
  });

  valuesToSkip = [0];

  test.each(
    [...integerTestCases, ...undefinedTestCase, ...maxLength]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't show CONVEYOR_BILLING with invalid start '%s'`, async (input, errors) => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONVEYOR_BILLING,

        start: input,
        end: `${end}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0];

  test.each(
    [...integerTestCases, ...undefinedTestCase, ...maxLength]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't show CONVEYOR_BILLING with invalid end '%s'`, async (input, errors) => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONVEYOR_BILLING,

        start: `${start}`,
        end: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test(`shouldn't show CONVEYOR_BILLING (User has no rights)'%s'`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONVEYOR_BILLING,

        start: `${start}`,
        end: `${end}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual('User has no rights');
  });

  test(`shouldn't show CONVEYOR_BILLING (less then now minus 600sec)'%s'`, async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONVEYOR_BILLING,

        start: `${start}`,
        end: `${end}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual('Parametr start end must be less then now minus 600sec');
  });

  test.skip(`shouldn't show CONVEYOR_BILLING (max interval 3600)'%s'`, async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONVEYOR_BILLING,

        start: `${start1}`,
        end: `${end1}`,
      }),
    );
    expect(response.status).toBe(200);
    debug(JSON.stringify(response.body));
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual('Parametr start end must be less then now minus 600sec');
  });
});
