import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('Path_to_folder (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: number;
  let folder_id: number;
  let folder_id_2: number;
  let company_id: any;
  let short_name: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    short_name = `project${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name,
        description: `new`,
        stages: [
          { title: 'develop', immutable: false },
          { title: 'production', immutable: true },
        ],
      }),
    );
    expect(responseProject.status).toBe(200);
    newProject = responseProject.body.ops[0].obj_id;
    newStage = responseProject.body.ops[0].stages[0];

    const responseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,

        title: `path_to_folder_${Date.now()}`,
        description: `folder`,
      }),
    );
    expect(responseFolder.status).toBe(200);
    folder_id = responseFolder.body.ops[0].obj_id;

    const responseFolder2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,

        title: `path_to_folder_${Date.now()}`,
        description: `folder`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseFolder2.status).toBe(200);
    folder_id_2 = responseFolder2.body.ops[0].obj_id;
  });

  test(`should list path_to_folder root folder with required param (my corezoid)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PATH_TO_FOLDER,
        obj_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].title).toBe('My corezoid');
  });

  test(`should list path_to_folder root folder with company_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PATH_TO_FOLDER,
        obj_id: 0,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].company_id).toBe(company_id);
  });

  test(`should list path_to_folder project with project_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PATH_TO_FOLDER,
        obj_id: newProject,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].company_id).toBe(company_id);
    expect(response.body.ops[0].list[1].obj_id).toBe(newProject);
  });

  test(`should list path_to_folder project with project_short_name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PATH_TO_FOLDER,
        obj_id: newProject,
        company_id,
        project_short_name: short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].company_id).toBe(company_id);
    expect(response.body.ops[0].list[1].short_name).toBe(short_name);
  });

  test(`should list path_to_folder new folder with obj_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PATH_TO_FOLDER,
        obj_id: folder_id,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].company_id).toBe(company_id);
    expect(response.body.ops[0].list[1].obj_id).toBe(folder_id);
  });

  test(`should list path_to_folder new folder in stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PATH_TO_FOLDER,
        obj_id: folder_id_2,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].company_id).toBe(company_id);
    expect(response.body.ops[0].list[1].obj_id).toBe(newProject);
    expect(response.body.ops[0].list[2].obj_id).toBe(newStage);
    expect(response.body.ops[0].list[3].obj_id).toBe(folder_id_2);
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);

    const responseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folder_id,
        company_id,
      }),
    );
    expect(responseFolder.status).toBe(200);
  });
});
