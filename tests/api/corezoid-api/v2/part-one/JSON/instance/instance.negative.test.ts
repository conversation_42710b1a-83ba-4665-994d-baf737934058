import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import { requestDeleteObj } from '../../../../../../../application/api/ApiObj';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import faker from 'faker';
import {
  integerTestCases,
  stringTestCases,
  stringNotValidTestCases,
  undefinedTestCase,
  companyTestCases,
  arrayTestCases,
  securityTestCases,
  maxLength,
  minLength,
  boolTestCases,
} from '../../../../../negativeCases';

describe('Instance (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newInstance: string | number;
  let newKey: string | number;
  let company_id: any;
  let valuesToSkip: any;
  let db_host: string;
  let db_name: string;
  let db_user: string;
  let db_pass: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    db_host = '**********';
    db_name = 'test';
    db_user = 'test_user';
    db_pass = '3sha1cheeH4uoziej1u';

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        instance_type: `db_call`,
        folder_id: 0,
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    newInstance = response.body.ops[0].obj_id;

    const responseKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: `Key_${Date.now()}`,
        logins: [{ type: 'api' }],
      }),
    );
    expect(responseKey.status).toBe(200);
    newKey = responseKey.body.ops[0].users[0].obj_id;
  });

  test.each(
    [
      ...stringTestCases,
      ...undefinedTestCase,
      ...stringNotValidTestCases,
      ...securityTestCases,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't check instance with invalid instance_type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: input,
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = ['te'];

  test.each(
    [...companyTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't check instance with invalid company_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.INSTANCE,
        company_id: input,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [-1, 0];

  test.each(
    [...integerTestCases, ...stringNotValidTestCases, ...securityTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't check instance with invalid company_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.INSTANCE,

        folder_id: input,
        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...minLength, ...maxLength, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't check instance with invalid title '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.INSTANCE,
        instance_type: 'db_call',
        title: input,
        description: `test`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [null];

  test.each(
    [...stringTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't check instance with invalid description '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: input,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([
    [faker.random.alphaNumeric(2001), `Value is not valid. Value's byte_size is more than maximum allowed: 2000`],
  ])(`shouldn't check instance with description'%s'`, async (description, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: `db_call`,
        title: `Instance_${Date.now()}`,
        description,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't check instance with invalid driver '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CHECK,
          obj: OBJ_TYPE.INSTANCE,

          instance_type: 'db_call',
          title: `Instance_${Date.now()}`,
          description: `test`,
          data: {
            driver: input,
            host: db_host,
            port: `5432`,
            username: db_user,
            password: db_pass,
            ssl: false,
            database: db_name,
            timeoutMs: 30000,
          },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [
      ...stringTestCases,
      ...minLength,
      ...maxLength,
      ...securityTestCases,
      ...stringNotValidTestCases,
      ...undefinedTestCase,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't check instance with invalid host '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: input,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [1];

  test.each(
    [...stringTestCases, ...maxLength, ...securityTestCases, ...stringNotValidTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't check instance with invalid port '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: db_host,
          port: input,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [
      ...stringTestCases,
      ...minLength,
      ...maxLength,
      ...securityTestCases,
      ...undefinedTestCase,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't check instance with invalid username '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: db_host,
          port: `5432`,
          username: input,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [
      ...stringTestCases,
      ...minLength,
      ...maxLength,
      ...securityTestCases,
      ...undefinedTestCase,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't check instance with invalid password '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: db_host,
          port: `5432`,
          username: db_user,
          password: input,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [
      ...stringTestCases,
      ...undefinedTestCase,
      ...minLength,
      ...maxLength,
      ...stringNotValidTestCases,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't check instance with invalid ssl '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: input,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [
      ...stringTestCases,
      ...undefinedTestCase,
      ...minLength,
      ...maxLength,
      ...stringNotValidTestCases,
      ...securityTestCases,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't check instance with invalid database '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: true,
          database: input,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [
      ...stringTestCases,
      ...undefinedTestCase,
      ...minLength,
      ...maxLength,
      ...stringNotValidTestCases,
      ...securityTestCases,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't check instance with invalid timeoutMs '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: true,
          database: db_name,
          timeoutMs: input,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.skip(`shouldn't check instance with invalid timeoutMs 40000000000000000000'%s'`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: true,
          database: db_name,
          timeoutMs: 40000000000000000000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(
      'json: cannot unmarshal number 40000000000000000000 into Go struct field Credentials.credentials.timeout_ms of type int',
    );
  });

  test.each(
    [
      ...stringTestCases,
      ...undefinedTestCase,
      ...stringNotValidTestCases,
      ...securityTestCases,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create instance with invalid instance_type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: input,
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = ['te'];

  test.each(
    [...companyTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create instance with invalid company_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id: input,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [-1, 0];

  test.each(
    [...integerTestCases, ...stringNotValidTestCases, ...securityTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create instance with invalid folder_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,

        folder_id: input,
        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...maxLength, ...minLength, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create instance with invalid title '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        instance_type: 'db_call',
        title: input,
        description: `test`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [null];

  test.each(
    [...stringTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create instance with invalid description '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: input,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([
    [faker.random.alphaNumeric(2001), `Value is not valid. Value's byte_size is more than maximum allowed: 2000`],
  ])(`shouldn't create instance with description'%s'`, async (description, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: `db_call`,
        title: `Instance_${Date.now()}`,
        description,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't create instance with invalid driver '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.INSTANCE,

          instance_type: 'db_call',
          title: `Instance_${Date.now()}`,
          description: `test`,
          data: {
            driver: input,
            host: db_host,
            port: `5432`,
            username: db_user,
            password: db_pass,
            ssl: false,
            database: db_name,
            timeoutMs: 30000,
          },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...minLength, ...maxLength, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create instance with invalid host '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: input,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [1];

  test.each(
    [...stringTestCases, ...maxLength, ...securityTestCases, ...stringNotValidTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create instance with invalid port '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: db_host,
          port: input,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...minLength, ...maxLength, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create instance with invalid username '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: db_host,
          port: `5432`,
          username: input,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...minLength, ...maxLength, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create instance with invalid password '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: db_host,
          port: `5432`,
          username: db_user,
          password: input,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [
      ...boolTestCases,
      ...undefinedTestCase,
      ...minLength,
      ...maxLength,
      ...stringNotValidTestCases,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create instance with invalid ssl '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: input,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...minLength, ...maxLength].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create instance with invalid database '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: true,
          database: input,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [1, 0, -1];

  test.each(
    [
      ...stringTestCases,
      ...maxLength,
      ...minLength,
      ...securityTestCases,
      ...stringNotValidTestCases,
      ...undefinedTestCase,
    ]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create instance with invalid timeoutMs '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: true,
          database: db_name,
          timeoutMs: input,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...integerTestCases, ...undefinedTestCase, ...securityTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't mofify instance with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: input,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [
      ...stringTestCases,
      ...undefinedTestCase,
      ...stringNotValidTestCases,
      ...securityTestCases,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't mofify instance with invalid instance_type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,

        instance_type: input,
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = ['te'];

  test.each(
    [...companyTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify instance with invalid company_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,
        company_id: input,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...minLength, ...maxLength, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify instance with invalid title '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,
        instance_type: 'db_call',
        title: input,
        description: `test`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [null];

  test.each(
    [...stringTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify instance with invalid description '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: input,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([
    [faker.random.alphaNumeric(2001), `Value is not valid. Value's byte_size is more than maximum allowed: 2000`],
  ])(`shouldn't modify instance with description'%s'`, async (description, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,

        instance_type: `db_call`,
        title: `Instance_${Date.now()}`,
        description,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify instance with invalid driver '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.INSTANCE,
          obj_id: newInstance,

          instance_type: 'db_call',
          title: `Instance_${Date.now()}`,
          description: `test`,
          data: {
            driver: input,
            host: db_host,
            port: `5432`,
            username: db_user,
            password: db_pass,
            ssl: false,
            database: db_name,
            timeoutMs: 30000,
          },
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...minLength, ...maxLength, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify instance with invalid host '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: input,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [1];

  test.each(
    [...stringTestCases, ...maxLength, ...securityTestCases, ...stringNotValidTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify instance with invalid port '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: db_host,
          port: input,
          username: db_user,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...minLength, ...maxLength, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify instance with invalid username '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: db_host,
          port: `5432`,
          username: input,
          password: db_pass,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...minLength, ...maxLength, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify instance with invalid password '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: db_host,
          port: `5432`,
          username: db_user,
          password: input,
          ssl: false,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [
      ...boolTestCases,
      ...undefinedTestCase,
      ...minLength,
      ...maxLength,
      ...stringNotValidTestCases,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify instance with invalid ssl '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: input,
          database: db_name,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...minLength, ...maxLength].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify instance with invalid database '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: true,
          database: input,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [1, 0, -1];

  test.each(
    [
      ...stringTestCases,
      ...maxLength,
      ...minLength,
      ...securityTestCases,
      ...stringNotValidTestCases,
      ...undefinedTestCase,
    ]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify instance with invalid timeoutMs '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,

        instance_type: 'db_call',
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: 'postgres',
          host: db_host,
          port: `5432`,
          username: db_user,
          password: db_pass,
          ssl: true,
          database: db_name,
          timeoutMs: input,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...integerTestCases, ...undefinedTestCase, ...securityTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't show instance with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: input,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't show instance with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.INSTANCE,
          obj_id: newInstance,
          company_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...integerTestCases, ...undefinedTestCase, ...securityTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't favorite instance with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: input,
        company_id,
        favorite: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't favorite instance with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.FAVORITE,
          obj: OBJ_TYPE.INSTANCE,
          obj_id: newInstance,
          company_id: input,
          favorite: true,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...boolTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't favorite instance with invalid favorite '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.FAVORITE,
          obj: OBJ_TYPE.INSTANCE,
          obj_id: newInstance,
          company_id,
          favorite: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...integerTestCases, ...undefinedTestCase, ...securityTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't list instance with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: input,
        company_id,
        list_obj: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't list instance with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.INSTANCE,
          obj_id: newInstance,
          company_id: input,
          list_obj: 'group',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't list instance with invalid list_obj '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,
        company_id,
        list_obj: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't link instance with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.INSTANCE,
          obj_id: input,
          company_id,

          obj_to: 'user',
          obj_to_id: newKey,
          privs: [
            { type: 'create', list_obj: ['all'] },
            { type: 'view', list_obj: ['all'] },
          ],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't link instance with invalid obj_to '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,
        company_id,

        obj_to: input,
        obj_to_id: newKey,
        privs: [
          { type: 'create', list_obj: ['all'] },
          { type: 'view', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't link instance with invalid obj_to_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.INSTANCE,
          obj_id: newInstance,
          company_id,

          obj_to: 'user',
          obj_to_id: input,
          privs: [
            { type: 'create', list_obj: ['all'] },
            { type: 'view', list_obj: ['all'] },
          ],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...arrayTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't link instance with invalid privs '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.INSTANCE,
          obj_id: newInstance,
          company_id,

          obj_to: 'user',
          obj_to_id: newKey,
          privs: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([[undefined, `already not belong to user`]])(
    `shouldn't link instance without privs'%s'`,
    async (input, reason) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.INSTANCE,
          obj_id: newInstance,
          company_id,

          obj_to: 'user',
          obj_to_id: newKey,
          privs: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toContain(reason);
    },
  );

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't link instance with invalid privs.type '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.INSTANCE,
          obj_id: newInstance,
          company_id,

          obj_to: 'user',
          obj_to_id: newKey,
          privs: [
            { type: input, list_obj: ['all'] },
            { type: 'view', list_obj: ['all'] },
          ],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...arrayTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't link instance with invalid privs.list_obj '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.INSTANCE,
          obj_id: newInstance,
          company_id,

          obj_to: 'user',
          obj_to_id: newKey,
          privs: [
            { type: 'create', list_obj: input },
            { type: 'view', list_obj: ['all'] },
          ],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...integerTestCases, ...undefinedTestCase, ...securityTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't delete instance with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: input,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't delete instance with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.INSTANCE,
          obj_id: newInstance,
          company_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...integerTestCases, ...undefinedTestCase, ...securityTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't restore instance with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: input,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't restore instance with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.RESTORE,
          obj: OBJ_TYPE.INSTANCE,
          obj_id: newInstance,
          company_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...integerTestCases, ...undefinedTestCase, ...securityTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't destroy instance with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: input,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't destroy instance with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DESTROY,
          obj: OBJ_TYPE.INSTANCE,
          obj_id: newInstance,
          company_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  afterAll(async () => {
    const responseDelUser = await requestDeleteObj(api, OBJ_TYPE.USER, newKey, company_id);
    expect(responseDelUser.status).toBe(200);

    const responseDelInstance = await requestDeleteObj(api, OBJ_TYPE.INSTANCE, newInstance, company_id);
    expect(responseDelInstance.status).toBe(200);
  });
});
