import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createSchema from '../../../../schemas/v2/DB-call/createInstance.schema.json';
import deleteSchema from '../../../../schemas/v2/DB-call/deleteInstance.schema.json';
import modifySchema from '../../../../schemas/v2/DB-call/modifyInstance.schema.json';
import checkSchema from '../../../../schemas/v2/DB-call/checkInstance.schema.json';
import restoreSchema from '../../../../schemas/v2/DB-call/restoreInstance.schema.json';
import favoriteSchema from '../../../../schemas/v2/DB-call/favoriteInstance.schema.json';
import showSchema from '../../../../schemas/v2/DB-call/showInstance.schema.json';
import listSchema from '../../../../schemas/v2/DB-call/listInstance.schema.json';
import linkSchema from '../../../../schemas/v2/DB-call/linkInstance.schema.json';

describe('Instance (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newFolder: string | number;
  let newProject: string | number;
  let newStage: string | number;
  let newGroup: string | number;
  let newKey: string | number;
  let newInstance: string | number;
  let newInstance2: string | number;
  let newInstance3: string | number;
  let company_id: any;
  let db_host: string;
  let db_nameP: string;
  let db_userP: string;
  let db_passP: string;
  let db_nameMyS: string;
  let db_userMyS: string;
  let db_passMyS: string;
  let db_nameMsS: string;
  let db_userMsS: string;
  let db_passMsS: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    db_host = '**********';
    db_nameP = 'test';
    db_userP = 'test_user';
    db_passP = '3sha1cheeH4uoziej1u';
    db_nameMyS = 'COR6783';
    db_userMyS = 'root';
    db_passMyS = 'she2kojiJo';
    db_nameMsS = 'SUPPORT1230';
    db_userMsS = 'sa';
    db_passMsS = 'wah3Jushai';

    const responseFolder = await requestCreateObj(api, OBJ_TYPE.FOLDER, company_id, `Folder_${Date.now()}`, 0);
    newFolder = responseFolder.body.ops[0].obj_id;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: `Project_${Date.now()}`,
        short_name: `name-${Date.now()}`,
      }),
    );
    expect(responseProject.status).toBe(200);
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        title: `Stage_${Date.now()}`,
        short_name: `namestage-${Date.now()}`,
        project_id: newProject,
      }),
    );
    expect(responseStage.status).toBe(200);
    newStage = responseStage.body.ops[0].obj_id;

    const responseGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        title: `Group_${Date.now()}`,
        obj_type: 'admins',
      }),
    );
    expect(responseGroup.status).toBe(200);
    newGroup = responseGroup.body.ops[0].obj_id;

    const responseKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: `Key_${Date.now()}`,
        logins: [{ type: 'api' }],
      }),
    );
    expect(responseKey.status).toBe(200);
    newKey = responseKey.body.ops[0].users[0].obj_id;
  });

  test(`should check postgres instance`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: `db_call`,
        title: `Instance_${Date.now()}`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_userP,
          password: db_passP,
          ssl: false,
          database: db_nameP,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(checkSchema, response.body);
  });

  test(`should create postgres instance`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        instance_type: `db_call`,
        folder_id: newFolder,
        title: `Instance_${Date.now()}`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_userP,
          password: db_passP,
          ssl: false,
          database: db_nameP,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('instance');
    newInstance = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`should check mysql instance`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: `db_call`,
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: `mysql`,
          host: db_host,
          port: `3306`,
          username: db_userMyS,
          password: db_passMyS,
          ssl: false,
          database: db_nameMyS,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(checkSchema, response.body);
  });

  test(`should create mysql instance`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        instance_type: `db_call`,
        folder_id: newFolder,
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: `mysql`,
          host: db_host,
          port: `3306`,
          username: db_userMyS,
          password: db_passMyS,
          ssl: false,
          database: db_nameMyS,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('instance');
    newInstance2 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`should check mssql instance`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.INSTANCE,

        instance_type: `db_call`,
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: `mssql`,
          host: db_host,
          port: `1434`,
          username: db_userMsS,
          password: db_passMsS,
          ssl: false,
          database: db_nameMsS,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(checkSchema, response.body);
  });

  test(`should create mssql instance`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        instance_type: `db_call`,
        folder_id: newFolder,
        title: `Instance_${Date.now()}`,
        description: `test`,
        data: {
          driver: `mssql`,
          host: db_host,
          port: `1433`,
          username: db_userMsS,
          password: db_passMsS,
          ssl: false,
          database: db_nameMsS,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('instance');
    newInstance3 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`should list instance after create`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstance })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstance2 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstance3 })]),
    );
  });

  test(`should modify instance mssql->postgres`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance3,
        company_id,

        instance_type: `db_call`,
        obj_type: 0,
        status: `active`,
        title: `Modify`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_userP,
          password: db_passP,
          ssl: true,
          database: db_nameP,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('instance');
    SchemaValidator.validate(modifySchema, response.body);
  });

  test(`should show instance after modify`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance3,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('instance');
    expect(response.body.ops[0].obj_id).toBe(newInstance3);
    expect(response.body.ops[0].title).toBe(`Modify`);
    expect(response.body.ops[0].data.driver).toBe(`postgres`);
    expect(response.body.ops[0].data.port).toBe(5432);
    expect(response.body.ops[0].data.username).toBe(db_userP);
    expect(response.body.ops[0].data.password).toBe(db_passP);
    expect(response.body.ops[0].data.timeoutMs).toBe(30000);
    expect(response.body.ops[0].data.ssl).toBe(true);
    SchemaValidator.validate(showSchema, response.body);
  });

  test(`should favorite "true" instance`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance3,
        company_id,
        favorite: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('instance');
    SchemaValidator.validate(favoriteSchema, response.body);
  });

  test(`should link instance to group`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance3,
        company_id,

        obj_to: 'group',
        obj_to_id: newGroup,
        privs: [
          { type: 'create', list_obj: ['all'] },
          { type: 'view', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toBe('instance');
    expect(response.body.ops[0].action_type).toBe('link');
    SchemaValidator.validate(linkSchema, response.body);
  });

  test(`should list instance`, async () => {
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance3,
        company_id,
        list_obj: 'group',
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].obj).toBe('instance');
    expect(responseList.body.ops[0].favorite).toBe(true);
    expect(responseList.body.ops[0].list[0].obj_id).toBe(newGroup);
    SchemaValidator.validate(listSchema, responseList.body);
  });

  test(`should link instance to key`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance3,
        company_id,

        obj_to: 'user',
        obj_to_id: newKey,
        privs: [
          { type: 'create', list_obj: ['all'] },
          { type: 'view', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toBe('instance');
    expect(response.body.ops[0].action_type).toBe('link');
    SchemaValidator.validate(linkSchema, response.body);
  });

  test(`should create postgres instance in project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        instance_type: `db_call`,
        folder_id: newStage,
        title: `Instance_${Date.now()}`,
        project_id: newProject,
        stage_id: newStage,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_userP,
          password: db_passP,
          ssl: false,
          database: db_nameP,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('instance');
    newInstance = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`should list instance after create in project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStage,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstance })]),
    );
  });

  test(`should modify instance mssql->postgres in project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,
        company_id,

        project_id: newProject,
        stage_id: newStage,
        instance_type: `db_call`,
        obj_type: 0,
        status: `active`,
        title: `Modify`,
        data: {
          driver: `postgres`,
          host: db_host,
          port: `5432`,
          username: db_userP,
          password: db_passP,
          ssl: true,
          database: db_nameP,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('instance');
    SchemaValidator.validate(modifySchema, response.body);
  });

  test(`should show instance after modify in project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,
        company_id,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('instance');
    expect(response.body.ops[0].obj_id).toBe(newInstance);
    expect(response.body.ops[0].title).toBe(`Modify`);
    expect(response.body.ops[0].data.driver).toBe(`postgres`);
    expect(response.body.ops[0].data.port).toBe(5432);
    expect(response.body.ops[0].data.username).toBe(db_userP);
    expect(response.body.ops[0].data.password).toBe(db_passP);
    expect(response.body.ops[0].data.timeoutMs).toBe(30000);
    expect(response.body.ops[0].data.ssl).toBe(true);
    SchemaValidator.validate(showSchema, response.body);
  });

  test(`should favorite "true" instance in project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,
        company_id,
        favorite: true,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('instance');
    SchemaValidator.validate(favoriteSchema, response.body);
  });

  test(`should link instance to key in project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,
        company_id,

        project_id: newProject,
        stage_id: newStage,
        obj_to: 'user',
        obj_to_id: newKey,
        privs: [
          { type: 'create', list_obj: ['all'] },
          { type: 'view', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toBe('instance');
    expect(response.body.ops[0].action_type).toBe('link');
    SchemaValidator.validate(linkSchema, response.body);
  });

  test(`should list instance in project`, async () => {
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,
        company_id,
        list_obj: 'group',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].obj).toBe('instance');
    expect(responseList.body.ops[0].favorite).toBe(true);
    expect(responseList.body.ops[0].list[0].obj_id).toBe(newKey);
    SchemaValidator.validate(listSchema, responseList.body);
  });

  test(`should delete instance in project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance,
        company_id,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe(`instance`);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test(`should delete instance`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance2,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe(`instance`);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test(`should find instance in list_folder filter_deleted`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
        filter: 'deleted',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_type: `instance` })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstance2 })]),
    );
  });

  test(`should not find instance in list_folder`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newInstance2)).toBeEmpty();
  });

  test(`should restore instance`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance2,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(restoreSchema, response.body);
    expect(response.body.ops[0].obj_id).toBe(newInstance2);
  });

  test(`should find restore instance in list_folder`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_type: `instance` })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstance2 })]),
    );
  });

  test(`should destroy instance`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance2,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe(`instance`);

    const responseDestroy = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: newInstance2,
        company_id,
      }),
    );
    expect(responseDestroy.status).toBe(200);
    expect(responseDestroy.body.ops[0].obj).toBe(`instance`);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  afterAll(async () => {
    const responseDelFolder = await requestDeleteObj(api, OBJ_TYPE.FOLDER, newFolder, company_id);
    expect(responseDelFolder.status).toBe(200);

    const responseDelProject = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(responseDelProject.status).toBe(200);

    const responseDelGroup = await requestDeleteObj(api, OBJ_TYPE.GROUP, newGroup, company_id);
    expect(responseDelGroup.status).toBe(200);

    const responseDelUser = await requestDeleteObj(api, OBJ_TYPE.USER, newKey, company_id);
    expect(responseDelUser.status).toBe(200);
  });
});
