import { <PERSON>pi<PERSON>eyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import loadSchema from '../../../../schemas/v2/api_code/loadApi_code.schema copy.json';
import getSchema from '../../../../schemas/v2/api_code/getApi_code.schema.json';
import compileSchema from '../../../../schemas/v2/api_code/compileApi_code.schema.json';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  PROC_STATUS,
  RESP_STATUS,
  generateName,
} from '../../../../../../../utils/corezoidRequest';
import { LANG, METHOD_SHOW, ShowApiCodeResponse } from '../../../../../../../application/api/obj_types/api_code';
import {
  requestConfirm,
  requestDeleteObj,
  requestCreateObjNew,
  requestListConv,
} from '../../../../../../../application/api/ApiObj';
import { generateCode, codeRating } from '../../../../../../../application/api/ApiGPTRequest';

describe('Api_code (positive)', (): void => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let conv_id: string | number;
  let company_id: any;
  let process_node_ID: string;
  let final_node_ID: string;
  let conv_type: string;
  let strCodeErl: string;
  let strCodeJS: string;
  let rating: any;

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;
      conv_type = 'process';

      const responseConv = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, generateName(OBJ_TYPE.CONV), {
        conv_type,
      });
      conv_id = responseConv.body.ops[0].obj_id;

      const responseList = await requestListConv(api, conv_id, company_id);
      expect(responseList.status).toBe(RESP_STATUS.OK);
      process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === conv_type).obj_id;
      final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

      const responseCreateCode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id,
          title: conv_type,
          obj_type: 0,
          logics: [
            { type: 'api_code', err_node_id: '', lang: 'js', src: 'var b = *********;data.a = b;' },
            { to_node_id: final_node_ID, format: 'json', type: 'go', node_title: 'final' },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseCreateCode.status).toBe(RESP_STATUS.OK);

      strCodeErl = await generateCode('Erlang');
      strCodeJS = await generateCode('JavaScript');
    },
  );

  const testCasePositiv: Array<{
    description: string;
    type: REQUEST_TYPE;
    schema: any;
  }> = [
    {
      description: 'should compile',
      type: REQUEST_TYPE.COMPILE,
      schema: compileSchema,
    },
    {
      description: 'should load',
      type: REQUEST_TYPE.LOAD,
      schema: loadSchema,
    },
  ];
  describe.each(testCasePositiv)('$description', ({ type, schema }): void => {
    test('api_code', async (): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type,
          obj: OBJ_TYPE.API_CODE,
          node_id: process_node_ID,
          conv_id,
          lang: 'js',
          src: 'var b = *********;data.a = b;',
        }),
      );
      expect(response.status).toBe(RESP_STATUS.OK);
      SchemaValidator.validate(schema, response.body);
    });
  });

  test('should get api_code', async (): Promise<void> => {
    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);
    const { proc: procCommit } = responseCommit.body.ops[0];
    expect(procCommit).toEqual(PROC_STATUS.OK);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.API_CODE,
        node_id: process_node_ID,
        conv_id,
        env: 'production',
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    SchemaValidator.validate(getSchema, response.body);
  });

  const testCasesPositive = [
    {
      description: 'method explain lang JavaScript scr "/n" ',
      lang: LANG.JS,
      method: METHOD_SHOW.EXPLAIN,
      src: (): string => '\n',
    },
    {
      description: 'method explain lang Erlang scr "tabulation" ',
      lang: LANG.ERLANG,
      method: METHOD_SHOW.EXPLAIN,
      src: (): string => '    ',
    },
    {
      description: 'method completion lang JavaScript scr " " ',
      lang: LANG.JS,
      method: METHOD_SHOW.COMPLETION,
      src: (): string => ' ',
    },
    {
      description: 'method completion lang Erlang scr " " ',
      lang: LANG.ERLANG,
      method: METHOD_SHOW.COMPLETION,
      src: (): string => ' ',
    },
  ];

  describe.each(testCasesPositive)('$description', ({ src, lang, method }): void => {
    test('should show api_code', async (): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.API_CODE,
          node_id: process_node_ID,
          conv_id,
          method,
          lang,
          src: src(),
        }),
      );
      const { ops: opsResponse } = response.body as ShowApiCodeResponse;
      expect(response.status).toBe(RESP_STATUS.OK);
      const { proc: procCode, method: methodCode, result: resultCode } = opsResponse[0];
      expect(procCode).toEqual(PROC_STATUS.OK);
      expect(methodCode).toEqual(method);
      expect(resultCode).not.toBeEmpty;
    });
  });

  const testCasesPositiveRating = [
    {
      description: 'method explain lang JavaScript',
      lang: LANG.JS,
      method: METHOD_SHOW.EXPLAIN,
      src: (): string => strCodeJS,
    },
    {
      description: 'method explain lang Erlang',
      lang: LANG.ERLANG,
      method: METHOD_SHOW.EXPLAIN,
      src: (): string => strCodeErl,
    },
    {
      description: 'method completion lang JavaScript',
      lang: LANG.JS,
      method: METHOD_SHOW.COMPLETION,
      src: (): string => 'let myArray = [1, 2, 3, 4, 5];function getSumArray(){',
    },
    {
      description: 'method completion lang Erlang',
      lang: LANG.ERLANG,
      method: METHOD_SHOW.COMPLETION,
      src: (): string =>
        "-module(example).\n-export([my_variable/0,my_array/0,my_list/0,my_function/1]).\nmy_variable()->Var='hello world',Var.\nmy_array()->[1,2,3,4,5].\nmy_list()->List=[1,2,3,4,5],List.\nmy_function(X)->",
    },
  ];

  describe.each(testCasesPositiveRating)('$description', ({ src, lang, method }): void => {
    test('should show api_code', async (): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.API_CODE,
          node_id: process_node_ID,
          conv_id,
          method,
          lang,
          src: src(),
        }),
      );
      const { ops: opsResponse } = response.body as ShowApiCodeResponse;
      expect(response.status).toBe(RESP_STATUS.OK);
      const { proc: procCode, method: methodCode, result: resultCode } = opsResponse[0];
      expect(procCode).toEqual(PROC_STATUS.OK);
      expect(methodCode).toEqual(method);
      expect(resultCode).not.toBeEmpty;
      rating = await codeRating(method, src(), resultCode);
      expect(rating).toBeGreaterThan(3);
    });
  });

  afterAll(
    async (): Promise<void> => {
      const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
      expect(response.status).toBe(RESP_STATUS.OK);
    },
  );
});
