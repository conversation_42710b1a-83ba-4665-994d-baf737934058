import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
  generateName,
} from '../../../../../../../utils/corezoidRequest';
import {
  LANG,
  METHOD_SHOW,
  ShowApiCodeResponse,
  GetApiCodeResponse,
  LoadApiCodeResponse,
  CompileApiCodeResponse,
} from '../../../../../../../application/api/obj_types/api_code';
import {
  integerTestCases,
  undefinedTestCase,
  maxLength,
  stringTestCases,
  stringNotValidTestCases,
} from '../../../../../negativeCases';
import { requestDeleteObj, requestCreateObjNew, requestListConv } from '../../../../../../../application/api/ApiObj';

describe('Api_code (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let conv_id: string | number;
  let company_id: any;
  let process_node_ID: string;
  let final_node_ID: string;
  let valuesToSkip: any;
  let conv_type: string;

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;
      conv_type = 'process';

      const responseConv = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, generateName(OBJ_TYPE.CONV), {
        conv_type,
      });
      conv_id = responseConv.body.ops[0].obj_id;

      const responseList = await requestListConv(api, conv_id, company_id);
      expect(responseList.status).toBe(RESP_STATUS.OK);
      process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === conv_type).obj_id;
      final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

      const responseCreateCode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id,
          title: conv_type,
          obj_type: 0,
          logics: [
            { type: 'api_code', err_node_id: '', lang: 'js', src: 'var b = *********;data.a = b;' },
            { to_node_id: final_node_ID, format: 'json', type: 'go', node_title: 'final' },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseCreateCode.status).toBe(200);
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't compile api_code with invalid conv_id '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPILE,
          obj: OBJ_TYPE.API_CODE,
          node_id: process_node_ID,
          conv_id: input,
          lang: 'js',
          src: 'var b = *********;data.a = b;',
        }),
      );
      const { ops: opsResponse, request_proc: reqprocREsponse } = response.body as CompileApiCodeResponse;
      const { proc: procResponse, description: descResponse } = opsResponse[0];
      expect(response.status).toBe(RESP_STATUS.OK);
      expect(reqprocREsponse).toBe(PROC_STATUS.OK);
      expect(procResponse).toBe(PROC_STATUS.ERROR);
      expect(errors).toEqual(expect.arrayContaining([descResponse]));
    },
  );

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase, ...maxLength].map(({ input, errors }) => [
      input,
      errors,
    ]),
  )(
    `shouldn't compile api_code with invalid node_id '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPILE,
          obj: OBJ_TYPE.API_CODE,
          node_id: input,
          conv_id,
          lang: 'js',
          src: 'var b = *********;data.a = b;',
        }),
      );
      const { ops: opsResponse, request_proc: reqprocREsponse } = response.body as CompileApiCodeResponse;
      const { proc: procResponse, description: descResponse } = opsResponse[0];
      expect(response.status).toBe(RESP_STATUS.OK);
      expect(reqprocREsponse).toBe(PROC_STATUS.OK);
      expect(procResponse).toBe(PROC_STATUS.ERROR);
      expect(errors).toEqual(expect.arrayContaining([descResponse]));
    },
  );

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(
    `shouldn't compile api_code with invalid lang '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPILE,
          obj: OBJ_TYPE.API_CODE,
          node_id: final_node_ID,
          conv_id,
          lang: input,
          src: 'var b = *********;data.a = b;',
        }),
      );
      const { ops: opsResponse, request_proc: reqprocREsponse } = response.body as CompileApiCodeResponse;
      const { proc: procResponse, description: descResponse } = opsResponse[0];
      expect(response.status).toBe(RESP_STATUS.OK);
      expect(reqprocREsponse).toBe(PROC_STATUS.OK);
      expect(procResponse).toBe(PROC_STATUS.ERROR);
      expect(errors).toEqual(expect.arrayContaining([descResponse]));
    },
  );

  valuesToSkip = ['test'];
  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(
    `shouldn't compile api_code with invalid src '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPILE,
          obj: OBJ_TYPE.API_CODE,
          node_id: final_node_ID,
          conv_id,
          lang: 'js',
          src: input,
        }),
      );
      const { ops: opsResponse, request_proc: reqprocREsponse } = response.body as CompileApiCodeResponse;
      const { proc: procResponse, description: descResponse } = opsResponse[0];
      expect(response.status).toBe(RESP_STATUS.OK);
      expect(reqprocREsponse).toBe(PROC_STATUS.OK);
      expect(procResponse).toBe(PROC_STATUS.ERROR);
      expect(errors).toEqual(expect.arrayContaining([descResponse]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't load api_code with invalid conv_id '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LOAD,
          obj: OBJ_TYPE.API_CODE,
          node_id: process_node_ID,
          conv_id: input,
          lang: 'js',
          src: 'var b = *********;data.a = b;',
          env: 'production',
        }),
      );
      const { ops: opsResponse, request_proc: reqprocREsponse } = response.body as LoadApiCodeResponse;
      const { proc: procResponse, description: descResponse } = opsResponse[0];
      expect(response.status).toBe(RESP_STATUS.OK);
      expect(reqprocREsponse).toBe(PROC_STATUS.OK);
      expect(procResponse).toBe(PROC_STATUS.ERROR);
      expect(errors).toEqual(expect.arrayContaining([descResponse]));
    },
  );

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase, ...maxLength].map(({ input, errors }) => [
      input,
      errors,
    ]),
  )(
    `shouldn't load api_code with invalid node_id '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LOAD,
          obj: OBJ_TYPE.API_CODE,
          node_id: input,
          conv_id,
          lang: 'js',
          src: 'var b = *********;data.a = b;',
          env: 'production',
        }),
      );
      const { ops: opsResponse, request_proc: reqprocREsponse } = response.body as LoadApiCodeResponse;
      const { proc: procResponse, description: descResponse } = opsResponse[0];
      expect(response.status).toBe(RESP_STATUS.OK);
      expect(reqprocREsponse).toBe(PROC_STATUS.OK);
      expect(procResponse).toBe(PROC_STATUS.ERROR);
      expect(errors).toEqual(expect.arrayContaining([descResponse]));
    },
  );

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(
    `shouldn't load api_code with invalid lang '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LOAD,
          obj: OBJ_TYPE.API_CODE,
          node_id: final_node_ID,
          conv_id,
          lang: input,
          src: 'var b = *********;data.a = b;',
          env: 'production',
        }),
      );
      const { ops: opsResponse, request_proc: reqprocREsponse } = response.body as LoadApiCodeResponse;
      const { proc: procResponse, description: descResponse } = opsResponse[0];
      expect(response.status).toBe(RESP_STATUS.OK);
      expect(reqprocREsponse).toBe(PROC_STATUS.OK);
      expect(procResponse).toBe(PROC_STATUS.ERROR);
      expect(errors).toEqual(expect.arrayContaining([descResponse]));
    },
  );

  valuesToSkip = ['test'];
  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(
    `shouldn't load api_code with invalid src '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LOAD,
          obj: OBJ_TYPE.API_CODE,
          node_id: final_node_ID,
          conv_id,
          lang: 'js',
          src: input,
          env: 'production',
        }),
      );
      const { ops: opsResponse, request_proc: reqprocREsponse } = response.body as LoadApiCodeResponse;
      const { proc: procResponse, description: descResponse } = opsResponse[0];
      expect(response.status).toBe(RESP_STATUS.OK);
      expect(reqprocREsponse).toBe(PROC_STATUS.OK);
      expect(procResponse).toBe(PROC_STATUS.ERROR);
      expect(errors).toEqual(expect.arrayContaining([descResponse]));
    },
  );

  test.each([...stringTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't load api_code with invalid env '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LOAD,
          obj: OBJ_TYPE.API_CODE,
          node_id: process_node_ID,
          conv_id,
          lang: 'js',
          src: 'var b = *********;data.a = b;',
          env: input,
        }),
      );
      const { ops: opsResponse, request_proc: reqprocREsponse } = response.body as LoadApiCodeResponse;
      const { proc: procResponse, description: descResponse } = opsResponse[0];
      expect(response.status).toBe(RESP_STATUS.OK);
      expect(reqprocREsponse).toBe(PROC_STATUS.OK);
      expect(procResponse).toBe(PROC_STATUS.ERROR);
      expect(errors).toEqual(expect.arrayContaining([descResponse]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't get api_code with invalid conv_id '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.GET,
          obj: OBJ_TYPE.API_CODE,
          node_id: process_node_ID,
          conv_id: input,
          env: 'production',
        }),
      );
      const { ops: opsResponse, request_proc: reqprocREsponse } = response.body as GetApiCodeResponse;
      const { proc: procResponse, description: descResponse } = opsResponse[0];
      expect(response.status).toBe(RESP_STATUS.OK);
      expect(reqprocREsponse).toBe(PROC_STATUS.OK);
      expect(procResponse).toBe(PROC_STATUS.ERROR);
      expect(errors).toEqual(expect.arrayContaining([descResponse]));
    },
  );

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase, ...maxLength].map(({ input, errors }) => [
      input,
      errors,
    ]),
  )(
    `shouldn't get api_code with invalid node_id '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.GET,
          obj: OBJ_TYPE.API_CODE,
          node_id: input,
          conv_id,
          env: 'production',
        }),
      );
      const { ops: opsResponse, request_proc: reqprocREsponse } = response.body as GetApiCodeResponse;
      const { proc: procResponse, description: descResponse } = opsResponse[0];
      expect(response.status).toBe(RESP_STATUS.OK);
      expect(reqprocREsponse).toBe(PROC_STATUS.OK);
      expect(procResponse).toBe(PROC_STATUS.ERROR);
      expect(errors).toEqual(expect.arrayContaining([descResponse]));
    },
  );

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(
    `shouldn't get api_code with invalid env '%s'`,
    async (input, errors): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.GET,
          obj: OBJ_TYPE.API_CODE,
          node_id: final_node_ID,
          conv_id,
          env: input,
        }),
      );
      const { ops: opsResponse, request_proc: reqprocREsponse } = response.body as GetApiCodeResponse;
      const { proc: procResponse, description: descResponse } = opsResponse[0];
      expect(response.status).toBe(RESP_STATUS.OK);
      expect(reqprocREsponse).toBe(PROC_STATUS.OK);
      expect(procResponse).toBe(PROC_STATUS.ERROR);
      expect(errors).toEqual(expect.arrayContaining([descResponse]));
    },
  );

  const testCasesNegative = [
    {
      description: 'method explain lang JavaScript',
      lang: LANG.JS,
      method: METHOD_SHOW.EXPLAIN,
    },
    {
      description: 'method explain lang Erlang',
      lang: LANG.ERLANG,
      method: METHOD_SHOW.EXPLAIN,
    },
    {
      description: 'method completion lang JavaScript',
      lang: LANG.JS,
      method: METHOD_SHOW.COMPLETION,
    },
    {
      description: 'method completion lang Erlang',
      lang: LANG.ERLANG,
      method: METHOD_SHOW.COMPLETION,
    },
  ];

  describe.each(testCasesNegative)('$description', ({ lang, method }) => {
    valuesToSkip = ['test', ''];
    test.each(
      [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase]
        .filter(({ input }) => !valuesToSkip.includes(input as string | number))
        .map(({ input, errors }) => [input, errors]),
    )(
      `shouldn't show api_code with invalid scr '%s'`,
      async (input, errors): Promise<void> => {
        const response = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.SHOW,
            obj: OBJ_TYPE.API_CODE,
            node_id: process_node_ID,
            conv_id,
            method,
            lang,
            src: input,
          }),
        );
        const { ops: opsResponse, request_proc: reqprocREsponse } = response.body as ShowApiCodeResponse;
        const { proc: procResponse, description: descResponse } = opsResponse[0];
        expect(response.status).toBe(RESP_STATUS.OK);
        expect(reqprocREsponse).toBe(PROC_STATUS.OK);
        expect(procResponse).toBe(PROC_STATUS.ERROR);
        expect(errors).toEqual(expect.arrayContaining([descResponse]));
      },
    );
  });

  const testCasesNegativMethod = [
    {
      description: 'lang JavaScript',
      lang: LANG.JS,
    },
    {
      description: 'lang Erlang',
      lang: LANG.ERLANG,
    },
  ];

  describe.each(testCasesNegativMethod)('$description', ({ lang }) => {
    test.each(
      [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [
        input,
        errors,
      ]),
    )(
      `shouldn't show api_code with invalid method '%s'`,
      async (input, errors): Promise<void> => {
        const response = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.SHOW,
            obj: OBJ_TYPE.API_CODE,
            node_id: process_node_ID,
            conv_id,
            method: input,
            lang,
            src: ' ',
          }),
        );
        const { ops: opsResponse, request_proc: reqprocREsponse } = response.body as ShowApiCodeResponse;
        const { proc: procResponse, description: descResponse } = opsResponse[0];
        expect(response.status).toBe(RESP_STATUS.OK);
        expect(reqprocREsponse).toBe(PROC_STATUS.OK);
        expect(procResponse).toBe(PROC_STATUS.ERROR);
        expect(errors).toEqual(expect.arrayContaining([descResponse]));
      },
    );
  });

  afterAll(
    async (): Promise<void> => {
      const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
      expect(response.status).toBe(RESP_STATUS.OK);
    },
  );
});
