import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import listCompany from '../../../../schemas/v2/company/listCompany.Schema.json';
import showCompany from '../../../../schemas/v2/company/showCompany.Schema.json';
import createCompany from '../../../../schemas/v2/company/createCompany.Schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { ApiUserClient } from '../../../../../../../application/api/ApiUserClient';
import { User } from '../../../../../../../infrastructure/model/User';
import { createAuthUser, Method } from '../../../../../../../utils/request';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';

describe('Company (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let apiUser: ApiUserClient;
  let user: User;
  let cookieUser: any;
  let company_id: any;
  let hostSS: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    hostSS = ConfigurationManager.getConfiguration().getSSUrl();

    user = await application.getAuthorizedUser({ company: {} }, 0);
    cookieUser = createAuthUser(user.cookieUser, 'cookie');
    apiUser = await application.getApiUserClient(user);
  });

  test(`should show create company`, async () => {
    const response = await apiUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,
        site: 'test.com',
        name: `Test-${Date.now()}`,
        description: 'test',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    company_id = response.body.ops[0].obj_id;
    SchemaValidator.validate(createCompany, response.body);
  });

  test(`should show list company(apikey)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    SchemaValidator.validate(listCompany, response.body);
  });

  test(`should show list company(apiuser)`, async () => {
    const response = await apiUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    SchemaValidator.validate(listCompany, response.body);
  });

  test(`should show company(apikey) (Access denied)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.COMPANY,
        company_id,
        obj_type: 'user_permissions',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Access denied');
  });

  test(`should show company`, async () => {
    const response = await apiUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.COMPANY,
        company_id,
        obj_type: 'user_permissions',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(showCompany, response.body);
  });

  test(`should show company (cookie user)`, async () => {
    const response = await apiUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.COMPANY,
        company_id,
        obj_type: 'user_permissions',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(showCompany, response.body);
  });

  test(`should delete company`, async () => {
    const responseMe = await cookieUser.request({
      method: Method.DELETE,
      url: `${hostSS}face/api/1/workspaces/${company_id}`,
    });
    expect(responseMe.status).toBe(200);
  });
});
