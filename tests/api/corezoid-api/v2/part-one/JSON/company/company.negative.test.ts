import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { ApiUserClient } from '../../../../../../../application/api/ApiUserClient';
import { User } from '../../../../../../../infrastructure/model/User';
import { debug } from '../../../../../../../support/utils/logger';
import {
  stringTestCases,
  undefinedTestCase,
  maxLength,
  min<PERSON>ength,
  companyTestCases,
  stringNotValidTestCases,
} from '../../../../../negativeCases';

describe('Company (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let apiUser: ApiUserClient;
  let user: User;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    user = await application.getAuthorizedUser({ company: {} }, 0);
    apiUser = await application.getApiUserClient(user);
  });

  test.each(
    [...stringTestCases, ...minLength, ...maxLength, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create company with invalid site '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,
        site: input,
        name: 'Key_company',
        description: 'test',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...minLength, ...maxLength, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create company with invalid name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,
        site: 'test.com',
        name: input,
        description: 'test',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...minLength, ...maxLength, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create company with invalid description '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,
        site: 'test.com',
        name: 'company',
        description: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't show company with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await apiUser.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.COMPANY,
          company_id: input,
          obj_type: 'user_permissions',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't show company with invalid obj_type '%s'`, async (input, errors) => {
    const response = await apiUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.COMPANY,
        company_id,
        obj_type: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    debug(response.body);
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });
});
