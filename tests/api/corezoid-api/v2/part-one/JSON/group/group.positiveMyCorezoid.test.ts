import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createSchema from '../../../../schemas/v2/users-groups/createGroupSchema.json';
import listGroupConvSchema from '../../../../schemas/v2/users-groups/listGroupConvSchema.json';
import listGroupUserSchema from '../../../../schemas/v2/users-groups/listGroupUserSchema.json';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('Group (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newGroupAdmins: string | number;
  let ApiKey2: string | number;
  let newConv: string | number;
  let titleForCompany: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    titleForCompany = `title-${Date.now()}`;

    const createKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(createKeyResponse.status).toBe(200);
    const user = createKeyResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    ApiKey2 = +newApiKey.id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,

        folder_id: 0,
        title: `Process_${Date.now()}`,
        status: 'active',
        obj_type: 0,
        description: 'test',
        create_mode: 'without_nodes',
        conv_type: 'process',
      }),
    );
    newConv = response.body.ops[0].obj_id;
  });

  test('should create group admins', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        title: titleForCompany,
        obj_type: 'admins',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('group');
    newGroupAdmins = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should modify group admins to not_actived status', async () => {
    titleForCompany = 'new' + titleForCompany;
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.GROUP,
        obj_id: newGroupAdmins,
        title: titleForCompany,
        obj_type: 'admins',
        status: 'not_actived',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('group');
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should modify group admins', async () => {
    titleForCompany = 'new' + titleForCompany;
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.GROUP,
        obj_id: newGroupAdmins,
        title: titleForCompany,
        obj_type: 'admins',
        status: 'actived',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('group');
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should list conv to group', async () => {
    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        obj_to: 'group',
        obj_to_id: newGroupAdmins,
        privs: [{ type: 'create', list_obj: ['all'] }],
      }),
    );
    expect(responseLink.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: newGroupAdmins,
        list_obj: 'conv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('group');
    expect(response.body.ops[0].obj_id).toBe(newGroupAdmins);
    expect(response.body.ops[0].list[0].obj_id).toBe(newConv);
    SchemaValidator.validate(listGroupConvSchema, response.body);
  });

  test('should list user to group', async () => {
    titleForCompany = `title-${Date.now()}`;
    const response1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,

        title: titleForCompany,
        logins: [{ type: 'api' }],
        group_id: newGroupAdmins,
        send_invite_if_user_not_exists: true,
      }),
    );
    expect(response1.status).toBe(200);
    const newApiKeyCompany = response1.body.ops[0].users[0].obj_id;
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: newGroupAdmins,
        list_obj: 'user',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('group');
    expect(response.body.ops[0].obj_id).toBe(newGroupAdmins);
    expect(response.body.ops[0].list[0].obj_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(listGroupUserSchema, response.body);
  });

  test('should delete existing group admins', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        obj_id: newGroupAdmins,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_id).toBe(newGroupAdmins);
  });

  test('should restore deleting group admins', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.GROUP,
        obj_id: newGroupAdmins,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_id).toBe(newGroupAdmins);

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        obj_id: newGroupAdmins,
      }),
    );
    expect(response.status).toBe(200);
  });

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
      }),
    );
  });
});
