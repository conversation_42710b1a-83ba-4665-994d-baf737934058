import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createSchema from '../../../../schemas/v2/users-groups/createGroupSchema.json';
import listSchema from '../../../../schemas/v2/users-groups/listGroupSchema.json';
import listGroupConvSchema from '../../../../schemas/v2/users-groups/listGroupConvSchema.json';
import listGroupUserSchema from '../../../../schemas/v2/users-groups/listGroupUserSchema.json';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { makeRequestWithRetries, MyApiResponse } from '../../../../../../../utils/requestRetries';

describe('Group (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let apiS: ApiKeyClient;
  let apiSuper: ApiKey;
  let newGroupAdmins: string | number;
  let newGroupSupers: string | number;
  let newConv: string | number;
  let titleForCompany: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    titleForCompany = `title-${Date.now()}`;
    apiSuper = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apiSuper);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        folder_id: 0,
        title: `Process_${Date.now()}`,
        status: 'active',
        obj_type: 0,
        description: 'test',
        create_mode: 'without_nodes',
        conv_type: 'process',
      }),
    );
    newConv = response.body.ops[0].obj_id;
  });

  test('should create group admins', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        title: titleForCompany,
        obj_type: 'admins',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('group');
    newGroupAdmins = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test.skip('should create group supers', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        title: titleForCompany,
        obj_type: 'supers',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('group');
    newGroupSupers = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should modify group admins to not_actived status', async () => {
    titleForCompany = 'new' + titleForCompany;
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.GROUP,
        obj_id: newGroupAdmins,
        company_id,
        title: titleForCompany,
        obj_type: 'admins',
        status: 'not_actived',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('group');
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should modify group admins', async () => {
    titleForCompany = 'new' + titleForCompany;
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.GROUP,
        obj_id: newGroupAdmins,
        company_id,
        title: titleForCompany,
        obj_type: 'admins',
        status: 'actived',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('group');
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should list conv to group', async () => {
    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: newConv,
        obj_to: 'group',
        obj_to_id: newGroupAdmins,
        privs: [{ type: 'create', list_obj: ['all'] }],
      }),
    );
    expect(responseLink.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_id: newGroupAdmins,
        list_obj: 'conv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('group');
    expect(response.body.ops[0].obj_id).toBe(newGroupAdmins);
    expect(response.body.ops[0].list[0].obj_id).toBe(newConv);
    SchemaValidator.validate(listGroupConvSchema, response.body);
  });

  test('should list user to group', async () => {
    titleForCompany = `title-${Date.now()}`;
    const response1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,

        title: titleForCompany,
        logins: [{ type: 'api' }],
        group_id: newGroupAdmins,
        send_invite_if_user_not_exists: true,
      }),
    );
    expect(response1.status).toBe(200);
    const newApiKeyCompany = response1.body.ops[0].users[0].obj_id;
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_id: newGroupAdmins,
        list_obj: 'user',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('group');
    expect(response.body.ops[0].obj_id).toBe(newGroupAdmins);
    expect(response.body.ops[0].list[0].obj_id).toBe(newApiKeyCompany);
    SchemaValidator.validate(listGroupUserSchema, response.body);
  });

  test('should get list of groups', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        filter: 'group',
        sort: 'title',
        order: 'asc',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('company_users');
    SchemaValidator.validate(listSchema, response.body);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newGroupAdmins })]),
    );
    expect(
      response.body.ops[0].list.find((item: { obj_id: string | number }) => item.obj_id === newGroupAdmins)?.is_owner,
    ).toEqual(true);
  });

  test('should delete existing group admins', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_id: newGroupAdmins,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_id).toBe(newGroupAdmins);
  });

  test.skip('should not delete existing group supers', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_id: newGroupSupers,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(`Access denied`);
  });

  test('should restore deleting group admins', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_id: newGroupAdmins,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_id).toBe(newGroupAdmins);
  });

  test('should modify group admins to deleted', async () => {
    titleForCompany = 'new' + titleForCompany;
    const responseModify = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.GROUP,
        obj_id: newGroupAdmins,
        company_id,
        title: titleForCompany,
        obj_type: 'admins',
        status: 'deleted',
      }),
    );
    expect(responseModify.status).toBe(200);
    expect(responseModify.body.request_proc).toBe('ok');
    expect(responseModify.body.ops[0].proc).toBe('ok');
    expect(responseModify.body.ops[0].obj).toBe('group');
    SchemaValidator.validate(createSchema, responseModify.body);

    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        response.body.ops[0].obj === 'company_users' &&
        response.body.ops[0].list.some((item: any) => item.obj_id === newGroupAdmins)
      );
    }

    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          company_id,
          filter: 'group',
          sort: 'title',
          order: 'asc',
        }),
      );
    }, checkConditions);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_id: newGroupAdmins,
      }),
    );
  });
});
