import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import faker from 'faker';
import data from './data/value.json';
import { stringTestCases, stringNotValidTestCases, securityTestCases } from '../../../../../negativeCases';

describe('Env_var (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let company_id: any;
  let stage_short_name: string;
  let project_short_name: string;
  let newVar: string;
  let value_encoding: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    stage_short_name = `stage-${Date.now()}`;
    project_short_name = `project-${Date.now()}`;
    value_encoding = data.value;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
        project_short_name: `test`,
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;

    const responseVar = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: 'short-name',
        description: 'test',
        title: 'Var',
        value: 'test',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(responseVar.status).toBe(200);
    expect(responseVar.body.ops[0].obj).toBe('env_var');
    newVar = responseVar.body.ops[0].obj_id;

    const responseVar2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: 'short-name1',
        description: 'test',
        title: 'Var',
        value: 'test',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(responseVar2.status).toBe(200);
    expect(responseVar2.body.ops[0].obj).toBe('env_var');
  });

  test.each([
    ['test', 'Company test does not exists'],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [1234, `Value '1234' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'company_id' is required`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't create env_var with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: `${Date.now()}`,
        description: 'test',
        title: 'Var',
        value: 'test',
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"secret\\\">>,<<\\\"visible\\\">>]\">>`,
    ],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    ['', `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"secret\\\">>,<<\\\"visible\\\">>]\">>`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [1234, `Value '1234' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'env_var_type' is required`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't create env_var with invalid env_var_type '%s'`, async (env_var_type, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type,
        short_name: `${Date.now()}`,
        description: 'test',
        title: 'Var',
        data_type: 'raw',
        value: 'test',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', `Value 'test' is not valid. Type of value is not 'list'`],
    [true, `Value 'true' is not valid. Type of value is not 'list'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'list'`],
    [[], `Key 'scopes.type' is required`],
    ['', `Value '' is not valid. Type of value is not 'list'`],
    [1, `Value '1' is not valid. Type of value is not 'list'`],
    [-1, `Value '-1' is not valid. Type of value is not 'list'`],
    [0, `Value '0' is not valid. Type of value is not 'list'`],
    [1234, `Value '1234' is not valid. Type of value is not 'list'`],
    [undefined, `Key 'scopes' is required`],
    [null, `Value 'null' is not valid. Type of value is not 'list'`],
  ])(`shouldn't create env_var with invalid scopes '%s'`, async (scopes, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: `${Date.now()}`,
        description: 'test',
        title: 'Var',
        value: 'test',
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each(['test', true, {}, [], '', 1, -1, 0, 1234, undefined, null])(
    `shouldn't create env_var with invalid scope.type (env_var_type:secret)'%s'`,
    async type => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ENV_VAR,
          company_id,

          env_var_type: 'secret',
          short_name: `${Date.now()}`,
          description: 'test',
          title: 'Var',
          value: 'test',
          data_type: 'raw',
          project_id: newProject,
          stage_id: newStage,
          scopes: [{ type, fields: ['cert_pem', 'api_secret_outer'] }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(
        `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`,
      );
    },
  );

  test.each(['test', true, {}, [], '', 1, -1, 0, 1234, undefined, null])(
    `shouldn't create env_var with invalid scope.type (env_var_type:visible)'%s'`,
    async type => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ENV_VAR,
          company_id,

          env_var_type: 'visible',
          short_name: `${Date.now()}`,
          description: 'test',
          title: 'Var',
          value: 'test',
          data_type: 'raw',
          project_id: newProject,
          stage_id: newStage,
          scopes: [{ type, fields: ['cert_pem', 'api_secret_outer'] }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(
        `Key 'type' is invalid, allowed only *, api_call, set_param, go_if_const, api_copy, db_call, api_rpc, api_rpc_reply, api_code`,
      );
    },
  );

  test.each([
    ['test', `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [true, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [{}, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [[], `Key 'fields' is invalid, allowed * or not empty array`],
    [['test'], `Key 'fields' is invalid, allowed only '[api_secret_outer,cert_pem,url,extra_headers,response,extra]'`],
    ['', `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [1, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [-1, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [0, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [1234, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [undefined, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [null, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
  ])(`shouldn't create env_var with invalid scope.fields (env_var_type:visible)'%s'`, async (fields, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'visible',
        short_name: `${Date.now()}`,
        description: 'test',
        title: 'Var',
        value: 'test',
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [true, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [{}, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [[], `Key 'fields' is invalid, allowed * or not empty array`],
    [['test'], `Key 'fields' is invalid, allowed only '[api_secret_outer,cert_pem,url,extra_headers,response,extra]'`],
    ['', `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [1, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [-1, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [0, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [1234, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [undefined, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [null, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
  ])(`shouldn't create env_var with invalid scope.fields (env_var_type:secret)'%s'`, async (fields, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: `${Date.now()}`,
        description: 'test',
        title: 'Var',
        value: 'test',
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
    [true, `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
    [{}, `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
    [[], `Key 'fields' is invalid, allowed * or not empty array`],
    [['test'], `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
    ['', `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
    [1, `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
    [-1, `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
    [0, `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
    [1234, `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
    [undefined, `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
    [null, `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
  ])(`shouldn't create env_var type: '*' with invalid scope.fields '%s'`, async (fields, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: `${Date.now()}`,
        description: 'test',
        title: 'Var',
        value: 'test',
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: '*', fields }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Key 'project_short_name' is required or Value 'null' is not valid project_id`],
    ['test', `Key 'project_short_name' is required or Value 'test' is not valid project_id`],
    [true, `Key 'project_short_name' is required or Value 'true' is not valid project_id`],
    [{}, `Key 'project_short_name' is required or Value '{[]}' is not valid project_id`],
    [[], `Key 'project_short_name' is required or Value '' is not valid project_id`],
    ['', `Key 'project_short_name' is required or Value '' is not valid project_id`],
    [1, `Project not found`],
    [-1, `Key 'project_short_name' is required or Value '-1' is not valid project_id`],
    [1234, `Project not found`],
    [undefined, `Key 'project_short_name' is required or Key 'project_id' is required`],
  ])(`shouldn't create env_var with invalid project_id '%s'`, async (project_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: `${Date.now()}`,
        description: 'test',
        title: 'Var',
        value: 'test',
        data_type: 'raw',
        project_id,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Key 'stage_short_name' is required or Value 'null' is not valid stage_id`],
    ['test', `Key 'stage_short_name' is required or Value 'test' is not valid stage_id`],
    [true, `Key 'stage_short_name' is required or Value 'true' is not valid stage_id`],
    [{}, `Key 'stage_short_name' is required or Value '{[]}' is not valid stage_id`],
    [[], `Key 'stage_short_name' is required or Value '' is not valid stage_id`],
    ['', `Key 'stage_short_name' is required or Value '' is not valid stage_id`],
    [1, `Stage not found`],
    [-1, `Value -1 not allowed for stage_id`],
    [1234, `Stage not found`],
    [undefined, `Key 'stage_short_name' is required or Key 'stage_id' is required`],
  ])(`shouldn't create env_var with invalid stage_id '%s'`, async (stage_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: `${Date.now()}`,
        description: 'test',
        title: 'Var',
        value: 'test',
        data_type: 'raw',
        project_id: newProject,
        stage_id,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      null,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    ['test', `Project is not found`],
    [
      true,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      {},
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      [],
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      '',
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      -1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      1234,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [undefined, `Key 'project_short_name' is required or Key 'project_id' is required`],
  ])(`shouldn't create env_var with invalid project_short_name '%s'`, async (project_short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: `${Date.now()}`,
        description: 'test',
        title: 'Var',
        value: 'test',
        data_type: 'raw',
        project_short_name,
        stage_short_name,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      null,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    ['test', `Stage not found`],
    [
      true,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      {},
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      [],
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      '',
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      -1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      1234,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [undefined, `Key 'stage_short_name' is required or Key 'stage_id' is required`],
  ])(`shouldn't create env_var with invalid stage_short_name '%s'`, async (stage_short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: `${Date.now()}`,
        description: 'test',
        title: 'Var',
        value: 'test',
        data_type: 'raw',
        project_short_name,
        stage_short_name,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'title' is required`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [faker.random.alphaNumeric(256), `Value is not valid. Value's byte_size is more than maximum allowed: 255`],
  ])(`shouldn't create env_var with invalid title '%s'`, async (title, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: `${Date.now()}`,
        description: 'test',
        title,
        value: 'test',
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      true,
      `Value 'true' is not valid. Type of value is not 'binary' or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [
      {},
      `Value '{[]}' is not valid. Type of value is not 'binary' or Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [
      [],
      `Value '[]' is not valid. Type of value is not 'binary' or Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [
      1,
      `Value '1' is not valid. Type of value is not 'binary' or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [
      -1,
      `Value '-1' is not valid. Type of value is not 'binary' or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [
      0,
      `Value '0' is not valid. Type of value is not 'binary' or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [faker.random.alphaNumeric(2001), `Value is not valid. Value's byte_size is more than maximum allowed: 2000`],
  ])(`shouldn't create env_var with invalid description '%s'`, async (description, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: `${Date.now()}`,
        description,
        title: 'test',
        value: 'test',
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([
    [
      null,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      true,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      {},
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      [],
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      'TEST',
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      '',
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      'new_test',
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      -1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      0,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      faker.random.alphaNumeric(128),
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    ['short-name', `Env_var short_name already exists`],
    [undefined, `Key 'short_name' is required`],
  ])(`shouldn't create env_var with invalid short_name '%s'`, async (short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name,
        description: 'test',
        title: 'test',
        value: 'test',
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'value' is required`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [faker.random.alphaNumeric(2097153), `Value is not valid. Value's byte_size is more than maximum allowed: 2097152`],
  ])(`shouldn't create env_var with invalid value '%s' data_type raw`, async (value, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: `${Date.now()}`,
        description: 'test',
        title: 'test',
        value,
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test(`shouldn't create env_var with invalid value=Incorrect encoding data_type raw`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: `${Date.now()}`,
        description: 'test',
        title: 'test',
        value: value_encoding,
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Unsupported unicode control code');
    expect(response.body.ops[0].value).toBeString();
  });

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'value' is required`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    ['test', `not valid json in value field`],
    ['{}', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [{ a: 1 }, `Value '[{<<\"a\">>,1}]' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't create env_var with invalid value '%s' data_type json`, async (value, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: `${Date.now()}`,
        description: 'test',
        title: 'test',
        value,
        data_type: 'json',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    ['', `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"raw\\\">>,<<\\\"json\\\">>]\">>`],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"raw\\\">>,<<\\\"json\\\">>]\">>`,
    ],
  ])(`shouldn't create env_var with invalid data_type '%s'`, async (data_type, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: `${Date.now()}`,
        description: 'test',
        title: 'test',
        value: 'test',
        data_type,
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([
    ['test', 'Company test does not exists'],
    [true, `Value is not valid`],
    [{}, `Value is not valid`],
    [[], `Value is not valid`],
    ['', `Value is not valid`],
    [1, `Value is not valid`],
    [-1, `Value is not valid`],
    [0, `Value is not valid`],
    [1234, `Value is not valid`],
  ])(`shouldn't delete env_var with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,
        obj_id: newVar,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', 'Value is not valid'],
    [true, `Value is not valid`],
    [{}, `Value is not valid`],
    [[], `Value is not valid`],
    ['', `Value is not valid`],
    [1, `Object env_var with id 1 does not exist`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [0, `Object env_var with id 0 does not exist`],
    [1234, `Object env_var with id 1234 does not exist`],
    [null, `Value is not valid`],
    [undefined, `Key 'obj_id' is required`],
  ])(`shouldn't delete env_var with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,
        obj_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', `Key 'project_short_name' is required or Value 'test' is not valid project_id`],
    [true, `Key 'project_short_name' is required or Value 'true' is not valid project_id`],
    [{}, `Key 'project_short_name' is required or Value '{[]}' is not valid project_id`],
    [[], `Key 'project_short_name' is required or Value '' is not valid project_id`],
    ['', `Key 'project_short_name' is required or Value '' is not valid project_id`],
    [1, `Project of object do not matches projectId of request`],
    [-1, `Key 'project_short_name' is required or Value '-1' is not valid project_id`],
    [0, `Value 0 not allowed for project_id`],
    [1234, `Project of object do not matches projectId of request`],
    [null, `Key 'project_short_name' is required or Value 'null' is not valid project_id`],
    [undefined, `Key 'project_short_name' is required or Key 'project_id' is required`],
  ])(`shouldn't delete env_var with invalid project_id '%s'`, async (project_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,
        obj_id: newVar,

        project_id,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', `Key 'stage_short_name' is required or Value 'test' is not valid stage_id`],
    [true, `Key 'stage_short_name' is required or Value 'true' is not valid stage_id`],
    [{}, `Key 'stage_short_name' is required or Value '{[]}' is not valid stage_id`],
    [[], `Key 'stage_short_name' is required or Value '' is not valid stage_id`],
    ['', `Key 'stage_short_name' is required or Value '' is not valid stage_id`],
    [1, `Stage of object do not matches stageId of request`],
    [-1, `Value -1 not allowed for stage_id`],
    [0, `Value 0 not allowed for stage_id`],
    [1234, `Stage of object do not matches stageId of request`],
    [null, `Key 'stage_short_name' is required or Value 'null' is not valid stage_id`],
    [undefined, `Key 'stage_short_name' is required or Key 'stage_id' is required`],
  ])(`shouldn't delete env_var with invalid stage_id '%s'`, async (stage_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,
        obj_id: newVar,

        project_id: newProject,
        stage_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', `Stage not found`],
    [
      true,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      {},
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      [],
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      '',
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      -1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      0,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      1234,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      null,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [undefined, `Key 'stage_short_name' is required or Key 'stage_id' is required`],
  ])(`shouldn't delete env_var with invalid stage_short_name '%s'`, async (stage_short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,
        obj_id: newVar,

        project_short_name,
        stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', `Project is not found`],
    [
      true,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      {},
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      [],
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      '',
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      -1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      0,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      1234,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      null,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [undefined, `Key 'project_short_name' is required or Key 'project_id' is required`],
  ])(`shouldn't delete env_var with invalid project_short_name '%s'`, async (project_short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,
        obj_id: newVar,

        project_short_name,
        stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [1234, `Value '1234' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'company_id' is required`],
  ])(`shouldn't list env_var with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Key 'project_short_name' is required or Value 'true' is not valid project_id`],
    [{}, `Key 'project_short_name' is required or Value '{[]}' is not valid project_id`],
    [[], `Key 'project_short_name' is required or Value '' is not valid project_id`],
    ['', `Key 'project_short_name' is required or Value '' is not valid project_id`],
    [-1, `Key 'project_short_name' is required or Value '-1' is not valid project_id`],
    [undefined, `Key 'project_short_name' is required or Key 'project_id' is required`],
  ])(`shouldn't list env_var with invalid project_id '%s'`, async (project_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        project_id,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Key 'stage_short_name' is required or Value 'true' is not valid stage_id`],
    [{}, `Key 'stage_short_name' is required or Value '{[]}' is not valid stage_id`],
    [[], `Key 'stage_short_name' is required or Value '' is not valid stage_id`],
    ['', `Key 'stage_short_name' is required or Value '' is not valid stage_id`],
    [1, `Object stage with id 1 does not exist`],
    [-1, `Object stage with id -1 does not exist`],
    [0, `Object stage with id 0 does not exist`],
    [1234, `Object stage with id 1234 does not exist`],
    [undefined, `Key 'stage_short_name' is required or Key 'stage_id' is required`],
  ])(`shouldn't list env_var with invalid stage_id '%s'`, async (stage_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        project_id: newProject,
        stage_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      true,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      {},
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      [],
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      '',
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      -1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      0,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      1234,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [undefined, `Key 'stage_short_name' is required or Key 'stage_id' is required`],
  ])(`shouldn't list env_var with invalid stage_short_name '%s'`, async (stage_short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        project_short_name,
        stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      true,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      {},
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      [],
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      '',
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      -1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      0,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      1234,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [undefined, `Key 'project_short_name' is required or Key 'project_id' is required`],
  ])(`shouldn't list env_var with invalid project_short_name '%s'`, async (project_short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        project_short_name,
        stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't list env_var with invalid scopes_filter '%s' '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        project_id: newProject,
        stage_id: newStage,
        scopes_filter: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...stringTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't list env_var with invalid scopes_filter:[] '%s' '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.ENV_VAR,
          company_id,

          project_id: newProject,
          stage_id: newStage,
          scopes_filter: [input],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([
    [true, `Value is not valid`],
    [{}, `Value is not valid`],
    [[], `Value is not valid`],
    ['', `Value is not valid`],
    ['test', `Company test does not exists`],
    [1, `Value is not valid`],
    [-1, `Value is not valid`],
    [0, `Value is not valid`],
    [1234, `Value is not valid`],
  ])(`shouldn't modify env_var with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'secret',
        title: `newName`,
        description: `modify`,
        short_name: `modify`,
        value: 'modified_value',
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value is not valid`],
    [{}, `Value is not valid`],
    [[], `Value is not valid`],
    ['', `Value is not valid`],
    ['test', `Value is not valid`],
    [1, `env_var not found`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [0, `env_var not found`],
    [1234, `env_var not found`],
    [null, `Value is not valid`],
    [undefined, `Key 'obj_id' is required`],
  ])(`shouldn't modify env_var with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id,
        company_id,

        env_var_type: 'secret',
        title: `newName`,
        description: `modify`,
        short_name: `modify`,
        value: 'modified_value',
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value is not valid`],
    [{}, `Value is not valid`],
    [[], `Value is not valid`],
    [1, `Value is not valid`],
    [-1, `Value is not valid`],
    [0, `Value is not valid`],
    ['', `Value is not valid`],
    [faker.random.alphaNumeric(256), `Value is not valid`],
  ])(`shouldn't modify env_var with invalid title '%s'`, async (title, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'secret',
        title,
        description: `modify`,
        short_name: `modify`,
        value: 'modified_value',
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      true,
      `Value 'true' is not valid. Type of value is not 'binary' or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [
      {},
      `Value '{[]}' is not valid. Type of value is not 'binary' or Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [
      [],
      `Value '[]' is not valid. Type of value is not 'binary' or Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [
      1,
      `Value '1' is not valid. Type of value is not 'binary' or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [
      -1,
      `Value '-1' is not valid. Type of value is not 'binary' or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [
      0,
      `Value '0' is not valid. Type of value is not 'binary' or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [faker.random.alphaNumeric(2001), `Value is not valid. Value's byte_size is more than maximum allowed: 2000`],
  ])(`shouldn't modify env_var with invalid description '%s'`, async (description, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'secret',
        title: 'test',
        description,
        short_name: `modify`,
        value: 'modified_value',
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([
    [
      null,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      true,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      {},
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      [],
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      'TEST',
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      '',
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      'new_test',
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      -1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      0,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      faker.random.alphaNumeric(128),
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    ['short-name1', `env_var name already exists`],
    [undefined, `Key 'short_name' is required`],
  ])(`shouldn't modify env_var with invalid short_name '%s'`, async (short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'secret',
        title: 'test1',
        description: `modify1`,
        short_name,
        value: 'modified_value',
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [faker.random.alphaNumeric(2097153), `Value is not valid. Value's byte_size is more than maximum allowed: 2097152`],
  ])(`shouldn't modify env_var with invalid value '%s' data_type raw`, async (value, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'secret',
        title: 'test',
        description: `modify`,
        short_name: `modify`,
        value,
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    ['test', `not valid json in value field`],
    ['{test}', `not valid json in value field`],
    [{ a: 1 }, `Value '[{<<\"a\">>,1}]' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't modify env_var with invalid value '%s' data_type json`, async (value, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'secret',
        title: 'test',
        description: `modify`,
        short_name: `modify`,
        value,
        data_type: 'json',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    ['', `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"raw\\\">>,<<\\\"json\\\">>]\">>`],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"raw\\\">>,<<\\\"json\\\">>]\">>`,
    ],
  ])(`shouldn't modify env_var with invalid data_type '%s'`, async (data_type, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'secret',
        title: 'test',
        description: `modify`,
        short_name: `modify`,
        value: 'test',
        data_type,
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'list'`],
    [true, `Value 'true' is not valid. Type of value is not 'list'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'list'`],
    [[], `Key 'scopes.type' is required`],
    [1, `Value '1' is not valid. Type of value is not 'list'`],
    [-1, `Value '-1' is not valid. Type of value is not 'list'`],
    [0, `Value '0' is not valid. Type of value is not 'list'`],
    ['', `Value '' is not valid. Type of value is not 'list'`],
  ])(`shouldn't modify env_var with invalid scopes '%s'`, async (scopes, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'secret',
        title: 'test',
        description: `modify`,
        short_name: `modify`,
        value: 'test',
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([null, true, {}, [], -1, 1, 0, '', undefined])(
    `shouldn't modify env_var with invalid scope.type (secret)'%s'`,
    async type => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.ENV_VAR,
          obj_id: newVar,
          company_id,

          env_var_type: 'secret',
          title: 'test',
          description: `modify`,
          short_name: `modify`,
          value: 'test',
          data_type: 'raw',
          project_id: newProject,
          stage_id: newStage,
          scopes: [{ type, fields: ['cert_pem', 'api_secret_outer'] }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(
        `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`,
      );
    },
  );

  test.each([null, true, {}, [], -1, 1, 0, '', undefined])(
    `shouldn't modify env_var with invalid scope.type (visible) '%s'`,
    async type => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.ENV_VAR,
          obj_id: newVar,
          company_id,

          env_var_type: 'visible',
          title: 'test',
          description: `modify`,
          short_name: `modify`,
          value: 'test',
          data_type: 'raw',
          project_id: newProject,
          stage_id: newStage,
          scopes: [{ type, fields: ['cert_pem', 'api_secret_outer'] }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(
        `Key 'type' is invalid, allowed only *, api_call, set_param, go_if_const, api_copy, db_call, api_rpc, api_rpc_reply, api_code`,
      );
    },
  );

  test.each([
    [null, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [true, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [{}, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [[], `Key 'fields' is invalid, allowed * or not empty array`],
    [['test'], `Key 'fields' is invalid, allowed only '[api_secret_outer,cert_pem,url,extra_headers,response,extra]'`],
    [1, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [-1, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [0, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    ['', `Key 'fields' is invalid, allowed only '*' or list of attributes`],
    [undefined, `Key 'fields' is invalid, allowed only '*' or list of attributes`],
  ])(`shouldn't modify env_var with invalid scope.fields '%s'`, async (fields, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'secret',
        title: 'test',
        description: `modify`,
        short_name: `modify`,
        value: 'test',
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
    [true, `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
    [{}, `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
    [[], `Key 'fields' is invalid, allowed * or not empty array`],
    [['test'], `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
    [1, `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
    [-1, `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
    [0, `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
    ['', `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
    [undefined, `Key 'env_var_type' is invalid, value 'secret' allowed only for types 'api_call', 'api_code'`],
  ])(`shouldn't modify env_var type: '*' with invalid scope.fields '%s'`, async (fields, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'secret',
        title: 'test',
        description: `modify`,
        short_name: `modify`,
        value: 'test',
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: '*', fields }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Key 'type' with value '*' allow with only fields value '*'`],
    [true, `Key 'type' with value '*' allow with only fields value '*'`],
    [{}, `Key 'type' with value '*' allow with only fields value '*'`],
    [[], `Key 'fields' is invalid, allowed * or not empty array`],
    [['test'], `Key 'type' with value '*' allow with only fields value '*'`],
    [1, `Key 'type' with value '*' allow with only fields value '*'`],
    [-1, `Key 'type' with value '*' allow with only fields value '*'`],
    [0, `Key 'type' with value '*' allow with only fields value '*'`],
    ['', `Key 'type' with value '*' allow with only fields value '*'`],
    [undefined, `Key 'type' with value '*' allow with only fields value '*'`],
  ])(`shouldn't modify env_var type: '*' with invalid scope.fields '%s'`, async (fields, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'visible',
        title: 'test',
        description: `modify`,
        short_name: `modify`,
        value: 'test',
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: '*', fields }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Key 'project_short_name' is required or Value 'null' is not valid project_id`],
    [true, `Key 'project_short_name' is required or Value 'true' is not valid project_id`],
    [{}, `Key 'project_short_name' is required or Value '{[]}' is not valid project_id`],
    [[], `Key 'project_short_name' is required or Value '' is not valid project_id`],
    [1, `Project of object do not matches projectId of request`],
    [-1, `Key 'project_short_name' is required or Value '-1' is not valid project_id`],
    [0, `Value 0 not allowed for project_id`],
    ['', `Key 'project_short_name' is required or Value '' is not valid project_id`],
    [undefined, `Key 'project_short_name' is required or Key 'project_id' is required`],
  ])(`shouldn't modify env_var with invalid project_id '%s'`, async (project_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'secret',
        title: 'test',
        description: `modify`,
        short_name: `modify`,
        value: 'test',
        data_type: 'raw',
        project_id,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Key 'stage_short_name' is required or Value 'null' is not valid stage_id`],
    [true, `Key 'stage_short_name' is required or Value 'true' is not valid stage_id`],
    [{}, `Key 'stage_short_name' is required or Value '{[]}' is not valid stage_id`],
    [[], `Key 'stage_short_name' is required or Value '' is not valid stage_id`],
    [1, `Stage of object do not matches stageId of request`],
    [-1, `Value -1 not allowed for stage_id`],
    [0, `Value 0 not allowed for stage_id`],
    ['', `Key 'stage_short_name' is required or Value '' is not valid stage_id`],
    [undefined, `Key 'stage_short_name' is required or Key 'stage_id' is required`],
  ])(`shouldn't modify env_var with invalid stage_id '%s'`, async (stage_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'secret',
        title: 'test',
        description: `modify`,
        short_name: `modify`,
        value: 'test',
        data_type: 'raw',
        project_id: newProject,
        stage_id,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      null,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      true,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      {},
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      [],
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      -1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      0,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [
      '',
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'stage_id' is required`,
    ],
    [undefined, `Key 'stage_short_name' is required or Key 'stage_id' is required`],
  ])(`shouldn't modify env_var with invalid stage_short_name '%s'`, async (stage_short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'secret',
        title: 'test',
        description: `modify`,
        short_name: `modify`,
        value: 'test',
        data_type: 'raw',
        project_short_name,
        stage_short_name,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      null,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      true,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      {},
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      [],
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      -1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      0,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      '',
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [undefined, `Key 'project_short_name' is required or Key 'project_id' is required`],
  ])(`shouldn't modify env_var with invalid project_short_name '%s'`, async (project_short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'secret',
        title: 'test',
        description: `modify`,
        short_name: `modify`,
        value: 'test',
        data_type: 'raw',
        project_short_name,
        stage_short_name,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
