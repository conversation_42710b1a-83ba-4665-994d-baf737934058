import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createVarSchema from '../../../../schemas/v2/env_var/createVar.Schema.json';
import listVarSchema from '../../../../schemas/v2/env_var/listVar.Schema.json';
import listVarVisibleSchema from '../../../../schemas/v2/env_var/listVarVisible.Schema.json';
import deleteVarSchema from '../../../../schemas/v2/env_var/deleteVar.Schema.json';
import modifyVarSchema from '../../../../schemas/v2/env_var/modifyVar.Schema.json';

describe('Env_var (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let newVar: string | number;
  let newVar1: string | number;
  let newVar2: string | number;
  let newVar3: string | number;
  let company_id: any;
  let project_short_name: string;
  let stage_short_name: string;
  let short_name: string;
  let fingerprints: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = `project-${Date.now()}`;
    stage_short_name = `stage-${Date.now()}`;
    short_name = `${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;
  });

  test(`should create env_var without data_type (raw)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name,
        description: 'test',
        title: 'Var',
        value: 'test',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].fingerprints[0].algo).toBe('md5');
    expect(response.body.ops[0].fingerprints[1].algo).toBe('sha256');
    expect(response.body.ops[0].fingerprints[0].value).not.toBeEmpty;
    expect(response.body.ops[0].fingerprints[1].value).not.toBeEmpty;
    newVar = response.body.ops[0].obj_id;
    SchemaValidator.validate(createVarSchema, response.body);
  });

  test(`should list ENV_VAR after create`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].title).toBe('Var');
    expect(response.body.ops[0].list[0].description).toBe('test');
    expect(response.body.ops[0].list[0].short_name).toContain(short_name);
    expect(response.body.ops[0].list[0].obj_id).toBe(newVar);
    expect(response.body.ops[0].list[0].value).toBe(null);
    expect(response.body.ops[0].list[0].data_type).toBe('raw');
    expect(response.body.ops[0].list[0].scopes).toEqual([
      { type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] },
    ]);
    SchemaValidator.validate(listVarSchema, response.body);
  });

  test(`shouldn't create env_var in company`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: `${Date.now()}`,
        description: 'test',
        title: 'Var',
        value: 'test',
        project_id: 0,
        stage_id: 0,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Value 0 not allowed for project_id');
  });

  test(`should modify ENV_VAR without data_type (raw)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'secret',
        title: `newName`,
        description: `modify`,
        short_name: `modify`,
        value: 'modified_value',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].obj_id).toBe(newVar);
    SchemaValidator.validate(modifyVarSchema, response.body);
  });

  test(`should list ENV_VAR after modify`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].title).toBe('newName');
    expect(response.body.ops[0].list[0].description).toBe('modify');
    expect(response.body.ops[0].list[0].short_name).toContain('modify');
    expect(response.body.ops[0].list[0].obj_id).toBe(newVar);
    expect(response.body.ops[0].list[0].data_type).toBe('raw');
    expect(response.body.ops[0].list[0].scopes).toEqual([
      { type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] },
    ]);
    fingerprints = response.body.ops[0].list[0].fingerprints;
    SchemaValidator.validate(listVarSchema, response.body);
  });

  test(`should modify ENV_VAR without data_type/scopes/value (raw)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'secret',
        description: `modify1`,
        short_name: `modify1`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].obj_id).toBe(newVar);
    SchemaValidator.validate(modifyVarSchema, response.body);
  });

  test(`should list ENV_VAR after modify`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].title).toBe('newName');
    expect(response.body.ops[0].list[0].description).toBe('modify1');
    expect(response.body.ops[0].list[0].short_name).toContain('modify');
    expect(response.body.ops[0].list[0].obj_id).toBe(newVar);
    expect(response.body.ops[0].list[0].data_type).toBe('raw');
    expect(response.body.ops[0].list[0].env_var_type).toBe('secret');
    expect(response.body.ops[0].list[0].scopes).toEqual([
      { type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] },
    ]);
    expect(response.body.ops[0].list[0].fingerprints).toEqual(fingerprints);
    SchemaValidator.validate(listVarSchema, response.body);
  });

  test(`should delete ENV_VAR`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].obj_id).toBe(newVar);
    SchemaValidator.validate(deleteVarSchema, response.body);
  });

  test(`should create env_var by short_name (data_type:raw)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: `${Date.now()}`,
        description: 'test',
        title: 'Var',
        value: 'test',
        data_type: 'raw',
        project_short_name,
        stage_short_name,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].fingerprints[0].algo).toBe('md5');
    expect(response.body.ops[0].fingerprints[1].algo).toBe('sha256');
    expect(response.body.ops[0].fingerprints[0].value).not.toBeEmpty;
    expect(response.body.ops[0].fingerprints[1].value).not.toBeEmpty;
    newVar1 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createVarSchema, response.body);
  });

  test(`should list ENV_VAR after create by short_name (data_type:raw)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar1,
        company_id,

        project_short_name,
        stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].title).toBe('Var');
    expect(response.body.ops[0].list[0].description).toBe('test');
    expect(response.body.ops[0].list[0].obj_id).toBe(newVar1);
    expect(response.body.ops[0].list[0].data_type).toBe('raw');
    expect(response.body.ops[0].list[0].scopes).toEqual([
      { type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] },
    ]);
    SchemaValidator.validate(listVarSchema, response.body);
  });

  test(`should modify ENV_VAR by short_name (data_type:raw)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar1,
        company_id,

        env_var_type: 'secret',
        title: `newName`,
        description: `modify`,
        short_name: `modify`,
        value: 'modified_value',
        project_short_name,
        stage_short_name,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].obj_id).toBe(newVar1);
    SchemaValidator.validate(modifyVarSchema, response.body);
  });

  test(`should list ENV_VAR after modify by short_name (data_type:raw)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar1,
        company_id,

        project_short_name,
        stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].title).toBe('newName');
    expect(response.body.ops[0].list[0].description).toBe('modify');
    expect(response.body.ops[0].list[0].short_name).toContain('modify');
    expect(response.body.ops[0].list[0].obj_id).toBe(newVar1);
    expect(response.body.ops[0].list[0].data_type).toBe('raw');
    expect(response.body.ops[0].list[0].scopes).toEqual([
      { type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] },
    ]);
    SchemaValidator.validate(listVarSchema, response.body);
  });

  test(`should delete ENV_VAR by short_name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar1,
        company_id,

        project_short_name,
        stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].obj_id).toBe(newVar1);
    SchemaValidator.validate(deleteVarSchema, response.body);
  });

  test(`should create env_var (data_type JSON)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name,
        description: 'test',
        title: 'Var',
        value: '{"test":1}',
        data_type: 'json',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].fingerprints[0].algo).toBe('md5');
    expect(response.body.ops[0].fingerprints[1].algo).toBe('sha256');
    expect(response.body.ops[0].fingerprints[0].value).not.toBeEmpty;
    expect(response.body.ops[0].fingerprints[1].value).not.toBeEmpty;
    newVar2 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createVarSchema, response.body);
  });

  test(`should not create env_var (data_type JSON) if value not valid json`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name,
        description: 'test',
        title: 'Var',
        value: 'test',
        data_type: 'json',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('not valid json in value field');
  });

  test(`should list ENV_VAR after create`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar2,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].title).toBe('Var');
    expect(response.body.ops[0].list[0].description).toBe('test');
    expect(response.body.ops[0].list[0].short_name).toContain(short_name);
    expect(response.body.ops[0].list[0].obj_id).toBe(newVar2);
    expect(response.body.ops[0].list[0].data_type).toBe('json');
    expect(response.body.ops[0].list[0].scopes).toEqual([
      { type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] },
    ]);
    SchemaValidator.validate(listVarSchema, response.body);
  });

  test(`should modify ENV_VAR data_type raw`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar2,
        company_id,

        env_var_type: 'secret',
        title: `newName`,
        description: `modify`,
        short_name: `modify`,
        value: 'modified_value',
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].obj_id).toBe(newVar2);
    SchemaValidator.validate(modifyVarSchema, response.body);
  });

  test(`should not modify ENV_VAR data_type json if value not valid json`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar2,
        company_id,

        env_var_type: 'secret',
        title: `newName`,
        description: `modify`,
        short_name: `modify`,
        value: 'modified_value',
        data_type: 'json',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('not valid json in value field');
  });

  test(`should list ENV_VAR after modify to raw`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar2,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].title).toBe('newName');
    expect(response.body.ops[0].list[0].description).toBe('modify');
    expect(response.body.ops[0].list[0].short_name).toContain('modify');
    expect(response.body.ops[0].list[0].obj_id).toBe(newVar2);
    expect(response.body.ops[0].list[0].value).toBe(null);
    expect(response.body.ops[0].list[0].data_type).toBe('raw');
    expect(response.body.ops[0].list[0].scopes).toEqual([
      { type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] },
    ]);
    SchemaValidator.validate(listVarSchema, response.body);
  });

  test(`should modify ENV_VAR data_type json`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar2,
        company_id,

        env_var_type: 'secret',
        title: `newName`,
        description: `modify`,
        short_name: `modify`,
        value: '{"test":1}',
        data_type: 'json',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].obj_id).toBe(newVar2);
    SchemaValidator.validate(modifyVarSchema, response.body);
  });

  test(`should modify ENV_VAR without value (data_type raw)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar2,
        company_id,

        env_var_type: 'secret',
        title: `newName1`,
        description: `modify1`,
        short_name: `modify1`,
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].obj_id).toBe(newVar2);
    SchemaValidator.validate(modifyVarSchema, response.body);
  });

  test(`should list ENV_VAR after modify to json`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar2,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].title).toBe('newName1');
    expect(response.body.ops[0].list[0].description).toBe('modify1');
    expect(response.body.ops[0].list[0].short_name).toContain('modify1');
    expect(response.body.ops[0].list[0].obj_id).toBe(newVar2);
    expect(response.body.ops[0].list[0].value).toBe(null);
    expect(response.body.ops[0].list[0].data_type).toBe('raw');
    expect(response.body.ops[0].list[0].scopes).toEqual([
      { type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] },
    ]);
    SchemaValidator.validate(listVarSchema, response.body);
  });

  test(`should list ENV_VAR after modify to visible (don't modify env_var_type)`, async () => {
    const responseModify = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar2,
        company_id,

        env_var_type: 'visible',
        title: `newName1`,
        description: `modify1`,
        short_name: `modify1`,
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(responseModify.status).toBe(200);
    expect(responseModify.body.ops[0].obj_id).toBe(newVar2);
    SchemaValidator.validate(modifyVarSchema, responseModify.body);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar2,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].title).toBe('newName1');
    expect(response.body.ops[0].list[0].description).toBe('modify1');
    expect(response.body.ops[0].list[0].short_name).toContain('modify1');
    expect(response.body.ops[0].list[0].obj_id).toBe(newVar2);
    expect(response.body.ops[0].list[0].value).toBe(null);
    expect(response.body.ops[0].list[0].env_var_type).toBe('secret');
    expect(response.body.ops[0].list[0].data_type).toBe('raw');
    expect(response.body.ops[0].list[0].scopes).toEqual([
      { type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] },
    ]);
    SchemaValidator.validate(listVarSchema, response.body);
  });

  test(`should delete ENV_VAR`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar2,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].obj_id).toBe(newVar2);
    SchemaValidator.validate(deleteVarSchema, response.body);
  });

  test(`should create env_var scopes *,* and env_var_type:'visible'`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'visible',
        short_name,
        description: 'test',
        title: 'Var',
        value: 'test',
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: '*', fields: '*' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].fingerprints[0].algo).toBe('md5');
    expect(response.body.ops[0].fingerprints[1].algo).toBe('sha256');
    expect(response.body.ops[0].fingerprints[0].value).not.toBeEmpty;
    expect(response.body.ops[0].fingerprints[1].value).not.toBeEmpty;
    newVar = response.body.ops[0].obj_id;
    SchemaValidator.validate(createVarSchema, response.body);
  });

  test(`should list ENV_VAR after create visible`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].title).toBe('Var');
    expect(response.body.ops[0].list[0].description).toBe('test');
    expect(response.body.ops[0].list[0].short_name).toContain(short_name);
    expect(response.body.ops[0].list[0].obj_id).toBe(newVar);
    expect(response.body.ops[0].list[0].data_type).toBe('raw');
    expect(response.body.ops[0].list[0].value).toBe('test');
    expect(response.body.ops[0].list[0].scopes).toEqual([{ type: '*', fields: '*' }]);
    SchemaValidator.validate(listVarVisibleSchema, response.body);
  });

  test(`should create env_var visible for set_param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'visible',
        short_name: `${Date.now()}`,
        data_type: 'json',
        description: 'test',
        title: 'Var',
        value: `{\"key\":\"key\"}`,
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'set_param', fields: ['extra'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    newVar3 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createVarSchema, response.body);
  });

  test(`should list ENV_VAR after create for set_param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar3,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ value: '{"key":"key"}' })]),
    );
    SchemaValidator.validate(listVarVisibleSchema, response.body);
  });

  test(`should list ENV_VAR after create with filter_scope (set_param)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        project_id: newProject,
        stage_id: newStage,
        scopes_filter: ['set_param'],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toBeArrayOfSize(2);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ scopes: [{ fields: '*', type: '*' }] })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ scopes: [{ type: 'set_param', fields: ['extra'] }] })]),
    );
    SchemaValidator.validate(listVarVisibleSchema, response.body);
  });

  test(`should list ENV_VAR with filter_scope (*)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        project_id: newProject,
        stage_id: newStage,
        scopes_filter: ['*'],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toBeArrayOfSize(1);
    expect(response.body.ops[0].list[0].scopes).toEqual(
      expect.arrayContaining([expect.objectContaining({ fields: '*', type: '*' })]),
    );
    SchemaValidator.validate(listVarVisibleSchema, response.body);
  });

  test(`should modify ENV_VAR with type api_copy`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'visible',
        title: `newName1`,
        value: `modify`,
        description: `modify1`,
        short_name: `modify1`,
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_copy', fields: '*' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].obj_id).toBe(newVar);
    SchemaValidator.validate(modifyVarSchema, response.body);
  });

  test(`should list ENV_VAR after modify to set_param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ value: 'modify' })]));
    SchemaValidator.validate(listVarVisibleSchema, response.body);
  });

  test(`should list ENV_VAR with filter_scope (api_copy)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        project_id: newProject,
        stage_id: newStage,
        scopes_filter: ['api_copy'],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toBeArrayOfSize(1);
    expect(response.body.ops[0].list[0].scopes).toEqual(
      expect.arrayContaining([expect.objectContaining({ fields: '*', type: 'api_copy' })]),
    );
    SchemaValidator.validate(listVarVisibleSchema, response.body);
  });

  test(`should list ENV_VAR with filter_scope (api_copy or set_param)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        project_id: newProject,
        stage_id: newStage,
        scopes_filter: ['api_copy', 'set_param'],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toBeArrayOfSize(2);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ scopes: [{ fields: ['extra'], type: 'set_param' }] })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ scopes: [{ fields: '*', type: 'api_copy' }] })]),
    );
    SchemaValidator.validate(listVarVisibleSchema, response.body);
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
