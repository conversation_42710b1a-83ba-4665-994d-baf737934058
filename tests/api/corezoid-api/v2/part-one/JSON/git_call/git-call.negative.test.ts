import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import faker from 'faker';
import { requestDeleteObj, requestCreateObj, requestListConv } from '../../../../../../../application/api/ApiObj';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  companyTestCases,
  stringNotValidTestCases,
} from '../../../../../negativeCases';
import { debug } from '../../../../../../../support/utils/logger';

describe('Task (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: number;
  let newNode: string;
  let newNode1: string;
  let company_id: any;
  let obj_id: string;
  const valuesToSkip: any = ['te'];

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    obj_id = faker.random.alphaNumeric(10);

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Process_${Date.now()}`);
    newConv = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, newConv, company_id);
    expect(responseList.status).toBe(200);

    const responseNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_type: 0,
        description: 'test',
        conv_id: newConv,
        title: `Node_${Date.now()}`,
        version: 1,
      }),
    );
    newNode = responseNode.body.ops[0].obj_id;

    const responseNode1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_type: 0,
        description: 'test',
        conv_id: newConv,
        title: `Node_${Date.now()}`,
        version: 1,
      }),
    );
    newNode1 = responseNode1.body.ops[0].obj_id;
  });

  test.each([
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    ['test', `Value is not valid. Value's byte_size is less than minimum allowed: 24`],
    ['i12345678', `Value is not valid. Value's byte_size is less than minimum allowed: 24`],
    [undefined, `Key 'obj_id' is required`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [123, `Value '123' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't modify node git-call in conv with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id,
        company_id,

        conv_id: newConv,
        obj_type: 0,
        title: 'git-call',
        description: 'git-call',
        version: 1,
        logics: [
          {
            type: 'git_call',
            version: 2,
            lang: 'js',
            err_node_id: '',
            code: '',
            repo: '',
            commit: '',
            script: '',
          },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value is not valid`],
    ['test', `Company test does not exists`],
    ['i12345678', `Company i12345678 does not exists`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
    [0, `Value is not valid`],
    [-1, `Value is not valid`],
    [123, `Value is not valid`],
    [1, `Value is not valid`],
    [faker.random.alphaNumeric(37), `Value is not valid`],
  ])(`shouldn't modify node git-call in conv with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,

        conv_id: newConv,
        obj_type: 0,
        title: 'git-call',
        description: 'git-call',
        version: 1,
        logics: [
          {
            type: 'git_call',
            version: 2,
            lang: 'js',
            err_node_id: '',
            code: '',
            repo: '',
            commit: '',
            script: '',
          },
        ],
      }),
    );
    expect(response.status).toBe(200);
    debug(JSON.stringify(response.body));
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([
    [true, `Wrong object reference. Validation error`],
    [null, `Wrong object reference. Validation error`],
    ['test', `Wrong object reference. Validation error`],
    ['i12345678', `Wrong object reference. Validation error`],
    [undefined, `Wrong object reference. Validation error`],
    [[], `Wrong object reference. Validation error`],
    [{}, `Wrong object reference. Validation error`],
    [0, `Object conv with id 0 does not exist`],
    [-1, `Object conv with id -1 does not exist`],
    [123, `Object conv with id 123 does not exist`],
    [1, `Object's company ID does not match company ID in the request`],
  ])(`shouldn't modify node git-call in conv with invalid conv_id '%s'`, async (conv_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,

        conv_id,
        obj_type: 0,
        title: 'git-call',
        description: 'git-call',
        version: 1,
        logics: [
          {
            type: 'git_call',
            version: 2,
            lang: 'js',
            err_node_id: '',
            code: '',
            repo: '',
            commit: '',
            script: '',
          },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[0,1,2,3]\">>`],
    [null, `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[0,1,2,3]\">>`],
    ['test', `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[0,1,2,3]\">>`],
    ['i12345678', `Value '<<\"i12345678\">>' is not valid. Value is not in allowed list <<\"[0,1,2,3]\">>`],
    [undefined, `Key 'obj_type' is required`],
    [[], `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[0,1,2,3]\">>`],
    [{}, `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[0,1,2,3]\">>`],
    [-1, `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[0,1,2,3]\">>`],
    [123, `Value '<<\"123\">>' is not valid. Value is not in allowed list <<\"[0,1,2,3]\">>`],
    [1, `you can't modify this type of node to any type except 'escalation' or 'normal'`],
  ])(`shouldn't modify node git-call in conv with invalid obj_type '%s'`, async (obj_type, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,

        conv_id: newConv,
        obj_type,
        title: 'git-call',
        description: 'git-call',
        version: 1,
        logics: [
          {
            type: 'git_call',
            version: 2,
            lang: 'js',
            err_node_id: '',
            code: '',
            repo: '',
            commit: '',
            script: '',
          },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [123, `Value '123' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [faker.random.alphaNumeric(256), `Value is not valid. Value's byte_size is more than maximum allowed: 255`],
  ])(`shouldn't modify node git-call in conv with invalid title '%s'`, async (title, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,

        conv_id: newConv,
        obj_type: 0,
        title,
        description: 'git-call',
        version: 1,
        logics: [
          {
            type: 'git_call',
            version: 2,
            lang: 'js',
            err_node_id: '',
            code: '',
            repo: '',
            commit: '',
            script: '',
          },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      true,
      `Value 'true' is not valid. Type of value is not 'binary' or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [
      [],
      `Value '[]' is not valid. Type of value is not 'binary' or Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [
      {},
      `Value '{[]}' is not valid. Type of value is not 'binary' or Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [
      0,
      `Value '0' is not valid. Type of value is not 'binary' or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [
      -1,
      `Value '-1' is not valid. Type of value is not 'binary' or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [
      123,
      `Value '123' is not valid. Type of value is not 'binary' or Value '<<\"123\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [
      1,
      `Value '1' is not valid. Type of value is not 'binary' or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[null]\">>`,
    ],
    [faker.random.alphaNumeric(2001), `Value is not valid. Value's byte_size is more than maximum allowed: 2000`],
  ])(`shouldn't modify node git-call in conv with invalid description '%s'`, async (description, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,

        conv_id: newConv,
        obj_type: 0,
        title: 'git-call',
        description,
        version: 1,
        logics: [
          {
            type: 'git_call',
            version: 2,
            lang: 'js',
            err_node_id: '',
            code: '',
            repo: '',
            commit: '',
            script: '',
          },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([
    [true, `Value 'true' is not valid. Type of value is not 'integer'`],
    [null, `Value 'null' is not valid. Type of value is not 'integer'`],
    ['i12345678', `Value 'i12345678' is not valid. Type of value is not 'integer'`],
    [[], `Value '[]' is not valid. Type of value is not 'integer'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'integer'`],
    [0, `There is unconfirmed previous version`],
    [-1, `There is unconfirmed previous version`],
    [123, `There is unconfirmed previous version`],
  ])(`shouldn't modify node git-call in conv with invalid version '%s'`, async (version, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,

        conv_id: newConv,
        obj_type: 0,
        title: 'git-call',
        description: 'git-call',
        version,
        logics: [
          {
            type: 'git_call',
            version: 2,
            lang: 'js',
            err_node_id: '',
            code: '',
            repo: '',
            commit: '',
            script: '',
          },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([true, null, 'test', [], {}, 0, -1, 123])(
    `shouldn't modify node git-call in conv with invalid type '%s'`,
    async type => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: newNode,
          company_id,

          conv_id: newConv,
          obj_type: 0,
          title: 'git-call',
          description: 'git-call',
          version: 1,
          logics: [
            {
              type,
              version: 2,
              lang: 'js',
              err_node_id: '',
              code: '',
              repo: '',
              commit: '',
              script: '',
            },
          ],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual('Unexpected logic type');
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create git_call with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GIT_CALL,
        company_id,
        obj_id: input,

        conv_id: newConv,
        obj_type: 'function_build',
        node_id: input,
        version: 2,
        lang: 'js',
        code: '',
        repo: '',
        commit: '',
        script: '',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create git_call with invalid node_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GIT_CALL,
        company_id,
        obj_id,

        conv_id: newConv,
        obj_type: 'function_build',
        node_id: input,
        version: 2,
        lang: 'js',
        code: '',
        repo: '',
        commit: '',
        script: '',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...companyTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create git_call with invalid company_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GIT_CALL,
        company_id: input,
        obj_id,

        conv_id: newConv,
        obj_type: 'function_build',
        node_id: newNode,
        version: 2,
        lang: 'js',
        code: '',
        repo: '',
        commit: '',
        script: '',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't create git_call with invalid conv_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.GIT_CALL,
          obj_id,
          company_id,

          conv_id: input,
          obj_type: 'function_build',
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: '',
          repo: '',
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create git_call with invalid obj_type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GIT_CALL,
        obj_id,
        company_id,

        conv_id: newConv,
        obj_type: input,
        node_id: newNode,
        version: 2,
        lang: 'js',
        code: '',
        repo: '',
        commit: '',
        script: '',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't create git_call with invalid version '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.GIT_CALL,
          obj_id,
          company_id,

          conv_id: newConv,
          obj_type: 'function_build',
          node_id: newNode,
          version: input,
          lang: 'js',
          code: '',
          repo: '',
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't create git_call with invalid lang '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.GIT_CALL,
          obj_id,
          company_id,

          conv_id: newConv,
          obj_type: 'function_build',
          node_id: newNode,
          version: 2,
          lang: input,
          code: '',
          repo: '',
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't create git_call with invalid code '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.GIT_CALL,
          obj_id,
          company_id,

          conv_id: newConv,
          obj_type: 'function_build',
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: input,
          repo: '',
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't create git_call with invalid repo '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.GIT_CALL,
          obj_id,
          company_id,

          conv_id: newConv,
          obj_type: 'function_build',
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: '',
          repo: input,
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't create git_call with invalid commit '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.GIT_CALL,
          obj_id,
          company_id,

          conv_id: newConv,
          obj_type: 'function_build',
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: '',
          repo: '',
          commit: input,
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't create git_call with invalid script '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.GIT_CALL,
          obj_id,
          company_id,

          conv_id: newConv,
          obj_type: 'function_build',
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: '',
          repo: '',
          commit: '',
          script: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't create git_call with invalid path '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.GIT_CALL,
          obj_id,
          company_id,

          conv_id: newConv,
          obj_type: 'function_build',
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: '',
          repo: '',
          commit: '',
          script: '',
          path: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't create git_call with invalid handler '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.GIT_CALL,
          obj_id,
          company_id,

          conv_id: newConv,
          obj_type: 'function_build',
          node_id: newNode,
          version: 2,
          lang: 'java',
          code: '',
          repo: '',
          commit: '',
          script: '',
          handler: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't get git_call with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.GET,
          obj: OBJ_TYPE.GIT_CALL,
          obj_id: input,

          conv_id: newConv,
          obj_type: 'function_build',
          node_id: newNode,
          version: 2,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't get git_call with invalid conv_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.GET,
          obj: OBJ_TYPE.GIT_CALL,
          obj_id,

          conv_id: input,
          obj_type: 'function_build',
          node_id: newNode,
          version: 2,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't get git_call with invalid node_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.GET,
          obj: OBJ_TYPE.GIT_CALL,
          obj_id,

          conv_id: newConv,
          obj_type: 'function_build',
          node_id: input,
          version: 2,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't get git_call with invalid version '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.GET,
          obj: OBJ_TYPE.GIT_CALL,
          obj_id,

          conv_id: newConv,
          obj_type: 'function_build',
          node_id: newNode,
          version: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't get git_call with invalid obj_type '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.GET,
          obj: OBJ_TYPE.GIT_CALL,
          obj_id,

          conv_id: newConv,
          obj_type: input,
          node_id: newNode,
          version: 2,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't compile git_call with invalid node_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPILE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: 'function',
          node_id: input,
          version: 2,
          lang: 'js',
          code: '',
          repo: '',
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't compile git_call with invalid conv_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPILE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: input,
          obj_type: 'function',
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: '',
          repo: '',
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't compile git_call with invalid obj_type '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPILE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: input,
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: '',
          repo: '',
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't compile git_call with invalid version '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPILE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: 'function',
          node_id: newNode,
          version: input,
          lang: 'js',
          code: '',
          repo: '',
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't compile git_call with invalid lang '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPILE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: 'function',
          node_id: newNode,
          version: 2,
          lang: input,
          code: '',
          repo: '',
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't compile git_call with invalid code '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPILE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: 'function',
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: input,
          repo: '',
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't compile git_call with invalid repo '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPILE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: 'function',
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: '',
          repo: input,
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't compile git_call with invalid commit '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPILE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: 'function',
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: '',
          repo: '',
          commit: input,
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't compile git_call with invalid script '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPILE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: 'function',
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: '',
          repo: '',
          commit: '',
          script: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't compile git_call with invalid path '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPILE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: 'function',
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: '',
          repo: '',
          commit: '',
          script: '',
          path: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't compile git_call with invalid handler '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPILE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: 'function',
          node_id: newNode,
          version: 2,
          lang: 'java',
          code: '',
          repo: '',
          commit: '',
          script: '',
          handler: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't load git_call with invalid node_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LOAD,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: 'function',
          node_id: input,
          version: 2,
          lang: 'js',
          code: '',
          repo: '',
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't load git_call with invalid conv_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LOAD,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: input,
          obj_type: 'function',
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: '',
          repo: '',
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't load git_call with invalid obj_type '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LOAD,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: input,
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: '',
          repo: '',
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't load git_call with invalid version '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LOAD,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: 'function',
          node_id: newNode,
          version: input,
          lang: 'js',
          code: '',
          repo: '',
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't load git_call with invalid lang '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LOAD,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: 'function',
          node_id: newNode,
          version: 2,
          lang: input,
          code: '',
          repo: '',
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't load git_call with invalid code '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LOAD,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: 'function',
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: input,
          repo: '',
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't load git_call with invalid repo '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LOAD,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: 'function',
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: '',
          repo: input,
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't load git_call with invalid commit '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LOAD,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: 'function',
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: '',
          repo: '',
          commit: input,
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't load git_call with invalid script '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LOAD,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: 'function',
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: '',
          repo: '',
          commit: '',
          script: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't load git_call with invalid path '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LOAD,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: 'function',
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: '',
          repo: '',
          commit: '',
          script: '',
          path: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't load git_call with invalid handler '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LOAD,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          obj_type: 'function',
          node_id: newNode,
          version: 2,
          lang: 'java',
          code: '',
          repo: '',
          commit: '',
          script: '',
          handler: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't load git_call with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LOAD,
          obj: OBJ_TYPE.GIT_CALL,
          company_id: input,

          conv_id: newConv,
          obj_type: 'function',
          node_id: newNode,
          version: 2,
          lang: 'js',
          code: '',
          repo: '',
          commit: '',
          script: '',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't reset git_call with invalid conv_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.RESET,
          obj: OBJ_TYPE.GIT_CALL,

          conv_id: input,
          obj_type: 'ssh_key',
          node_id: newNode,
          version: 2,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't reset git_call with invalid node_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.RESET,
          obj: OBJ_TYPE.GIT_CALL,

          conv_id: newConv,
          obj_type: 'ssh_key',
          node_id: input,
          version: 2,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't reset git_call with invalid version '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.RESET,
          obj: OBJ_TYPE.GIT_CALL,

          conv_id: newConv,
          obj_type: 'ssh_key',
          node_id: newNode,
          version: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't reset git_call with invalid obj_type '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.RESET,
          obj: OBJ_TYPE.GIT_CALL,

          conv_id: newConv,
          obj_type: input,
          node_id: newNode,
          version: 2,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't clone git_call with invalid version '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CLONE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          from_node_id: newNode,
          to_node_id: newNode1,
          version: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't clone git_call with invalid conv_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CLONE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: input,
          from_node_id: newNode,
          to_node_id: newNode1,
          version: 2,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't clone git_call with invalid from_node_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CLONE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          from_node_id: input,
          to_node_id: newNode1,
          version: 2,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't clone git_call with invalid to_node_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CLONE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id,

          conv_id: newConv,
          from_node_id: newNode,
          to_node_id: input,
          version: 2,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't clone git_call with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CLONE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id: input,

          conv_id: newConv,
          from_node_id: newNode,
          to_node_id: newNode1,
          version: 2,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);

    const responseConv = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, company_id);
    expect(responseConv.status).toBe(200);
  });
});
