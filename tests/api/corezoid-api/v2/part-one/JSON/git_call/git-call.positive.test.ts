import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import modifyNodeSchema from '../../../../schemas/v2/actions-objects/modifyNode.schema.json';
import createGitcallSchema from '../../../../schemas/v2/git-call/createGitcall.schema.json';
import resetGitcallSchema from '../../../../schemas/v2/git-call/resetGitcall.schema.json';
import getGitcallFSchema from '../../../../schemas/v2/git-call/getGitcallF.schema.json';
import compileGitcallSchema from '../../../../schemas/v2/git-call/compileGitcall.schema.json';
import compileGitcallErrorSchema from '../../../../schemas/v2/git-call/compileGitcallError.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import faker from 'faker';
import { makeRequestWithRetries, MyApiResponse } from '../../../../../../../utils/requestRetries';

describe('Task (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: string | number;
  let companyId: any;
  let newNode: string;
  let newNode1: string;
  const obj_id: string = faker.random.alphaNumeric(10);
  let id: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    companyId = apikey.companies[0].id;

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id: companyId,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: 0,
        conv_type: 'process',
      }),
    );
    newConv = responseConv.body.ops[0].obj_id;

    const responseNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id: companyId,
        obj_type: 0,
        description: 'test',
        conv_id: newConv,
        title: `Node_${Date.now()}`,
        version: 1,
      }),
    );
    newNode = responseNode.body.ops[0].obj_id;
  });

  test.each(['js', 'pyton', 'golang', 'php', 'java', 'clojure', 'prolog', 'lisp', 'dockerfile'])(
    `should modify node conv_id git-call '%s'`,
    async lang => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: newNode,
          company_id: companyId,

          conv_id: newConv,
          obj_type: 0,
          title: 'git-call',
          description: 'git-call',
          version: 1,
          logics: [
            {
              type: 'git_call',
              version: 2,
              lang,
              err_node_id: '',
              code: '',
              repo: '',
              commit: '',
              script: '',
            },
          ],
        }),
      );
      expect(response.status).toBe(200);
      SchemaValidator.validate(modifyNodeSchema, response.body);
    },
  );

  test.each([
    ['js', 'module.exports = (data) => {data.a = 1234; return data;};'],
    ['pyton', 'def handle(data):data["hello"] = "Hello world!"; return data'],
    [
      'golang',
      'package main;import("context";"github.com/corezoid/gitcall-go-runner/gitcall");func usercode(ctx context.Context,data map[string]interface{})error {return nil};func main(){gitcall.Handle(usercode);}',
    ],
    ['php', `<?php function handle($data) {$data['hello'] = \"Hello world!\"; return $data;}`],
    [
      'java',
      'package com.corezoid.usercode; import com.corezoid.gitcall.runner.api.UsercodeHandler; import java.util.Map; public class Usercode implements UsercodeHandler<Map<String, String>, Map<String, String>> {@java.lang.Override public Map<String, String> handle(Map<String, String> data) throws Exception {data.put("hello", "Hello world!"); return data;}}',
    ],
    ['clojure', '(ns usercode.usercode) (defn handle [data] (assoc data :clojure "pass"))'],
    ['prolog', '":- module(usercode, [handle/2]). handle(Data, Result) :- put_dict(prolog, Data, "pass!", Result).'],
    [
      'lisp',
      `(defpackage #:usercode (:use #:cl) (:export :handle)) (in-package #:usercode) (defun handle (data) (setf (gethash 'lisp data) 'pass) data)`,
    ],
    [
      'dockerfile',
      `FROM node:22-alpine as builder\nRUN apk add git\nWORKDIR /app\nRUN git clone https://github.com/corezoid/gitcall-examples .\n\nWORKDIR /app/dockerfile/js\n\nRUN addgroup --gid 501 usercode && \\\n    adduser --disabled-password \\\n    --gecos \"\" \\\n    --shell /usr/sbin/nologin \\\n    --ingroup usercode \\\n    --uid 501 \\\n    usercode\nUSER usercode\n\nENTRYPOINT [\"node\", \"/app/dockerfile/js/src/main.js\"]`,
    ],
  ])(`should create git-call '%s'`, async (lang, code) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GIT_CALL,
        obj_id,
        company_id: companyId,

        conv_id: newConv,
        obj_type: 'function_build',
        node_id: newNode,
        version: 2,
        lang,
        code,
        repo: '',
        commit: '',
        script: '',
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(createGitcallSchema, response.body);
  });

  test.each([
    ['js', 'module.exports = (data) => {data.a = 1234; return data;};'],
    ['pyton', 'def handle(data):data["hello"] = "Hello world!"; return data'],
    [
      'golang',
      'package main;import("context";"github.com/corezoid/gitcall-go-runner/gitcall");func usercode(ctx context.Context,data map[string]interface{})error {return nil};func main(){gitcall.Handle(usercode);}',
    ],
    ['php', `<?php function handle($data) {$data['hello'] = \"Hello world!\"; return $data;}`],
    [
      'java',
      'package com.corezoid.usercode; import com.corezoid.gitcall.runner.api.UsercodeHandler; import java.util.Map; public class Usercode implements UsercodeHandler<Map<String, String>, Map<String, String>> {@java.lang.Override public Map<String, String> handle(Map<String, String> data) throws Exception {data.put("hello", "Hello world!"); return data;}}',
    ],
    ['clojure', '(ns usercode.usercode) (defn handle [data] (assoc data :clojure "pass"))'],
    ['prolog', '":- module(usercode, [handle/2]). handle(Data, Result) :- put_dict(prolog, Data, "pass!", Result).'],
    [
      'lisp',
      `(defpackage #:usercode (:use #:cl) (:export :handle)) (in-package #:usercode) (defun handle (data) (setf (gethash 'lisp data) 'pass) data)`,
    ],
    [
      'dockerfile',
      `FROM node:22-alpine as builder\nRUN apk add git\nWORKDIR /app\nRUN git clone https://github.com/corezoid/gitcall-examples .\n\nWORKDIR /app/dockerfile/js\n\nRUN addgroup --gid 501 usercode && \\\n    adduser --disabled-password \\\n    --gecos \"\" \\\n    --shell /usr/sbin/nologin \\\n    --ingroup usercode \\\n    --uid 501 \\\n    usercode\nUSER usercode\n\nENTRYPOINT [\"node\", \"/app/dockerfile/js/src/main.js\"]`,
    ],
  ])(`should create git-call with path '%s'`, async (lang, code) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GIT_CALL,
        obj_id,
        company_id: companyId,

        conv_id: newConv,
        obj_type: 'function_build',
        node_id: newNode,
        version: 2,
        lang,
        code,
        repo: '',
        commit: '',
        script: '',
        path: '',
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(createGitcallSchema, response.body);
  });

  test.each([
    ['js', 'module.exports = (data) => {data.a = 1234; return data;};'],
    ['pyton', 'def handle(data):data["hello"] = "Hello world!"; return data'],
    [
      'golang',
      'package main;import("context";"github.com/corezoid/gitcall-go-runner/gitcall");func usercode(ctx context.Context,data map[string]interface{})error {return nil};func main(){gitcall.Handle(usercode);}',
    ],
    ['php', `<?php function handle($data) {$data['hello'] = \"Hello world!\"; return $data;}`],
    [
      'java',
      'package com.corezoid.usercode; import com.corezoid.gitcall.runner.api.UsercodeHandler; import java.util.Map; public class Usercode implements UsercodeHandler<Map<String, String>, Map<String, String>> {@java.lang.Override public Map<String, String> handle(Map<String, String> data) throws Exception {data.put("hello", "Hello world!"); return data;}}',
    ],
    ['clojure', '(ns usercode.usercode) (defn handle [data] (assoc data :clojure "pass"))'],
    ['prolog', '":- module(usercode, [handle/2]). handle(Data, Result) :- put_dict(prolog, Data, "pass!", Result).'],
    [
      'lisp',
      `(defpackage #:usercode (:use #:cl) (:export :handle)) (in-package #:usercode) (defun handle (data) (setf (gethash 'lisp data) 'pass) data)`,
    ],
    [
      'dockerfile',
      `FROM node:22-alpine as builder\nRUN apk add git\nWORKDIR /app\nRUN git clone https://github.com/corezoid/gitcall-examples .\n\nWORKDIR /app/dockerfile/js\n\nRUN addgroup --gid 501 usercode && \\\n    adduser --disabled-password \\\n    --gecos \"\" \\\n    --shell /usr/sbin/nologin \\\n    --ingroup usercode \\\n    --uid 501 \\\n    usercode\nUSER usercode\n\nENTRYPOINT [\"node\", \"/app/dockerfile/js/src/main.js\"]`,
    ],
  ])(`should compile git-call '%s'`, async (lang, code) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPILE,
        obj: OBJ_TYPE.GIT_CALL,
        company_id: companyId,

        conv_id: newConv,
        obj_type: 'function',
        node_id: newNode,
        version: 2,
        lang,
        code,
        repo: '',
        commit: '',
        script: '',
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(compileGitcallErrorSchema, response.body);
  });

  test.each([
    ['js', 'module.exports = (data) => {data.a = 1234; return data;};'],
    ['pyton', 'def handle(data):data["hello"] = "Hello world!"; return data'],
    [
      'golang',
      'package main;import("context";"github.com/corezoid/gitcall-go-runner/gitcall");func usercode(ctx context.Context,data map[string]interface{})error {return nil};func main(){gitcall.Handle(usercode);}',
    ],
    ['php', `<?php function handle($data) {$data['hello'] = \"Hello world!\"; return $data;}`],
    [
      'java',
      'package com.corezoid.usercode; import com.corezoid.gitcall.runner.api.UsercodeHandler; import java.util.Map; public class Usercode implements UsercodeHandler<Map<String, String>, Map<String, String>> {@java.lang.Override public Map<String, String> handle(Map<String, String> data) throws Exception {data.put("hello", "Hello world!"); return data;}}',
    ],
    ['clojure', '(ns usercode.usercode) (defn handle [data] (assoc data :clojure "pass"))'],
    ['prolog', '":- module(usercode, [handle/2]). handle(Data, Result) :- put_dict(prolog, Data, "pass!", Result).'],
    [
      'lisp',
      `(defpackage #:usercode (:use #:cl) (:export :handle)) (in-package #:usercode) (defun handle (data) (setf (gethash 'lisp data) 'pass) data)`,
    ],
    [
      'dockerfile',
      `FROM node:22-alpine as builder\nRUN apk add git\nWORKDIR /app\nRUN git clone https://github.com/corezoid/gitcall-examples .\n\nWORKDIR /app/dockerfile/js\n\nRUN addgroup --gid 501 usercode && \\\n    adduser --disabled-password \\\n    --gecos \"\" \\\n    --shell /usr/sbin/nologin \\\n    --ingroup usercode \\\n    --uid 501 \\\n    usercode\nUSER usercode\n\nENTRYPOINT [\"node\", \"/app/dockerfile/js/src/main.js\"]`,
    ],
  ])(`should compile git-call with path '%s'`, async (lang, code) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPILE,
        obj: OBJ_TYPE.GIT_CALL,
        company_id: companyId,

        conv_id: newConv,
        obj_type: 'function',
        node_id: newNode,
        version: 2,
        lang,
        code,
        repo: '',
        commit: '',
        script: '',
        path: '',
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(compileGitcallErrorSchema, response.body);
  });

  test.each([
    ['js', 'module.exports = (data) => {data.a = 1234; return data;};'],
    ['pyton', 'def handle(data):data["hello"] = "Hello world!"; return data'],
    [
      'golang',
      'package main;import("context";"github.com/corezoid/gitcall-go-runner/gitcall");func usercode(ctx context.Context,data map[string]interface{})error {return nil};func main(){gitcall.Handle(usercode);}',
    ],
    ['php', `<?php function handle($data) {$data['hello'] = \"Hello world!\"; return $data;}`],
    [
      'java',
      'package com.corezoid.usercode; import com.corezoid.gitcall.runner.api.UsercodeHandler; import java.util.Map; public class Usercode implements UsercodeHandler<Map<String, String>, Map<String, String>> {@java.lang.Override public Map<String, String> handle(Map<String, String> data) throws Exception {data.put("hello", "Hello world!"); return data;}}',
    ],
    ['clojure', '(ns usercode.usercode) (defn handle [data] (assoc data :clojure "pass"))'],
    ['prolog', '":- module(usercode, [handle/2]). handle(Data, Result) :- put_dict(prolog, Data, "pass!", Result).'],
    [
      'lisp',
      `(defpackage #:usercode (:use #:cl) (:export :handle)) (in-package #:usercode) (defun handle (data) (setf (gethash 'lisp data) 'pass) data)`,
    ],
    [
      'dockerfile',
      `FROM node:22-alpine as builder\nRUN apk add git\nWORKDIR /app\nRUN git clone https://github.com/corezoid/gitcall-examples .\n\nWORKDIR /app/dockerfile/js\n\nRUN addgroup --gid 501 usercode && \\\n    adduser --disabled-password \\\n    --gecos \"\" \\\n    --shell /usr/sbin/nologin \\\n    --ingroup usercode \\\n    --uid 501 \\\n    usercode\nUSER usercode\n\nENTRYPOINT [\"node\", \"/app/dockerfile/js/src/main.js\"]`,
    ],
  ])(`should Load git-call '%s'`, async (lang, code) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LOAD,
        obj: OBJ_TYPE.GIT_CALL,
        company_id: companyId,

        conv_id: newConv,
        obj_type: 'function',
        node_id: newNode,
        version: 2,
        lang,
        code,
        repo: '',
        commit: '',
        script: '',
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(compileGitcallErrorSchema, response.body);
  });

  test.each([
    ['js', 'module.exports = (data) => {data.a = 1234; return data;};'],
    ['pyton', 'def handle(data):data["hello"] = "Hello world!"; return data'],
    [
      'golang',
      'package main;import("context";"github.com/corezoid/gitcall-go-runner/gitcall");func usercode(ctx context.Context,data map[string]interface{})error {return nil};func main(){gitcall.Handle(usercode);}',
    ],
    ['php', `<?php function handle($data) {$data['hello'] = \"Hello world!\"; return $data;}`],
    [
      'java',
      'package com.corezoid.usercode; import com.corezoid.gitcall.runner.api.UsercodeHandler; import java.util.Map; public class Usercode implements UsercodeHandler<Map<String, String>, Map<String, String>> {@java.lang.Override public Map<String, String> handle(Map<String, String> data) throws Exception {data.put("hello", "Hello world!"); return data;}}',
    ],
    ['clojure', '(ns usercode.usercode) (defn handle [data] (assoc data :clojure "pass"))'],
    ['prolog', '":- module(usercode, [handle/2]). handle(Data, Result) :- put_dict(prolog, Data, "pass!", Result).'],
    [
      'lisp',
      `(defpackage #:usercode (:use #:cl) (:export :handle)) (in-package #:usercode) (defun handle (data) (setf (gethash 'lisp data) 'pass) data)`,
    ],
    [
      'dockerfile',
      `FROM node:22-alpine as builder\nRUN apk add git\nWORKDIR /app\nRUN git clone https://github.com/corezoid/gitcall-examples .\n\nWORKDIR /app/dockerfile/js\n\nRUN addgroup --gid 501 usercode && \\\n    adduser --disabled-password \\\n    --gecos \"\" \\\n    --shell /usr/sbin/nologin \\\n    --ingroup usercode \\\n    --uid 501 \\\n    usercode\nUSER usercode\n\nENTRYPOINT [\"node\", \"/app/dockerfile/js/src/main.js\"]`,
    ],
  ])(`should Load git-call with path '%s'`, async (lang, code) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LOAD,
        obj: OBJ_TYPE.GIT_CALL,
        company_id: companyId,

        conv_id: newConv,
        obj_type: 'function',
        node_id: newNode,
        version: 2,
        lang,
        code,
        repo: '',
        commit: '',
        script: '',
        path: '',
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(compileGitcallErrorSchema, response.body);
  });

  test(`should get git-call function`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.GIT_CALL,

        conv_id: newConv,
        obj_type: 'function',
        node_id: newNode,
        version: 2,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(getGitcallFSchema, response.body);
  });

  test.skip(`should get git-call function_build - build_status`, async () => {
    const responseCreate = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GIT_CALL,
        obj_id,
        company_id: companyId,

        conv_id: newConv,
        obj_type: 'function_build',
        node_id: newNode,
        version: 2,
        lang: 'js',
        code: 'module.exports = (data) => {data.a = 1234; return data;};',
        repo: '',
        commit: '',
        script: '',
      }),
    );
    expect(responseCreate.status).toBe(200);
    id = responseCreate.body.ops[0].obj_id;
    SchemaValidator.validate(createGitcallSchema, responseCreate.body);
    await new Promise(r => setTimeout(r, 40000));

    await new Promise(r => setTimeout(r, 4000));
    function checkConditions(response: MyApiResponse): boolean {
      return response.body.ops[0].build_status === 'done';
    }
    const response = await makeRequestWithRetries(
      async () => {
        return await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.GET,
            obj: OBJ_TYPE.GIT_CALL,
            obj_id: id,
            company_id: companyId,

            conv_id: newConv,
            obj_type: 'function_build',
            node_id: newNode,
            version: 2,
          }),
        );
      },
      checkConditions,
      20,
    );
    expect(response.status).toBe(200);
  });

  test.skip(`should compile git-call after function_build`, async () => {
    await new Promise(r => setTimeout(r, 4000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPILE,
        obj: OBJ_TYPE.GIT_CALL,
        company_id: companyId,

        conv_id: newConv,
        obj_type: 'function',
        node_id: newNode,
        version: 2,
        lang: 'js',
        code: 'module.exports = (data) => {data.a = 1234; return data;};',
        repo: '',
        commit: '',
        script: '',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_type).toBe('function');
    SchemaValidator.validate(compileGitcallSchema, response.body);
  });

  test.skip(`should Load git-call after function_build`, async () => {
    await new Promise(r => setTimeout(r, 4000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LOAD,
        obj: OBJ_TYPE.GIT_CALL,
        company_id: companyId,

        conv_id: newConv,
        obj_type: 'function',
        node_id: newNode,
        version: 2,
        lang: 'js',
        code: 'module.exports = (data) => {data.a = 1234; return data;};',
        repo: '',
        commit: '',
        script: '',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_type).toBe('function');
    SchemaValidator.validate(compileGitcallSchema, response.body);
  });

  test(`should clone git-call`, async () => {
    const responseNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id: companyId,
        obj_type: 0,
        description: 'test',
        conv_id: newConv,
        title: `Node_${Date.now()}`,
        version: 1,
      }),
    );
    newNode1 = responseNode.body.ops[0].obj_id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CLONE,
        obj: OBJ_TYPE.GIT_CALL,
        company_id: companyId,

        conv_id: newConv,
        from_node_id: newNode,
        to_node_id: newNode1,
        version: 2,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
  });

  test(`should reset git-call ssh_key`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESET,
        obj: OBJ_TYPE.GIT_CALL,
        company_id: companyId,

        conv_id: newConv,
        obj_type: 'ssh_key',
        node_id: newNode,
        version: 2,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('git_call');
    SchemaValidator.validate(resetGitcallSchema, response.body);
  });

  test(`should get git-call ssh_key`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.GIT_CALL,
        company_id: companyId,

        conv_id: newConv,
        obj_type: 'ssh_key',
        node_id: newNode,
        version: 2,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('git_call');
    SchemaValidator.validate(resetGitcallSchema, response.body);
  });

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id: companyId,
      }),
    );
  });
});
