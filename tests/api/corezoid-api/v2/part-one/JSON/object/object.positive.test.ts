import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import getSchema from '../../../../schemas/v2/actions-objects/getObjectScheme.schema.json';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';

describe('Object Get (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let newConv: string | number;
  let newFolder: string | number;
  let newDash: string | number;
  let newGroup: string | number;
  let newVersion: string | number;
  let newAlias: string | number;
  let newInst: string | number;
  let company_id: any;
  let project_short_name: string;
  let stage_short_name: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = `project-${Date.now()}`;
    stage_short_name = `stage-${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;

    const responseConvProject = await requestCreateObj(
      api,
      OBJ_TYPE.CONV,
      company_id,
      `Conv_${Date.now()}`,
      newStage,
      'process',
      newProject,
      newStage,
    );
    newConv = responseConvProject.body.ops[0].obj_id;

    const responseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,

        title: `Folder_${Date.now()}`,
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    newFolder = responseFolder.body.ops[0].obj_id;

    const responseDash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        project_id: newProject,
        stage_id: newStage,
        title: `Dash_${Date.now()}`,
      }),
    );
    newDash = responseDash.body.ops[0].obj_id;

    const responseAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        short_name: `${Date.now()}`,
        title: 'Alias2',
        description: 'test',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    newAlias = responseAlias.body.ops[0].obj_id;

    const responseGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,

        title: `Group_${Date.now()}`,
        obj_type: 'admins',
      }),
    );
    newGroup = responseGroup.body.ops[0].obj_id;

    const responseVersion = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn: `${Date.now()}`,
        stage_id: newStage,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    expect(responseVersion.status).toBe(200);
    expect(responseVersion.body.ops[0].obj).toBe('version');
    newVersion = responseVersion.body.ops[0].obj_id;

    const responseInst = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        instance_type: `db_call`,
        folder_id: newFolder,
        title: `Instance_${Date.now()}`,
        data: {
          driver: `postgres`,
          host: `COR-6938.middleware.loc`,
          port: `5432`,
          username: `postgres`,
          password: `pass`,
          ssl: false,
          database: `dbcall`,
          timeoutMs: 30000,
        },
      }),
    );
    expect(responseInst.status).toBe(200);
    expect(responseInst.body.ops[0].obj).toBe('instance');
    newInst = responseInst.body.ops[0].obj_id;
  });

  test(`should get Object (conv)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        company_id,
        obj_id: newConv,

        obj_type: 'conv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(getSchema, response.body);
  });

  test(`should get Object (conv) string`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        company_id,
        obj_id: `${newConv}`,

        obj_type: 'conv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(getSchema, response.body);
  });

  test(`should get Object (folder) string`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: `${newFolder}`,

        obj_type: 'folder',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(getSchema, response.body);
  });

  test(`should get Object (dashboard)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: newDash,

        obj_type: 'dashboard',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(getSchema, response.body);
  });

  test(`should get Object (alias)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: newAlias,

        obj_type: 'alias',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(getSchema, response.body);
  });

  test(`should get Object (stage)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: newStage,

        obj_type: 'stage',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(getSchema, response.body);
  });

  test(`should get Object (project)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: newProject,

        obj_type: 'project',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(getSchema, response.body);
  });

  test(`should get Object (group)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: newGroup,

        obj_type: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(getSchema, response.body);
  });

  test(`should get Object (instance)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: newInst,

        obj_type: 'instance',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(getSchema, response.body);
  });

  test(`should get Object (version)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: newVersion,

        obj_type: 'version',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(getSchema, response.body);
  });

  afterAll(async () => {
    const responseProject = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(responseProject.status).toBe(200);
  });
});
