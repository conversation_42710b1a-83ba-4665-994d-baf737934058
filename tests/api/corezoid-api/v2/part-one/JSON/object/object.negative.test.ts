import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';
import {
  stringTestCases,
  undefinedTestCase,
  stringNotValidTestCases,
  securityTestCases,
} from '../../../../../negativeCases';

describe('Get Oblect (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let newConv: string | number;
  let company_id: any;
  let stage_short_name: string;
  let project_short_name: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    stage_short_name = `stage-${Date.now()}`;
    project_short_name = `project-${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
        project_short_name: `test`,
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;

    const responseConvProject = await requestCreateObj(
      api,
      OBJ_TYPE.CONV,
      company_id,
      `Conv_${Date.now()}`,
      newStage,
      'process',
      newProject,
      newStage,
    );
    newConv = responseConvProject.body.ops[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        project_id: newProject,
        stage_id: newStage,
        title: `Dash_${Date.now()}`,
      }),
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        short_name: `modify`,
        title: 'Alias2',
        description: 'test',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't get object with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: input,
        obj_type: 'conv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [
      ...stringTestCases,
      ...stringNotValidTestCases,
      ...undefinedTestCase,
      ...securityTestCases,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't get object with invalid obj_type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: newConv,
        obj_type: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  afterAll(async () => {
    const responseProject = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(responseProject.status).toBe(200);
  });
});
