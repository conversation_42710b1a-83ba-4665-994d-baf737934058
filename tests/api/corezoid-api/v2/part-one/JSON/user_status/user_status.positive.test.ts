import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { User } from '../../../../../../../infrastructure/model/User';
import { ApiUserClient } from '../../../../../../../application/api/ApiUserClient';
import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import modifyUserStatusSchema from '../../../../schemas/v2/user_status/modifyUserStatusSchema.json';
import { createAuthUser, Method } from '../../../../../../../utils/request';

describe('User_status (positive)', () => {
  let company_id: any;
  let apiS: ApiKeyClient;
  let apiSuper: ApiKey;
  let token: string;
  let host: string;
  let hostSS: string;
  let login1: string;
  let user: User;
  let cookieUser: any;
  let userID1: string | number;
  let conv_id1: string | number;
  let conv_id2: string | number;
  let userToken: any;

  let apiUserToken: ApiUserClient;
  let apiUserСookie: ApiUserClient;
  let user0: User;

  beforeAll(async () => {
    await ConfigurationManager.getConfiguration().initialize();
    const config = ConfigurationManager.getConfiguration();
    host = config.getApiUrl();
    hostSS = config.getSSUrl();
    apiSuper = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apiSuper);
    token = await application.createToken(0);

    user0 = await application.getAuthorizedUser();
    apiUserToken = await application.getApiUserClient(user0);

    user = await application.getAuthorizedUser({ company: {} }, 2);
    cookieUser = createAuthUser(user.cookieUser, 'cookie');
    apiUserСookie = await application.getApiUserClient(user);
    userToken = createAuthUser(token, 'token');

    const responseMe = await cookieUser.request({
      method: Method.POST,
      url: `${host}auth/me`,
      data: { get: 'me' },
    });
    expect(responseMe.status).toBe(200);
    expect(responseMe.data.status).toBe('actived');
    userID1 = responseMe.data.user_id;
    login1 = responseMe.data.login;

    const responseCreateCompany = await apiUserToken.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,
        name: 'Corezoid',
        description: 'company',
        site: 'corezoid.com',
      }),
    );
    expect(responseCreateCompany.status).toBe(200);
    expect(responseCreateCompany.body.ops[0].proc).toBe('ok');
    company_id = responseCreateCompany.body.ops[0].obj_id;

    const responseCreateInvite = await apiUserToken.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        login: login1,
        login_type: 'google',
        company_id,
      }),
    );
    expect(responseCreateInvite.status).toBe(200);
    expect(responseCreateInvite.body.ops[0].proc).toBe('ok');
    expect(responseCreateInvite.body.ops[0].obj).toBe('invite');

    await new Promise(r => setTimeout(r, 3000));

    const responseMeSS = await cookieUser.request({
      method: Method.GET,
      url: `${hostSS}face/api/1/users/me`,
    });
    expect(responseMeSS.status).toBe(200);

    await new Promise(r => setTimeout(r, 4000));

    const responseCreateConv1 = await apiUserСookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        conv_type: 'process',
        obj_type: 0,
        status: 'active',
        title: 'NewProcessUser1',
        description: '',
        folder_id: 0,
        company_id,
      }),
    );
    expect(responseCreateConv1.status).toBe(200);
    expect(responseCreateConv1.body.ops[0].proc).toBe('ok');
    conv_id1 = responseCreateConv1.body.ops[0].obj_id;

    const responseCreateConv2 = await apiUserСookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        conv_type: 'process',
        obj_type: 0,
        status: 'active',
        title: 'NewProcessUser1',
        description: '',
        folder_id: 0,
        company_id: null,
      }),
    );
    expect(responseCreateConv2.status).toBe(200);
    expect(responseCreateConv2.body.ops[0].proc).toBe('ok');
    conv_id2 = responseCreateConv2.body.ops[0].obj_id;
  });

  test('should create requests for modify user status in Corezoid', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id: userID1,
        status: 'blocked',
        blocked_reason: 'test',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].status).toBe('blocked');
    SchemaValidator.validate(modifyUserStatusSchema, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseMeCor = await cookieUser.request({
      method: Method.POST,
      url: `${host}auth/me`,
      data: { get: 'me' },
    });
    expect(responseMeCor.status).toBe(200);
    expect(responseMeCor.data.login).toBe(login1);
    expect(responseMeCor.data.status).toBe('blocked');

    const responseListFolder = await apiUserСookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
      }),
    );
    expect(responseListFolder.status).toBe(403);
    expect(responseListFolder.body.ops[0].proc).toBe('error');
    expect(responseListFolder.body.ops[0].description).toBe('user blocked, please contact support');

    const responseShowConv1Block = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv_id1,
      }),
    );
    expect(responseShowConv1Block.status).toBe(200);
    expect(responseShowConv1Block.body.ops[0].status).toBe('blocked');

    const responseShowConv2NBlock = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id2,
      }),
    );
    expect(responseShowConv2NBlock.status).toBe(200);
    expect(responseShowConv2NBlock.body.ops[0].status).toBe('blocked');

    const responseActive = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id: userID1,
        status: 'actived',
        blocked_reason: 'test',
      }),
    );
    expect(responseActive.status).toBe(200);
    expect(responseActive.body.ops[0].status).toBe('actived');
    SchemaValidator.validate(modifyUserStatusSchema, responseActive.body);

    await new Promise(r => setTimeout(r, 8000));

    const responseListFolder1 = await apiUserСookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
      }),
    );
    expect(responseListFolder1.status).toBe(200);
    expect(responseListFolder1.body.ops[0].proc).toBe('ok');

    const responseShowConv1Active = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv_id1,
      }),
    );
    expect(responseShowConv1Active.status).toBe(200);
    expect(responseShowConv1Active.body.ops[0].status).toBe('active');

    const responseShowConv2Active = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id2,
      }),
    );
    expect(responseShowConv2Active.status).toBe(200);
    expect(responseShowConv2Active.body.ops[0].status).toBe('active');
  });

  test('should create requests for modify user status in company', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id: userID1,
        status: 'blocked',
        blocked_reason: 'test',
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].status).toBe('blocked');
    SchemaValidator.validate(modifyUserStatusSchema, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseListFolder = await apiUserСookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.ops[0].proc).toBe('error');
    expect(responseListFolder.body.ops[0].description).toBe('User not in company');

    const responseShowConv1Block = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv_id1,
      }),
    );
    expect(responseShowConv1Block.status).toBe(200);
    expect(responseShowConv1Block.body.ops[0].status).toBe('blocked');

    const responseListFolderMyCorezoid = await apiUserСookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id: null,
        obj_id: 0,
      }),
    );
    expect(responseListFolderMyCorezoid.status).toBe(200);
    expect(responseListFolderMyCorezoid.body.ops[0].proc).toBe('ok');

    const responseShowConv2NBlock = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id2,
      }),
    );
    expect(responseShowConv2NBlock.status).toBe(200);
    expect(responseShowConv2NBlock.body.ops[0].status).toBe('active');

    const responseActive = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id: userID1,
        status: 'actived',
        blocked_reason: 'test',
        company_id,
      }),
    );
    expect(responseActive.status).toBe(200);
    expect(responseActive.body.ops[0].status).toBe('actived');
    SchemaValidator.validate(modifyUserStatusSchema, responseActive.body);

    await new Promise(r => setTimeout(r, 8000));

    const responseListFolder1 = await apiUserСookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
      }),
    );
    expect(responseListFolder1.status).toBe(200);
    expect(responseListFolder1.body.ops[0].proc).toBe('ok');

    const responseShowConv1Active = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv_id1,
      }),
    );
    expect(responseShowConv1Active.status).toBe(200);
    expect(responseShowConv1Active.body.ops[0].status).toBe('active');

    const responseShowConv2Active = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id2,
      }),
    );
    expect(responseShowConv2Active.status).toBe(200);
    expect(responseShowConv2Active.body.ops[0].status).toBe('active');
  });

  test('should create requests for modify user status in Corezoid ("obj_type":"user")', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id: userID1,
        status: 'blocked',
        blocked_reason: 'test',
        obj_type: 'user',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].status).toBe('blocked');
    SchemaValidator.validate(modifyUserStatusSchema, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseMeCor = await cookieUser.request({
      method: Method.POST,
      url: `${host}auth/me`,
      data: { get: 'me' },
    });
    expect(responseMeCor.status).toBe(200);
    expect(responseMeCor.data.login).toBe(login1);
    expect(responseMeCor.data.status).toBe('blocked');

    const responseListFolder = await apiUserСookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
      }),
    );
    expect(responseListFolder.status).toBe(403);
    expect(responseListFolder.body.ops[0].proc).toBe('error');
    expect(responseListFolder.body.ops[0].description).toBe('user blocked, please contact support');

    const responseShowConv1Block = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv_id1,
      }),
    );
    expect(responseShowConv1Block.status).toBe(200);
    expect(responseShowConv1Block.body.ops[0].status).toBe('active');

    const responseShowConv2NBlock = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id2,
      }),
    );
    expect(responseShowConv2NBlock.status).toBe(200);
    expect(responseShowConv2NBlock.body.ops[0].status).toBe('active');

    const responseActive = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id: userID1,
        status: 'actived',
        blocked_reason: 'test',
        obj_type: 'user',
      }),
    );
    expect(responseActive.status).toBe(200);
    expect(responseActive.body.ops[0].status).toBe('actived');
    SchemaValidator.validate(modifyUserStatusSchema, responseActive.body);

    await new Promise(r => setTimeout(r, 8000));

    const responseListFolder1 = await apiUserСookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
      }),
    );
    expect(responseListFolder1.status).toBe(200);
    expect(responseListFolder1.body.ops[0].proc).toBe('ok');

    const responseShowConv1Active = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv_id1,
      }),
    );
    expect(responseShowConv1Active.status).toBe(200);
    expect(responseShowConv1Active.body.ops[0].status).toBe('active');

    const responseShowConv2Active = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id2,
      }),
    );
    expect(responseShowConv2Active.status).toBe(200);
    expect(responseShowConv2Active.body.ops[0].status).toBe('active');
  });

  test('should create requests for modify user status in Corezoid ("obj_type":"process")', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id: userID1,
        status: 'blocked',
        blocked_reason: 'test',
        obj_type: 'process',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].status).toBe('blocked');
    SchemaValidator.validate(modifyUserStatusSchema, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseMeCor = await cookieUser.request({
      method: Method.POST,
      url: `${host}auth/me`,
      data: { get: 'me' },
    });
    expect(responseMeCor.status).toBe(200);
    expect(responseMeCor.data.login).toBe(login1);
    expect(responseMeCor.data.status).toBe('actived');

    const responseListFolder = await apiUserСookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.ops[0].proc).toBe('ok');

    const responseShowConv1Block = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv_id1,
      }),
    );
    expect(responseShowConv1Block.status).toBe(200);
    expect(responseShowConv1Block.body.ops[0].status).toBe('blocked');

    const responseShowConv2NBlock = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id2,
      }),
    );
    expect(responseShowConv2NBlock.status).toBe(200);
    expect(responseShowConv2NBlock.body.ops[0].status).toBe('blocked');

    const responseActive = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id: userID1,
        status: 'actived',
        blocked_reason: 'test',
        obj_type: 'process',
      }),
    );
    expect(responseActive.status).toBe(200);
    expect(responseActive.body.ops[0].status).toBe('actived');
    SchemaValidator.validate(modifyUserStatusSchema, responseActive.body);

    await new Promise(r => setTimeout(r, 8000));

    const responseListFolder1 = await apiUserСookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
      }),
    );
    expect(responseListFolder1.status).toBe(200);
    expect(responseListFolder1.body.ops[0].proc).toBe('ok');

    const responseShowConv1Active = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv_id1,
      }),
    );
    expect(responseShowConv1Active.status).toBe(200);
    expect(responseShowConv1Active.body.ops[0].status).toBe('active');

    const responseShowConv2Active = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id2,
      }),
    );
    expect(responseShowConv2Active.status).toBe(200);
    expect(responseShowConv2Active.body.ops[0].status).toBe('active');
  });

  afterAll(async () => {
    const responseUser = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id: userID1,
        status: 'actived',
        blocked_reason: 'test',
      }),
    );
    expect(responseUser.status).toBe(200);

    const responseConv = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id1,
      }),
    );
    expect(responseConv.status).toBe(200);

    const responseConv2 = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id2,
      }),
    );
    expect(responseConv2.status).toBe(200);

    const response = await userToken.request({
      method: Method.DELETE,
      url: `${hostSS}face/api/1/workspaces/${company_id}`,
    });
    expect(response.status).toBe(200);
  });
});
