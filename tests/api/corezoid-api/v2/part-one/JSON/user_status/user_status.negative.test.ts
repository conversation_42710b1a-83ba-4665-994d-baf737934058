import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import faker from 'faker';

describe('User_status (negative)', () => {
  let apiS: ApiKeyClient;
  let apiSuper: ApiKey;
  let company_id: any;
  let newApiKey: string | number;

  beforeAll(async () => {
    apiSuper = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apiSuper);

    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('user');
    newApiKey = response.body.ops[0].users[0].obj_id;
  });

  test.each([
    [0, `User 0 not found.`],
    [-1, `User -1 not found.`],
    [true, `Value is not valid`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
    ['test', `Value is not valid`],
    ['', `Value is not valid`],
    [null, `Value is not valid`],
    [undefined, `Key 'obj_id' is required`],
    [faker.random.alphaNumeric(256), `Value is not valid`],
  ])(`should not modify user_status with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id,
        company_id,
        status: 'actived',
        blocked_reason: 'test',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      0,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"blocked\\\">>,<<\\\"actived\\\">>]\">>`,
    ],
    [
      -1,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"blocked\\\">>,<<\\\"actived\\\">>]\">>`,
    ],
    [
      true,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"blocked\\\">>,<<\\\"actived\\\">>]\">>`,
    ],
    [
      [],
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"blocked\\\">>,<<\\\"actived\\\">>]\">>`,
    ],
    [
      {},
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"blocked\\\">>,<<\\\"actived\\\">>]\">>`,
    ],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"blocked\\\">>,<<\\\"actived\\\">>]\">>`,
    ],
    ['', `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"blocked\\\">>,<<\\\"actived\\\">>]\">>`],
    [
      null,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"blocked\\\">>,<<\\\"actived\\\">>]\">>`,
    ],
    [undefined, `Key 'status' is required`],
  ])(`should not modify user_status with invalid status '%s'`, async (status, reason) => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id: newApiKey,
        company_id,
        status,
        blocked_reason: 'test',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'blocked_reason' is required`],
    [faker.random.alphaNumeric(2001), `Value is not valid. Value's byte_size is more than maximum allowed: 2000`],
  ])(`should not modify user_status with invalid blocked_reason '%s'`, async (blocked_reason, reason) => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id: newApiKey,
        company_id,
        status: 'actived',
        blocked_reason,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [0, `Value is not valid`],
    [-1, `Value is not valid`],
    ['', `Value is not valid`],
    ['test', `User not in company`],
    [true, `Value is not valid`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
  ])(`should not modify user_status with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id: newApiKey,
        company_id,
        status: 'actived',
        blocked_reason: 'test',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    ['', `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"user\\\">>,<<\\\"process\\\">>]\">>`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"user\\\">>,<<\\\"process\\\">>]\">>`,
    ],
  ])(`should not modify user_status with invalid obj_type '%s'`, async (obj_type, reason) => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.USER_STATUS,
        obj_id: newApiKey,
        company_id,
        status: 'actived',
        blocked_reason: 'test',
        obj_type,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  afterAll(async () => {
    const responseDel = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        company_id,
        obj_id: newApiKey,
      }),
    );
    expect(responseDel.status).toBe(200);
  });
});
