import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import faker from 'faker';
import { requestDeleteObj } from '../../../../../../../application/api/ApiObj';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  maxLength,
  stringNotValidTestCases,
  minLength,
  companyTestCases,
  boolTestCases,
  arrayTestCases,
} from '../../../../../negativeCases';

describe('Stage (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let valuesToSkip: any;
  let newProject: string | number;
  let newStage: string | number;
  let newGroup: string | number;
  let company_id: any;
  let stage_short_name: string;
  let project_short_name: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    stage_short_name = `stage-${Date.now()}`;
    project_short_name = `project-${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;

    const responseCreate = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        title: 'titleForCompany',
        obj_type: 'admins',
      }),
    );
    expect(responseCreate.status).toBe(200);
    expect(responseCreate.body.ops[0].obj).toBe('group');
    newGroup = responseCreate.body.ops[0].obj_id;
  });

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't create stage  with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.STAGE,
          company_id: input,

          title: `Stage_${Date.now()}`,
          short_name: `stage-${Date.now()}`,
          description: 'test',
          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't create stage  with invalid project_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.STAGE,
          company_id,

          title: `Stage_${Date.now()}`,
          short_name: `stage-${Date.now()}`,
          description: 'test',
          project_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [null];

  test.each(
    [...stringTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't create stage  with invalid description '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: `stage-${Date.now()}`,
        description: input,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([faker.random.alphaNumeric(2001)])(
    `shouldn't shouldn't create stage  with invalid description '%s'`,
    async input => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.STAGE,
          company_id,

          title: `Stage_${Date.now()}`,
          short_name: `stage-${Date.now()}`,
          description: input,
          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toContain(
        `Value is not valid. Value's byte_size is more than maximum allowed: 2000`,
      );
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...minLength, ...maxLength].map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't create stage  with invalid short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: input,
        description: 'test',
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...stringTestCases, ...undefinedTestCase, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't create stage  with invalid title '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.STAGE,
          company_id,

          title: input,
          short_name: stage_short_name,
          description: 'test',
          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...maxLength, ...minLength].map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't create stage  with invalid project_short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: input,
        short_name: stage_short_name,
        description: 'test',
        project_short_name: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...boolTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't create stage  with invalid immutable '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.STAGE,
          company_id,

          title: input,
          short_name: stage_short_name,
          description: 'test',
          project_short_name,
          immutable: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...boolTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't create stage  with invalid update_not_uniq_short_name '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.STAGE,
          company_id,

          title: input,
          short_name: stage_short_name,
          description: 'test',
          project_short_name,
          update_not_uniq_short_name: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't modify stage  with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.STAGE,
          obj_id: newStage,
          company_id: input,
          title: `StageModify`,
          short_name: `stage-modify`,
          description: 'testModify',
          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases, ...maxLength].map(({ input, errors }) => [
      input,
      errors,
    ]),
  )(`shouldn't shouldn't modify stage  with invalid obj_short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.STAGE,
        company_id,

        obj_short_name: input,
        title: `StageModify`,
        short_name: `stage-modify`,
        description: 'testModify',
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...integerTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't modify stage  with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.STAGE,
        obj_id: input,
        company_id,
        title: `StageModify`,
        short_name: `stage-modify`,
        description: 'testModify',
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0];

  test.each(
    [...integerTestCases, ...stringNotValidTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't modify stage  with invalid project_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        company_id,
        title: `StageModify`,
        short_name: `stage-modify`,
        description: 'testModify',
        project_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...stringTestCases, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't modify stage  with invalid title '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.STAGE,
          obj_id: newStage,
          company_id,
          title: input,
          short_name: `stage-modify`,
          description: 'testModify',
          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...maxLength, ...minLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't modify stage  with invalid short_name '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.STAGE,
          obj_id: newStage,
          company_id,
          title: `stage-modify`,
          short_name: input,
          description: 'testModify',
          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [null];

  test.each(
    [...stringTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't modify stage  with invalid description '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        company_id,
        title: `stage-modify`,
        short_name: `stage-modify`,
        description: input,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([faker.random.alphaNumeric(2001)])(
    `shouldn't shouldn't modify stage  with invalid description '%s'`,
    async input => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: newStage,

          title: `Stage1_${Date.now()}`,
          short_name: `stage1-${Date.now()}`,
          description: input,
          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toContain(
        `Value is not valid. Value's byte_size is more than maximum allowed: 2000`,
      );
    },
  );

  test.each(
    [...integerTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't show stage  with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAGE,
        obj_id: input,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't show stage  with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.STAGE,
          obj_id: newStage,
          company_id: input,
          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0];

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't show stage  with invalid project_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        company_id,
        project_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't show stage with invalid project_short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        company_id,
        project_short_name: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't list stage with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: input,

          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't delete stage  with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.STAGE,
          company_id: input,
          obj_id: newStage,

          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't list stage  with invalid project_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        company_id,
        obj_id: newStage,

        project_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...stringTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't list stage  with invalid filter '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: newStage,

          project_id: newProject,
          filter: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't list stage  with invalid sort '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: newStage,

          project_id: newProject,
          filter: 'conv',
          sort: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't list stage  with invalid list_obj '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: newStage,

          project_id: newProject,
          list_obj: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't list stage  with invalid obj_short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        company_id,

        project_id: newProject,
        obj_short_name: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't list stage  with invalid project_short_name '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.STAGE,
          company_id,

          project_short_name: input,
          obj_short_name: stage_short_name,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't link stage  with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: input,

          project_id: newProject,
          obj_to: 'group',
          obj_to_id: newGroup,
          privs: [{ type: 'create', list_obj: ['all'] }],
          is_need_to_notify: true,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't link stage  with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.STAGE,
          company_id: input,
          obj_id: newStage,

          project_id: newProject,
          obj_to: 'group',
          obj_to_id: newGroup,
          privs: [{ type: 'create', list_obj: ['all'] }],
          is_need_to_notify: true,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't link stage  with invalid project_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.STAGE,
        company_id,
        obj_id: newStage,

        project_id: input,
        obj_to: 'group',
        obj_to_id: newGroup,
        privs: [{ type: 'create', list_obj: ['all'] }],
        is_need_to_notify: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't link stage  with invalid obj_to '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.STAGE,
        company_id,
        obj_id: newStage,

        project_id: newProject,
        obj_to: input,
        obj_to_id: newGroup,
        privs: [{ type: 'create', list_obj: ['all'] }],
        is_need_to_notify: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't link stage  with invalid obj_to_id (group) '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: newStage,

          project_id: newProject,
          obj_to: 'group',
          obj_to_id: input,
          privs: [{ type: 'create', list_obj: ['all'] }],
          is_need_to_notify: true,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't link stage  with invalid obj_to_id (user) '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: newStage,

          project_id: newProject,
          obj_to: 'user',
          obj_to_id: input,
          privs: [{ type: 'create', list_obj: ['all'] }],
          is_need_to_notify: true,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...arrayTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't link stage  with invalid privs '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: newStage,

          project_id: newProject,
          obj_to: 'group',
          obj_to_id: newGroup,
          privs: input,
          is_need_to_notify: true,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't link stage  with invalid privs.type '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: newStage,

          project_id: newProject,
          obj_to: 'group',
          obj_to_id: newGroup,
          privs: [{ type: input, list_obj: ['all'] }],
          is_need_to_notify: true,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...arrayTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't link stage  with invalid privs.list_obj '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: newStage,

          project_id: newProject,
          obj_to: 'group',
          obj_to_id: newGroup,
          privs: [{ type: 'create', list_obj: input }],
          is_need_to_notify: true,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...boolTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't link stage  with invalid is_need_to_notify '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: newStage,

          project_id: newProject,
          obj_to: 'group',
          obj_to_id: newGroup,
          privs: [],
          is_need_to_notify: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't delete stage with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: input,

          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't delete stage  with invalid project_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        obj_id: newStage,

        project_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't delete stage  with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.STAGE,
          company_id: input,
          obj_id: newStage,

          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't delete stage  with invalid obj_short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        project_id: newProject,
        obj_short_name: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't restore stage  with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.RESTORE,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: input,

          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't restore stage  with invalid project_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        obj_id: newStage,

        project_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't restore stage  with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.RESTORE,
          obj: OBJ_TYPE.STAGE,
          company_id: input,
          obj_id: newStage,

          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't restore stage  with invalid obj_short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        project_id: newProject,
        obj_short_name: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't destroy stage  with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DESTROY,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: input,

          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't destroy stage  with invalid project_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.STAGE,
        company_id,
        obj_id: newStage,

        project_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't destroy stage  with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DESTROY,
          obj: OBJ_TYPE.STAGE,
          company_id: input,
          obj_id: newStage,

          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't destroy stage  with invalid obj_short_name '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.STAGE,
        company_id,

        project_id: newProject,
        obj_short_name: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(response.status).toBe(200);
  });
});
