import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import { requestDeleteObj } from '../../../../../../../application/api/ApiObj';
import createSchema from '../../../../schemas/v2/project/createStage.schema.json';
import modifySchema from '../../../../schemas/v2/project/modifyStage.schema.json';
import listCSchema from '../../../../schemas/v2/project/listStageConv.schema.json';
import listFSchema from '../../../../schemas/v2/project/listStageFolder.schema.json';
import listDSchema from '../../../../schemas/v2/project/listStageDashboard.schema.json';
import listGSchema from '../../../../schemas/v2/project/listStageGroup.schema.json';
import listUnSchema from '../../../../schemas/v2/project/listStageUncommited.schema.json';
import listPtFSchema from '../../../../schemas/v2/project/listPtoFStage.schema.json';
import linkSchema from '../../../../schemas/v2/project/linkStage.schema.json';
import deleteSchema from '../../../../schemas/v2/project/deleteStage.schema.json';
import destroySchema from '../../../../schemas/v2/project/destroyStage.schema.json';

describe('Project (positive)', () => {
  let api: ApiKeyClient;
  let apiS: ApiKeyClient;
  let apikey: ApiKey;
  let apiSuper: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let newStageIm: string | number;
  let company_id: any;
  let newConv: number;
  let newConv2: string | number;
  let newApiKey: string | number;
  let newGroup: string | number;
  let nodeLogic: string;
  let project_short_name: string;
  let stage_short_name: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    apiSuper = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apiSuper);
    (project_short_name = `project-${Date.now()}`), (stage_short_name = `stage-${Date.now()}`);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = response.body.ops[0].obj_id;

    const responseApiKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(responseApiKey.status).toBe(200);
    newApiKey = responseApiKey.body.ops[0].users[0].obj_id;

    const responseCreate = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        title: 'titleForCompany',
        obj_type: 'admins',
      }),
    );
    expect(responseCreate.status).toBe(200);
    expect(responseCreate.body.ops[0].obj).toBe('group');
    newGroup = responseCreate.body.ops[0].obj_id;
  });

  test(`should create Stage (immutable true)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_short_name,
        immutable: true,
        folder_id: newProject,
        update_not_uniq_short_name: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    newStageIm = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        project_id: newProject,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[0].uniq_undeployed_versions).toBe(0);
    expect(responseList.body.ops[0].list[0].uniq_undeployed_versions_detail).toBeArray;
    expect(responseList.body.ops[0].list[0].undeployed).toBe(0);
    expect(responseList.body.ops[0].list[0].obj_type).toBe('stage');
    expect(responseList.body.ops[0].list[0].obj_id).toBe(newStageIm);
    expect(responseList.body.ops[0].list[0].immutable).toBe(true);
  });

  test(`should create Stage (only required param)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,

        title: `Stage_${Date.now()}`,
        short_name: `stage-${Date.now()}`,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    newStage = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        project_id: newProject,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[1].uniq_undeployed_versions).toBe(0);
    expect(responseList.body.ops[0].list[1].uniq_undeployed_versions_detail).toBeArray;
    expect(responseList.body.ops[0].list[1].undeployed).toBe(0);
    expect(responseList.body.ops[0].list[1].obj_type).toBe('stage');
    expect(responseList.body.ops[0].list[1].obj_id).toBe(newStage);
    expect(responseList.body.ops[0].list[1].immutable).toBe(false);
  });

  test(`should list Stage obj_id number (only required parameter)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    expect(response.body.ops[0].obj_id).toBe(newStage);
    expect(response.body.ops[0].list).toBeArrayOfSize(0);
  });

  test(`should list path_to_folder obj_id stage immutable false`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PATH_TO_FOLDER,
        obj_id: newStage,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('path_to_folder');
    expect(response.body.ops[0].list[2].immutable).toBe(false);
    SchemaValidator.validate(listPtFSchema, response.body);
  });

  test(`should modify Stage obj_id number`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        company_id,

        title: `StageModify`,
        short_name: `stage-modify`,
        description: 'testModify',
        project_id: newProject,
        immutable: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    SchemaValidator.validate(modifySchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        project_id: newProject,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[1].obj_type).toBe('stage');
    expect(responseList.body.ops[0].list[1].obj_id).toBe(newStage);
    expect(responseList.body.ops[0].list[1].title).toBe('StageModify');
    expect(responseList.body.ops[0].list[1].short_name).toBe('stage-modify');
    expect(responseList.body.ops[0].list[1].description).toBe('testModify');
    expect(responseList.body.ops[0].list[1].immutable).toBe(true);
  });

  test(`should list path_to_folder obj_id stage immutable true`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PATH_TO_FOLDER,
        obj_id: newStage,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('path_to_folder');
    expect(response.body.ops[0].list[2].immutable).toBe(true);
    SchemaValidator.validate(listPtFSchema, response.body);
  });

  test(`should modify Stage obj_short_name/project_short_name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `StageModify`,
        obj_short_name: `stage-modify`,
        short_name: `stage-modify1`,
        description: 'testModify',
        project_short_name,
        immutable: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    SchemaValidator.validate(modifySchema, response.body);
  });

  test(`should modify Stage obj_id string (only required parameters)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.STAGE,
        obj_id: `${newStage}`,

        immutable: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    SchemaValidator.validate(modifySchema, response.body);
  });

  test('should show Stage', async () => {
    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id: company_id,
        title: `CONV_${Date.now()}`,
        folder_id: newStage,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    newConv = responseConv.body.ops[0].obj_id;

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id: company_id,
      }),
    );
    nodeLogic = responseList.body.ops[0].list[1].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeLogic,
        company_id: company_id,

        conv_id: newConv,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'POST',
            url: undefined,
          },
          { node_title: 'final', format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );

    const responseConv2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id: company_id,
        title: `CONV_${Date.now()}`,
        folder_id: newStage,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    newConv2 = responseConv2.body.ops[0].obj_id;

    const responseList2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv2,
        company_id: company_id,
      }),
    );
    nodeLogic = responseList2.body.ops[0].list[1].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeLogic,
        company_id: company_id,

        conv_id: newConv2,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'POST',
            url: undefined,
          },
        ],
        semaphors: [],
        version: 23,
      }),
    );

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        company_id: company_id,
        project_id: newProject,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].uniq_undeployed_versions).toBe(2);
    expect(responseShow.body.ops[0].uniq_undeployed_versions_detail).toBeArray;
    expect(responseShow.body.ops[0].undeployed).toBe(2);
    expect(responseShow.body.ops[0].uniq_undeployed_versions_detail).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]),
    );
    expect(responseShow.body.ops[0].uniq_undeployed_versions_detail).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv2 })]),
    );
    expect(responseShow.body.ops[0].uniq_undeployed_versions_detail).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_type: 'conv' })]),
    );
    expect(responseShow.body.ops[0].uniq_undeployed_versions_detail).toEqual(
      expect.arrayContaining([expect.objectContaining({ version: 23 })]),
    );
  });

  test(`should show Stage obj_id string (only required parameter)`, async () => {
    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAGE,
        obj_id: `${newStage}`,
        project_short_name,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].uniq_undeployed_versions).toBe(2);
    expect(responseShow.body.ops[0].uniq_undeployed_versions_detail).toBeArray;
    expect(responseShow.body.ops[0].undeployed).toBe(2);
    expect(responseShow.body.ops[0].uniq_undeployed_versions_detail).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]),
    );
    expect(responseShow.body.ops[0].uniq_undeployed_versions_detail).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv2 })]),
    );
    expect(responseShow.body.ops[0].uniq_undeployed_versions_detail).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_type: 'conv' })]),
    );
    expect(responseShow.body.ops[0].uniq_undeployed_versions_detail).toEqual(
      expect.arrayContaining([expect.objectContaining({ version: 23 })]),
    );
  });

  test(`should list Stage obj_id string`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        obj_id: `${newStage}`,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    expect(response.body.ops[0].list).toBeArrayOfSize(2);
    expect(response.body.ops[0].list[0].obj_type).toBe('conv');
    expect(response.body.ops[0].list[1].obj_type).toBe('conv');
    SchemaValidator.validate(listCSchema, response.body);
  });

  test(`should list Stage with filter folder`, async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id: company_id,
        title: `folder_${Date.now()}`,
        folder_id: newStage,
        project_id: newProject,
        stage_id: newStage,
      }),
    );

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        obj_id: `${newStage}`,
        company_id,
        project_id: newProject,
        filter: 'folder',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    expect(response.body.ops[0].list).toBeArrayOfSize(1);
    expect(response.body.ops[0].list[0].obj_type).toBe('folder');
    SchemaValidator.validate(listFSchema, response.body);
  });

  test(`should list Stage filter conveyor`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        company_id,
        project_id: newProject,
        filter: 'conveyor',
        sort: 'date',
        order: 'asc',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    expect(response.body.ops[0].list).toBeArrayOfSize(2);
    expect(response.body.ops[0].list[0].obj_type).toBe('conv');
    expect(response.body.ops[0].list[1].obj_type).toBe('conv');
    SchemaValidator.validate(listCSchema, response.body);
  });

  test(`should list Stage with filter dashboard`, async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id: company_id,
        title: `folder_${Date.now()}`,
        folder_id: newStage,
        project_id: newProject,
        stage_id: newStage,
      }),
    );

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        obj_id: `${newStage}`,
        company_id,
        project_id: newProject,
        filter: 'dashboard',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    expect(response.body.ops[0].list).toBeArrayOfSize(1);
    expect(response.body.ops[0].list[0].obj_type).toBe('dashboard');
    SchemaValidator.validate(listDSchema, response.body);
  });

  test(`should list Stage with filter shared`, async () => {
    const responseFalderS = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id: company_id,
        title: 'folderS',
        folder_id: newStage,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseFalderS.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        obj_id: `${newStage}`,
        company_id,
        project_id: newProject,
        filter: 'shared',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    expect(response.body.ops[0].list).toBeArrayOfSize(1);
    expect(response.body.ops[0].list[0].obj_type).toBe('folder');
    SchemaValidator.validate(listFSchema, response.body);
  });

  test(`should list Stage with filter my`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        obj_id: `${newStage}`,
        company_id,
        project_id: newProject,
        filter: 'my',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    expect(response.body.ops[0].list).toBeArrayOfSize(4);
  });

  test(`should list Stage with list_obj uncommited`, async () => {
    const responseModifyConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeLogic,
        company_id: company_id,

        conv_id: newConv2,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'POST',
            url: undefined,
          },
        ],
        semaphors: [],
        version: 23,
      }),
    );
    expect(responseModifyConv.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        company_id,
        project_id: newProject,
        list_obj: 'uncommited',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    expect(response.body.ops[0].list).toBeArrayOfSize(2);
    expect(response.body.ops[0].list[0].obj_type).toBe('conv');
    expect(response.body.ops[0].list[1].obj_type).toBe('conv');
    SchemaValidator.validate(listUnSchema, response.body);
  });

  test(`should link Stage to group (obj_id number)`, async () => {
    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.STAGE,
        company_id,
        obj_id: newStage,

        obj_to: 'group',
        obj_to_id: newGroup,
        privs: [
          { type: 'create', list_obj: ['all'] },
          { type: 'modify', list_obj: ['all'] },
        ],
      }),
    );
    expect(responseLink.status).toBe(200);
    expect(responseLink.body.request_proc).toBe('ok');
    SchemaValidator.validate(linkSchema, responseLink.body);
  });

  test(`should list Stage with list_obj group (linked group)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        company_id,
        project_id: newProject,
        list_obj: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    expect(response.body.ops[0].list).toBeArrayOfSize(1);
    expect(response.body.ops[0].list[0].obj).toBe('group');
    SchemaValidator.validate(listGSchema, response.body);
  });

  test(`should link Stage to user (obj_id string)`, async () => {
    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.STAGE,
        company_id,
        obj_id: `${newStage}`,

        obj_to: 'user',
        obj_to_id: newApiKey,
        privs: [{ type: 'create', list_obj: ['all'] }],
        is_need_to_notify: true,
      }),
    );
    expect(responseLink.status).toBe(200);
    expect(responseLink.body.request_proc).toBe('ok');
    SchemaValidator.validate(linkSchema, responseLink.body);
  });

  test(`should list Stage with list_obj group (linked api_user)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        company_id,
        project_id: newProject,
        list_obj: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    expect(response.body.ops[0].list).toBeArrayOfSize(2);
    expect(response.body.ops[0].list[0].obj).toBe('group');
    expect(response.body.ops[0].list[1].obj).toBe('user');
    SchemaValidator.validate(listGSchema, response.body);
  });

  test(`should unlink Stage`, async () => {
    const responseLinkG = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        company_id,
        obj_to: 'group',
        obj_to_id: newGroup,
        privs: [],
      }),
    );
    expect(responseLinkG.status).toBe(200);
    expect(responseLinkG.body.ops[0].action_type).toBe('unlink');
    SchemaValidator.validate(linkSchema, responseLinkG.body);

    const responseLinkU = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.STAGE,
        company_id,
        obj_id: `${newStage}`,
        obj_to: 'user',
        obj_to_id: newApiKey,
        privs: [],
        is_need_to_notify: false,
      }),
    );
    expect(responseLinkU.status).toBe(200);
    expect(responseLinkU.body.ops[0].action_type).toBe('unlink');
    SchemaValidator.validate(linkSchema, responseLinkU.body);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        company_id,
        project_id: newProject,
        list_obj: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    expect(response.body.ops[0].list).toBeArrayOfSize(0);
  });

  test(`should link Stage to user (obj_short_name/project_short_name)`, async () => {
    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.STAGE,
        company_id,

        obj_short_name: stage_short_name,
        project_short_name,
        obj_to: 'user',
        obj_to_id: newApiKey,
        privs: [{ type: 'create', list_obj: ['all'] }],
        is_need_to_notify: true,
      }),
    );
    expect(responseLink.status).toBe(200);
    expect(responseLink.body.request_proc).toBe('ok');
    SchemaValidator.validate(linkSchema, responseLink.body);
  });

  test(`should list Stage with list_obj group (obj_short_name/project_short_name)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        company_id,
        obj_short_name: stage_short_name,
        project_short_name,
        list_obj: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    expect(response.body.ops[0].list).toBeArrayOfSize(1);
    expect(response.body.ops[0].list[0].obj).toBe('user');
    SchemaValidator.validate(listGSchema, response.body);
  });

  test('should delete stage number', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newStage);
    expect(response.body.ops[0].obj).toBe('stage');
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should delete stage obj_short_name/project_short_name', async () => {
    const responseM = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStageIm,
        company_id,

        immutable: false,
      }),
    );
    expect(responseM.status).toBe(200);
    expect(responseM.body.ops[0].obj).toBe('stage');

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        project_short_name,
        obj_short_name: stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newStageIm);
    expect(response.body.ops[0].obj).toBe('stage');
    SchemaValidator.validate(deleteSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        project_id: newProject,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list).toEqual([]);
  });

  test('should restore stage number', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newStage);
    expect(response.body.ops[0].obj).toBe('stage');
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should restore stage obj_short_name/project_short_name', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        project_short_name,
        obj_short_name: stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newStageIm);
    expect(response.body.ops[0].obj).toBe('stage');
    SchemaValidator.validate(deleteSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        project_id: newProject,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list).toBeArrayOfSize(2);
  });

  test('should delete stage string (only required parameter)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.STAGE,
        obj_id: `${newStage}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newStage);
    expect(response.body.ops[0].obj).toBe('stage');
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should restore stage string (only required parameter)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.STAGE,
        obj_id: `${newStage}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newStage);
    expect(response.body.ops[0].obj).toBe('stage');
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should destroy stage number', async () => {
    const responseDel = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
      }),
    );
    expect(responseDel.status).toBe(200);
    expect(responseDel.body.ops[0].obj_id).toBe(newStage);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toBe('stage');
    expect(response.body.ops[0].obj_id).toBe(newStage);
    SchemaValidator.validate(destroySchema, response.body);
  });

  test('should destroy stage obj_short_name', async () => {
    const responseDel = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStageIm,
      }),
    );
    expect(responseDel.status).toBe(200);
    expect(responseDel.body.ops[0].obj_id).toBe(newStageIm);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.STAGE,
        project_id: newProject,
        obj_short_name: stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toBe('stage');
    expect(response.body.ops[0].obj_id).toBe(newStageIm);
    SchemaValidator.validate(destroySchema, response.body);
  });

  test('should destroy stage string (only required param)', async () => {
    const responseC = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,

        title: `Stage1_${Date.now()}`,
        short_name: `stage1-${Date.now()}`,
        project_id: newProject,
      }),
    );
    expect(responseC.status).toBe(200);
    newStage = responseC.body.ops[0].obj_id;

    const responseDel = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        project_id: newProject,
      }),
    );
    expect(responseDel.status).toBe(200);
    expect(responseDel.body.ops[0].obj_id).toBe(newStage);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.STAGE,
        obj_id: `${newStage}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toBe('stage');
    expect(response.body.ops[0].obj_id).toBe(newStage);
    SchemaValidator.validate(destroySchema, response.body);
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(response.status).toBe(200);
  });
});
