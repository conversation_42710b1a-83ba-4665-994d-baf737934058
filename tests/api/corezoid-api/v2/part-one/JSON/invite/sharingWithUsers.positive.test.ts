import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import linkSchema from '../../../../schemas/v2/actions-objects/linkObject.schema.json';
import listLinkSchema from '../../../../schemas/v2/actions-objects/listLinkObject.schema.json';
import createSchema from '../../../../schemas/v2/users-groups/createInviteSchema2.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('Sharing (positive)', () => {
  let api: ApiKeyClient;
  let newApi: ApiKeyClient;
  let apikey: ApiKey;
  let newFolder: string | number;
  let Folder2: string | number;
  let newConv: string | number;
  let newDasboard: string | number;
  let ApiKey1: string | number;
  let ApiKey2: string | number;
  let Conv2: string | number;
  let invite_hash: string | number;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    ApiKey1 = +apikey.id;
    company_id = apikey.companies[0].id;
    const createFolderResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: 0,
        obj_type: 0,
        status: 'active',
        tite: `Folder_${Date.now()}`,
      }),
    );
    expect(createFolderResponse.status).toBe(200);
    newFolder = createFolderResponse.body.ops[0].obj_id;
    const createFolder2Response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: newFolder,
        obj_type: 0,
        status: 'active',
        tite: `Folder2_${Date.now()}`,
      }),
    );
    expect(createFolder2Response.status).toBe(200);
    Folder2 = createFolder2Response.body.ops[0].obj_id;
    const createConvResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: newFolder,
        create_mode: 'without_nodes',
        tite: `Process_${Date.now()}`,
      }),
    );
    expect(createConvResponse.status).toBe(200);
    newConv = createConvResponse.body.ops[0].obj_id;
    const createConv2Response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: newFolder,
        create_mode: 'without_nodes',
        tite: `Process2_${Date.now()}`,
      }),
    );
    expect(createConv2Response.status).toBe(200);
    Conv2 = createConv2Response.body.ops[0].obj_id;
    const createDashboardResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        folder_id: newFolder,
        tite: `Dashboard_${Date.now()}`,
        time_range: { select: 'online', start: 1597847260, stop: 1597847260, timezone_offset: -180 },
      }),
    );
    expect(createDashboardResponse.status).toBe(200);
    newDasboard = createDashboardResponse.body.ops[0].obj_id;
  });

  test(`should create invite`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        company_id,

        login: '<EMAIL>',
        login_type: 'google',
        link_to_obj: 'conv',
        link_to_obj_id: Conv2,
        is_need_to_notify: false,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('invite');
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`should link`, async () => {
    const CreateKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(200);
    const user = CreateKeyResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newApi = application.getApiKeyClient(newApiKey);
    ApiKey2 = +newApiKey.id;
    const ResponseLinkFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: Folder2,
        company_id,

        obj_to: 'user',
        obj_to_id: newApiKey.id,
        is_need_to_notify: false,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(ResponseLinkFolder.status).toBe(200);
    expect(ResponseLinkFolder.body.ops[0].obj_type).toBe('folder');
    expect(ResponseLinkFolder.body.ops[0].action_type).toBe('link');
    SchemaValidator.validate(linkSchema, ResponseLinkFolder.body);
    const ResponseLinkConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,

        obj_to: 'user',
        obj_to_id: newApiKey.id,
        is_need_to_notify: false,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
        ],
      }),
    );
    expect(ResponseLinkConv.status).toBe(200);
    expect(ResponseLinkConv.body.ops[0].obj_type).toBe('conv');
    expect(ResponseLinkConv.body.ops[0].action_type).toBe('link');
    SchemaValidator.validate(linkSchema, ResponseLinkConv.body);
    const ResponseLinkConv2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: Conv2,
        company_id,

        obj_to: 'user',
        obj_to_id: newApiKey.id,
        is_need_to_notify: false,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
        ],
      }),
    );
    expect(ResponseLinkConv2.status).toBe(200);
    expect(ResponseLinkConv2.body.ops[0].obj_type).toBe('conv');
    expect(ResponseLinkConv2.body.ops[0].action_type).toBe('link');
    SchemaValidator.validate(linkSchema, ResponseLinkConv2.body);
    const ResponseLinkDashboard = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDasboard,
        company_id,

        obj_to: 'user',
        obj_to_id: newApiKey.id,
        is_need_to_notify: true,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
          { type: 'modify', list_obj: ['all'] },
          { type: 'delete', list_obj: ['all'] },
        ],
      }),
    );
    expect(ResponseLinkDashboard.status).toBe(200);
    expect(ResponseLinkDashboard.body.ops[0].obj_type).toBe('dashboard');
    expect(ResponseLinkDashboard.body.ops[0].action_type).toBe('link');
    SchemaValidator.validate(linkSchema, ResponseLinkDashboard.body);
  });

  test.skip(`should list link`, async () => {
    const ResponseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: Folder2,
        company_id,
        list_obj: 'group',
      }),
    );
    expect(ResponseListFolder.status).toBe(200);
    expect(ResponseListFolder.body.ops[0].obj).toBe('folder');
    expect(ResponseListFolder.body.ops[0].list_obj).toBe('group');
    expect(ResponseListFolder.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ privs: [{ type: 'view', list_obj: ['all'] }] })]),
    );
    expect(ResponseListFolder.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ login_id: ApiKey2 })]),
    );
    SchemaValidator.validate(listLinkSchema, ResponseListFolder.body);
    const ResponseListConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
        list_obj: 'group',
      }),
    );
    expect(ResponseListConv.status).toBe(200);
    expect(ResponseListConv.body.ops[0].obj).toBe('conv');
    expect(ResponseListConv.body.ops[0].list_obj).toBe('group');
    expect(ResponseListConv.body.ops[0].list).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          privs: [
            { type: 'view', list_obj: ['all'] },
            { type: 'create', list_obj: ['all'] },
          ],
        }),
      ]),
    );
    expect(ResponseListConv.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ login_id: ApiKey2 })]),
    );
    SchemaValidator.validate(listLinkSchema, ResponseListConv.body);
    const ResponseListConv2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: Conv2,
        company_id,
        list_obj: 'group',
      }),
    );
    expect(ResponseListConv2.status).toBe(200);
    expect(ResponseListConv2.body.ops[0].obj).toBe('conv');
    expect(ResponseListConv2.body.ops[0].list_obj).toBe('group');
    expect(ResponseListConv2.body.ops[0].list).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          privs: [
            { type: 'view', list_obj: ['all'] },
            { type: 'create', list_obj: ['all'] },
          ],
        }),
      ]),
    );
    expect(ResponseListConv2.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: '<EMAIL>' })]),
    );
    expect(ResponseListConv.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ login_id: ApiKey2 })]),
    );
    SchemaValidator.validate(listLinkSchema, ResponseListConv2.body);
    invite_hash = ResponseListConv2.body.ops[0].list[1].invite_hash;
    const ResponseListDashboard = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDasboard,
        company_id,
        list_obj: 'group',
      }),
    );
    expect(ResponseListDashboard.status).toBe(200);
    expect(ResponseListDashboard.body.ops[0].obj).toBe('dashboard');
    expect(ResponseListDashboard.body.ops[0].list_obj).toBe('group');
    expect(ResponseListDashboard.body.ops[0].list).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          privs: [
            { type: 'view', list_obj: ['all'] },
            { type: 'create', list_obj: ['all'] },
            { type: 'delete', list_obj: ['all'] },
            { type: 'modify', list_obj: ['all'] },
          ],
        }),
      ]),
    );
    expect(ResponseListFolder.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ login_id: ApiKey2 })]),
    );
    SchemaValidator.validate(listLinkSchema, ResponseListDashboard.body);
  });

  test(`should list obj user2(api) after sharing`, async () => {
    await new Promise(r => setTimeout(r, 2000));
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        sort: 'date',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: Folder2 })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]));
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDasboard })]),
    );
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ owner_id: ApiKey1 })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ is_owner: false })]));
  });

  test(`should ownership`, async () => {
    const ResponseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newConv,
        company_id,
        obj_to_id: ApiKey2,
        obj_type: 'conv',
        save_src_privs: true,
      }),
    );
    expect(ResponseConv.status).toBe(200);
    expect(ResponseConv.body.request_proc).toBe('ok');
    expect(ResponseConv.body.ops[0].proc).toBe('ok');
    expect(ResponseConv.body.ops).toEqual(expect.arrayContaining([expect.objectContaining({ owner_id: ApiKey2 })]));
    const ResponseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: Folder2,
        company_id,
        obj_to_id: ApiKey2,
        obj_type: 'folder',
        save_src_privs: true,
      }),
    );
    expect(ResponseFolder.status).toBe(200);
    expect(ResponseFolder.body.request_proc).toBe('ok');
    expect(ResponseFolder.body.ops[0].proc).toBe('ok');
    expect(ResponseFolder.body.ops).toEqual(expect.arrayContaining([expect.objectContaining({ owner_id: ApiKey2 })]));
    const ResponseDashboard = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newDasboard,
        company_id,
        obj_to_id: ApiKey2,
        obj_type: 'dashboard',
        save_src_privs: true,
      }),
    );
    expect(ResponseDashboard.status).toBe(200);
    expect(ResponseDashboard.body.request_proc).toBe('ok');
    expect(ResponseDashboard.body.ops[0].proc).toBe('ok');
    expect(ResponseDashboard.body.ops).toEqual(
      expect.arrayContaining([expect.objectContaining({ owner_id: ApiKey2 })]),
    );
  });

  test(`should list obj user2(api) after change owner`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        sort: 'date',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: Folder2 })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]));
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDasboard })]),
    );
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ owner_id: ApiKey2 })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ is_owner: true })]));
  });

  test.skip(`should unlink and delete invite`, async () => {
    const ResponseDeleteInvite = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.INVITE,
        company_id,
        hash: invite_hash,
      }),
    );
    expect(ResponseDeleteInvite.status).toBe(200);
    expect(ResponseDeleteInvite.body.ops[0].proc).toBe('ok');
    const ResponseLinkConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: Conv2,
        company_id,
        obj_to: 'user',
        obj_to_id: ApiKey2,
        is_need_to_notify: false,
        privs: [],
      }),
    );
    expect(ResponseLinkConv.status).toBe(200);
    expect(ResponseLinkConv.body.ops[0].obj_type).toBe('conv');
    expect(ResponseLinkConv.body.ops[0].action_type).toBe('unlink');
    SchemaValidator.validate(linkSchema, ResponseLinkConv.body);
    const ResponseListConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: Conv2,
        company_id,
        list_obj: 'group',
      }),
    );
    expect(ResponseListConv.status).toBe(200);
    expect(ResponseListConv.body.ops[0].obj).toBe('conv');
    expect(ResponseListConv.body.ops[0].list_obj).toBe('group');
    expect(ResponseListConv.body.ops).toEqual(expect.arrayContaining([expect.objectContaining({ list: [] })]));
    SchemaValidator.validate(listLinkSchema, ResponseListConv.body);
  });

  afterAll(async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    const ResponseDestroyFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(ResponseDestroyFolder.status).toBe(200);
    expect(ResponseDestroyFolder.body.ops[0].obj_id).toBe(newFolder);
    const ResponseDeleteApiKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
        company_id,
      }),
    );
    expect(ResponseDeleteApiKey.status).toBe(200);
  });
});
