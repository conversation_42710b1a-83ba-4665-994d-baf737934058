import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  maxLength,
  companyTestCases,
  stringNotValidTestCases,
  securityTestCases,
  arrayTestCases,
} from '../../../../../negativeCases';

describe('Invite (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let apiS: ApiKeyClient;
  let apiSuper: ApiKey;
  let newConv: string | number;
  let company_id: any;
  const valuesToSkip: any = [];
  let url: string;
  let hash: string;
  let id: string | number;
  let value: string;

  beforeAll(async () => {
    apiSuper = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apiSuper);
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Process_${Date.now()}`);
    newConv = response.body.ops[0].obj_id;

    const responseList = await apiS.requestSuperadmin('superadmin/api/1/json', {
      type: 'list',
      obj: 'values',
    });
    expect(responseList.status).toBe(200);
    expect(responseList.body.result).toBe('ok');
    expect(responseList.body.list).toEqual(
      expect.arrayContaining([expect.objectContaining({ key: `capi_user_notify_conv` })]),
    );
    value = (responseList.body.list as Array<any>).find(item => item.key === `capi_user_notify_conv`).value;
    id = (responseList.body.list as Array<any>).find(item => item.key === `capi_user_notify_conv`).id;

    const responseSetNull = await apiS.requestSuperadmin('superadmin/api/1/json', {
      type: 'set',
      obj: 'value',
      id,
      key: 'capi_user_notify_conv',
      value: null,
      obj_type: 'integer',
      is_required: false,
      allowed: [],
    });
    expect(responseSetNull.status).toBe(200);
    expect(responseSetNull.body.result).toBe('ok');
  });

  test.each([...integerTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't create invite to conv with invalid link_to_obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.INVITE,
          company_id,

          login: '<EMAIL>',
          login_type: 'google',
          link_to_obj: 'conv',
          link_to_obj_id: input,
          is_need_to_notify: false,
          privs: [
            { type: 'view', list_obj: ['all'] },
            { type: 'create', list_obj: ['all'] },
          ],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...companyTestCases, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't create invite to conv with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.INVITE,
          company_id: input,

          login: '<EMAIL>',
          login_type: 'google',
          link_to_obj: 'conv',
          link_to_obj_id: newConv,
          is_need_to_notify: false,
          privs: [
            { type: 'view', list_obj: ['all'] },
            { type: 'create', list_obj: ['all'] },
          ],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [
      ...stringTestCases,
      ...stringNotValidTestCases,
      ...undefinedTestCase,
      ...maxLength,
      ...securityTestCases,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create invite to conv with invalid login '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        company_id,

        login: input,
        login_type: 'google',
        link_to_obj: 'conv',
        link_to_obj_id: newConv,
        is_need_to_notify: false,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create invite to conv with invalid link_to_obj '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        company_id,

        login: '<EMAIL>',
        login_type: 'google',
        link_to_obj: input,
        link_to_obj_id: newConv,
        is_need_to_notify: false,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...arrayTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't create invite to conv with invalid privs '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.INVITE,
          company_id,

          login: '<EMAIL>',
          login_type: 'google',
          link_to_obj: 'conv',
          link_to_obj_id: newConv,
          is_need_to_notify: true,
          privs: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create invite to conv with invalid privs.type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        company_id,

        login: '<EMAIL>',
        login_type: 'google',
        link_to_obj: 'conv',
        link_to_obj_id: newConv,
        is_need_to_notify: true,
        privs: [{ type: input, list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...arrayTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't create invite to conv with invalid privs.list_obj '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.INVITE,
          company_id,

          login: '<EMAIL>',
          login_type: 'google',
          link_to_obj: 'conv',
          link_to_obj_id: newConv,
          is_need_to_notify: true,
          privs: [{ type: 'create', list_obj: input }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases, ...maxLength].map(({ input, errors }) => [
      input,
      errors,
    ]),
  )(`shouldn't delete invite with invalid hash '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.INVITE,
        company_id,
        hash: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test(`should create invite + delete invite`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,

        login: '<EMAIL>',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('invite');
    url = response.body.ops[0].url;
    const match = url.match(/\/([^/]+)$/);
    hash = match ? match[1] : 'Not hash';

    const ResponseDeleteInvite = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.INVITE,
        company_id,
        hash,
      }),
    );
    expect(ResponseDeleteInvite.status).toBe(200);
    expect(ResponseDeleteInvite.body.ops[0].proc).toBe('ok');
  });

  test.each(
    [...companyTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't delete invite with invalid company_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.INVITE,
        company_id: input,
        hash,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  afterAll(async () => {
    const responseConv = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, company_id);
    expect(responseConv.status).toBe(200);

    const responseSetValue = await apiS.requestSuperadmin('superadmin/api/1/json', {
      type: 'set',
      obj: 'value',
      id,
      key: 'capi_user_notify_conv',
      value,
      obj_type: 'integer',
      is_required: false,
      allowed: [],
    });
    expect(responseSetValue.status).toBe(200);
    expect(responseSetValue.body.result).toBe('ok');
  });
});
