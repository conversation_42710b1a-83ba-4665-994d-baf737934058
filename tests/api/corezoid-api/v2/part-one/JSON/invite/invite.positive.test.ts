import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import deleteSchema from '../../../../schemas/v2/users-groups/deleteInviteSchema.json';
import createSchema from '../../../../schemas/v2/users-groups/createInviteSchema2.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';

describe('Sharing (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let apiS: ApiKeyClient;
  let apiSuper: ApiKey;
  let newFolder: string | number;
  let newConv: string | number;
  let newGroup: string | number;
  let newDasboard: string | number;
  let hash: string;
  let url: string;
  let company_id: any;
  let id: string;
  let value: string;

  beforeAll(async () => {
    apiSuper = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apiSuper);
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseList = await apiS.requestSuperadmin('superadmin/api/1/json', {
      type: 'list',
      obj: 'values',
    });
    expect(responseList.status).toBe(200);
    expect(responseList.body.result).toBe('ok');
    expect(responseList.body.list).toEqual(
      expect.arrayContaining([expect.objectContaining({ key: `capi_user_notify_conv` })]),
    );
    value = (responseList.body.list as Array<any>).find(item => item.key === `capi_user_notify_conv`).value;
    id = (responseList.body.list as Array<any>).find(item => item.key === `capi_user_notify_conv`).id;

    const responseFolder = await requestCreateObj(api, OBJ_TYPE.FOLDER, company_id, `Folder_${Date.now()}`, 0);
    newFolder = responseFolder.body.ops[0].obj_id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Process_${Date.now()}`, newFolder);
    newConv = response.body.ops[0].obj_id;

    const createGroupResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        title: `Group_${Date.now()}`,
        obj_type: 'admins',
      }),
    );
    expect(createGroupResponse.status).toBe(200);
    newGroup = createGroupResponse.body.ops[0].obj_id;

    const createDashboardResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        folder_id: newFolder,
        title: `Dashboard_${Date.now()}`,
        time_range: { select: 'online', start: 1597847260, stop: 1597847260, timezone_offset: -180 },
      }),
    );
    expect(createDashboardResponse.status).toBe(200);
    newDasboard = createDashboardResponse.body.ops[0].obj_id;

    const responseSetNull = await apiS.requestSuperadmin('superadmin/api/1/json', {
      type: 'set',
      obj: 'value',
      id,
      key: 'capi_user_notify_conv',
      value: null,
      obj_type: 'integer',
      is_required: false,
      allowed: [],
    });
    expect(responseSetNull.status).toBe(200);
    expect(responseSetNull.body.result).toBe('ok');
  });

  test(`should create invite (all parameters) (link_to_obj: 'conv')`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        company_id,

        login: '<EMAIL>',
        login_type: 'google',
        link_to_obj: 'conv',
        link_to_obj_id: newConv,
        is_need_to_notify: false,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('invite');
    url = response.body.ops[0].url;
    const match = url.match(/\/([^/]+)$/);
    hash = match ? match[1] : 'Not hash';
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`should delete invite (all parameters)`, async () => {
    const ResponseDeleteInvite = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.INVITE,
        company_id,
        hash,
      }),
    );
    expect(ResponseDeleteInvite.status).toBe(200);
    expect(ResponseDeleteInvite.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(deleteSchema, ResponseDeleteInvite.body);
  });

  test(`should create invite (all parameters) (link_to_obj: 'dashboard')`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        company_id,

        login: '<EMAIL>',
        login_type: 'google',
        link_to_obj: 'dashboard',
        link_to_obj_id: newDasboard,
        is_need_to_notify: false,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('invite');
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`should create invite (all parameters) (link_to_obj: 'folder')`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        company_id,

        login: '<EMAIL>',
        login_type: 'google',
        link_to_obj: 'folder',
        link_to_obj_id: newFolder,
        is_need_to_notify: false,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('invite');
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`should create invite (all parameters) (link_to_obj: 'group')`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        company_id,

        login: '<EMAIL>',
        login_type: 'corezoid',
        link_to_obj: 'group',
        link_to_obj_id: newGroup,
        is_need_to_notify: true,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
          { type: 'modify', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('invite');
    url = response.body.ops[0].url;
    const match = url.match(/\/([^/]+)$/);
    hash = match ? match[1] : 'Not hash';
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`should delete invite (all parameters)`, async () => {
    const ResponseDeleteInvite = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.INVITE,
        company_id,
        hash,
      }),
    );
    expect(ResponseDeleteInvite.status).toBe(200);
    expect(ResponseDeleteInvite.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(deleteSchema, ResponseDeleteInvite.body);
  });

  test(`should create invite (only required parameters)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,

        login: '<EMAIL>',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('invite');
    url = response.body.ops[0].url;
    const match = url.match(/\/([^/]+)$/);
    hash = match ? match[1] : 'Not hash';
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`should delete invite (only required parameters)`, async () => {
    const ResponseDeleteInvite = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.INVITE,
        hash,
      }),
    );
    expect(ResponseDeleteInvite.status).toBe(200);
    expect(ResponseDeleteInvite.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(deleteSchema, ResponseDeleteInvite.body);
  });

  afterAll(async () => {
    const responseFolder = await requestDeleteObj(api, OBJ_TYPE.FOLDER, newFolder, company_id);
    expect(responseFolder.status).toBe(200);

    const responseGroup = await requestDeleteObj(api, OBJ_TYPE.GROUP, newGroup, company_id);
    expect(responseGroup.status).toBe(200);

    const responseSetValue = await apiS.requestSuperadmin('superadmin/api/1/json', {
      type: 'set',
      obj: 'value',
      id,
      key: 'capi_user_notify_conv',
      value,
      obj_type: 'integer',
      is_required: false,
      allowed: [],
    });
    expect(responseSetValue.status).toBe(200);
    expect(responseSetValue.body.result).toBe('ok');
  });
});
