import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('Sharing (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newFolder: string | number;
  let newConv: string | number;
  let newDasboard: string | number;
  let ApiKey2: string | number;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    const createFolderResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: 0,
        obj_type: 0,
        status: 'active',
        title: `Folder_${Date.now()}`,
      }),
    );
    expect(createFolderResponse.status).toBe(200);
    newFolder = createFolderResponse.body.ops[0].obj_id;
    const createConvResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: 0,
        tite: `Process_${Date.now()}`,
      }),
    );
    expect(createConvResponse.status).toBe(200);
    newConv = createConvResponse.body.ops[0].obj_id;
    const createDashboardResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        folder_id: 0,
        tite: `Dashboard_${Date.now()}`,
        time_range: { select: 'online', start: 1597847260, stop: 1597847260, timezone_offset: -180 },
      }),
    );
    expect(createDashboardResponse.status).toBe(200);
    newDasboard = createDashboardResponse.body.ops[0].obj_id;
  });

  test.each([
    ['test_etssetgmail.com', `invalid email`],
    [undefined, `Key 'login' is required`],
  ])(`should create invite with invalid login '%s'`, async (login, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        company_id,

        login,
        login_type: 'google',
        link_to_obj: 'conv',
        link_to_obj_id: newConv,
        is_need_to_notify: false,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value is not valid`],
    ['test', `Value is not valid`],
    [true, `Value is not valid`],
    ['', `Value is not valid`],
    ['1', `Access denied`],
    [1234, `Object folder with id 1234 does not exist`],
  ])(`should create invite with invalid link_to_obj_id '%s'`, async (link_to_obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        company_id,

        login: '<EMAIL>',
        login_type: 'google',
        link_to_obj: 'folder',
        link_to_obj_id,
        is_need_to_notify: false,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([[undefined, `invite`]])(
    `should create invite with invalid is_need_to_notify '%s'`,
    async (is_need_to_notify, reason) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.INVITE,
          company_id,

          login: '<EMAIL>',
          login_type: 'google',
          link_to_obj: 'dashboard',
          link_to_obj_id: newDasboard,
          is_need_to_notify,
          privs: [
            { type: 'view', list_obj: ['all'] },
            { type: 'create', list_obj: ['all'] },
          ],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.request_proc).toBe('ok');
      expect(response.body.ops[0].proc).toBe('ok');
      expect(response.body.ops[0].obj).toEqual(reason);
    },
  );

  test.each([0, true, {}, [], 1234])(`should create invite with invalid company_id '%s'`, async company_id => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        company_id,

        login: '<EMAIL>',
        login_type: 'google',
        link_to_obj: 'dashboard',
        link_to_obj_id: newDasboard,
        is_need_to_notify: false,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(`Value is not valid`);
  });

  test.each(['test', 'i0000'])(`should create invite with invalid company_id '%s'`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        company_id: param,

        login: '<EMAIL>',
        login_type: 'google',
        link_to_obj: 'dashboard',
        link_to_obj_id: newDasboard,
        is_need_to_notify: false,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(`Access denied`);
  });

  test.each([
    [
      '',
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"create\\\">>,<<\\\"view\\\">>,<<\\\"modify\\\">>,<<\\\"delete\\\">>]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<>>},{<<\\\"list_obj\\\">>,[<<\\\"all\\\">>]}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
    ],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"create\\\">>,<<\\\"view\\\">>,<<\\\"modify\\\">>,<<\\\"delete\\\">>]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<\\\"test\\\">>},{<<\\\"list_obj\\\">>,[<<\\\"all\\\">>]}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
    ],
    [
      {},
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"create\\\">>,<<\\\"view\\\">>,<<\\\"modify\\\">>,<<\\\"delete\\\">>]\">> or Value '<<\"[[{<<\\\"type\\\">>,{[]}},{<<\\\"list_obj\\\">>,[<<\\\"all\\\">>]}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
    ],
    [
      [],
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"create\\\">>,<<\\\"view\\\">>,<<\\\"modify\\\">>,<<\\\"delete\\\">>]\">> or Value '<<\"[[{<<\\\"type\\\">>,[]},{<<\\\"list_obj\\\">>,[<<\\\"all\\\">>]}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
    ],
    [
      1234,
      `Value '<<\"1234\">>' is not valid. Value is not in allowed list <<\"[<<\\\"create\\\">>,<<\\\"view\\\">>,<<\\\"modify\\\">>,<<\\\"delete\\\">>]\">> or Value '<<\"[[{<<\\\"type\\\">>,1234},{<<\\\"list_obj\\\">>,[<<\\\"all\\\">>]}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
    ],
    [
      0,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"create\\\">>,<<\\\"view\\\">>,<<\\\"modify\\\">>,<<\\\"delete\\\">>]\">> or Value '<<\"[[{<<\\\"type\\\">>,0},{<<\\\"list_obj\\\">>,[<<\\\"all\\\">>]}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
    ],
    [
      true,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"create\\\">>,<<\\\"view\\\">>,<<\\\"modify\\\">>,<<\\\"delete\\\">>]\">> or Value '<<\"[[{<<\\\"type\\\">>,true},{<<\\\"list_obj\\\">>,[<<\\\"all\\\">>]}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
    ],
  ])(`should create invite with invalid privs '%s'`, async (type, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        company_id,

        login: '<EMAIL>',
        login_type: 'google',
        link_to_obj: 'folder',
        link_to_obj_id: newFolder,
        is_need_to_notify: false,
        privs: [{ type, list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      '',
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[[<<\\\"all\\\">>]]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<\\\"view\\\">>},{<<\\\"list_obj\\\">>,<<>>}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
    ],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[[<<\\\"all\\\">>]]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<\\\"view\\\">>},{<<\\\"list_obj\\\">>,<<\\\"test\\\">>}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
    ],
    [
      {},
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[[<<\\\"all\\\">>]]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<\\\"view\\\">>},{<<\\\"list_obj\\\">>,{[]}}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
    ],
    [
      [],
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[[<<\\\"all\\\">>]]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<\\\"view\\\">>},{<<\\\"list_obj\\\">>,[]}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
    ],
    [
      1234,
      `Value '<<\"1234\">>' is not valid. Value is not in allowed list <<\"[[<<\\\"all\\\">>]]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<\\\"view\\\">>},{<<\\\"list_obj\\\">>,1234}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
    ],
    [
      0,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[[<<\\\"all\\\">>]]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<\\\"view\\\">>},{<<\\\"list_obj\\\">>,0}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
    ],
    [
      true,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[[<<\\\"all\\\">>]]\">> or Value '<<\"[[{<<\\\"type\\\">>,<<\\\"view\\\">>},{<<\\\"list_obj\\\">>,true}]]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
    ],
  ])(`should create invite with invalid privs '%s'`, async (list_obj, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        company_id,

        login: '<EMAIL>',
        login_type: 'google',
        link_to_obj: 'conv',
        link_to_obj_id: newConv,
        is_need_to_notify: false,
        privs: [{ type: 'view', list_obj }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test(`should link folder with invalid obj (conv)`, async () => {
    const CreateKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(200);
    const user = CreateKeyResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    ApiKey2 = +newApiKey.id;
    const ResponseLinkFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: 11111111111,
        company_id,

        obj_to: 'user',
        obj_to_id: newApiKey.id,
        is_need_to_notify: false,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(ResponseLinkFolder.status).toBe(200);
    expect(ResponseLinkFolder.body.ops[0].obj_type).toBe('conv');
    expect(ResponseLinkFolder.body.ops[0].description).toEqual(`Object conv with id 11111111111 does not exist`);
  });

  test.each([
    ['test', 'Value is not valid'],
    [true, 'Value is not valid'],
    [null, 'Value is not valid'],
    ['', 'Value is not valid'],
    [{}, 'Value is not valid'],
    [1234, `Object folder with id 1234 does not exist`],
    ['1234', `Object folder with id 1234 does not exist`],
    [0, `Object's company ID does not match company ID in the request`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [1, `Object's company ID does not match company ID in the request`],
    [undefined, `Key 'obj_id' is required`],
  ])(`should link folder with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id,
        company_id,

        obj_to: 'user',
        obj_to_id: ApiKey2,
        is_need_to_notify: false,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      'test',
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid`,
    ],
    [
      true,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid`,
    ],
    [
      null,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid`,
    ],
    [
      '',
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid`,
    ],
    [
      {},
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid`,
    ],
    [1234, `Link conv to user failed. Reason: user is not in the same company, what and specified conveyor.`],
    ['1234', `Link conv to user failed. Reason: user is not in the same company, what and specified conveyor.`],
    [0, `Object user with id 0 does not exist`],
    [
      -1,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Value is not valid. Value's limit is less than minimum allowed: 0`,
    ],
    [1, `Link conv to user failed. Reason: user is not in the same company, what and specified conveyor.`],
    [
      undefined,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"new_user\\\">>]\">> or Key 'obj_to_id' is required`,
    ],
  ])(`should link conv with invalid obj_to_id '%s'`, async (obj_to_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
        obj_to: 'user',
        obj_to_id,
        is_need_to_notify: false,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each(['test', null, '', {}, 1234, '1234', 0, -1, 1])(
    `should link obj with invalid is_need_to_notify '%s'`,
    async is_need_to_notify => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.DASHBOARD,
          obj_id: newDasboard,
          company_id,
          obj_to: 'user',
          obj_to_id: ApiKey2,
          is_need_to_notify,
          privs: [{ type: 'view', list_obj: ['all'] }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.request_proc).toBe('ok');
      expect(response.body.ops[0].proc).toBe('error');
      expect(response.body.ops[0].description).toEqual(`Value is not valid`);
    },
  );

  test.each([true, [], {}, 1234, 0, -1, 1])(`should link obj with invalid company_id '%s'`, async company_id => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDasboard,
        company_id,

        obj_to: 'user',
        obj_to_id: ApiKey2,
        is_need_to_notify: false,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(`Value is not valid`);
  });

  test.each(['test', 'i0000'])(`should link obj with invalid company_id '%s'`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDasboard,
        company_id: param,

        obj_to: 'user',
        obj_to_id: ApiKey2,
        is_need_to_notify: false,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(`Object's company ID does not match company ID in the request`);
  });

  test.each([
    [
      true,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or privs are not valid or Key 'obj_type' is required`,
    ],
    [
      'test',
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or privs are not valid or Key 'obj_type' is required`,
    ],
    [
      [],
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or privs are not valid or Key 'obj_type' is required`,
    ],
    [
      {},
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or privs are not valid or Key 'obj_type' is required`,
    ],
    [
      '',
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or privs are not valid or Key 'obj_type' is required`,
    ],
    [
      1234,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or privs are not valid or Key 'obj_type' is required`,
    ],
    [
      0,
      `Value '<<\"user\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>]\">> or privs are not valid or Key 'obj_type' is required`,
    ],
  ])(`should link obj with invalid privs '%s'`, async (type, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        obj_to: 'user',
        obj_to_id: ApiKey2,
        is_need_to_notify: false,
        privs: [{ type, list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `privs are not valid`],
    ['test', `privs are not valid`],
    [[], `list_obj does not coincide with one of the expected values [all]`],
    [{}, `privs are not valid`],
    ['', `privs are not valid`],
    [1234, `privs are not valid`],
    [0, `privs are not valid`],
  ])(`should link obj with invalid privs '%s'`, async (list_obj, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDasboard,
        company_id,
        obj_to: 'user',
        obj_to_id: ApiKey2,
        is_need_to_notify: false,
        privs: [{ type: 'view', list_obj }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test(`should link obj with invalid privs undefined`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
        obj_to: 'user',
        obj_to_id: ApiKey2,
        is_need_to_notify: false,
        privs: undefined,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(`privs are not valid`);
  });

  test(`should list link conv with invalid obj (folder)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newConv,
        company_id,
        list_obj: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(`Object folder with id ${newConv} does not exist`);
  });

  test.each([
    [true, `Value is not valid`],
    [null, `Value is not valid`],
    ['test', `Value is not valid`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
    ['', `Value is not valid`],
    [1234, `Object conv with id 1234 does not exist`],
    ['1234', `Object conv with id 1234 does not exist`],
    [1, `Object's company ID does not match company ID in the request`],
    [undefined, `Key 'obj_id' is required`],
  ])(`should list link conv with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id,
        company_id,
        list_obj: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      true,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"all\\\">>]\">>`,
    ],
    [
      null,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"all\\\">>]\">>`,
    ],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"all\\\">>]\">>`,
    ],
    [[], `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"all\\\">>]\">>`],
    [{}, `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"all\\\">>]\">>`],
    ['', `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"all\\\">>]\">>`],
    [
      1234,
      `Value '<<\"1234\">>' is not valid. Value is not in allowed list <<\"[<<\\\"group\\\">>,<<\\\"all\\\">>]\">>`,
    ],
  ])(`should list link folder with invalid list_obj '%s'`, async (list_obj, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
        list_obj,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value is not valid`],
    ['test', `Company test does not exists`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
    ['i0000', `Company i0000 does not exists`],
  ])(`should list link obj with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        list_obj: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value is not valid`],
    ['test', `Value is not valid`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
    ['', `Value is not valid`],
    [1, `Access denied. Reason: not owner.`],
    [undefined, `Key 'obj_id' is required`],
    [1234, `Couldn't find object owner. Object id 1234 type 0 is not found or destroyed`],
    ['1234', `Couldn't find object owner. Object id 1234 type 0 is not found or destroyed`],
  ])(`should modify owner with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id,
        company_id,
        obj_to_id: ApiKey2,
        obj_type: 'folder',
        save_src_privs: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value is not valid or Key 'obj_to_login' is required`],
    ['test', `Value is not valid or Key 'obj_to_login' is required`],
    ['', `Value is not valid or Key 'obj_to_login' is required`],
    [null, `Value is not valid or Key 'obj_to_login' is required`],
    [undefined, `Key 'obj_to_id' is required or Key 'obj_to_login' is required`],
    [1, `New owner is not member of specified company.`],
    ['1234', `New owner is not member of specified company.`],
    [1234, `New owner is not member of specified company.`],
  ])(`should modify owner with invalid obj_to_id '%s'`, async (obj_to_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: newFolder,
        company_id,
        obj_to_id,
        obj_type: 'folder',
        save_src_privs: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([1234, '1234', null, 'test', ''])(
    `should modify owner with invalid is_need_to_notify '%s'`,
    async param => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.OWNER,
          obj_id: newDasboard,
          company_id,
          obj_to_id: ApiKey2,
          obj_type: 'dashboard',
          save_src_privs: param,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.request_proc).toBe('ok');
      expect(response.body.ops[0].proc).toBe('error');
      expect(response.body.ops[0].description).toEqual(`Value '${param}' is not valid. Type of value is not 'boolean'`);
    },
  );

  afterAll(async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    const ResponseDestroyFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(ResponseDestroyFolder.status).toBe(200);
    expect(ResponseDestroyFolder.body.ops[0].obj_id).toBe(newFolder);
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
    const ResponseDestroyConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
    expect(ResponseDestroyConv.status).toBe(200);
    expect(ResponseDestroyConv.body.ops[0].obj_id).toBe(newConv);
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDasboard,
        company_id,
      }),
    );
    const ResponseDestroyDashboard = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: newDasboard,
        company_id,
      }),
    );
    expect(ResponseDestroyDashboard.status).toBe(200);
    expect(ResponseDestroyDashboard.body.ops[0].obj_id).toBe(newDasboard);
    const ResponseDeleteApiKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
        company_id,
        group_id: 0,
      }),
    );
    expect(ResponseDeleteApiKey.status).toBe(200);
  });
});
