import { debug } from '../../../../../../../support/utils/logger';
import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { axiosInstance } from '../../../../../../../application/api/AxiosClient';
import { addMsg } from 'jest-html-reporters/helper';
import modifySchema from '../../../../schemas/v2/conv_auth/modifyConv_auth.Schema.json';
import showSchema from '../../../../schemas/v2/conv_auth/showConv_auth.Schema.json';
import deleteSchema from '../../../../schemas/v2/conv_auth/deleteConv_auth.Schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import {
  requestList,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
} from '../../../../../../../application/api/ApiObj';

describe('Conv_auth (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: number;
  let company_id: any;
  let hash: string;
  let final_node_ID: string;
  let newProject: string;
  let newStage: string;
  let project_short_name: string;
  let stage_short_name: string;
  let newAlias: string;
  let aliasHash: string;
  let newVarJson: string;
  let newVarRaw: string;
  let short_namejson: string;
  let short_nameraw: string;
  let base64Credentials: string;
  let password: string;
  let username: string;
  let base64CredentialsModify: string;
  let passwordModify: string;
  let usernameModify: string;
  let base64CredentialsVar: string;
  let passwordVar: string;
  let usernameVar: string;
  let base64CredentialsVarGluing: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = `project-${Date.now()}`;
    stage_short_name = `stage-${Date.now()}`;
    short_namejson = `var-${Date.now()}`;
    short_nameraw = `var1-${Date.now()}`;
    (password = `pass_${Date.now()}`),
      (username = `user_${Date.now()}`),
      (base64Credentials = Buffer.from(`${username}:${password}`).toString('base64'));
    (passwordModify = `passModify_${Date.now()}`),
      (usernameModify = `usernameModify_${Date.now()}`),
      (base64CredentialsModify = Buffer.from(`${usernameModify}:${passwordModify}`).toString('base64'));
    (passwordVar = `passVar_${Date.now()}`),
      (usernameVar = `userVar_${Date.now()}`),
      (base64CredentialsVar = Buffer.from(`${usernameVar}:${passwordVar}`).toString('base64'));
    base64CredentialsVarGluing = Buffer.from(`name+${usernameVar}_${passwordVar}:pass-${passwordVar}`).toString(
      'base64',
    );

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;

    const response = await requestCreateObj(
      api,
      OBJ_TYPE.CONV,
      company_id,
      `Process_${Date.now()}`,
      newStage,
      `process`,
      newProject,
      newStage,
    );
    newConv = response.body.ops[0].obj_id;
    hash = response.body.ops[0].hash;

    const responseList = await requestListConv(api, newConv, company_id);
    expect(responseList.status).toBe(200);
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        short_name: 'aliasshortname',
        title: 'Alias2',
        description: 'test',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    newAlias = responseAlias.body.ops[0].obj_id;

    const responseHash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: 'alias',
        alias_id: newAlias,
      }),
    );
    expect(responseHash.status).toBe(200);
    aliasHash = responseHash.body.ops[0].callback_hash;

    const responselink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
        obj_to_id: newConv,
        obj_to_type: `conv`,
        link: true,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responselink.status).toBe(200);
    expect(responselink.body.ops[0].obj_id).toBe(newAlias);

    const responseVar = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: short_namejson,
        description: 'test',
        title: 'Var',
        value: `{"pass":"${passwordVar}","user":"${usernameVar}","test":[{"passtest":"${passwordVar}"}]}`,
        data_type: 'json',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(responseVar.status).toBe(200);
    expect(responseVar.body.ops[0].obj).toBe('env_var');
    expect(responseVar.body.ops[0].fingerprints[0].algo).toBe('md5');
    expect(responseVar.body.ops[0].fingerprints[1].algo).toBe('sha256');
    expect(responseVar.body.ops[0].fingerprints[0].value).not.toBeEmpty;
    expect(responseVar.body.ops[0].fingerprints[1].value).not.toBeEmpty;
    newVarJson = responseVar.body.ops[0].obj_id;

    const responseVarRaw = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: short_nameraw,
        description: 'test',
        title: 'Var',
        value: `${passwordVar}`,
        data_type: 'raw',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(responseVarRaw.status).toBe(200);
    expect(responseVarRaw.body.ops[0].obj).toBe('env_var');
    expect(responseVarRaw.body.ops[0].fingerprints[0].algo).toBe('md5');
    expect(responseVarRaw.body.ops[0].fingerprints[1].algo).toBe('sha256');
    expect(responseVarRaw.body.ops[0].fingerprints[0].value).not.toBeEmpty;
    expect(responseVarRaw.body.ops[0].fingerprints[1].value).not.toBeEmpty;
    newVarRaw = responseVarRaw.body.ops[0].obj_id;
  });

  test('should show conv_auth = []', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV_AUTH,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe(`There is no active extra authorization in process`);
  });

  test('should create conv_auth', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV_AUTH,

        data: { user: `${username}`, pass: `${password}` },
        obj_type: 'basic',
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(modifySchema, response.body);
  });

  test('should show conv_auth after create', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV_AUTH,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].data.user).toBe(username);
    expect(response.body.ops[0].data.pass).toBe(password);
    SchemaValidator.validate(showSchema, response.body);
  });

  test(`shouldn't create task by direct url (without header: basic)`, async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/${newConv}/${hash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { a: 2, ref: 'chupakabra' },
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(400);
    expect(response.data.ops[0].proc).toEqual('error');
    expect(response.data.ops[0].description).toEqual('access denied, authorize first');
  });

  test(`shouldn't create task by direct url by alias (without header: basic)`, async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/@aliasshortname/${newProject}/${newStage}/${company_id}/${aliasHash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { a: 2, ref: 'chupakabra' },
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(400);
    expect(response.data.ops[0].proc).toEqual('error');
    expect(response.data.ops[0].description).toEqual('access denied, authorize first');
  });

  test('should create task by direct url (with header: basic)', async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/${newConv}/${hash}`;
    const response = await axiosInstance({
      method: 'POST',
      headers: { Authorization: `Basic ${base64Credentials}` },
      url: uri,
      data: { a: 2, ref: 'chupakabra' },
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(200);
    expect(response.data.ops.proc).toEqual('ok');
    expect(response.data.ops.obj).toEqual('task');
    expect(response.data.ops.ref).toEqual(null);

    await new Promise(r => setTimeout(r, 3000));

    const responseList = await requestList(api, final_node_ID, newConv, company_id, 1);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.a).toEqual(2);
    expect(responseList.body.ops[0].list[0].data.ref).toEqual('chupakabra');
  });

  test('should create task by direct url by alias (with header: basic)', async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/@aliasshortname/${newProject}/${newStage}/${company_id}/${aliasHash}`;
    const response = await axiosInstance({
      method: 'POST',
      headers: { Authorization: `Basic ${base64Credentials}` },
      url: uri,
      data: { a: 3, ref: 'chupakabra1' },
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(200);
    expect(response.data.ops.proc).toEqual('ok');
    expect(response.data.ops.obj).toEqual('task');
    expect(response.data.ops.ref).toEqual(null);

    await new Promise(r => setTimeout(r, 3000));

    const responseList = await requestList(api, final_node_ID, newConv, company_id, 1);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.a).toEqual(3);
    expect(responseList.body.ops[0].list[0].data.ref).toEqual('chupakabra1');
  });

  test('should modify conv_auth and create tasks (old basic/new basic)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV_AUTH,

        data: { user: `${usernameModify}`, pass: `${passwordModify}` },
        obj_type: 'basic',
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(modifySchema, response.body);

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV_AUTH,
        conv_id: newConv,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].proc).toBe('ok');
    expect(responseShow.body.ops[0].data.user).toBe(usernameModify);
    expect(responseShow.body.ops[0].data.pass).toBe(passwordModify);

    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/${newConv}/${hash}`;
    const responseCreateTaskN = await axiosInstance({
      method: 'POST',
      headers: { Authorization: `Basic ${base64Credentials}` },
      url: uri,
      data: { a: 2, ref: 'chupakabra' },
    });
    await addMsg({ message: JSON.stringify(responseCreateTaskN.data, null, 2), context: '' });
    expect(responseCreateTaskN.status).toBe(400);
    expect(responseCreateTaskN.data.ops[0].proc).toEqual('error');
    expect(responseCreateTaskN.data.ops[0].description).toEqual('access denied, authorize first');

    const responseCreateTask = await axiosInstance({
      method: 'POST',
      headers: { Authorization: `Basic ${base64CredentialsModify}` },
      url: uri,
      data: { a: 2, ref: 'chupakabra' },
    });
    await addMsg({ message: JSON.stringify(responseCreateTask.data, null, 2), context: '' });
    expect(responseCreateTask.status).toBe(200);
    expect(responseCreateTask.data.ops.proc).toEqual('ok');
    expect(responseCreateTask.data.ops.obj).toEqual('task');
    expect(responseCreateTask.data.ops.ref).toEqual(null);

    await new Promise(r => setTimeout(r, 3000));

    const responseList = await requestList(api, final_node_ID, newConv, company_id, 1);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.a).toEqual(2);
    expect(responseList.body.ops[0].list[0].data.ref).toEqual('chupakabra');
  });

  test('should delete conv_auth', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV_AUTH,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should delete conv_auth after delete', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV_AUTH,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe(`There is no active extra authorization in process`);
  });

  test('should show conv_auth after delete', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.CONV_AUTH,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe(`There is no active extra authorization in process`);
  });

  test('should create task by direct url (without header: basic)', async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/${newConv}/${hash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { a: 2, ref: 'chupakabra' },
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(200);
    expect(response.data.ops.proc).toEqual('ok');
    expect(response.data.ops.obj).toEqual('task');
    expect(response.data.ops.ref).toEqual(null);

    await new Promise(r => setTimeout(r, 3000));

    const responseList = await requestList(api, final_node_ID, newConv, company_id, 10);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.a).toEqual(2);
    expect(responseList.body.ops[0].list[0].data.ref).toEqual('chupakabra');
  });

  test('should modify conv_auth with env_var_id Json +create task by direct_url', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV_AUTH,

        data: { user: `{{env_var[${newVarJson}].user}}`, pass: `{{env_var[${newVarJson}].pass}}` },
        obj_type: 'basic',
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(modifySchema, response.body);

    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/${newConv}/${hash}`;
    const responseTask = await axiosInstance({
      method: 'POST',
      headers: { Authorization: `Basic ${base64CredentialsVar}` },
      url: uri,
      data: { a: 3, ref: 'chupakabra' },
    });
    await addMsg({ message: JSON.stringify(responseTask.data, null, 2), context: '' });
    expect(responseTask.status).toBe(200);
    expect(responseTask.data.ops.proc).toEqual('ok');
    expect(responseTask.data.ops.obj).toEqual('task');
    expect(responseTask.data.ops.ref).toEqual(null);

    await new Promise(r => setTimeout(r, 3000));

    const responseList = await requestList(api, final_node_ID, newConv, company_id, 1);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.a).toEqual(3);
    expect(responseList.body.ops[0].list[0].data.ref).toEqual('chupakabra');
  });

  test('should modify conv_auth with env_var_short_name Json +create task by direct_url', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV_AUTH,

        data: { user: `{{env_var[@${short_namejson}].user}}`, pass: `{{env_var[@${short_namejson}].pass}}` },
        obj_type: 'basic',
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(modifySchema, response.body);

    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/${newConv}/${hash}`;
    const responseTask = await axiosInstance({
      method: 'POST',
      headers: { Authorization: `Basic ${base64CredentialsVar}` },
      url: uri,
      data: { a: 4, ref: 'chupakabra' },
    });
    await addMsg({ message: JSON.stringify(responseTask.data, null, 2), context: '' });
    expect(responseTask.status).toBe(200);
    expect(responseTask.data.ops.proc).toEqual('ok');
    expect(responseTask.data.ops.obj).toEqual('task');
    expect(responseTask.data.ops.ref).toEqual(null);

    await new Promise(r => setTimeout(r, 3000));

    const responseList = await requestList(api, final_node_ID, newConv, company_id, 1);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.a).toEqual(4);
    expect(responseList.body.ops[0].list[0].data.ref).toEqual('chupakabra');
  });

  test('should create conv_auth with env_var_short_name Json(nestedness) +create task by direct_url', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV_AUTH,

        data: { user: `{{env_var[@${short_namejson}].user}}`, pass: `{{env_var[${newVarJson}].test[0].passtest}}` },
        obj_type: 'basic',
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(modifySchema, response.body);

    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/${newConv}/${hash}`;
    const responseTask = await axiosInstance({
      method: 'POST',
      headers: { Authorization: `Basic ${base64CredentialsVar}` },
      url: uri,
      data: { a: 11, ref: 'chupakabra' },
    });
    await addMsg({ message: JSON.stringify(responseTask.data, null, 2), context: '' });
    expect(responseTask.status).toBe(200);
    expect(responseTask.data.ops.proc).toEqual('ok');
    expect(responseTask.data.ops.obj).toEqual('task');
    expect(responseTask.data.ops.ref).toEqual(null);

    await new Promise(r => setTimeout(r, 3000));

    const responseList = await requestList(api, final_node_ID, newConv, company_id, 1);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.a).toEqual(11);
    expect(responseList.body.ops[0].list[0].data.ref).toEqual('chupakabra');
  });

  test('should modify conv_auth with env_var_short_name pass-Raw +create task by direct_url', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV_AUTH,

        data: { user: `{{env_var[@${short_namejson}].user}}`, pass: `{{env_var[${newVarRaw}]}}` },
        obj_type: 'basic',
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(modifySchema, response.body);

    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/${newConv}/${hash}`;
    const responseTask = await axiosInstance({
      method: 'POST',
      headers: { Authorization: `Basic ${base64CredentialsVar}` },
      url: uri,
      data: { a: 5, ref: 'chupakabra' },
    });
    await addMsg({ message: JSON.stringify(responseTask.data, null, 2), context: '' });
    expect(responseTask.status).toBe(200);
    expect(responseTask.data.ops.proc).toEqual('ok');
    expect(responseTask.data.ops.obj).toEqual('task');
    expect(responseTask.data.ops.ref).toEqual(null);

    await new Promise(r => setTimeout(r, 3000));

    const responseList = await requestList(api, final_node_ID, newConv, company_id, 1);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.a).toEqual(5);
    expect(responseList.body.ops[0].list[0].data.ref).toEqual('chupakabra');
  });

  test('should modify conv_auth with env_var_short_name Json(nestedness+gluing) +create task by direct_url', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV_AUTH,

        data: {
          user: `name+{{env_var[@${short_namejson}].user}}_{{env_var[${newVarRaw}]}}`,
          pass: `pass-{{env_var[${newVarJson}].test[0].passtest}}`,
        },
        obj_type: 'basic',
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(modifySchema, response.body);

    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/${newConv}/${hash}`;
    const responseTask = await axiosInstance({
      method: 'POST',
      headers: { Authorization: `Basic ${base64CredentialsVarGluing}` },
      url: uri,
      data: { a: 11, ref: 'chupakabra' },
    });
    await addMsg({ message: JSON.stringify(responseTask.data, null, 2), context: '' });
    expect(responseTask.status).toBe(200);
    expect(responseTask.data.ops.proc).toEqual('ok');
    expect(responseTask.data.ops.obj).toEqual('task');
    expect(responseTask.data.ops.ref).toEqual(null);

    await new Promise(r => setTimeout(r, 3000));

    const responseList = await requestList(api, final_node_ID, newConv, company_id, 1);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.a).toEqual(11);
    expect(responseList.body.ops[0].list[0].data.ref).toEqual('chupakabra');
  });

  test('should modify conv_auth + create task by direct_url after delete/create env_var', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV_AUTH,

        data: { user: `{{env_var[@${short_namejson}].user}}`, pass: `{{env_var[@${short_namejson}].pass}}` },
        obj_type: 'basic',
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(modifySchema, response.body);

    const responseDel = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVarJson,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseDel.status).toBe(200);
    expect(responseDel.body.ops[0].obj).toBe('env_var');

    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/${newConv}/${hash}`;
    const responseTask = await axiosInstance({
      method: 'POST',
      headers: { Authorization: `Basic ${base64CredentialsVar}` },
      url: uri,
      data: { a: 11, ref: 'chupakabra' },
    });
    await addMsg({ message: JSON.stringify(responseTask.data, null, 2), context: '' });
    expect(responseTask.status).toBe(400);
    expect(responseTask.data.ops[0].proc).toEqual('error');
    expect(responseTask.data.ops[0].description).toEqual('access denied, authorize first');

    const responseVar = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'secret',
        short_name: short_namejson,
        description: 'test',
        title: 'Var',
        value: `{"pass":"${passwordVar}","user":"${usernameVar}","test":[{"passtest":"${passwordVar}"}]}`,
        data_type: 'json',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(responseVar.status).toBe(200);
    expect(responseVar.body.ops[0].obj).toBe('env_var');
    newVarJson = responseVar.body.ops[0].obj_id;

    const responseTask1 = await axiosInstance({
      method: 'POST',
      headers: { Authorization: `Basic ${base64CredentialsVar}` },
      url: uri,
      data: { a: 12, ref: 'chupakabra' },
    });
    await addMsg({ message: JSON.stringify(responseTask.data, null, 2), context: '' });
    expect(responseTask1.status).toBe(200);
    expect(responseTask1.data.ops.proc).toEqual('ok');
    expect(responseTask1.data.ops.obj).toEqual('task');
    expect(responseTask1.data.ops.ref).toEqual(null);

    await new Promise(r => setTimeout(r, 3000));

    const responseList = await requestList(api, final_node_ID, newConv, company_id, 1);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.a).toEqual(12);
    expect(responseList.body.ops[0].list[0].data.ref).toEqual('chupakabra');
  });

  test('should modify conv_auth + create task by direct_url after modify env_var', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV_AUTH,

        data: { user: `{{env_var[@${short_namejson}].user}}`, pass: `{{env_var[@${short_namejson}].pass}}` },
        obj_type: 'basic',
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(modifySchema, response.body);

    const responseVarModify = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVarJson,
        company_id,

        env_var_type: 'secret',
        short_name: short_namejson,
        description: 'test',
        title: 'Var',
        value: `{"pass1":"${passwordVar}","user":"${usernameVar}","test":[{"passtest":"${passwordVar}"}]}`,
        data_type: 'json',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    debug(JSON.stringify(responseVarModify.body));
    expect(responseVarModify.status).toBe(200);
    expect(responseVarModify.body.ops[0].obj).toBe('env_var');

    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/${newConv}/${hash}`;
    const responseTask = await axiosInstance({
      method: 'POST',
      headers: { Authorization: `Basic ${base64CredentialsVar}` },
      url: uri,
      data: { a: 11, ref: 'chupakabra' },
    });
    await addMsg({ message: JSON.stringify(responseTask.data, null, 2), context: '' });
    expect(responseTask.status).toBe(400);
    expect(responseTask.data.ops[0].proc).toEqual('error');
    expect(responseTask.data.ops[0].description).toEqual('access denied, authorize first');

    const responseVar = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVarJson,
        company_id,

        env_var_type: 'secret',
        short_name: short_namejson,
        description: 'test',
        title: 'Var',
        value: `{"pass":"${passwordVar}","user":"${usernameVar}","test":[{"passtest":"${passwordVar}"}]}`,
        data_type: 'json',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    debug(JSON.stringify(responseVar.body));
    expect(responseVar.status).toBe(200);
    expect(responseVar.body.ops[0].obj).toBe('env_var');

    const responseTask1 = await axiosInstance({
      method: 'POST',
      headers: { Authorization: `Basic ${base64CredentialsVar}` },
      url: uri,
      data: { a: 12, ref: 'chupakabra' },
    });
    await addMsg({ message: JSON.stringify(responseTask.data, null, 2), context: '' });
    expect(responseTask1.status).toBe(200);
    expect(responseTask1.data.ops.proc).toEqual('ok');
    expect(responseTask1.data.ops.obj).toEqual('task');
    expect(responseTask1.data.ops.ref).toEqual(null);

    await new Promise(r => setTimeout(r, 3000));

    const responseList = await requestList(api, final_node_ID, newConv, company_id, 1);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].data.a).toEqual(12);
    expect(responseList.body.ops[0].list[0].data.ref).toEqual('chupakabra');
  });

  afterAll(async () => {
    const responseConv = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, company_id);
    expect(responseConv.status).toBe(200);

    const responseProject = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(responseProject.status).toBe(200);
  });
});
