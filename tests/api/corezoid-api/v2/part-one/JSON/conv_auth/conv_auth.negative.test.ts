import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj, requestListConv } from '../../../../../../../application/api/ApiObj';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  minLength,
  objectTestCases,
  maxLength,
} from '../../../../../negativeCases';

describe('Conv_auth (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: number;
  let company_id: any;
  let newProject: string;
  let newStage: string;
  let project_short_name: string;
  let stage_short_name: string;
  let password: string;
  let username: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = `project-${Date.now()}`;
    stage_short_name = `stage-${Date.now()}`;
    (password = `pass_${Date.now()}`), (username = `user_${Date.now()}`);

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;

    const response = await requestCreateObj(
      api,
      OBJ_TYPE.CONV,
      company_id,
      `Process_${Date.now()}`,
      newStage,
      `process`,
      newProject,
      newStage,
    );
    newConv = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, newConv, company_id);
    expect(responseList.status).toBe(200);
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify conv_auth with invalid conv_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.CONV_AUTH,

          data: { user: `${username}`, pass: `${password}` },
          obj_type: 'basic',
          conv_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify conv_auth with invalid obj_type '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.CONV_AUTH,

          data: { user: `${username}`, pass: `${password}` },
          obj_type: input,
          conv_id: newConv,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...minLength, ...maxLength].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify conv_auth with invalid data.user '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV_AUTH,

        data: { user: input, pass: `${password}` },
        obj_type: 'basic',
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...minLength, ...maxLength].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify conv_auth with invalid data.pass '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV_AUTH,

        data: { user: `${username}`, pass: input },
        obj_type: 'basic',
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...objectTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify conv_auth with invalid data '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.CONV_AUTH,

          data: input,
          obj_type: 'basic',
          conv_id: newConv,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't show conv_auth with invalid conv_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.CONV_AUTH,
          conv_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't delete conv_auth with invalid conv_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.CONV_AUTH,
          conv_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  afterAll(async () => {
    const responseConv = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, company_id);
    expect(responseConv.status).toBe(200);

    const responseProject = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(responseProject.status).toBe(200);
  });
});
