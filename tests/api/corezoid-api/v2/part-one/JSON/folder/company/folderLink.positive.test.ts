import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../../application/api/SchemaValidator';
import linkMoveSchema from '../../../../../schemas/v2/actions-objects/linkMoveFolder.schema.json';
import linkSchema from '../../../../../schemas/v2/actions-objects/linkFolder.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../../utils/corezoidRequest';

describe('Folder link in company (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newApi: ApiKeyClient;
  let apiKey2: string | number;
  let newFolder: number;
  let newFolder2: number;
  let newFolder3: number;
  let newConv: number;
  let newDashboard: number;
  let newInstance: number;
  let group_id: number;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    const title = `Folder_${Date.now()}`;

    const CreateKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(200);
    const user = CreateKeyResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newApi = application.getApiKeyClient(newApiKey);
    apiKey2 = +newApiKey.id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: 0,
        description: 'test',
        title,
      }),
    );
    expect(response.status).toBe(200);
    newFolder = response.body.ops[0].obj_id;

    const response2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: newFolder,
        description: 'test',
        title,
      }),
    );
    expect(response2.status).toBe(200);
    newFolder2 = response2.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 1000));

    const response3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        folder_id: newFolder,
        title: `Conv_${Date.now()}`,
        status: 'active',
        obj_type: 0,
        description: 'test',
        create_mode: 'without_nodes',
        conv_type: 'process',
      }),
    );
    expect(response3.status).toBe(200);
    newConv = response3.body.ops[0].obj_id;

    const responseSD = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        folder_id: newFolder,
        title: `SD_${Date.now()}`,
        status: 'active',
        obj_type: 0,
        description: 'test',
        create_mode: 'without_nodes',
        conv_type: 'state',
      }),
    );
    expect(responseSD.status).toBe(200);

    const response4 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: newFolder,
        obj_type: 0,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    expect(response4.status).toBe(200);
    newDashboard = response4.body.ops[0].obj_id;

    const response5 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        instance_type: `db_call`,
        folder_id: newFolder,
        title: `Instance_${Date.now()}`,
        data: {
          driver: `postgres`,
          host: `COR-6938.middleware.loc`,
          port: `5432`,
          username: `postgres`,
          password: `pass`,
          ssl: false,
          database: `dbcall`,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response5.status).toBe(200);
    newInstance = response5.body.ops[0].obj_id;

    const responseGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,

        obj_type: `admins`,
        title: `Group_${Date.now()}`,
      }),
    );
    group_id = responseGroup.body.ops[0].obj_id;

    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: apiKey2,
        company_id,

        group_id,
        level: 1,
      }),
    );
    expect(responseLink.status).toBe(200);

    const responseCreateFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: newFolder,
        description: 'test',
        title: `Folder3_${Date.now()}`,
      }),
    );
    expect(responseCreateFolder.status).toBe(200);
    newFolder3 = responseCreateFolder.body.ops[0].obj_id;
  });

  test('should link folder (moving) with required param', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder2,
        company_id,
        obj_type: 'folder',
        folder_id: newFolder3,
        parent_id: newFolder,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toEqual('folder');
    expect(response.body.ops[0].action_type).toEqual('move');
    expect(response.body.ops[0].to_folder).toEqual(newFolder3);
    expect(response.body.ops[0].from_folder).toEqual(newFolder);
    SchemaValidator.validate(linkMoveSchema, response.body);
  });

  test('should link folder moving conv', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newConv,
        company_id,
        obj_type: 'conv',
        folder_id: newFolder2,
        parent_id: newFolder,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toEqual('conv');
    expect(response.body.ops[0].action_type).toEqual('move');
    expect(response.body.ops[0].to_folder).toEqual(newFolder2);
    expect(response.body.ops[0].from_folder).toEqual(newFolder);
    SchemaValidator.validate(linkMoveSchema, response.body);
  });

  test('should link folder moving dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newDashboard,
        company_id,
        obj_type: 'dashboard',
        folder_id: newFolder2,
        parent_id: newFolder,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toEqual('dashboard');
    expect(response.body.ops[0].action_type).toEqual('move');
    expect(response.body.ops[0].to_folder).toEqual(newFolder2);
    expect(response.body.ops[0].from_folder).toEqual(newFolder);
    SchemaValidator.validate(linkMoveSchema, response.body);
  });

  test('should link folder moving instance', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newInstance,
        company_id,
        obj_type: 'instance',
        folder_id: newFolder2,
        parent_id: newFolder,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toEqual('instance');
    expect(response.body.ops[0].action_type).toEqual('move');
    expect(response.body.ops[0].to_folder).toEqual(newFolder2);
    expect(response.body.ops[0].from_folder).toEqual(newFolder);
    SchemaValidator.validate(linkMoveSchema, response.body);
  });

  test('should list folder after moving obj', async () => {
    const responselist = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder3,
        company_id,
      }),
    );
    expect(responselist.status).toBe(200);
    expect(responselist.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder2 })]),
    );

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder2,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]));
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDashboard })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstance })]),
    );
  });

  test('should link (sharing) folder on user and  is_need_to_notify: true', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder3,
        company_id,

        obj_to: 'user',
        obj_to_id: apiKey2,
        is_need_to_notify: true,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toEqual('folder');
    expect(response.body.ops[0].obj_to_type).toEqual('user');
    expect(response.body.ops[0].action_type).toEqual('link');
    SchemaValidator.validate(linkSchema, response.body);
  });

  test('should link (sharing) folder on group and  is_need_to_notify: false', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder2,
        company_id,

        obj_to: 'group',
        obj_to_id: group_id,
        is_need_to_notify: false,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
          { type: 'modify', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toEqual('folder');
    expect(response.body.ops[0].obj_to_type).toEqual('group');
    expect(response.body.ops[0].action_type).toEqual('link');
    SchemaValidator.validate(linkSchema, response.body);
  });

  test('should list folder3 after sharing', async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder3 })]),
    );
    expect(response.body.ops[0].list[0].privs).toEqual(
      expect.arrayContaining([expect.objectContaining({ type: 'view' })]),
    );
    expect(response.body.ops[0].list[0].privs).toEqual(
      expect.arrayContaining([expect.objectContaining({ type: 'create' })]),
    );
    expect(response.body.ops[0].list[0].privs).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ type: 'modify' })]),
    );
  });

  test('should unlink (sharing) folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder3,
        company_id,
        obj_to: 'user',
        obj_to_id: apiKey2,
        privs: [],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toEqual('folder');
    expect(response.body.ops[0].obj_to_type).toEqual('user');
    expect(response.body.ops[0].action_type).toEqual('unlink');
    SchemaValidator.validate(linkSchema, response.body);
  });

  test('should list folder3 after unlink', async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder3 })]),
    );
    expect(response.body.ops[0].list[0].privs).toEqual(
      expect.arrayContaining([expect.objectContaining({ type: 'view' })]),
    );
    expect(response.body.ops[0].list[0].privs).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ type: 'create' })]),
    );
  });

  test('should link (sharing) folder on all', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder3,
        company_id,

        obj_to: 'all',
        obj_to_id: apiKey2,
        is_need_to_notify: true,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toEqual('folder');
    expect(response.body.ops[0].obj_to_type).toEqual('all');
    expect(response.body.ops[0].action_type).toEqual('link');
  });

  afterAll(async () => {
    const Response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(Response.status).toBe(200);

    const ResponseDeleteApiKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: apiKey2,
        company_id,
      }),
    );
    expect(ResponseDeleteApiKey.status).toBe(200);

    const ResponseDeleteGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        obj_id: group_id,
        company_id,
      }),
    );
    expect(ResponseDeleteGroup.status).toBe(200);
  });
});
