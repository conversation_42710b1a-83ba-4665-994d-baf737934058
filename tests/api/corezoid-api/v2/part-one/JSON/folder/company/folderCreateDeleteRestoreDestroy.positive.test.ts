import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { <PERSON>pi<PERSON><PERSON> } from '../../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../../application/api/SchemaValidator';
import createSchema from '../../../../../schemas/v2/actions-objects/createFolder.schema.json';
import deleteSchema from '../../../../../schemas/v2/actions-objects/deleteObject.schema.json';
import restoreSchema from '../../../../../schemas/v2/actions-objects/restoreObject.schema.json';
import destroySchema from '../../../../../schemas/v2/actions-objects/destroyObject.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../../utils/corezoidRequest';
import { makeRequestWithRetries, MyApiResponse } from '../../../../../../../../utils/requestRetries';

describe('Folder create/delete/restore/destroy in company (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newFolder: string | number;
  let newFolder2: string | number;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
  });

  test(`should create folder with required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    newFolder = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`should create folder in root company with all param`, async () => {
    const title = `Folder_${Date.now()}`;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: 0,
        description: 'test',
        title,
      }),
    );
    expect(response.status).toBe(200);
    newFolder2 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder2,
        company_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].title).toBe(title);
  });

  test(`should create folder in folder`, async () => {
    const responseCreate = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,

        folder_id: newFolder2,
        obj_type: 0,
        description: 'test',
        status: 'active',
        title: `Folder_${Date.now()}`,
      }),
    );
    expect(responseCreate.status).toBe(200);
    SchemaValidator.validate(createSchema, responseCreate.body);
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        response.body.ops[0].list.some((item: any) => item.obj_id === newFolder) &&
        response.body.ops[0].list.some((item: any) => item.obj_id === newFolder2)
      );
    }
    const response = await makeRequestWithRetries(
      async () => {
        return await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.FOLDER,
            obj_id: 0,
            company_id,
          }),
        );
      },
      checkConditions,
      {
        maxRetries: 10,
      },
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test('should delete folder with required param', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(deleteSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
        filter: 'deleted',
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder })]),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder)?.is_owner).toEqual(
      true,
    );
  });

  test('should delete folder2 with company id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder2,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder2);
    SchemaValidator.validate(deleteSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
        filter: 'deleted',
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder2 })]),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder2)?.is_owner).toEqual(
      true,
    );
  });

  test('should restore folder with company id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(restoreSchema, response.body);
  });

  test('should find folder in list_folder', async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        response.body.ops[0].list.some((item: any) => item.obj_id === newFolder) &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder)?.is_owner === true
      );
    }
    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.FOLDER,
          obj_id: 0,
          company_id,
        }),
      );
    }, checkConditions);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test('should restore folder with required param', async () => {
    const responseDelete = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(responseDelete.status).toBe(200);
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(restoreSchema, response.body);
  });

  test('should destroy folder with company_id', async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(destroySchema, response.body);
  });

  test('should not find folder in list_folder filter_deleted', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
        filter: 'deleted',
      }),
    );
    expect(response.status).toBe(200);
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newFolder)).toBeEmpty();
  });

  test('should destroy folder with required param', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder2,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder2);
    SchemaValidator.validate(destroySchema, response.body);
  });
});
