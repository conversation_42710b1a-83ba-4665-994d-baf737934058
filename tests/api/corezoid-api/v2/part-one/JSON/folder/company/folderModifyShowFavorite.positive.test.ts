import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../../application/api/SchemaValidator';
import modifySchema from '../../../../../schemas/v2/actions-objects/modifyFolder.schema.json';
import showSchema from '../../../../../schemas/v2/actions-objects/showFolder.schema.json';
import favoriteSchema from '../../../../../schemas/v2/actions-objects/favoriteObject.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../../utils/corezoidRequest';

describe('Folder modify/show/favorite in company (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newFolder: string | number;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    const title = `Folder_${Date.now()}`;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: 0,
        description: 'test',
        title,
      }),
    );
    expect(response.status).toBe(200);
    newFolder = response.body.ops[0].obj_id;
  });

  test(`should modify folder with required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(modifySchema, response.body);
  });

  test(`should modify folder with all param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        title: 'modify',
        description: 'new description',
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(modifySchema, response.body);
  });

  test(`should show folder with required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].title).toBe('modify');
    SchemaValidator.validate(showSchema, response.body);
  });

  test(`should show folder with company_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].title).toBe('modify');
    SchemaValidator.validate(showSchema, response.body);
  });

  test('should favorite folder true with required param', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        favorite: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(favoriteSchema, response.body);
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
      }),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder)?.favorite).toEqual(
      true,
    );
  });

  test('should favorite folder false with company_id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        favorite: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(favoriteSchema, response.body);
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
      }),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder)?.favorite).toEqual(
      false,
    );
  });

  afterAll(async () => {
    const Response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(Response.status).toBe(200);
  });
});
