import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../../application/api/SchemaValidator';
import createSchema from '../../../../../schemas/v2/actions-objects/createFolder.schema.json';
import deleteSchema from '../../../../../schemas/v2/actions-objects/deleteObject.schema.json';
import restoreSchema from '../../../../../schemas/v2/actions-objects/restoreObject.schema.json';
import destroySchema from '../../../../../schemas/v2/actions-objects/destroyObject.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../../utils/corezoidRequest';

describe('Folder create/delete/restore/destroy in project (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newFolder: string | number;
  let newFolder2: string | number;
  let newFolder3: number;
  let newFolder4: number;
  let newProject: number;
  let newStage: number;
  let short_name: string;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    short_name = `project${Date.now()}`;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: short_name,
        short_name,
        description: 'test',
        stages: ['develop'],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;
    newStage = response.body.ops[0].stages[0];
  });

  test(`should create folder in project with stage_id and project_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        description: 'test',
        title: `Folder_${Date.now()}`,
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    newFolder = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(responseShow.status).toBe(200);
  });

  test(`should create folder in project with project_short_name and stage_short_name`, async () => {
    const title = `Folder2_${Date.now()}`;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        description: 'test',
        title,
        project_short_name: short_name,
        stage_short_name: 'develop',
      }),
    );
    expect(response.status).toBe(200);
    newFolder2 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder2,
        company_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].title).toBe(title);
  });

  test(`should create folder in project with folder_id and project_id/stage_short_name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,

        folder_id: newFolder2,
        description: 'test',
        title: `Folder3_${Date.now()}`,
        project_id: newProject,
        stage_short_name: 'develop',
      }),
    );
    expect(response.status).toBe(200);
    newFolder3 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`should create folder in project with project_short_name and stage_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,

        description: 'test',
        title: `Folder3_${Date.now()}`,
        project_short_name: short_name,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    newFolder4 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStage,
        company_id,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder })]),
    );
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder2 })]),
    );
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder4 })]),
    );
  });

  test('should delete folder with stage_id and project_id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should delete folder3 with project_short_name and stage_short_name', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder3,
        company_id,
        project_short_name: short_name,
        stage_short_name: 'develop',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder3);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should delete folder2 with project_id and stage_short_name', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder2,
        project_id: newProject,
        stage_short_name: 'develop',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder2);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should delete folder4 with project_short_name and stage_id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder4,
        project_short_name: short_name,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder4);
    SchemaValidator.validate(deleteSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
        filter: 'deleted',
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder })]),
    );
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder2 })]),
    );
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder4 })]),
    );
  });

  test('should restore folder with stage_id and project_id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(restoreSchema, response.body);
  });

  test('should restore folder3 with project_short_name and stage_short_name', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder3,
        company_id,
        project_short_name: short_name,
        stage_short_name: 'develop',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder3);
    SchemaValidator.validate(restoreSchema, response.body);
  });

  test('should restore folder2 with project_id and stage_short_name', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder2,
        project_id: newProject,
        stage_short_name: 'develop',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder2);
    SchemaValidator.validate(restoreSchema, response.body);
  });

  test('should restore folder4 with project_short_name and stage_id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder4,
        company_id,
        project_short_name: short_name,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder4);
    SchemaValidator.validate(restoreSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStage,
        company_id,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder })]),
    );
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder2 })]),
    );
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder4 })]),
    );
  });

  test('should delete folder with required param', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should restore folder with required param', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(restoreSchema, response.body);
  });

  test('should destroy folder with stage_id and project_id', async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(destroySchema, response.body);
  });

  test('should destroy folder3 with project_short_name and stage_short_name', async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder3,
        company_id,
      }),
    );
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder3,
        company_id,
        project_short_name: short_name,
        stage_short_name: 'develop',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder3);
    SchemaValidator.validate(destroySchema, response.body);
  });

  test('should destroy folder2 with project_id and stage_short_name', async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder2,
        company_id,
      }),
    );
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder2,
        company_id,
        project_id: newProject,
        stage_short_name: 'develop',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder2);
    SchemaValidator.validate(destroySchema, response.body);
  });

  test('should destroy folder4 with project_short_name and stage_id', async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder4,
        company_id,
      }),
    );
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder4,
        company_id,
        project_short_name: short_name,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder4);
    SchemaValidator.validate(destroySchema, response.body);
  });

  test('should destroy folder with required param', async () => {
    const responseCreate = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        description: 'test',
        title: `Folder_${Date.now()}`,
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    newFolder = responseCreate.body.ops[0].obj_id;
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
      }),
    );
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(destroySchema, response.body);
  });

  afterAll(async () => {
    const Response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(Response.status).toBe(200);
  });
});
