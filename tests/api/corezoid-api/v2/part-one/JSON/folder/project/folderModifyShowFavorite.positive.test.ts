import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../../application/api/SchemaValidator';
import modifySchema from '../../../../../schemas/v2/actions-objects/modifyFolder.schema.json';
import showSchema from '../../../../../schemas/v2/actions-objects/showFolder.schema.json';
import favoriteSchema from '../../../../../schemas/v2/actions-objects/favoriteObject.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../../utils/corezoidRequest';

describe('Folder modify/show/favorite in project (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newFolder: string | number;
  let company_id: any;
  let newProject: number;
  let newStage: number;
  let short_name: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    const title = `Folder_${Date.now()}`;
    short_name = `project${Date.now()}`;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: short_name,
        short_name,
        description: 'test',
        stages: ['develop'],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;
    newStage = response.body.ops[0].stages[0];

    const responseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: newStage,
        description: 'test',
        title,
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    expect(responseFolder.status).toBe(200);
    newFolder = responseFolder.body.ops[0].obj_id;
  });

  test(`should modify folder with required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(modifySchema, response.body);
  });

  test(`should modify folder with all param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        title: 'modify',
        description: 'new description',
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(modifySchema, response.body);
  });

  test(`should show folder with required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].title).toBe('modify');
    SchemaValidator.validate(showSchema, response.body);
  });

  test(`should show folder with company_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].title).toBe('modify');
    SchemaValidator.validate(showSchema, response.body);
  });

  test(`should show folder with stage_id and project_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].title).toBe('modify');
    SchemaValidator.validate(showSchema, response.body);
  });

  test(`should show folder with project_short_name and stage_short_name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        project_short_name: short_name,
        stage_short_name: 'develop',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].title).toBe('modify');
    SchemaValidator.validate(showSchema, response.body);
  });

  test(`should show folder with project_id and stage_short_name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        project_id: newProject,
        stage_short_name: 'develop',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].title).toBe('modify');
    SchemaValidator.validate(showSchema, response.body);
  });

  test(`should show folder with project_short_name and stage_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        project_short_name: short_name,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].title).toBe('modify');
    SchemaValidator.validate(showSchema, response.body);
  });

  test('should favorite folder true with required param', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        favorite: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(favoriteSchema, response.body);
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStage,
        company_id,
      }),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder)?.favorite).toEqual(
      true,
    );
  });

  test('should favorite folder false with company_id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        favorite: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(favoriteSchema, response.body);
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStage,
        company_id,
      }),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder)?.favorite).toEqual(
      false,
    );
  });

  test('should favorite folder true with stage_id and project_id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        favorite: true,
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(favoriteSchema, response.body);
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStage,
        company_id,
      }),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder)?.favorite).toEqual(
      true,
    );
  });

  test('should favorite folder false with project_short_name and stage_short_name', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        favorite: false,
        project_short_name: short_name,
        stage_short_name: 'develop',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(favoriteSchema, response.body);
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStage,
        company_id,
      }),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder)?.favorite).toEqual(
      false,
    );
  });

  test('should favorite folder true with project_id and stage_short_name', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        favorite: true,
        project_id: newProject,
        stage_short_name: 'develop',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(favoriteSchema, response.body);
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStage,
        company_id,
      }),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder)?.favorite).toEqual(
      true,
    );
  });

  test('should favorite folder false with project_short_name and stage_id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        favorite: false,
        project_short_name: short_name,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(favoriteSchema, response.body);
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStage,
        company_id,
      }),
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder)?.favorite).toEqual(
      false,
    );
  });

  afterAll(async () => {
    const Response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(Response.status).toBe(200);
  });
});
