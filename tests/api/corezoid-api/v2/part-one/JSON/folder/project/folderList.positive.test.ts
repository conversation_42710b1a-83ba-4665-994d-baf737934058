import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { <PERSON>pi<PERSON><PERSON> } from '../../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../../application/api/SchemaValidator';
import listSchema from '../../../../../schemas/v2/actions-objects/listFolderObj.schema.json';
import listGroupSchema from '../../../../../schemas/v2/actions-objects/listFolderGroup.schema.json';
import listFilterDeletedSchema from '../../../../../schemas/v2/actions-objects/listFolderFilterDeleted.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../../utils/corezoidRequest';

describe('Folder list in project (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newApi: ApiKeyClient;
  let apiS: ApiKeyClient;
  let apiSuper: ApiKey;
  let apiKey2: string | number;
  let newFolder: number;
  let newFolder2: number;
  let newFolder3: number;
  let newConv: number;
  let newSD: number;
  let newDashboard: number;
  let newInstance: number;
  let newProject: number;
  let newStage: number;
  let short_name: string;
  let group_id: number;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    apiSuper = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apiSuper);
    company_id = apikey.companies[0].id;
    const title = `Folder_${Date.now()}`;
    short_name = `project${Date.now()}`;

    const CreateKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(200);
    const user = CreateKeyResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newApi = application.getApiKeyClient(newApiKey);
    apiKey2 = +newApiKey.id;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: short_name,
        short_name,
        description: 'test',
        stages: ['develop'],
        status: 'active',
      }),
    );
    expect(responseProject.status).toBe(200);
    newProject = responseProject.body.ops[0].obj_id;
    newStage = responseProject.body.ops[0].stages[0];

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: newStage,
        description: 'test',
        title,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    newFolder = response.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 1000));

    const response2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: newFolder,
        description: 'test',
        title,
      }),
    );
    expect(response2.status).toBe(200);
    newFolder2 = response2.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 1000));

    const response3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        folder_id: newFolder,
        title: `Conv_${Date.now()}`,
        status: 'active',
        obj_type: 0,
        description: 'test',
        create_mode: 'without_nodes',
        conv_type: 'process',
      }),
    );
    expect(response3.status).toBe(200);
    newConv = response3.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 1000));

    const responseSD = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        folder_id: newFolder,
        title: `SD_${Date.now()}`,
        status: 'active',
        obj_type: 0,
        description: 'test',
        create_mode: 'without_nodes',
        conv_type: 'state',
      }),
    );
    expect(responseSD.status).toBe(200);
    newSD = responseSD.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 1000));

    const response4 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: newFolder,
        obj_type: 0,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
      }),
    );
    expect(response4.status).toBe(200);
    newDashboard = response4.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 1000));

    const response5 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        instance_type: `db_call`,
        folder_id: newFolder,
        title: `Instance_${Date.now()}`,
        data: {
          driver: `postgres`,
          host: `COR-6938.middleware.loc`,
          port: `5432`,
          username: `postgres`,
          password: `pass`,
          ssl: false,
          database: `dbcall`,
          timeoutMs: 30000,
        },
      }),
    );
    expect(response5.status).toBe(200);
    newInstance = response5.body.ops[0].obj_id;

    const responseGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,

        obj_type: `admins`,
        title: `Group_${Date.now()}`,
      }),
    );
    group_id = responseGroup.body.ops[0].obj_id;

    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: apiKey2,
        company_id,

        group_id,
        level: 1,
      }),
    );
    expect(responseLink.status).toBe(200);

    const responseLinkFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,

        obj_to: 'group',
        obj_to_id: group_id,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(responseLinkFolder.status).toBe(200);

    const responseLinkFolder2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,

        obj_to: 'user',
        obj_to_id: apiKey2,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
          { type: 'modify', list_obj: ['all'] },
        ],
      }),
    );
    expect(responseLinkFolder2.status).toBe(200);

    await new Promise(r => setTimeout(r, 1000));

    const responseCreateFolder = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: newFolder,
        description: 'test',
        title: `Folder3_${Date.now()}`,
      }),
    );
    expect(responseCreateFolder.status).toBe(200);
    newFolder3 = responseCreateFolder.body.ops[0].obj_id;

    const responseFavorite = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder2,
        favorite: true,
      }),
    );
    expect(responseFavorite.status).toBe(200);
  });

  test('should list folder with company_id in stage', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStage,
        company_id,
      }),
    );
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder)?.favorite).toEqual(false);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder with obj_id newfolder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder2)?.favorite).toEqual(true);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder with list_obj=group', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        list_obj: 'group',
        project_short_name: short_name,
        stage_short_name: 'develop',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].obj_id).toEqual(group_id);
    SchemaValidator.validate(listGroupSchema, response.body);
  });

  test('should list folder with filter=folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        filter: 'folder',
        project_id: newProject,
        stage_short_name: 'develop',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder2 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder3 })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]),
    );
    expect(response.body.ops[0].list).not.toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newSD })]));
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDashboard })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstance })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test.skip('should list folder with filter=conv', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        filter: 'conv',
        project_short_name: short_name,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder2 })]),
    );
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newSD })]));
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDashboard })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstance })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder3 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder with filter=conveyor', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        filter: 'conveyor',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder2 })]),
    );
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newSD })]));
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDashboard })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstance })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder3 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder with filter=dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        filter: 'dashboard',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder2 })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]),
    );
    expect(response.body.ops[0].list).not.toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newSD })]));
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDashboard })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstance })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder3 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder with filter=instance', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        filter: 'instance',
        project_short_name: short_name,
        stage_short_name: 'develop',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder2 })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]),
    );
    expect(response.body.ops[0].list).not.toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newSD })]));
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDashboard })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstance })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder3 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder with filter=my', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        filter: 'my',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder2 })]),
    );
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newSD })]));
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDashboard })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstance })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder3 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder with filter=shared', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        filter: 'shared',
        project_id: newProject,
        stage_short_name: 'develop',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder2 })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]),
    );
    expect(response.body.ops[0].list).not.toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newSD })]));
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDashboard })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstance })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder3 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder with filter=favorites', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        filter: 'favorites',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder2 })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]),
    );
    expect(response.body.ops[0].list).not.toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newSD })]));
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDashboard })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstance })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder3 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder with filter=recent', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        filter: 'recent',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder2 })]),
    );
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newSD })]));
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDashboard })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstance })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder3 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder with filter=deleted', async () => {
    const responseCreate = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: newFolder,
        description: 'test',
        title: 'folder_delete',
      }),
    );
    expect(responseCreate.status).toBe(200);
    const Folder = responseCreate.body.ops[0].obj_id;

    const responseDelete = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: Folder,
        company_id,
      }),
    );
    expect(responseDelete.status).toBe(200);

    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        filter: 'deleted',
        project_id: newProject,
        stage_short_name: 'develop',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder2 })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]),
    );
    expect(response.body.ops[0].list).not.toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newSD })]));
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDashboard })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstance })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder3 })]),
    );
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: Folder })]));
    SchemaValidator.validate(listFilterDeletedSchema, response.body);
  });

  test('should list folder with sort=name', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        sort: 'name',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].obj_id).toEqual(newFolder3);
    expect(response.body.ops[0].list[1].obj_id).toEqual(newFolder2);
    expect(response.body.ops[0].list[2].obj_id).toEqual(newConv);
    expect(response.body.ops[0].list[3].obj_id).toEqual(newDashboard);
    expect(response.body.ops[0].list[4].obj_id).toEqual(newInstance);
    expect(response.body.ops[0].list[5].obj_id).toEqual(newSD);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder with sort=title', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        sort: 'title',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].obj_id).toEqual(newFolder3);
    expect(response.body.ops[0].list[1].obj_id).toEqual(newFolder2);
    expect(response.body.ops[0].list[2].obj_id).toEqual(newConv);
    expect(response.body.ops[0].list[3].obj_id).toEqual(newDashboard);
    expect(response.body.ops[0].list[4].obj_id).toEqual(newInstance);
    expect(response.body.ops[0].list[5].obj_id).toEqual(newSD);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder with sort=owner', async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        sort: 'owner',
        project_short_name: short_name,
        stage_short_name: 'develop',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].obj_id).toEqual(newFolder3);
    expect(response.body.ops[0].list[1].obj_id).toEqual(newFolder2);
    expect(response.body.ops[0].list[2].obj_id).toEqual(newSD);
    expect(response.body.ops[0].list[3].obj_id).toEqual(newConv);
    expect(response.body.ops[0].list[4].obj_id).toEqual(newDashboard);
    expect(response.body.ops[0].list[5].obj_id).toEqual(newInstance);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder with sort=date', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        sort: 'date',
        project_short_name: short_name,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].obj_id).toEqual(newFolder2);
    expect(response.body.ops[0].list[1].obj_id).toEqual(newFolder3);
    expect(response.body.ops[0].list[2].obj_id).toEqual(newConv);
    expect(response.body.ops[0].list[3].obj_id).toEqual(newSD);
    expect(response.body.ops[0].list[4].obj_id).toEqual(newDashboard);
    expect(response.body.ops[0].list[5].obj_id).toEqual(newInstance);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder with order=asc', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        order: 'asc',
        sort: 'name',
        project_short_name: short_name,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].obj_id).toEqual(newFolder3);
    expect(response.body.ops[0].list[1].obj_id).toEqual(newFolder2);
    expect(response.body.ops[0].list[2].obj_id).toEqual(newConv);
    expect(response.body.ops[0].list[3].obj_id).toEqual(newDashboard);
    expect(response.body.ops[0].list[4].obj_id).toEqual(newInstance);
    expect(response.body.ops[0].list[5].obj_id).toEqual(newSD);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder with order=desc', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        order: 'desc',
        sort: 'name',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].obj_id).toEqual(newFolder2);
    expect(response.body.ops[0].list[1].obj_id).toEqual(newFolder3);
    expect(response.body.ops[0].list[2].obj_id).toEqual(newSD);
    expect(response.body.ops[0].list[3].obj_id).toEqual(newInstance);
    expect(response.body.ops[0].list[4].obj_id).toEqual(newDashboard);
    expect(response.body.ops[0].list[5].obj_id).toEqual(newConv);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list folder with owner_id', async () => {
    const responseLinkFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,

        obj_to: 'group',
        obj_to_id: group_id,
        privs: [],
      }),
    );
    expect(responseLinkFolder.status).toBe(200);

    const responseLinkFolder2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,

        obj_to: 'user',
        obj_to_id: apiKey2,
        privs: [],
      }),
    );
    expect(responseLinkFolder2.status).toBe(200);

    await new Promise(r => setTimeout(r, 1000));

    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        owner_id: apiKey2,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder2 })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]),
    );
    expect(response.body.ops[0].list).not.toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newSD })]));
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newDashboard })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newInstance })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder3 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  afterAll(async () => {
    const Response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(Response.status).toBe(200);

    const ResponseDeleteApiKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: apiKey2,
        company_id,
      }),
    );
    expect(ResponseDeleteApiKey.status).toBe(200);

    const ResponseDeleteGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        obj_id: group_id,
        company_id,
      }),
    );
    expect(ResponseDeleteGroup.status).toBe(200);

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(responseProject.status).toBe(200);
  });
});
