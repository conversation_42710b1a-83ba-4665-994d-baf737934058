import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createSchema from '../../../../schemas/v2/actions-objects/createFolder.schema.json';
import deleteSchema from '../../../../schemas/v2/actions-objects/deleteObject.schema.json';
import restoreSchema from '../../../../schemas/v2/actions-objects/restoreObject.schema.json';
import destroySchema from '../../../../schemas/v2/actions-objects/destroyObject.schema.json';
import modifySchema from '../../../../schemas/v2/actions-objects/modifyFolder.schema.json';
import showSchema from '../../../../schemas/v2/actions-objects/showFolder.schema.json';
import linkSchema from '../../../../schemas/v2/actions-objects/linkMoveFolder.schema.json';
import favoriteSchema from '../../../../schemas/v2/actions-objects/favoriteObject.schema.json';
import listSchema from '../../../../schemas/v2/actions-objects/listFolderObj.schema.json';
import copySync from '../../../../schemas/v2/actions-objects/copyObjectSync.schema.json';
import copyAsync from '../../../../schemas/v2/actions-objects/copyObjectAsync.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { makeRequestWithRetries, MyApiResponse } from '../../../../../../../utils/requestRetries';

describe('Folder create/delete/restore/destroy in my corezoid (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newFolder: string | number;
  let newFolder2: number;
  let linkFolder: string | number;
  let company_id: any;
  let copyFolderSync: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = null;
  });

  test(`should create folder`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: 0,
        obj_type: 0,
        description: 'test',
        status: 'active',
        title: `Folder_${Date.now()}`,
      }),
    );
    expect(response.status).toBe(200);
    newFolder = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`should create folder with required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
      }),
    );
    expect(response.status).toBe(200);
    newFolder2 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test('should find create folder in list_folder', async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        response.body.ops[0].list.some((item: any) => item.obj_id === newFolder) &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder)?.is_owner === true
      );
    }
    const response = await makeRequestWithRetries(
      async () => {
        return await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.FOLDER,
            obj_id: 0,
            company_id,
          }),
        );
      },
      checkConditions,
      {
        maxRetries: 10,
      },
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test(`should modify folder`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        title: `ModifyFolder`,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(modifySchema, response.body);
  });

  test(`should show folder`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].title).toBe('ModifyFolder');
    SchemaValidator.validate(showSchema, response.body);
  });

  test('should link folder', async () => {
    const responseCreateFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: 0,
        obj_type: 0,
        description: 'test',
        status: 'active',
        title: `Folder_${Date.now()}`,
      }),
    );
    linkFolder = responseCreateFolder.body.ops[0].obj_id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: linkFolder,
        company_id,
        folder_id: newFolder,
        obj_type: 'folder',
        parent_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_type).toBe('folder');
    expect(response.body.ops[0].obj_id).toBe(linkFolder);
    expect(response.body.ops[0].obj_to_id).toBe(newFolder);
    expect(response.body.ops[0].obj_to_title).toBe('ModifyFolder');
    expect(response.body.ops[0].from_folder).toBe(0);
    expect(response.body.ops[0].to_folder).toBe(newFolder);
    expect(response.body.ops[0].to_folder_name).toBe('ModifyFolder');
    SchemaValidator.validate(linkSchema, response.body);
  });

  test('should find link folder in list_folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: linkFolder })]),
    );
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === linkFolder).is_owner).toEqual(true);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should link folder back', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: linkFolder,
        company_id,
        folder_id: 0,
        obj_type: 'folder',
        parent_id: newFolder,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_type).toBe('folder');
    expect(response.body.ops[0].obj_id).toBe(linkFolder);
    expect(response.body.ops[0].obj_to_id).toBe(0);
    expect(response.body.ops[0].obj_to_title).toBe('Root folder');
    expect(response.body.ops[0].from_folder).toBe(newFolder);
    expect(response.body.ops[0].to_folder).toBe(0);
    expect(response.body.ops[0].to_folder_name).toBe('Root folder');
    SchemaValidator.validate(linkSchema, response.body);
  });

  test('should find link folder in list_folder after back', async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        response.body.ops[0].list.some((item: any) => item.obj_id === linkFolder) &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === linkFolder)?.is_owner === true
      );
    }
    const response = await makeRequestWithRetries(
      async () => {
        return await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.FOLDER,
            obj_id: 0,
            company_id,
          }),
        );
      },
      checkConditions,
      {
        maxRetries: 10,
      },
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test('should copy folder sync', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolder,

        folder_id: 0,
        obj_type: 'folder',
        title: 'copyFolderSync',
        ignore_errors: true,
        async: false,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].scheme[0].old_obj_id).toBe(newFolder);
    expect(response.body.ops[0].scheme[0].obj_type).toBe('folder');
    expect(response.body.ops[0].scheme[0].title).toBe('copyFolderSync');
    copyFolderSync = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copySync, response.body);
  });

  test('should copy folder async', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: linkFolder,

        folder_id: newFolder,
        obj_type: 'folder',
        title: 'copyFolderAsync',
        ignore_errors: true,
        async: true,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('obj_copy');
    SchemaValidator.validate(copyAsync, response.body);
  });

  test('should find copy folder in list_folder and after delete/destroy', async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        response.body.ops[0].list.some((item: any) => item.obj_id === copyFolderSync) &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === copyFolderSync)?.is_owner === true
      );
    }
    const response = await makeRequestWithRetries(
      async () => {
        return await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.FOLDER,
            obj_id: 0,
            company_id,
          }),
        );
      },
      checkConditions,
      {
        maxRetries: 10,
      },
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');

    const deleteresponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: copyFolderSync,
        company_id,
      }),
    );
    expect(deleteresponse.status).toBe(200);
    expect(deleteresponse.body.ops[0].obj_id).toBe(copyFolderSync);
    const destroyresponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: copyFolderSync,
        company_id,
      }),
    );
    expect(destroyresponse.status).toBe(200);
    expect(destroyresponse.body.ops[0].obj_id).toBe(copyFolderSync);
  });

  test('should copy folder sync in other folder in company', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: linkFolder,

        folder_id: newFolder,
        obj_type: 'folder',
        title: 'copyFolderSync',
        ignore_errors: true,
        async: false,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].scheme[0].old_obj_id).toBe(linkFolder);
    expect(response.body.ops[0].scheme[0].obj_type).toBe('folder');
    expect(response.body.ops[0].scheme[0].title).toBe('copyFolderSync');
    copyFolderSync = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copySync, response.body);
  });

  test('should find copy folder sync in other folder in company', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: copyFolderSync })]),
    );
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === copyFolderSync).is_owner).toEqual(
      true,
    );
  });

  test('should copy folder sync with company in my corezoid', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: linkFolder,

        folder_id: 0,
        obj_type: 'folder',
        title: 'copyFolderSync',
        ignore_errors: true,
        async: false,
        from_company_id: company_id,
        to_company_id: null,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].scheme[0].old_obj_id).toBe(linkFolder);
    expect(response.body.ops[0].scheme[0].obj_type).toBe('folder');
    expect(response.body.ops[0].scheme[0].title).toBe('copyFolderSync');
    copyFolderSync = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copySync, response.body);
  });

  test('should copy folder sync in my corezoid', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: copyFolderSync,

        folder_id: copyFolderSync,
        obj_type: 'folder',
        title: 'copyFolderSyncMyCorezoid',
        ignore_errors: true,
        async: false,
        from_company_id: null,
        to_company_id: null,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].scheme[0].old_obj_id).toBe(copyFolderSync);
    expect(response.body.ops[0].scheme[0].obj_type).toBe('folder');
    expect(response.body.ops[0].scheme[0].title).toBe('copyFolderSyncMyCorezoid');
  });

  test('should copy folder sync with my corezoid in company', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: copyFolderSync,

        folder_id: newFolder,
        obj_type: 'folder',
        title: 'newcopyFolderSync',
        ignore_errors: true,
        async: false,
        from_company_id: null,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].scheme[0].old_obj_id).toBe(copyFolderSync);
    expect(response.body.ops[0].scheme[0].obj_type).toBe('folder');
    expect(response.body.ops[0].scheme[0].title).toBe('newcopyFolderSync');
    SchemaValidator.validate(copySync, response.body);
  });

  test('should find copy folder in my corezoid and after delete/destroy', async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        response.body.ops[0].list.some((item: any) => item.obj_id === copyFolderSync) &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === copyFolderSync)?.is_owner === true
      );
    }
    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.FOLDER,
          obj_id: 0,
          company_id: null,
        }),
      );
    }, checkConditions);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');

    const deleteresponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: copyFolderSync,
        company_id: null,
      }),
    );
    expect(deleteresponse.status).toBe(200);
    expect(deleteresponse.body.ops[0].obj_id).toBe(copyFolderSync);
    const destroyresponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: copyFolderSync,
        company_id: null,
      }),
    );
    expect(destroyresponse.status).toBe(200);
    expect(destroyresponse.body.ops[0].obj_id).toBe(copyFolderSync);
  });

  test('should favorite folder true', async () => {
    const responseFavorite = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        favorite: true,
      }),
    );
    expect(responseFavorite.status).toBe(200);
    expect(responseFavorite.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(favoriteSchema, responseFavorite.body);
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder)?.favorite === true
      );
    }
    const response = await makeRequestWithRetries(
      async () => {
        return await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.FOLDER,
            obj_id: 0,
            company_id,
          }),
        );
      },
      checkConditions,
      {
        maxRetries: 10,
      },
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test('should favorite folder false', async () => {
    const responseFavorite = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
        favorite: false,
      }),
    );
    expect(responseFavorite.status).toBe(200);
    expect(responseFavorite.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(favoriteSchema, responseFavorite.body);
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 &&
        (response.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder)?.favorite === false
      );
    }
    const response = await makeRequestWithRetries(async () => {
      return await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.FOLDER,
          obj_id: 0,
          company_id,
        }),
      );
    }, checkConditions);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test('should delete folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should delete folder with required param', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder2,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder2);
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should find folder in list_folder filter_deleted', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
        filter: 'deleted',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newFolder })]));
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder)?.is_owner).toEqual(true);
  });

  test('should not find folder in list_folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newFolder)).toBeEmpty();
  });

  test('should restore folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(restoreSchema, response.body);
  });

  test('should find folder in list_folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newFolder })]));
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder)?.is_owner).toEqual(true);
  });

  test('should not find folder in list_folder filter_deleted', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
        filter: 'deleted',
      }),
    );
    expect(response.status).toBe(200);
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newFolder)).toBeEmpty();
  });

  test('should destroy folder linkFolder', async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: linkFolder,
        company_id,
      }),
    );
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: linkFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(linkFolder);
    SchemaValidator.validate(destroySchema, response.body);
  });

  test('should destroy folder', async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newFolder);
    SchemaValidator.validate(destroySchema, response.body);
  });

  test('should not find folder in list_folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newFolder)).toBeEmpty();
  });

  test('should not find folder in list_folder filter_deleted', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
        filter: 'deleted',
      }),
    );
    expect(response.status).toBe(200);
    expect((response.body.ops[0].list as Array<any>).filter(item => item.obj_id === newFolder)).toBeEmpty();
  });
});
