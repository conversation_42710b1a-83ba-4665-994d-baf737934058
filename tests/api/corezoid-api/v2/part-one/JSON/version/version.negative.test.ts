import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  createRequestWithObj,
  OBJ_TYPE,
  REQUEST_TYPE,
} from '../../../../../../../utils/corezoidRequest';
import faker from 'faker';
import { integerTestCases, undefinedTestCase, companyTestCases } from '../../../../../negativeCases';

describe('Version (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let newVersion: string | number;
  let company_id: any;
  const valuesToSkip: any = [0];
  let project_short_name: string;
  let stage_short_name: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = `project-${Date.now()}`;
    stage_short_name = `stage-${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        title: `Conv_${Date.now()}`,
        conv_type: 'process',
        create_mode: 'without_nodes',
        stage_id: newStage,
        project_id: newProject,
      }),
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,

        title: `Folder_${Date.now()}`,
        obj_type: 0,
        stage_id: newStage,
        project_id: newProject,
      }),
    );

    const responseVersion = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn: `${Date.now()}`,
        stage_from_id: newStage,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    newVersion = responseVersion.body.ops[0].obj_id;
  });

  test.each([
    ['test', 'Company test does not exists'],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [1234, `Value '1234' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'company_id' is required`],
  ])(`shouldn't create version with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn: `${Date.now()}`,
        stage_from_id: newStage,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid project_id or Key 'project_short_name' is required`],
    ['test', `Value 'test' is not valid project_id or Key 'project_short_name' is required`],
    [true, `Value 'true' is not valid project_id or Key 'project_short_name' is required`],
    [{}, `Value '{[]}' is not valid project_id or Key 'project_short_name' is required`],
    [[], `Value '' is not valid project_id or Key 'project_short_name' is required`],
    ['', `Value '' is not valid project_id or Key 'project_short_name' is required`],
    [1, `Object project with id 1 does not exist`],
    [-1, `Value '-1' is not valid project_id or Key 'project_short_name' is required`],
    [0, `Object project with id 0 does not exist`],
    [1234, `Object project with id 1234 does not exist`],
    [undefined, `Object project with id 0 does not exist`],
  ])(`shouldn't create version with invalid project_id '%s'`, async (project_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn: `${Date.now()}`,
        stage_from_id: newStage,
        changelog: 'test',
        project_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'vsn' is required`],
  ])(`shouldn't create version with invalid vsn '%s'`, async (vsn, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn,
        stage_from_id: newStage,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      true,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[null]\">> or Value 'true' is not valid. Type of value is not 'binary'`,
    ],
    [
      {},
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[null]\">> or Value '{[]}' is not valid. Type of value is not 'binary'`,
    ],
    [
      [],
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[null]\">> or Value '[]' is not valid. Type of value is not 'binary'`,
    ],
    [
      1,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[null]\">> or Value '1' is not valid. Type of value is not 'binary'`,
    ],
    [
      -1,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[null]\">> or Value '-1' is not valid. Type of value is not 'binary'`,
    ],
    [
      0,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[null]\">> or Value '0' is not valid. Type of value is not 'binary'`,
    ],
    [faker.random.alphaNumeric(5001), `Value is not valid. Value's byte_size is more than maximum allowed: 5000`],
  ])(`shouldn't create version with invalid changelog '%s'`, async (changelog, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn: `${Date.now()}`,
        stage_from_id: newStage,
        changelog,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([true, {}, [], 1, -1, 0, faker.random.alphaNumeric(257), ''])(
    `shouldn't create version with invalid title '%s'`,
    async title => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.VERSION,
          company_id,

          vsn: `${Date.now()}`,
          stage_from_id: newStage,
          changelog: '',
          title,
          project_id: newProject,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toContain('version title is not valid');
    },
  );

  test.each([
    [null, `Value 'null' is not valid stage_id`],
    ['test', `Value 'test' is not valid stage_id`],
    [true, `Value 'true' is not valid stage_id`],
    [{}, `Value '{[]}' is not valid stage_id`],
    [[], `Value '' is not valid stage_id`],
    ['', `Value '' is not valid stage_id`],
    [1, `Stage not found`],
    [1234, `Stage not found`],
    [-1, `Value '-1' is not valid stage_id`],
    [0, `Value '0' is not valid stage_id`],
    [undefined, `Stage not found`],
  ])(`shouldn't create version with invalid stage_from_id '%s'`, async (stage_from_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn: `${Date.now()}`,
        stage_from_id,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Stage not found`],
    ['test', `Stage not found`],
    [true, `Stage not found`],
    [{}, `Stage not found`],
    [[], `Stage not found`],
    ['', `Stage not found`],
    [1, `Stage not found`],
    [-1, `Stage not found`],
    [0, `Stage not found`],
    [1234, `Stage not found`],
    [undefined, `Stage not found`],
  ])(`shouldn't create version with invalid stage_short_name '%s'`, async (stage_short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn: `${Date.now()}`,
        stage_short_name,
        changelog: 'test',
        project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Stage not found`],
    ['test', `Project is not found`],
    [true, `Stage not found`],
    [{}, `Stage not found`],
    [[], `Stage not found`],
    ['', `Stage not found`],
    [1, `Stage not found`],
    [-1, `Stage not found`],
    [0, `Stage not found`],
    [1234, `Stage not found`],
    [undefined, `Stage not found`],
  ])(`shouldn't create version with invalid project_short_name '%s'`, async (project_short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn: `${Date.now()}`,
        stage_short_name,
        changelog: 'test',
        project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', 'Company test does not exists'],
    [true, 'Value is not valid'],
    [{}, 'Value is not valid'],
    [[], 'Value is not valid'],
    ['', 'Value is not valid'],
    [1, `Value is not valid`],
    [-1, `Value is not valid`],
    [0, `Value is not valid`],
    [1234, `Value is not valid`],
  ])(`shouldn't modify version with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn: `${Date.now()}`,
        obj_id: newVersion,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid project_id or Key 'project_short_name' is required`],
    ['test', `Value 'test' is not valid project_id or Key 'project_short_name' is required`],
    [true, `Value 'true' is not valid project_id or Key 'project_short_name' is required`],
    [{}, `Value '{[]}' is not valid project_id or Key 'project_short_name' is required`],
    [[], `Value '' is not valid project_id or Key 'project_short_name' is required`],
    ['', `Value '' is not valid project_id or Key 'project_short_name' is required`],
    [1, `Project of object do not matches projectId of request`],
    [-1, `Value '-1' is not valid project_id or Key 'project_short_name' is required`],
    [1234, `Project of object do not matches projectId of request`],
  ])(`shouldn't modify version with invalid project_id '%s'`, async (project_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn: `${Date.now()}`,
        obj_id: newVersion,
        changelog: 'test',
        project_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([['test', `Project is not found`]])(
    `shouldn't modify version with invalid project_short_name '%s'`,
    async (project_short_name, reason) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.VERSION,
          company_id,

          vsn: `${Date.now()}`,
          obj_id: newVersion,
          changelog: 'test',
          project_short_name,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([
    [true, `Value is not valid`],
    [{}, `Value is not valid`],
    [[], `Value is not valid`],
    [1, `Value is not valid`],
    [-1, `Value is not valid`],
    [0, `Value is not valid`],
    [faker.random.alphaNumeric(5001), `Value is not valid`],
  ])(`shouldn't modify version with invalid changelog '%s'`, async (changelog, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn: `${Date.now()}`,
        obj_id: newVersion,
        changelog,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([
    [null, `Value is not valid`],
    ['test', `Value is not valid`],
    [true, `Value is not valid`],
    [{}, `Value is not valid`],
    [[], `Value is not valid`],
    ['', `Value is not valid`],
    [1, `Object version with id 1 does not exist`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [0, `Object version with id 0 does not exist`],
    [1234, `Object version with id 1234 does not exist`],
    [undefined, `Key 'obj_id' is required`],
  ])(`shouldn't modify version with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn: `${Date.now()}`,
        obj_id,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', 'User not in company'],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [1234, `Value '1234' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'company_id' is required`],
  ])(`shouldn't list versions with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.VERSIONS,
        company_id,
        project_id: newProject,
        sort: 'vsn',
        order: 'asc',
        filter: 'all',
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid project_id or Key 'project_short_name' is required`],
    ['test', `Value 'test' is not valid project_id or Key 'project_short_name' is required`],
    [true, `Value 'true' is not valid project_id or Key 'project_short_name' is required`],
    [{}, `Value '{[]}' is not valid project_id or Key 'project_short_name' is required`],
    [[], `Value '' is not valid project_id or Key 'project_short_name' is required`],
    ['', `Value '' is not valid project_id or Key 'project_short_name' is required`],
    [1, `Object project with id 1 does not exist`],
    [-1, `Value '-1' is not valid project_id or Key 'project_short_name' is required`],
    [1234, `Object project with id 1234 does not exist`],
  ])(`shouldn't list versions with invalid project_id '%s'`, async (project_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.VERSIONS,
        company_id,
        project_id,
        sort: 'vsn',
        order: 'asc',
        filter: 'all',
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      null,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>,<<\\\"deleted\\\">>,<<\\\"active\\\">>]\">>`,
    ],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>,<<\\\"deleted\\\">>,<<\\\"active\\\">>]\">>`,
    ],
    [
      true,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>,<<\\\"deleted\\\">>,<<\\\"active\\\">>]\">>`,
    ],
    [
      {},
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>,<<\\\"deleted\\\">>,<<\\\"active\\\">>]\">>`,
    ],
    [
      [],
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>,<<\\\"deleted\\\">>,<<\\\"active\\\">>]\">>`,
    ],
    [
      '',
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>,<<\\\"deleted\\\">>,<<\\\"active\\\">>]\">>`,
    ],
    [
      1,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>,<<\\\"deleted\\\">>,<<\\\"active\\\">>]\">>`,
    ],
    [
      -1,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>,<<\\\"deleted\\\">>,<<\\\"active\\\">>]\">>`,
    ],
    [
      1234,
      `Value '<<\"1234\">>' is not valid. Value is not in allowed list <<\"[<<\\\"all\\\">>,<<\\\"deleted\\\">>,<<\\\"active\\\">>]\">>`,
    ],
  ])(`shouldn't list versions with invalid filter '%s'`, async (filter, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.VERSIONS,
        company_id,
        project_id: newProject,
        sort: 'vsn',
        order: 'asc',
        filter,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([['test', `Project is not found`]])(
    `shouldn't list versions with invalid project_short_name '%s'`,
    async (project_short_name, reason) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.VERSIONS,
          company_id,
          project_short_name,
          sort: 'vsn',
          order: 'asc',
          filter: 'all',
          stage_id: newStage,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([
    [
      null,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"title\\\">>,<<\\\"vsn\\\">>,<<\\\"date\\\">>,<<\\\"status\\\">>]\">>`,
    ],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"title\\\">>,<<\\\"vsn\\\">>,<<\\\"date\\\">>,<<\\\"status\\\">>]\">>`,
    ],
    [
      true,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"title\\\">>,<<\\\"vsn\\\">>,<<\\\"date\\\">>,<<\\\"status\\\">>]\">>`,
    ],
    [
      {},
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"title\\\">>,<<\\\"vsn\\\">>,<<\\\"date\\\">>,<<\\\"status\\\">>]\">>`,
    ],
    [
      [],
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"title\\\">>,<<\\\"vsn\\\">>,<<\\\"date\\\">>,<<\\\"status\\\">>]\">>`,
    ],
    [
      '',
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"title\\\">>,<<\\\"vsn\\\">>,<<\\\"date\\\">>,<<\\\"status\\\">>]\">>`,
    ],
    [
      1,
      `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"title\\\">>,<<\\\"vsn\\\">>,<<\\\"date\\\">>,<<\\\"status\\\">>]\">>`,
    ],
    [
      -1,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"title\\\">>,<<\\\"vsn\\\">>,<<\\\"date\\\">>,<<\\\"status\\\">>]\">>`,
    ],
    [
      1234,
      `Value '<<\"1234\">>' is not valid. Value is not in allowed list <<\"[<<\\\"title\\\">>,<<\\\"vsn\\\">>,<<\\\"date\\\">>,<<\\\"status\\\">>]\">>`,
    ],
  ])(`shouldn't list versions with invalid sort '%s'`, async (sort, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.VERSIONS,
        company_id,
        project_id: newProject,
        sort,
        order: 'asc',
        filter: 'all',
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      null,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`,
    ],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`,
    ],
    [
      true,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`,
    ],
    [{}, `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`],
    [[], `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`],
    ['', `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`],
    [1, `Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`],
    [-1, `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`],
    [
      1234,
      `Value '<<\"1234\">>' is not valid. Value is not in allowed list <<\"[<<\\\"asc\\\">>,<<\\\"desc\\\">>]\">>`,
    ],
  ])(`shouldn't list versions with invalid order '%s'`, async (order, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.VERSIONS,
        company_id,
        project_id: newProject,
        sort: 'vsn',
        order,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([...integerTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't upload version in stage with invalid stage_to_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithObj({
          type: REQUEST_TYPE.UPLOAD,
          obj: OBJ_TYPE.VERSION,
          version_id: newVersion,
          stage_to_id: input,
          project_id: newProject,
          company_id,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test(`shouldn't upload version with invalid stage_id 'undefined'`, async () => {
    const response = await api.request(
      createRequestWithObj({
        type: REQUEST_TYPE.UPLOAD,
        obj: OBJ_TYPE.VERSION,
        version_id: newVersion,
        stage_to_id: undefined,
        project_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors[0].errors[0]).toContain('stage with that short name already exists');
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't upload version in stage with invalid version_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithObj({
          type: REQUEST_TYPE.UPLOAD,
          obj: OBJ_TYPE.VERSION,
          version_id: input,
          stage_to_id: newStage,
          project_id: newProject,
          company_id,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't upload version in stage with invalid project_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithObj({
        type: REQUEST_TYPE.UPLOAD,
        obj: OBJ_TYPE.VERSION,
        version_id: newVersion,
        stage_to_id: newStage,
        project_id: input,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...companyTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't upload version in stage with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithObj({
          type: REQUEST_TYPE.UPLOAD,
          obj: OBJ_TYPE.VERSION,
          version_id: newVersion,
          stage_to_id: newStage,
          project_id: newProject,
          company_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([
    ['test', 'Company test does not exists'],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [1234, `Value '1234' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'company_id' is required`],
  ])(`shouldn't delete version with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersion,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value is not valid`],
    ['test', `Value is not valid`],
    [true, `Value is not valid`],
    [{}, `Value is not valid`],
    [[], `Value is not valid`],
    ['', `Value is not valid`],
    [1, `Couldnt delete version`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [1234, `Couldnt delete version`],
    [undefined, `Key 'obj_id' is required`],
  ])(`shouldn't delete version with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.VERSION,
        obj_id,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Key 'project_short_name' is required or Value 'null' is not valid project_id`],
    ['test', `Key 'project_short_name' is required or Value 'test' is not valid project_id`],
    [true, `Key 'project_short_name' is required or Value 'true' is not valid project_id`],
    [{}, `Key 'project_short_name' is required or Value '{[]}' is not valid project_id`],
    [[], `Key 'project_short_name' is required or Value '' is not valid project_id`],
    ['', `Key 'project_short_name' is required or Value '' is not valid project_id`],
    [1, `Object project with id 1 does not exist`],
    [-1, `Key 'project_short_name' is required or Value '-1' is not valid project_id`],
    [1234, `Object project with id 1234 does not exist`],
  ])(`shouldn't delete version with invalid project_id '%s'`, async (project_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersion,
        company_id,
        project_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      null,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    ['test', `Project is not found`],
    [
      true,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      {},
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      [],
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      '',
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      -1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      1234,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
  ])(`shouldn't delete version with invalid project_short_name '%s'`, async (project_short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersion,
        company_id,
        project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', `does not exist`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [1234, `Value '1234' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'company_id' is required`],
  ])(`shouldn't restore version with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersion,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([
    [null, `Value is not valid`],
    ['test', `Value is not valid`],
    [true, `Value is not valid`],
    [{}, `Value is not valid`],
    [[], `Value is not valid`],
    ['', `Value is not valid`],
    [1, `Object version with id 1 does not exist`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [1234, `Object version with id 1234 does not exist`],
    [undefined, `Key 'obj_id' is required`],
  ])(`shouldn't restore version with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.VERSION,
        obj_id,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid project_id`],
    ['test', `Value 'test' is not valid project_id`],
    [true, `Value 'true' is not valid project_id`],
    [{}, `Value '{[]}' is not valid project_id`],
    [[], `Value '' is not valid project_id`],
    ['', `Value '' is not valid project_id`],
    [1, `does not exist`],
    [1234, `does not exist`],
  ])(`shouldn't restore version with invalid project_id '%s'`, async (project_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersion,
        company_id,
        project_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([
    [
      null,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    ['test', `Project is not found`],
    [
      true,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      {},
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      [],
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      '',
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      1234,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
  ])(`shouldn't restore version with invalid project_short_name '%s'`, async (project_short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersion,
        company_id,
        project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([
    ['test', `does not exist`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [1234, `Value '1234' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'company_id' is required`],
  ])(`shouldn't destroy version with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersion,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([
    [null, `Value is not valid`],
    ['test', `Value is not valid`],
    [true, `Value is not valid`],
    [{}, `Value is not valid`],
    [[], `Value is not valid`],
    ['', `Value is not valid`],
    [1, `Object version with id 1 does not exist`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [1234, `Object version with id 1234 does not exist`],
    [undefined, `Key 'obj_id' is required`],
  ])(`shouldn't destroy version with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.VERSION,
        obj_id,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid project_id`],
    ['test', `Value 'test' is not valid project_id`],
    [true, `Value 'true' is not valid project_id`],
    [{}, `Value '{[]}' is not valid project_id`],
    [[], `Value '' is not valid project_id`],
    ['', `Value '' is not valid project_id`],
    [1, `does not exist`],
    [1234, `does not exist`],
  ])(`shouldn't destroy version with invalid project_id '%s'`, async (project_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersion,
        company_id,
        project_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  test.each([
    [
      null,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    ['test', `Project is not found`],
    [
      true,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      {},
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      [],
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      '',
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
    [
      1234,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols or Key 'project_id' is required`,
    ],
  ])(`shouldn't destroy version with invalid project_short_name '%s'`, async (project_short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersion,
        company_id,
        project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(reason);
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
