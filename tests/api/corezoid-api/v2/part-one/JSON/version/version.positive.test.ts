import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  createRequestWithObj,
  OBJ_TYPE,
  REQUEST_TYPE,
} from '../../../../../../../utils/corezoidRequest';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createSchema from '../../../../schemas/v2/project/createVersion.schema.json';
import modifySchema from '../../../../schemas/v2/project/modifyVersion.schema.json';
import getSchema from '../../../../schemas/v2/project/getVersion.schema.json';
import listSchema from '../../../../schemas/v2/project/listVersion.schema.json';
import deleteSchema from '../../../../schemas/v2/project/deleteVersion.schema.json';
import destroySchema from '../../../../schemas/v2/project/destroyVersion.schema.json';
import uploadSchema from '../../../../schemas/v2/project/uploadVersion.schema.json';
import uploadAsyncSchema from '../../../../schemas/v2/project/uploadAsyncVersion.schema.json';

describe('Version (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let newVersion: string | number;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: `Project_${Date.now()}`,
        short_name: `project-${Date.now()}`,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        title: `Stage_${Date.now()}`,
        short_name: `stage-${Date.now()}`,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `Conv_${Date.now()}`,
        conv_type: 'process',
        create_mode: 'without_nodes',
        stage_id: newStage,
        project_id: newProject,
      }),
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        title: `Folder_${Date.now()}`,
        obj_type: 0,
        stage_id: newStage,
        project_id: newProject,
      }),
    );
  });

  test(`should create Version`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,
        vsn: `${Date.now()}`,
        stage_id: newStage,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('version');
    newVersion = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.VERSIONS,
        company_id,
        project_id: newProject,
        sort: 'vsn',
        order: 'asc',
        filter: 'all',
        stage_id: newStage,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[0].obj_type).toBe('version');
    expect(responseList.body.ops[0].list[0].obj_id).toBe(newVersion);
  });

  test(`should list Versions`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.VERSIONS,
        company_id,
        project_id: newProject,
        sort: 'vsn',
        order: 'asc',
        filter: 'all',
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('versions');
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should list Versions (only required parameter)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.VERSIONS,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('versions');
    SchemaValidator.validate(listSchema, response.body);
  });

  test(`should modify Version`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersion,
        company_id,
        vsn: `111.111.111`,
        changelog: `modify`,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('version');
    SchemaValidator.validate(modifySchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.VERSIONS,
        company_id,
        project_id: newProject,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[0].obj_type).toBe('version');
    expect(responseList.body.ops[0].list[0].obj_id).toBe(newVersion);
    expect(responseList.body.ops[0].list[0].changelog).toBe('modify');
  });

  test(`should get Version`, async () => {
    await new Promise(r => setTimeout(r, 1000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersion,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('version');
    SchemaValidator.validate(getSchema, response.body);
  });

  test(`should get Version (only required parameter)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersion,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('version');
    expect(response.body.ops[0].status).toBe(1);
    SchemaValidator.validate(getSchema, response.body);
  });

  test(`should create Version (required parameter)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,
        vsn: `${Date.now()}`,
        stage_from_id: newStage,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('version');
    newVersion = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);
  });

  test(`should upload Version (async:false by default)`, async () => {
    const response = await api.request(
      createRequestWithObj({
        type: REQUEST_TYPE.UPLOAD,
        obj: OBJ_TYPE.VERSION,
        version_id: newVersion,
        stage_to_id: newStage,
        project_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('version');
    expect(response.body.ops[0].stage_to_id).toBe(newStage);
    SchemaValidator.validate(uploadSchema, response.body);
  });

  test(`should upload Version (async:true)`, async () => {
    const response = await api.request(
      createRequestWithObj({
        type: REQUEST_TYPE.UPLOAD,
        obj: OBJ_TYPE.VERSION,
        version_id: `${newVersion}`,
        stage_to_id: newStage,
        project_id: newProject,
        async: true,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].hash).toBeString;
    SchemaValidator.validate(uploadAsyncSchema, response.body);
  });

  test('should delete Version number', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersion,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newVersion);
    expect(response.body.ops[0].obj).toBe('version');
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should restore Version number', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersion,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newVersion);
    expect(response.body.ops[0].obj).toBe('version');
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test('should delete Version string', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.VERSION,
        obj_id: `${newVersion}`,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newVersion);
    expect(response.body.ops[0].obj).toBe('version');
    SchemaValidator.validate(deleteSchema, response.body);
  });

  test(`should get Version (after delete)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersion,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toContain(`Object version with id ${newVersion} does not exist`);
  });

  test('should destroy Version', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersion,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('version');
    expect(response.body.ops[0].obj_id).toBe(newVersion);
    SchemaValidator.validate(destroySchema, response.body);
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
