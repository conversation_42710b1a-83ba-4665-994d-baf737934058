import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import listSchema from '../../../../schemas/v2/actions-objects/listDashboards.schema.json';

describe('Dashboards (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let company_id: any;
  let dashboard_id: string | number;
  let dashboard_id_2: string | number;
  let project_short_name: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = `project${Date.now()}`;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: ['production'],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;
    newStage = response.body.ops[0].stages[0];

    const responseCreate = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: newStage,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseCreate.status).toBe(200);
    dashboard_id = responseCreate.body.ops[0].obj_id;

    const responseCreate2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: newStage,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseCreate2.status).toBe(200);
    dashboard_id_2 = responseCreate2.body.ops[0].obj_id;
  });

  test('should list dashboards required param', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.DASHBOARDS,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list dashboards in company', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.DASHBOARDS,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list dashboards in project limit=1, patern=name', async () => {
    await new Promise(r => setTimeout(r, 2000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.DASHBOARDS,
        company_id,

        project_id: newProject,
        stage_id: newStage,
        pattern: `Dashboard`,
        limit: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list.length).toBe(1);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: dashboard_id })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: dashboard_id_2 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list dashboards in project limit=1, patern=id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.DASHBOARDS,
        company_id,

        project_id: newProject,
        stage_short_name: 'production',
        pattern: `${dashboard_id_2}`,
        limit: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list.length).toBe(1);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: dashboard_id_2 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list dashboards in project patern=name without limit', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.DASHBOARDS,
        company_id,

        project_short_name: project_short_name,
        stage_id: newStage,
        pattern: `Dashboard`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list.length).toBe(2);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: dashboard_id })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: dashboard_id_2 })]),
    );
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list dashboards in project limit=1 without pattern', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.DASHBOARDS,
        company_id,

        project_short_name: project_short_name,
        stage_id: newStage,
        limit: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list.length).toBe(1);
    SchemaValidator.validate(listSchema, response.body);
  });

  afterAll(async () => {
    await new Promise(r => setTimeout(r, 2000));
    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(responseProject.status).toBe(200);
  });
});
