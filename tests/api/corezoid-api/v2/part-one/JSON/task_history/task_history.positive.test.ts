import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import listTaskHostorySchema from '../../../../schemas/v2/tasks/listTaskHistorySchema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj, requestListConv } from '../../../../../../../application/api/ApiObj';

describe('Task_history (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: number;
  let newSD: number;
  let taskObjId1: string | number;
  let taskObjId2: string | number;
  let taskObjIdSD: string | number;
  let nodeStart: string;
  let companyId: any;
  let process_node_ID: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    companyId = apikey.companies[0].id;

    const responseConv = await requestCreateObj(api, OBJ_TYPE.CONV, null, `Conv`);
    newConv = responseConv.body.ops[0].obj_id;

    const responseListConv = await requestListConv(api, newConv, null);
    expect(responseListConv.status).toBe(200);
    process_node_ID = (responseListConv.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;

    const responseSD = await requestCreateObj(api, OBJ_TYPE.CONV, companyId, `SD`, 0, 'state');
    newSD = responseSD.body.ops[0].obj_id;

    const responseListSD = await requestListConv(api, newSD, companyId);
    expect(responseListSD.status).toBe(200);
    nodeStart = (responseListSD.body.ops[0].list as Array<any>).find(item => item.title === 'start').obj_id;
  });

  test('should list task_history by obj_id Conv (my corezoid)', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: newConv,
        action: 'user',
        data: { a: '1' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    taskObjId1 = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.TASK_HISTORY,
        obj_id: taskObjId1,
        conv_id: newConv,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(listTaskHostorySchema, response.body);
    expect(response.body.ops[0].list).toBeArrayOfSize(3);
    expect(response.body.ops[0].obj_id).toEqual(taskObjId1);
    expect(response.body.ops[0].list[0].conv_id).toEqual(newConv);
  });

  test('should list task_history by obj_id Conv (my corezoid) limit+offset', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.TASK_HISTORY,
        obj_id: taskObjId1,
        conv_id: newConv,
        limit: 10,
        offset: 2,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(listTaskHostorySchema, response.body);
    expect(response.body.ops[0].list).toBeArrayOfSize(1);
    expect(response.body.ops[0].obj_id).toEqual(taskObjId1);
    expect(response.body.ops[0].list[0].conv_id).toEqual(newConv);
    expect(response.body.ops[0].list[0].node_prev_id).toEqual(process_node_ID);
  });

  test('should list task_history SD (company)', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: newSD,
        action: 'user',
        data: { a: '1' },
        ref: `ref_123`,
      }),
    );
    expect(responseTask.status).toBe(200);
    taskObjId2 = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.TASK_HISTORY,
        obj_id: taskObjId2,
        company_id: companyId,
        conv_id: newSD,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(listTaskHostorySchema, response.body);
    expect(response.body.ops[0].list).toBeArrayOfSize(2);
    expect(response.body.ops[0].obj_id).toEqual(taskObjId2);
    expect(response.body.ops[0].list[0].conv_id).toEqual(newSD);
  });

  test('should list task_history SD (company) limit', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: newSD,
        action: 'user',
        data: { a: '1' },
        ref: `ref_123_1`,
      }),
    );
    expect(responseTask.status).toBe(200);
    taskObjIdSD = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.TASK_HISTORY,
        obj_id: taskObjIdSD,
        company_id: companyId,
        conv_id: newSD,
        limit: 1,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(listTaskHostorySchema, response.body);
    expect(response.body.ops[0].list).toBeArrayOfSize(1);
    expect(response.body.ops[0].obj_id).toEqual(taskObjIdSD);
    expect(response.body.ops[0].list[0].conv_id).toEqual(newSD);
    expect(response.body.ops[0].list[0].node_prev_id).toEqual(null);
  });

  test('should list task_history SD (company) limit 2 + offset 0', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.TASK_HISTORY,
        obj_id: taskObjIdSD,
        company_id: companyId,
        conv_id: newSD,
        limit: 2,
        offset: 0,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(listTaskHostorySchema, response.body);
    expect(response.body.ops[0].list).toBeArrayOfSize(2);
    expect(response.body.ops[0].obj_id).toEqual(taskObjIdSD);
  });

  test('should list task_history SD (company) limit 1 + offset 1', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.TASK_HISTORY,
        obj_id: taskObjIdSD,
        company_id: companyId,
        conv_id: newSD,
        limit: 1,
        offset: 1,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(listTaskHostorySchema, response.body);
    expect(response.body.ops[0].list).toBeArrayOfSize(1);
    expect(response.body.ops[0].obj_id).toEqual(taskObjIdSD);
    expect(response.body.ops[0].list[0].conv_id).toEqual(newSD);
    expect(response.body.ops[0].list[0].node_prev_id).toEqual(nodeStart);
  });

  test('should list task_history SD (company) limit 100', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.TASK_HISTORY,
        obj_id: taskObjIdSD,
        company_id: companyId,
        conv_id: newSD,
        limit: 100,
        offset: 1,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(listTaskHostorySchema, response.body);
    expect(response.body.ops[0].list).toBeArrayOfSize(1);
    expect(response.body.ops[0].obj_id).toEqual(taskObjIdSD);
    expect(response.body.ops[0].list[0].conv_id).toEqual(newSD);
    expect(response.body.ops[0].list[0].status).toEqual('created');
    expect(response.body.ops[0].list[0].node_prev_id).toEqual(nodeStart);
  });

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);

    const responseConv = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, null);
    expect(responseConv.status).toBe(200);

    const responseSD = await requestDeleteObj(api, OBJ_TYPE.CONV, newSD, companyId);
    expect(responseSD.status).toBe(200);
  });
});
