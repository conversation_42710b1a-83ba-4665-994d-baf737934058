import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';
import { integerTestCases, undefinedTestCase } from '../../../../../negativeCases';

describe('Task_history (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: number;
  let newSD: number;
  let companyId: any;
  const valuesToSkip: any = [0];
  let taskObjIdSD: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    companyId = apikey.companies[0].id;

    const responseConv = await requestCreateObj(api, OBJ_TYPE.CONV, null, `Conv`);
    newConv = responseConv.body.ops[0].obj_id;

    const responseSD = await requestCreateObj(api, OBJ_TYPE.CONV, companyId, `SD`, 0, 'state');
    newSD = responseSD.body.ops[0].obj_id;

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: newSD,
        action: 'user',
        data: { a: '1' },
        ref: `ref_123_1`,
      }),
    );
    expect(responseTask.status).toBe(200);
    taskObjIdSD = responseTask.body.ops[0].obj_id;
  });

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't list task_history in conv with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.TASK_HISTORY,
          obj_id: input,
          company_id: companyId,
          conv_id: newSD,
          limit: 1,
          offset: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't list task_history in conv with invalid conv_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.TASK_HISTORY,
          obj_id: taskObjIdSD,
          company_id: companyId,
          conv_id: input,
          limit: 1,
          offset: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't shouldn't list task_history in conv with invalid limit '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.TASK_HISTORY,
          obj_id: taskObjIdSD,
          company_id: companyId,
          conv_id: newSD,
          limit: input,
          offset: 0,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([101])(`shouldn't list task_history with invalid limit 101`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.TASK_HISTORY,
        obj_id: taskObjIdSD,
        company_id: companyId,
        conv_id: newSD,
        limit: param,
        offset: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(
      `Value is not valid. Value's limit is more than maximum allowed: 100`,
    );
  });

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't shouldn't list task_history in conv with invalid offset '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.TASK_HISTORY,
        obj_id: taskObjIdSD,
        company_id: companyId,
        conv_id: newSD,
        limit: 1,
        offset: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([101])(`shouldn't list task_history with invalid offset 101`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.TASK_HISTORY,
        obj_id: taskObjIdSD,
        company_id: companyId,
        conv_id: newSD,
        limit: 99,
        offset: param,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(
      `Value is not valid. Value's limit is more than maximum allowed: 100`,
    );
  });

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);

    const responseConv = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, null);
    expect(responseConv.status).toBe(200);

    const responseSD = await requestDeleteObj(api, OBJ_TYPE.CONV, newSD, companyId);
    expect(responseSD.status).toBe(200);
  });
});
