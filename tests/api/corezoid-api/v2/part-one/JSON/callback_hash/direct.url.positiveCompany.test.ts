import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createHash from '../../../../schemas/v2/actions-objects/direct-url/createCallback_hash.schema.json';
import getHash from '../../../../schemas/v2/actions-objects/direct-url/getCallback_hash.schema.json';
import deleteHash from '../../../../schemas/v2/actions-objects/direct-url/deleteCallback_hash.schema.json';
import deleteHash<PERSON>lias from '../../../../schemas/v2/actions-objects/direct-url/deleteCallback_hashAlias.schema.json';
import modifyHash from '../../../../schemas/v2/actions-objects/direct-url/modifyCallback_hash.schema.json';
import modifyHashAlias from '../../../../schemas/v2/actions-objects/direct-url/modifyCallback_hashAlias.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';

describe('Direct URL company (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let conv_id: string | number;
  let alias_id: number;
  let hash: string;
  let hashAlias: string;
  let hashstr: string | number;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseConv = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Conv_${Date.now()}`, 0);
    conv_id = responseConv.body.ops[0].obj_id;

    const createAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: `short${Date.now()}`,
        title: `Alias_${Date.now()}`,
      }),
    );
    expect(createAlias.status).toBe(200);
    alias_id = createAlias.body.ops[0].obj_id;
  });

  test(`should create callback_hash`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        alias_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].callback_hash).not.toBe('');
    hashAlias = response.body.ops[0].callback_hash;
    hashstr = hashAlias.length;
    expect(hashstr).toBe(40);
    SchemaValidator.validate(createHash, response.body);
  });

  test(`should get callback_hash conv`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        conv_id: conv_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].callback_hash).not.toBe('');
    hash = response.body.ops[0].callback_hash;
    hashstr = hash.length;
    expect(hashstr).toBe(40);
    SchemaValidator.validate(getHash, response.body);
  });

  test(`should get callback_hash alias`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        alias_id,
        obj_type: 'alias',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].callback_hash).toBe(hashAlias);
    SchemaValidator.validate(getHash, response.body);
  });

  test(`should delete callback_hash conv`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        conv_id: conv_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    SchemaValidator.validate(deleteHash, response.body);
  });

  test(`should delete callback_hash alias`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        alias_id,
        obj_type: 'alias',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    SchemaValidator.validate(deleteHashAlias, response.body);
  });

  test(`should get callback_hash after delete`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        conv_id: conv_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].callback_hash).toBe('');
    SchemaValidator.validate(getHash, response.body);
  });

  test(`should get callback_hash alias after delete `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        alias_id,
        obj_type: 'alias',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].callback_hash).toBe('');
    SchemaValidator.validate(getHash, response.body);
  });

  test(`should modify callback_hash conv`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        conv_id: conv_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].callback_hash).not.toBe('');
    expect(response.body.ops[0].callback_hash).not.toBe(hash);
    expect(response.body.ops[0].conv_id).toBe(conv_id);
    SchemaValidator.validate(modifyHash, response.body);
  });

  test(`should modify callback_hash alias`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        alias_id,
        obj_type: 'alias',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].callback_hash).not.toBe('');
    expect(response.body.ops[0].callback_hash).not.toBe(hashAlias);
    SchemaValidator.validate(modifyHashAlias, response.body);
  });

  afterAll(async () => {
    const responseConv = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(responseConv.status).toBe(200);

    const response = await requestDeleteObj(api, OBJ_TYPE.ALIAS, alias_id, company_id);
    expect(response.status).toBe(200);
  });
});
