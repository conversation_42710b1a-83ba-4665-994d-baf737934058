import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import getHash from '../../../../schemas/v2/actions-objects/direct-url/getCallback_hash.schema.json';
import deleteHash from '../../../../schemas/v2/actions-objects/direct-url/deleteCallback_hash.schema.json';
import modifyHash from '../../../../schemas/v2/actions-objects/direct-url/modifyCallback_hash.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';

describe('Direct URL my corezoid (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let conv_id: string | number;
  let hash: string;
  let hashstr: string | number;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);

    const responseConv = await requestCreateObj(api, OBJ_TYPE.CONV, null, `Conv_${Date.now()}`, 0);
    conv_id = responseConv.body.ops[0].obj_id;
  });

  test(`should get callback_hash`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        conv_id: conv_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].callback_hash).not.toBe('');
    hash = response.body.ops[0].callback_hash;
    hashstr = hash.length;
    expect(hashstr).toBe(40);
    SchemaValidator.validate(getHash, response.body);
  });

  test(`should delete callback_hash`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        conv_id: conv_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    SchemaValidator.validate(deleteHash, response.body);
  });

  test(`should get callback_hash after delete`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        conv_id: conv_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].callback_hash).toBe('');
    SchemaValidator.validate(getHash, response.body);
  });

  test(`should modify callback_hash`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        conv_id: conv_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].callback_hash).not.toBe('');
    expect(response.body.ops[0].callback_hash).not.toBe(hash);
    expect(response.body.ops[0].conv_id).toBe(conv_id);
    SchemaValidator.validate(modifyHash, response.body);
  });

  afterAll(async () => {
    const responseConv = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(responseConv.status).toBe(200);
  });
});
