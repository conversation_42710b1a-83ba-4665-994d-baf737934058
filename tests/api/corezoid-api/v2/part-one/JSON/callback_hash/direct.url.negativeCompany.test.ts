import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';
import faker from 'faker';

describe('Direct URL company (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let conv_id: string | number;
  let company_id: any;
  let alias_id: number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseConv = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Conv_${Date.now()}`, 0);
    conv_id = responseConv.body.ops[0].obj_id;

    const createAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: `short${Date.now()}`,
        title: `Alias_${Date.now()}`,
      }),
    );
    expect(createAlias.status).toBe(200);
    alias_id = createAlias.body.ops[0].obj_id;
  });

  test.each([
    ['te', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'company_id' is required`],
    [faker.random.alphaNumeric(256), `Value is not valid. Value's byte_size is more than maximum allowed: 36`],
  ])(`shouldn't create callback_hash with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        alias_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', 'Value is not valid'],
    [true, `Value is not valid`],
    [null, `Value is not valid`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
    [0, `Alias not found`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [undefined, `Key 'alias_id' is required`],
  ])(`shouldn't create callback_hash with invalid alias_id '%s'`, async (alias_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        alias_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['te', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'company_id' is required`],
    [faker.random.alphaNumeric(256), `Value is not valid. Value's byte_size is more than maximum allowed: 36`],
  ])(`shouldn't get callback_hash with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        alias_id,
        obj_type: 'alias',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', 'Value is not valid'],
    [true, 'Value is not valid'],
    [null, 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [0, `Object conv with id 0 does not exist`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [1, `Access denied`],
    [undefined, `Key 'conv_id' is required`],
  ])(`shouldn't get callback_hash with invalid conv_id '%s'`, async (conv_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        conv_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', 'Value is not valid'],
    [true, 'Value is not valid'],
    [null, 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [0, `Alias not found`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [1, `Alias not found`],
    [undefined, `Key 'alias_id' is required`],
  ])(`shouldn't get callback_hash with invalid alias_id '%s'`, async (alias_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        alias_id,
        obj_type: 'alias',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each(['test', true, null, [], {}, 0, -1, 1, undefined])(
    `shouldn't get callback_hash with invalid obj_type '%s'`,
    async obj_type => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.GET,
          obj: OBJ_TYPE.CALLBACK_HASH,
          company_id,
          alias_id,
          obj_type,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(`Key 'conv_id' is required`);
    },
  );

  test.each([
    ['te', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'company_id' is required`],
    [faker.random.alphaNumeric(256), `Value is not valid. Value's byte_size is more than maximum allowed: 36`],
  ])(`shouldn't delete callback_hash with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        alias_id,
        obj_type: 'alias',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', 'Value is not valid'],
    [true, 'Value is not valid'],
    [null, 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [0, `Object conv with id 0 does not exist`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [1, `Access denied`],
    [undefined, `Key 'conv_id' is required`],
  ])(`shouldn't delete callback_hash with invalid conv_id '%s'`, async (conv_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CALLBACK_HASH,
        conv_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', 'Value is not valid'],
    [true, 'Value is not valid'],
    [null, 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [0, `Alias not found`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [1, `Alias not found`],
    [undefined, `Key 'alias_id' is required`],
  ])(`shouldn't delete callback_hash with invalid alias_id '%s'`, async (alias_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        alias_id,
        obj_type: 'alias',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each(['test', true, null, [], {}, 0, -1, 1, undefined])(
    `shouldn't delete callback_hash with invalid obj_type '%s'`,
    async obj_type => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.CALLBACK_HASH,
          company_id,
          alias_id,
          obj_type,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(`Key 'conv_id' is required`);
    },
  );

  test.each([
    ['te', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'company_id' is required`],
    [faker.random.alphaNumeric(256), `Value is not valid. Value's byte_size is more than maximum allowed: 36`],
  ])(`shouldn't modify callback_hash with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        alias_id,
        obj_type: 'alias',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', 'Value is not valid'],
    [true, 'Value is not valid'],
    [null, 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [0, `Object conv with id 0 does not exist`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [1, `Access denied`],
    [undefined, `Key 'conv_id' is required`],
  ])(`shouldn't modify callback_hash with invalid conv_id '%s'`, async (conv_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CALLBACK_HASH,
        conv_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', 'Value is not valid'],
    [true, 'Value is not valid'],
    [null, 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [0, `Alias not found`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [1, `Alias not found`],
    [undefined, `Key 'alias_id' is required`],
  ])(`shouldn't modify callback_hash with invalid alias_id '%s'`, async (alias_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        alias_id,
        obj_type: 'alias',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each(['test', true, null, [], {}, 0, -1, 1, undefined])(
    `shouldn't modify callback_hash with invalid obj_type '%s'`,
    async obj_type => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.CALLBACK_HASH,
          company_id,
          alias_id,
          obj_type,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(`Key 'conv_id' is required`);
    },
  );

  afterAll(async () => {
    const responseConv = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(responseConv.status).toBe(200);

    const response = await requestDeleteObj(api, OBJ_TYPE.ALIAS, alias_id, company_id);
    expect(response.status).toBe(200);
  });
});
