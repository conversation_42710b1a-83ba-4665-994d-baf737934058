import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import listApiKey from '../../../../schemas/v2/company_users/listCompanyUsersApikey.schema.json';
import listGroup from '../../../../schemas/v2/company_users/listCompanyUsersGroup.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj } from '../../../../../../../application/api/ApiObj';
import listUser from '../../../../schemas/v2/company_users/listCompanyUsersUser.schema.json';

describe('Company users (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newKey: ApiKeyClient;
  let company_id: any;
  let groupObjId: number;
  let groupObjId2: number;
  let keyObjId: number;
  let keyObjId2: number;
  let date: number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = null;
    date = Date.now();

    const responseGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_type: 'admins',
        title: `${date}_Group`,
      }),
    );
    expect(responseGroup.status).toBe(200);
    groupObjId = responseGroup.body.ops[0].obj_id;

    const responseGroup2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_type: 'admins',
        title: `${date}_Group2`,
      }),
    );
    expect(responseGroup2.status).toBe(200);
    groupObjId2 = responseGroup2.body.ops[0].obj_id;

    const responseUser = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: `ApiKey`,
        logins: [{ type: 'api' }],
      }),
    );
    expect(responseUser.status).toBe(200);
    keyObjId = responseUser.body.ops[0].users[0].obj_id;
    const user_api = responseUser.body;
    const newApiKey = {
      key: `${user_api.ops[0].users[0].logins[0].obj_id}`,
      secret: user_api.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user_api.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newKey = application.getApiKeyClient(newApiKey);
    keyObjId = +newApiKey.id;

    const responseUser2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: `ApiKey2`,
        logins: [{ type: 'api' }],
      }),
    );
    expect(responseUser2.status).toBe(200);
    keyObjId2 = responseUser2.body.ops[0].users[0].obj_id;

    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: keyObjId,
        company_id,
        group_id: groupObjId,
        level: 1,
      }),
    );
    expect(responseLink.status).toBe(200);
  });

  test(`should company users`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list).toBeArray();
  });

  test(`should company user`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        sort: 'title',
        name: 'start',
        filter: 'user',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list).toBeArray();
    SchemaValidator.validate(listUser, response.body);
  });

  test(`should company users api_key`, async () => {
    await new Promise(r => setTimeout(r, 2000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        sort: 'title',
        name: 'ApiKey',
        filter: 'api_key',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list[0].obj_id).toBe(keyObjId);
    expect(response.body.ops[0].list[1].obj_id).toBe(keyObjId2);
    SchemaValidator.validate(listApiKey, response.body);
  });

  test(`should company users api_key order:desc`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        sort: 'title',
        name: 'ApiKey',
        filter: 'api_key',
        order: 'desc',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list[0].obj_id).toBe(keyObjId2);
    expect(response.body.ops[0].list[1].obj_id).toBe(keyObjId);
    SchemaValidator.validate(listApiKey, response.body);
  });

  test(`should company users group`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        sort: 'title',
        name: `${date}`,
        order: 'asc',
        filter: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list[0].obj_id).toBe(groupObjId);
    expect(response.body.ops[0].list[1].obj_id).toBe(groupObjId2);
    SchemaValidator.validate(listGroup, response.body);
  });

  test(`should company users group without name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        sort: 'title',
        order: 'asc',
        filter: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: groupObjId })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: groupObjId2 })]),
    );
    SchemaValidator.validate(listGroup, response.body);
  });

  test(`should company users shared`, async () => {
    const response = await newKey.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        sort: 'title',
        order: 'asc',
        filter: 'shared',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list[0].obj_id).toBe(groupObjId);
    SchemaValidator.validate(listGroup, response.body);
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.GROUP, groupObjId, company_id);
    expect(response.status).toBe(200);

    const response2 = await requestDeleteObj(api, OBJ_TYPE.GROUP, groupObjId2, company_id);
    expect(response2.status).toBe(200);

    const response3 = await requestDeleteObj(api, OBJ_TYPE.USER, keyObjId, company_id);
    expect(response3.status).toBe(200);

    const response4 = await requestDeleteObj(api, OBJ_TYPE.USER, keyObjId2, company_id);
    expect(response4.status).toBe(200);
  });
});
