import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import {
  stringTestCases,
  companyTestCases,
  securityTestCases,
  maxLength,
  minLength,
  stringNotValidTestCases,
} from '../../../../../negativeCases';

describe('Company users (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
  });

  test.each([...companyTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't show company users with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          company_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...minLength, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't show company users with invalid name '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          company_id,
          sort: 'title',
          name: input,
          filter: 'api_key',
          order: 'desc',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...securityTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't show company users with invalid sort '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        sort: input,
        name: 'test',
        filter: 'api_key',
        order: 'desc',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...securityTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't show company users with invalid filter '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        sort: 'title',
        name: 'test',
        filter: input,
        order: 'desc',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...securityTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't show company users with invalid order '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        sort: 'title',
        name: 'test',
        filter: 'api_key',
        order: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });
});
