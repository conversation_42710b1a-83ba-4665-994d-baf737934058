import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import listConvsSchema from '../../../../schemas/v2/actions-objects/listConvs.schema.json';
import { makeRequestWithRetries, MyApiResponse } from '../../../../../../../utils/requestRetries';

describe('Convs (positive)', () => {
  let api: ApiKeyClient;
  let apiS: ApiKeyClient;
  let apikey: ApiKey;
  let apikeyS: ApiKey;
  let newConv: string | number;
  let newConv2: string | number;
  let newConv3: string | number;
  let newConv4: string | number;
  let newConv5: string | number;
  let newConv6: string | number;
  let newConv7: string | number;
  let newConv8: string | number;
  let newConv9: string | number;
  let company_id: any;
  let title: string;
  let titleSearchProc: string;
  let titleSearchConv9: string;
  let short_name_project: string;
  let process_node_ID: string | number;
  let api_call_node: any;
  let final_node_ID: string | number;
  let conv_id_sd: string | number;
  let owner: string | number;
  let project_id: string | number;
  let project_id_2: string | number;
  let stage_id: string | number;
  let stage_id_2: string | number;
  let stage_id_3: string | number;
  let err_node_ID: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    owner = +apikey.id;
    company_id = apikey.companies[0].id;

    apikeyS = await application.getApiKey();
    apiS = application.getApiKeyClient(apikeyS);

    title = `Conv_${Date.now()}`;
    titleSearchProc = `ConvSearch_${Date.now()}`;
    titleSearchConv9 = `Conv9Search`;

    const createConvResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: 0,
        title,
        description: 'search',
        status: 'actived',
      }),
    );
    expect(createConvResponse.status).toBe(200);
    newConv = createConvResponse.body.ops[0].obj_id;

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
      }),
    );
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    //
    const responseCreateNodeError = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        title: 'Errorfinal',
        conv_id: newConv,
        obj_type: 2,
        version: 22,
      }),
    );
    expect(responseCreateNodeError.status).toBe(200);
    err_node_ID = responseCreateNodeError.body.ops[0].obj_id;
    //

    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id: newConv,
        title: 'LogicCallNew',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'POST',
            url: 'https://test.com',
            extra: { ukrainian: 'true', cityId: '1', keyWords: 'director' },
            extra_type: { ukrainian: 'boolean', cityId: 'string', keyWords: 'string' },
            max_threads: 5,
            err_node_id: err_node_ID,
            extra_headers: { 'x-api-key': '' },
            send_sys: false,
            cert_pem: '',
            content_type: 'application/json',
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);

    //
    await new Promise(r => setTimeout(r, 1000));
    //

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        conv_id: newConv,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);

    //
    const responseListObjs = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
      }),
    );
    expect(responseListObjs.status).toBe(200);
    expect(responseListObjs.body.ops[0].proc).toBe('ok');
    api_call_node = (responseListObjs.body.ops[0].list as Array<any>).find(item => item.title === 'LogicCallNew');
    expect(api_call_node.logics[0].url).toBe('https://test.com');
    //

    const createSDRespons = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: titleSearchProc,
        status: 'actived',
        obj_type: 0,
        conv_type: 'state',
      }),
    );
    conv_id_sd = createSDRespons.body.ops[0].obj_id;

    const createConv2Response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id: null,
        folder_id: 0,
        title,
      }),
    );
    expect(createConv2Response.status).toBe(200);
    newConv2 = createConv2Response.body.ops[0].obj_id;

    const createConv = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: 0,
        title: titleSearchConv9,
      }),
    );
    expect(createConv.status).toBe(200);
    newConv9 = createConv.body.ops[0].obj_id;

    const createConv3Response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: 0,
        title,
      }),
    );
    expect(createConv3Response.status).toBe(200);
    newConv3 = createConv3Response.body.ops[0].obj_id;

    const createConv4Response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: 0,
        title: titleSearchProc,
      }),
    );
    expect(createConv4Response.status).toBe(200);
    newConv4 = createConv4Response.body.ops[0].obj_id;

    short_name_project = `project-${Date.now()}`;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: short_name_project,
        description: 'test',
        stages: [
          { title: 'develop', immutable: false },
          { title: 'develop2', immutable: false },
        ],
        status: 'active',
      }),
    );
    project_id = response.body.ops[0].obj_id;
    stage_id = response.body.ops[0].stages[0];
    stage_id_2 = response.body.ops[0].stages[1];

    const createConv5Response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        project_id,
        title,
        stage_id,
      }),
    );
    expect(createConv5Response.status).toBe(200);
    newConv5 = createConv5Response.body.ops[0].obj_id;

    const createConv6Response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        project_id,
        title: 'search',
        stage_id,
      }),
    );
    expect(createConv6Response.status).toBe(200);
    newConv6 = createConv6Response.body.ops[0].obj_id;

    const createConv7Response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        project_id,
        title: 'search',
        stage_id: stage_id_2,
      }),
    );
    expect(createConv7Response.status).toBe(200);
    newConv7 = createConv7Response.body.ops[0].obj_id;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: `project2${Date.now()}`,
        description: 'test',
        stages: [{ title: 'develop', immutable: false }],
        status: 'active',
      }),
    );
    project_id_2 = responseProject.body.ops[0].obj_id;
    stage_id_3 = responseProject.body.ops[0].stages[0];

    const createConv8Response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        project_id: project_id_2,
        title: 'search',
        stage_id: stage_id_3,
      }),
    );
    expect(createConv8Response.status).toBe(200);
    newConv8 = createConv8Response.body.ops[0].obj_id;
  });

  test(`should list convs required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]));
    SchemaValidator.validate(listConvsSchema, response.body);
  });

  test(`should list convs my corezoid`, async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id: null,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv2 })]));
    SchemaValidator.validate(listConvsSchema, response.body);
  });

  test(`should list convs pattern title`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        pattern: title,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]));
    SchemaValidator.validate(listConvsSchema, response.body);
  });

  test.skip(`should list convs pattern description`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        pattern: 'search',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]));
    SchemaValidator.validate(listConvsSchema, response.body);
  });

  test(`should list convs pattern id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        pattern: `${newConv}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]));
    SchemaValidator.validate(listConvsSchema, response.body);
  });

  test(`should list convs pattern URL`, async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return response.status === 200 && response.body.ops[0].list.some((item: any) => item.obj_id === newConv);
    }
    const response = await makeRequestWithRetries(
      async () => {
        return await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.CONVS,
            company_id,
            pattern: `https://test.com`,
          }),
        );
      },
      checkConditions,
      30,
    );

    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    SchemaValidator.validate(listConvsSchema, response.body);
  });

  test.skip(`should list convs pattern title conv_type=process`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        pattern: titleSearchProc,
        obj_type: 'process',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list[0].obj_id).toBe(newConv);
    expect(response.body.ops[0].list[0].conv_type).toBe('process');
    SchemaValidator.validate(listConvsSchema, response.body);
  });

  test.skip(`should list convs pattern title conv_type=state`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        pattern: title,
        obj_type: 'state',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list[0].obj_id).toBe(conv_id_sd);
    expect(response.body.ops[0].list[0].conv_type).toBe('state');
    SchemaValidator.validate(listConvsSchema, response.body);
  });

  test(`should list convs limit=1`, async () => {
    const responseAll = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        pattern: titleSearchProc,
      }),
    );
    expect(responseAll.status).toBe(200);
    expect(responseAll.body.request_proc).toBe('ok');
    expect(responseAll.body.ops[0].proc).toBe('ok');

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        limit: 1,
        pattern: titleSearchConv9,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv9 })]));
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: conv_id_sd })]),
    );
    SchemaValidator.validate(listConvsSchema, response.body);
  });

  test(`should list convs limit=2`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        limit: 2,
        pattern: titleSearchProc,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv4 })]));
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: conv_id_sd })]),
    );
    SchemaValidator.validate(listConvsSchema, response.body);
  });

  test(`should list convs with owner_id`, async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        owner_id: owner,
        pattern: title,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv3 })]));
    SchemaValidator.validate(listConvsSchema, response.body);
  });

  test(`should list convs with project_short_name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        project_short_name: short_name_project,
        stage_short_name: 'develop',
        pattern: title,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv5 })]));
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv6 })]),
    );
    SchemaValidator.validate(listConvsSchema, response.body);

    const response2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        project_short_name: short_name_project,
        stage_id,
        pattern: title,
      }),
    );
    expect(response2.status).toBe(200);
    expect(response2.body.request_proc).toBe('ok');
    expect(response2.body.ops[0].proc).toBe('ok');
    expect(response2.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv5 })]));
    expect(response2.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv6 })]),
    );
    SchemaValidator.validate(listConvsSchema, response.body);
  });

  test(`should list convs with project_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        project_id,
        stage_short_name: 'develop',
        pattern: title,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv5 })]));
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv6 })]),
    );
    SchemaValidator.validate(listConvsSchema, response.body);

    const response2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        project_id,
        stage_id,
        pattern: title,
      }),
    );
    expect(response2.status).toBe(200);
    expect(response2.body.request_proc).toBe('ok');
    expect(response2.body.ops[0].proc).toBe('ok');
    expect(response2.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv5 })]));
    expect(response2.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv6 })]),
    );
    SchemaValidator.validate(listConvsSchema, response.body);
  });

  test(`should list convs location folder`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        location: 'folder',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv3 })]));
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv5 })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv6 })]),
    );
    SchemaValidator.validate(listConvsSchema, response.body);
  });

  test(`should list convs location stage `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        project_id,
        stage_id,
        location: 'stage',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv5 })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv6 })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv7 })]));
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv3 })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv8 })]),
    );
    SchemaValidator.validate(listConvsSchema, response.body);
  });

  test(`should list convs location project `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONVS,
        company_id,
        location: 'project',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv5 })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv6 })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv7 })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newConv8 })]));
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConv3 })]),
    );
    SchemaValidator.validate(listConvsSchema, response.body);
  });

  afterAll(async () => {
    const ResponseDeleteApiConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
    expect(ResponseDeleteApiConv.status).toBe(200);
    const ResponseDeleteApiConv2 = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv2,
        company_id: null,
      }),
    );
    expect(ResponseDeleteApiConv2.status).toBe(200);
    const ResponseDeleteApiConv3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv3,
        company_id,
      }),
    );
    expect(ResponseDeleteApiConv3.status).toBe(200);
    const ResponseDeleteApiConv4 = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv4,
        company_id,
      }),
    );
    expect(ResponseDeleteApiConv4.status).toBe(200);
    const ResponseDeleteSD = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id_sd,
        company_id,
      }),
    );
    expect(ResponseDeleteSD.status).toBe(200);
    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: project_id,
        company_id,
      }),
    );
    expect(responseProject.status).toBe(200);
  });
});
