import { error } from '../../../../../../../support/utils/logger';
import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import listSchema from '../../../../schemas/v2/actions-objects/listNodes.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestCreateObj } from '../../../../../../../application/api/ApiObj';
import { makeRequestWithRetries, MyApiResponse } from '../../../../../../../utils/requestRetries';

describe('Nodes (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: number;
  let newNode: string;
  let newNode1: string;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Process_${Date.now()}`);
    newConv = response.body.ops[0].obj_id;

    const responseN = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_type: 0,
        description: 'normal',
        conv_id: newConv,
        title: `normal`,
        version: 1,
      }),
    );
    expect(responseN.status).toBe(200);
    newNode = responseN.body.ops[0].obj_id;

    const responseE = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_type: 3,
        description: 'escalation',
        conv_id: newConv,
        title: `escalation`,
        version: 1,
      }),
    );
    expect(responseE.status).toBe(200);
    newNode1 = responseE.body.ops[0].obj_id;

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        obj_id: newConv,
        obj_type: 'conv',
        version: 1,
      }),
    );
    expect(responseCommit.status).toBe(200);

    await new Promise(r => setTimeout(r, 6000));
  });

  test('should list nodes obj_type 0 by title', async () => {
    function checkConditions(response: MyApiResponse): boolean {
      try {
        SchemaValidator.validate(listSchema, response.body);
        return (
          response.status === 200 &&
          response.body.ops[0].list[0].type === 0 &&
          response.body.ops[0].list[0].status === 1
        );
      } catch (err) {
        error('Schema validation failed:', err);
        return false;
      }
    }
    const response = await makeRequestWithRetries(
      async () => {
        return await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.NODES,
            conv_id: newConv,
            pattern: 'normal',
          }),
        );
      },
      checkConditions,
      {
        maxRetries: 40,
      },
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test('should list nodes obj_type 0 by id', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODES,
        conv_id: newConv,
        pattern: newNode,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list[0].type).toBe(0);
    expect(response.body.ops[0].list[0].status).toBe(1);
    expect(response.body.ops[0].list[0].title).toBe('normal');
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list nodes by title (only requaried parameter)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODES,
        pattern: 'normal',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list[0].type).toBe(0);
    expect(response.body.ops[0].list[0].status).toBe(1);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list nodes obj_type 1', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODES,
        conv_id: newConv,
        pattern: 'Start',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list[0].type).toBe(1);
    expect(response.body.ops[0].list[0].status).toBe(1);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list nodes obj_type 2', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODES,
        conv_id: newConv,
        pattern: 'Final',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list[0].type).toBe(2);
    expect(response.body.ops[0].list[0].status).toBe(1);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list nodes obj_type 3', async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return (
        response.status === 200 && response.body.ops[0].list[0].type === 3 && response.body.ops[0].list[0].status === 1
      );
    }
    const response = await makeRequestWithRetries(
      async () => {
        return await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.NODES,
            conv_id: newConv,
            pattern: 'escalation',
          }),
        );
      },
      checkConditions,
      20,
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list nodes obj_type 3 after delete node', async () => {
    const responseDel = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.NODE,
        conv_id: newConv,
        obj_id: newNode1,
      }),
    );
    expect(responseDel.status).toBe(200);

    await new Promise(r => setTimeout(r, 5000));

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        obj_id: newConv,
        obj_type: 'conv',
        version: 1,
      }),
    );
    expect(responseCommit.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODES,
        conv_id: newConv,
        pattern: 'escalation',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toBeArrayOfSize(0);
    SchemaValidator.validate(listSchema, response.body);
  });

  afterAll(async () => {
    // const responseConv = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, company_id);
    // expect(responseConv.status).toBe(200);
  });
});
