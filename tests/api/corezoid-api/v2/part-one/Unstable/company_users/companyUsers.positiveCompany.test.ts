import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import listApiKey from '../../../../schemas/v2/company_users/listCompanyUsersApikey.schema.json';
import listUser from '../../../../schemas/v2/company_users/listCompanyUsersUser.schema.json';
import listGroup from '../../../../schemas/v2/company_users/listCompanyUsersGroup.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { makeRequestWithRetries, MyApiResponse } from '../../../../../../../utils/requestRetries';

describe('Company users (positive)', () => {
  let api: ApiKeyClient;
  let apiK: ApiKeyClient;
  let apikey: ApiKey;
  let newKey: ApiKeyClient;
  let company_id: any;
  let groupObjId: number;
  let groupObjId2: number;
  let keyObjId: number;
  let keyObjId2: number;
  let title: string;
  let title2: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    apiK = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    title = `abcKey`;
    title2 = `abcKey2`;

    const response = await apiK.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title,
        logins: [{ type: 'api' }],
      }),
    );
    expect(response.status).toBe(200);
    keyObjId = response.body.ops[0].users[0].obj_id;
    const user = response.body;
    const ApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    api = application.getApiKeyClient(apikey);

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        obj_id: keyObjId,
        company_id,
        obj_type: 'company',
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].title).toBe(title);

    const responseGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_type: 'admins',
        title: `Group`,
      }),
    );
    expect(responseGroup.status).toBe(200);
    groupObjId = responseGroup.body.ops[0].obj_id;

    const responseGroup2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_type: 'admins',
        title: `Group2`,
      }),
    );
    expect(responseGroup2.status).toBe(200);
    groupObjId2 = responseGroup2.body.ops[0].obj_id;

    const responseUser = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title,
        logins: [{ type: 'api' }],
      }),
    );
    expect(responseUser.status).toBe(200);
    keyObjId = responseUser.body.ops[0].users[0].obj_id;
    const user_api = responseUser.body;
    const newApiKey = {
      key: `${user_api.ops[0].users[0].logins[0].obj_id}`,
      secret: user_api.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user_api.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newKey = application.getApiKeyClient(newApiKey);
    keyObjId = +newApiKey.id;

    const responseUser2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: title2,
        logins: [{ type: 'api' }],
      }),
    );
    expect(responseUser2.status).toBe(200);
    keyObjId2 = responseUser2.body.ops[0].users[0].obj_id;

    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: keyObjId,
        company_id,
        group_id: groupObjId,
        level: 1,
      }),
    );
    expect(responseLink.status).toBe(200);
  });

  test(`show api key - for test`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        obj_id: keyObjId,
        company_id,
        obj_type: 'company',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].title).toBe(title);
  });

  test(`show api key2 - for test`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        obj_id: keyObjId2,
        company_id,
        obj_type: 'company',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].title).toBe(title2);
  });

  test(`should company users`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list).toBeArray();
  });

  test(`should company user`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        sort: 'title',
        name: 'start',
        filter: 'user',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list).toBeArray();
    SchemaValidator.validate(listUser, response.body);
  });

  test(`should company users api_key`, async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return response.status === 200 && response.body.ops[0].list.some((item: any) => item.obj_id === keyObjId);
    }
    const response = await makeRequestWithRetries(
      async () => {
        return await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.COMPANY_USERS,
            company_id,
            sort: 'title',
            name: title,
            filter: 'api_key',
          }),
        );
      },
      checkConditions,
      20,
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    SchemaValidator.validate(listApiKey, response.body);
  });

  test(`should company users api_key order:desc`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        sort: 'title',
        name: title,
        filter: 'api_key',
        order: 'desc',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list[0].obj_id).toBe(keyObjId2);
    expect(response.body.ops[0].list[2].obj_id).toBe(keyObjId);
    SchemaValidator.validate(listApiKey, response.body);
  });

  test(`should company users group`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        sort: 'title',
        name: 'group',
        order: 'asc',
        filter: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: groupObjId })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: groupObjId2 })]),
    );
    SchemaValidator.validate(listGroup, response.body);
  });

  test(`should company users group without name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        sort: 'title',
        order: 'desc',
        filter: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    const groupIds = response.body.ops[0].list.map((item: any) => item.obj_id);
    expect(groupIds).toContain(groupObjId);
    expect(groupIds).toContain(groupObjId2);

    SchemaValidator.validate(listGroup, response.body);
  });

  test(`should company users shared`, async () => {
    const response = await newKey.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        sort: 'title',
        order: 'asc',
        filter: 'shared',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list[0].obj_id).toBe(groupObjId);
    SchemaValidator.validate(listGroup, response.body);
  });

  afterAll(async () => {
    // const response = await requestDeleteObj(api, OBJ_TYPE.GROUP, groupObjId, company_id);
    // expect(response.status).toBe(200);
    // const response2 = await requestDeleteObj(api, OBJ_TYPE.GROUP, groupObjId2, company_id);
    // expect(response2.status).toBe(200);
    // const response3 = await requestDeleteObj(api, OBJ_TYPE.USER, keyObjId, company_id);
    // expect(response3.status).toBe(200);
    // const response4 = await requestDeleteObj(api, OBJ_TYPE.USER, keyObjId2, company_id);
    // expect(response4.status).toBe(200);
    // const response5 = await requestDeleteObj(apiSuper, OBJ_TYPE.USER, keyObjId0, company_id);
    // expect(response5.status).toBe(200);
  });
});
