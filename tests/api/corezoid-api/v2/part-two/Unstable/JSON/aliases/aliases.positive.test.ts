import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../../application/api/SchemaValidator';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../../utils/corezoidRequest';
import listAliasesSchema from '../../../../../schemas/v2/aliases/listAliases.Schema.json';

describe('Aliases (positive)', () => {
  let api: ApiKeyClient;
  let apiS: ApiKeyClient;
  let apikey: ApiKey;
  let apikeyS: ApiKey;
  let conv_id: string | number;
  let conv_id_2: string | number;
  let company_id: any;
  let title: string;
  let short_name_project: string;
  let short_name_alias: string;
  let alias_id: string | number;
  let alias_id_2: string | number;
  let alias_id_3: string | number;
  let alias_id_4: string | number;
  let alias_id_5: string | number;
  let alias_id_6: string | number;
  let alias_id_7: string | number;
  let alias_id_8: string | number;
  let project_id: string | number;
  let project_id_2: string | number;
  let stage_id: string | number;
  let stage_id_2: string | number;
  let stage_id_3: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    apikeyS = await application.getApiKey();
    apiS = application.getApiKeyClient(apikeyS);

    title = `Conv_${Date.now()}`;
    short_name_alias = `${Date.now()}`;
    short_name_project = `project-${Date.now()}`;

    const createConvResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: 0,
        title,
        description: 'search',
        status: 'actived',
      }),
    );
    expect(createConvResponse.status).toBe(200);
    conv_id = createConvResponse.body.ops[0].obj_id;

    const createAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        title: short_name_alias,
        short_name: `alias${short_name_alias}`,
        project_id: 0,
        stage_id: 0,
      }),
    );
    alias_id = createAlias.body.ops[0].obj_id;

    const linkAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: alias_id,
        company_id,
        obj_to_type: 'conv',
        obj_to_id: conv_id,
        link: true,
      }),
    );
    expect(linkAlias.status).toBe(200);

    const createAlias2 = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        title: `alias2${short_name_alias}`,
        short_name: `alias2${short_name_alias}`,
        project_id: 0,
        stage_id: 0,
      }),
    );
    alias_id_2 = createAlias2.body.ops[0].obj_id;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: short_name_project,
        description: 'test',
        stages: [
          { title: 'develop', immutable: false },
          { title: 'develop2', immutable: false },
        ],
        status: 'active',
      }),
    );
    project_id = responseProject.body.ops[0].obj_id;
    stage_id = responseProject.body.ops[0].stages[0];
    stage_id_2 = responseProject.body.ops[0].stages[1];

    const createAlias3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        title: `alias3${short_name_alias}`,
        short_name: `alias3${short_name_alias}`,
        project_id,
        stage_id,
      }),
    );
    alias_id_3 = createAlias3.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 1000));

    const createAlias4 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        title: `alias4${short_name_alias}`,
        short_name: `alias4${short_name_alias}`,
        project_id,
        stage_id,
      }),
    );
    alias_id_4 = createAlias4.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 1000));

    const createAlias5 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        title: `alias5${short_name_alias}`,
        short_name: `alias5${short_name_alias}`,
        project_id,
        stage_id,
      }),
    );
    alias_id_5 = createAlias5.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 1000));

    const createAlias6 = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        title: `alias6${short_name_alias}`,
        short_name: `alias6${short_name_alias}`,
        project_id,
        stage_id,
      }),
    );
    alias_id_6 = createAlias6.body.ops[0].obj_id;

    const createConv2Response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title,
        description: 'search',
        status: 'actived',
        project_id,
        stage_id,
      }),
    );
    expect(createConv2Response.status).toBe(200);
    conv_id_2 = createConv2Response.body.ops[0].obj_id;

    const link2Alias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: alias_id_6,
        company_id,
        obj_to_type: 'conv',
        obj_to_id: conv_id_2,
        link: true,
      }),
    );
    expect(link2Alias.status).toBe(200);

    const createAlias7 = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        title: `alias7${short_name_alias}`,
        short_name: `alias7${short_name_alias}`,
        project_id,
        stage_id: stage_id_2,
      }),
    );
    alias_id_7 = createAlias7.body.ops[0].obj_id;

    const responseProject2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project2_${Date.now()}`,
        short_name: `project2${Date.now()}`,
        description: 'test',
        stages: [{ title: 'develop', immutable: false }],
        status: 'active',
      }),
    );
    project_id_2 = responseProject2.body.ops[0].obj_id;
    stage_id_3 = responseProject2.body.ops[0].stages[0];

    const createAlias8 = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        title: `alias8${short_name_alias}`,
        short_name: `alias8${short_name_alias}`,
        project_id: project_id_2,
        stage_id: stage_id_3,
      }),
    );
    alias_id_8 = createAlias8.body.ops[0].obj_id;
  });

  test(`should list aliases required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: alias_id })]));
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_2 })]),
    );
    SchemaValidator.validate(listAliasesSchema, response.body);
  });

  test.skip(`should list aliases my corezoid`, async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id: null,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: alias_id })]));
    SchemaValidator.validate(listAliasesSchema, response.body);
  });

  test(`should list aliases pattern short name`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        pattern: `alias${short_name_alias}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: alias_id })]));
    SchemaValidator.validate(listAliasesSchema, response.body);
  });

  test(`should list aliases conv_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        conv_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: alias_id })]));
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_2 })]),
    );
    SchemaValidator.validate(listAliasesSchema, response.body);
  });

  test(`should list aliases project_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id,
        stage_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_3 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_4 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_5 })]),
    );
    SchemaValidator.validate(listAliasesSchema, response.body);
  });

  test(`should list aliases stage_id`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_short_name: short_name_project,
        stage_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_3 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_4 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_5 })]),
    );
    SchemaValidator.validate(listAliasesSchema, response.body);
  });

  test(`should list aliases default sort: date and order: desc`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_short_name: short_name_project,
        stage_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list[0].obj_id).toEqual(alias_id_6);
    expect(response.body.ops[0].list[1].obj_id).toEqual(alias_id_5);
    expect(response.body.ops[0].list[2].obj_id).toEqual(alias_id_4);
    expect(response.body.ops[0].list[3].obj_id).toEqual(alias_id_3);
    SchemaValidator.validate(listAliasesSchema, response.body);
  });

  test.each([['title'], ['name'], ['short_name'], ['date']])(`should list aliases sort '%s'`, async sort => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id,
        stage_id,
        sort,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list[0].obj_id).toEqual(alias_id_6);
    expect(response.body.ops[0].list[1].obj_id).toEqual(alias_id_5);
    expect(response.body.ops[0].list[2].obj_id).toEqual(alias_id_4);
    expect(response.body.ops[0].list[3].obj_id).toEqual(alias_id_3);
    SchemaValidator.validate(listAliasesSchema, response.body);
  });

  test(`should list aliases sort: owner `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id,
        stage_id,
        sort: 'owner',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_3 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_4 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_5 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_6 })]),
    );
    SchemaValidator.validate(listAliasesSchema, response.body);
  });

  test(`should list aliases sort: obj_to_id `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id,
        stage_id,
        sort: 'obj_to_id',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_3 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_5 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_4 })]),
    );
    expect(response.body.ops[0].list[3].obj_id).toEqual(alias_id_6);
    SchemaValidator.validate(listAliasesSchema, response.body);
  });

  test(`should list aliases order: asc `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id,
        stage_id,
        order: 'asc',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list[0].obj_id).toEqual(alias_id_3);
    expect(response.body.ops[0].list[1].obj_id).toEqual(alias_id_4);
    expect(response.body.ops[0].list[2].obj_id).toEqual(alias_id_5);
    expect(response.body.ops[0].list[3].obj_id).toEqual(alias_id_6);
    SchemaValidator.validate(listAliasesSchema, response.body);
  });

  test(`should list aliases order: desc `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id,
        stage_id,
        order: 'desc',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list[0].obj_id).toEqual(alias_id_6);
    expect(response.body.ops[0].list[1].obj_id).toEqual(alias_id_5);
    expect(response.body.ops[0].list[2].obj_id).toEqual(alias_id_4);
    expect(response.body.ops[0].list[3].obj_id).toEqual(alias_id_3);
    SchemaValidator.validate(listAliasesSchema, response.body);
  });

  test(`should list aliases location folder`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        location: 'folder',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: alias_id })]));
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_2 })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_3 })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_7 })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_8 })]),
    );
    SchemaValidator.validate(listAliasesSchema, response.body);
  });

  test(`should list aliases location stage `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id,
        stage_id,
        location: 'stage',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_3 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_4 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_5 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_6 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_7 })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_8 })]),
    );
    SchemaValidator.validate(listAliasesSchema, response.body);
  });

  test(`should list aliases location project `, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        location: 'project',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_3 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_4 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_5 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_6 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_7 })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id_8 })]),
    );
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: alias_id })]),
    );
    SchemaValidator.validate(listAliasesSchema, response.body);
  });

  afterAll(async () => {
    const ResponseDeleteApiConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
      }),
    );
    expect(ResponseDeleteApiConv.status).toBe(200);

    const ResponseDeleteAlias = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: alias_id,
        company_id,
      }),
    );
    expect(ResponseDeleteAlias.status).toBe(200);

    const ResponseDeleteAlias2 = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: alias_id_2,
        company_id,
      }),
    );
    expect(ResponseDeleteAlias2.status).toBe(200);

    const ResponseDeleteProject = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: project_id,
        company_id,
      }),
    );
    expect(ResponseDeleteProject.status).toBe(200);

    const ResponseDeleteProject2 = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: project_id_2,
        company_id,
      }),
    );
    expect(ResponseDeleteProject2.status).toBe(200);
  });
});
