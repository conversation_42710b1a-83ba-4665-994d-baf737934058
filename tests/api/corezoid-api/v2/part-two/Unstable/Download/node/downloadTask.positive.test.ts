import { debug } from '../../../../../../../../support/utils/logger';
import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../../application/api/SchemaValidator';
import downloadTaskSchema from '../../../../../schemas/v2/tasks/downloadTaskSchema.json';
import { ConfigurationManager } from '../../../../../../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../../utils/corezoidRequest';
import { makeRequestWithRetries, MyApiResponse } from '../../../../../../../../utils/requestRetries';
import axios, { AxiosRequestConfig, Method } from 'axios';

describe('DownloadTask (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: string | number;
  let newConvMyC: string | number;
  let nodeIdFinal: string;
  let nodeIdFinalMyC: string;
  let company_id: any;
  const endUnix = Math.round(+new Date() / 1000 + 100);
  const startUnix = endUnix - 1200;
  let baseUrl: string | number;
  let downloadUrl: string;
  let downloadUrl2: string;

  baseUrl = ConfigurationManager.getConfiguration().getUrl();

  async function makeRequest(method: Method, url: string): Promise<void> {
    const config: AxiosRequestConfig = {
      method,
      url,
    };

    const res = await axios(config);
    baseUrl = res.data.web_settings.host.site;
  }

  makeRequest('get', `${baseUrl}system/conf`);

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: 0,
        conv_type: 'process',
      }),
    );
    newConv = responseConv.body.ops[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        conv_id: newConv,
        data: {
          param: 'value',
          param2: 'val|ue2',
          param3: 'value3,;',
          param4: 12345,
          param5: true,
          param6: { key6: 'value6' },
          param7: { 'ke|y6': 'value6,;' },
          param8: [12345, true, 54321],
          param9: ['test,;', 'te|st2'],
          param10: [{ key10: 'valu|e10' }, { 'key|10_2': 'value10_2' }],
          param11: [{ key11: [12312, 3456, 4545] }, { key11_2: [{ key: 'valu|e' }, { key2: 123456 }] }],
        },
        ref: `ref_${Date.now()}`,
      }),
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: newConv,
        data: { a: '1' },
        ref: `ref_${Date.now()}`,
      }),
    );

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
        get_counter: true,
      }),
    );
    nodeIdFinal = response.body.ops[0].list[2].obj_id;
    expect(response.status).toBe(200);

    const responseConvMyCorezoid = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: 0,
        conv_type: 'process',
      }),
    );
    newConvMyC = responseConvMyCorezoid.body.ops[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,

        conv_id: newConvMyC,
        data: {
          param: 'value',
          param1: 12345,
          param2: true,
          param3: { key6: 'value6' },
          param4: [12345, true, 54321],
          param5: [{ key10: 'valu|e10' }, { 'key|10_2': 'value10_2' }],
        },
        ref: `ref_${Date.now()}`,
      }),
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: newConvMyC,
        data: { a: '1' },
        ref: `ref_${Date.now()}`,
      }),
    );

    const responseMyCorezoid = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConvMyC,
        get_counter: true,
      }),
    );
    nodeIdFinalMyC = responseMyCorezoid.body.ops[0].list[2].obj_id;
    expect(responseMyCorezoid.status).toBe(200);

    await new Promise(r => setTimeout(r, 120000));
  });

  test('should download task from node (select: last10min) My Corezoid', async () => {
    await new Promise(r => setTimeout(r, 120000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id: newConvMyC,
        obj_id: nodeIdFinalMyC,
        select: 'last10minute',
        timezone_offset: 0,
        offset: 0,
        limit: 1,
        format: 'csv',
        extra_columns: ['ref'],
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(downloadTaskSchema, response.body);
    expect(response.body.ops[0].obj).toEqual(`node`);
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`.csv`);
    expect(response.body.ops[0].statistics_id).toContain(`.csv`);
  });

  test('should download task from node (select: custom) company', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,
        company_id,

        conv_id: newConv,
        obj_id: nodeIdFinal,
        select: 'custom',
        offset: 0,
        limit: 1,
        format: 'csv',
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(downloadTaskSchema, response.body);
    expect(response.body.ops[0].obj).toEqual(`node`);
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`.csv`);
    expect(response.body.ops[0].statistics_id).toContain(`.csv`);
  });

  test('should download task from node only requred parameter', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id: newConv,
        obj_id: nodeIdFinal,
        limit: 1,
        format: 'csv',
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(downloadTaskSchema, response.body);
    expect(response.body.ops[0].obj).toEqual(`node`);
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`.csv`);
    expect(response.body.ops[0].statistics_id).toContain(`.csv`);
  });

  test('should download task from node (select: lastHour)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id: newConv,
        obj_id: nodeIdFinal,
        select: 'lastHour',
        timezone_offset: 0,
        offset: 0,
        limit: 1,
        format: 'csv',
        extra_columns: ['ref'],
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(downloadTaskSchema, response.body);
    expect(response.body.ops[0].obj).toEqual(`node`);
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`.csv`);
    expect(response.body.ops[0].statistics_id).toContain(`.csv`);
  });

  test('should download task from node (select: last6hour)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id: newConv,
        obj_id: nodeIdFinal,
        select: 'last6hour',
        timezone_offset: 0,
        offset: 0,
        limit: 1,
        format: 'csv',
        extra_columns: ['ref'],
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(downloadTaskSchema, response.body);
    expect(response.body.ops[0].obj).toEqual(`node`);
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`.csv`);
    expect(response.body.ops[0].statistics_id).toContain(`.csv`);
  });

  test('should download task from node (select: lastWeek)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id: newConv,
        obj_id: nodeIdFinal,
        select: 'lastWeek',
        timezone_offset: 0,
        offset: 0,
        limit: 1,
        format: 'csv',
        extra_columns: ['ref'],
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(downloadTaskSchema, response.body);
    expect(response.body.ops[0].obj).toEqual(`node`);
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`.csv`);
    expect(response.body.ops[0].statistics_id).toContain(`.csv`);
  });

  test('should download task from node (select: lastMonth)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id: newConv,
        obj_id: nodeIdFinal,
        select: 'lastMonth',
        timezone_offset: 0,
        offset: 0,
        limit: 1,
        format: 'csv',
        extra_columns: ['ref'],
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(downloadTaskSchema, response.body);
    expect(response.body.ops[0].obj).toEqual(`node`);
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`.csv`);
    expect(response.body.ops[0].statistics_id).toContain(`.csv`);
  });

  test('should download task from node (select: previous_hour)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id: newConv,
        obj_id: nodeIdFinal,
        select: 'previous_hour',
        timezone_offset: 0,
        offset: 0,
        limit: 1,
        format: 'csv',
        extra_columns: ['ref'],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toEqual(`ok`);
    expect(response.body.ops[0].obj).toEqual(`node`);
    expect(response.body.ops[0].proc).toInclude(`error`);
    expect(response.body.ops[0].description).toContain(`there is no tasks in selected range`);
  });

  test('should download task from node (select: fromTo)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id: newConv,
        obj_id: nodeIdFinal,
        select: 'fromTo',
        start: startUnix,
        end: endUnix,
        offset: 0,
        limit: 1,
        format: 'csv',
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(downloadTaskSchema, response.body);
    expect(response.body.ops[0].obj).toEqual(`node`);
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`.csv`);
    expect(response.body.ops[0].statistics_id).toContain(`.csv`);
    downloadUrl = response.body.ops[0].download_url;
  });

  test('should get task from node extra_columns', async () => {
    const response = await api.requestGET(`${downloadUrl}`);
    expect(response.status).toBe(200);
    expect(response.body).toContain('a\n1');
  });

  test('should download task with shielded `""`', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,
        company_id,

        conv_id: newConv,
        obj_id: nodeIdFinal,
        select: 'custom',
        offset: 1,
        limit: 1,
        format: 'csv',
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(downloadTaskSchema, response.body);
    expect(response.body.ops[0].obj).toEqual(`node`);
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`.csv`);
    expect(response.body.ops[0].statistics_id).toContain(`.csv`);
    downloadUrl2 = response.body.ops[0].download_url;
  });

  test(`should get task with shielded ""`, async () => {
    await new Promise(r => setTimeout(r, 4000));

    function checkConditions(response: MyApiResponse): boolean {
      debug(JSON.stringify(response.body));
      return (
        response.status === 200 &&
        response.body.includes(
          `param9|param8|param7|param6|param5|param4|param3|param2|param11|param10|param\n"[""test,;"",""te|st2""]"|[12345,true,54321]|"{""ke|y6"":""value6,;""}"|"{""key6"":""value6""}"|true|12345|value3,;|"val|ue2"|"[{""key11"":[12312,3456,4545]},{""key11_2"":[{""key"":""valu|e""},{""key2"":123456}]}]"|"[{""key10"":""valu|e10""},{""key|10_2"":""value10_2""}]"|value`,
        )
      );
    }

    const response = await makeRequestWithRetries(async () => {
      return await api.requestGET(`${downloadUrl2}`);
    }, checkConditions);

    expect(response.status).toBe(200);
  });

  test('should download task from node extra_columns', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,
        company_id,
        conv_id: newConv,
        obj_id: nodeIdFinal,
        extra_columns: ['ref', 'task_id', 'status', 'user_id', 'create_time', 'change_time', 'node_prev_id'],
        select: 'custom',
        offset: 0,
        limit: 1,
        format: 'csv',
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(downloadTaskSchema, response.body);
    expect(response.body.ops[0].obj).toEqual(`node`);
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`.csv`);
    expect(response.body.ops[0].statistics_id).toContain(`.csv`);
    downloadUrl = response.body.ops[0].download_url;
  });

  test('should get task from node extra_columns', async () => {
    function checkConditions(response: MyApiResponse): boolean {
      return response.status === 200 && response.body.includes('1|ref_');
    }
    const response = await makeRequestWithRetries(
      async () => {
        return await api.requestGET(`${downloadUrl}`);
      },
      checkConditions,
      30,
    );
    expect(response.status).toBe(200);
    expect(response.body).toContain(
      'a|__ref__|__task_id__|__status__|__user_id__|__create_time__|__change_time__|__node_prev_id__',
    );
  });

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
  });
});
