import { debug } from '../../../../../../../../support/utils/logger';
import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../../infrastructure/model/ApiKey';
import { ConfigurationManager } from '../../../../../../../../infrastructure/config/ConfigurationManager';
import { OBJ_TYPE } from '../../../../../../../../utils/corezoidRequest';
import { addMsg } from 'jest-html-reporters/helper';
import {
  requestList,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
} from '../../../../../../../../application/api/ApiObj';
import { SchemaValidator } from '../../../../../../../../application/api/SchemaValidator';
import uploadAsyncFalse from '../../../../../schemas/v2/download-upload/uploadTaskAsyncFalse.Schema.json';
import uploadAsyncTrue from '../../../../../schemas/v2/download-upload/uploadTaskAsyncTrue.Schema.json';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';

describe('Upload task (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let key: string;
  let secret: string;
  let company_id: any;
  let newConv: number;
  let final_node_ID: string | number;
  const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
  const scriptPath = 'tests/api/corezoid-api/sh/uploadTask.sh';
  const filename = 'upload-task.csv';
  const exec = promisify(execCallback);

  function parseStdoutToJson(stdout: any): any {
    const jsonStartIndex = stdout.indexOf('{');
    const jsonString = stdout.slice(jsonStartIndex);
    return JSON.parse(jsonString);
  }

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key = apikey.key;
    secret = apikey.secret;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Delay`);
    newConv = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, newConv, company_id);
    expect(responseList.status).toBe(200);
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
  });

  test(`upload task async:false`, async () => {
    const { stdout } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        CONV_ID: `${newConv}`,
        ASYNC: 'false',
        DIVIDER: '|',
        ENCODING: 'utf-8',
        NAMES_IN_FIRST_ROW: 'false',
        COMPANY_ID: company_id,
        REFERENCE_COL_NAME: '',
        COLS_CONVERT_TO: ['object', 'array', 'boolean', 'number', 'string'].join(','),
        EXTRA_SYS_IP: 'true',
        EXTRA_SYS_FILENAME: 'true',
        FILE_NAME: filename,
      },
    });
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.request_proc).toBe(`ok`);
    expect(jsonResponse.ops[0].obj).toBe(`csv_upload`);
    SchemaValidator.validate(uploadAsyncFalse, jsonResponse);
  });

  test('should list node after upload task async:false', async () => {
    await new Promise(r => setTimeout(r, 3000));
    const responseListFinal = await requestList(api, final_node_ID, newConv, company_id);
    expect(responseListFinal.status).toBe(200);
    expect(responseListFinal.body.ops[0].proc).toEqual('ok');
    expect(responseListFinal.body.ops[0].count).toEqual(21);
    expect(responseListFinal.body.ops[0].list[0].data.__source_origin__.filename).toEqual(filename);
    expect(responseListFinal.body.ops[0].list[0].data.__source_origin__.ip).toBeString();
  });

  test(`upload task async:true, extra_sys_ip & extra_sys_filename:false`, async () => {
    const { stdout } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        CONV_ID: `${newConv}`,
        ASYNC: 'true',
        DIVIDER: '|',
        ENCODING: 'utf-8',
        NAMES_IN_FIRST_ROW: 'false',
        COMPANY_ID: company_id,
        REFERENCE_COL_NAME: '',
        COLS_CONVERT_TO: ['object', 'array', 'boolean', 'number', 'string'].join(','),
        EXTRA_SYS_IP: 'false',
        EXTRA_SYS_FILENAME: 'false',
        FILE_NAME: filename,
      },
    });
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.request_proc).toBe(`ok`);
    expect(jsonResponse.ops[0].obj).toBe(`csv_upload`);
    SchemaValidator.validate(uploadAsyncTrue, jsonResponse);
  });

  test('should list node after upload task async:true, extra_sys_ip & extra_sys_filename:false', async () => {
    await new Promise(r => setTimeout(r, 3000));
    const responseListFinal = await requestList(api, final_node_ID, newConv, company_id);
    expect(responseListFinal.status).toBe(200);
    expect(responseListFinal.body.ops[0].proc).toEqual('ok');
    expect(responseListFinal.body.ops[0].count).toEqual(42);
    expect(responseListFinal.body.ops[0].list[0].data).not.toContainKey('__source_origin__');
  });

  test(`upload task with encoding:windows-1251`, async () => {
    const { stdout } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        CONV_ID: `${newConv}`,
        ASYNC: 'false',
        DIVIDER: ',',
        ENCODING: 'windows-1251',
        NAMES_IN_FIRST_ROW: 'false',
        COMPANY_ID: company_id,
        REFERENCE_COL_NAME: '',
        COLS_CONVERT_TO: ['object', 'array', 'boolean', 'number', 'string'].join(','),
        EXTRA_SYS_IP: 'false',
        EXTRA_SYS_FILENAME: 'false',
        FILE_NAME: filename,
      },
    });
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.request_proc).toBe(`ok`);
    SchemaValidator.validate(uploadAsyncFalse, jsonResponse);
  });

  test('should list node after upload task async:true, extra_sys_ip & extra_sys_filename:false', async () => {
    await new Promise(r => setTimeout(r, 3000));
    const responseListFinal = await requestList(api, final_node_ID, newConv, company_id);
    expect(responseListFinal.status).toBe(200);
    expect(responseListFinal.body.ops[0].proc).toEqual('ok');
    expect(responseListFinal.body.ops[0].count).toEqual(63);
  });

  test(`upload task NAMES_IN_FIRST_ROW:true`, async () => {
    const { stdout } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        CONV_ID: `${newConv}`,
        ASYNC: 'false',
        DIVIDER: '|',
        ENCODING: 'utf-8',
        NAMES_IN_FIRST_ROW: 'true',
        COMPANY_ID: company_id,
        REFERENCE_COL_NAME: '',
        COLS_CONVERT_TO: ['object', 'array', 'boolean', 'number', 'string'].join(','),
        EXTRA_SYS_IP: 'true',
        EXTRA_SYS_FILENAME: 'true',
        FILE_NAME: filename,
      },
    });
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.request_proc).toBe(`ok`);
    expect(jsonResponse.ops[0].obj).toBe(`csv_upload`);
    SchemaValidator.validate(uploadAsyncFalse, jsonResponse);
  });

  test('should list node after upload task async:false', async () => {
    await new Promise(r => setTimeout(r, 3000));
    const responseListFinal = await requestList(api, final_node_ID, newConv, company_id);
    expect(responseListFinal.status).toBe(200);
    expect(responseListFinal.body.ops[0].proc).toEqual('ok');
    expect(responseListFinal.body.ops[0].count).toEqual(83);
    expect(responseListFinal.body.ops[0].list[0].data.__source_origin__.filename).toEqual(filename);
    expect(responseListFinal.body.ops[0].list[0].data.__source_origin__.ip).toBeString();
  });

  test(`upload task with REFERENCE_COL_NAME`, async () => {
    const { stdout } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        CONV_ID: `${newConv}`,
        ASYNC: 'false',
        DIVIDER: '|',
        ENCODING: 'utf-8',
        NAMES_IN_FIRST_ROW: 'true',
        COMPANY_ID: company_id,
        REFERENCE_COL_NAME: 'data_5',
        COLS_CONVERT_TO: ['object', 'array', 'boolean', 'integer', 'float'].join(','),
        EXTRA_SYS_IP: 'true',
        EXTRA_SYS_FILENAME: 'true',
        FILE_NAME: filename,
      },
    });
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.request_proc).toBe(`ok`);
    expect(jsonResponse.ops[0].obj).toBe(`csv_upload`);
    SchemaValidator.validate(uploadAsyncFalse, jsonResponse);
  });

  test('should list node after upload task with reference_col_name', async () => {
    await new Promise(r => setTimeout(r, 3000));
    const responseListFinal = await requestList(api, final_node_ID, newConv, company_id);
    expect(responseListFinal.status).toBe(200);
    expect(responseListFinal.body.ops[0].proc).toEqual('ok');
    expect(responseListFinal.body.ops[0].count).toEqual(103);
    expect(responseListFinal.body.ops[0].list[0].data.__source_origin__.filename).toEqual(filename);
    expect(responseListFinal.body.ops[0].list[0].data.__source_origin__.ip).toBeString();
    expect(responseListFinal.body.ops[0].list[0].ref).toEqual(`{\"param\":123123,\"param_2\":\"test\"}`);
  });

  test.skip(`upload task DIVIDER`, async () => {
    const { stdout } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        CONV_ID: `${newConv}`,
        ASYNC: 'false',
        DIVIDER: ';',
        ENCODING: 'utf-8',
        NAMES_IN_FIRST_ROW: 'false',
        COMPANY_ID: company_id,
        REFERENCE_COL_NAME: '',
        COLS_CONVERT_TO: 'string',
        EXTRA_SYS_IP: 'true',
        EXTRA_SYS_FILENAME: 'true',
        FILE_NAME: filename,
      },
    });
    debug(stdout);
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.request_proc).toBe(`ok`);
    expect(jsonResponse.ops[0].obj).toBe(`csv_upload`);
    SchemaValidator.validate(uploadAsyncFalse, jsonResponse);
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, company_id);
    expect(response.status).toBe(200);
  });
});
