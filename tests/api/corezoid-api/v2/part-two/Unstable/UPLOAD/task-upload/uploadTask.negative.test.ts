import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../../../../../../../infrastructure/model/ApiKey';
import { ConfigurationManager } from '../../../../../../../../infrastructure/config/ConfigurationManager';
import { OBJ_TYPE } from '../../../../../../../../utils/corezoidRequest';
import { addMsg } from 'jest-html-reporters/helper';
import { requestCreateObj } from '../../../../../../../../application/api/ApiObj';
import { integerTestCases, stringTestCases, undefinedTestCase, boolTestCases } from '../../../../../../negativeCases';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';

describe('Upload task (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let key: string;
  let secret: string;
  let company_id: any;
  let newConv: number;
  const valuesToSkip: any = [0];
  const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
  const scriptPath = 'tests/api/corezoid-api/sh/uploadTask.sh';
  const filename = 'upload-task.csv';
  const exec = promisify(execCallback);

  function parseStdoutToJson(stdout: any): any {
    const jsonStartIndex = stdout.indexOf('{');
    const jsonString = stdout.slice(jsonStartIndex);
    return JSON.parse(jsonString);
  }

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key = apikey.key;
    secret = apikey.secret;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Delay`);
    newConv = response.body.ops[0].obj_id;
  });

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't upload in stage with invalid CONV_ID: '%s'`, async (input, errors) => {
    const { stdout } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        CONV_ID: (input as unknown) as string,
        ASYNC: 'false',
        DIVIDER: '|',
        ENCODING: 'utf-8',
        NAMES_IN_FIRST_ROW: 'false',
        COMPANY_ID: company_id,
        REFERENCE_COL_NAME: '',
        COLS_CONVERT_TO: ['object', 'array', 'boolean', 'number', 'string'].join(','),
        EXTRA_SYS_IP: 'true',
        EXTRA_SYS_FILENAME: 'true',
        FILE_NAME: filename,
      },
    });
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.ops[0].proc).toBe('error');
    const error = jsonResponse.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...boolTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't upload in stage with invalid ASYNC '%s'`,
    async (input, errors) => {
      const { stdout } = await exec(scriptPath, {
        env: {
          API_KEY_LOGIN: key,
          API_KEY_SECRET: secret,
          API_URL: baseUrl + 'api/2/upload',
          CONV_ID: `${newConv}`,
          ASYNC: (input as unknown) as string,
          DIVIDER: '|',
          ENCODING: 'utf-8',
          NAMES_IN_FIRST_ROW: 'false',
          COMPANY_ID: company_id,
          REFERENCE_COL_NAME: '',
          COLS_CONVERT_TO: ['object', 'array', 'boolean', 'number', 'string'].join(','),
          EXTRA_SYS_IP: 'true',
          EXTRA_SYS_FILENAME: 'true',
          FILE_NAME: filename,
        },
      });
      const jsonResponse = parseStdoutToJson(stdout);
      addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
      expect(jsonResponse.ops[0].proc).toBe('error');
      const error = jsonResponse.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't upload in stage with invalid DIVIDER '%s'`,
    async (input, errors) => {
      void errors;
      const { stdout } = await exec(scriptPath, {
        env: {
          API_KEY_LOGIN: key,
          API_KEY_SECRET: secret,
          API_URL: baseUrl + 'api/2/upload',
          CONV_ID: `${newConv}`,
          ASYNC: 'false',
          DIVIDER: (input as unknown) as string,
          ENCODING: 'utf-8',
          NAMES_IN_FIRST_ROW: 'false',
          COMPANY_ID: company_id,
          REFERENCE_COL_NAME: '',
          COLS_CONVERT_TO: ['object', 'array', 'boolean', 'number', 'string'].join(','),
          EXTRA_SYS_IP: 'true',
          EXTRA_SYS_FILENAME: 'true',
          FILE_NAME: filename,
        },
      });
      await addMsg({ message: JSON.stringify(stdout), context: '' });
      expect(stdout).toContain(`{"request_proc":"ok","ops":[{"proc":"error","description":"Key divider`);
    },
  );

  test.each([...stringTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't upload in stage with invalid ENCODING '%s'`,
    async (input, errors) => {
      void errors;
      const { stdout } = await exec(scriptPath, {
        env: {
          API_KEY_LOGIN: key,
          API_KEY_SECRET: secret,
          API_URL: baseUrl + 'api/2/upload',
          CONV_ID: `${newConv}`,
          ASYNC: 'false',
          DIVIDER: '|',
          ENCODING: (input as unknown) as string,
          NAMES_IN_FIRST_ROW: 'false',
          COMPANY_ID: company_id,
          REFERENCE_COL_NAME: '',
          COLS_CONVERT_TO: ['object', 'array', 'boolean', 'number', 'string'].join(','),
          EXTRA_SYS_IP: 'true',
          EXTRA_SYS_FILENAME: 'true',
          FILE_NAME: filename,
        },
      });
      await addMsg({ message: JSON.stringify(stdout), context: '' });
      expect(stdout).toContain(`{"request_proc":"ok","ops":[{"proc":"error","description":"Key encoding.`);
    },
  );

  test.each([...boolTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't upload in stage with invalid NAMES_IN_FIRST_ROW '%s'`,
    async (input, errors) => {
      const { stdout } = await exec(scriptPath, {
        env: {
          API_KEY_LOGIN: key,
          API_KEY_SECRET: secret,
          API_URL: baseUrl + 'api/2/upload',
          CONV_ID: `${newConv}`,
          ASYNC: 'false',
          DIVIDER: '|',
          ENCODING: 'utf-8',
          NAMES_IN_FIRST_ROW: (input as unknown) as string,
          COMPANY_ID: company_id,
          REFERENCE_COL_NAME: '',
          COLS_CONVERT_TO: ['object', 'array', 'boolean', 'number', 'string'].join(','),
          EXTRA_SYS_IP: 'true',
          EXTRA_SYS_FILENAME: 'true',
          FILE_NAME: filename,
        },
      });
      const jsonResponse = parseStdoutToJson(stdout);
      addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
      expect(jsonResponse.ops[0].proc).toBe('error');
      const error = jsonResponse.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([null, true, 12, -1, 0])(`shouldn't upload in stage with invalid COLS_CONVERT_TO '%s'`, async param => {
    const { stdout } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        CONV_ID: `${newConv}`,
        ASYNC: 'false',
        DIVIDER: '|',
        ENCODING: 'utf-8',
        NAMES_IN_FIRST_ROW: 'false',
        COMPANY_ID: company_id,
        REFERENCE_COL_NAME: '',
        COLS_CONVERT_TO: (param as unknown) as string,
        EXTRA_SYS_IP: 'true',
        EXTRA_SYS_FILENAME: 'true',
        FILE_NAME: filename,
      },
    });
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.ops[0].proc).toBe('error');
    expect(jsonResponse.ops[0].description).toEqual(
      `Key cols_convert_to. Value '${param}' is not valid. Type of value is not 'list'`,
    );
  });

  test.each([...boolTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't upload in stage with invalid EXTRA_SYS_IP '%s'`,
    async (input, errors) => {
      const { stdout } = await exec(scriptPath, {
        env: {
          API_KEY_LOGIN: key,
          API_KEY_SECRET: secret,
          API_URL: baseUrl + 'api/2/upload',
          CONV_ID: `${newConv}`,
          ASYNC: 'false',
          DIVIDER: '|',
          ENCODING: 'utf-8',
          NAMES_IN_FIRST_ROW: 'false',
          COMPANY_ID: company_id,
          REFERENCE_COL_NAME: '',
          COLS_CONVERT_TO: ['object', 'array', 'boolean', 'number', 'string'].join(','),
          EXTRA_SYS_IP: (input as unknown) as string,
          EXTRA_SYS_FILENAME: 'true',
          FILE_NAME: filename,
        },
      });
      const jsonResponse = parseStdoutToJson(stdout);
      addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
      expect(jsonResponse.ops[0].proc).toBe('error');
      const error = jsonResponse.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...boolTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't upload in stage with invalid EXTRA_SYS_FILENAME '%s'`,
    async (input, errors) => {
      const { stdout } = await exec(scriptPath, {
        env: {
          API_KEY_LOGIN: key,
          API_KEY_SECRET: secret,
          API_URL: baseUrl + 'api/2/upload',
          CONV_ID: `${newConv}`,
          ASYNC: 'false',
          DIVIDER: '|',
          ENCODING: 'utf-8',
          NAMES_IN_FIRST_ROW: 'false',
          COMPANY_ID: company_id,
          REFERENCE_COL_NAME: '',
          COLS_CONVERT_TO: ['object', 'array', 'boolean', 'number', 'string'].join(','),
          EXTRA_SYS_IP: 'true',
          EXTRA_SYS_FILENAME: (input as unknown) as string,
          FILE_NAME: filename,
        },
      });
      const jsonResponse = parseStdoutToJson(stdout);
      addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
      expect(jsonResponse.ops[0].proc).toBe('error');
      const error = jsonResponse.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  afterAll(async () => {
    // const response = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, company_id);
    // expect(response.status).toBe(200);
  });
});
