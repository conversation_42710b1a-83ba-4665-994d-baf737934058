import { debug } from '../../../../../../../../support/utils/logger';
import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../../infrastructure/model/ApiKey';
import { ConfigurationManager } from '../../../../../../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../../utils/corezoidRequest';
import { addMsg } from 'jest-html-reporters/helper';
import { SchemaValidator } from '../../../../../../../../application/api/SchemaValidator';
import uploadAsyncFalse from '../../../../../schemas/v2/download-upload/uploadAsyncFalse.Schema.json';
import uploadAsyncTrue from '../../../../../schemas/v2/download-upload/uploadAsyncTrue.Schema.json';
import validateObj from '../../../../../schemas/v2/download-upload/validateObj.Schema.json';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';

describe('Upload object (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let key: string;
  let secret: string;
  let newFolder: string | number;
  let newFolder2: string | number;
  let newFolder3: number;
  let newFolder4: number;
  let newProject: number;
  let newConv2: number;
  let newStage: number;
  let newStage2: number;
  let newStage3: number;
  let short_name: string;
  let company_id: any;
  let newConv: string | number;
  const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
  const scriptPath = 'tests/api/corezoid-api/sh/uploadObject.sh';
  const scriptPath1 = 'tests/api/corezoid-api/sh/uploadObjectInStage.sh';
  const scriptPathValidate = 'tests/api/corezoid-api/sh/validateObject.sh';
  const scriptPathValidate2 = 'tests/api/corezoid-api/sh/validateObjectInStage.sh';
  const exec = promisify(execCallback);

  function parseStdoutToJson(stdout: any): any {
    const jsonStartIndex = stdout.indexOf('{');
    const jsonString = stdout.slice(jsonStartIndex);
    return JSON.parse(jsonString);
  }

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key = apikey.key;
    secret = apikey.secret;
    short_name = `project${Date.now()}`;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: 0,
        title: `Folder_${Date.now()}`,
      }),
    );
    newFolder2 = response.body.ops[0].obj_id;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: short_name,
        short_name,
        description: 'test',
        stages: ['dev', 'pre'],
        status: 'active',
      }),
    );
    expect(responseProject.status).toBe(200);
    newProject = responseProject.body.ops[0].obj_id;
    newStage = responseProject.body.ops[0].stages[0];
    newStage2 = responseProject.body.ops[0].stages[1];
  });

  test(`upload async=false`, async () => {
    const { stdout } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        FOLDER_ID: '0',
        ASYNC: 'false',
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        VALIDATE_SCHEME: 'false',
        COMPANY_ID: company_id,
        FILE_NAME: 'folder_for_test_upload.zip',
      },
    });
    debug(stdout);
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.request_proc).toBe(`ok`);
    expect(jsonResponse.ops[0].scheme[0].title).toBe(`Test_upload`);
    newFolder = jsonResponse.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(uploadAsyncFalse, jsonResponse);
  });

  test('should find folder after upload', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder));
  });

  test('should find conv folder after upload', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: `Test_upload` })]),
    );
    newConv = response.body.ops[0].list[0].obj_id;
  });

  test('should find param conv after upload folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(
      (response.body.ops[0].list as Array<any>).find(item => item.title === 'Set Param').logics[0].extra,
    ).toStrictEqual({
      param: `{{conv[{{conv[${newConv}].ref[StateDiagramIds].{{countryCode}}}}].ref[GlobalVariables].CoreAPIBaseUrl}}/health`,
    });
  });

  test(`upload async=true`, async () => {
    const { stdout } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        FOLDER_ID: `${newFolder2}`,
        ASYNC: 'true',
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        VALIDATE_SCHEME: 'false',
        COMPANY_ID: company_id,
        FILE_NAME: 'folder_for_test_upload.zip',
      },
    });
    debug(stdout);
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.ops[0].proc).toBe(`ok`);
    expect(jsonResponse.ops[0].hash).toBeString();
    SchemaValidator.validate(uploadAsyncTrue, jsonResponse);
  });

  test('should find conv folder after upload async=true', async () => {
    await new Promise(r => setTimeout(r, 3000));

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder2,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: `Test_upload` })]),
    );
  });

  test(`upload in stage OBJ_TO_TYPE=stage`, async () => {
    const { stdout } = await exec(scriptPath1, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        ASYNC: 'false',
        OBJ_TO_ID: `${newStage}`,
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        VALIDATE_SCHEME: 'false',
        COMPANY_ID: company_id,
        FILE_NAME: 'folder_for_test_upload.zip',
        SKIP_ROOT: 'false',
        OBJ_TO_TYPE: 'stage',
      },
    });
    debug(stdout);
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.ops[0].proc).toBe(`ok`);
    SchemaValidator.validate(uploadAsyncFalse, jsonResponse);
    newFolder3 = jsonResponse.ops[0].scheme[0].obj_id;
  });

  test('should find conv folder after upload OBJ_TO_TYPE=stage', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStage,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: `Test_upload` })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder3 })]),
    );
  });

  test(`upload in stage OBJ_TO_TYPE=folder`, async () => {
    const { stdout } = await exec(scriptPath1, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        ASYNC: 'false',
        OBJ_TO_ID: `${newFolder3}`,
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        VALIDATE_SCHEME: 'false',
        COMPANY_ID: company_id,
        FILE_NAME: 'folder_for_test_upload.zip',
        SKIP_ROOT: 'false',
        OBJ_TO_TYPE: 'folder',
      },
    });
    debug(stdout);
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.ops[0].proc).toBe(`ok`);
    SchemaValidator.validate(uploadAsyncFalse, jsonResponse);
    newFolder4 = jsonResponse.ops[0].scheme[0].obj_id;
  });

  test('should find conv folder after upload OBJ_TO_TYPE=folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder3,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: `Test_upload` })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolder4 })]),
    );
  });

  test(`upload in stage OBJ_TO_TYPE=project`, async () => {
    const { stdout } = await exec(scriptPath1, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        ASYNC: 'false',
        OBJ_TO_ID: `${newProject}`,
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        VALIDATE_SCHEME: 'false',
        COMPANY_ID: company_id,
        FILE_NAME: 'stage_alias_test.zip',
        SKIP_ROOT: 'false',
        OBJ_TO_TYPE: 'project',
      },
    });
    debug(stdout);
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.ops[0].proc).toBe(`ok`);
    SchemaValidator.validate(uploadAsyncFalse, jsonResponse);
    newStage3 = jsonResponse.ops[0].scheme[0].obj_id;
  });

  test('should find stage in project after upload OBJ_TO_TYPE=project', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newStage3 })]));
  });

  test(`upload in stage SKIP_ROOT=true`, async () => {
    const { stdout } = await exec(scriptPath1, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        ASYNC: 'false',
        OBJ_TO_ID: `${newStage2}`,
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        VALIDATE_SCHEME: 'false',
        COMPANY_ID: company_id,
        FILE_NAME: 'stage_alias_test.zip',
        SKIP_ROOT: 'true',
        OBJ_TO_TYPE: 'stage',
      },
    });
    debug(stdout);
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.ops[0].proc).toBe(`ok`);
    SchemaValidator.validate(uploadAsyncFalse, jsonResponse);
  });

  test('should find obj in folder after upload SKIP_ROOT=true', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStage2,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_type: 'dashboard' })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_type: 'folder' })]),
    );
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_type: 'conv' })]));

    const responseAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id: newProject,
        stage_id: newStage2,
      }),
    );
    expect(responseAlias.status).toBe(200);
    newConv2 = responseAlias.body.ops[0].list[0].obj_to_id;
  });

  test(`upload in stage - Alias with short_name already exists`, async () => {
    const { stdout } = await exec(scriptPath1, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        ASYNC: 'false',
        OBJ_TO_ID: `${newStage2}`,
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        VALIDATE_SCHEME: 'false',
        COMPANY_ID: company_id,
        FILE_NAME: 'stage_alias_test.zip',
        SKIP_ROOT: 'true',
        OBJ_TO_TYPE: 'stage',
      },
    });
    debug(stdout);
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.ops[0].proc).toBe(`error`);
    expect(jsonResponse.ops[0].errors[0].destinations[0].errors).toBeArray();
  });

  test(`upload in stage REWRITE_ALIAS=true`, async () => {
    const { stdout } = await exec(scriptPath1, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        ASYNC: 'false',
        OBJ_TO_ID: `${newStage2}`,
        REWRITE_ALIAS: 'true',
        WITH_ALIAS: 'true',
        VALIDATE_SCHEME: 'false',
        COMPANY_ID: company_id,
        FILE_NAME: 'stage_alias_test.zip',
        SKIP_ROOT: 'true',
        OBJ_TO_TYPE: 'stage',
      },
    });
    debug(stdout);
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.ops[0].proc).toBe(`ok`);
    SchemaValidator.validate(uploadAsyncFalse, jsonResponse);
  });

  test('should find alias after upload REWRITE_ALIAS=true', async () => {
    const responseAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id: newProject,
        stage_id: newStage2,
      }),
    );
    expect(responseAlias.status).toBe(200);
    expect(responseAlias.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_to_id: newConv2 })]),
    );
  });

  test(`validate with folder_id and company_id, WITH_ALIAS=true`, async () => {
    const { stdout } = await exec(scriptPathValidate, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        FOLDER_ID: `${newFolder2}`,
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        COMPANY_ID: company_id,
        FILE_NAME: 'folder_for_test_upload.zip',
      },
    });
    debug(stdout);
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.request_proc).toBe(`ok`);
    expect(jsonResponse.ops[0].proc).toBe(`ok`);
    SchemaValidator.validate(validateObj, jsonResponse);
  });

  test(`validate in stage OBJ_TO_TYPE=stage`, async () => {
    const { stdout } = await exec(scriptPathValidate2, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        OBJ_TO_ID: `${newStage}`,
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        COMPANY_ID: company_id,
        FILE_NAME: 'folder_for_test_upload.zip',
        OBJ_TO_TYPE: 'stage',
      },
    });
    debug(stdout);
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.ops[0].proc).toBe(`ok`);
    SchemaValidator.validate(validateObj, jsonResponse);
  });

  test(`validate in stage OBJ_TO_TYPE=folder`, async () => {
    const { stdout } = await exec(scriptPathValidate2, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        OBJ_TO_ID: `${newFolder3}`,
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        COMPANY_ID: company_id,
        FILE_NAME: 'folder_for_test_upload.zip',
        OBJ_TO_TYPE: 'folder',
      },
    });
    debug(stdout);
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.ops[0].proc).toBe(`ok`);
    SchemaValidator.validate(validateObj, jsonResponse);
  });

  test(`validate in stage OBJ_TO_TYPE=project`, async () => {
    const { stdout } = await exec(scriptPathValidate2, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        OBJ_TO_ID: `${newProject}`,
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        COMPANY_ID: company_id,
        FILE_NAME: 'stage_alias_test.zip',
        OBJ_TO_TYPE: 'project',
      },
    });
    debug(stdout);
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.ops[0].proc).toBe(`ok`);
    SchemaValidator.validate(validateObj, jsonResponse);
  });

  test(`validate in stage REWRITE_ALIAS=true`, async () => {
    const { stdout } = await exec(scriptPathValidate2, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        OBJ_TO_ID: `${newStage2}`,
        REWRITE_ALIAS: 'true',
        WITH_ALIAS: 'true',
        COMPANY_ID: company_id,
        FILE_NAME: 'stage_alias_test.zip',
        OBJ_TO_TYPE: 'stage',
      },
    });
    debug(stdout);
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.ops[0].proc).toBe(`ok`);
    SchemaValidator.validate(validateObj, jsonResponse);
  });

  test(`validate in stage WITH_ALIAS=false`, async () => {
    const { stdout } = await exec(scriptPathValidate2, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        OBJ_TO_ID: `${newStage2}`,
        REWRITE_ALIAS: 'true',
        WITH_ALIAS: 'false',
        COMPANY_ID: company_id,
        FILE_NAME: 'stage_alias_test.zip',
        OBJ_TO_TYPE: 'stage',
      },
    });
    debug(stdout);
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.ops[0].proc).toBe(`ok`);
    SchemaValidator.validate(validateObj, jsonResponse);
  });

  afterAll(async () => {
    const ResponseDeleteFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(ResponseDeleteFolder.status).toBe(200);

    const ResponseDeleteFolder2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder2,
        company_id,
      }),
    );
    expect(ResponseDeleteFolder2.status).toBe(200);

    const ResponseDeleteProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(ResponseDeleteProject.status).toBe(200);
  });
});
