import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { <PERSON>pi<PERSON><PERSON> } from '../../../../../../../../infrastructure/model/ApiKey';
import { ConfigurationManager } from '../../../../../../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../../utils/corezoidRequest';
import { addMsg } from 'jest-html-reporters/helper';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';

describe('Upload object (positive)', () => {
  let api: ApiKeyClient;
  let apikey: Api<PERSON><PERSON>;
  let key: string;
  let secret: string;
  let company_id: any;
  let newProject: number;
  let newStage: any;
  let short_name: string;
  const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
  const scriptPath = 'tests/api/corezoid-api/sh/uploadObject.sh';
  const scriptPath2 = 'tests/api/corezoid-api/sh/uploadObjectInStage.sh';
  const scriptPathValidate = 'tests/api/corezoid-api/sh/validateObject.sh';
  const scriptPathValidate2 = 'tests/api/corezoid-api/sh/validateObjectInStage.sh';
  const exec = promisify(execCallback);

  function parseStdoutToJson(stdout: any): any {
    const jsonStartIndex = stdout.indexOf('{');
    const jsonString = stdout.slice(jsonStartIndex);
    return JSON.parse(jsonString);
  }

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key = apikey.key;
    secret = apikey.secret;
    short_name = `project${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: short_name,
        short_name,
        description: 'test',
        stages: ['dev', 'pre'],
        status: 'active',
      }),
    );
    expect(responseProject.status).toBe(200);
    newProject = responseProject.body.ops[0].obj_id;
    newStage = responseProject.body.ops[0].stages[0];
  });

  test.skip(`validate scheme: no valid zip`, async () => {
    const { stdout, stderr } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        VALIDATE_SCHEME: 'false',
        COMPANY_ID: company_id,
        FOLDER_ID: '0',
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        FILE_NAME: '28fb121d-9977-4f06-bdb5-d65fe0aaf9c0.zip',
        ASYNC: 'false',
      },
    });
    expect(stderr).toBe(``);
    await addMsg({ message: JSON.stringify(stdout), context: '' });
    expect(stdout).toContain(
      `{"request_proc":"ok","ops":[{"obj":"obj_scheme","proc":"error","description":"no valid zip"}]}`,
    );
  });

  test.each([null, 1234, 'test', -1, 2, [], {}])(`shouldn't upload in stage with invalid ASYNC: '%s'`, async param => {
    const { stdout } = await exec(scriptPath2, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        ASYNC: (param as unknown) as string,
        OBJ_TO_ID: newStage,
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        VALIDATE_SCHEME: 'false',
        COMPANY_ID: company_id,
        FILE_NAME: 'folder_for_test_upload.zip',
        SKIP_ROOT: 'true',
        OBJ_TO_TYPE: 'stage',
      },
    });
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.ops[0].proc).toBe('error');
    expect(jsonResponse.ops[0].description).toEqual('Key async. Value is not valid');
  });

  test.each([null, 1234, 'test', -1, 2, [], {}])(
    `shouldn't upload in stage with invalid SKIP_ROOT: '%s'`,
    async param => {
      const { stdout } = await exec(scriptPath2, {
        env: {
          API_KEY_LOGIN: key,
          API_KEY_SECRET: secret,
          API_URL: baseUrl + 'api/2/upload',
          ASYNC: 'false',
          OBJ_TO_ID: newStage,
          REWRITE_ALIAS: 'true',
          WITH_ALIAS: 'true',
          VALIDATE_SCHEME: 'false',
          COMPANY_ID: company_id,
          FILE_NAME: 'folder_for_test_upload.zip',
          SKIP_ROOT: (param as unknown) as string,
          OBJ_TO_TYPE: 'stage',
        },
      });
      const jsonResponse = parseStdoutToJson(stdout);
      addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
      expect(jsonResponse.ops[0].proc).toBe('error');
      expect(jsonResponse.ops[0].description).toEqual('Key skip_root. Value is not valid');
    },
  );

  test.each([null, 1234, 'test', -1, 2, [], {}])(
    `shouldn't upload in stage with invalid WITH_ALIAS: '%s'`,
    async param => {
      const { stdout } = await exec(scriptPath2, {
        env: {
          API_KEY_LOGIN: key,
          API_KEY_SECRET: secret,
          API_URL: baseUrl + 'api/2/upload',
          ASYNC: 'false',
          OBJ_TO_ID: newStage,
          REWRITE_ALIAS: 'true',
          WITH_ALIAS: (param as unknown) as string,
          VALIDATE_SCHEME: 'false',
          COMPANY_ID: company_id,
          FILE_NAME: 'folder_for_test_upload.zip',
          SKIP_ROOT: 'true',
          OBJ_TO_TYPE: 'stage',
        },
      });
      const jsonResponse = parseStdoutToJson(stdout);
      addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
      expect(jsonResponse.ops[0].proc).toBe('error');
      expect(jsonResponse.ops[0].description).toEqual('Key with_alias. Value is not valid');
    },
  );

  test.each([null, 1234, 'test', -1, 2, [], {}])(
    `shouldn't upload in stage with invalid REWRITE_ALIAS: '%s'`,
    async param => {
      const { stdout } = await exec(scriptPath2, {
        env: {
          API_KEY_LOGIN: key,
          API_KEY_SECRET: secret,
          API_URL: baseUrl + 'api/2/upload',
          ASYNC: 'false',
          OBJ_TO_ID: newStage,
          REWRITE_ALIAS: (param as unknown) as string,
          WITH_ALIAS: 'true',
          VALIDATE_SCHEME: 'false',
          COMPANY_ID: company_id,
          FILE_NAME: 'folder_for_test_upload.zip',
          SKIP_ROOT: 'true',
          OBJ_TO_TYPE: 'stage',
        },
      });
      const jsonResponse = parseStdoutToJson(stdout);
      addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
      expect(jsonResponse.ops[0].proc).toBe('error');
      expect(jsonResponse.ops[0].description).toEqual('Key rewrite_alias. Value is not valid');
    },
  );

  test.each([null, true, 'test', [], {}, undefined])(
    `shouldn't upload in stage with invalid FOLDER_ID: '%s'`,
    async param => {
      const { stdout } = await exec(scriptPath, {
        env: {
          API_KEY_LOGIN: key,
          API_KEY_SECRET: secret,
          API_URL: baseUrl + 'api/2/upload',
          FOLDER_ID: (param as unknown) as string,
          ASYNC: 'false',
          REWRITE_ALIAS: 'false',
          WITH_ALIAS: 'true',
          VALIDATE_SCHEME: 'false',
          COMPANY_ID: company_id,
          FILE_NAME: 'folder_for_test_upload.zip',
        },
      });
      const jsonResponse = parseStdoutToJson(stdout);
      addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
      expect(jsonResponse.ops[0].proc).toBe('error');
      expect(jsonResponse.ops[0].description).toEqual(`Value is not valid or Key 'obj_to_id' is required`);
    },
  );

  test.each([null, 1234, 'test', -1, 2, [], {}])(
    `shouldn't validate in stage with invalid WITH_ALIAS: '%s'`,
    async param => {
      const { stdout } = await exec(scriptPathValidate2, {
        env: {
          API_KEY_LOGIN: key,
          API_KEY_SECRET: secret,
          API_URL: baseUrl + 'api/2/upload',
          OBJ_TO_ID: newStage,
          REWRITE_ALIAS: 'false',
          WITH_ALIAS: (param as unknown) as string,
          COMPANY_ID: company_id,
          FILE_NAME: 'folder_for_test_upload.zip',
          OBJ_TO_TYPE: 'stage',
        },
      });
      const jsonResponse = parseStdoutToJson(stdout);
      addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
      expect(jsonResponse.ops[0].proc).toBe('error');
      expect(jsonResponse.ops[0].description).toEqual('Key with_alias. Value is not valid');
    },
  );

  test.each([null, 1234, 'test', -1, 2, [], {}])(
    `shouldn't validate in stage with invalid REWRITE_ALIAS: '%s'`,
    async param => {
      const { stdout } = await exec(scriptPathValidate2, {
        env: {
          API_KEY_LOGIN: key,
          API_KEY_SECRET: secret,
          API_URL: baseUrl + 'api/2/upload',
          OBJ_TO_ID: newStage,
          REWRITE_ALIAS: (param as unknown) as string,
          WITH_ALIAS: 'true',
          COMPANY_ID: company_id,
          FILE_NAME: 'folder_for_test_upload.zip',
          OBJ_TO_TYPE: 'stage',
        },
      });
      const jsonResponse = parseStdoutToJson(stdout);
      addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
      expect(jsonResponse.ops[0].proc).toBe('error');
      expect(jsonResponse.ops[0].description).toEqual('Key rewrite_alias. Value is not valid');
    },
  );

  test.each([null, true, 'test', [], {}, undefined])(
    `shouldn't validate in stage with invalid FOLDER_ID: '%s'`,
    async param => {
      const { stdout } = await exec(scriptPathValidate, {
        env: {
          API_KEY_LOGIN: key,
          API_KEY_SECRET: secret,
          API_URL: baseUrl + 'api/2/upload',
          FOLDER_ID: (param as unknown) as string,
          REWRITE_ALIAS: 'false',
          WITH_ALIAS: 'true',
          COMPANY_ID: company_id,
          FILE_NAME: 'folder_for_test_upload.zip',
        },
      });
      const jsonResponse = parseStdoutToJson(stdout);
      addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
      expect(jsonResponse.ops[0].proc).toBe('error');
      expect(jsonResponse.ops[0].description).toEqual(`Value is not valid or Key 'obj_to_id' is required`);
    },
  );

  afterAll(async () => {
    const ResponseDeleteProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(ResponseDeleteProject.status).toBe(200);
  });
});
