import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { ApiUserClient } from '../../../../../../../../application/api/ApiUserClient';
import { application } from '../../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../../infrastructure/model/ApiKey';
import { ConfigurationManager } from '../../../../../../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../../application/api/ApiObj';
import { User } from '../../../../../../../../infrastructure/model/User';
import { addMsg } from 'jest-html-reporters/helper';
import { SchemaValidator } from '../../../../../../../../application/api/SchemaValidator';
import Upload_info from '../../../../../schemas/v2/download-upload/Upload_info.Schema.json';
import Upload_info_with_error from '../../../../../schemas/v2/download-upload/Upload_info_with_error.Schema.json';
import Upload_info_error from '../../../../../schemas/v2/download-upload/Upload_info_error.Schema.json';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';
import { createAuthUser, Method } from '../../../../../../../../utils/request';

describe('Upload_info (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let apiUserCookie: ApiUserClient;
  let key: string;
  let secret: string;
  let newFolder: string | number;
  let newConv: string | number;
  let newAlias: string | number;
  let newDash: string | number;
  let newFolder2: string | number;
  let newInstance: string | number;
  let newProject: string | number;
  let newStage: string | number;
  let newStageProd: string | number;
  let newStageDev: string | number;
  let hashUploadFolder: string | number;
  let hashUploadConv: string | number;
  let company_id: any;
  let hashCopy: string | number;
  let hashMerge: string | number;
  let user: User;
  let cookieUserOwner: string;
  const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
  const scriptPath = 'tests/api/corezoid-api/sh/uploadObject.sh';
  const scriptPath1 = 'tests/api/corezoid-api/sh/uploadObjectInStage.sh';
  const exec = promisify(execCallback);

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key = apikey.key;
    secret = apikey.secret;

    user = await application.getAuthorizedUser({ company: {} }, 0);
    cookieUserOwner = user.cookieUser;
    apiUserCookie = await application.getApiUserClient(user);

    const responseFolder = await requestCreateObj(api, OBJ_TYPE.FOLDER, company_id, `Folder_${Date.now()}`, 0);
    newFolder = responseFolder.body.ops[0].obj_id;

    const responseFolder2 = await requestCreateObj(api, OBJ_TYPE.FOLDER, company_id, `Folder_${Date.now()}`, newFolder);
    newFolder2 = responseFolder2.body.ops[0].obj_id;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: `Project_${Date.now()}`,
        short_name: `name-${Date.now()}`,
      }),
    );
    expect(responseProject.status).toBe(200);
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        title: `Stage_${Date.now()}`,
        short_name: `namestage-${Date.now()}`,
        project_id: newProject,
      }),
    );
    expect(responseStage.status).toBe(200);
    newStage = responseStage.body.ops[0].obj_id;

    const responseStageDev = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        title: `StageDev_${Date.now()}`,
        short_name: `namestagedev-${Date.now()}`,
        project_id: newProject,
      }),
    );
    expect(responseStageDev.status).toBe(200);
    newStageDev = responseStageDev.body.ops[0].obj_id;

    const responseStageProd = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `StageProd_${Date.now()}`,
        short_name: `namestageprod-${Date.now()}`,
        project_id: newProject,
        immutable: true,
      }),
    );
    expect(responseStageProd.status).toBe(200);
    newStageProd = responseStageProd.body.ops[0].obj_id;

    const { stdout, stderr } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        FOLDER_ID: `${newFolder}`,
        VALIDATE_SCHEME: 'false',
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        COMPANY_ID: company_id,
        FILE_NAME: 'folder_for_test_upload_univer.suite_70conv.zip',
        ASYNC: 'true',
      },
    });
    expect(stderr).toBe(``);
    expect(stdout).toContain(`{"request_proc":"ok","ops":[{"obj":"obj_scheme","proc":"ok","hash"`);
    await addMsg({ message: JSON.stringify(stdout), context: '' });
    hashUploadFolder = stdout.substr(76, 146);
  });

  test('should get Upload_info after copy folder', async () => {
    const responseCopyFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolder2,
        folder_id: newFolder,
        obj_type: 'folder',
        title: 'copyFolderAsync',
        ignore_errors: true,
        async: true,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(responseCopyFolder.status).toBe(200);
    expect(responseCopyFolder.body.ops[0].obj).toBe('obj_copy');
    hashCopy = responseCopyFolder.body.ops[0].hash;

    await new Promise(r => setTimeout(r, 1000));

    const response = await api.requestGET(`${baseUrl}api/2/upload/info`, `hash=${hashCopy}`);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].status).toBe('success');
    expect(response.body.ops[0].scheme[0].old_obj_id).toBe(newFolder2);
    expect(response.body.ops[0].scheme[0].obj_type).toBe('folder');
    SchemaValidator.validate(Upload_info, response.body);
  });

  test('should get Upload_info in_progress upload folder', async () => {
    const response = await api.requestGET(`${baseUrl}api/2/upload/info`, `hash=${hashUploadFolder}`);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].status).toBe('in_progress');
    SchemaValidator.validate(Upload_info, response.body);
    await new Promise(r => setTimeout(r, 5000));
  });

  test('should get Upload_info after upload folder', async () => {
    await new Promise(r => setTimeout(r, 12000));
    const response = await api.requestGET(`${baseUrl}api/2/upload/info`, `hash=${hashUploadFolder}`);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].status).toBe('success');
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'folder').title).toEqual('5');
    expect(response.body.ops[0].scheme[0].obj_type).toBe('conv');
    SchemaValidator.validate(Upload_info, response.body);
  });

  test('should get Upload_info after upload conv in stage', async () => {
    const { stdout, stderr } = await exec(scriptPath1, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        COMPANY_ID: company_id,
        OBJ_TO_ID: `${newStage}`,
        FILE_NAME: 'upload_conv_with_alias_another_env.zip',
        ASYNC: 'true',
        SKIP_ROOT: 'false',
        OBJ_TO_TYPE: 'stage',
        WITH_ALIAS: 'true',
        REWRITE_ALIAS: 'false',
      },
    });
    expect(stderr).toBe(``);
    await addMsg({ message: JSON.stringify(stdout), context: '' });
    expect(stdout).toContain(`{"request_proc":"ok","ops":[{"obj":"obj_scheme","proc":"ok","hash":`);
    hashUploadConv = stdout.substr(76, 146);

    await new Promise(r => setTimeout(r, 2000));
    const response = await api.requestGET(`${baseUrl}api/2/upload/info`, `hash=${hashUploadConv}`);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].status).toBe('success');
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'conv').title).toEqual('alias2');
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'alias').title).toEqual('alias1');
    SchemaValidator.validate(Upload_info, response.body);
  });

  test('should get Upload_info after upload in stage with error', async () => {
    const { stdout, stderr } = await exec(scriptPath1, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        COMPANY_ID: company_id,
        OBJ_TO_ID: `${newStage}`,
        FILE_NAME: 'folder_with_alias.zip',
        ASYNC: 'true',
        SKIP_ROOT: 'false',
        OBJ_TO_TYPE: 'stage',
        WITH_ALIAS: 'true',
        REWRITE_ALIAS: 'false',
      },
    });
    expect(stderr).toBe(``);
    await addMsg({ message: JSON.stringify(stdout), context: '' });
    expect(stdout).toContain(`{"request_proc":"ok","ops":[{"obj":"obj_scheme","proc":"ok","hash":`);
    hashUploadFolder = stdout.substr(76, 146);

    await new Promise(r => setTimeout(r, 2000));
    const response = await api.requestGET(`${baseUrl}api/2/upload/info`, `hash=${hashUploadFolder}`);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].status).toBe('error');
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'conv').title).toEqual(
      'New object',
    );
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'alias').title).toEqual(
      'test123',
    );
    expect(response.body.ops[0].errors[0].obj).toBe('folder');
    expect(response.body.ops[0].errors[0].count).toBe(1);
    SchemaValidator.validate(Upload_info_with_error, response.body);
  });

  test('should get Upload_info error upload', async () => {
    const { stdout, stderr } = await exec(scriptPath1, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        COMPANY_ID: company_id,
        OBJ_TO_ID: `${newStage}`,
        FILE_NAME: 'folder_with_errors.zip',
        ASYNC: 'true',
        SKIP_ROOT: 'false',
        OBJ_TO_TYPE: 'stage',
        WITH_ALIAS: 'true',
        REWRITE_ALIAS: 'false',
      },
    });
    expect(stderr).toBe(``);
    await addMsg({ message: JSON.stringify(stdout), context: '' });
    expect(stdout).toContain(`{"request_proc":"ok","ops":[{"obj":"obj_scheme","proc":"ok","hash":`);
    hashUploadFolder = stdout.substr(76, 146);

    await new Promise(r => setTimeout(r, 1000));
    const response = await api.requestGET(`${baseUrl}api/2/upload/info`, `hash=${hashUploadFolder}`);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].status).toBe('error');
    expect(response.body.ops[0].scheme).toBeEmpty;
    expect(response.body.ops[0].errors[0].obj).toBe('folder');
    expect(response.body.ops[0].errors[0].errors[0]).toBe(
      `Value is not valid. Value's byte_size is more than maximum allowed: 255`,
    );
    SchemaValidator.validate(Upload_info_error, response.body);
  });

  test('should get Upload_info after merge (all objects: conv/folder/dash/inst/alias)', async () => {
    const responseFolder = await requestCreateObj(api, OBJ_TYPE.FOLDER, company_id, `Folder_merge`, newStageDev);
    newFolder2 = responseFolder.body.ops[0].obj_id;

    const responseConv = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Conv_merge`, newStageDev);
    newConv = responseConv.body.ops[0].obj_id;

    const responseDash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `DashTitle-${Date.now()}`,
        project_id: newProject,
        stage_id: newStageDev,
        time_range: { select: 'online', start: 1712042841, stop: 1712042841, timezone_offset: -180 },
      }),
    );
    expect(responseDash.status).toBe(200);
    newDash = responseDash.body.ops[0].obj_id;

    const responseAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        short_name: `alias${Date.now()}`,
        title: `aliasTitle-${Date.now()}`,
        project_id: newProject,
        stage_id: newStageDev,
      }),
    );
    expect(responseAlias.status).toBe(200);
    newAlias = responseAlias.body.ops[0].obj_id;

    const responseInst = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        instance_type: `db_call`,
        folder_id: newStageDev,
        title: `Instance_${Date.now()}`,
        data: {
          driver: `postgres`,
          host: `COR-6938.middleware.loc`,
          port: `5432`,
          username: `postgres`,
          password: `pass`,
          ssl: false,
          database: `dbcall`,
          timeoutMs: 30000,
          project_id: newProject,
          stage_id: newStageDev,
        },
      }),
    );
    expect(responseInst.status).toBe(200);
    expect(responseInst.body.ops[0].obj).toBe('instance');
    newInstance = responseInst.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 3000));

    const responseMerge = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,

        project_id: newProject,
        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id: newStageDev,
        apply_mode: true,
        async: true,
      }),
    );
    expect(responseMerge.status).toBe(200);
    expect(responseMerge.body.request_proc).toBe('ok');
    hashMerge = responseMerge.body.ops[0].hash;

    await new Promise(r => setTimeout(r, 5000));

    const response = await api.requestGET(`${baseUrl}api/2/upload/info`, `hash=${hashMerge}`);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].status).toBe('success');
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'conv').old_obj_id).toEqual(
      newConv,
    );
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'folder').old_obj_id).toEqual(
      newFolder2,
    );
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'dashboard').old_obj_id).toEqual(
      newDash,
    );
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'instance').old_obj_id).toEqual(
      newInstance,
    );
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'alias').old_obj_id).toEqual(
      newAlias,
    );
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'conv').title).toEqual(
      'Conv_merge',
    );
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'folder').title).toEqual(
      'Folder_merge',
    );
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'alias').title).toContain(
      'aliasTitle-',
    );
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'instance').title).toContain(
      'Instance_',
    );
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'dashboard').title).toContain(
      'DashTitle-',
    );

    SchemaValidator.validate(Upload_info, response.body);
  });

  test('should get Upload_info after repeated merge (without changes all objects)', async () => {
    const responseMerge = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,

        project_id: newProject,
        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id: newStageDev,
        apply_mode: true,
        async: true,
      }),
    );
    expect(responseMerge.status).toBe(200);
    expect(responseMerge.body.request_proc).toBe('ok');
    hashMerge = responseMerge.body.ops[0].hash;

    await new Promise(r => setTimeout(r, 5000));

    const response = await api.requestGET(`${baseUrl}api/2/upload/info`, `hash=${hashMerge}`);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].status).toBe('success');
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'conv').old_obj_id).toEqual(
      newConv,
    );
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'folder').old_obj_id).toEqual(
      newFolder2,
    );
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'dashboard').old_obj_id).toEqual(
      newDash,
    );
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'instance').old_obj_id).toEqual(
      newInstance,
    );
    expect((response.body.ops[0].scheme as Array<any>).find(item => item.obj_type === 'alias').old_obj_id).toEqual(
      newAlias,
    );
    SchemaValidator.validate(Upload_info, response.body);
  });

  test('should create request get user_info by cookie', async () => {
    user = await application.getAuthorizedUser({ company: {} }, 0);
    cookieUserOwner = user.cookieUser;

    const responseFolder = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        title: 'folder',
        company_id,
      }),
    );
    expect(responseFolder.status).toBe(200);
    newFolder2 = responseFolder.body.ops[0].obj_id;

    const responseCopyFolder = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_type: 'folder',
        obj_id: newFolder2,
        title: 'folderCopy',
        folder_id: 0,
        ignore_errors: true,
        async: true,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(responseCopyFolder.status).toBe(200);
    expect(responseCopyFolder.body.ops[0].obj).toBe('obj_copy');
    hashCopy = responseCopyFolder.body.ops[0].hash;

    await new Promise(r => setTimeout(r, 1000));

    const cookieUserTemp = createAuthUser(cookieUserOwner, 'cookie');
    const responseInfo = await cookieUserTemp.request({
      method: Method.GET,
      url: `${baseUrl}api/2/upload/info?hash=${hashCopy}`,
    });
    expect(responseInfo.status).toBe(200);
    expect(responseInfo.status).toBe(200);
    expect(responseInfo.data.ops[0].proc).toBe('ok');
    expect(responseInfo.data.ops[0].status).toBe('success');
    expect(responseInfo.data.ops[0].scheme[0].old_obj_id).toBe(newFolder2);
    expect(responseInfo.data.ops[0].scheme[0].obj_type).toBe('folder');
    SchemaValidator.validate(Upload_info, responseInfo.data);
  });

  afterAll(async () => {
    const responseDelFolder = await requestDeleteObj(api, OBJ_TYPE.FOLDER, newFolder, company_id);
    expect(responseDelFolder.status).toBe(200);

    const responseDel = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder2,
      }),
    );
    expect(responseDel.status).toBe(200);

    const responseDelProject = await requestDeleteObj(api, OBJ_TYPE.FOLDER, newProject, company_id);
    expect(responseDelProject.status).toBe(200);
  });
});
