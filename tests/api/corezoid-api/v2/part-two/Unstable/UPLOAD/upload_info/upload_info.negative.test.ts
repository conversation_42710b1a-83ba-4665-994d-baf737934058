import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../../../../../../../infrastructure/model/ApiKey';
import { axiosInstance } from '../../../../../../../../application/api/AxiosClient';
import { ConfigurationManager } from '../../../../../../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../../application/api/ApiObj';
import {
  stringTestCases,
  stringNotValidTestCases,
  undefinedTestCase,
  securityTestCases,
  maxLength,
} from '../../../../../../negativeCases';

describe('Upload_info (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newFolder: string | number;
  let newFolder2: string | number;
  let company_id: any;
  let hash: string | number;
  const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseFolder = await requestCreateObj(api, OBJ_TYPE.FOLDER, company_id, `Folder_${Date.now()}`, 0);
    newFolder = responseFolder.body.ops[0].obj_id;

    const responseFolder2 = await requestCreateObj(api, OBJ_TYPE.FOLDER, company_id, `Folder_${Date.now()}`, newFolder);
    newFolder2 = responseFolder2.body.ops[0].obj_id;

    const responseCopyFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolder2,

        folder_id: newFolder,
        obj_type: 'folder',
        title: 'copyFolderAsync',
        ignore_errors: true,
        async: true,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(responseCopyFolder.status).toBe(200);
    expect(responseCopyFolder.body.ops[0].obj).toBe('obj_copy');
    hash = responseCopyFolder.body.ops[0].hash;
  });

  test.each(
    [
      ...stringTestCases,
      ...undefinedTestCase,
      ...stringNotValidTestCases,
      ...securityTestCases,
      ...maxLength,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't get upload_info with invalid hash '%s'`, async input => {
    await new Promise(r => setTimeout(r, 1000));

    const response = await api.requestGET(`${baseUrl}api/2/upload/info`, `hash=${input}`);
    expect(response.status).toBe(400);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual('not_found');
  });

  test(`'shouldn't get upload_info with invalid cookie '%s'`, async () => {
    const responseInfo = await axiosInstance({
      method: 'GET',
      url: `${baseUrl}api/2/upload/info?hash=${hash}`,
      data: {},
      headers: {
        Cookie: 'test',
        Origin: baseUrl,
      },
    });
    expect(responseInfo.status).toBe(403);
    expect(responseInfo.data.ops[0].proc).toBe('error');
    expect(responseInfo.data.ops[0].description).toBe('cookie or headers are not valid');
  });

  afterAll(async () => {
    const responseDelFolder = await requestDeleteObj(api, OBJ_TYPE.FOLDER, newFolder, company_id);
    expect(responseDelFolder.status).toBe(200);
  });
});
