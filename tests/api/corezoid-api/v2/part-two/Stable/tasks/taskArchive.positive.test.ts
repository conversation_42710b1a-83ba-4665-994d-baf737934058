import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import listConvSchema from '../../../../schemas/v2/actions-objects/listConv.schema.json';
import listNodeSchema from '../../../../schemas/v2/tasks/listNodeSchema.json';
import showStatSchema from '../../../../schemas/v2/tasks/showStatNodeSchema.json';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import axios, { AxiosRequestConfig, Method } from 'axios';

describe('TaskArchive (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: string | number;
  let taskRef: string;
  let taskObjId: string;
  let nodeIdFinal: string;
  let company_id: any;
  const endUnix = Math.round(+new Date() / 1000 + 100);
  const startUnix = endUnix - 1200;
  let baseUrl: string | number;

  baseUrl = ConfigurationManager.getConfiguration().getUrl();

  async function makeRequest(method: Method, url: string): Promise<void> {
    const config: AxiosRequestConfig = {
      method,
      url,
    };

    const res = await axios(config);
    baseUrl = res.data.web_settings.host.site;
  }

  makeRequest('get', `${baseUrl}system/conf`);

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: 0,
        conv_type: 'process',
      }),
    );
    newConv = responseConv.body.ops[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        conv_id: newConv,
        data: {
          param: 'value',
          param2: 'val|ue2',
          param3: 'value3,;',
          param4: 12345,
          param5: true,
          param6: { key6: 'value6' },
          param7: { 'ke|y6': 'value6,;' },
          param8: [12345, true, 54321],
          param9: ['test,;', 'te|st2'],
          param10: [{ key10: 'valu|e10' }, { 'key|10_2': 'value10_2' }],
          param11: [{ key11: [12312, 3456, 4545] }, { key11_2: [{ key: 'valu|e' }, { key2: 123456 }] }],
        },
        ref: `ref_${Date.now()}`,
      }),
    );

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: newConv,
        data: { a: '1' },
        ref: `ref_${Date.now()}`,
      }),
    );
    taskRef = responseTask.body.ops[0].ref;
    taskObjId = responseTask.body.ops[0].obj_id;
  });

  test('should list conv', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
        get_counter: true,
      }),
    );
    nodeIdFinal = response.body.ops[0].list[2].obj_id;
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toEqual(newConv);
    expect(response.body.ops[0].list[0].title).toEqual('start');
    expect(response.body.ops[0].size).toEqual(3);
    SchemaValidator.validate(listConvSchema, response.body);
  });

  test('should list node', async () => {
    await new Promise(r => setTimeout(r, 4500));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeIdFinal,
        company_id,
        conv_id: newConv,
        get_counter: true,
        limit: 10,
        offset: 0,
      }),
    );
    expect(response.status).toBe(200);
    const ops = response.body.ops[0];
    expect(ops.obj_id).toEqual(nodeIdFinal);
    expect(ops.count).toEqual(2);
    const foundItem = (ops.list as Array<any>).find(item => item.data && item.data.a === '1');
    expect(foundItem.obj_id).toEqual(taskObjId);
    expect(foundItem.ref).toEqual(taskRef);
    SchemaValidator.validate(listNodeSchema, response.body);
  });

  test('should show stat by node_id conv_id number', async () => {
    await new Promise(r => setTimeout(r, 200000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAT,
        company_id,

        conv_id: newConv,
        node_id: nodeIdFinal,
        interval: 'minute',
        group: 'time',
        start: startUnix,
        end: endUnix,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(showStatSchema, response.body);
    expect(response.body.ops[0].node_id).toEqual(nodeIdFinal);
    expect(response.body.ops[0].conv_id).toEqual(newConv);
    expect(response.body.ops[0].data[0].in).toEqual(2);
  });

  test('should show stat by node_id conv_id string', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAT,
        company_id,

        conv_id: `${newConv}`,
        node_id: nodeIdFinal,
        interval: 'minute',
        group: 'time',
        start: startUnix,
        end: endUnix,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(showStatSchema, response.body);
    expect(response.body.ops[0].node_id).toEqual(nodeIdFinal);
    expect(response.body.ops[0].conv_id).toEqual(newConv);
    expect(response.body.ops[0].data[0].in).toEqual(2);
  });

  test('should list node filter ref', async () => {
    let taskforConv;
    for (taskforConv = 0; taskforConv < 3; taskforConv++) {
      await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          company_id,
          conv_id: newConv,
          data: { a: '1' },
          ref: `test`,
        }),
      );
    }

    await new Promise(r => setTimeout(r, 3000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeIdFinal,
        company_id,

        conv_id: newConv,
        get_counter: true,
        limit: 10,
        offset: 0,
        filters: [
          {
            name: 'ref',
            value: 'test',
            fun: 'eq',
          },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toEqual(nodeIdFinal);
    expect(response.body.ops[0].count).toEqual(5);
    expect(response.body.ops[0].list).toBeArrayOfSize(3);
    expect(response.body.ops[0].list[0].ref).toEqual('test');
    expect(response.body.ops[0].list[1].ref).toEqual('test');
    expect(response.body.ops[0].list[2].ref).toEqual('test');
    expect(response.body.ops[0].list[0].data).toEqual({ a: '1' });
    SchemaValidator.validate(listNodeSchema, response.body);
  });

  test('should list node data {}', async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: newConv,
        data: {},
        ref: `data`,
      }),
    );

    await new Promise(r => setTimeout(r, 3000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeIdFinal,
        company_id,

        conv_id: newConv,
        limit: 10,
        offset: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toEqual(nodeIdFinal);
    expect(response.body.ops[0].count).toEqual(6);
    expect(response.body.ops[0].list).toBeArrayOfSize(6);
    expect(response.body.ops[0].list[0].data).toEqual({});
    SchemaValidator.validate(listNodeSchema, response.body);
  });

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
  });
});
