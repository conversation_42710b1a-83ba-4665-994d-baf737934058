import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import axios, { AxiosRequestConfig, Method } from 'axios';
import { stringTestCases, maxLength, minLength, boolTestCases } from '../../../../../negativeCases';

describe('TaskArchive (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: string | number;
  let newNode: string;
  let company_id: any;
  const endUnix = Math.round(+new Date() / 1000);
  const startUnix = endUnix - 600;
  let baseUrl: string | number;

  baseUrl = ConfigurationManager.getConfiguration().getUrl();

  async function makeRequest(method: Method, url: string): Promise<void> {
    const config: AxiosRequestConfig = {
      method,
      url,
    };

    const res = await axios(config);
    baseUrl = res.data.web_settings.host.site;
  }

  makeRequest('get', `${baseUrl}system/conf`);

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: 0,
        conv_type: 'process',
      }),
    );
    newConv = responseConv.body.ops[0].obj_id;

    const responseListConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
    newNode = responseListConv.body.ops[0].list[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: newConv,
        data: { a: '1' },
        ref: `ref_${Date.now()}`,
      }),
    );
  });

  test.each([
    [null, 'Value is not valid'],
    [true, 'Value is not valid'],
    ['test', 'Value is not valid'],
    [{}, 'Value is not valid'],
    [[], 'Value is not valid'],
    [undefined, `Key 'obj_id' is required`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [1, `Object's company ID does not match company ID in the request`],
    [0, `Object conv with id 0 does not exist`],
  ])(`shouldn't list conv in conv with invalid conv_id '%s'`, async (param, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: param,
        company_id,
        get_counter: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([1, 0, -1, null, 'test', {}, []])(
    `shouldn't list conv in conv with invalid get_counter '%s'`,
    async param => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.CONV,
          obj_id: newConv,
          company_id,
          get_counter: param,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(`Value is not valid`);
    },
  );

  test.each([1, 0, -1, true, {}, []])(`shouldn't list conv in conv with invalid company_id '%s'`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id: param,
        get_counter: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(`Value is not valid`);
  });

  test(`shouldn't list conv in conv with invalid company_id 'test'`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id: 'test',
        get_counter: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(`Company test does not exists`);
  });

  test.each([
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    ['test', `Value is not valid. Value's byte_size is less than minimum allowed: 24`],
    [undefined, `Key 'obj_id' is required`],
  ])(`shouldn't list node in conv with invalid obj_id '%s'`, async (param, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: param,
        company_id: 'test',
        conv_id: newConv,
        limit: 10,
        offset: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, 'Wrong object reference. Validation error'],
    ['test', 'Wrong object reference. Validation error'],
    [true, 'Wrong object reference. Validation error'],
    [{}, 'Wrong object reference. Validation error'],
    [[], 'Wrong object reference. Validation error'],
    [undefined, 'Wrong object reference. Validation error'],
    [1, `Object's company ID does not match company ID in the request`],
    [-1, 'Object conv with id -1 does not exist'],
    [0, 'Object conv with id 0 does not exist'],
  ])(`shouldn't list node in conv with invalid conv_id '%s'`, async (param, reason) => {
    const request = createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: newNode,
      company_id,
      conv_id: param,
      limit: 10,
      offset: 0,
    });
    const response = await api.request(request);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [1, 'Value is not valid'],
    [0, 'Value is not valid'],
    [-1, 'Value is not valid'],
    [true, 'Value is not valid'],
    [{}, 'Value is not valid'],
    [[], 'Value is not valid'],
    ['test', 'Company test does not exists'],
  ])(`shouldn't list node in conv with invalid company_id '%s'`, async (param, reason) => {
    const request = await createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: newNode,
      company_id: param,
      conv_id: newConv,
      limit: 10,
      offset: 0,
    });
    const response = await api.request(request);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each(['test', true, {}, []])(`shouldn't list node in conv with invalid limit '%s'`, async param => {
    const request = createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: newNode,
      company_id,
      conv_id: newConv,
      limit: param,
      offset: 0,
    });
    const response = await api.request(request);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(`Value is not valid`);
  });

  test.each([-1])(`shouldn't list node in conv with invalid limit '%s'`, async param => {
    const request = createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: newNode,
      company_id,
      conv_id: newConv,
      limit: param,
      offset: 0,
    });
    const response = await api.request(request);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(
      `Value is not valid. Value's limit is less than minimum allowed: 0`,
    );
  });

  test.each(['test', true, {}, []])(`shouldn't list node in conv with invalid offset '%s'`, async param => {
    const request = createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: newNode,
      company_id,
      conv_id: newConv,
      limit: 10,
      offset: param,
    });
    const response = await api.request(request);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(`Value is not valid`);
  });

  test.each([-1])(`shouldn't list node in conv with invalid offset '%s'`, async param => {
    const request = createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: newNode,
      company_id,
      conv_id: newConv,
      limit: 10,
      offset: param,
    });
    const response = await api.request(request);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(
      `Value is not valid. Value's limit is less than minimum allowed: 0`,
    );
  });

  test.each(['test', true, {}, -1, 1, '', null])(
    `shouldn't list node in conv with invalid filters '%s'`,
    async filters => {
      const request = createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: newNode,
        company_id,
        conv_id: newConv,
        limit: 10,
        offset: 0,
        filters,
      });
      const response = await api.request(request);
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(`Value is not valid`);
    },
  );

  test.each([
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    ['test', `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ref\\\">>]\">>`],
    ['', `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"ref\\\">>]\">>`],
    [undefined, `Key 'filters.name' is required`],
  ])(`shouldn't list node in conv with invalid name '%s'`, async (name, res) => {
    const request = createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: newNode,
      company_id,
      conv_id: newConv,
      limit: 10,
      offset: 0,
      filters: [{ name, value: 'test', fun: 'eq' }],
    });
    const response = await api.request(request);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(res);
  });

  test.each([
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 1`],
    [undefined, `Key 'filters.value' is required`],
  ])(`shouldn't list node in conv with invalid value '%s'`, async (value, res) => {
    const request = createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: newNode,
      company_id,
      conv_id: newConv,
      limit: 10,
      offset: 0,
      filters: [{ name: 'ref', value, fun: 'eq' }],
    });
    const response = await api.request(request);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(res);
  });

  test.each([
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    ['test', `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"eq\\\">>]\">>`],
    ['', `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"eq\\\">>]\">>`],
    [undefined, `Key 'filters.fun' is required`],
  ])(`shouldn't list node in conv with invalid fun '%s'`, async (fun, res) => {
    const request = createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: newNode,
      company_id,
      conv_id: newConv,
      limit: 10,
      offset: 0,
      filters: [{ name: 'ref', value: 'test', fun }],
    });
    const response = await api.request(request);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(res);
  });

  test(`shouldn't list node in conv without limit`, async () => {
    const request = createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: newNode,
      company_id,
      conv_id: newConv,
    });
    const response = await api.request(request);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(`Key 'limit' is required`);
  });

  test.each([
    [1, 'User has no rights'],
    [0, 'Object conv with id 0 does not exist'],
    [-1, 'Object conv with id -1 does not exist'],
    [true, 'conv_id has not valid type, expected types [integer, binary_integer]'],
    [{}, 'conv_id has not valid type, expected types [integer, binary_integer]'],
    [[], 'conv_id has not valid type, expected types [integer, binary_integer]'],
    ['test', 'conv_id has not valid type, expected types [integer, binary_integer]'],
    [undefined, 'No dashboard_id neither conv_id not set'],
  ])(`shouldn't show stat node with invalid conv_id '%s'`, async (conv_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAT,
        company_id,

        conv_id,
        node_id: newNode,
        interval: 'minute',
        group: 'time',
        start: startUnix,
        end: endUnix,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [1, 'node_id has not valid type, expected types [binary]'],
    [0, 'node_id has not valid type, expected types [binary]'],
    [-1, 'node_id has not valid type, expected types [binary]'],
    [true, 'node_id has not valid type, expected types [binary]'],
    [{}, 'node_id has not valid type, expected types [binary]'],
    [[], 'node_id has not valid type, expected types [binary]'],
    ['test', 'node_id less than minimum allowed size (range) 24'],
    [undefined, 'node not found'],
  ])(`shouldn't show stat node with invalid node_id '%s'`, async (node_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAT,
        company_id,

        conv_id: newConv,
        node_id,
        interval: 'minute',
        group: 'time',
        start: startUnix,
        end: endUnix,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [1, 'interval does not coincide with one of the expected values [hour, minute, day]'],
    [0, 'interval does not coincide with one of the expected values [hour, minute, day]'],
    [-1, 'interval does not coincide with one of the expected values [hour, minute, day]'],
    [true, 'interval does not coincide with one of the expected values [hour, minute, day]'],
    [{}, 'interval does not coincide with one of the expected values [hour, minute, day]'],
    [[], 'interval does not coincide with one of the expected values [hour, minute, day]'],
    ['test', 'interval does not coincide with one of the expected values [hour, minute, day]'],
    [undefined, 'interval field is missing'],
  ])(`shouldn't show stat node with invalid interval '%s'`, async (interval, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAT,
        company_id,

        conv_id: newConv,
        node_id: newNode,
        interval,
        group: 'time',
        start: startUnix,
        end: endUnix,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [1, 'group does not coincide with one of the expected values [all, time]'],
    [0, 'group does not coincide with one of the expected values [all, time]'],
    [-1, 'group does not coincide with one of the expected values [all, time]'],
    [true, 'group does not coincide with one of the expected values [all, time]'],
    [{}, 'group does not coincide with one of the expected values [all, time]'],
    [[], 'group does not coincide with one of the expected values [all, time]'],
    ['test', 'group does not coincide with one of the expected values [all, time]'],
    [undefined, 'group field is missing'],
  ])(`shouldn't show stat node with invalid group '%s'`, async (group, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAT,
        company_id,

        conv_id: newConv,
        node_id: newNode,
        interval: 'minute',
        group,
        start: startUnix,
        end: endUnix,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, 'start has not valid type, expected types [integer, binary_integer]'],
    [null, 'start has not valid type, expected types [integer, binary_integer]'],
    [{}, 'start has not valid type, expected types [integer, binary_integer]'],
    [[], 'start has not valid type, expected types [integer, binary_integer]'],
    ['test', 'start has not valid type, expected types [integer, binary_integer]'],
    [undefined, 'start field is missing'],
  ])(`shouldn't show stat node with invalid start '%s'`, async (start, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAT,
        company_id,

        conv_id: newConv,
        node_id: newNode,
        interval: 'minute',
        group: 'time',
        start,
        end: endUnix,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, 'end has not valid type, expected types [integer, binary_integer]'],
    [null, 'end has not valid type, expected types [integer, binary_integer]'],
    [{}, 'end has not valid type, expected types [integer, binary_integer]'],
    [[], 'end has not valid type, expected types [integer, binary_integer]'],
    ['test', 'end has not valid type, expected types [integer, binary_integer]'],
    [undefined, 'end field is missing'],
  ])(`shouldn't show stat node with invalid end '%s'`, async (end, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAT,
        company_id,

        conv_id: newConv,
        node_id: newNode,
        interval: 'hour',
        group: 'time',
        start: startUnix,
        end,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  const valuesToSkip: any = [0, 1, -1];

  test.each(
    [...stringTestCases, ...minLength, ...maxLength]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't check instance with invalid timezone_offset '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAT,
        company_id,
        conv_id: newConv,
        node_id: newNode,
        start: startUnix,
        end: endUnix,
        interval: 'minute',
        group: 'time',
        timezone_offset: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...boolTestCases, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't check instance with invalid timestamp '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.STAT,
          company_id,
          conv_id: newConv,
          node_id: newNode,
          start: startUnix,
          end: endUnix,
          interval: 'minute',
          group: 'time',
          timestamp: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id: apikey.companies[0].id,
      }),
    );
  });
});
