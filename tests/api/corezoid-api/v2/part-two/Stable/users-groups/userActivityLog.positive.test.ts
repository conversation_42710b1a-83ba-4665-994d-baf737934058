import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import userLog from '../../../../schemas/v2/users-groups/userActivityLogSchema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('List history (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  const endUnix = Math.round(+new Date() / 1000);
  const startUnix = endUnix - 864000;

  beforeAll(async () => {
    apikey = await application.getApiKeySuper();
    api = application.getApiKeyClient(apikey);
  });

  test(`should show list history user`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id: null,

        list_type: 'delete',
        list_obj: 'user',
        start: startUnix,
        end: endUnix,
        limit: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    SchemaValidator.validate(userLog, response.body);
  });
});
