import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import { application } from '../../../../../../../application/Application';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import sharedSchema from '../../../../schemas/v2/users-groups/sharedGroupSchema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('Shared groups (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let objIdNewApiKey: number;
  let groupId: number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
  });

  test('should get list of shared groups', async () => {
    const CreateKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(200);
    objIdNewApiKey = CreateKeyResponse.body.ops[0].users[0].obj_id;
    const user = CreateKeyResponse.body;

    const CreateGroupResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        title: `ApiKey_${Date.now()}`,
        obj_type: 'admins',
      }),
    );
    expect(CreateGroupResponse.status).toBe(200);
    groupId = CreateGroupResponse.body.ops[0].obj_id;

    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    const newApi = application.getApiKeyClient(newApiKey);

    const linkUserResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: newApiKey.id,
        company_id,
        group_id: groupId,
        level: 1,
      }),
    );
    expect(linkUserResponse.status).toBe(200);

    const ListGroupsResponse = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        filter: 'shared',
      }),
    );
    expect(ListGroupsResponse.status).toBe(200);
    expect(ListGroupsResponse.body.ops[0].list[0].obj_id).toBe(groupId);
    expect(ListGroupsResponse.body.ops[0].list[0].owner_id).toBe(+apikey.id);
    SchemaValidator.validate(sharedSchema, ListGroupsResponse.body);
  });

  afterAll(async () => {
    const responseUser = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: objIdNewApiKey,
        company_id,
      }),
    );
    expect(responseUser.status).toBe(200);

    const responseGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupId,
        company_id,
      }),
    );
    expect(responseGroup.status).toBe(200);
  });
});
