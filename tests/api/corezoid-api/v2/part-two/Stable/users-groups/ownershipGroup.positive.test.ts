import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import modifyOwnerSchema from '../../../../schemas/v2/users-groups/modifyOwnerGroupSchema.json';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('Group (positive)', () => {
  let api: ApiKeyClient;
  let apiS: ApiKeyClient;
  let apikey: ApiKey;
  let apiSuper: ApiKey;
  let company_id: any;
  let newKeyObjId: string | number;
  let newKey2ObjId: string | number;
  let newGroup: string | number;
  let newGroup2: string | number;
  let conv: string | number;
  let folder: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    apiSuper = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apiSuper);

    const responseKey1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: `key_${Date.now()}`,
        logins: [{ type: 'api' }],
      }),
    );
    expect(responseKey1.status).toBe(200);
    expect(responseKey1.body.ops[0].proc).toEqual('ok');
    newKeyObjId = responseKey1.body.ops[0].users[0].obj_id;

    const responseKey2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: `key_${Date.now()}`,
        logins: [{ type: 'api' }],
      }),
    );
    expect(responseKey2.status).toBe(200);
    expect(responseKey2.body.ops[0].proc).toEqual('ok');
    newKey2ObjId = responseKey2.body.ops[0].users[0].obj_id;

    const responseGroup1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        title: `Group_${Date.now()}`,
        obj_type: 'admins',
      }),
    );
    newGroup = responseGroup1.body.ops[0].obj_id;

    const responseGroup2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        title: `Group_${Date.now()}`,
        obj_type: 'admins',
      }),
    );
    newGroup2 = responseGroup2.body.ops[0].obj_id;

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: 0,
        conv_type: 'process',
      }),
    );
    conv = responseConv.body.ops[0].obj_id;

    const responseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        title: `${OBJ_TYPE.FOLDER}_${Date.now()}`,
        folder_id: 0,
      }),
    );
    folder = responseFolder.body.ops[0].obj_id;
  });

  test('should modify owner group save_src_privs:true', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        company_id,
        obj_id: newGroup,
        save_src_privs: true,
        obj_to_id: newKeyObjId,
        obj_type: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newKeyObjId);
    SchemaValidator.validate(modifyOwnerSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_id: newGroup,
        list_obj: 'user',
        sort: 'title',
        order: 'asc',
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.request_proc).toBe('ok');
    expect(responseList.body.ops[0].proc).toBe('ok');
    expect(responseList.body.ops[0].list[0].obj).toBe('user');
    expect(responseList.body.ops[0].list[0].title).toBe(apikey.title);
  });

  test(`shouldn't modify owner group save_src_privs:true repeatedly`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        company_id,
        obj_id: newGroup,
        save_src_privs: true,
        obj_to_id: newKeyObjId,
        obj_type: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe(`Couldn't share object to it's owner`);
  });

  test('should modify owner group save_src_privs:true superadmin', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        company_id,
        obj_id: newGroup,
        save_src_privs: true,
        obj_to_id: newKey2ObjId,
        obj_type: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newKey2ObjId);
    SchemaValidator.validate(modifyOwnerSchema, response.body);
  });

  test(`shouldn't modify owner group not owner`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        company_id,
        obj_id: newGroup,
        save_src_privs: true,
        obj_to_id: newKeyObjId,
        obj_type: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Access denied. Reason: not owner.');
  });

  test('should modify owner group save_src_privs:false', async () => {
    const responseShareFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: folder,

        privs: [
          { type: 'create', list_obj: ['all'] },
          { type: 'modify', list_obj: ['all'] },
          { type: 'delete', list_obj: ['all'] },
          { type: 'view', list_obj: ['all'] },
        ],
        obj_to_id: newGroup2,
        obj_to: 'group',
      }),
    );
    expect(responseShareFolder.status).toBe(200);
    expect(responseShareFolder.body.request_proc).toBe('ok');
    expect(responseShareFolder.body.ops[0].proc).toBe('ok');
    expect(responseShareFolder.body.ops[0].obj_type).toBe('folder');
    expect(responseShareFolder.body.ops[0].obj_to_type).toBe('group');
    expect(responseShareFolder.body.ops[0].action_type).toBe('link');

    const responseShareConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv,
        privs: [{ type: 'view', list_obj: ['all'] }],
        obj_to_id: newGroup2,
        obj_to: 'group',
      }),
    );
    expect(responseShareConv.status).toBe(200);
    expect(responseShareConv.body.request_proc).toBe('ok');
    expect(responseShareConv.body.ops[0].proc).toBe('ok');
    expect(responseShareConv.body.ops[0].obj_type).toBe('conv');
    expect(responseShareConv.body.ops[0].obj_to_type).toBe('group');
    expect(responseShareConv.body.ops[0].action_type).toBe('link');

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        company_id,
        obj_id: newGroup2,
        save_src_privs: false,
        obj_to_id: newKeyObjId,
        obj_type: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].owner_id).toBe(newKeyObjId);
    SchemaValidator.validate(modifyOwnerSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_id: newGroup2,
        list_obj: 'user',
        sort: 'title',
        order: 'asc',
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.request_proc).toBe('ok');
    expect(responseList.body.ops[0].proc).toBe('error');
    expect(responseList.body.ops[0].description).toBe('Access denied');

    const responseListS = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_id: newGroup2,
        list_obj: 'user',
        sort: 'title',
        order: 'asc',
      }),
    );
    expect(responseListS.status).toBe(200);
    expect(responseListS.body.request_proc).toBe('ok');
    expect(responseListS.body.ops[0].proc).toBe('ok');
    expect(responseListS.body.ops[0].list).toBeArrayOfSize(0);

    const responseListConvGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: conv,
        list_obj: 'group',
      }),
    );
    expect(responseListConvGroup.status).toBe(200);
    expect(responseListConvGroup.body.request_proc).toBe('ok');
    expect(responseListConvGroup.body.ops[0].proc).toBe('ok');
    expect(responseListConvGroup.body.ops[0].list[0].obj_id).toBe(newGroup2);

    const responseListFolderGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: folder,
        list_obj: 'group',
      }),
    );
    expect(responseListFolderGroup.status).toBe(200);
    expect(responseListFolderGroup.body.request_proc).toBe('ok');
    expect(responseListFolderGroup.body.ops[0].proc).toBe('ok');
    expect(responseListConvGroup.body.ops[0].list[0].obj_id).toBe(newGroup2);
  });

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        obj_id: newGroup,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        obj_id: newGroup2,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: newKeyObjId,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: newKey2ObjId,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folder,
        company_id,
      }),
    );
  });
});
