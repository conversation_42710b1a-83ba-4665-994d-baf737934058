import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('UsersCompany (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
  });

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'login' is required`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [12345, `Value '12345' is not valid. Type of value is not 'binary'`],
    ['test', `invalid email`],
    ['test.com', `invalid email`],
  ])(`shouldn't create invite, with 'login' %s`, async (login, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        company_id,

        login,
        login_type: 'corezoid',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([0, [], {}, true])(`shouldn't create invite, with 'company_id' %s`, async company => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        company_id: company,

        login: '<EMAIL>',
        login_type: 'corezoid',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(`Value is not valid`);
  });

  test(`shouldn't create invite, with 'company_id' "test"`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        company_id: 'test',

        login: '<EMAIL>',
        login_type: 'corezoid',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(`Access denied`);
  });
});
