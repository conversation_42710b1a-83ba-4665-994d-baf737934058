import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';

describe('Group (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let ApiKey2: string | number;
  let newGroup: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        title: `Group_${Date.now()}`,
        obj_type: 'admins',
      }),
    );
    newGroup = response.body.ops[0].obj_id;

    const createKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(createKeyResponse.status).toBe(200);
    const user = createKeyResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    ApiKey2 = +newApiKey.id;
  });

  test.each([
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [undefined, `Key 'obj_id' is required`],
    ['test', 'Value is not valid'],
    [0, `Old owner are invalid. Reason: not found (note that object may be deleted)`],
    [1234, `Couldn't find object owner. Object id 1234 type 7 is not found or destroyed`],
    [null, `Value is not valid`],
    ['', `Value is not valid`],
    [true, `Value is not valid`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
  ])(`shouldn't modify owner group with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        company_id,
        obj_id,
        save_src_privs: true,
        obj_to_id: ApiKey2,
        obj_type: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [[], `Value is not valid or Key 'obj_to_login' is required`],
    [{}, `Value is not valid or Key 'obj_to_login' is required`],
    [undefined, `Key 'obj_to_id' is required or Key 'obj_to_login' is required`],
    ['test', `Value is not valid or Key 'obj_to_login' is required`],
    [0, `User 0 not found.`],
    [1234, `New owner is not member of specified company.`],
    [null, `Value is not valid or Key 'obj_to_login' is required`],
    ['', `Value is not valid or Key 'obj_to_login' is required`],
    [true, `Value is not valid or Key 'obj_to_login' is required`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0 or Key 'obj_to_login' is required`],
  ])(`shouldn't modify owner group with invalid obj_to_id '%s'`, async (obj_to_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        company_id,
        obj_id: newGroup,
        save_src_privs: true,
        obj_to_id,
        obj_type: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [
      [],
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"alias\\\">>]\">>`,
    ],
    [
      {},
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"alias\\\">>]\">>`,
    ],
    [undefined, `Key 'obj_type' is required`],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"alias\\\">>]\">>`,
    ],
    [
      0,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"alias\\\">>]\">>`,
    ],
    [
      -1,
      `Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"alias\\\">>]\">>`,
    ],
    [
      1234,
      `Value '<<\"1234\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"alias\\\">>]\">>`,
    ],
    [
      null,
      `Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"alias\\\">>]\">>`,
    ],
    [
      true,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"alias\\\">>]\">>`,
    ],
    [
      '',
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"group\\\">>,<<\\\"instance\\\">>,\\n <<\\\"config\\\">>,<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"alias\\\">>]\">>`,
    ],
  ])(`shouldn't modify owner group with invalid obj_type '%s'`, async (obj_type, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        company_id,
        obj_id: newGroup,
        save_src_privs: true,
        obj_to_id: ApiKey2,
        obj_type,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [[], `Value '[]' is not valid. Type of value is not 'boolean'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'boolean'`],
    ['test', `Value 'test' is not valid. Type of value is not 'boolean'`],
    [0, `Value '0' is not valid. Type of value is not 'boolean'`],
    [1234, `Value '1234' is not valid. Type of value is not 'boolean'`],
    [null, `Value 'null' is not valid. Type of value is not 'boolean'`],
    ['', `Value '' is not valid. Type of value is not 'boolean'`],
  ])(`shouldn't modify owner group with invalid save_src_privs '%s'`, async (save_src_privs, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        company_id,
        obj_id: newGroup,
        save_src_privs,
        obj_to_id: ApiKey2,
        obj_type: 'group',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  afterAll(async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_id: newGroup,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
        company_id,
      }),
    );
  });
});
