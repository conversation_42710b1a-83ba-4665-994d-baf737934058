import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import listSchema from '../../../../schemas/v2/users-groups/listUserSchema.json';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createSchema from '../../../../schemas/v2/users-groups/createInviteSchema.json';
import createSchema2 from '../../../../schemas/v2/users-groups/createInviteSchema2.json';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { User } from '../../../../../../../infrastructure/model/User';
import { createAuthUser, Method } from '../../../../../../../utils/request';

describe('UsersCompany (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let user: User;
  let cookie: any;

  beforeAll(async () => {
    user = await application.getAuthorizedUser({ company: {} }, 1);
    cookie = createAuthUser(user.cookieUser, 'cookie');

    const data = {
      type: 'set',
      obj: 'value',
      id: 28,
      obj_type: 'integer',
      key: 'capi_user_notify_conv',
      value: null,
      description: 'Conveyor for sharing, invites, downloading tasks ot objects',
      is_required: false,
      allowed: [],
      tags: [],
    };
    await cookie.request({
      method: Method.POST,
      url: 'https://superadmin-pre.corezoid.com/superadmin/api/1/json',
      data: JSON.stringify(data),
    });
  });

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
  });

  test.each(['corezoid', 'google'])(
    `should create invite, with 'login_type' %s If the process ID is not entered in setting`,
    async type => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.INVITE,
          company_id,

          login: '<EMAIL>',
          login_type: type,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.request_proc).toBe('ok');
      expect(response.body.ops[0].obj).toBe('invite');
      const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
      expect(response.body.ops[0].url).toInclude(`${baseUrl}api/2/plugins/invite/`);
      SchemaValidator.validate(createSchema, response.body);
    },
  );

  test(`should create invite, if value of the user_notify_conv parameter is added`, async () => {
    const data = {
      type: 'set',
      obj: 'value',
      id: 28,
      obj_type: 'integer',
      key: 'capi_user_notify_conv',
      value: '14',
      description: 'Conveyor for sharing, invites, downloading tasks ot objects',
      is_required: false,
      allowed: [],
      tags: [],
    };
    await cookie.request({
      method: Method.POST,
      url: 'https://superadmin-pre.corezoid.com/superadmin/api/1/json',
      data: JSON.stringify(data),
    });

    const responseInvite = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INVITE,
        company_id,

        login: '<EMAIL>',
        login_type: 'corezoid',
      }),
    );
    expect(responseInvite.status).toBe(200);
    expect(responseInvite.body.request_proc).toBe('ok');
    expect(responseInvite.body.ops[0].obj).toBe('invite');
    expect(responseInvite.body.ops[0]).not.toContainKey(`url`);
    SchemaValidator.validate(createSchema2, responseInvite.body);
  });

  test('should get list of users', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,

        filter: 'user',
        sort: 'title',
        order: 'asc',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('company_users');
    expect(response.body.ops[0].list).toBeArray;
    SchemaValidator.validate(listSchema, response.body);
  });
});
