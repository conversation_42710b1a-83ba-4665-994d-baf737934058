import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('List history (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  const endUnix = Math.round(+new Date() / 1000);
  const startUnix = endUnix - 864000;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKeySuper();
    api = application.getApiKeyClient(apikey);
  });

  test.each([
    [true, 'Value is not valid'],
    [[], 'Value is not valid'],
    [1234, `Value is not valid`],
    [{}, `Value is not valid`],
    [1, `Value is not valid`],
    [-1, `Value is not valid`],
    [0, `Value is not valid`],
  ])(`shouldn't list history with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_type: 'delete',
        list_obj: 'user',
        start: startUnix,
        end: endUnix,
        limit: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't list history with invalid list_type '%s'`, async (list_type, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_type,
        list_obj: 'user',
        start: startUnix,
        end: endUnix,
        limit: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't list history with invalid list_obj '%s'`, async (list_obj, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_type: 'delete',
        list_obj,
        start: startUnix,
        end: endUnix,
        limit: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value is not valid`],
    [[], `Value is not valid`],
    ['1234', `Time between start and end cannot be more than 6 month`],
    [{}, `Value is not valid`],
    [1, `Time between start and end cannot be more than 6 month`],
    [-1, `Time between start and end cannot be more than 6 month`],
    [0, `Time between start and end cannot be more than 6 month`],
    ['', `Value is not valid`],
    ['test', `Value is not valid`],
    [undefined, `obj_id field is missing`],
  ])(`shouldn't list history with invalid start '%s'`, async (start, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_type: 'delete',
        list_obj: 'user',
        start,
        end: endUnix,
        limit: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value is not valid`],
    [[], `Value is not valid`],
    ['1234', `Time between start and end cannot be more than 6 month`],
    [{}, `Value is not valid`],
    [1, `Time between start and end cannot be more than 6 month`],
    [-1, `Time between start and end cannot be more than 6 month`],
    [0, `Time between start and end cannot be more than 6 month`],
    ['', `Value is not valid`],
    ['test', `Value is not valid`],
    [undefined, `obj_id field is missing`],
  ])(`shouldn't list history with invalid start '%s'`, async (start, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_type: 'delete',
        list_obj: 'user',
        start,
        end: endUnix,
        limit: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value is not valid`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
    ['', `Value is not valid`],
    ['test', `Value is not valid`],
  ])(`shouldn't list history with invalid end '%s'`, async (end, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_type: 'delete',
        list_obj: 'user',
        start: startUnix,
        end,
        limit: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, `Value is not valid`],
    [[], `Value is not valid`],
    ['1234', `Value is not valid. Value's limit is more than maximum allowed: 100`],
    [{}, `Value is not valid`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 1`],
    [0, `Value is not valid. Value's limit is less than minimum allowed: 1`],
    ['', `Value is not valid`],
    ['test', `Value is not valid`],
  ])(`shouldn't list history with invalid limit '%s'`, async (limit, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_type: 'delete',
        list_obj: 'user',
        start: startUnix,
        end: endUnix,
        limit,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });
});
