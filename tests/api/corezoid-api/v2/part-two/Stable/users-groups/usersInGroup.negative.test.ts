import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { CreateApiKeyResponse } from '../../../../../../../application/api/usergroup/modesl/apikey.model';
import { CreateGroupResponse } from '../../../../../../../application/api/usergroup/modesl/group.model';
import { application } from '../../../../../../../application/Application';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('Users in users group (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let group: CreateGroupResponse;
  let user: CreateApiKeyResponse;
  let company_id: any;
  let objIdNewApiKey: number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies.length ? apikey.companies[0].id : null;
    const createGroupResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_type: 'admins',
        title: `ApiKey_${Date.now()}`,
      }),
    );
    group = createGroupResponse.body.ops[0].obj_id;

    const createUserResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        logins: [{ type: 'api' }],
        title: 'API',
      }),
    );
    user = createUserResponse.body;
    objIdNewApiKey = createUserResponse.body.ops[0].users[0].obj_id;
  });

  test.each([
    [undefined, `Key 'obj_id' is required`],
    ['0', 'User not in company'],
    [0, 'User not in company'],
  ])(`shouldn't add user with invalid 'obj_id':'%s' to group `, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id,
        company_id,
        group_id: group,
        level: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe(reason);
  });

  test.each([null, 'test', ''])(`shouldn't add user with invalid 'obj_id':'%s' to group `, async obj_id => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id,
        company_id,
        group_id: group,
        level: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Value is not valid');
    expect(response.body.ops[0].value).toBe(obj_id);
  });

  test.each([
    [undefined, `Key 'group_id' is required`],
    ['0', 'Group not found'],
    [0, 'Group not found'],
  ])(`shouldn't add user to group with invalid 'group_id':'%s' `, async (group_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: user.ops[0].users[0].obj_id,
        company_id,
        group_id,
        level: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe(reason);
  });

  test.each([null, 'test', ''])(`shouldn't add user to group with invalid 'group_id':'%s' `, async group_id => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: user.ops[0].users[0].obj_id,
        company_id,
        group_id,
        level: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Value is not valid');
    expect(response.body.ops[0].value).toBe(group_id);
  });

  test.each([
    [undefined, `Key 'obj_id' is required`],
    [null, 'Value is not valid'],
    ['', 'Value is not valid'],
    ['test', 'Value is not valid'],
    ['0', `Access denied`],
    [0, `Access denied`],
  ])(`shouldn't get list of users in group with invalid 'obj_id':'%s' `, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id,
        company_id,
        list_obj: 'user',
        sort: 'title',
        order: 'asc',
      }),
    );
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe(reason);
  });

  test.each([
    [undefined, `Key 'group_id' is required`],
    ['0', 'Group not found'],
    [0, 'Group not found'],
  ])(`shouldn't delete user from group with invalid 'group_id':'%s'`, async (group_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: user.ops[0].users[0].obj_id,
        company_id,
        group_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe(reason);
  });

  test.each([null, '', 'test'])(`shouldn't delete user from group with invalid 'group_id':'%s'`, async group_id => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: user.ops[0].users[0].obj_id,
        company_id,
        group_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Value is not valid');
    expect(response.body.ops[0].value).toBe(group_id);
  });

  test.each([
    [undefined, `Key 'obj_id' is required`],
    ['0', 'User not in company'],
    [0, 'User not in company'],
  ])(`shouldn't delete user from group with invalid 'obj_id':'%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id,
        company_id,
        group_id: group,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe(reason);
  });

  test.each([null, '', 'test'])(`shouldn't delete user from group with invalid 'obj_id':'%s'`, async obj_id => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id,
        company_id,
        group_id: group,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Value is not valid');
    expect(response.body.ops[0].value).toBe(obj_id);
  });

  afterAll(async () => {
    const responseKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: objIdNewApiKey,
        company_id,
      }),
    );
    expect(responseKey.status).toBe(200);

    const responseGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        obj_id: group,
        company_id,
      }),
    );
    expect(responseGroup.status).toBe(200);
  });
});
