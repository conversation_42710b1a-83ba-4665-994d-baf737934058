import { debug } from '../../../../../../../support/utils/logger';
import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import linkUser from '../../../../schemas/v2/users-groups/linkUserSchema.json';
import listUser from '../../../../schemas/v2/users-groups/listUserSchema.json';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { User } from '../../../../../../../infrastructure/model/User';
import { createAuthUser, Method } from '../../../../../../../utils/request';

describe('Users in users group (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let group: string | number;
  let ApiKey2: string | number;
  let company_id: any;
  let api_objID: string | number;
  let title: string | number;
  let user: User;
  let cookieUser: any;
  let url_superadmin: string;

  beforeAll(async () => {
    url_superadmin = `${ConfigurationManager.getConfiguration().getSuperadminUrl()}`;
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies.length ? apikey.companies[0].id : null;
    api_objID = apikey.id;
    const createGroupResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_type: 'admins',
        title: `ApiKey_${Date.now()}`,
      }),
    );
    group = createGroupResponse.body.ops[0].obj_id;

    const createUserResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        logins: [{ type: 'api' }],
        title: 'API',
      }),
    );
    const user = createUserResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    ApiKey2 = +newApiKey.id;

    const listApiKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        obj_id: api_objID,
        company_id,
      }),
    );
    expect(listApiKey.status).toBe(200);
    title = listApiKey.body.ops[0].title;
    debug('title', title);
  });

  beforeAll(async () => {
    user = await application.getAuthorizedUser({ company: {} }, 1);
    cookieUser = createAuthUser(user.cookieUser, 'cookie');
  });

  test('should add user to group', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
        company_id,
        group_id: group,
        level: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('user');
    expect(response.body.ops[0].obj_id).toBeNumber();
    expect(response.body.ops[0].logins);
    SchemaValidator.validate(linkUser, response.body);
  });

  test(`shouldn't add existing user to group`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
        company_id,
        group_id: group,
        level: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('User is already appended to group');
  });

  test('should get list of users in group', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: group,
        company_id,
        list_obj: 'user',
        sort: 'title',
        order: 'asc',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(group);
    expect(response.body.ops[0].owner_id).toBe(+apikey.id);
    expect(response.body.ops[0].owner_name).toBe(title);
    expect(response.body.ops[0].list[0].obj_id).toBe(ApiKey2);
    expect(response.body.ops[0].list[0].title).toBe('API');
    SchemaValidator.validate(listUser, response.body);
  });

  test('should delete user from group', async () => {
    const responseLinkUser = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
        company_id,
        group_id: group,
      }),
    );
    expect(responseLinkUser.status).toBe(200);
    expect(responseLinkUser.body.ops[0].proc).toBe('ok');
    expect(responseLinkUser.body.ops[0].obj).toBe('user');
    SchemaValidator.validate(linkUser, responseLinkUser.body);
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: group,
        company_id,
        list_obj: 'user',
        sort: 'title',
        order: 'asc',
      }),
    );
    expect(responseList.body.ops[0].list).toBeEmpty();
  });

  test('should show user if capi_site_auth_corezoid false', async () => {
    const data = {
      type: 'set',
      obj: 'value',
      id: 213,
      obj_type: 'boolean',
      key: 'capi_share_api_keys_in_company',
      value: 'false',
      description: 'Share api keys in company for all members',
      is_required: false,
      allowed: [],
      tags: [{ id: 1, tag: 'capi' }],
    };

    await cookieUser.request({
      method: Method.POST,
      url: `${url_superadmin}superadmin/api/1/json`,
      data,
    });

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        obj_id: api_objID,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Access denied');
  });

  afterAll(async () => {
    const data = {
      type: 'set',
      obj: 'value',
      id: 213,
      obj_type: 'boolean',
      key: 'capi_share_api_keys_in_company',
      value: 'true',
      description: 'Share api keys in company for all members',
      is_required: false,
      allowed: [],
      tags: [{ id: 1, tag: 'capi' }],
    };
    await cookieUser.request({
      method: Method.POST,
      url: `${url_superadmin}superadmin/api/1/json`,
      data,
    });

    const ResponseDeleteApiKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
        company_id,
      }),
    );
    expect(ResponseDeleteApiKey.status).toBe(200);
    const ResponseDeleteGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        obj_id: group,
        company_id,
      }),
    );
    expect(ResponseDeleteGroup.status).toBe(200);
  });
});
