import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('Merge object (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let newProject: string | number;
  let newStageProd: string | number;
  let newStage: string | number;
  let convProject: string | number;
  let aliasProject: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: `project${Date.now()}`,
        description: 'test',
        stages: [{ title: 'production', immutable: true }],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;
    newStageProd = response.body.ops[0].stages[0];

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        title: `Stage_${Date.now()}`,
        short_name: `stage-${Date.now()}`,
        description: 'test',
        project_id: newProject,
      }),
    );
    expect(responseStage.status).toBe(200);
    expect(responseStage.body.ops[0].obj).toBe('stage');
    newStage = responseStage.body.ops[0].obj_id;

    const responseConvProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: newStage,
        conv_type: 'process',
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    convProject = responseConvProject.body.ops[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        title: `${OBJ_TYPE.FOLDER}_${Date.now()}`,
        folder_id: newStage,
        stage_id: newStage,
        project_id: newProject,
      }),
    );

    const responseAliasProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: `alias-${Date.now()}`,
        description: `aliasDesk-${Date.now()}`,
        title: `aliasTitle-${Date.now()}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseAliasProject.status).toBe(200);
    aliasProject = responseAliasProject.body.ops[0].obj_id;

    const responseLinkAliasProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: aliasProject,
        company_id,
        link: true,
        obj_to_id: convProject,
        obj_to_type: 'conv',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseLinkAliasProject.status).toBe(200);
    expect(responseLinkAliasProject.body.ops[0].proc).toBe('ok');

    const responseVersion = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,
        vsn: `${Date.now()}`,
        stage_id: newStage,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    expect(responseVersion.status).toBe(200);
    expect(responseVersion.body.ops[0].obj).toBe('version');
  });

  test.each([
    [true, 'One or more processes has errors', `Value is not valid`],
    [{}, 'One or more processes has errors', `Value is not valid`],
    [[], 'One or more processes has errors', `Value is not valid`],
    ['test', 'One or more processes has errors', `Value is not valid`],
    [null, 'One or more processes has errors', `Value is not valid`],
  ])(`shouln't merge with invalid obj_id '%s'`, async (obj_id, reason, descr) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id,
        company_id,
        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id: newStage,
        apply_mode: true,
        async: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
    expect(response.body.ops[0].errors.description).toEqual(descr);
  });

  test.each([
    [0, 'Object stage with id 0 does not exist'],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [1, 'Object stage with id 1 does not exist'],
    [undefined, `Key 'obj_id' is required`],
  ])(`shouln't merge obj_id with invalid obj_id'%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id,
        company_id,
        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id: newStage,
        apply_mode: true,
        async: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, 'One or more processes has errors', `Value is not valid`],
    [{}, 'One or more processes has errors', `Value is not valid`],
    [[], 'One or more processes has errors', `Value is not valid`],
    [0, 'One or more processes has errors', 'Value is not valid'],
    [-1, `One or more processes has errors`, 'Value is not valid'],
    [1, 'One or more processes has errors', 'Value is not valid'],
  ])(`shouln't merge with invalid company_id '%s'`, async (company_id, reason, descr) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,
        project_id: newProject,
        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id: newStage,
        apply_mode: true,
        async: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
    expect(response.body.ops[0].errors.description).toEqual(descr);
  });

  test.each([
    ['test', 'Company of object do not matches company_id of request'],
    [null, 'Company of object do not matches company_id of request'],
    [undefined, `Company of object do not matches company_id of request`],
  ])(`shouln't merge with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,
        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id: newStage,
        apply_mode: true,
        async: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([true, {}, [], 'test', null])(`shouln't merge with invalid project_id '%s'`, async project_id => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,
        project_id,
        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id: newStage,
        apply_mode: true,
        async: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual('One or more processes has errors');
    expect(response.body.ops[0].errors.description).toEqual(`Value is not valid`);
  });

  test.skip.each([0, -1, 1])(`shouln't merge with invalid project_id '%s'`, async project_id => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,
        project_id,
        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id: newStage,
        apply_mode: true,
        async: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual('Project of object do not matches project_id of request');
  });

  test.each([
    [null, 'One or more processes has errors', `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, 'One or more processes has errors', `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, 'One or more processes has errors', `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], 'One or more processes has errors', `Value '[]' is not valid. Type of value is not 'binary'`],
    [0, 'One or more processes has errors', `Value '0' is not valid. Type of value is not 'binary'`],
    [
      'test',
      'One or more processes has errors',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"stage\\\">>]\">>`,
    ],
    [-1, `One or more processes has errors`, `Value '-1' is not valid. Type of value is not 'binary'`],
    [1, 'One or more processes has errors', `Value '1' is not valid. Type of value is not 'binary'`],
  ])(`shouln't merge with invalid obj_type '%s'`, async (obj_type, res, descr) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,
        project_id: newProject,
        obj_type,
        obj_to_type: 'stage',
        obj_to_id: newStage,
        apply_mode: true,
        async: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(res);
    expect(response.body.ops[0].errors.description).toEqual(descr);
  });

  test.each([
    [null, 'One or more processes has errors', `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, 'One or more processes has errors', `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, 'One or more processes has errors', `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], 'One or more processes has errors', `Value '[]' is not valid. Type of value is not 'binary'`],
    [0, 'One or more processes has errors', `Value '0' is not valid. Type of value is not 'binary'`],
    [
      'test',
      'One or more processes has errors',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"stage\\\">>,<<\\\"version\\\">>,<<\\\"scheme\\\">>]\">>`,
    ],
    [-1, `One or more processes has errors`, `Value '-1' is not valid. Type of value is not 'binary'`],
    [1, 'One or more processes has errors', `Value '1' is not valid. Type of value is not 'binary'`],
  ])(`shouln't merge with invalid obj_to_type '%s'`, async (obj_to_type, res, descr) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,
        project_id: newProject,
        obj_type: 'stage',
        obj_to_type,
        obj_to_id: newStage,
        apply_mode: true,
        async: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(res);
    expect(response.body.ops[0].errors.description).toEqual(descr);
  });

  test.each([[undefined, `Key 'obj_type' is required`]])(
    `shouln't merge merge with invalid obj_type '%s'`,
    async (obj_type, res) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MERGE,
          obj: OBJ_TYPE.OBJ_SCHEME,
          obj_id: newStageProd,
          company_id,
          project_id: newProject,
          obj_type,
          obj_to_type: 'stage',
          obj_to_id: newStage,
          apply_mode: true,
          async: true,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(res);
    },
  );

  test.each([[undefined, `Key 'obj_to_type' is required`]])(
    `shouln't merge with invalid obj_to_type '%s'`,
    async (obj_to_type, res) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MERGE,
          obj: OBJ_TYPE.OBJ_SCHEME,
          obj_id: newStageProd,
          company_id,
          project_id: newProject,
          obj_type: 'stage',
          obj_to_type,
          obj_to_id: newStage,
          apply_mode: true,
          async: true,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(res);
    },
  );

  test.each([
    [true, 'One or more processes has errors', `Value is not valid`],
    [{}, 'One or more processes has errors', `Value is not valid`],
    [[], 'One or more processes has errors', `Value is not valid`],
    ['test', 'One or more processes has errors', `Value is not valid`],
    [null, 'One or more processes has errors', `Value is not valid`],
  ])(`shouln't merge obj_to_id '%s'`, async (obj_to_id, reason, descr) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,
        project_id: newProject,
        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id,
        apply_mode: true,
        async: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
    expect(response.body.ops[0].errors.description).toEqual(descr);
  });

  test.each([
    [0, 'Object stage with id 0 does not exist'],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [1, 'Object stage with id 1 does not exist'],
    [undefined, `Key 'obj_to_id' is required`],
  ])(`shouln't merge with invalid obj_to_id '%s'`, async (obj_to_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPARE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,
        project_id: newProject,
        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id,
        apply_mode: true,
        async: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, 'One or more processes has errors', `Value 'null' is not valid. Type of value is not 'boolean'`],
    [{}, 'One or more processes has errors', `Value '{[]}' is not valid. Type of value is not 'boolean'`],
    [[], 'One or more processes has errors', `Value '[]' is not valid. Type of value is not 'boolean'`],
    ['test', 'One or more processes has errors', `Value 'test' is not valid. Type of value is not 'boolean'`],
    [-1, `One or more processes has errors`, `Value '-1' is not valid. Type of value is not 'boolean'`],
    [2, `One or more processes has errors`, `Value '2' is not valid. Type of value is not 'boolean'`],
  ])(`shouln't merge with invalid apply_mode '%s'`, async (apply_mode, res, descr) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,
        project_id: newProject,
        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id: newStage,
        apply_mode,
        async: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(res);
    expect(response.body.ops[0].errors.description).toEqual(descr);
  });

  test.each([
    [null, 'One or more processes has errors', `Value is not valid`],
    [{}, 'One or more processes has errors', `Value is not valid`],
    [[], 'One or more processes has errors', `Value is not valid`],
    ['test', 'One or more processes has errors', `Value is not valid`],
    [-1, `One or more processes has errors`, `Value is not valid`],
    [2, `One or more processes has errors`, `Value is not valid`],
  ])(`shouln't merge with invalid async '%s'`, async (async, res, descr) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,
        project_id: newProject,
        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id: newStage,
        apply_mode: true,
        async,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(res);
    expect(response.body.ops[0].errors.description).toEqual(descr);
  });

  afterAll(async () => {
    const responseDeleteProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: `${newProject}`,
        company_id,
      }),
    );
    expect(responseDeleteProject.status).toBe(200);

    const responseDestroyProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.PROJECT,
        obj_id: `${newProject}`,
        company_id,
      }),
    );
    expect(responseDestroyProject.status).toBe(200);
  });
});
