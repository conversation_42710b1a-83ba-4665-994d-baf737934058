import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { addMsg } from 'jest-html-reporters/helper';
import merge_apply_modeFalse from '../../../../schemas/v2/merge/merge_apply_modeFalseObject.Schema.json';
import merge_apply_modeTrueAsyncFalse from '../../../../schemas/v2/merge/merge_apply_modeTrueObject.Schema.json';
import merge_apply_modeTrueAsyncTrue from '../../../../schemas/v2/merge/merge_apply_modeTrueAsyncObject.Schema.json';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';

describe('Merge object (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let key: string;
  let secret: string;
  let company_id: any;
  let newProject: string | number;
  let newStageProd: string | number;
  let newStage: string | number;
  let convProject: string | number;
  let aliasProject: string | number;
  let newVersion: string | number;
  let idScheme: string | number;
  const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
  const scriptPath = 'tests/api/corezoid-api/sh/uploadObjectShemeId.sh';
  const exec = promisify(execCallback);

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key = apikey.key;
    secret = apikey.secret;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: `project${Date.now()}`,
        description: 'test',
        stages: [{ title: 'production', immutable: true }],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;
    newStageProd = response.body.ops[0].stages[0];

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: `stage-${Date.now()}`,
        description: 'test',
        project_id: newProject,
      }),
    );
    expect(responseStage.status).toBe(200);
    expect(responseStage.body.ops[0].obj).toBe('stage');
    newStage = responseStage.body.ops[0].obj_id;

    const responseConvProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: newStage,
        conv_type: 'process',
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    convProject = responseConvProject.body.ops[0].obj_id;

    const responseVersion = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn: `${Date.now()}`,
        stage_id: newStage,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    expect(responseVersion.status).toBe(200);
    expect(responseVersion.body.ops[0].obj).toBe('version');
    newVersion = responseVersion.body.ops[0].obj_id;
  });

  test(`should merge apply_mode: false, async: false`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,

        project_id: newProject,
        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id: newStage,
        apply_mode: false,
        async: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].diff[0].type).toBe('create');
    SchemaValidator.validate(merge_apply_modeFalse, response.body);

    const responseListStageProd = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newStageProd,

        project_id: newProject,
      }),
    );
    expect(responseListStageProd.status).toBe(200);
    expect(responseListStageProd.body.ops[0].list).toBeEmpty;
  });

  test(`should merge apply_mode: true, async: false`, async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,

        title: `${OBJ_TYPE.FOLDER}_${Date.now()}`,
        folder_id: newStage,
        stage_id: newStage,
        project_id: newProject,
      }),
    );

    const responseAliasProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        short_name: `alias-${Date.now()}`,
        description: `aliasDesk-${Date.now()}`,
        title: `aliasTitle-${Date.now()}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseAliasProject.status).toBe(200);
    aliasProject = responseAliasProject.body.ops[0].obj_id;

    const responseLinkAliasProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: aliasProject,
        company_id,

        link: true,
        obj_to_id: convProject,
        obj_to_type: 'conv',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseLinkAliasProject.status).toBe(200);
    expect(responseLinkAliasProject.body.ops[0].proc).toBe('ok');

    const responseMerge = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,

        project_id: newProject,
        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id: newStage,
        apply_mode: true,
        async: false,
      }),
    );
    expect(responseMerge.status).toBe(200);
    expect(responseMerge.body.request_proc).toBe('ok');
    expect(responseMerge.body.ops[0].obj).toBe('obj_scheme');
    expect(responseMerge.body.ops[0].diff.obj_type).toBe('stage');
    expect(responseMerge.body.ops[0].diff.obj_id).toBe(newStageProd);
    SchemaValidator.validate(merge_apply_modeTrueAsyncFalse, responseMerge.body);

    const responseListStageProd = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newStageProd,

        project_id: newProject,
      }),
    );
    expect(responseListStageProd.status).toBe(200);
    expect(
      (responseListStageProd.body.ops[0].list as Array<any>).find(item => item.obj_type === 'folder')?.is_owner,
    ).toEqual(true);
    expect(
      (responseListStageProd.body.ops[0].list as Array<any>).find(item => item.obj_type === 'conv')?.is_owner,
    ).toEqual(true);

    const responseListAliasStageProd = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,

        project_id: newProject,
        satage_id: newStageProd,
      }),
    );
    expect(responseListAliasStageProd.status).toBe(200);
    expect(responseListAliasStageProd.body.ops[0].list[0].obj_type).toBe('alias');
  });

  test(`should merge apply_mode: true, async: true`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,

        project_id: newProject,
        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id: newStage,
        apply_mode: true,
        async: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].hash).toBeString;
    SchemaValidator.validate(merge_apply_modeTrueAsyncTrue, response.body);
  });

  test(`should merge only required parameter`, async () => {
    await new Promise(r => setTimeout(r, 4000));

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,

        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].hash).toBeString;
    SchemaValidator.validate(merge_apply_modeTrueAsyncTrue, response.body);
  });

  test(`should merge apply_mode:true, async:true obj_to_type:version`, async () => {
    await new Promise(r => setTimeout(r, 2000));

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,

        obj_type: 'stage',
        obj_to_type: 'version',
        obj_to_id: newVersion,
        apply_mode: true,
        async: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].hash).toBeString;
    SchemaValidator.validate(merge_apply_modeTrueAsyncFalse, response.body);
  });

  test(`should marge apply_mode:true, async:true obj_to_type:scheme`, async () => {
    const { stdout, stderr } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        APPLY_MODE: 'false',
        COMPANY_ID: company_id,
        FILE_NAME: 'version_8473_1656672185782.zip',
      },
    });
    expect(stderr).toBe(``);
    expect(stdout).toContain(
      `{"request_proc":"ok","ops":[{"obj":"obj_scheme","proc":"ok","scheme_type":"stage","scheme_id":`,
    );
    await addMsg({ message: JSON.stringify(stdout), context: '' });
    idScheme = stdout.substr(103, 24);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,

        obj_type: 'stage',
        obj_to_type: 'scheme',
        obj_to_id: idScheme,
        apply_mode: true,
        async: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].hash).toBeString;
    SchemaValidator.validate(merge_apply_modeTrueAsyncFalse, response.body);
  });

  afterAll(async () => {
    const responseDeleteProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: `${newProject}`,
        company_id,
      }),
    );
    expect(responseDeleteProject.status).toBe(200);

    const responseDestroyProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.PROJECT,
        obj_id: `${newProject}`,
        company_id,
      }),
    );
    expect(responseDestroyProject.status).toBe(200);
  });
});
