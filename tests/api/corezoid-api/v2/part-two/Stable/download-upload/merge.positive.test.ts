import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { addMsg } from 'jest-html-reporters/helper';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';

describe('Merge stage/version schema (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let key: string;
  let secret: string;
  let scheme_id: string | number;
  let company_id: any;
  let production: string | number;
  let newProject: string | number;
  const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
  const scriptPath = 'tests/api/corezoid-api/sh/uploadObjectVersion.apply_mode.sh';

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key = apikey.key;
    secret = apikey.secret;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: `project${Date.now()}`,
        description: 'test',
        stages: [
          { title: 'production', immutable: true },
          { title: 'develop', immutable: false },
        ],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;
    production = response.body.ops[0].stages[0];
  });

  const exec = promisify(execCallback);

  test(`upload`, async () => {
    const { stdout, stderr } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        COMPANY_ID: company_id,
      },
    });
    expect(stderr).toBe(``);
    expect(stdout).toContain(`{"request_proc":"ok","ops":[{"obj":"obj_scheme","proc":"ok","scheme_type":"stage"`);
    await addMsg({ message: JSON.stringify(stdout), context: '' });
    scheme_id = stdout.substr(103, 24);
  });

  test('merge stage/version schema', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: production,
        company_id,
        obj_type: 'stage',
        obj_to_id: scheme_id,
        obj_to_type: 'scheme',
        apply_mode: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].obj).toEqual('obj_scheme');
  });

  afterAll(async () => {
    const ResponseDeleteConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(ResponseDeleteConv.status).toBe(200);
  });
});
