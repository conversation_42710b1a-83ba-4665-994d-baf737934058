import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { addMsg } from 'jest-html-reporters/helper';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';

describe('Upload object with alias', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let key: string;
  let secret: string;
  let short_name_project: string;
  let company_id: any;
  let project_id: string | number;
  let stage_id: string;
  const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
  const scriptPath = 'tests/api/corezoid-api/sh/uploadObjectInStage.sh';

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key = apikey.key;
    secret = apikey.secret;
    short_name_project = `project-${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: short_name_project,
        description: 'test',
        stages: [
          { title: 'develop', immutable: false },
          { title: 'develop2', immutable: false },
        ],
        status: 'active',
      }),
    );
    project_id = responseProject.body.ops[0].obj_id;
    stage_id = responseProject.body.ops[0].stages[0];
  });

  const exec = promisify(execCallback);

  test(`upload conv with alias in stage`, async () => {
    const { stdout, stderr } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        COMPANY_ID: company_id,
        OBJ_TO_ID: stage_id,
        FILE_NAME: 'upload_conv_with_alias_another_env.zip',
        ASYNC: 'true',
        SKIP_ROOT: 'false',
        OBJ_TO_TYPE: 'stage',
        WITH_ALIAS: 'true',
        REWRITE_ALIAS: 'false',
      },
    });
    expect(stderr).toBe(``);
    await addMsg({ message: JSON.stringify(stdout), context: '' });
    expect(stdout).toContain(`{"request_proc":"ok","ops":[{"obj":"obj_scheme","proc":"ok","hash":`);
  });

  test(`reloading conv with alias in stage`, async () => {
    await new Promise(r => setTimeout(r, 2000));
    const { stdout, stderr } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        COMPANY_ID: company_id,
        OBJ_TO_ID: stage_id,
        FILE_NAME: 'upload_conv_with_alias_another_env.zip',
        ASYNC: 'true',
        SKIP_ROOT: 'false',
        OBJ_TO_TYPE: 'stage',
        WITH_ALIAS: 'true',
        REWRITE_ALIAS: 'false',
      },
    });
    expect(stderr).toBe(``);
    await addMsg({ message: JSON.stringify(stdout), context: '' });
    expect(stdout).toContain(`{"request_proc":"ok","ops":[{"obj":"obj_scheme","proc":"error","errors":`);
    expect(stdout).toContain(`["Alias with short_name 'alias1' already exists"]`);
    expect(stdout).toContain(`["Alias with short_name 'alias2' already exists"]`);
  });

  afterAll(async () => {
    const ResponseDeleteProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: project_id,
        company_id,
      }),
    );
    expect(ResponseDeleteProject.status).toBe(200);
  });
});
