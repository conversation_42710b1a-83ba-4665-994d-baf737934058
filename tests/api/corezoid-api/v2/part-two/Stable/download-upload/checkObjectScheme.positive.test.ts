import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import { application } from '../../../../../../../application/Application';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import checkSchema from '../../../../schemas/v2/download-upload/checkObj.Schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('Check object scheme (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let newConv: number;
  let newFolder: number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const createFolderResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        title: `Folder_${Date.now()}`,
      }),
    );
    newFolder = createFolderResponse.body.ops[0].obj_id;

    const createConvResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: newFolder,
        title: `Conv_${Date.now()}`,
        conv_type: `process`,
      }),
    );
    newConv = createConvResponse.body.ops[0].obj_id;

    const createNodeResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        conv_id: newConv,
        version: 1,
        obj_type: 2,
      }),
    );
    expect(createNodeResponse.status).toBe(200);
  });

  test('should check object scheme folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CHECK,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newFolder,
        obj_type: `folder`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe(`error`);
    expect(response.body.ops[0].errors[0].description).toBe(
      `The process with ID ${newConv} has an uncommitted version`,
    );
    SchemaValidator.validate(checkSchema, response.body);
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
