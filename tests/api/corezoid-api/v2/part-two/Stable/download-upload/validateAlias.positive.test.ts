import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { addMsg } from 'jest-html-reporters/helper';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';

describe('Check object scheme (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let key: string;
  let company_id: any;
  let secret: string;
  let newFolder: string;
  let alias: string | number;
  let alias2: string | number;
  const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
  const scriptPath = 'tests/api/corezoid-api/sh/uploadObject.sh';
  const exec = promisify(execCallback);

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key = apikey.key;
    secret = apikey.secret;

    const createFolderResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        title: `Folder_${Date.now()}`,
      }),
    );
    newFolder = createFolderResponse.body.ops[0].obj_id;
  });

  test(`upload with aliases`, async () => {
    const { stdout, stderr } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        FOLDER_ID: newFolder,
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        VALIDATE_SCHEME: 'false',
        COMPANY_ID: company_id,
        FILE_NAME: 'folder_with_aliases_for_validate.zip',
        ASYNC: 'true',
      },
    });
    expect(stderr).toBe(``);
    expect(stdout).toContain(`{"request_proc":"ok","ops":[{"obj":"obj_scheme","proc":"ok"`);
    await addMsg({ message: JSON.stringify(stdout), context: '' });
  });

  test(`upload with aliases validate`, async () => {
    await new Promise(r => setTimeout(r, 5000));

    const { stdout, stderr } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        FOLDER_ID: newFolder,
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        VALIDATE_SCHEME: 'false',
        COMPANY_ID: company_id,
        FILE_NAME: 'folder_with_aliases_for_validate.zip',
        ASYNC: 'true',
      },
    });
    expect(stderr).toBe(``);
    expect(stdout).toContain(
      `{\"request_proc\":\"ok\",\"ops\":[{\"obj\":\"obj_scheme\",\"proc\":\"error\",\"errors\":[{\"obj_id\":0,\"obj\":\"folder\",\"title\":null,\"count\":2,\"destinations\":[{\"obj_id\":45479,\"obj\":\"alias\",\"short_name\":\"alias\",\"obj_to_id_scheme\":918855,\"obj_to_type_scheme\":\"conv\",`,
    );
    await addMsg({ message: JSON.stringify(stdout), context: '' });
  });

  test(`upload with aliases validate and conv`, async () => {
    await new Promise(r => setTimeout(r, 5000));

    const { stdout, stderr } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        FOLDER_ID: newFolder,
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        VALIDATE_SCHEME: 'true',
        COMPANY_ID: company_id,
        FILE_NAME: 'folder_with_aliases_for_validate.zip',
        ASYNC: 'true',
      },
    });
    expect(stderr).toBe(``);
    expect(stdout).toContain(
      `{\"request_proc\":\"ok\",\"ops\":[{\"obj\":\"obj_scheme\",\"proc\":\"error\",\"errors\":[{\"obj_id\":0,\"obj\":\"folder\",\"title\":null,\"count\":3,\"destinations\":[{\"obj_id\":45479,\"obj\":\"alias\",\"short_name\":\"alias\",\"obj_to_id_scheme\":918855,\"obj_to_type_scheme\":\"conv\",`,
    );
    await addMsg({ message: JSON.stringify(stdout), context: '' });
  });

  afterAll(async () => {
    const responseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(responseFolder.status).toBe(200);

    const responseListAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
      }),
    );
    expect(responseListAlias.status).toBe(200);
    alias = (responseListAlias.body.ops[0].list as Array<any>).find(item => item.short_name === 'alias').obj_id;
    alias2 = (responseListAlias.body.ops[0].list as Array<any>).find(item => item.short_name === 'alias2').obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: alias,
        company_id,
      }),
    );
    expect(responseListAlias.status).toBe(200);

    const responseDeleteAlias2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: alias2,
        company_id,
      }),
    );
    expect(responseDeleteAlias2.status).toBe(200);
  });
});
