import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { addMsg } from 'jest-html-reporters/helper';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';

describe('Upload object (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let key: string;
  let secret: string;
  let newProject: string | number;
  let company_id: any;
  const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
  const scriptPath = 'tests/api/corezoid-api/sh/uploadValidateObjScheme.sh';
  const exec = promisify(execCallback);

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key = apikey.key;
    secret = apikey.secret;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: `project${Date.now()}`,
        description: 'test',
        stages: ['production'],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;
  });

  test(`validate scheme: upload stage paused conv`, async () => {
    const { stdout, stderr } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        COMPANY_ID: company_id,
        FILE_NAME: 'stage_call_paused_conv.zip',
      },
    });
    expect(stderr).toBe(``);
    await addMsg({ message: JSON.stringify(stdout), context: '' });
    expect(stdout).toContain(`"count":1,"errors":["Only active process can be used"]`);
  });

  test(`validate scheme: Object is not supported to upload`, async () => {
    const { stdout, stderr } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        COMPANY_ID: company_id,
        FILE_NAME: 'ssl_ecc6.zip',
      },
    });
    expect(stderr).toBe(``);
    await addMsg({ message: JSON.stringify(stdout), context: '' });
    expect(stdout).toContain(
      `{"request_proc":"ok","ops":[{"proc":"error","description":"Object is not supported to upload"}]}`,
    );
  });

  test(`validate scheme: Process does not exists`, async () => {
    const { stdout, stderr } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        COMPANY_ID: company_id,
        FILE_NAME: 'stage_call_non-existent_conv.zip',
      },
    });
    expect(stderr).toBe(``);
    await addMsg({ message: JSON.stringify(stdout), context: '' });
    expect(stdout).toContain(`"count":1,"errors":["process does not exists"]`);
  });

  test(`validate scheme: Conv in other company`, async () => {
    const { stdout, stderr } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        COMPANY_ID: company_id,
        FILE_NAME: 'stage_conv_in_other_comp.zip',
      },
    });
    expect(stderr).toBe(``);
    await addMsg({ message: JSON.stringify(stdout), context: '' });
    expect(stdout).toContain(
      `"count":1,"destinations":[{"obj_id":"63035c0a094bab0e4a004c20","obj":"node","title":"","count":1,"errors":["Access denied to obj conv with id`,
    );
  });

  afterAll(async () => {
    const ResponseDeleteConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(ResponseDeleteConv.status).toBe(200);
  });
});
