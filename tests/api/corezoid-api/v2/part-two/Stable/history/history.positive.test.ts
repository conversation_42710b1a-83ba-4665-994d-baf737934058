import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import listOperations from '../../../../schemas/v2/actions-objects/listOperationsHistory.schema.json';
import listSchema from '../../../../schemas/v2/actions-objects/listHistory.schema.json';
import {
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
  requestConfirm,
} from '../../../../../../../application/api/ApiObj';

describe('List history (positive)', () => {
  let api: ApiKeyClient;
  let apiS: ApiKeyClient;
  let apikey: ApiKey;
  let owner: string | number;
  let apikeyS: ApiKey;
  let obj_idKey: any;
  let newProject: string | number;
  let newStage: string | number;
  let newStage2: string | number;
  let company_id: any;
  let folder_id: number;
  let linkFolder_id: number;
  let conv_id: number;
  let conv_id_2: number;
  let group_id: number;
  let alias_id: number;
  let alias_id_2: number;
  let dashboard_id: number;
  let instance_id: number;
  let version_id: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let project_short_name: string;
  const endUnix = Math.round(+new Date() / 1000) + 100;
  const startUnix = endUnix - 1800;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    owner = +apikey.id;
    company_id = apikey.companies[0].id;
    project_short_name = `project${Date.now()}`;

    apikeyS = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apikeyS);

    const responseUser = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'Key',
        logins: [{ type: 'api' }],
      }),
    );
    expect(responseUser.status).toBe(200);
    expect(responseUser.body.ops[0].proc).toBe('ok');
    const userOw = responseUser.body;
    const newApikey = {
      key: `${userOw.ops[0].users[0].logins[0].obj_id}`,
      secret: userOw.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${userOw.ops[0].users[0].obj_id}`,
    } as ApiKey;
    obj_idKey = +newApikey.id;

    const responseGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_type: 'admins',
        title: 'TestGroup',
      }),
    );
    expect(responseGroup.status).toBe(200);
    group_id = responseGroup.body.ops[0].obj_id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: ['production'],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;
    newStage = response.body.ops[0].stages[0];

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        project_id: newProject,
      }),
    );
    expect(responseStage.status).toBe(200);
    newStage2 = responseStage.body.ops[0].obj_id;

    const responseFolderInCompany = await requestCreateObj(
      api,
      OBJ_TYPE.FOLDER,
      company_id,
      `Folder_${Date.now()}`,
      newStage,
    );
    folder_id = responseFolderInCompany.body.ops[0].obj_id;

    const responseCreateFolder = await requestCreateObj(
      api,
      OBJ_TYPE.FOLDER,
      company_id,
      `FolderLink_${Date.now()}`,
      newStage,
    );
    linkFolder_id = responseCreateFolder.body.ops[0].obj_id;

    const responseLinkFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: linkFolder_id,
        company_id,
        folder_id: folder_id,
        obj_type: 'folder',
        parent_id: 0,
      }),
    );
    expect(responseLinkFolder.status).toBe(200);
    expect(responseLinkFolder.body.request_proc).toBe('ok');

    const responseFavorite = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folder_id,
        company_id,
        favorite: true,
      }),
    );
    expect(responseFavorite.status).toBe(200);

    const responseConv = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Delay`, newStage);
    conv_id = responseConv.body.ops[0].obj_id;

    const responseConv2 = await requestCreateObj(apiS, OBJ_TYPE.CONV, company_id, `Delay`, 0);
    conv_id_2 = responseConv2.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_id, company_id);
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateQueue = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,

        conv_id: conv_id,
        title: 'process',
        obj_type: 0,
        logics: [],
        semaphors: [{ type: 'time', value: 30, dimension: 'sec', to_node_id: final_node_ID }],
        version: 22,
      }),
    );
    expect(responseCreateQueue.status).toBe(200);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(200);

    const responseAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        short_name: project_short_name,
        description: `aliasDesk-${Date.now()}`,
        title: `aliasTitle-${Date.now()}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseAlias.status).toBe(200);
    alias_id = responseAlias.body.ops[0].obj_id;

    const responseAlias2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        short_name: project_short_name,
        description: `aliasDesk-${Date.now()}`,
        title: `aliasTitle-${Date.now()}`,
      }),
    );
    expect(responseAlias2.status).toBe(200);
    alias_id_2 = responseAlias2.body.ops[0].obj_id;

    const responseAliasUpsert = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.UPSERT,
        obj: OBJ_TYPE.ALIAS,
        obj_id: alias_id,
        company_id,

        short_name: project_short_name,
        description: `aliasDesk-${Date.now()}`,
        title: `modify-${Date.now()}`,
      }),
    );
    expect(responseAliasUpsert.status).toBe(200);

    const responseCreate = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        folder_id: newStage,
        description: 'test',
        time_range: {
          select: 'online',
          start: 1597847260,
          stop: 1597847260,
          timezone_offset: -180,
        },
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseCreate.status).toBe(200);
    dashboard_id = responseCreate.body.ops[0].obj_id;

    const responseInstance = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        instance_type: `db_call`,
        folder_id: newStage,
        project_id: newProject,
        stage_id: newStage,
        title: `Instance_${Date.now()}`,
        data: {
          driver: `postgres`,
          host: `COR-6938.middleware.loc`,
          port: `5432`,
          username: `postgres`,
          password: `pass`,
          ssl: false,
          database: `dbcall`,
          timeoutMs: 30000,
        },
      }),
    );
    expect(responseInstance.status).toBe(200);
    instance_id = responseInstance.body.ops[0].obj_id;

    const responseVersion = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn: `${Date.now()}`,
        stage_id: newStage,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    expect(responseVersion.status).toBe(200);
    version_id = responseVersion.body.ops[0].obj_id;

    const responseLinkConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: conv_id,
        company_id,
        folder_id: folder_id,
        obj_type: 'conv',
        parent_id: 0,
      }),
    );
    expect(responseLinkConv.status).toBe(200);
    expect(responseLinkConv.body.request_proc).toBe('ok');

    const responseFavoriteConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
        favorite: true,
      }),
    );
    expect(responseFavoriteConv.status).toBe(200);

    const responseResetNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESET,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID,
        company_id,

        conv_id,
      }),
    );
    expect(responseResetNode.status).toBe(200);

    const responseDownload = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: folder_id,
        obj_type: `folder`,
        with_alias: false,
        async: true,
        format: 'zip',
      }),
    );
    expect(responseDownload.status).toBe(200);

    await new Promise(r => setTimeout(r, 6000));
  });

  test('should list history conv', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        obj_id: conv_id,

        obj_type: 'conv',
        limit: 10,
        offset: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_id).toBe(conv_id);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list history folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        obj_id: folder_id,

        obj_type: 'folder',
        limit: 10,
        offset: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_id).toBe(folder_id);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list history alias', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        obj_id: alias_id,

        obj_type: 'alias',
        limit: 10,
        offset: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_id).toBe(alias_id);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list history dashboard', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        obj_id: dashboard_id,

        obj_type: 'dashboard',
        limit: 10,
        offset: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_id).toBe(dashboard_id);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list history project', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        obj_id: newProject,

        obj_type: 'project',
        limit: 10,
        offset: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_id).toBe(newProject);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list history stage', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        obj_id: newStage,

        obj_type: 'stage',
        limit: 10,
        offset: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_id).toBe(newStage);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list history instance', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        obj_id: instance_id,

        obj_type: 'instance',
        limit: 10,
        offset: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_id).toBe(instance_id);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list history version', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        obj_id: version_id,

        obj_type: 'version',
        limit: 10,
        offset: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_id).toBe(version_id);
    SchemaValidator.validate(listSchema, response.body);
  });

  test('should list history create version regular user', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,

        list_obj: 'version',
        limit: 10,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `create`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Access denied');
  });

  test.skip('should list history superadmin with required param', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        start: startUnix,
        end: endUnix,
        limit: 150,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newProject })]),
    );
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: conv_id })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: folder_id })]));

    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: dashboard_id })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: instance_id })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: version_id })]),
    );
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: group_id })]));

    expect((response.body.ops[0].list as Array<any>).find(item => item.obj === 'user').response).toContain(
      `${obj_idKey}`,
    );
    SchemaValidator.validate(listOperations, response.body);
  });

  test.skip('should list history create conv superadmin', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'conv',
        limit: 60,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `create`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: conv_id })]));
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: conv_id_2 })]));
    SchemaValidator.validate(listOperations, response.body);
  });

  test.skip('should list history create conv superadmin with user_id', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'conv',
        limit: 20,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `create`,
        user_id: owner,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: conv_id })]));
    expect(response.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: conv_id_2 })]),
    );
    SchemaValidator.validate(listOperations, response.body);
  });

  test.skip('should list history create project superadmin', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'project',
        limit: 20,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `create`,
        user_id: owner,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newProject })]),
    );
    SchemaValidator.validate(listOperations, response.body);
  });

  test('should list history delete conv', async () => {
    const responseConv = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(responseConv.status).toBe(200);

    await new Promise(r => setTimeout(r, 4000));

    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'conv',
        limit: 20,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `delete`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');

    SchemaValidator.validate(listOperations, response.body);
  });

  test('should list history create folder superadmin', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'folder',
        limit: 20,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `create`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');

    SchemaValidator.validate(listOperations, response.body);
  });

  test('should list history link folder', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'folder',
        limit: 20,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `link`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');

    SchemaValidator.validate(listOperations, response.body);
  });

  test('should list history delete folder', async () => {
    const responseConv = await requestDeleteObj(api, OBJ_TYPE.FOLDER, folder_id, company_id);
    expect(responseConv.status).toBe(200);

    await new Promise(r => setTimeout(r, 5000));

    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'folder',
        limit: 20,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `delete`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');

    SchemaValidator.validate(listOperations, response.body);
  });

  test('should list history create dashboard', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'dashboard',
        limit: 20,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `create`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: dashboard_id })]),
    );
    SchemaValidator.validate(listOperations, response.body);
  });

  test.skip('should list history delete dashboard', async () => {
    const responseDelete = await requestDeleteObj(api, OBJ_TYPE.DASHBOARD, dashboard_id, company_id);
    expect(responseDelete.status).toBe(200);

    await new Promise(r => setTimeout(r, 4000));

    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'dashboard',
        limit: 150,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `delete`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: dashboard_id })]),
    );
    SchemaValidator.validate(listOperations, response.body);
  });

  test('should list history create stage', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'stage',
        limit: 20,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `create`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newStage2 })]));
    SchemaValidator.validate(listOperations, response.body);
  });

  test('should list history delete stage', async () => {
    const responseDelete = await requestDeleteObj(api, OBJ_TYPE.STAGE, newStage2, company_id);
    expect(responseDelete.status).toBe(200);

    await new Promise(r => setTimeout(r, 5000));

    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'stage',
        limit: 20,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `delete`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');

    SchemaValidator.validate(listOperations, response.body);
  });

  test('should list history create instance', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'instance',
        limit: 20,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `create`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: instance_id })]),
    );
    SchemaValidator.validate(listOperations, response.body);
  });

  test('should list history delete instance', async () => {
    const responseDelete = await requestDeleteObj(api, OBJ_TYPE.INSTANCE, instance_id, company_id);
    expect(responseDelete.status).toBe(200);

    await new Promise(r => setTimeout(r, 4000));

    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'instance',
        limit: 20,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `delete`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');

    SchemaValidator.validate(listOperations, response.body);
  });

  test('should list history create version', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'version',
        limit: 20,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `create`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: version_id })]),
    );
    SchemaValidator.validate(listOperations, response.body);
  });

  test('should list history delete version', async () => {
    const responseDelete = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.VERSION,
        obj_id: version_id,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseDelete.status).toBe(200);

    await new Promise(r => setTimeout(r, 4500));

    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'version',
        limit: 20,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `delete`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');

    SchemaValidator.validate(listOperations, response.body);
  });

  test('should list history create user', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'user',
        limit: 20,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `create`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');

    SchemaValidator.validate(listOperations, response.body);
  });

  test('should list history delete user', async () => {
    const responseDelete = await requestDeleteObj(apiS, OBJ_TYPE.USER, obj_idKey, company_id);
    expect(responseDelete.status).toBe(200);

    await new Promise(r => setTimeout(r, 4500));

    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'user',
        limit: 50,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `delete`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');

    SchemaValidator.validate(listOperations, response.body);
  });

  test('should list history create group', async () => {
    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'group',
        limit: 20,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `create`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: group_id })]));
    SchemaValidator.validate(listOperations, response.body);
  });

  test('should list history delete group', async () => {
    const responseDelete = await requestDeleteObj(api, OBJ_TYPE.GROUP, group_id, company_id);
    expect(responseDelete.status).toBe(200);

    await new Promise(r => setTimeout(r, 5000));

    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'group',
        limit: 20,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `delete`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');

    SchemaValidator.validate(listOperations, response.body);
  });

  test('should list history delete project', async () => {
    const responseDelete = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(responseDelete.status).toBe(200);

    await new Promise(r => setTimeout(r, 4500));

    const response = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.HISTORY,
        company_id,

        list_obj: 'project',
        limit: 30,
        offset: 0,
        start: startUnix,
        end: endUnix,
        list_type: `delete`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');

    SchemaValidator.validate(listOperations, response.body);
  });

  afterAll(async () => {
    const responseProject = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(responseProject.status).toBe(200);

    const responseConv = await requestDeleteObj(apiS, OBJ_TYPE.CONV, conv_id_2, company_id);
    expect(responseConv.status).toBe(200);

    const responseAlias = await requestDeleteObj(api, OBJ_TYPE.ALIAS, alias_id_2, company_id);
    expect(responseAlias.status).toBe(200);

    const responseUser = await requestDeleteObj(apiS, OBJ_TYPE.USER, obj_idKey, company_id);
    expect(responseUser.status).toBe(200);

    const responseGroup = await requestDeleteObj(api, OBJ_TYPE.GROUP, group_id, company_id);
    expect(responseGroup.status).toBe(200);
  });
});
