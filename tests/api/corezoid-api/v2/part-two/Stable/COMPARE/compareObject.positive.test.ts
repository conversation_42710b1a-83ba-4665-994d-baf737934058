import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { addMsg } from 'jest-html-reporters/helper';
import compare from '../../../../schemas/v2/compare/compareObject.Schema.json';
import compareNum_statFalse from '../../../../schemas/v2/compare/compareNumSFObject.Schema.json';
import compareDiff_statFalse from '../../../../schemas/v2/compare/compareDiffSFObject.Schema.json';
import compareDiff_statFNum_statF from '../../../../schemas/v2/compare/compareDiffSFNumSFObject.Schema.json';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';

describe('Compare object (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let key: string;
  let secret: string;
  let company_id: any;
  let newProject: string | number;
  let newStageProd: string | number;
  let newStage: string | number;
  let convProject: string | number;
  let aliasProject: string | number;
  let newVersion: string | number;
  let idScheme: string | number;
  const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
  const scriptPath = 'tests/api/corezoid-api/sh/uploadObjectShemeId.sh';

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key = apikey.key;
    secret = apikey.secret;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: `project${Date.now()}`,
        description: 'test',
        stages: ['production'],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;
    newStageProd = response.body.ops[0].stages[0];

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: `stage-${Date.now()}`,
        description: 'test',
        project_id: newProject,
      }),
    );
    expect(responseStage.status).toBe(200);
    expect(responseStage.body.ops[0].obj).toBe('stage');
    newStage = responseStage.body.ops[0].obj_id;

    const responseConvProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: newStage,
        conv_type: 'process',
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    convProject = responseConvProject.body.ops[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,

        title: `${OBJ_TYPE.FOLDER}_${Date.now()}`,
        folder_id: newStage,
        stage_id: newStage,
        project_id: newProject,
      }),
    );

    const responseAliasProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        short_name: `alias-${Date.now()}`,
        description: `aliasDesk-${Date.now()}`,
        title: `aliasTitle-${Date.now()}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseAliasProject.status).toBe(200);
    aliasProject = responseAliasProject.body.ops[0].obj_id;

    const responseLinkAliasProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: aliasProject,
        company_id,

        link: true,
        obj_to_id: convProject,
        obj_to_type: 'conv',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseLinkAliasProject.status).toBe(200);
    expect(responseLinkAliasProject.body.ops[0].proc).toBe('ok');

    const responseVersion = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn: `${Date.now()}`,
        stage_id: newStage,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    expect(responseVersion.status).toBe(200);
    expect(responseVersion.body.ops[0].obj).toBe('version');
    newVersion = responseVersion.body.ops[0].obj_id;
  });

  const exec = promisify(execCallback);

  test(`should compare diff_status:true/num_stat:true`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPARE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,

        project_id: newProject,
        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id: newStage,
        diff_status: true,
        num_stat: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].list[0].__num_stat.added).toBe(1);
    expect(response.body.ops[0].list[0].__status).toBe('added');
    SchemaValidator.validate(compare, response.body);
  });

  test(`should compare (only requred parameters)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPARE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,

        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    SchemaValidator.validate(compareDiff_statFNum_statF, response.body);
  });

  test(`should compare diff_status:true/num_stat:false`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPARE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,

        project_id: newProject,
        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id: newStage,
        diff_status: true,
        num_stat: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].list[0].__status).toBe('added');
    SchemaValidator.validate(compareNum_statFalse, response.body);
  });

  test(`should compare diff_status:false/num_stat:true`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPARE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,

        project_id: newProject,
        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id: newStage,
        diff_status: false,
        num_stat: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].list[0].__num_stat.added).toBe(1);
    SchemaValidator.validate(compareDiff_statFalse, response.body);
  });

  test(`should compare diff_status:false/num_stat:false`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPARE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,

        project_id: newProject,
        obj_type: 'stage',
        obj_to_type: 'stage',
        obj_to_id: newStage,
        diff_status: false,
        num_stat: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    SchemaValidator.validate(compareDiff_statFNum_statF, response.body);
  });

  test(`should compare obj_to_type:version diff_status:true`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPARE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,

        project_id: newProject,
        obj_type: 'stage',
        obj_to_type: 'version',
        obj_to_id: newVersion,
        diff_status: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].list[0].__status).toBe('added');
    SchemaValidator.validate(compareNum_statFalse, response.body);
  });

  test(`should compare obj_type:version diff_status:true`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPARE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newVersion,
        company_id,

        project_id: newProject,
        obj_type: 'version',
        obj_to_type: 'stage',
        obj_to_id: newStageProd,
        diff_status: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].list[0].__status).toBe('deleted');
    SchemaValidator.validate(compareNum_statFalse, response.body);
  });

  test(`should compare obj_to_type:scheme diff_status:true`, async () => {
    const { stdout, stderr } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        APPLY_MODE: 'false',
        COMPANY_ID: company_id,
        FILE_NAME: 'version_8473_1656672185782.zip',
      },
    });
    expect(stderr).toBe(``);
    expect(stdout).toContain(
      `{"request_proc":"ok","ops":[{"obj":"obj_scheme","proc":"ok","scheme_type":"stage","scheme_id":`,
    );
    await addMsg({ message: JSON.stringify(stdout), context: '' });
    idScheme = stdout.substr(103, 24);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPARE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStageProd,
        company_id,

        project_id: newProject,
        obj_type: 'stage',
        obj_to_type: 'scheme',
        obj_to_id: idScheme,
        diff_status: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].list[0].__status).toBe('added');
    SchemaValidator.validate(compareNum_statFalse, response.body);
  });

  afterAll(async () => {
    const responseDeleteProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: `${newProject}`,
        company_id,
      }),
    );
    expect(responseDeleteProject.status).toBe(200);

    const responseDestroyProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.PROJECT,
        obj_id: `${newProject}`,
        company_id,
      }),
    );
    expect(responseDestroyProject.status).toBe(200);
  });
});
