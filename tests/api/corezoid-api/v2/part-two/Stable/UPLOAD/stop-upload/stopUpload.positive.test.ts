import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../../../../../../../infrastructure/model/ApiKey';
import { ConfigurationManager } from '../../../../../../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../../utils/corezoidRequest';
import { addMsg } from 'jest-html-reporters/helper';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../../application/api/ApiObj';
import { SchemaValidator } from '../../../../../../../../application/api/SchemaValidator';
import stopUpload from '../../../../../schemas/v2/download-upload/stopUpload.Schema.json';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';

describe('Stop Upload (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let key: string;
  let secret: string;
  let hashUploadFolder: string;
  let newFolder: string | number;
  let company_id: any;
  let newConv: number;
  let hash: string;
  const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
  const scriptPath = 'tests/api/corezoid-api/sh/uploadTask.sh';
  const filename = 'upload-task.csv';
  const scriptPath1 = 'tests/api/corezoid-api/sh/uploadObject.sh';
  const exec = promisify(execCallback);

  function parseStdoutToJson(stdout: any): any {
    const jsonStartIndex = stdout.indexOf('{');
    const jsonString = stdout.slice(jsonStartIndex);
    return JSON.parse(jsonString);
  }

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key = apikey.key;
    secret = apikey.secret;

    const responseFolder = await requestCreateObj(api, OBJ_TYPE.FOLDER, company_id, `Folder_${Date.now()}`, 0);
    newFolder = responseFolder.body.ops[0].obj_id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Delay`);
    newConv = response.body.ops[0].obj_id;
  });

  test(`stop upload task`, async () => {
    const { stdout } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        CONV_ID: `${newConv}`,
        ASYNC: 'true',
        DIVIDER: '|',
        ENCODING: 'utf-8',
        NAMES_IN_FIRST_ROW: 'false',
        COMPANY_ID: company_id,
        REFERENCE_COL_NAME: '',
        COLS_CONVERT_TO: ['object', 'array', 'boolean', 'number', 'string'].join(','),
        EXTRA_SYS_IP: 'false',
        EXTRA_SYS_FILENAME: 'false',
        FILE_NAME: filename,
      },
    });
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.request_proc).toBe(`ok`);
    expect(jsonResponse.ops[0].obj).toBe(`csv_upload`);
    hash = jsonResponse.ops[0].hash;

    const responseStop = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STOP,
        obj: OBJ_TYPE.UPLOAD,
        company_id,

        hash,
        obj_type: 'task',
      }),
    );
    expect(responseStop.status).toBe(200);
    expect(responseStop.body.ops[0].proc).toBe('ok');
    expect(responseStop.body.ops[0].obj).toBe('upload');
    SchemaValidator.validate(stopUpload, responseStop.body);
  });

  test(`stop upload obj_scheme`, async () => {
    const { stdout } = await exec(scriptPath1, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        FOLDER_ID: `${newFolder}`,
        VALIDATE_SCHEME: 'false',
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        COMPANY_ID: company_id,
        FILE_NAME: 'folder_for_test_upload_univer.suite_70conv.zip',
        ASYNC: 'true',
      },
    });
    hashUploadFolder = stdout.substr(76, 146);
    expect(stdout).toContain(`{"request_proc":"ok","ops":[{"obj":"obj_scheme","proc":"ok","hash"`);

    const responseStop = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STOP,
        obj: OBJ_TYPE.UPLOAD,
        company_id,

        hash: hashUploadFolder,
        obj_type: 'obj_scheme',
      }),
    );
    expect(responseStop.status).toBe(200);
    expect(responseStop.body.ops[0].proc).toBe('ok');
    expect(responseStop.body.ops[0].obj).toBe('upload');
    SchemaValidator.validate(stopUpload, responseStop.body);
  });

  afterAll(async () => {
    const responseDelFolder = await requestDeleteObj(api, OBJ_TYPE.FOLDER, newFolder, company_id);
    expect(responseDelFolder.status).toBe(200);

    const response = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, company_id);
    expect(response.status).toBe(200);
  });
});
