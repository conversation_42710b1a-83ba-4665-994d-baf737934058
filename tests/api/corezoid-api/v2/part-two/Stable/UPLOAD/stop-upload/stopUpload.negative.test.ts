import { debug } from '../../../../../../../../support/utils/logger';
import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../../infrastructure/model/ApiKey';
import { ConfigurationManager } from '../../../../../../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../../utils/corezoidRequest';
import { addMsg } from 'jest-html-reporters/helper';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../../application/api/ApiObj';
import {
  stringTestCases,
  undefinedTestCase,
  maxLength,
  stringNotValidTestCases,
} from '../../../../../../negativeCases';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';

describe('Upload task (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let key: string;
  let secret: string;
  let hash: string;
  let company_id: any;
  let newConv: number;
  const valuesToSkip: any = [0];
  const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
  const scriptPath = 'tests/api/corezoid-api/sh/uploadTask.sh';
  const filename = 'upload-task.csv';
  const exec = promisify(execCallback);

  function parseStdoutToJson(stdout: any): any {
    const jsonStartIndex = stdout.indexOf('{');
    const jsonString = stdout.slice(jsonStartIndex);
    return JSON.parse(jsonString);
  }

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key = apikey.key;
    secret = apikey.secret;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Delay`);
    newConv = response.body.ops[0].obj_id;

    const { stdout } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        CONV_ID: `${newConv}`,
        ASYNC: 'true',
        DIVIDER: '|',
        ENCODING: 'utf-8',
        NAMES_IN_FIRST_ROW: 'false',
        COMPANY_ID: company_id,
        REFERENCE_COL_NAME: '',
        COLS_CONVERT_TO: ['object', 'array', 'boolean', 'number', 'string'].join(','),
        EXTRA_SYS_IP: 'false',
        EXTRA_SYS_FILENAME: 'false',
        FILE_NAME: filename,
      },
    });
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.request_proc).toBe(`ok`);
    expect(jsonResponse.ops[0].obj).toBe(`csv_upload`);
    hash = jsonResponse.ops[0].hash;
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't stop upload with invalid obj_type: '%s'`, async (input, errors) => {
    const responseStop = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STOP,
        obj: OBJ_TYPE.UPLOAD,
        company_id,

        hash,
        obj_type: input,
      }),
    );
    expect(responseStop.status).toBe(200);
    expect(responseStop.body.ops[0].proc).toEqual('error');
    const error = responseStop.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...maxLength, ...stringNotValidTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't stop upload with invalid hash: '%s'`, async (input, errors) => {
    const responseStop = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STOP,
        obj: OBJ_TYPE.UPLOAD,
        company_id,

        hash: input,
        obj_type: 'task',
      }),
    );
    expect(responseStop.status).toBe(200);
    expect(responseStop.body.ops[0].proc).toEqual('error');
    const error = responseStop.body.ops[0].description;

    if (error !== undefined) {
      expect(errors).toEqual(expect.arrayContaining([error]));
    } else {
      debug('Свойство description не найдено. Проверка пропущена.');
    }
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, company_id);
    expect(response.status).toBe(200);
  });
});
