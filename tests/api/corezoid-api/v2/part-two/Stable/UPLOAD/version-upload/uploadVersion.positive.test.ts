import { Api<PERSON>eyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../../application/api/SchemaValidator';
import uploadVersionAsyncFalseSchema from '../../../../../schemas/v2/download-upload/uploadVersionAsyncFalse.Schema.json';
import uploadVersionAsyncTrueSchema from '../../../../../schemas/v2/download-upload/uploadVersionAsyncTrue.Schema.json';
import {
  createRequestWithOps,
  createRequestWithObj,
  OBJ_TYPE,
  REQUEST_TYPE,
} from '../../../../../../../../utils/corezoidRequest';

describe('Upload version (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let titleFolder: string;
  let titleConv: string;
  let newVersion: number;
  let newProject: number;
  let newStage: number;
  let newStageProd: number;
  let newStagePre: number;
  let short_name: string;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    titleFolder = `Folder_${Date.now()}`;
    titleConv = `Conv_${Date.now()}`;
    short_name = `project${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: short_name,
        short_name,
        description: 'test',
        stages: ['develop', 'prod', 'pre'],
        status: 'active',
      }),
    );
    expect(responseProject.status).toBe(200);
    newProject = responseProject.body.ops[0].obj_id;
    newStage = responseProject.body.ops[0].stages[0];
    newStageProd = responseProject.body.ops[0].stages[1];
    newStagePre = responseProject.body.ops[0].stages[2];

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: newStage,
        description: 'test',
        title: titleFolder,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);

    const response3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: newStage,
        title: titleConv,
        status: 'active',
        obj_type: 0,
        description: 'test',
        create_mode: 'without_nodes',
        conv_type: 'process',
      }),
    );
    expect(response3.status).toBe(200);

    const responseVersion = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,
        vsn: `${Date.now()}`,
        stage_id: newStage,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    expect(responseVersion.status).toBe(200);
    newVersion = responseVersion.body.ops[0].obj_id;
  });

  test('should upload version with required param', async () => {
    const response = await api.request(
      createRequestWithObj({
        type: REQUEST_TYPE.UPLOAD,
        obj: OBJ_TYPE.VERSION,
        version_id: newVersion,
        stage_to_id: newStageProd,
        project_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].stage_to_id).toEqual(newStageProd);
    SchemaValidator.validate(uploadVersionAsyncFalseSchema, response.body);
  });

  test('should find obj in stage after upload version', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStageProd,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ title: titleConv })]));
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: titleFolder })]),
    );
  });

  test('should upload version with async: false', async () => {
    const response = await api.request(
      createRequestWithObj({
        type: REQUEST_TYPE.UPLOAD,
        obj: OBJ_TYPE.VERSION,
        version_id: newVersion,
        stage_to_id: newStage,
        project_id: newProject,
        company_id,
        async: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].stage_to_id).toEqual(newStage);
    SchemaValidator.validate(uploadVersionAsyncFalseSchema, response.body);
  });

  test('should upload version with async: true', async () => {
    const response = await api.request(
      createRequestWithObj({
        type: REQUEST_TYPE.UPLOAD,
        obj: OBJ_TYPE.VERSION,
        version_id: newVersion,
        stage_to_id: newStagePre,
        project_id: newProject,
        company_id,
        async: true,
      }),
    );
    expect(response.status).toBe(200);
    SchemaValidator.validate(uploadVersionAsyncTrueSchema, response.body);
  });

  test('should find obj in stage after upload version async: true', async () => {
    await new Promise(r => setTimeout(r, 3000));

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStagePre,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ title: titleConv })]));
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: titleFolder })]),
    );
  });

  afterAll(async () => {
    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(responseProject.status).toBe(200);
  });
});
