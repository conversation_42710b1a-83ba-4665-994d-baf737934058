import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  createRequestWithObj,
  OBJ_TYPE,
  REQUEST_TYPE,
} from '../../../../../../../../utils/corezoidRequest';
import { integerTestCases, undefinedTestCase, companyTestCases, boolTestCases } from '../../../../../../negativeCases';

describe('Upload version (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let titleFolder: string;
  let titleConv: string;
  let newVersion: number;
  let newProject: number;
  let newStage: number;
  let newStageProd: number;
  let short_name: string;
  let company_id: any;
  const valuesToSkip: any = [0];

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    titleFolder = `Folder_${Date.now()}`;
    titleConv = `Conv_${Date.now()}`;
    short_name = `project${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: short_name,
        short_name,
        description: 'test',
        stages: ['develop', 'prod', 'pre'],
        status: 'active',
      }),
    );
    expect(responseProject.status).toBe(200);
    newProject = responseProject.body.ops[0].obj_id;
    newStage = responseProject.body.ops[0].stages[0];
    newStageProd = responseProject.body.ops[0].stages[1];

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        folder_id: newStage,
        description: 'test',
        title: titleFolder,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);

    const response3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: newStage,
        title: titleConv,
        status: 'active',
        obj_type: 0,
        description: 'test',
        create_mode: 'without_nodes',
        conv_type: 'process',
      }),
    );
    expect(response3.status).toBe(200);

    const responseVersion = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,
        vsn: `${Date.now()}`,
        stage_id: newStage,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    expect(responseVersion.status).toBe(200);
    newVersion = responseVersion.body.ops[0].obj_id;
  });

  test.each([...companyTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't upload version with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithObj({
          type: REQUEST_TYPE.UPLOAD,
          obj: OBJ_TYPE.VERSION,
          version_id: newVersion,
          stage_to_id: newStageProd,
          project_id: newProject,
          company_id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't upload version with invalid version_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithObj({
          type: REQUEST_TYPE.UPLOAD,
          obj: OBJ_TYPE.VERSION,
          version_id: input,
          stage_to_id: newStageProd,
          project_id: newProject,
          company_id,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...integerTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't upload version with invalid stage_to_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithObj({
          type: REQUEST_TYPE.UPLOAD,
          obj: OBJ_TYPE.VERSION,
          version_id: newVersion,
          stage_to_id: input,
          project_id: newProject,
          company_id,
          async: false,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test(`shouldn't upload version with invalid stage_to_id undefined`, async () => {
    const response = await api.request(
      createRequestWithObj({
        type: REQUEST_TYPE.UPLOAD,
        obj: OBJ_TYPE.VERSION,
        version_id: newVersion,
        stage_to_id: undefined,
        project_id: newProject,
        company_id,
        async: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors[0].errors[0]).toEqual(
      `Unable to modify stage with short_name 'develop' because a stage with that short name already exists`,
    );
  });

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't upload version with invalid project_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithObj({
        type: REQUEST_TYPE.UPLOAD,
        obj: OBJ_TYPE.VERSION,
        version_id: newVersion,
        stage_to_id: newStageProd,
        project_id: input,
        company_id,
        async: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...boolTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't upload version with invalid async '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithObj({
          type: REQUEST_TYPE.UPLOAD,
          obj: OBJ_TYPE.VERSION,
          version_id: newVersion,
          stage_to_id: newStageProd,
          project_id: newProject,
          company_id,
          async: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  afterAll(async () => {
    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(responseProject.status).toBe(200);
  });
});
