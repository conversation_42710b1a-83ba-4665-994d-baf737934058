import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../../utils/corezoidRequest';

describe('Download object (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: string | number;
  let newFolder: string | number;
  let newInstance: string | number;
  let company_id: any;
  let newDasboard: string | number;
  let newProject: string | number;
  let newStage: string | number;
  let convProject: string | number;
  let aliasProject: string | number;
  let newVersion: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const createFolderResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        title: `folder_${Date.now()}`,
      }),
    );
    expect(createFolderResponse.status).toBe(200);
    newFolder = createFolderResponse.body.ops[0].obj_id;

    const createConvResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `conv_${Date.now()}`,
        folder_id: newFolder,
      }),
    );
    expect(createConvResponse.status).toBe(200);
    newConv = createConvResponse.body.ops[0].obj_id;

    const createDashboardResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        folder_id: newFolder,
        title: `Dashboard_${Date.now()}`,
        time_range: { select: 'online', start: 1597847260, stop: 1597847260, timezone_offset: -180 },
      }),
    );
    expect(createDashboardResponse.status).toBe(200);
    newDasboard = createDashboardResponse.body.ops[0].obj_id;

    const createInstanceResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        folder_id: newFolder,
        title: `INSTANCE_${Date.now()}`,
        status: 'active',
        instance_type: 'db_call',
        obj_type: 0,
        data: {
          driver: 'postgres',
          database: 'rogue.db.elephantsql.com',
          host: 'rogue.db.elephantsql.com',
          port: '5432',
          username: 'kykfopeu',
          password: 'sNvY2zNTzi_ZQADDpIRCOhSNqGRHfE_w',
          ssl: false,
          timeoutMs: 3000,
        },
      }),
    );
    expect(createInstanceResponse.status).toBe(200);
    newInstance = createInstanceResponse.body.ops[0].obj_id;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: `project-${Date.now()}`,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: `stage-${Date.now()}`,
        description: 'test',
        project_id: newProject,
      }),
    );
    expect(responseStage.status).toBe(200);
    expect(responseStage.body.ops[0].obj).toBe('stage');
    newStage = responseStage.body.ops[0].obj_id;

    const responseConvProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: newStage,
        conv_type: 'process',
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    convProject = responseConvProject.body.ops[0].obj_id;

    const responseAliasProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        short_name: `alias-${Date.now()}`,
        description: `aliasDesk-${Date.now()}`,
        title: `aliasTitle-${Date.now()}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseAliasProject.status).toBe(200);
    aliasProject = responseAliasProject.body.ops[0].obj_id;

    const responseLinkAliasProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: aliasProject,
        company_id,

        link: true,
        obj_to_id: convProject,
        obj_to_type: 'conv',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseLinkAliasProject.status).toBe(200);
    expect(responseLinkAliasProject.body.ops[0].proc).toBe('ok');

    const responseVersion = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn: `${Date.now()}`,
        stage_id: newStage,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    expect(responseVersion.status).toBe(200);
    expect(responseVersion.body.ops[0].obj).toBe('version');
    newVersion = responseVersion.body.ops[0].obj_id;
  });

  test.each([
    [0, `process does not exists`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [1, `access denied`],
    [undefined, `Key 'obj_id' is required`],
  ])(`shouldn't download conv zip with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id,
        company_id,
        obj_type: `conv`,
        with_alias: false,
        async: true,
        format: 'zip',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', 'One or more processes has errors', 'Value is not valid'],
    [true, 'One or more processes has errors', 'Value is not valid'],
    [null, 'One or more processes has errors', 'Value is not valid'],
    ['', 'One or more processes has errors', 'Value is not valid'],
    [[], 'One or more processes has errors', 'Value is not valid'],
    [{}, 'One or more processes has errors', 'Value is not valid'],
  ])(`shouldn't download conv zip with invalid obj_id '%s'`, async (obj_id, reason, descr) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id,
        company_id,
        obj_type: `conv`,
        with_alias: false,
        async: true,
        format: 'zip',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
    expect(response.body.ops[0].errors.description).toEqual(descr);
  });

  test.each([
    [0, 'One or more processes has errors', 'Value is not valid'],
    [1234, 'One or more processes has errors', 'Value is not valid'],
    [[], 'One or more processes has errors', 'Value is not valid'],
    [{}, 'One or more processes has errors', 'Value is not valid'],
    [1, 'One or more processes has errors', 'Value is not valid'],
    [-1, 'One or more processes has errors', 'Value is not valid'],
    [true, 'One or more processes has errors', 'Value is not valid'],
  ])(`shouldn't download folder zip with invalid company_id '%s'`, async (company_id, reason, descr) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newFolder,
        company_id,
        obj_type: `folder`,
        with_alias: false,
        async: true,
        format: 'json',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
    expect(response.body.ops[0].errors.description).toEqual(descr);
  });

  test.each([
    [0, 'One or more processes has errors', `Value '0' is not valid. Type of value is not 'binary'`],
    [
      '',
      'One or more processes has errors',
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"config\\\">>,\\n <<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"version\\\">>]\">>`,
    ],
    [-1, 'One or more processes has errors', `Value '-1' is not valid. Type of value is not 'binary'`],
    [1234, 'One or more processes has errors', `Value '1234' is not valid. Type of value is not 'binary'`],
    [[], 'One or more processes has errors', `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, 'One or more processes has errors', `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [true, 'One or more processes has errors', `Value 'true' is not valid. Type of value is not 'binary'`],
    [
      'test',
      'One or more processes has errors',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"config\\\">>,\\n <<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"version\\\">>]\">>`,
    ],
  ])(`shouldn't download conv zip with invalid obj_type '%s'`, async (obj_type, reason, descr) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newConv,
        company_id,
        obj_type,
        with_alias: false,
        async: true,
        format: 'zip',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
    expect(response.body.ops[0].errors.description).toEqual(descr);
  });

  test.each([[undefined, `Key 'obj_type' is required`]])(
    `shouldn't download conv zip with invalid obj_type '%s'`,
    async (obj_type, reason) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DOWNLOAD,
          obj: OBJ_TYPE.OBJ_SCHEME,
          obj_id: newConv,
          company_id,
          obj_type,
          with_alias: false,
          async: true,
          format: 'zip',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([
    [0, 'One or more processes has errors', 'Value is not valid'],
    [-1, 'One or more processes has errors', 'Value is not valid'],
    [1234, 'One or more processes has errors', 'Value is not valid'],
    [[], 'One or more processes has errors', 'Value is not valid'],
    [{}, 'One or more processes has errors', 'Value is not valid'],
    ['test', 'One or more processes has errors', 'Value is not valid'],
    ['', 'One or more processes has errors', 'Value is not valid'],
    [null, 'One or more processes has errors', 'Value is not valid'],
  ])(`shouldn't download dashboard zip with invalid with_alias '%s'`, async (with_alias, reason, descr) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newDasboard,
        company_id,
        obj_type: `dashboard`,
        with_alias,
        async: true,
        format: 'zip',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
    expect(response.body.ops[0].errors.description).toEqual(descr);
  });

  test.each([
    [0, `Couldn't convert value '0' for key 'async' `],
    [-1, `Couldn't convert value '-1' for key 'async' `],
    ['', `Couldn't convert value '' for key 'async' `],
    [1234, `Couldn't convert value '1234' for key 'async' `],
    [[], `Couldn't convert value '[]' for key 'async' `],
    [{}, `Couldn't convert value '{[]}' for key 'async' `],
    ['test', `Couldn't convert value 'test' for key 'async' `],
    [undefined, `Key 'async' is required`],
  ])(`shouldn't download instance zip with invalid async '%s'`, async (async, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newInstance,
        company_id,
        obj_type: `instance`,
        with_alias: false,
        async,
        format: 'zip',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [1234, `Value '1234' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"zip\\\">>,<<\\\"json\\\">>]\">>`,
    ],
    ['', `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"zip\\\">>,<<\\\"json\\\">>]\">>`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't download folder with invalid format '%s'`, async (format, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newFolder,
        company_id,
        obj_type: `folder`,
        with_alias: false,
        async: true,
        format,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(reason);
  });

  test.each([
    [0, 'Couldnt download in 0 format'],
    [-1, 'Couldnt download in -1 format'],
    [1234, 'Couldnt download in 1234 format'],
    [[], 'Couldnt download in  format'],
    [{}, 'Couldnt download in {[]} format'],
    [true, 'Couldnt download in true format'],
    [null, 'Couldnt download in null format'],
    ['test', 'Couldnt download in test format'],
    ['json', 'Couldnt download in json format'],
  ])(`shouldn't download project with invalid format '%s'`, async (format, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newProject,
        company_id,
        obj_type: `project`,
        with_alias: false,
        async: true,
        format,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(reason);
  });

  test.each([
    [0, 'Couldnt download in 0 format'],
    [-1, 'Couldnt download in -1 format'],
    [1234, 'Couldnt download in 1234 format'],
    [[], 'Couldnt download in  format'],
    [{}, 'Couldnt download in {[]} format'],
    [true, 'Couldnt download in true format'],
    [null, 'Couldnt download in null format'],
    ['test', 'Couldnt download in test format'],
    ['json', 'Couldnt download in json format'],
  ])(`shouldn't download stage with invalid format '%s'`, async (format, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStage,
        company_id,
        obj_type: `stage`,
        with_alias: false,
        async: true,
        format,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(reason);
  });

  test.each([
    [0, 'Couldnt download in 0 format'],
    [-1, 'Couldnt download in -1 format'],
    [1234, 'Couldnt download in 1234 format'],
    [[], 'Couldnt download in  format'],
    [{}, 'Couldnt download in {[]} format'],
    [true, 'Couldnt download in true format'],
    [null, 'Couldnt download in null format'],
    ['test', 'Couldnt download in test format'],
    ['json', 'Couldnt download in json format'],
  ])(`shouldn't download version with invalid format '%s'`, async (format, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newVersion,
        company_id,
        obj_type: `version`,
        with_alias: false,
        async: true,
        format,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(reason);
  });

  afterAll(async () => {
    const ResponseDeleteFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(ResponseDeleteFolder.status).toBe(200);

    const ResponseDestroyFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(ResponseDestroyFolder.status).toBe(200);

    const responseDeleteProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(responseDeleteProject.status).toBe(200);

    const responseDestroyProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(responseDestroyProject.status).toBe(200);
  });
});
