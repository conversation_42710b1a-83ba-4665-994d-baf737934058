import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../../application/api/SchemaValidator';
import { ConfigurationManager } from '../../../../../../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../../utils/corezoidRequest';
import download from '../../../../../schemas/v2/download-upload/downloadObject.Schema.json';
import axios, { AxiosRequestConfig } from 'axios';

describe('Download object (positive)', () => {
  let api: ApiKeyClient;
  let apiMyCorezoid: ApiKeyClient;
  let apikey: ApiKey;
  let apikeyMyCorezoid: ApiKey;
  let baseUrl: string | number;
  let newConv: string | number;
  let newFolder: string | number;
  let newConvMC: string | number;
  let newFolderMC: string | number;
  let newInstance: string | number;
  let company_id: any;
  let newDasboard: string | number;
  let newProject: string | number;
  let newStage: string | number;
  let convProject: string | number;
  let aliasProject: string | number;
  let newVersion: string | number;
  baseUrl = ConfigurationManager.getConfiguration().getUrl();

  async function makeRequest(): Promise<void> {
    const config: AxiosRequestConfig = {
      method: 'get',
      url: `${baseUrl}system/conf`,
    };

    const res = await axios(config);
    baseUrl = res.data.web_settings.host.site;
  }

  makeRequest();

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    apikeyMyCorezoid = await application.getApiKey();
    apiMyCorezoid = application.getApiKeyClient(apikeyMyCorezoid);

    const createFolderResponseMC = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        title: `folder_${Date.now()}`,
      }),
    );
    expect(createFolderResponseMC.status).toBe(200);
    newFolderMC = +createFolderResponseMC.body.ops[0].obj_id;

    const createConvResponseMC = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        title: `conv_${Date.now()}`,
        folder_id: newFolderMC,
      }),
    );
    expect(createConvResponseMC.status).toBe(200);
    newConvMC = createConvResponseMC.body.ops[0].obj_id;

    const createFolderResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        title: `folder_${Date.now()}`,
      }),
    );
    expect(createFolderResponse.status).toBe(200);
    newFolder = +createFolderResponse.body.ops[0].obj_id;

    const createConvResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `conv_${Date.now()}`,
        folder_id: newFolder,
      }),
    );
    expect(createConvResponse.status).toBe(200);
    newConv = createConvResponse.body.ops[0].obj_id;

    const createDashboardResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        folder_id: newFolder,
        title: `Dashboard_${Date.now()}`,
        time_range: { select: 'online', start: 1597847260, stop: 1597847260, timezone_offset: -180 },
      }),
    );
    expect(createDashboardResponse.status).toBe(200);
    newDasboard = createDashboardResponse.body.ops[0].obj_id;

    const createInstanceResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        folder_id: newFolder,
        title: `INSTANCE_${Date.now()}`,
        status: 'active',
        instance_type: 'db_call',
        obj_type: 0,
        data: {
          driver: 'postgres',
          database: 'rogue.db.elephantsql.com',
          host: 'rogue.db.elephantsql.com',
          port: '5432',
          username: 'kykfopeu',
          password: 'sNvY2zNTzi_ZQADDpIRCOhSNqGRHfE_w',
          ssl: false,
          timeoutMs: 3000,
        },
      }),
    );
    expect(createInstanceResponse.status).toBe(200);
    newInstance = createInstanceResponse.body.ops[0].obj_id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: `project${Date.now()}`,
        description: 'test',
        stages: ['production'],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: `stage-${Date.now()}`,
        description: 'test',
        project_id: newProject,
      }),
    );
    expect(responseStage.status).toBe(200);
    expect(responseStage.body.ops[0].obj).toBe('stage');
    newStage = responseStage.body.ops[0].obj_id;

    const responseConvProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: newStage,
        conv_type: 'process',
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    convProject = responseConvProject.body.ops[0].obj_id;

    const responseAliasProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        short_name: `alias-${Date.now()}`,
        description: `aliasDesk-${Date.now()}`,
        title: `aliasTitle-${Date.now()}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseAliasProject.status).toBe(200);
    aliasProject = responseAliasProject.body.ops[0].obj_id;

    const responseLinkAliasProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: aliasProject,
        company_id,

        link: true,
        obj_to_id: convProject,
        obj_to_type: 'conv',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseLinkAliasProject.status).toBe(200);
    expect(responseLinkAliasProject.body.ops[0].proc).toBe('ok');

    const responseVersion = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn: `${Date.now()}`,
        stage_id: newStage,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    expect(responseVersion.status).toBe(200);
    expect(responseVersion.body.ops[0].obj).toBe('version');
    newVersion = responseVersion.body.ops[0].obj_id;
  });

  test(`should download folder async zip My Corezoid`, async () => {
    const response = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newFolderMC,
        obj_type: `folder`,
        with_alias: false,
        async: true,
        format: 'zip',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`zip`);
    expect(response.body.ops[0].statistics_id).toBeString();
    SchemaValidator.validate(download, response.body);
  });

  test(`should download conv async zip My Corezoid`, async () => {
    const response = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newConvMC,
        obj_type: `conv`,
        with_alias: true,
        async: true,
        format: 'zip',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`zip`);
    expect(response.body.ops[0].statistics_id).toBeString();
    SchemaValidator.validate(download, response.body);
  });

  test(`should download folder async:false(only requred parameters)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newFolder,
        obj_type: `folder`,
        async: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`zip`);
    expect(response.body.ops[0].statistics_id).toBeString();
    SchemaValidator.validate(download, response.body);
  });

  test(`should download folder async:true zip`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newFolder,
        company_id,
        obj_type: `folder`,
        with_alias: false,
        async: true,
        format: 'zip',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`zip`);
    expect(response.body.ops[0].statistics_id).toBeString();
    SchemaValidator.validate(download, response.body);
  });

  test(`should download conv async zip and company_id: undefined`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newConv,
        company_id: undefined,
        obj_type: `conv`,
        with_alias: true,
        async: true,
        format: 'zip',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`zip`);
    expect(response.body.ops[0].statistics_id).toBeString();
    SchemaValidator.validate(download, response.body);
  });

  test(`should download dashboard async zip`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newDasboard,
        company_id,
        obj_type: `dashboard`,
        with_alias: false,
        async: true,
        format: 'zip',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`zip`);
    expect(response.body.ops[0].statistics_id).toBeString();
    SchemaValidator.validate(download, response.body);
  });

  test(`should download instance async zip`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newInstance,
        company_id,
        obj_type: `instance`,
        with_alias: false,
        async: true,
        format: 'zip',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`zip`);
    expect(response.body.ops[0].statistics_id).toBeString();
    SchemaValidator.validate(download, response.body);
  });

  test(`should download folder async json`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newFolder,
        company_id,
        obj_type: `folder`,
        with_alias: false,
        async: true,
        format: 'json',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`json`);
    expect(response.body.ops[0].statistics_id).toBeString();
    SchemaValidator.validate(download, response.body);
  });

  test(`should download folder async false zip`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newFolder,
        company_id,
        obj_type: `folder`,
        with_alias: false,
        async: false,
        format: 'zip',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`zip`);
    expect(response.body.ops[0].statistics_id).toBeString();
    SchemaValidator.validate(download, response.body);
  });

  test(`should download project async false zip`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newProject,
        company_id,
        obj_type: `project`,
        with_alias: false,
        async: false,
        format: 'zip',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`zip`);
    expect(response.body.ops[0].statistics_id).toBeString();
    SchemaValidator.validate(download, response.body);
  });

  test(`should download project async true zip`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newProject,
        company_id,
        obj_type: `project`,
        with_alias: false,
        async: true,
        format: 'zip',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`zip`);
    expect(response.body.ops[0].statistics_id).toBeString();
    SchemaValidator.validate(download, response.body);
  });

  test(`should download stage async true zip`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStage,
        company_id,
        obj_type: `stage`,
        with_alias: false,
        async: true,
        format: 'zip',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`zip`);
    expect(response.body.ops[0].statistics_id).toBeString();
    SchemaValidator.validate(download, response.body);
  });

  test(`should download conv(project) with alias async true zip`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: convProject,
        company_id,
        obj_type: `conv`,
        with_alias: true,
        async: true,
        format: 'zip',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`zip`);
    expect(response.body.ops[0].statistics_id).toBeString();
    SchemaValidator.validate(download, response.body);
  });

  test(`should download version async true zip`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newVersion,
        company_id,
        obj_type: `version`,
        with_alias: false,
        async: true,
        format: 'zip',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].download_url).toInclude(`https://${baseUrl}/user_downloads/`);
    expect(response.body.ops[0].download_url).toContain(`zip`);
    expect(response.body.ops[0].statistics_id).toBeNull;
  });

  afterAll(async () => {
    const ResponseDeleteFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(ResponseDeleteFolder.status).toBe(200);

    const ResponseDestroyFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(ResponseDestroyFolder.status).toBe(200);

    const responseDeleteProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: `${newProject}`,
        company_id,
      }),
    );
    expect(responseDeleteProject.status).toBe(200);

    const responseDestroyProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.PROJECT,
        obj_id: `${newProject}`,
        company_id,
      }),
    );
    expect(responseDestroyProject.status).toBe(200);
  });
});
