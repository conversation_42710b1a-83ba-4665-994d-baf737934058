import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../../utils/corezoidRequest';
import { ConfigurationManager } from '../../../../../../../../infrastructure/config/ConfigurationManager';
import axios, { AxiosRequestConfig, Method } from 'axios';

describe('TaskArchive (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: string | number;
  let newNode: string;
  let company_id: any;
  const endUnix = Math.round(+new Date() / 1000);
  const startUnix = endUnix - 600;
  let baseUrl: string | number;

  baseUrl = ConfigurationManager.getConfiguration().getUrl();

  async function makeRequest(method: Method, url: string): Promise<void> {
    const config: AxiosRequestConfig = {
      method,
      url,
    };

    const res = await axios(config);
    baseUrl = res.data.web_settings.host.site;
  }

  makeRequest('get', `${baseUrl}system/conf`);

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: 0,
        conv_type: 'process',
      }),
    );
    newConv = responseConv.body.ops[0].obj_id;

    const responseListConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
    newNode = responseListConv.body.ops[0].list[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: newConv,
        data: { a: '1' },
        ref: `ref_${Date.now()}`,
      }),
    );
  });

  test.each([
    [0, 'One or more processes has errors', `Value is not valid. Value's limit is less than minimum allowed: 1`],
    [-1, 'One or more processes has errors', `Value is not valid. Value's limit is less than minimum allowed: 1`],
    [true, 'One or more processes has errors', `Value 'true' is not valid. Type of value is not 'integer'`],
    [{}, 'One or more processes has errors', `Value '{[]}' is not valid. Type of value is not 'integer'`],
    [[], 'One or more processes has errors', `Value '[]' is not valid. Type of value is not 'integer'`],
    ['test', 'One or more processes has errors', `Value 'test' is not valid. Type of value is not 'integer'`],
    [null, 'One or more processes has errors', `Value 'null' is not valid. Type of value is not 'integer'`],
  ])(`shouldn't download task node with invalid conv_id '%s'`, async (conv_id, reason, descr) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id,
        obj_id: newNode,
        select: 'fromTo',
        start: startUnix,
        end: endUnix,
        offset: 0,
        limit: 1,
        format: 'csv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
    expect(response.body.ops[0].errors.description).toEqual(descr);
  });

  test.each([
    [1, 'access denied'],
    [undefined, `Key 'conv_id' is required`],
  ])(`shouldn't download task node with invalid conv_id '%s'`, async (conv_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id,
        obj_id: newNode,
        select: 'fromTo',
        start: startUnix,
        end: endUnix,
        offset: 0,
        limit: 1,
        format: 'csv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [1, 'One or more processes has errors', `Value '1' is not valid. Type of value is not 'binary'`],
    [0, 'One or more processes has errors', `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, 'One or more processes has errors', `Value '-1' is not valid. Type of value is not 'binary'`],
    [true, 'One or more processes has errors', `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, 'One or more processes has errors', `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], 'One or more processes has errors', `Value '[]' is not valid. Type of value is not 'binary'`],
    [
      'test',
      'One or more processes has errors',
      `Value is not valid. Value's byte_size is less than minimum allowed: 24`,
    ],
    [null, 'One or more processes has errors', `Value 'null' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't download task node with invalid obj_id '%s'`, async (obj_id, reason, descr) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id: newConv,
        obj_id,
        select: 'fromTo',
        start: startUnix,
        end: endUnix,
        offset: 0,
        limit: 1,
        format: 'csv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
    expect(response.body.ops[0].errors.description).toEqual(descr);
  });

  test.each([[undefined, `Key 'obj_id' is required`]])(
    `shouldn't download task node with invalid obj_id '%s'`,
    async (obj_id, reason) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DOWNLOAD,
          obj: OBJ_TYPE.NODE,

          conv_id: newConv,
          obj_id,
          select: 'fromTo',
          start: startUnix,
          end: endUnix,
          offset: 0,
          limit: 1,
          format: 'csv',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([
    [1, 'One or more processes has errors', `Value is not valid`],
    [0, 'One or more processes has errors', `Value is not valid`],
    [-1, 'One or more processes has errors', `Value is not valid`],
    [true, 'One or more processes has errors', `Value is not valid`],
    [{}, 'One or more processes has errors', `Value is not valid`],
    [[], 'One or more processes has errors', `Value is not valid`],
  ])(`shouldn't download task node with invalid company_id '%s'`, async (company_id, reason, descr) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,
        company_id,

        conv_id: newConv,
        obj_id: newNode,
        select: 'fromTo',
        start: startUnix,
        end: endUnix,
        offset: 0,
        limit: 1,
        format: 'csv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
    expect(response.body.ops[0].errors.description).toEqual(descr);
  });

  test.each([
    ['te', 'there is no tasks in selected range'],
    ['i973400319', 'there is no tasks in selected range'],
  ])(`shouldn't download task node with invalid company_id '%s'`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,
        company_id,

        conv_id: newConv,
        obj_id: newNode,
        select: 'fromTo',
        start: startUnix,
        end: endUnix,
        offset: 0,
        limit: 1,
        format: 'csv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([1, 0, -1, true, null, 'test'])(`shouldn't download task node with invalid select '%s'`, async select => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id: newConv,
        obj_id: newNode,
        select,
        start: startUnix,
        end: endUnix,
        offset: 0,
        limit: 1,
        format: 'csv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(
      `Value '<<\"${select}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"fromTo\\\">>]\">> or Value '<<\"${select}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"last10minute\\\">>,<<\\\"lastHour\\\">>,<<\\\"last6hour\\\">>,<<\\\"last12hour\\\">>,\\n <<\\\"last24hour\\\">>,<<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previou...\">>`,
    );
  });

  test.each([
    [
      {},
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"fromTo\\\">>]\">> or Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"last10minute\\\">>,<<\\\"lastHour\\\">>,<<\\\"last6hour\\\">>,<<\\\"last12hour\\\">>,\\n <<\\\"last24hour\\\">>,<<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previou...\">>`,
    ],
    [
      [],
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"fromTo\\\">>]\">> or Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"last10minute\\\">>,<<\\\"lastHour\\\">>,<<\\\"last6hour\\\">>,<<\\\"last12hour\\\">>,\\n <<\\\"last24hour\\\">>,<<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previou...\">>`,
    ],
  ])(`shouldn't download task node with invalid select '%s'`, async (select, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id: newConv,
        obj_id: newNode,
        select,
        start: startUnix,
        end: endUnix,
        offset: 0,
        limit: 1,
        format: 'csv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [1, `there is no tasks in selected range`],
    [0, `there is no tasks in selected range`],
    [
      -1,
      `Value is not valid. Value's limit is less than minimum allowed: 0 or Value '<<\"fromTo\">>' is not valid. Value is not in allowed list <<\"[<<\\\"last10minute\\\">>,<<\\\"lastHour\\\">>,<<\\\"last6hour\\\">>,<<\\\"last12hour\\\">>,\\n <<\\\"last24hour\\\">>,<<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previou...\">>`,
    ],
    [
      true,
      `Value is not valid or Value '<<\"fromTo\">>' is not valid. Value is not in allowed list <<\"[<<\\\"last10minute\\\">>,<<\\\"lastHour\\\">>,<<\\\"last6hour\\\">>,<<\\\"last12hour\\\">>,\\n <<\\\"last24hour\\\">>,<<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previou...\">>`,
    ],
    [
      {},
      `Value is not valid or Value '<<\"fromTo\">>' is not valid. Value is not in allowed list <<\"[<<\\\"last10minute\\\">>,<<\\\"lastHour\\\">>,<<\\\"last6hour\\\">>,<<\\\"last12hour\\\">>,\\n <<\\\"last24hour\\\">>,<<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previou...\">>`,
    ],
    [
      [],
      `Value is not valid or Value '<<\"fromTo\">>' is not valid. Value is not in allowed list <<\"[<<\\\"last10minute\\\">>,<<\\\"lastHour\\\">>,<<\\\"last6hour\\\">>,<<\\\"last12hour\\\">>,\\n <<\\\"last24hour\\\">>,<<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previou...\">>`,
    ],
    [
      'test',
      `Value is not valid or Value '<<\"fromTo\">>' is not valid. Value is not in allowed list <<\"[<<\\\"last10minute\\\">>,<<\\\"lastHour\\\">>,<<\\\"last6hour\\\">>,<<\\\"last12hour\\\">>,\\n <<\\\"last24hour\\\">>,<<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previou...\">>`,
    ],
    [
      undefined,
      `Key 'start' is required or Value '<<\"fromTo\">>' is not valid. Value is not in allowed list <<\"[<<\\\"last10minute\\\">>,<<\\\"lastHour\\\">>,<<\\\"last6hour\\\">>,<<\\\"last12hour\\\">>,\\n <<\\\"last24hour\\\">>,<<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previou...\">>`,
    ],
  ])(`shouldn't download task node with invalid start '%s'`, async (start, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id: newConv,
        obj_id: newNode,
        select: 'fromTo',
        start,
        end: endUnix,
        offset: 0,
        limit: 1,
        format: 'csv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [1, `there is no tasks in selected range`],
    [0, `there is no tasks in selected range`],
    [
      -1,
      `Value is not valid. Value's limit is less than minimum allowed: 0 or Value '<<\"fromTo\">>' is not valid. Value is not in allowed list <<\"[<<\\\"last10minute\\\">>,<<\\\"lastHour\\\">>,<<\\\"last6hour\\\">>,<<\\\"last12hour\\\">>,\\n <<\\\"last24hour\\\">>,<<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previou...\">>`,
    ],
    [
      true,
      `Value is not valid or Value '<<\"fromTo\">>' is not valid. Value is not in allowed list <<\"[<<\\\"last10minute\\\">>,<<\\\"lastHour\\\">>,<<\\\"last6hour\\\">>,<<\\\"last12hour\\\">>,\\n <<\\\"last24hour\\\">>,<<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previou...\">>`,
    ],
    [
      {},
      `Value is not valid or Value '<<\"fromTo\">>' is not valid. Value is not in allowed list <<\"[<<\\\"last10minute\\\">>,<<\\\"lastHour\\\">>,<<\\\"last6hour\\\">>,<<\\\"last12hour\\\">>,\\n <<\\\"last24hour\\\">>,<<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previou...\">>`,
    ],
    [
      [],
      `Value is not valid or Value '<<\"fromTo\">>' is not valid. Value is not in allowed list <<\"[<<\\\"last10minute\\\">>,<<\\\"lastHour\\\">>,<<\\\"last6hour\\\">>,<<\\\"last12hour\\\">>,\\n <<\\\"last24hour\\\">>,<<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previou...\">>`,
    ],
    [
      'test',
      `Value is not valid or Value '<<\"fromTo\">>' is not valid. Value is not in allowed list <<\"[<<\\\"last10minute\\\">>,<<\\\"lastHour\\\">>,<<\\\"last6hour\\\">>,<<\\\"last12hour\\\">>,\\n <<\\\"last24hour\\\">>,<<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previou...\">>`,
    ],
    [
      undefined,
      `Key 'end' is required or Value '<<\"fromTo\">>' is not valid. Value is not in allowed list <<\"[<<\\\"last10minute\\\">>,<<\\\"lastHour\\\">>,<<\\\"last6hour\\\">>,<<\\\"last12hour\\\">>,\\n <<\\\"last24hour\\\">>,<<\\\"lastWeek\\\">>,<<\\\"lastMonth\\\">>,<<\\\"previou...\">>`,
    ],
  ])(`shouldn't download task node with invalid end '%s'`, async (end, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id: newConv,
        obj_id: newNode,
        select: 'fromTo',
        start: startUnix,
        end,
        offset: 0,
        limit: 1,
        format: 'csv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [-1, `One or more processes has errors`, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [true, `One or more processes has errors`, `Value 'true' is not valid. Type of value is not 'integer'`],
    [null, `One or more processes has errors`, `Value 'null' is not valid. Type of value is not 'integer'`],
    [{}, `One or more processes has errors`, `Value '{[]}' is not valid. Type of value is not 'integer'`],
    [[], `One or more processes has errors`, `Value '[]' is not valid. Type of value is not 'integer'`],
    ['test', `One or more processes has errors`, `Value 'test' is not valid. Type of value is not 'integer'`],
  ])(`shouldn't download task node with invalid offset '%s'`, async (offset, reason, descr) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id: newConv,
        obj_id: newNode,
        select: 'fromTo',
        start: startUnix,
        end: endUnix,
        offset,
        limit: 1,
        format: 'csv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
    expect(response.body.ops[0].errors.description).toEqual(descr);
  });

  test.each([[1, `there is no tasks in selected range`]])(
    `shouldn't download task node with invalid offset '%s'`,
    async (offset, reason) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DOWNLOAD,
          obj: OBJ_TYPE.NODE,

          conv_id: newConv,
          obj_id: newNode,
          select: 'fromTo',
          start: startUnix,
          end: endUnix,
          offset,
          limit: 1,
          format: 'csv',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([true, null, {}, [], 'test'])(
    `shouldn't download task node with invalid timezone_offset '%s'`,
    async offset => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DOWNLOAD,
          obj: OBJ_TYPE.NODE,

          conv_id: newConv,
          obj_id: newNode,
          select: 'fromTo',
          start: startUnix,
          end: endUnix,
          offset: 0,
          limit: 1,
          format: 'csv',
          timezone_offset: offset,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(`One or more processes has errors`);
      expect(response.body.ops[0].errors.description).toEqual(`Value is not valid`);
    },
  );

  test.each([1, -1])(`shouldn't download task node with invalid timezone_offset '%s'`, async offset => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id: newConv,
        obj_id: newNode,
        select: 'fromTo',
        start: startUnix,
        end: endUnix,
        offset: 0,
        limit: 1,
        format: 'csv',
        timezone_offset: offset,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual('there is no tasks in selected range');
  });

  test.each([
    [0, `One or more processes has errors`, `Value is not valid. Value's limit is less than minimum allowed: 1`],
    [-1, `One or more processes has errors`, `Value is not valid. Value's limit is less than minimum allowed: 1`],
    [true, `One or more processes has errors`, `Value 'true' is not valid. Type of value is not 'integer'`],
    [null, `One or more processes has errors`, `Value 'null' is not valid. Type of value is not 'integer'`],
    [{}, `One or more processes has errors`, `Value '{[]}' is not valid. Type of value is not 'integer'`],
    [[], `One or more processes has errors`, `Value '[]' is not valid. Type of value is not 'integer'`],
    ['test', `One or more processes has errors`, `Value 'test' is not valid. Type of value is not 'integer'`],
    [10001, `One or more processes has errors`, `Value is not valid. Value's limit is more than maximum allowed: 1000`],
  ])(`shouldn't download task node with invalid limit '%s'`, async (limit, reason, descr) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id: newConv,
        obj_id: newNode,
        select: 'fromTo',
        start: startUnix,
        end: endUnix,
        offset: 0,
        limit,
        format: 'csv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
    expect(response.body.ops[0].errors.description).toEqual(descr);
  });

  test.each([[undefined, `Key 'limit' is required`]])(
    `shouldn't download task node with invalid limit '%s'`,
    async (limit, reason) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DOWNLOAD,
          obj: OBJ_TYPE.NODE,

          conv_id: newConv,
          obj_id: newNode,
          select: 'fromTo',
          start: startUnix,
          end: endUnix,
          offset: 0,
          limit,
          format: 'csv',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([
    [1, `One or more processes has errors`, `Value '1' is not valid. Type of value is not 'binary'`],
    [0, `One or more processes has errors`, `Value '0' is not valid. Type of value is not 'binary'`],
    [-1, `One or more processes has errors`, `Value '-1' is not valid. Type of value is not 'binary'`],
    [true, `One or more processes has errors`, `Value 'true' is not valid. Type of value is not 'binary'`],
    [null, `One or more processes has errors`, `Value 'null' is not valid. Type of value is not 'binary'`],
    [{}, `One or more processes has errors`, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `One or more processes has errors`, `Value '[]' is not valid. Type of value is not 'binary'`],
    [
      'test',
      `One or more processes has errors`,
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"csv\\\">>]\">>`,
    ],
  ])(`shouldn't download task node with invalid format '%s'`, async (format, reason, descr) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id: newConv,
        obj_id: newNode,
        select: 'fromTo',
        start: startUnix,
        end: endUnix,
        offset: 0,
        limit: 1,
        format,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
    expect(response.body.ops[0].errors.description).toEqual(descr);
  });

  test.each([[undefined, `Key 'format' is required`]])(
    `shouldn't download task node with invalid format '%s'`,
    async (format, reason) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DOWNLOAD,
          obj: OBJ_TYPE.NODE,

          conv_id: newConv,
          obj_id: newNode,
          select: 'fromTo',
          start: startUnix,
          end: endUnix,
          offset: 0,
          limit: 1,
          format,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([
    [1, `Wrong validation function or Value '<<\"1\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`],
    [-1, `Wrong validation function or Value '<<\"-1\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`],
    [0, `Wrong validation function or Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`],
    [true, `Wrong validation function or Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`],
    [null, `Wrong validation function or Value '<<\"null\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`],
    [[], `there is no tasks in selected range`],
    [{}, `Wrong validation function or Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`],
    [
      'test',
      `Wrong validation function or Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
    ],
    [
      ['test'],
      `Value is not valid or Value '<<\"[<<\\\"test\\\">>]\">>' is not valid. Value is not in allowed list <<\"[[]]\">>`,
    ],
  ])(`shouldn't download task node with invalid extra_columns '%s'`, async (extra_columns, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,

        conv_id: newConv,
        obj_id: newNode,
        extra_columns,
        select: 'fromTo',
        start: startUnix,
        end: endUnix,
        offset: 0,
        limit: 1,
        format: 'csv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id: apikey.companies[0].id,
      }),
    );
  });
});
