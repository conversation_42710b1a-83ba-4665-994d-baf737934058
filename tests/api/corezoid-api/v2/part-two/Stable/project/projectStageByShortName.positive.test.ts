import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import createVersionSchema from '../../../../schemas/v2/project/createVersion.schema.json';
import createStageSchema from '../../../../schemas/v2/project/createStage.schema.json';
import deleteStageSchema from '../../../../schemas/v2/project/deleteStage.schema.json';
import destroyStageSchema from '../../../../schemas/v2/project/destroyStage.schema.json';
import destroyProjectSchema from '../../../../schemas/v2/project/destroyProject.schema.json';
import deleteProjectSchema from '../../../../schemas/v2/project/deleteProject.schema.json';
import listStageSchema from '../../../../schemas/v2/project/listStage.schema.json';
import modifyProjectSchema from '../../../../schemas/v2/project/modifyProject.schema.json';
import linkKeyProjectSchema from '../../../../schemas/v2/project/linkKeyProject.schema.json';
import linkKeyStageSchema from '../../../../schemas/v2/project/linkKeyStage.schema.json';
import favoriteProjectSchema from '../../../../schemas/v2/project/favoriteProject.schema.json';
import createConvSchema from '../../../../schemas/v2/actions-objects/createProcess.schema.json';
import createFolderSchema from '../../../../schemas/v2/actions-objects/createDashboard.schema.json';
import createDashboardSchema from '../../../../schemas/v2/actions-objects/createFolder.schema.json';
import createSchema from '../../../../schemas/v2/project/createAlias.schema.json';
import linkSchema from '../../../../schemas/v2/project/linkAlias.schema.json';
import createTaskSchema from '../../../../schemas/v2/tasks/createTaskSchema.json';
import showProjectSchema from '../../../../schemas/v2/project/showProject.schema.json';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import axios from 'axios';

describe('Project Stage short_name (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let newConv: string | number;
  let newVersion: string | number;
  let newAlias: string | number;
  let company_id: any;
  let taskRef: string | number;
  let taskObjId: string | number;
  let aliasHash: string;
  let taskObjId2: string | number;
  let project_short_name: string;
  let stage_short_name: string;
  let alias_short_name: string;
  let newKeyLoginId: string | number;
  let newKeyObjId: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = `project-${Date.now()}`;
    stage_short_name = `stage-${Date.now()}`;
    alias_short_name = `alias-${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,

        title: `key_${Date.now()}`,
        logins: [{ type: 'api' }],
      }),
    );
    newKeyLoginId = responseKey.body.ops[0].users[0].obj_id;
    newKeyObjId = responseKey.body.ops[0].users[0].obj_id;
  });

  test(`should create Stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    newStage = response.body.ops[0].obj_id;
    SchemaValidator.validate(createStageSchema, response.body);
  });

  test(`should modify Stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage1_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test1',
        project_short_name,
        obj_short_name: stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    SchemaValidator.validate(createStageSchema, response.body);
  });

  test(`should list Stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        company_id,
        project_short_name,
        obj_short_name: stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    SchemaValidator.validate(listStageSchema, response.body);
  });

  test(`should show_project (stages)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        obj_short_name: project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].stages).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newStage })]),
    );
    expect(response.body.ops[0].stages).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_short_name: stage_short_name })]),
    );
    SchemaValidator.validate(showProjectSchema, response.body);
  });

  test(`should modify Project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project1_${Date.now()}`,
        short_name: project_short_name,
        description: 'test1',
        obj_short_name: project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('project');
    SchemaValidator.validate(modifyProjectSchema, response.body);
  });

  test(`should favorite Project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        favorite: true,
        obj_short_name: project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('project');
    SchemaValidator.validate(favoriteProjectSchema, response.body);
  });

  test(`should link_key Project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        obj_to: 'user',
        obj_to_id: newKeyLoginId,
        obj_short_name: project_short_name,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toBe('project');
    SchemaValidator.validate(linkKeyProjectSchema, response.body);
  });

  test(`should link_key Stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.STAGE,
        company_id,

        obj_to: 'user',
        obj_to_id: newKeyLoginId,
        project_short_name,
        obj_short_name: stage_short_name,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toBe('stage');
    SchemaValidator.validate(linkKeyStageSchema, response.body);
  });

  test(`should list_project stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        obj_short_name: project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newStage })]));
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newStage)?.is_owner).toEqual(true);
    SchemaValidator.validate(listStageSchema, response.body);
  });

  test(`should create Conv`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,

        title: `Conv_${Date.now()}`,
        conv_type: 'process',
        stage_short_name,
        project_short_name,
      }),
    );
    newConv = response.body.ops[0].obj_id;
    SchemaValidator.validate(createConvSchema, response.body);
  });

  test(`should create Folder`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,

        title: `Conv_${Date.now()}`,
        stage_short_name,
        project_short_name,
      }),
    );
    SchemaValidator.validate(createFolderSchema, response.body);
  });

  test(`should create Dashboard`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Conv_${Date.now()}`,
        stage_short_name,
        project_short_name,
      }),
    );
    SchemaValidator.validate(createDashboardSchema, response.body);
  });

  test(`should create Alias`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        short_name: alias_short_name,
        title: 'Alias',
        description: 'test',
        project_short_name,
        stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('alias');
    newAlias = response.body.ops[0].obj_id;
    SchemaValidator.validate(createSchema, response.body);

    const responseHash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,
        obj_type: 'alias',
        alias_id: newAlias,
      }),
    );
    expect(responseHash.status).toBe(200);
    aliasHash = responseHash.body.ops[0].callback_hash;
  });

  test(`should list Aliases`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_short_name,
        stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].obj_type).toBe('alias');
    expect(response.body.ops[0].list[0].obj_id).toBe(newAlias);
  });

  test(`should link Alias`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
        obj_to_id: newConv,
        obj_to_type: `conv`,
        link: true,
        project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newAlias);
    SchemaValidator.validate(linkSchema, response.body);
  });

  test(`should create task by Alias`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        obj_alias: alias_short_name,
        ref: `ref_${Date.now()}`,
        data: { a: '1' },
        stage_short_name,
        project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    taskRef = response.body.ops[0].ref;
    taskObjId = response.body.ops[0].obj_id;
    SchemaValidator.validate(createTaskSchema, response.body);

    await new Promise(r => setTimeout(r, 1000));
    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: newConv,
        ref: taskRef,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].data).toEqual({ a: '1' });
    expect(responseShow.body.ops[0].ref).toEqual(taskRef);
    expect(responseShow.body.ops[0].obj_id).toEqual(taskObjId);
  });

  test(`should create task by direct url by alias`, async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/@${alias_short_name}/${project_short_name}/${stage_short_name}/${company_id}/${aliasHash}`;
    const response = await axios({
      method: 'POST',
      url: uri,
      data: { test: 1 },
    });
    expect(response.status).toBe(200);
    expect(response.data.ops.proc).toEqual('ok');
    expect(response.data.ops.obj).toEqual('task');
    taskObjId2 = response.data.ops.obj_id;
    expect(response.data.ops.obj_id).not.toEqual('');
  });

  test(`should show task after create task across Webhook Alias`, async () => {
    await new Promise(r => setTimeout(r, 1000));
    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: newConv,
        obj_id: taskObjId2,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].data).toEqual({ test: 1 });
    expect(responseShow.body.ops[0].obj_id).toEqual(taskObjId2);
  });

  test(`should create Version`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,

        vsn: `${Date.now()}`,
        stage_short_name,
        changelog: 'test',
        project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('version');
    newVersion = response.body.ops[0].obj_id;
    SchemaValidator.validate(createVersionSchema, response.body);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.VERSIONS,
        company_id,
        project_short_name,
        sort: 'vsn',
        order: 'asc',
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[0].obj_type).toBe('version');
    expect(responseList.body.ops[0].list[0].obj_id).toBe(newVersion);
  });

  test(`should delete version`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.VERSION,
        company_id,
        obj_id: newVersion,
        project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('version');
    expect(response.body.ops[0].obj_id).toBe(newVersion);
  });

  test(`should restore version`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.VERSION,
        company_id,
        obj_id: newVersion,
        project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('version');
    expect(response.body.ops[0].obj_id).toBe(newVersion);
  });

  test(`should destroy version`, async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.VERSION,
        company_id,
        obj_id: newVersion,
        project_short_name,
      }),
    );
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.VERSION,
        company_id,
        obj_id: newVersion,
        project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('version');
    expect(response.body.ops[0].obj_id).toBe(newVersion);
  });

  test(`should delete project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        obj_short_name: project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newProject);
    SchemaValidator.validate(deleteProjectSchema, response.body);
  });

  test(`should restore project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        obj_short_name: project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newProject);
    SchemaValidator.validate(deleteProjectSchema, response.body);
  });

  test('should delete stage', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        project_short_name,
        obj_short_name: stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newStage);
    expect(response.body.ops[0].obj).toBe('stage');
    SchemaValidator.validate(deleteStageSchema, response.body);
  });

  test('should restore stage', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        project_short_name,
        obj_short_name: stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newStage);
    expect(response.body.ops[0].obj).toBe('stage');
    SchemaValidator.validate(deleteStageSchema, response.body);
  });

  test('should destroy stage', async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        project_short_name,
        obj_short_name: stage_short_name,
      }),
    );
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.STAGE,
        company_id,
        project_short_name,
        obj_short_name: stage_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newStage);
    expect(response.body.ops[0].obj_type).toBe('stage');
    SchemaValidator.validate(destroyStageSchema, response.body);
  });

  test(`should destroy project`, async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        obj_short_name: project_short_name,
      }),
    );
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        obj_short_name: project_short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newProject);
    SchemaValidator.validate(destroyProjectSchema, response.body);
  });

  afterAll(async () => {
    const responseKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: newKeyObjId,
        company_id,

        group_id: 0,
        level: '',
      }),
    );
    expect(responseKey.status).toBe(200);
  });
});
