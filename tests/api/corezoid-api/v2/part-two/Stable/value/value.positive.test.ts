import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import getSchema from '../../../../schemas/v2/value/getValueSchema.json';
import setSchema from '../../../../schemas/v2/value/setValueSchema.json';

describe('Value (positive)', () => {
  let api_s: ApiKeyClient;
  let apikey_s: ApiKey;
  let value: number;
  let id: number;
  let description: string;
  let obj_type: string;
  let is_required: boolean;
  let allowed: A<PERSON>y<PERSON><PERSON>er;

  beforeAll(async () => {
    apikey_s = await application.getApiKeySuper();
    api_s = application.getApiKeyClient(apikey_s);
  });

  test(`Get value`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'get',
      obj: 'value',
      key: 'capi_share_api_keys_in_company',
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    value = response.body.value;
    id = response.body.id;
    description = response.body.description;
    obj_type = response.body.obj_type;
    is_required = response.body.is_required;
    allowed = response.body.allowed;
    SchemaValidator.validate(getSchema, response.body);
  });

  test(`Set value`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'set',
      obj: 'value',
      id,
      key: 'capi_share_api_keys_in_company',
      value: `${value}`,
      description,
      obj_type,
      is_required,
      tags: [
        {
          id: 1,
          tag: 'capi',
        },
      ],
      allowed,
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    SchemaValidator.validate(setSchema, response.body);
  });

  test(`Set value with required param`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'set',
      obj: 'value',
      key: 'capi_share_api_keys_in_company',
      value: 'true',
      obj_type: 'boolean',
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    SchemaValidator.validate(setSchema, response.body);
  });

  test.skip(`Delete value`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'delete',
      obj: 'value',
      key: 'capi_share_api_keys_in_company',
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
  });
});
