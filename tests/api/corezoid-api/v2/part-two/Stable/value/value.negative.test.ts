import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import {
  integerTestCases,
  undefinedTestCase,
  stringTestCases,
  stringNotValidTestCases,
  arrayTestCases,
} from '../../../../../negativeCases';
import { debug } from '../../../../../../../support/utils/logger';

describe('Value (negative)', () => {
  let api_s: ApiKeyClient;
  let apikey_s: ApiKey;
  let value: number;
  let description: string;
  let obj_type: string;
  let is_required: boolean;
  let allowed: ArrayBuffer;
  let valuesToSkip: any;

  beforeAll(async () => {
    apikey_s = await application.getApiKeySuper();
    api_s = application.getApiKeyClient(apikey_s);

    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'get',
      obj: 'value',
      key: 'capi_share_api_keys_in_company',
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    value = response.body.value;
    description = response.body.description;
    obj_type = response.body.obj_type;
    is_required = response.body.is_required;
    allowed = response.body.allowed;
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't get value with invalid key '%s'`, async (input, errors) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'get',
      obj: 'value',
      key: input,
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toEqual('error');
    const error = response.body.message;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...stringTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't set value with invalid key '%s'`,
    async (input, errors) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'set',
        obj: 'value',
        key: input,
        value: `${value}`,
        description,
        obj_type,
        is_required,
        tags: [
          {
            id: 1,
            tag: 'capi',
          },
        ],
        allowed,
      });
      expect(response.status).toBe(400);
      expect(response.body.result).toEqual('error');
      const error = response.body.description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't set value with invalid key '%s'`,
    async (input, errors) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'set',
        obj: 'value',
        key: input,
        value: `${value}`,
        description,
        obj_type,
        is_required,
        tags: [
          {
            id: 1,
            tag: 'capi',
          },
        ],
        allowed,
      });
      expect(response.status).toBe(400);
      expect(response.body.result).toEqual('error');
      const error = response.body.message;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [true, null];

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't set value with invalid value '%s'`, async (input, errors) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'set',
      obj: 'value',
      key: 'capi_share_api_keys_in_company',
      value: input,
      description,
      obj_type,
      is_required,
      tags: [
        {
          id: 1,
          tag: 'capi',
        },
      ],
      allowed,
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toEqual('error');
    const error = response.body.message;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...stringTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't set value with invalid obj_type '%s'`,
    async (input, errors) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'set',
        obj: 'value',
        key: 'capi_share_api_keys_in_company',
        value: `${value}`,
        description,
        obj_type: input,
        is_required,
        tags: [
          {
            id: 1,
            tag: 'capi',
          },
        ],
        allowed,
      });
      expect(response.status).toBe(400);
      debug(JSON.stringify(response.body));
      expect(response.body.result).toEqual('error');
      const error = response.body.description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't set value with invalid obj_type '%s'`,
    async (input, errors) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'set',
        obj: 'value',
        key: 'capi_share_api_keys_in_company',
        value: `${value}`,
        description,
        obj_type: input,
        is_required,
        tags: [
          {
            id: 1,
            tag: 'capi',
          },
        ],
        allowed,
      });
      expect(response.status).toBe(400);
      expect(response.body.result).toEqual('error');
      const error = response.body.message;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...arrayTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't set value with invalid tags '%s'`,
    async (input, errors) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'set',
        obj: 'value',
        key: 'capi_share_api_keys_in_company',
        value: `${value}`,
        description,
        obj_type,
        is_required,
        tags: input,
        allowed,
      });
      expect(response.status).toBe(400);
      expect(response.body.result).toEqual('error');
      const error = response.body.description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't set value with invalid vsn '%s'`, async (input, errors) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'set',
      obj: 'value',
      key: 'capi_share_api_keys_in_company',
      value: `${value}`,
      description,
      obj_type,
      vsn: input,
      is_required,
      tags: [
        {
          id: 1,
          tag: 'capi',
        },
      ],
      allowed,
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toEqual('error');
    const error = response.body.description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });
});
