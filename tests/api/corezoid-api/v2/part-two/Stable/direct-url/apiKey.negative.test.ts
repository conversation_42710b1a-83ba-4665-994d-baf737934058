import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('Direct URL apiKey (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
  });

  test.each([
    ['test', 'Value is not valid'],
    [true, 'Value is not valid'],
    [null, 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [0, `Object conv with id 0 does not exist`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [1, `User has no rights`],
    [undefined, `Key 'obj_id' is required`],
  ])(`shouldn't show api_key with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.API_KEY,
        company_id,
        obj_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });
});
