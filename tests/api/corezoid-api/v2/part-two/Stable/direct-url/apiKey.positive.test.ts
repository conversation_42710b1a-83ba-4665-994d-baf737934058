import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import linkSchema from '../../../../schemas/v2/actions-objects/linkObject.schema.json';
import showApiKey from '../../../../schemas/v2/actions-objects/direct-url/showApiKey.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('Direct URL apiKey (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let ApiKey2: string | number;
  let newConv: string | number;
  let key: string;
  let keystr: string | number;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    const createConvResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        folder_id: 0,
        tite: `Conv_${Date.now()}`,
      }),
    );
    expect(createConvResponse.status).toBe(200);
    newConv = createConvResponse.body.ops[0].obj_id;
  });

  test(`should link`, async () => {
    const CreateKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        logins: [{ type: 'api' }],
        title: 'API',
      }),
    );
    expect(CreateKeyResponse.status).toBe(200);
    const user = CreateKeyResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    ApiKey2 = +newApiKey.id;
    const ResponseLinkConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,

        obj_to_id: newApiKey.id,
        obj_to: 'user',
        is_need_to_notify: false,
        privs: [
          { type: 'view', list_obj: ['all'] },
          { type: 'create', list_obj: ['all'] },
        ],
      }),
    );
    expect(ResponseLinkConv.status).toBe(200);
    expect(ResponseLinkConv.body.ops[0].obj_type).toBe('conv');
    expect(ResponseLinkConv.body.ops[0].action_type).toBe('link');
    SchemaValidator.validate(linkSchema, ResponseLinkConv.body);
  });

  test(`should show api_key`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.API_KEY,
        obj_id: newConv,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_id).toBe(ApiKey2);
    key = response.body.ops[0].key;
    keystr = key.length;
    expect(keystr).toBe(50);
    SchemaValidator.validate(showApiKey, response.body);
  });

  test(`should unlink`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
        obj_to_id: ApiKey2,
        obj_to: 'user',
        is_need_to_notify: false,
        privs: [],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toBe('conv');
    expect(response.body.ops[0].action_type).toBe('unlink');
    SchemaValidator.validate(linkSchema, response.body);
  });

  test(`should show api_key after unlink`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.API_KEY,
        obj_id: newConv,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('api_keys not found');
  });

  afterAll(async () => {
    const ResponseDeleteApiConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
    expect(ResponseDeleteApiConv.status).toBe(200);
    const ResponseDeleteApiKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
        company_id,
        group_id: 0,
      }),
    );
    expect(ResponseDeleteApiKey.status).toBe(200);
  });
});
