import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import listSchema from '../../../../schemas/v2/value/listValuesSchema.json';

describe('Values (positive)', () => {
  let api_s: ApiKeyClient;
  let apikey_s: ApiKey;

  beforeAll(async () => {
    apikey_s = await application.getApiKeySuper();
    api_s = application.getApiKeyClient(apikey_s);
  });

  test(`Get value`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'list',
      obj: 'values',
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    SchemaValidator.validate(listSchema, response.body);
  });
});
