import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { undefinedTestCase, stringTestCases, stringNotValidTestCases } from '../../../../../negativeCases';

describe('Values (negative)', () => {
  let api_s: ApiKeyClient;
  let apikey_s: ApiKey;

  beforeAll(async () => {
    apikey_s = await application.getApiKeySuper();
    api_s = application.getApiKeyClient(apikey_s);
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't list values with invalid type '%s'`, async (input, errors) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: input,
      obj: 'values',
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toEqual('error');
    const error = response.body.message;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't list values with invalid obj '%s'`, async (input, errors) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'list',
      obj: input,
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toEqual('error');
    const error = response.body.message;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });
});
