import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import copyAsync from '../../../../schemas/v2/actions-objects/copyObjectAsync.schema.json';
import copyConvSync from '../../../../schemas/v2/actions-objects/copyConvSync.schema.json';
import copyObjectSync from '../../../../schemas/v2/actions-objects/copyObjectSync.schema.json';
import copyAsyncErrors from '../../../../schemas/v2/actions-objects/copyAsyncErrors.schema.json';
import axios, { AxiosRequestConfig } from 'axios';

import { ApiUserClient } from '../../../../../../../application/api/ApiUserClient';
import { User } from '../../../../../../../infrastructure/model/User';
import { createAuthUser, Method } from '../../../../../../../utils/request';

describe('Copy object (positive)', () => {
  let api: ApiKeyClient;
  let apiMyCorezoid: ApiKeyClient;
  let apikey: ApiKey;
  let apikeyMyCorezoid: ApiKey;
  let apiS: ApiKeyClient;
  let apiSuper: ApiKey;
  let baseUrl: string | number;
  let newConv: string | number;
  let copyConv: string | number;
  let copyInstance: string | number;
  let copyFolder: string | number;
  let copyFolderNew: string | number;
  let copyDash: string | number;
  let copyStage: string | number;
  let copyProject: string | number;
  let copyConvMC: string | number;
  let copyFolderMC: string | number;
  let copyFolderToMC: string | number;
  let copyFolderToCompany: string | number;
  let copyFolderToStage: string | number;
  let newFolder: string | number;
  let newFolderForCopy: string | number;
  let newConvMC: string | number;
  let newFolderMC: string | number;
  let newFolderMCforCopy: string | number;
  let newInstance: string | number;
  let company_id: any;
  let newDashboard: string | number;
  let newProject: string | number;
  let newStage: string | number;
  let newCompany: any;
  let newAlias: string | number;
  let token: string;
  let userToken: any;
  let hostSS: string;
  baseUrl = ConfigurationManager.getConfiguration().getUrl();
  let copyTitle: string;
  let copySName: string;
  let titleAlias: string;

  let apiUser: ApiUserClient;
  let user0: User;

  async function makeRequest(): Promise<void> {
    const config: AxiosRequestConfig = {
      method: 'get',
      url: `${baseUrl}system/conf`,
    };

    const res = await axios(config);
    baseUrl = res.data.web_settings.host.site;
  }

  makeRequest();

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const config = ConfigurationManager.getConfiguration();
    token = await application.createToken(0);
    userToken = createAuthUser(token, 'token');

    user0 = await application.getAuthorizedUser();
    apiUser = await application.getApiUserClient(user0);

    hostSS = config.getSSUrl();

    apikeyMyCorezoid = await application.getApiKey();
    apiMyCorezoid = application.getApiKeyClient(apikeyMyCorezoid);

    apiSuper = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apiSuper);

    copyTitle = `StageCopy_${Date.now()}`;
    copySName = `stagecopy-${Date.now()}`;
    titleAlias = `alias-${Date.now()}`;

    const createFolderResponseMC = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        title: `folder_${Date.now()}`,
      }),
    );
    expect(createFolderResponseMC.status).toBe(200);
    newFolderMC = +createFolderResponseMC.body.ops[0].obj_id;

    const createFolderResponseMCforCopy = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        title: `folder_${Date.now()}`,
      }),
    );
    expect(createFolderResponseMCforCopy.status).toBe(200);
    newFolderMCforCopy = +createFolderResponseMCforCopy.body.ops[0].obj_id;

    const createConvResponseMC = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        title: `conv_${Date.now()}`,
        folder_id: newFolderMC,
      }),
    );
    expect(createConvResponseMC.status).toBe(200);
    newConvMC = createConvResponseMC.body.ops[0].obj_id;

    const createFolderResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        title: `folder_${Date.now()}`,
      }),
    );
    expect(createFolderResponse.status).toBe(200);
    newFolderForCopy = +createFolderResponse.body.ops[0].obj_id;

    const createfolderResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        title: `conv_${Date.now()}`,
        folder_id: newFolderForCopy,
      }),
    );
    expect(createfolderResponse.status).toBe(200);
    newFolder = +createfolderResponse.body.ops[0].obj_id;

    const createConvResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `conv_${Date.now()}`,
        folder_id: newFolderForCopy,
      }),
    );
    expect(createConvResponse.status).toBe(200);
    newConv = createConvResponse.body.ops[0].obj_id;

    const responseAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        short_name: titleAlias,
        description: titleAlias,
        title: titleAlias,
      }),
    );
    expect(responseAlias.status).toBe(200);
    newAlias = responseAlias.body.ops[0].obj_id;

    const responseLinkAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,

        link: true,
        obj_to_id: newConv,
        obj_to_type: 'conv',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseLinkAlias.status).toBe(200);
    expect(responseLinkAlias.body.ops[0].proc).toBe('ok');

    const createDashboardResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        folder_id: newFolderForCopy,
        title: `Dashboard_${Date.now()}`,
        time_range: { select: 'online', start: 1597847260, stop: 1597847260, timezone_offset: -180 },
      }),
    );
    expect(createDashboardResponse.status).toBe(200);
    newDashboard = createDashboardResponse.body.ops[0].obj_id;

    const createInstanceResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        folder_id: newFolder,
        title: `INSTANCE_${Date.now()}`,
        status: 'active',
        instance_type: 'db_call',
        obj_type: 0,
        data: {
          driver: 'postgres',
          database: 'rogue.db.elephantsql.com',
          host: 'rogue.db.elephantsql.com',
          port: '5432',
          username: 'kykfopeu',
          password: 'sNvY2zNTzi_ZQADDpIRCOhSNqGRHfE_w',
          ssl: false,
          timeoutMs: 3000,
        },
      }),
    );
    expect(createInstanceResponse.status).toBe(200);
    newInstance = createInstanceResponse.body.ops[0].obj_id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: `project${Date.now()}`,
        description: 'test',
        stages: ['production'],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: `stage-${Date.now()}`,
        description: 'test',
        project_id: newProject,
      }),
    );
    expect(responseStage.status).toBe(200);
    expect(responseStage.body.ops[0].obj).toBe('stage');
    newStage = responseStage.body.ops[0].obj_id;
  });

  test(`should copy conv My Corezoid in folder`, async () => {
    const response = await apiMyCorezoid.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newConvMC,

        obj_to_type: `folder`,
        obj_to_id: newFolderMCforCopy,
        obj_type: 'conv',
        title: 'CopyConvMC',
        with_aliases: false,
        async: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_copy');
    expect(response.body.ops[0].hash).toBeString();
    expect(response.body.ops[0].statistics_id).toContain(`zip`);
    SchemaValidator.validate(copyAsync, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseListFolder = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolderMCforCopy,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.request_proc).toBe('ok');
    copyConvMC = (responseListFolder.body.ops[0].list as Array<any>).find(item => item.title === 'CopyConvMC').obj_id;
    expect((responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyConvMC).status).toEqual(
      'active',
    );
  });

  test(`should copy conv My Corezoid in 0`, async () => {
    const response = await apiMyCorezoid.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newConvMC,

        obj_to_type: `folder`,
        obj_to_id: 0,
        obj_type: 'conv',
        title: 'CopyConvMC1',
        with_aliases: false,
        async: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_copy');
    expect(response.body.ops[0].scheme[0].hash).toBeString();
    copyConvMC = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copyConvSync, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseListFolder = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.request_proc).toBe('ok');
    expect((responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyConvMC).status).toEqual(
      'active',
    );
  });

  test(`should copy folder My Corezoid in folder`, async () => {
    const response = await apiMyCorezoid.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolderMC,

        obj_to_type: `folder`,
        obj_to_id: newFolderMCforCopy,
        obj_type: 'folder',
        title: 'CopyFolderMC',
        with_aliases: false,
        async: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_copy');
    expect(response.body.ops[0].hash).toBeString();
    expect(response.body.ops[0].statistics_id).toContain(`zip`);
    SchemaValidator.validate(copyAsync, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseListFolder = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolderMCforCopy,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.request_proc).toBe('ok');
    copyFolderMC = (responseListFolder.body.ops[0].list as Array<any>).find(item => item.title === 'CopyFolderMC')
      .obj_id;
    expect(
      (responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyFolderMC).obj_type,
    ).toEqual('folder');
  });

  test(`should copy fodler My Corezoid in 0`, async () => {
    const response = await apiMyCorezoid.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolderMC,

        obj_to_type: `folder`,
        obj_to_id: 0,
        obj_type: 'folder',
        title: 'CopyFolderMC1',
        with_aliases: false,
        async: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_copy');
    copyFolderMC = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copyObjectSync, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseListFolder = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.request_proc).toBe('ok');
    expect(
      (responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyFolderMC).title,
    ).toEqual('CopyFolderMC1');
  });

  test(`should copy conv (company) in folder`, async () => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newConv,

        obj_to_type: `folder`,
        obj_to_id: newFolderForCopy,
        obj_type: 'conv',
        title: 'CopyConv',
        with_aliases: false,
        async: true,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_copy');
    expect(response.body.ops[0].hash).toBeString();
    expect(response.body.ops[0].statistics_id).toContain(`zip`);
    SchemaValidator.validate(copyAsync, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolderForCopy,
        company_id,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.request_proc).toBe('ok');
    copyConv = (responseListFolder.body.ops[0].list as Array<any>).find(item => item.title === 'CopyConv').obj_id;
    expect((responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyConv).status).toEqual(
      'active',
    );
  });

  test(`should copy conv (company) in folder 0`, async () => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newConv,

        obj_to_type: `folder`,
        obj_to_id: 0,
        obj_type: 'conv',
        title: 'CopyConv',
        with_aliases: false,
        async: false,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_copy');
    expect(response.body.ops[0].scheme[0].hash).toBeString();
    copyConvMC = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copyConvSync, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.request_proc).toBe('ok');
    copyConv = (responseListFolder.body.ops[0].list as Array<any>).find(item => item.title === 'CopyConv').obj_id;
    expect((responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyConv).status).toEqual(
      'active',
    );
  });

  test(`should copy folder (company) in folder`, async () => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolder,

        obj_to_type: `folder`,
        obj_to_id: newFolderForCopy,
        obj_type: 'folder',
        title: 'CopyFolder',
        with_aliases: false,
        async: true,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_copy');
    expect(response.body.ops[0].hash).toBeString();
    expect(response.body.ops[0].statistics_id).toContain(`zip`);
    SchemaValidator.validate(copyAsync, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolderForCopy,
        company_id,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.request_proc).toBe('ok');
    copyFolder = (responseListFolder.body.ops[0].list as Array<any>).find(item => item.title === 'CopyFolder').obj_id;
    expect(
      (responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyFolder).obj_type,
    ).toEqual('folder');
  });

  test(`should copy folder (company) in 0`, async () => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolder,

        obj_to_type: `folder`,
        obj_to_id: 0,
        obj_type: 'folder',
        title: 'CopyFolder1',
        with_aliases: false,
        async: false,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_copy');
    copyFolder = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copyObjectSync, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.request_proc).toBe('ok');
    copyFolder = (responseListFolder.body.ops[0].list as Array<any>).find(item => item.title === 'CopyFolder1').obj_id;
    expect(
      (responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyFolder).obj_type,
    ).toEqual('folder');
  });

  test(`should copy folder (company) in my corezoid folder`, async () => {
    const response = await apiMyCorezoid.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolder,

        obj_to_type: `folder`,
        obj_to_id: newFolderMCforCopy,
        obj_type: 'folder',
        title: 'CopyFolderToMyCorezoid',
        with_aliases: false,
        async: false,
        from_company_id: company_id,
        to_company_id: null,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_copy');
    copyFolderToMC = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copyObjectSync, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseListFolder = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolderMCforCopy,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.request_proc).toBe('ok');
    expect(
      (responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyFolderToMC).obj_type,
    ).toEqual('folder');
  });

  test(`should copy folder (my corezoid) in company folder`, async () => {
    const response = await apiMyCorezoid.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolderMC,

        obj_to_type: `folder`,
        obj_to_id: newFolder,
        obj_type: 'folder',
        title: 'CopyFolderToCompany',
        with_aliases: false,
        async: false,
        from_company_id: null,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_copy');
    copyFolderToCompany = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copyObjectSync, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseListFolder = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.request_proc).toBe('ok');
    expect(
      (responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyFolderToCompany).obj_type,
    ).toEqual('folder');
  });

  test(`should copy dashboard (company) in folder`, async () => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newDashboard,

        obj_to_type: `folder`,
        obj_to_id: newFolderForCopy,
        obj_type: 'dashboard',
        title: 'CopyDash',
        with_aliases: false,
        async: true,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_copy');
    expect(response.body.ops[0].hash).toBeString();
    expect(response.body.ops[0].statistics_id).toContain(`zip`);
    SchemaValidator.validate(copyAsync, response.body);

    await new Promise(r => setTimeout(r, 6000));

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolderForCopy,
        company_id,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.request_proc).toBe('ok');
    copyDash = (responseListFolder.body.ops[0].list as Array<any>).find(item => item.title === 'CopyDash').obj_id;
    expect((responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyDash).title).toEqual(
      'CopyDash',
    );
    expect((responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyDash).obj_type).toEqual(
      'dashboard',
    );
  });

  test(`should copy dashboard (company) in 0`, async () => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newDashboard,

        obj_to_type: `folder`,
        obj_to_id: 0,
        obj_type: 'dashboard',
        title: 'CopyDash1',
        with_aliases: false,
        async: false,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_copy');
    copyDash = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copyObjectSync, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.request_proc).toBe('ok');
    expect((responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyDash).title).toEqual(
      'CopyDash1',
    );
    expect((responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyDash).obj_type).toEqual(
      'dashboard',
    );
  });

  test(`should copy instance (company) in folder`, async () => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newInstance,

        obj_to_type: `folder`,
        obj_to_id: newFolderForCopy,
        obj_type: 'instance',
        title: 'CopyInstance',
        with_aliases: false,
        async: true,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_copy');
    expect(response.body.ops[0].hash).toBeString();
    expect(response.body.ops[0].statistics_id).toContain(`zip`);
    SchemaValidator.validate(copyAsync, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolderForCopy,
        company_id,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.request_proc).toBe('ok');
    copyInstance = (responseListFolder.body.ops[0].list as Array<any>).find(item => item.title === 'CopyInstance')
      .obj_id;
    expect(
      (responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyInstance).obj_type,
    ).toEqual('instance');
  });

  test(`should copy insnance (company) in folder 0`, async () => {
    const response = await apiMyCorezoid.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newInstance,

        obj_to_type: `folder`,
        obj_to_id: 0,
        obj_type: 'instance',
        title: 'CopyInstance',
        with_aliases: false,
        async: false,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_copy');
    copyInstance = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copyObjectSync, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseListFolder = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.request_proc).toBe('ok');
    expect(
      (responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyInstance).obj_type,
    ).toEqual('instance');
  });

  test(`should copy folder in stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolder,

        obj_to_type: `stage`,
        obj_to_id: newStage,
        obj_type: 'folder',
        title: 'CopyFolderToStage',
        with_aliases: false,
        async: false,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_copy');
    copyFolderToStage = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copyObjectSync, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newStage,
        company_id,
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.request_proc).toBe('ok');
    expect(
      (responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyFolderToStage).obj_type,
    ).toEqual('folder');
  });

  test(`should copy stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newStage,

        obj_to_type: `project`,
        obj_to_id: newProject,
        obj_type: 'stage',
        title: copyTitle,
        short_name: copySName,
        ignore_errors: true,
        with_aliases: false,
        async: false,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_copy');
    copyStage = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copyObjectSync, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.request_proc).toBe('ok');
    expect(
      (responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyStage).obj_type,
    ).toEqual('stage');
  });

  test(`should copy stage (ignore_errors: false/async: false)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newStage,

        obj_to_type: `project`,
        obj_to_id: newProject,
        obj_type: 'stage',
        title: copyTitle,
        short_name: copySName,
        ignore_errors: false,
        with_aliases: false,
        async: false,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].errors[0].obj).toBe('stage');
    expect(response.body.ops[0].errors[0].errors).toBeArray();
    SchemaValidator.validate(copyAsyncErrors, response.body);
  });

  test(`should copy project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newProject,

        obj_to_type: `folder`,
        obj_to_id: 0,
        obj_type: 'project',
        title: copyTitle,
        short_name: copySName,
        ignore_errors: true,
        with_aliases: false,
        async: false,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_copy');
    copyProject = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copyObjectSync, response.body);

    await new Promise(r => setTimeout(r, 2000));

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECTS,
        obj_id: 0,
        company_id,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.request_proc).toBe('ok');
    expect(
      (responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyProject).obj_type,
    ).toEqual('project');
  });

  test(`should copy folder with alias (in newCompany)`, async () => {
    const createCompany = await apiUser.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,
        name: `Company_${Date.now()}`,
        description: `Company_${Date.now()}`,
        site: 'corezoid.com',
      }),
    );
    expect(createCompany.status).toBe(200);
    expect(createCompany.body.ops[0].proc).toBe('ok');
    newCompany = createCompany.body.ops[0].obj_id;

    const response = await apiS.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolderForCopy,

        obj_to_type: `folder`,
        obj_to_id: 0,
        obj_type: 'folder',
        title: 'CopyFolder',
        with_aliases: true,
        async: false,
        from_company_id: company_id,
        to_company_id: newCompany,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_copy');
    copyFolderNew = response.body.ops[0].scheme[0].obj_id;
    SchemaValidator.validate(copyObjectSync, response.body);

    await new Promise(r => setTimeout(r, 4000));

    const responseListFolder = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id: newCompany,
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.request_proc).toBe('ok');
    expect(
      (responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === copyFolderNew).obj_type,
    ).toEqual('folder');

    const responseListFolderCopy = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: copyFolderNew,
        company_id: newCompany,
      }),
    );
    expect(responseListFolderCopy.status).toBe(200);
    expect(responseListFolderCopy.body.request_proc).toBe('ok');
    expect(responseListFolderCopy.body.ops[0].list).toBeArrayOfSize(7);

    const responseListAlias = await apiS.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id: newCompany,
      }),
    );
    expect(responseListAlias.status).toBe(200);
    expect(responseListAlias.body.request_proc).toBe('ok');
    expect(responseListAlias.body.ops[0].list[0].title).toBe(titleAlias);
  });

  afterAll(async () => {
    const ResponseDeleteConvMC = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConvMC,
      }),
    );
    expect(ResponseDeleteConvMC.status).toBe(200);

    const ResponseDeleteConvMCCopy = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: copyConvMC,
      }),
    );
    expect(ResponseDeleteConvMCCopy.status).toBe(200);

    const ResponseDeleteConvCopy = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: copyConv,
        company_id,
      }),
    );
    expect(ResponseDeleteConvCopy.status).toBe(200);

    const ResponseDeleteAlias = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
      }),
    );
    expect(ResponseDeleteAlias.status).toBe(200);

    const ResponseDeleteConvInstance = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: copyInstance,
        company_id,
      }),
    );
    expect(ResponseDeleteConvInstance.status).toBe(200);

    const ResponseDeleteDashboardCopy = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: copyDash,
        company_id,
      }),
    );
    expect(ResponseDeleteDashboardCopy.status).toBe(200);

    const ResponseDeleteFolderCopy = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: copyFolder,
        company_id,
      }),
    );
    expect(ResponseDeleteFolderCopy.status).toBe(200);

    const ResponseDeleteFolderMCCopy = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolderMCforCopy,
      }),
    );
    expect(ResponseDeleteFolderMCCopy.status).toBe(200);

    const ResponseDeleteFolderMC = await apiMyCorezoid.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolderMC,
      }),
    );
    expect(ResponseDeleteFolderMC.status).toBe(200);

    const ResponseDeleteFolderCopyMC = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: copyFolderMC,
      }),
    );
    expect(ResponseDeleteFolderCopyMC.status).toBe(200);

    const ResponseDeleteFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolder,
        company_id,
      }),
    );
    expect(ResponseDeleteFolder.status).toBe(200);

    const responseDeleteProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(responseDeleteProject.status).toBe(200);

    const responseDeleteProjectCopy = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: copyProject,
        company_id,
      }),
    );
    expect(responseDeleteProjectCopy.status).toBe(200);

    const responseDel = await userToken.request({
      method: Method.DELETE,
      url: `${hostSS}face/api/1/workspaces/${newCompany}`,
    });
    expect(responseDel.status).toBe(200);
  });
});
