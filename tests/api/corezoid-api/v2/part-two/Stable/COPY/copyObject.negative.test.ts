import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import faker from 'faker';

describe('copy object (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newFolder: string | number;
  let newFolderForCopy: string | number;
  let company_id: any;
  let newProject: string | number;
  let newStage: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const createFolderResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        title: `folder_${Date.now()}`,
      }),
    );
    expect(createFolderResponse.status).toBe(200);
    newFolderForCopy = +createFolderResponse.body.ops[0].obj_id;

    const createfolderResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        title: `conv_${Date.now()}`,
        folder_id: newFolderForCopy,
      }),
    );
    expect(createfolderResponse.status).toBe(200);
    newFolder = +createfolderResponse.body.ops[0].obj_id;

    const createConvResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `conv_${Date.now()}`,
        folder_id: newFolderForCopy,
      }),
    );
    expect(createConvResponse.status).toBe(200);

    const createDashboardResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        folder_id: newFolderForCopy,
        title: `Dashboard_${Date.now()}`,
        time_range: { select: 'online', start: 1597847260, stop: 1597847260, timezone_offset: -180 },
      }),
    );
    expect(createDashboardResponse.status).toBe(200);

    const createInstanceResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,

        folder_id: newFolder,
        title: `INSTANCE_${Date.now()}`,
        status: 'active',
        instance_type: 'db_call',
        obj_type: 0,
        data: {
          driver: 'postgres',
          database: 'rogue.db.elephantsql.com',
          host: 'rogue.db.elephantsql.com',
          port: '5432',
          username: 'kykfopeu',
          password: 'sNvY2zNTzi_ZQADDpIRCOhSNqGRHfE_w',
          ssl: false,
          timeoutMs: 3000,
        },
      }),
    );
    expect(createInstanceResponse.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: `project${Date.now()}`,
        description: 'test',
        stages: ['production'],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: `stage-${Date.now()}`,
        description: 'test',
        project_id: newProject,
      }),
    );
    expect(responseStage.status).toBe(200);
    expect(responseStage.body.ops[0].obj).toBe('stage');
    newStage = responseStage.body.ops[0].obj_id;
  });

  test.each([
    [1, 'access denied'],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [0, 'process does not exists'],
    [undefined, `Key 'obj_id' is required`],
  ])(`shouldn't copy conv zip with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id,

        obj_to_type: `folder`,
        obj_to_id: 0,
        obj_type: 'conv',
        title: 'CopyConvMC1',
        with_aliases: false,
        async: false,
        from_company_id: company_id,
        to_company_id: company_id,
        ignore_errors: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    ['test', 'Value is not valid'],
    [true, 'Value is not valid'],
    [null, 'Value is not valid'],
    ['', 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
  ])(`shouldn't copy conv zip with invalid obj_id '%s'`, async (obj_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id,

        obj_to_type: `folder`,
        obj_to_id: 0,
        obj_type: 'conv',
        title: 'CopyConvMC1',
        with_aliases: false,
        async: false,
        from_company_id: company_id,
        to_company_id: company_id,
        ignore_errors: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(reason);
  });

  test.each([
    [1, 'access denied'],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    ['test', `Key 'folder_id' is required or Value is not valid`],
    [true, `Key 'folder_id' is required or Value is not valid`],
    [null, `Key 'folder_id' is required or Value is not valid`],
    ['', `Key 'folder_id' is required or Value is not valid`],
    [[], `Key 'folder_id' is required or Value is not valid`],
    [{}, `Key 'folder_id' is required or Value is not valid`],
    [undefined, `Key 'folder_id' is required or Key 'obj_to_id' is required`],
  ])(`shouldn't copy conv zip with invalid obj_to_id '%s'`, async (obj_to_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolder,
        obj_to_type: `folder`,
        obj_to_id,
        obj_type: 'folder',
        title: 'CopyConvMC1',
        with_aliases: false,
        async: false,
        from_company_id: company_id,
        to_company_id: company_id,
        ignore_errors: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [1, `Key 'folder_id' is required or Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Key 'folder_id' is required or Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Key 'folder_id' is required or Value '0' is not valid. Type of value is not 'binary'`],
    [
      'test',
      `Key 'folder_id' is required or Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"folder\\\">>]\">>`,
    ],
    [true, `Key 'folder_id' is required or Value 'true' is not valid. Type of value is not 'binary'`],
    [null, `Key 'folder_id' is required or Value 'null' is not valid. Type of value is not 'binary'`],
    [
      '',
      `Key 'folder_id' is required or Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"project\\\">>,<<\\\"stage\\\">>,<<\\\"folder\\\">>]\">>`,
    ],
    [[], `Key 'folder_id' is required or Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Key 'folder_id' is required or Value '{[]}' is not valid. Type of value is not 'binary'`],
    [undefined, `Key 'folder_id' is required or Key 'obj_to_type' is required`],
  ])(`shouldn't copy conv zip with invalid obj_to_type '%s'`, async (obj_to_type, reason) => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolder,

        obj_to_type,
        obj_to_id: newFolderForCopy,
        obj_type: 'conv',
        title: 'CopyConvMC1',
        with_aliases: false,
        async: false,
        from_company_id: company_id,
        to_company_id: company_id,
        ignore_errors: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([[undefined, `Key 'obj_type' is required`]])(
    `shouldn't copy conv zip with invalid obj_type '%s'`,
    async (obj_type, reason) => {
      const response = await api.request(
        createRequestWithOps({
          obj: OBJ_TYPE.OBJ_COPY,
          obj_id: newFolder,

          obj_to_type: 'folder',
          obj_to_id: newFolderForCopy,
          obj_type,
          title: 'CopyConvMC1',
          with_aliases: false,
          async: false,
          from_company_id: company_id,
          to_company_id: company_id,
          ignore_errors: true,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"config\\\">>,\\n <<\\\"project\\\">>,<<\\\"stage\\\">>]\">>`,
    ],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [
      '',
      `Value '<<>>' is not valid. Value is not in allowed list <<\"[<<\\\"conv\\\">>,<<\\\"folder\\\">>,<<\\\"dashboard\\\">>,<<\\\"instance\\\">>,<<\\\"config\\\">>,\\n <<\\\"project\\\">>,<<\\\"stage\\\">>]\">>`,
    ],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't copy conv zip with invalid obj_type '%s'`, async (obj_type, reason) => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolder,

        obj_to_type: 'folder',
        obj_to_id: newFolderForCopy,
        obj_type,
        title: 'CopyConvMC1',
        with_aliases: false,
        async: false,
        from_company_id: company_id,
        to_company_id: company_id,
        ignore_errors: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(reason);
  });

  test.each([
    [1, `Value is not valid`],
    [-1, `Value is not valid`],
    [0, `Value is not valid`],
    ['test', `Value is not valid`],
    [null, `Value is not valid`],
    ['', `Value is not valid`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
  ])(`shouldn't copy conv zip with invalid async '%s'`, async (async, reason) => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolder,

        obj_to_type: 'folder',
        obj_to_id: newFolderForCopy,
        obj_type: 'folder',
        title: 'CopyConvMC1',
        short_name: 'test',
        with_aliases: false,
        async,
        from_company_id: company_id,
        to_company_id: company_id,
        ignore_errors: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(reason);
  });

  test.each([
    [1, `Value is not valid`],
    [-1, `Value is not valid`],
    [0, `Value is not valid`],
    ['test', `Value is not valid`],
    [null, `Value is not valid`],
    ['', `Value is not valid`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
  ])(`shouldn't copy conv zip with invalid ignore_errors '%s'`, async (ignore_errors, reason) => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolder,

        obj_to_type: 'folder',
        obj_to_id: newFolderForCopy,
        obj_type: 'folder',
        title: 'CopyConvMC1',
        short_name: 'test',
        with_aliases: false,
        async: true,
        from_company_id: company_id,
        to_company_id: company_id,
        ignore_errors,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(reason);
  });

  test.each([
    [1, `access denied`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    ['test', `Value is not valid or Key 'obj_to_id' is required`],
    [null, `Value is not valid or Key 'obj_to_id' is required`],
    [true, `Value is not valid or Key 'obj_to_id' is required`],
    ['', `Value is not valid or Key 'obj_to_id' is required`],
    [[], `Value is not valid or Key 'obj_to_id' is required`],
    [{}, `Value is not valid or Key 'obj_to_id' is required`],
  ])(`shouldn't copy conv zip with invalid folder_id '%s'`, async (folder_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolder,

        folder_id,
        obj_type: 'folder',
        title: 'CopyConvMC1',
        short_name: 'test',
        with_aliases: false,
        async: true,
        from_company_id: company_id,
        to_company_id: company_id,
        ignore_errors: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [1, `Value is not valid`],
    [-1, `Value is not valid`],
    [0, `Value is not valid`],
    [true, `Value is not valid`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
  ])(`shouldn't copy conv zip with invalid from_company_id '%s'`, async (from_company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolder,

        obj_to_type: 'folder',
        obj_to_id: newFolderForCopy,
        obj_type: 'folder',
        title: 'CopyConvMC1',
        with_aliases: false,
        async: false,
        from_company_id,
        to_company_id: company_id,
        ignore_errors: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(reason);
  });

  test.each([
    [1, `Value is not valid`],
    [-1, `Value is not valid`],
    [0, `Value is not valid`],
    [true, `Value is not valid`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
  ])(`shouldn't copy conv zip with invalid to_company_id '%s'`, async (to_company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolder,

        obj_to_type: 'folder',
        obj_to_id: newFolderForCopy,
        obj_type: 'folder',
        title: 'CopyConvMC1',
        with_aliases: false,
        async: false,
        from_company_id: null,
        to_company_id,
        ignore_errors: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(reason);
  });

  test.each([[undefined, `Key 'title' is required`]])(
    `shouldn't copy conv zip with invalid title '%s'`,
    async (title, reason) => {
      const response = await api.request(
        createRequestWithOps({
          obj: OBJ_TYPE.OBJ_COPY,
          obj_id: newFolder,

          folder_id: newFolderForCopy,
          obj_type: 'folder',
          title,
          short_name: 'test',
          with_aliases: false,
          async: true,
          from_company_id: company_id,
          to_company_id: company_id,
          ignore_errors: true,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      expect(response.body.ops[0].description).toEqual(reason);
    },
  );

  test.each([
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [faker.random.alphaNumeric(256), `Value is not valid. Value's byte_size is more than maximum allowed: 255`],
  ])(`shouldn't copy conv zip with invalid title '%s'`, async (title, reason) => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolder,

        folder_id: newFolderForCopy,
        obj_type: 'folder',
        title,
        short_name: 'test',
        with_aliases: false,
        async: true,
        from_company_id: company_id,
        to_company_id: company_id,
        ignore_errors: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(reason);
  });

  test.each([
    [
      1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      -1,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      0,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      null,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      true,
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      'e_1',
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      [],
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      {},
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
    [
      faker.random.alphaNumeric(256),
      `Only letters (a-z), numbers (0-9), and dashes  are allowed for short name, and length must be less than 127 symbols`,
    ],
  ])(`shouldn't copy stage zip with invalid short_name '%s'`, async (short_name, reason) => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newStage,

        folder_id: newProject,
        obj_type: 'stage',
        title: 'test',
        short_name,
        with_aliases: false,
        async: true,
        from_company_id: company_id,
        to_company_id: company_id,
        ignore_errors: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(reason);
  });

  test.each([
    [1, `Value is not valid`],
    [-1, `Value is not valid`],
    [0, `Value is not valid`],
    [null, `Value is not valid`],
    ['test', `Value is not valid`],
    ['', `Value is not valid`],
    [[], `Value is not valid`],
    [{}, `Value is not valid`],
  ])(`shouldn't copy conv zip with invalid with_aliases '%s'`, async (with_aliases, reason) => {
    const response = await api.request(
      createRequestWithOps({
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolder,

        folder_id: newFolderForCopy,
        obj_type: 'folder',
        title: 'folder',
        short_name: 'test',
        with_aliases,
        async: true,
        from_company_id: company_id,
        to_company_id: company_id,
        ignore_errors: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].errors.description).toEqual(reason);
  });

  afterAll(async () => {
    const ResponseDeleteFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolderForCopy,
        company_id,
      }),
    );
    expect(ResponseDeleteFolder.status).toBe(200);

    const responseDeleteProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(responseDeleteProject.status).toBe(200);
  });
});
