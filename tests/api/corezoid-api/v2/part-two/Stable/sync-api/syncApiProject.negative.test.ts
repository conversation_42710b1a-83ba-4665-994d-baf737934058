import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { ApiKey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('SyncApi Task Project (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: string | number;
  let nodeLogic: string;
  let nodeStart: string;
  let nodeFinal: string;
  let company_id: any;
  let newNodeApi: string;
  let newAlias: string | number;
  let aliasShortName: string;
  let projectShortName: string;
  let stageShortName: string;
  let newProject: string | number;
  let newStage: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    aliasShortName = `alias-${Date.now()}`;
    projectShortName = `project-${Date.now()}`;
    stageShortName = `stage-${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: projectShortName,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,

        title: `Stage_${Date.now()}`,
        short_name: stageShortName,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStage = responseStage.body.ops[0].obj_id;

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        project_id: newProject,
        stage_id: newStage,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
      }),
    );
    newConv = responseConv.body.ops[0].obj_id;

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    nodeLogic = responseList.body.ops[0].list[1].obj_id;
    nodeStart = responseList.body.ops[0].list[0].obj_id;
    nodeFinal = responseList.body.ops[0].list[2].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeLogic,
        company_id,

        conv_id: newConv,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'POST',
            url: '{{__callback_url}}',
            extra: { a: '{{bookstore}}' },
            extra_type: { a: 'array' },
            max_theads: '',
            err_node_id: '',
            extra_headers: {},
            send_sys: false,
            cert_pem: '',
            content_type: 'application/json',
          },
          { node_title: 'final', to_node_id: nodeFinal, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );

    const responseNewNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        title: 'api_sync',
        conv_id: newConv,
        obj_type: 0,
        version: 22,
      }),
    );
    newNodeApi = responseNewNode.body.ops[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeStart,
        company_id,

        conv_id: newConv,
        title: 'start',
        description: '',
        obj_type: 1,
        logics: [{ to_node_id: newNodeApi, type: 'go' }],
        semaphors: [],
        version: 22,
      }),
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: newNodeApi,
        company_id,

        conv_id: newConv,
        title: 'api',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'GET',
            url: 'https://www.w3schools.com/php/books.xml',
            extra: {},
            extra_type: {},
            max_theads: '',
            err_node_id: '',
            extra_headers: {},
            send_sys: false,
            cert_pem: '',
            content_type: 'text/xml',
          },
          { to_node_id: nodeLogic, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        conv_id: newConv,
        version: 22,
      }),
    );

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        short_name: aliasShortName,
        description: `aliasDesk-${Date.now()}`,
        title: `aliasTitle-${Date.now()}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    newAlias = response.body.ops[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,

        obj_to_id: newConv,
        obj_to_type: 'conv',
        link: true,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
  });

  test.each([
    [null, `Value 'null' is not valid stage_id or Key 'stage_short_name' is required`],
    [true, `Value 'true' is not valid stage_id or Key 'stage_short_name' is required`],
    ['', `Value '' is not valid stage_id or Key 'stage_short_name' is required`],
    ['test', `Value 'test' is not valid stage_id or Key 'stage_short_name' is required`],
    [{}, `Value '{[]}' is not valid stage_id or Key 'stage_short_name' is required`],
    [[], `Value '' is not valid stage_id or Key 'stage_short_name' is required`],
  ])(`shouldn't create task syncApi with invalid stage_id'%s'`, async (param, reason) => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        conv_id: newConv,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
        project_id: newProject,
        stage_id: param,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid project_id or Key 'project_short_name' is required`],
    [true, `Value 'true' is not valid project_id or Key 'project_short_name' is required`],
    ['', `Value '' is not valid project_id or Key 'project_short_name' is required`],
    ['test', `Value 'test' is not valid project_id or Key 'project_short_name' is required`],
    [{}, `Value '{[]}' is not valid project_id or Key 'project_short_name' is required`],
    [[], `Value '' is not valid project_id or Key 'project_short_name' is required`],
    [-1, `Value '-1' is not valid project_id or Key 'project_short_name' is required`],
  ])(`shouldn't create task syncApi with invalid project_id'%s'`, async (param, reason) => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        conv_id: newConv,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
        project_id: param,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Wrong object reference. Validation error`],
    [true, `Wrong object reference. Validation error`],
    ['', `Wrong object reference. Validation error`],
    ['test', `Wrong object reference. Validation error`],
    [{}, `Wrong object reference. Validation error`],
    [[], `Wrong object reference. Validation error`],
    [-1, `conveyor_not_found`],
    [0, `conveyor_not_found`],
    [1, `access_denied`],
    [undefined, `Wrong object reference. Validation error`],
  ])(`shouldn't create task syncApi with invalid conv_id '%s'`, async (param, reason) => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        conv_id: param,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Wrong object reference. Validation error`],
    [true, `Wrong object reference. Validation error`],
    ['', `Wrong object reference. Validation error`],
    ['test', `Object alias not found`],
    [{}, `Wrong object reference. Validation error`],
    [[], `Wrong object reference. Validation error`],
    [-1, `Wrong object reference. Validation error`],
    [0, `Wrong object reference. Validation error`],
    [1, `Wrong object reference. Validation error`],
    [undefined, `Wrong object reference. Validation error`],
  ])(`shouldn't create task syncApi with invalid obj_alias'%s'`, async (param, reason) => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        obj_alias: param,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Wrong object reference. Validation error`],
    [true, `Wrong object reference. Validation error`],
    ['', `Wrong object reference. Validation error`],
    ['test', `Object alias not found`],
    [{}, `Wrong object reference. Validation error`],
    [[], `Wrong object reference. Validation error`],
    [-1, `Wrong object reference. Validation error`],
    [0, `Wrong object reference. Validation error`],
    [1, `Wrong object reference. Validation error`],
    [undefined, `Wrong object reference. Validation error`],
  ])(`shouldn't create task syncApi with invalid obj_alias project_short_name'%s'`, async (param, reason) => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        obj_alias: param,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
        project_short_name: projectShortName,
        stage_short_name: stageShortName,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Stage not found`],
    [true, `Stage not found`],
    ['', `Stage not found`],
    ['test', `Project is not found`],
    [{}, `Stage not found`],
    [[], `Stage not found`],
    [-1, `Stage not found`],
    [0, `Stage not found`],
    [1, `Stage not found`],
    [undefined, `Stage not found`],
  ])(`shouldn't create task syncApi with invalid project_short_name'%s'`, async (param, reason) => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        obj_alias: aliasShortName,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
        project_short_name: param,
        stage_short_name: stageShortName,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, `Object alias not found`],
    [true, `Object alias not found`],
    ['', `Object alias not found`],
    ['test', `Stage not found`],
    [{}, `Object alias not found`],
    [[], `Object alias not found`],
    [-1, `Object alias not found`],
    [0, `Object alias not found`],
    [1, `Object alias not found`],
    [undefined, `Object alias not found`],
  ])(`shouldn't create task syncApi with invalid stage_short_name'%s'`, async (param, reason) => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        obj_alias: aliasShortName,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
        project_short_name: projectShortName,
        stage_short_name: param,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
      }),
    );
  });
});
