import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { User } from '../../../../../../../infrastructure/model/User';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { createAuthUser, Method } from '../../../../../../../utils/request';

describe('SyncApi Task (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: string | number;
  let newConv1: string | number;
  let nodeLogic: string;
  let nodeStart: string;
  let nodeFinal: string;
  let companyId: any;
  let newNodeApi: string;
  let newApi: ApiKeyClient;
  let ApiKey2: string | number;
  let user: User;
  let cookieUser: any;
  let url_superadmin: string;

  beforeAll(async () => {
    url_superadmin = `${ConfigurationManager.getConfiguration().getSuperadminUrl()}`;
    user = await application.getAuthorizedUser({ company: {} }, 1);
    cookieUser = createAuthUser(user.cookieUser, 'cookie');
  });

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    companyId = apikey.companies[0].id;

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id: companyId,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: 0,
      }),
    );
    newConv = responseConv.body.ops[0].obj_id;

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id: companyId,
      }),
    );
    nodeLogic = responseList.body.ops[0].list[1].obj_id;
    nodeStart = responseList.body.ops[0].list[0].obj_id;
    nodeFinal = responseList.body.ops[0].list[2].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeLogic,
        company_id: companyId,
        conv_id: newConv,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'POST',
            url: '{{__callback_url}}',
            extra: { a: '{{bookstore}}' },
            extra_type: { a: 'array' },
            max_theads: '',
            err_node_id: '',
            extra_headers: {},
            send_sys: false,
            cert_pem: '',
            content_type: 'application/json',
          },
          { node_title: 'final', to_node_id: nodeFinal, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );

    const responseNewNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id: companyId,
        title: 'api_sync',
        conv_id: newConv,
        obj_type: 0,
        version: 22,
      }),
    );
    newNodeApi = responseNewNode.body.ops[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeStart,
        company_id: companyId,
        conv_id: newConv,
        title: 'start',
        description: '',
        obj_type: 1,
        logics: [{ to_node_id: newNodeApi, type: 'go' }],
        semaphors: [],
        version: 22,
      }),
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: newNodeApi,
        company_id: companyId,
        conv_id: newConv,
        title: 'api',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'GET',
            url: 'https://www.w3schools.com/php/books.xml',
            extra: {},
            extra_type: {},
            max_theads: '',
            err_node_id: '',
            extra_headers: {},
            send_sys: false,
            cert_pem: '',
            content_type: 'text/xml',
          },
          { to_node_id: nodeLogic, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id: companyId,
        conv_id: newConv,
        version: 22,
      }),
    );

    const CreateKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id: companyId,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(200);
    const user = CreateKeyResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newApi = application.getApiKeyClient(newApiKey);
    ApiKey2 = +newApiKey.id;
  });

  test.each([
    [null, 'Wrong object reference. Validation error'],
    [true, 'Wrong object reference. Validation error'],
    ['', 'Wrong object reference. Validation error'],
    ['test', 'Wrong object reference. Validation error'],
    [{}, 'Wrong object reference. Validation error'],
    [[], 'Wrong object reference. Validation error'],
    [0, `conveyor_not_found`],
    [1, `access_denied`],
    [-1, `conveyor_not_found`],
    [undefined, `Wrong object reference. Validation error`],
  ])(`shouldn't create task syncApi with invalid conv_id '%s'`, async (param, reason) => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: param,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, 'Wrong object reference. Validation error'],
    [true, 'Wrong object reference. Validation error'],
    ['', 'Wrong object reference. Validation error'],
    ['test', 'Object alias not found'],
    [{}, 'Wrong object reference. Validation error'],
    [[], 'Wrong object reference. Validation error'],
    [0, `Wrong object reference. Validation error`],
    [1, `Wrong object reference. Validation error`],
    [-1, `Wrong object reference. Validation error`],
    [undefined, `Wrong object reference. Validation error`],
  ])(`shouldn't create task syncApi with invalid obj_alias'%s'`, async (param, reason) => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        obj_alias: param,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, 'conveyor_not_found'],
    [true, 'Value is not valid'],
    ['', 'Value is not valid'],
    ['test', 'conveyor_not_found'],
    [{}, 'Value is not valid'],
    [[], 'Value is not valid'],
    [0, `Value is not valid`],
    [1, `Value is not valid`],
    [-1, `Value is not valid`],
  ])(`shouldn't create task syncApi with invalid company_id '%s'`, async (param, reason) => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: param,
        conv_id: newConv,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [true, 'Value is not valid'],
    ['', 'Value is not valid'],
    [{}, 'Value is not valid'],
    [[], 'Value is not valid'],
    [0, `Value is not valid`],
    [1, `Value is not valid`],
    [-1, `Value is not valid`],
  ])(`shouldn't create task syncApi with invalid ref '%s'`, async (param, reason) => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: param,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [null, 'error', `Invalid value of the 'data' key`],
    [true, 'error', `Invalid value of the 'data' key`],
    ['', 'error', `Invalid value of the 'data' key`],
    ['test', 'error', `Invalid value of the 'data' key`],
    [0, 'error', `Invalid value of the 'data' key`],
    [1, 'error', `Invalid value of the 'data' key`],
    [-1, 'error', `Invalid value of the 'data' key`],
    [['test'], 'error', `Invalid value of the 'data' key`],
    [[1], 'error', `Invalid value of the 'data' key`],
    [[null], 'error', `Invalid value of the 'data' key`],
    [[true], 'error', `Invalid value of the 'data' key`],
    [[], 'error', `Invalid value of the 'data' key`],
  ])(`shouldn't create task syncApi with invalid data'%s'`, async (param, proc, reason) => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        data: param,
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual(proc);
    expect(response.body.ops[0].description[0].description).toContain(reason);
  });

  test.each([
    [undefined, 'error', `Key 'data' is required`],
    [[{}], 'error', 'Incorrect body'],
  ])(`shouldn't create task syncApi with invalid data'%s'`, async (param, proc, reason) => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        data: param,
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual(proc);
    expect(response.body.ops[0].description).toContain(reason);
  });

  test(`shouldn't create task syncApi with invalid data'{}'`, async () => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        data: {},
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(response.status).toBeOneOf([502, 504]);
  });

  test(`shouldn't create task sync_api - access_denied`, async () => {
    const responseTask = await newApi.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('error');
    expect(responseTask.body.ops[0].description).toEqual('access_denied');
  });

  test.skip(`shouldn't create task sync_api - user limit`, async () => {
    const data = {
      type: 'set',
      obj: 'limits',
      obj_id: ApiKey2,
      obj_type: 'user',
      company_id: companyId,
      user_id: null,
      limits: [{ type: 'rps', value: 0, edit: true }],
      force: false,
    };

    await cookieUser.request({
      method: Method.POST,
      url: `${url_superadmin}superadmin/api/1/json`,
      data,
    });

    const responseConv = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id: companyId,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: 0,
      }),
    );
    newConv1 = responseConv.body.ops[0].obj_id;

    const responseTask = await newApi.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv1,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('error');
    expect(responseTask.body.ops[0].description).toEqual('too many requests, you exceeded user limit 0/sec');
  });

  test(`shouldn't create task sync_api - conveyor_is_not_active`, async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV,
        company_id: companyId,
        obj_id: newConv,
        status: 'paused',
      }),
    );
    const responseTask = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('error');
    expect(responseTask.body.ops[0].description).toEqual('conveyor_is_not_active');
  });

  test(`shouldn't create task sync_api - conveyor_not_found`, async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id: companyId,
      }),
    );
    const responseTask = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('error');
    expect(responseTask.body.ops[0].description).toEqual('conveyor_not_found');
  });

  test(`shouldn't create task sync_api - Timeout for create task`, async () => {
    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id: companyId,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: 0,
      }),
    );
    newConv1 = responseConv.body.ops[0].obj_id;

    const responseTask = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv1,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBeOneOf([502, 504]);
  });

  test(`shouldn't create task sync_api - not_unical_ref`, async () => {
    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id: companyId,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: 0,
        conv_type: 'state',
      }),
    );
    newConv1 = responseConv.body.ops[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv1,
        data: { a: '1' },
        ref: `test`,
      }),
    );

    const responseTask = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv1,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `test`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('error');
    expect(responseTask.body.ops[0].description).toEqual('not unical reference');
  });

  test(`shouldn't create task syncApi with invalid body`, async () => {
    const response = await api.requestSyncApi(
      createRequestWithOps({
        op: [
          { type: 'create', conv_id: newConv, obj: 'task', action: 'user', company_id: companyId, data: {}, ref: '1' },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual('Invalid obj or type');
  });

  test(`shouldn't create task syncApi with Bad signature`, async () => {
    const newApiKey = {
      key: `5381`,
      secret: `5381`,
      companies: [],
      title: '',
      id: `5381`,
    } as ApiKey;
    newApi = application.getApiKeyClient(newApiKey);
    const responseTask = await newApi.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(401);
    expect(responseTask.body.ops[0].proc).toEqual('error');
    expect(responseTask.body.ops[0].description).toEqual(`Bad signature`);
  });

  afterAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id: companyId,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv1,
        company_id: companyId,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
        company_id: companyId,
      }),
    );
  });
});
