import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { <PERSON>pi<PERSON><PERSON> } from '../../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../../utils/corezoidRequest';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  companyTestCases,
  stringNotValidTestCases,
  minLength,
  maxLength,
} from '../../../../../../negativeCases';

describe('Sys Stat (negative)', () => {
  let api: ApiKeyClient;
  let apikey: Api<PERSON>ey;
  const endUnix = Math.round(+new Date() / 1000);
  const startUnix = endUnix - 150;
  let company_id: any;
  let valuesToSkip: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
  });

  test.each([...companyTestCases, ...minLength, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't show sys stat with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.SYS_STAT,
          company_id: input,
          order_field: 'opers',
          order_by: 'DESC',
          limit: 10,
          start: startUnix,
          end: endUnix,
          owner_id: 123,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0, -1];

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't show sys stat with invalid start '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field: 'opers',
        order_by: 'DESC',
        limit: 10,
        start: input,
        end: endUnix,
        owner_id: 123,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0, -1];

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't show sys stat with invalid end '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field: 'opers',
        order_by: 'DESC',
        limit: 10,
        start: startUnix,
        end: input,
        owner_id: 123,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't show sys stat with invalid order_field '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field: input,
        order_by: 'DESC',
        limit: 10,
        start: startUnix,
        end: endUnix,
        owner_id: 123,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't show sys stat with invalid order_by '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field: 'opers',
        order_by: input,
        limit: 10,
        start: startUnix,
        end: endUnix,
        owner_id: 123,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't show sys stat with invalid owner_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field: 'opers',
        order_by: 'DESC',
        limit: 10,
        start: startUnix,
        end: endUnix,
        owner_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't show sys stat with invalid conv_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,

        order_field: 'opers',
        order_by: 'DESC',
        limit: 10,
        start: startUnix,
        end: endUnix,
        owner_id: 123,
        conv_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...integerTestCases, ...minLength, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't show sys stat with invalid limit '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.SYS_STAT,
          company_id,
          order_field: 'opers',
          order_by: 'DESC',
          limit: input,
          start: startUnix,
          end: endUnix,
          owner_id: 123,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test(`shouldn't show sys stat with invalid limit more 15`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field: 'opers',
        order_by: 'DESC',
        limit: 16,
        start: startUnix,
        end: endUnix,
        owner_id: 123,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe(`Value is not valid. Value's limit is more than maximum allowed: 15`);
  });
});
