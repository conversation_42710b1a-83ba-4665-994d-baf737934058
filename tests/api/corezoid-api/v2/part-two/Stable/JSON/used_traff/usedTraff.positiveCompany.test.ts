import { ApiKeyClient } from '../../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../../application/Application';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../../application/api/SchemaValidator';
import showUsed_traffOpers from '../../../../../schemas/v2/actions-objects/activity-monitor/showUsed_traff.opers.schema.json';
import showUsed_traffTacts from '../../../../../schemas/v2/actions-objects/activity-monitor/showUsed_traff.tacts.schema.json';
import showUsed_traffTraff from '../../../../../schemas/v2/actions-objects/activity-monitor/showUsed_traff.traff.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../../utils/corezoidRequest';

describe('Used Traff in Company (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: string | number;
  let newConv2: string | number;
  let newApi: ApiKeyClient;
  let ApiKey2: string | number;
  const endUnix = Math.round(+new Date() / 1000);
  const startUnix = endUnix - 150;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const CreateKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(200);
    const user = CreateKeyResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newApi = application.getApiKeyClient(newApiKey);
    ApiKey2 = +newApiKey.id;

    const createConvResponse = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `conv_${Date.now()}`,
      }),
    );
    expect(createConvResponse.status).toBe(200);
    newConv = createConvResponse.body.ops[0].obj_id;

    const createConv2Response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `conv_${Date.now()}`,
      }),
    );
    expect(createConvResponse.status).toBe(200);
    newConv2 = createConv2Response.body.ops[0].obj_id;

    let taskforConv;
    for (taskforConv = 0; taskforConv < 5; taskforConv++) {
      await newApi.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          company_id,
          conv_id: newConv,
          data: { x: '1' },
          ref: `task_${Date.now()}`,
        }),
      );
    }

    let taskforConv2;
    for (taskforConv2 = 0; taskforConv2 < 7; taskforConv2++) {
      await newApi.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          company_id,
          conv_id: newConv2,
          data: { x: '1' },
          ref: `task_${Date.now()}`,
        }),
      );
    }
  });

  test(`should show used_traff opers`, async () => {
    await new Promise(r => setTimeout(r, 120000));
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER_TRAF,
        company_id,
        filter: 'opers',
        interval: 'minute',
        start: startUnix,
        end: endUnix,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list[0].opers).toBe(12);
    SchemaValidator.validate(showUsed_traffOpers, response.body);
  });

  test(`should show used_traff tacts`, async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER_TRAF,
        company_id,
        filter: 'tacts',
        interval: 'minute',
        start: `${startUnix}`,
        end: endUnix,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list[0].tacts).toBe(36);
    SchemaValidator.validate(showUsed_traffTacts, response.body);
  });

  test(`should show used_traff traff`, async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER_TRAF,
        company_id,
        filter: 'traff',
        interval: 'minute',
        start: startUnix,
        end: `${endUnix}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list[0].traff).toBe(0);
    SchemaValidator.validate(showUsed_traffTraff, response.body);
  });

  test(`should show used_traff interval: hour`, async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER_TRAF,
        company_id,
        filter: 'tacts',
        interval: 'hour',
        start: `${startUnix}`,
        end: endUnix,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list[0].tacts).toBe(36);
    SchemaValidator.validate(showUsed_traffTacts, response.body);
  });

  test(`should show used_traff interval: day`, async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER_TRAF,
        company_id,
        filter: 'tacts',
        interval: 'day',
        start: `${startUnix}`,
        end: endUnix,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list[0].tacts).toBe(36);
    SchemaValidator.validate(showUsed_traffTacts, response.body);
  });

  test(`should show used_traff interval: month`, async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER_TRAF,
        company_id,
        filter: 'tacts',
        interval: 'month',
        start: `${startUnix}`,
        end: endUnix,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list[0].tacts).toBe(36);
    SchemaValidator.validate(showUsed_traffTacts, response.body);
  });

  afterAll(async () => {
    const ResponseDeleteConv = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv,
        company_id,
      }),
    );
    expect(ResponseDeleteConv.status).toBe(200);

    const ResponseDeleteConv2 = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv2,
        company_id,
      }),
    );
    expect(ResponseDeleteConv2.status).toBe(200);

    const ResponseDeleteApiKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
        company_id,
      }),
    );
    expect(ResponseDeleteApiKey.status).toBe(200);
  });
});
