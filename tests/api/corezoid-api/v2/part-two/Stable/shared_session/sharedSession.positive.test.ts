import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';

describe('Shared session (positive)', () => {
  let api_s: ApiKeyClient;
  let apikey_s: Api<PERSON><PERSON>;

  beforeAll(async () => {
    apikey_s = await application.getApiKeySuper();
    api_s = application.getApiKeyClient(apikey_s);
  });

  test.skip(`Set shared session with required param`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'set',
      obj: 'shared_session',
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    expect(response.body.data).toBeArray();
  });

  test.skip(`Show shared session`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'show',
      obj: 'shared_session',
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
  });

  test.skip(`Delete shared session`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'delete',
      obj: 'shared_session',
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
  });
});
