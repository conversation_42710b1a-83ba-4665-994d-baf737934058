import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { integerTestCases, undefinedTestCase } from '../../../../../negativeCases';

describe('Shared session (negative)', () => {
  let api_s: ApiKeyClient;
  let apikey_s: ApiKey;

  beforeAll(async () => {
    apikey_s = await application.getApiKeySuper();
    api_s = application.getApiKeyClient(apikey_s);
  });

  test.skip.each([...integerTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]))(
    `shouldn't set shared session with invalid obj_id '%s'`,
    async (input, errors) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'set',
        obj: 'shared_session',
      });
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );
});
