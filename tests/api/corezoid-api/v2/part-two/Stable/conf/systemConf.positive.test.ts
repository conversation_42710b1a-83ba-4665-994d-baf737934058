import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import systemConf from '../../../../schemas/v2/conf/systemConf.Schema.json';
import { axiosInstance } from '../../../../../../../application/api/AxiosClient';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import faker from 'faker';

describe('System conf (positive & negative)', () => {
  const config = ConfigurationManager.getConfiguration();
  const host = config.getApiUrl();

  test(`should system conf`, async () => {
    const response = await axiosInstance({
      method: 'GET',
      url: `${host}system/conf`,
    });
    expect(response.status).toBe(200);
    expect(response.data.result).toBe('ok');
    SchemaValidator.validate(systemConf, response.data);
  });

  test(`shouldn't system conf with invalid conf`, async () => {
    const response = await axiosInstance({
      method: 'GET',
      url: `${host}system/${faker.random.words()}`,
    });
    expect(response.status).toBe(200);
    expect(response.data.result).toBe('error');
    expect(response.data.description).toBe('System method not allowed');
  });

  test(`shouldn't system conf with invalid system`, async () => {
    const response = await axiosInstance({
      method: 'GET',
      url: `${host}${faker.random.words()}/conf`,
    });
    expect(response.status).toBe(200);
  });
});
