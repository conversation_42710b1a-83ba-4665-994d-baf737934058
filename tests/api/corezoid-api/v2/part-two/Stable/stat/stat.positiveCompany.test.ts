import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import showStatAllNodes from '../../../../schemas/v2/stat/showStatAllNodes.schema.json';
import showStatOneNodes from '../../../../schemas/v2/stat/showStatOneNodes.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj, requestListConv } from '../../../../../../../application/api/ApiObj';

describe('Stat in Company (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let conv_id: number;
  let dashboard_id: number;
  let newApi: ApiKeyClient;
  let ApiKey2: string | number;
  const endUnix = Math.round(+new Date() / 1000);
  const startUnix = endUnix - 150;
  let company_id: any;
  let process_node_ID: string | number;
  let final_node_ID: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const CreateKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(200);
    const user = CreateKeyResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newApi = application.getApiKeyClient(newApiKey);
    ApiKey2 = +newApiKey.id;

    const response = await requestCreateObj(newApi, OBJ_TYPE.CONV, company_id, `Api_Call`);
    conv_id = response.body.ops[0].obj_id;

    const responseList = await requestListConv(newApi, conv_id, company_id);
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseDashboard = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,

        title: `Dashboard_${Date.now()}`,
        description: 'test',
        time_range: {
          select: 'online',
          start: startUnix,
          stop: endUnix,
          timezone_offset: -180,
        },
      }),
    );
    expect(responseDashboard.status).toBe(200);
    dashboard_id = responseDashboard.body.ops[0].obj_id;

    let taskforConv;
    for (taskforConv = 0; taskforConv < 5; taskforConv++) {
      await newApi.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          company_id,
          conv_id: conv_id,
          data: { x: '1' },
          ref: `task_${Date.now()}`,
        }),
      );
    }
    await new Promise(r => setTimeout(r, 130000));
  });

  test(`should show stat with required param`, async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAT,
        conv_id: conv_id,
        group: 'all',
        interval: 'minute',
        start: startUnix,
        end: endUnix,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].data).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: 'start', in: 5, out: 5 })]),
    );
    expect(response.body.ops[0].data).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: 'final', in: 5, out: 0 })]),
    );
    expect(response.body.ops[0].data).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: 'process', in: 5, out: 5 })]),
    );
    SchemaValidator.validate(showStatAllNodes, response.body);
  });

  test(`should show stat with timezone_offset and one node`, async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAT,

        conv_id: conv_id,
        node_id: final_node_ID,
        group: 'time',
        interval: 'hour',
        start: startUnix,
        end: endUnix,
        timezone_offset: -180,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].data).toEqual(expect.arrayContaining([expect.objectContaining({ in: 5, out: 0 })]));
    SchemaValidator.validate(showStatOneNodes, response.body);
  });

  test(`should show stat with two nodes`, async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAT,

        conv_id: conv_id,
        node_ids: [final_node_ID, process_node_ID],
        group: 'all',
        interval: 'day',
        start: startUnix,
        end: endUnix,
        timezone_offset: -180,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].data).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: 'final', in: 5, out: 0 })]),
    );
    expect(response.body.ops[0].data).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: 'process', in: 5, out: 5 })]),
    );
    SchemaValidator.validate(showStatAllNodes, response.body);
  });

  test(`should show stat one node with timestamp=false`, async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAT,
        company_id,

        conv_id: conv_id,
        node_id: final_node_ID,
        group: 'time',
        interval: 'hour',
        start: startUnix,
        end: endUnix,
        timezone_offset: -180,
        timestamp: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].data).toEqual(expect.arrayContaining([expect.objectContaining({ in: 5, out: 0 })]));
    expect(response.body.ops[0].data[0].date).toBeString();
    SchemaValidator.validate(showStatOneNodes, response.body);
  });

  test(`should show stat one node with timestamp=true`, async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAT,
        company_id,

        conv_id: conv_id,
        node_id: final_node_ID,
        group: 'time',
        interval: 'hour',
        start: startUnix,
        end: endUnix,
        timezone_offset: -180,
        timestamp: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].data).toEqual(expect.arrayContaining([expect.objectContaining({ in: 5, out: 0 })]));
    expect(response.body.ops[0].data[0].date).toBeNumber();
    SchemaValidator.validate(showStatOneNodes, response.body);
  });

  test(`should show stat with two nodes timestamp=true`, async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.STAT,

        conv_id: conv_id,
        node_ids: [final_node_ID, process_node_ID],
        group: 'all',
        interval: 'day',
        start: startUnix,
        end: endUnix,
        timezone_offset: -180,
        timestamp: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].data).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: 'final', in: 5, out: 0 })]),
    );
    expect(response.body.ops[0].data).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: 'process', in: 5, out: 5 })]),
    );
    SchemaValidator.validate(showStatAllNodes, response.body);
  });

  afterAll(async () => {
    const ResponseDeleteConv = await requestDeleteObj(newApi, OBJ_TYPE.CONV, conv_id, company_id);
    expect(ResponseDeleteConv.status).toBe(200);

    const ResponseDeleteDashboard = await requestDeleteObj(newApi, OBJ_TYPE.DASHBOARD, dashboard_id, company_id);
    expect(ResponseDeleteDashboard.status).toBe(200);

    const ResponseDeleteApiKey = await requestDeleteObj(api, OBJ_TYPE.USER, ApiKey2, company_id);
    expect(ResponseDeleteApiKey.status).toBe(200);
  });
});
