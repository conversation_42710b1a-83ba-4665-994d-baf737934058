import { debug } from '../../../../../../../support/utils/logger';
import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { addMsg } from 'jest-html-reporters/helper';
import { SchemaValidator } from '../../../../../../../application/api/SchemaValidator';
import GetStat from '../../../../schemas/v2/download-upload/GetStatistics.Schema.json';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';

describe('Get object statistics (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let key: string;
  let secret: string;
  let newFolder: string | number;
  let newFolder2: string | number;
  let statistics_id: string | number;
  let hashUpload: string | number;
  let company_id: any;
  let hash: string | number;
  let conv_id: any;
  const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
  const scriptPath = 'tests/api/corezoid-api/sh/uploadObject.sh';
  const exec = promisify(execCallback);
  const scriptPathTask = 'tests/api/corezoid-api/sh/uploadTask.sh';
  const filename = 'upload-task.csv';

  function parseStdoutToJson(stdout: any): any {
    const jsonStartIndex = stdout.indexOf('{');
    const jsonString = stdout.slice(jsonStartIndex);
    return JSON.parse(jsonString);
  }

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key = apikey.key;
    secret = apikey.secret;

    const responseFolder = await requestCreateObj(api, OBJ_TYPE.FOLDER, company_id, `Folder_${Date.now()}`, 0);
    newFolder = responseFolder.body.ops[0].obj_id;

    const { stdout, stderr } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        FOLDER_ID: `${newFolder}`,
        VALIDATE_SCHEME: 'false',
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        COMPANY_ID: company_id,
        FILE_NAME: 'folder_for_test_upload_univer.suite_70conv.zip',
        ASYNC: 'true',
      },
    });
    expect(stderr).toBe(``);

    expect(stdout).toContain(`{"request_proc":"ok","ops":[{"obj":"obj_scheme","proc":"ok","hash"`);
    await addMsg({ message: JSON.stringify(stdout), context: '' });
    hashUpload = stdout.substr(76, 146);

    const responseConv = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Delay`, newFolder);
    conv_id = responseConv.body.ops[0].obj_id;
  });

  test('should GET statistics start upload folder with required param', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.STATISTICS,
        action: 'upload',
        obj_type: 'obj_scheme',
        id: `${hashUpload}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data[0].progress).toBe(0);
  });

  test('should GET statistics `progress > 0` upload folder', async () => {
    await new Promise(r => setTimeout(r, 1000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.STATISTICS,
        company_id,
        action: 'upload',
        obj_type: 'obj_scheme',
        id: `${hashUpload}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data[0].progress).toBeNumber();
    SchemaValidator.validate(GetStat, response.body);
  });

  test('should GET statistics `progress < 100` upload folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.STATISTICS,
        action: 'upload',
        obj_type: 'obj_scheme',
        id: `${hashUpload}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data[0].progress).toBeLessThan(99);
    expect(response.body.ops[0].data[0].progress).toBeNumber();
  });

  test('should GET statistics finish upload folder', async () => {
    await new Promise(r => setTimeout(r, 18000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.STATISTICS,
        company_id,
        action: 'upload',
        obj_type: 'obj_scheme',
        id: `${hashUpload}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data[0].progress).toBe(100);
    SchemaValidator.validate(GetStat, response.body);
  });

  test('should copy folder async', async () => {
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newFolder,
      }),
    );
    expect(responseList.status).toBe(200);
    newFolder2 = responseList.body.ops[0].list[0].obj_id;
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newFolder2,

        folder_id: newFolder,
        obj_type: 'folder',
        title: 'copyFolderAsync',
        ignore_errors: true,
        async: true,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('obj_copy');
    hash = response.body.ops[0].hash;
  });

  test('should GET statistics start copy folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.STATISTICS,
        action: 'copy',
        obj_type: 'obj_scheme',
        id: `${hash}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data[0].progress).toBe(0);
    SchemaValidator.validate(GetStat, response.body);
  });

  test('should GET statistics `progress > 0` copy folder', async () => {
    await new Promise(r => setTimeout(r, 3000));

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.STATISTICS,
        action: 'copy',
        obj_type: 'obj_scheme',
        id: `${hash}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data[0].progress).toBeGreaterThan(1);
    expect(response.body.ops[0].data[0].progress).toBeNumber();
    SchemaValidator.validate(GetStat, response.body);
  });

  test('should GET statistics `progress < 100` copy folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.STATISTICS,
        company_id,
        action: 'copy',
        obj_type: 'obj_scheme',
        id: `${hash}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data[0].progress).toBeLessThan(99);
    expect(response.body.ops[0].data[0].progress).toBeNumber();
  });

  test('should GET statistics finish copy folder', async () => {
    await new Promise(r => setTimeout(r, 18000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.STATISTICS,
        company_id,
        action: 'copy',
        obj_type: 'obj_scheme',
        id: `${hash}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data[0].progress).toBe(100);
    SchemaValidator.validate(GetStat, response.body);
  });

  test(`should download folder`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newFolder,
        company_id,
        obj_type: `folder`,
        with_alias: false,
        async: true,
        format: 'zip',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].statistics_id).toBeString();
    statistics_id = response.body.ops[0].statistics_id;
  });

  test.skip('should GET statistics start download folder', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.STATISTICS,
        company_id,
        action: 'download',
        obj_type: 'obj_scheme',
        id: `${statistics_id}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data[0].progress).toBe(0);
    SchemaValidator.validate(GetStat, response.body);
  });

  test('should GET statistics after download folder', async () => {
    await new Promise(r => setTimeout(r, 2000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.STATISTICS,
        action: 'download',
        obj_type: 'obj_scheme',
        id: `${statistics_id}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data[0].progress).toBe(100);
    SchemaValidator.validate(GetStat, response.body);
  });

  test('should GET statistics upload tasks', async () => {
    const { stdout } = await exec(scriptPathTask, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        CONV_ID: conv_id,
        ASYNC: 'true',
        DIVIDER: '|',
        ENCODING: 'utf-8',
        NAMES_IN_FIRST_ROW: 'false',
        COMPANY_ID: company_id,
        REFERENCE_COL_NAME: '',
        COLS_CONVERT_TO: ['object', 'array', 'boolean', 'number', 'string'].join(','),
        EXTRA_SYS_IP: 'false',
        EXTRA_SYS_FILENAME: 'false',
        FILE_NAME: filename,
      },
    });
    debug(stdout);
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.request_proc).toBe(`ok`);
    hash = jsonResponse.ops[0].hash;

    await new Promise(r => setTimeout(r, 1000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.STATISTICS,
        action: 'upload',
        obj_type: 'csv_upload',
        id: hash,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data[0].progress).toBe(100);
    SchemaValidator.validate(GetStat, response.body);
  });

  afterAll(async () => {
    const ResponseDeleteConv = await requestDeleteObj(api, OBJ_TYPE.FOLDER, newFolder, company_id);
    expect(ResponseDeleteConv.status).toBe(200);
  });
});
