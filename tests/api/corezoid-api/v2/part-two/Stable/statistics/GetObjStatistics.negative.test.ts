import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { ConfigurationManager } from '../../../../../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';
import { addMsg } from 'jest-html-reporters/helper';
import { requestDeleteObj, requestCreateObj } from '../../../../../../../application/api/ApiObj';
import { stringTestCases, stringNotValidTestCases, minLength, maxLength } from '../../../../../negativeCases';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';

describe('Get object statistics (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let key: string;
  let secret: string;
  let newFolder: string | number;
  let hashUpload: string | number;
  let company_id: any;
  const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
  const scriptPath = 'tests/api/corezoid-api/sh/uploadObject.sh';
  const exec = promisify(execCallback);

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key = apikey.key;
    secret = apikey.secret;

    const responseFolder = await requestCreateObj(api, OBJ_TYPE.FOLDER, company_id, `Folder_${Date.now()}`, 0);
    newFolder = responseFolder.body.ops[0].obj_id;

    const { stdout, stderr } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        FOLDER_ID: `${newFolder}`,
        VALIDATE_SCHEME: 'false',
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        COMPANY_ID: company_id,
        FILE_NAME: 'folder_for_test_upload_univer.suite_70conv.zip',
        ASYNC: 'true',
      },
    });
    expect(stderr).toBe(``);

    expect(stdout).toContain(`{"request_proc":"ok","ops":[{"obj":"obj_scheme","proc":"ok","hash"`);
    await addMsg({ message: JSON.stringify(stdout), context: '' });
    hashUpload = stdout.substr(76, 146);
  });

  test.each([...stringTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't show GET statistics with invalid action '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.GET,
          obj: OBJ_TYPE.STATISTICS,
          action: input,
          obj_type: 'obj_scheme',
          id: `${hashUpload}`,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...stringNotValidTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't show GET statistics with invalid obj_type '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.GET,
          obj: OBJ_TYPE.STATISTICS,
          action: 'upload',
          obj_type: input,
          id: `${hashUpload}`,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...maxLength, ...minLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't show GET statistics with invalid id '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.GET,
          obj: OBJ_TYPE.STATISTICS,
          action: 'upload',
          obj_type: 'obj_scheme',
          id: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  afterAll(async () => {
    const ResponseDeleteConv = await requestDeleteObj(api, OBJ_TYPE.FOLDER, newFolder, company_id);
    expect(ResponseDeleteConv.status).toBe(200);
  });
});
