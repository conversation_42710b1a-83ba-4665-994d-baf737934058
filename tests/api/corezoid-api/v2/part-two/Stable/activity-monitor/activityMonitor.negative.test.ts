import { ApiKeyClient } from '../../../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../../../utils/corezoidRequest';

describe('Activity monitor (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let owner: string | number;
  let company_id: any;
  const unix = Math.round(+new Date() / 1000);

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    owner = +apikey.id;
    company_id = apikey.companies[0].id;
  });

  test.each([null, 'test', true, ''])(`shouldn't show used_traff with invalid start '%s'`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER_TRAF,
        company_id,
        filter: 'opers',
        interval: 'minute',
        start: param,
        end: unix,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Value is not valid');
  });

  test.each([null, 'test', true, ''])(`shouldn't show used_traff with invalid end '%s'`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER_TRAF,
        company_id,
        filter: 'tacts',
        interval: 'minute',
        start: unix,
        end: param,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Value is not valid');
  });

  test.each([0, 1234, true, [], {}])(`shouldn't show used_traff with invalid company_id '%s'`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER_TRAF,
        company_id: param,
        filter: 'tacts',
        interval: 'minute',
        start: unix,
        end: unix,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(`Value is not valid`);
  });

  test(`shouldn't show sys_stat with invalid order_field 'test'`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field: 'test',
        order_by: 'DESC',
        start: unix - 240,
        end: unix,
        owner_id: owner,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe(
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"opers\\\">>,<<\\\"tacts\\\">>,<<\\\"traff\\\">>]\">>`,
    );
  });

  test.each([
    [
      {},
      `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"opers\\\">>,<<\\\"tacts\\\">>,<<\\\"traff\\\">>]\">>`,
    ],
    [
      [],
      `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"opers\\\">>,<<\\\"tacts\\\">>,<<\\\"traff\\\">>]\">>`,
    ],
    [
      true,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"opers\\\">>,<<\\\"tacts\\\">>,<<\\\"traff\\\">>]\">>`,
    ],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"opers\\\">>,<<\\\"tacts\\\">>,<<\\\"traff\\\">>]\">>`,
    ],
    [
      `*(select 1)*`,
      `Value '<<\"*(select 1)*\">>' is not valid. Value is not in allowed list <<\"[<<\\\"opers\\\">>,<<\\\"tacts\\\">>,<<\\\"traff\\\">>]\">>`,
    ],
    [
      0,
      `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"opers\\\">>,<<\\\"tacts\\\">>,<<\\\"traff\\\">>]\">>`,
    ],
  ])(`shouldn't show SYS_STAT with invalid order_field '%s`, async (order_field, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field,
        order_by: 'DESC',
        limit: 10,
        start: unix,
        end: unix,
        owner_id: owner,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [{}, `Value '<<\"{[]}\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ASC\\\">>,<<\\\"DESC\\\">>]\">>`],
    [[], `Value '<<\"[]\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ASC\\\">>,<<\\\"DESC\\\">>]\">>`],
    [
      true,
      `Value '<<\"true\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ASC\\\">>,<<\\\"DESC\\\">>]\">>`,
    ],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ASC\\\">>,<<\\\"DESC\\\">>]\">>`,
    ],
    [
      `*(select 1)*`,
      `Value '<<\"*(select 1)*\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ASC\\\">>,<<\\\"DESC\\\">>]\">>`,
    ],
    [0, `Value '<<\"0\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ASC\\\">>,<<\\\"DESC\\\">>]\">>`],
  ])(`shouldn't show SYS_STAT with invalid order_by '%s`, async (order_by, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field: 'opers',
        order_by,
        limit: 10,
        start: unix,
        end: unix,
        owner_id: owner,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test(`shouldn't show sys_stat with invalid order_by 'test'`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field: 'opers',
        order_by: 'test',
        limit: 10,
        start: unix - 240,
        end: unix,
        owner_id: owner,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe(
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"ASC\\\">>,<<\\\"DESC\\\">>]\">>`,
    );
  });

  test.each([
    ['', 'Value is not valid'],
    [true, 'Value is not valid'],
    [[], 'Value is not valid'],
    [{}, 'Value is not valid'],
    [0, `Value is not valid. Value's limit is less than minimum allowed: 1`],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 1`],
    [50, `Value is not valid. Value's limit is more than maximum allowed: 15`],
  ])(`shouldn't show sys_stat with invalid limit '%s'`, async (limit, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field: 'opers',
        order_by: 'DESC',
        limit,
        start: unix - 240,
        end: unix,
        owner_id: owner,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe(reason);
  });

  test.each([null, 'test', true, ''])(`shouldn't show sys_stat with invalid start '%s'`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field: 'opers',
        order_by: 'DESC',
        limit: 10,
        start: param,
        end: unix,
        owner_id: owner,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Value is not valid');
  });

  test.each([null, 'test', true, ''])(`shouldn't show sys_stat with invalid end '%s'`, async param => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field: 'opers',
        order_by: 'DESC',
        limit: 10,
        start: unix,
        end: param,
        owner_id: owner,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Value is not valid');
  });

  test.each([
    [null, 'Value is not valid'],
    ['', 'Value is not valid'],
    [true, 'Value is not valid'],
    ['test', 'Value is not valid'],
    [-1, `Value is not valid. Value's limit is less than minimum allowed: 0`],
    [0, `User has no rights`],
  ])(`shouldn't show sys_stat with invalid owner_id '%s'`, async (owner_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field: 'opers',
        order_by: 'DESC',
        limit: 10,
        start: unix,
        end: unix,
        owner_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [{}, 'Value is not valid'],
    [[], 'Value is not valid'],
    [true, 'Value is not valid'],
    ['test', 'Company not exists'],
    ['i883227690', 'User is not in company i883227690'],
    [0, `Value is not valid`],
  ])(`shouldn't show SYS_STAT with invalid company_id '%s`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field: 'opers',
        order_by: 'DESC',
        limit: 10,
        start: unix,
        end: unix,
        owner_id: owner,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [{}, 'Value is not valid'],
    [[], 'Value is not valid'],
    [true, 'Value is not valid'],
    ['test', 'Company not exists'],
    ['i883227690', 'User is not in company i883227690'],
    [0, `Value is not valid`],
  ])(`shouldn't show used_traff with invalid company_id '%s`, async (company_id, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER_TRAF,
        company_id,
        filter: 'traff',
        interval: 'minute',
        start: unix,
        end: unix,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"traff\\\">>,<<\\\"tacts\\\">>,<<\\\"opers\\\">>]\">>`,
    ],
    [
      `*(select 1)*`,
      `Value '<<\"*(select 1)*\">>' is not valid. Value is not in allowed list <<\"[<<\\\"traff\\\">>,<<\\\"tacts\\\">>,<<\\\"opers\\\">>]\">>`,
    ],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't show used_traff with invalid filter '%s`, async (filter, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER_TRAF,
        company_id,
        filter,
        interval: 'minute',
        start: unix,
        end: unix,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });

  test.each([
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"minute\\\">>,<<\\\"hour\\\">>,<<\\\"day\\\">>,<<\\\"month\\\">>]\">>`,
    ],
    [
      `*(select 1)*`,
      `Value '<<\"*(select 1)*\">>' is not valid. Value is not in allowed list <<\"[<<\\\"minute\\\">>,<<\\\"hour\\\">>,<<\\\"day\\\">>,<<\\\"month\\\">>]\">>`,
    ],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't show used_traff with invalid interval '%s`, async (interval, reason) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER_TRAF,
        company_id,
        filter: 'traff',
        interval,
        start: unix,
        end: unix,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toEqual(reason);
  });
});
