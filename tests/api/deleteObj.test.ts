import { application } from '../../application/Application';
import { Api<PERSON><PERSON> } from '../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../utils/corezoidRequest';
import { User } from '../../infrastructure/model/User';
import { ConfigurationManager } from '../../infrastructure/config/ConfigurationManager';
import { createAuthUser, Method } from '../../utils/request';
import { debug } from '../../support/utils/logger';
import { ApiUserClient } from '../../application/api/ApiUserClient';
import { ApiKeyClient } from '../../application/api/ApiKeyClient';

describe('Delete obj', (): void => {
  let apikey: ApiKey;
  let api: ApiKeyClient;
  let company_id: any;
  let user: User;
  let superUser: User;
  let cookie: any;
  let url_superadmin: string;
  let apiSUser: ApiUserClient;
  let apiUser: ApiUserClient;

  async function setCapiShareValue(value: boolean): Promise<any> {
    const response = await cookie.request({
      method: Method.POST,
      url: `${url_superadmin}superadmin/api/1/json`,
      data: {
        type: 'set',
        obj: 'value',
        id: 213,
        obj_type: 'boolean',
        key: 'capi_share_api_keys_in_company',
        value: String(value),
        description: 'Share api keys in company for all members',
        is_required: false,
        allowed: [],
        tags: [{ id: 1, tag: 'capi' }],
      },
    });
    expect(response.status).toBe(200);
    return response;
  }

  function logProgress(current: number, total: number, type: string, name: string): void {
    if (current % 5 === 0) {
      debug(
        `Удалено ${current} ${type} из ${total}. Осталось удалить ${total - current} объектов у пользователя ${name}.`,
      );
    }
  }

  beforeAll(
    async (): Promise<void> => {
      apikey = apikey = {
        key: '119155',
        secret: 'Wz1nNW8a51H1KZrDUBK3MOriRfPmswMjkXlO7CbwD6MiULBO8q',
        companies: [{ id: 'i738314881' }],
        id: '118338',
        title: 'user5',
      };
      api = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;
      url_superadmin = `${ConfigurationManager.getConfiguration().getSuperadminUrl()}`;

      superUser = await application.getAuthorizedUser({ company: {} }, 1);
      cookie = createAuthUser(superUser.cookieUser, 'cookie');
      apiSUser = await application.getApiUserClient(superUser);

      user = await application.getAuthorizedUser();
      apiUser = await application.getApiUserClient(user);

      await setCapiShareValue(false);
    },
  );

  const userList = [
    { name: 'ApiKey Client', client: (): ApiKeyClient => api },
    { name: 'Regular User', client: (): ApiUserClient => apiUser },
    { name: 'Super User', client: (): ApiUserClient => apiSUser },
  ];

  const companyList = [(): any => company_id, null];

  describe.each(userList)('by $name', ({ name, client }): void => {
    async function deleteObjectsByType(listParams: any, deleteParams: any, objectType: string): Promise<number> {
      let totalDeleted = 0;
      let objsExist = true;

      while (objsExist) {
        const response = await client().request(createRequestWithOps(listParams));
        expect(response.status).toBe(200);

        let objects = (response.body.ops[0].list || []).filter(
          (item: any) =>
            item.project_id !== 1019527 &&
            item.project_id !== 1019509 &&
            item.obj_id !== 118338 &&
            item.obj_id !== 11028,
        );

        if (deleteParams.obj === OBJ_TYPE.INSTANCE) {
          objects = response.body.ops[0].instances || [];
        }

        if (!objects.length) {
          objsExist = false;
          break;
        }

        for (const obj of objects) {
          totalDeleted++;

          const deleteResponse = await apiSUser.request(
            createRequestWithOps({
              type: deleteParams.type || REQUEST_TYPE.DELETE,
              obj: obj.obj_type || deleteParams.obj,
              obj_id: obj.obj_id,
              company_id: deleteParams.company_id,
            }),
          );

          expect(deleteResponse.status).toBe(200);
          expect(deleteResponse.body.ops[0].proc).toBe('ok');

          logProgress(totalDeleted, objects.length, objectType, name);
        }
      }

      debug(`Всего удалено у пользователя ${name} ${objectType}: ${totalDeleted}`);
      return totalDeleted;
    }

    test.each(companyList)(
      'delete obj in list folder company_id %s',
      async (getCompany): Promise<void> => {
        const company = typeof getCompany === 'function' ? getCompany() : getCompany;

        await deleteObjectsByType(
          {
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.FOLDER,
            obj_id: 0,
            company_id: company,
          },
          {
            obj: OBJ_TYPE.FOLDER,
          },
          `объектов в company_id=${company}`,
        );
      },
    );

    test('delete project', async (): Promise<void> => {
      await deleteObjectsByType(
        {
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.PROJECTS,
          obj_id: 0,
          company_id,
        },
        {
          obj: OBJ_TYPE.PROJECT,
        },
        'проектов',
      );
    });

    test('delete instance', async (): Promise<void> => {
      await deleteObjectsByType(
        {
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.INSTANCES,
          company_id,
          instance_type: 'db_call',
          limit: 100,
        },
        {
          obj: OBJ_TYPE.INSTANCE,
        },
        'инстансов',
      );
    });

    test('delete alias', async (): Promise<void> => {
      await deleteObjectsByType(
        {
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.ALIASES,
          company_id,
        },
        {
          obj: OBJ_TYPE.ALIAS,
        },
        'алиасов',
      );
    });

    test('delete dashboards', async (): Promise<void> => {
      await deleteObjectsByType(
        {
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.DASHBOARDS,
          company_id,
          limit: 100,
        },
        {
          obj: OBJ_TYPE.DASHBOARD,
        },
        'дашбордов',
      );
    });

    test.each(companyList)(
      'delete group in company_id %s',
      async (getCompany): Promise<void> => {
        const company = typeof getCompany === 'function' ? getCompany() : getCompany;

        await deleteObjectsByType(
          {
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.COMPANY_USERS,
            obj_id: 0,
            company_id: company,
            filter: 'group',
          },
          {
            obj: OBJ_TYPE.GROUP,
            company_id: company,
          },
          `групп в company_id=${company}`,
        );
      },
    );

    test('delete key in company', async (): Promise<void> => {
      await deleteObjectsByType(
        {
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          obj_id: 0,
          company_id,
          filter: 'api_key',
        },
        {
          obj: OBJ_TYPE.USER,
          company_id,
        },
        'ключей',
      );
    });

    test('delete obj in list convs company', async (): Promise<void> => {
      await deleteObjectsByType(
        {
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.CONVS,
          company_id,
        },
        {
          obj: OBJ_TYPE.CONVS,
          company_id,
        },
        'convs',
      );
    });

    test.each(companyList)(
      'Destroy obj in trash company_id %s',
      async (getCompany): Promise<void> => {
        const company = typeof getCompany === 'function' ? getCompany() : getCompany;

        await deleteObjectsByType(
          {
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.FOLDER,
            obj_id: 0,
            company_id: company,
            filter: 'deleted',
          },
          {
            type: REQUEST_TYPE.DESTROY,
            obj: OBJ_TYPE.FOLDER,
          },
          `объектов из корзины в company_id=${company}`,
        );
      },
    );
  });

  test('capi share api keys = true', async (): Promise<void> => {
    const response = await setCapiShareValue(true);
    expect(response.data.result).toBe('ok');
    expect(response.data.value).toBe(true);
  });

  afterAll(
    async (): Promise<void> => {
      await setCapiShareValue(true);
    },
  );
});
