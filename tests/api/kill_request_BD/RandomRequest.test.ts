import { application } from '../../../application/Application';
import { User } from '../../../infrastructure/model/User';
import { ApiUserClient } from '../../../application/api/ApiUserClient';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import { debug, error } from '../../../support/utils/logger';

function checkResponse(response: any): void {
  expect(response.status).toBe(200);
  const responseBody = response.body;

  if (responseBody.request_proc !== 'ok') {
    throw new Error(`Unexpected request_proc: ${responseBody.request_proc}`);
  }

  const op = responseBody.ops[0];
  if (op.proc !== 'ok') {
    const expectedErrors = ['timeout of query execution'];
    if (expectedErrors.includes(op.description)) {
      debug(`Expected error occurred: ${op.description}`);
    } else {
      throw new Error(`Unexpected operation error: ${op.description}`);
    }
  } else {
    expect(op.proc).toBe('ok');
  }
}

describe('List history (positive)', () => {
  let api: ApiUserClient;
  let user: User;
  let project_id: number;
  let stage_id: number;
  let company_id: any;
  let folder_id: number;
  let project_short_name: string;

  beforeAll(async () => {
    user = await application.getAuthorizedUser({ company: {} }, 0);
    company_id = user.companies[0].id;
    api = await application.getApiUserClient(user);

    project_short_name = `project${Date.now()}`;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: project_short_name,
        short_name: project_short_name,
        description: 'test',
        stages: ['develop'],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    project_id = response.body.ops[0].obj_id;
    stage_id = response.body.ops[0].stages[0];

    const responseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        title: `Folder_${Date.now()}`,
        folder_id: stage_id,
      }),
    );
    folder_id = responseFolder.body.ops[0].obj_id;
  });

  test(`modify folder`, async () => {
    try {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.FOLDER,
          obj_id: folder_id,
          company_id,
          title: `MODIFY`,
        }),
      );
      checkResponse(response);
    } catch (err) {
      error('Unexpected error:', err);
      throw err;
    }
  });

  test(`create folder`, async () => {
    try {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.FOLDER,
          company_id,
          title: `Folder_${Date.now()}`,
          folder_id: stage_id,
        }),
      );
      checkResponse(response);
    } catch (err) {
      error('Unexpected error:', err);
      throw err;
    }
  });

  test(`show folder`, async () => {
    try {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.FOLDER,
          obj_id: folder_id,
          company_id,
        }),
      );
      checkResponse(response);
    } catch (err) {
      error('Unexpected error:', err);
      throw err;
    }
  });

  afterAll(async () => {
    const responseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: project_id,
        company_id,
      }),
    );
    expect(responseFolder.status).toBe(200);
  });
});
