import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';
import {
  requestDeleteObj,
  requestLinkObj,
  requestCreateObjNew,
  requestListConv,
  requestConfirm,
  requestCreateTask,
  requestModifyConvStatus,
} from '../../../../application/api/ApiObj';

describe('Projects_collaboration', (): void => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let obj_id_apikey: any;

  let newApi: ApiKeyClient;
  let newApiKeyId: string | number;

  let newProject1: string | number;
  let newProject2: string | number;
  let newProjectKey: string | number;
  let newStage1: string | number;
  let newStage2: string | number;
  let newStage3: string | number;
  let newStageKey: string | number;
  let newConv1: number;
  let newConv2: number;
  let newConv3: number;
  let newConvKey: number;
  let newConvFolders: number;
  let newAlias1: string | number;
  let newAlias2: string | number;
  let newAlias3: string | number;

  let newConvSD1: string | number;
  let newAliasSD: string | number;
  let short_nameSD1: string;
  let newConvSD2: string | number;

  let short_name1: string;
  let project_short_name1: string;
  let stage_short_name1: string;

  let short_name2: string;
  let project_short_name2: string;
  let stage_short_name2: string;

  let short_name3: string;
  let stage_short_name3: string;
  let project_short_name3: string;

  let process_node_ID1: string;
  let process_node_IDFolders: string;
  let final_node_IDFolders: string;
  let final_node_ID1: string;
  let final_node_ID2: string;
  let process_node_ID3: string;
  let final_node_ID3: string;
  let final_node_IDKey: string;

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;
      obj_id_apikey = apikey.id;

      project_short_name1 = `project1-${Date.now()}`;
      stage_short_name1 = `stage1-${Date.now()}`;
      short_name1 = `alias1-${Date.now()}`;
      short_nameSD1 = `aliassd-${Date.now()}`;

      stage_short_name3 = `stage3-${Date.now()}`;
      short_name3 = `alias3-${Date.now()}`;

      project_short_name2 = `project2-${Date.now()}`;
      stage_short_name2 = `stage2-${Date.now()}`;
      short_name2 = `alias2-${Date.now()}`;

      project_short_name3 = `project3-${Date.now()}`;

      const responseProject1 = await requestCreateObjNew(api, OBJ_TYPE.PROJECT, company_id, `Project1_${Date.now()}`, {
        short_name: project_short_name1,
        description: 'test',
        stages: [],
      });
      newProject1 = responseProject1.body.ops[0].obj_id;

      const responseStage1 = await requestCreateObjNew(api, OBJ_TYPE.STAGE, company_id, `Stage1_${Date.now()}`, {
        short_name: stage_short_name1,
        description: 'test',
        project_id: newProject1,
      });
      newStage1 = responseStage1.body.ops[0].obj_id;

      const responseConv1 = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, `Conv1_${Date.now()}`, {
        conv_type: 'process',
        stage_id: newStage1,
        project_id: newProject1,
      });
      newConv1 = responseConv1.body.ops[0].obj_id;

      const responseAlias1 = await requestCreateObjNew(api, OBJ_TYPE.ALIAS, company_id, `Alias1_${Date.now()}`, {
        short_name: short_name1,
        project_id: newProject1,
        stage_id: newStage1,
      });
      newAlias1 = responseAlias1.body.ops[0].obj_id;

      await requestLinkObj(api, OBJ_TYPE.ALIAS, company_id, newAlias1, true, newConv1, 'conv');

      const responseListConv1 = await requestListConv(api, newConv1, company_id);
      process_node_ID1 = (responseListConv1.body.ops[0].list as Array<any>).find(item => item.title === 'process')
        .obj_id;
      final_node_ID1 = (responseListConv1.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

      const responseConvSD1 = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, `ConvSD_${Date.now()}`, {
        conv_type: 'state',
        stage_id: newStage1,
        project_id: newProject1,
      });
      newConvSD1 = responseConvSD1.body.ops[0].obj_id;

      const responseAliasSD1 = await requestCreateObjNew(api, OBJ_TYPE.ALIAS, company_id, `AliasSD_${Date.now()}`, {
        short_name: short_nameSD1,
        project_id: newProject1,
        stage_id: newStage1,
      });
      newAliasSD = responseAliasSD1.body.ops[0].obj_id;

      await requestLinkObj(api, OBJ_TYPE.ALIAS, company_id, newAliasSD, true, newConvSD1, 'conv');

      const responseStage = await requestCreateObjNew(api, OBJ_TYPE.STAGE, company_id, `Stage3_${Date.now()}`, {
        short_name: stage_short_name3,
        description: 'test',
        project_id: newProject1,
      });
      newStage3 = responseStage.body.ops[0].obj_id;

      const responseConv = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, `Conv3_${Date.now()}`, {
        conv_type: 'process',
        stage_id: newStage3,
        project_id: newProject1,
      });
      newConv3 = responseConv.body.ops[0].obj_id;

      const responseAlias = await requestCreateObjNew(api, OBJ_TYPE.ALIAS, company_id, `Alias3_${Date.now()}`, {
        short_name: short_name3,
        project_id: newProject1,
        stage_id: newStage3,
      });
      newAlias3 = responseAlias.body.ops[0].obj_id;

      await requestLinkObj(api, OBJ_TYPE.ALIAS, company_id, newAlias3, true, newConv3, 'conv');

      const responseListConv = await requestListConv(api, newConv3, company_id);
      final_node_ID3 = (responseListConv.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
      process_node_ID3 = (responseListConv.body.ops[0].list as Array<any>).find(item => item.title === 'process')
        .obj_id;

      const responseModifyConv = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID3,
          conv_id: newConv3,
          title: 'process',
          obj_type: 0,
          logics: [
            {
              type: 'api_rpc_reply',
              mode: 'key_value',
              throw_exception: false,
              res_data: {},
              res_data_type: {},
            },
            {
              to_node_id: final_node_ID3,
              type: 'go',
            },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseModifyConv.status).toBe(200);

      const responseCommit3 = await requestConfirm(api, newConv3, company_id);
      expect(responseCommit3.status).toBe(200);

      const responseProject2 = await requestCreateObjNew(api, OBJ_TYPE.PROJECT, company_id, `Project2_${Date.now()}`, {
        short_name: project_short_name2,
        description: 'test',
        stages: [],
      });
      newProject2 = responseProject2.body.ops[0].obj_id;

      const responseStage2 = await requestCreateObjNew(api, OBJ_TYPE.STAGE, company_id, `Stage2_${Date.now()}`, {
        short_name: stage_short_name2,
        description: 'test',
        project_id: newProject2,
      });
      newStage2 = responseStage2.body.ops[0].obj_id;

      const responseConv2 = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, `Conv2_${Date.now()}`, {
        conv_type: 'process',
        stage_id: newStage2,
        project_id: newProject2,
      });
      newConv2 = responseConv2.body.ops[0].obj_id;

      const responseAlias2 = await requestCreateObjNew(api, OBJ_TYPE.ALIAS, company_id, `Alias2_${Date.now()}`, {
        short_name: short_name2,
        project_id: newProject2,
        stage_id: newStage2,
      });
      newAlias2 = responseAlias2.body.ops[0].obj_id;

      await requestLinkObj(api, OBJ_TYPE.ALIAS, company_id, newAlias2, true, newConv2, 'conv');

      const responseListConv2 = await requestListConv(api, newConv2, company_id);
      final_node_ID2 = (responseListConv2.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

      const responseModifyConv2 = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID1,
          conv_id: newConv2,
          title: 'process',
          obj_type: 0,
          logics: [
            {
              type: 'api_rpc_reply',
              mode: 'key_value',
              throw_exception: false,
              res_data: {},
              res_data_type: {},
            },
            {
              to_node_id: final_node_ID2,
              type: 'go',
            },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseModifyConv2.status).toBe(200);

      const responseCommit = await requestConfirm(api, newConv2, company_id);
      expect(responseCommit.status).toBe(200);

      const responseConvSD2 = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, `ConvSD_${Date.now()}`, {
        conv_type: 'state',
        stage_id: newStage2,
        project_id: newProject2,
      });
      newConvSD2 = responseConvSD2.body.ops[0].obj_id;

      await requestCreateTask(api, OBJ_TYPE.TASK, company_id, newConvSD1, 'ref', {
        conv_id: `${newConv2}`,
        stage_id: `${newStage2}`,
        project_id: `${newProject2}`,
        alias: `${short_name2}`,
        stage_short_name: `${stage_short_name2}`,
        project_short_name: `${project_short_name2}`,
      });

      const responseConvFolders = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, `Conv1_${Date.now()}`, {
        conv_type: 'process',
      });
      newConvFolders = responseConvFolders.body.ops[0].obj_id;

      const responseListConvFolders = await requestListConv(api, newConvFolders, company_id);
      process_node_IDFolders = (responseListConvFolders.body.ops[0].list as Array<any>).find(
        item => item.title === 'process',
      ).obj_id;
      final_node_IDFolders = (responseListConvFolders.body.ops[0].list as Array<any>).find(
        item => item.title === 'final',
      ).obj_id;

      const CreateKeyResponse = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.USER,
          company_id,
          title: 'API',
          logins: [{ type: 'api' }],
        }),
      );
      expect(CreateKeyResponse.status).toBe(200);
      const user_api = CreateKeyResponse.body;
      const newApiKey = {
        key: `${user_api.ops[0].users[0].logins[0].obj_id}`,
        secret: user_api.ops[0].users[0].logins[0].key,
        companies: [],
        title: '',
        id: `${user_api.ops[0].users[0].obj_id}`,
      } as ApiKey;
      newApi = application.getApiKeyClient(newApiKey);
      newApiKeyId = +newApiKey.id;

      const responseProjectKey = await requestCreateObjNew(
        newApi,
        OBJ_TYPE.PROJECT,
        company_id,
        `Project2_${Date.now()}`,
        {
          short_name: project_short_name3,
          description: 'test',
          stages: [],
        },
      );
      newProjectKey = responseProjectKey.body.ops[0].obj_id;

      const responseStageKey = await requestCreateObjNew(newApi, OBJ_TYPE.STAGE, company_id, `Stage2_${Date.now()}`, {
        short_name: stage_short_name2,
        description: 'test',
        project_id: newProjectKey,
      });
      newStageKey = responseStageKey.body.ops[0].obj_id;

      const responseConvKey = await requestCreateObjNew(newApi, OBJ_TYPE.CONV, company_id, `Conv2_${Date.now()}`, {
        conv_type: 'process',
        stage_id: newStageKey,
        project_id: newProjectKey,
      });
      newConvKey = responseConvKey.body.ops[0].obj_id;

      const responseListConvKey = await requestListConv(newApi, newConvKey, company_id);
      final_node_IDKey = (responseListConvKey.body.ops[0].list as Array<any>).find(item => item.title === 'final')
        .obj_id;
    },
  );

  const testCasesNegative = [
    {
      description: 'Conv_not_found_or_deleted',
      conv_id: (): string | number => newConv2,
      stage_id: (): string | number => newStage2,
      project_id: (): string | number => newProject2,
      return_description: (): string => `Param: conv_id, User: ${obj_id_apikey}, Conv_id: ${newConv2}`,
      tag: (): string => `conv_not_found_or_deleted`,
    },
    {
      description: 'Not_found_conv_by_alias',
      conv_id: (): string => `@${short_name2}`,
      stage_id: (): string | number => newStage2,
      project_id: (): string | number => newProject2,
      return_description: (): string =>
        `There is no such process linked by alias - ${short_name2} in project_id: ${newProject2}, stage_id: ${newStage2}`,
      tag: (): string => `not_found_conv_by_alias`,
    },
    {
      description: 'Project/Stage is either invalid or not found',
      conv_id: (): string => `@${short_name2}`,
      stage_id: (): string => `@test`,
      project_id: (): string => `@test`,
      return_description: (): string => `The project: @test or stage: @test is either invalid or not found.`,
      tag: (): string => `not_found_project_or_stage`,
    },
    {
      description: 'Project/Stage try convert to: integer.',
      conv_id: (): string => `@${short_name2}`,
      stage_id: (): string => `test`,
      project_id: (): string => `test`,
      return_description: (): string => `Param: __copy_task.project_id__, Value: test, Try convert to: integer`,
      tag: (): string => `copy_task_wrong_convert_param`,
    },
    {
      description: `There isn't found process_id in stage_id/project_id`,
      conv_id: (): number => newConvFolders,
      stage_id: (): string | number => newStage2,
      project_id: (): string | number => newProject2,
      return_description: (): string =>
        `Process (ID: ${newConvFolders}) cannot be called from outside a project into a project or from within a project to outside a project`,
      tag: (): string => `access_denied`,
    },
    {
      description: `It's not allowed to read data between stages`,
      conv_id: (): string => `{{conv[${newConvSD2}].ref[ref].key}}`,
      stage_id: (): string => `{{conv[${newConvSD2}].ref[ref].key}}`,
      project_id: (): string => `{{conv[${newConvSD2}].ref[ref].key}}`,
      return_description: (): string => `It's not allowed to read data between stages`,
      tag: (): string => `access_denied`,
    },
    {
      description: `Access_denied to Project`,
      conv_id: (): number => newConvKey,
      stage_id: (): string | number => newStageKey,
      project_id: (): string | number => newProjectKey,
      return_description: (): string => `It's not allowed to read data between stages`,
      tag: (): string => `access_denied`,
    },
  ];

  const testCasesPositive = [
    {
      description: 'Conv/Stage/Project - direct IDs number',
      conv_id: (): number => newConv2,
      stage_id: (): string | number => newStage2,
      project_id: (): string | number => newProject2,
      conv_idCalled: (): number => newConv2,
      final_nodeCalled: (): string => final_node_ID2,
    },
    {
      description: '[between project]Conv/Stage/Project - direct IDs string',
      conv_id: (): string => `${newConv2}`,
      stage_id: (): string => `${newStage2}`,
      project_id: (): string => `${newProject2}`,
      conv_idCalled: (): number => newConv2,
      final_nodeCalled: (): string => final_node_ID2,
    },
    {
      description: '[between project]Conv - alias and Stage/Project - ID',
      conv_id: (): string => `@${short_name2}`,
      stage_id: (): string | number => newStage2,
      project_id: (): string | number => newProject2,
      conv_idCalled: (): number => newConv2,
      final_nodeCalled: (): string => final_node_ID2,
    },
    {
      description: '[between project]Conv/Stage/Project - aliases',
      conv_id: (): string => `@${short_name2}`,
      stage_id: (): string => `@${stage_short_name2}`,
      project_id: (): string => `@${project_short_name2}`,
      conv_idCalled: (): number => newConv2,
      final_nodeCalled: (): string => final_node_ID2,
    },
    {
      description: '[between project]Conv/Stage/Project - aliases dinamic, stage/project short_name',
      conv_id: (): string => `@{{conv_sname}}`,
      stage_id: (): string => `@${stage_short_name2}`,
      project_id: (): string => `@${project_short_name2}`,
      conv_idCalled: (): number => newConv2,
      final_nodeCalled: (): string => final_node_ID2,
    },
    {
      description: '[between project]Conv/Stage/Project - aliases dinamic, stage/project id',
      conv_id: (): string => `@{{conv_sname}}`,
      stage_id: (): string | number => newStage2,
      project_id: (): string | number => newProject2,
      conv_idCalled: (): number => newConv2,
      final_nodeCalled: (): string => final_node_ID2,
    },
    {
      description: '[between project]Conv/Stage/Project - conv dinamic, stage/project short_name',
      conv_id: (): string => `{{conv}}`,
      stage_id: (): string => `@${stage_short_name2}`,
      project_id: (): string => `@${project_short_name2}`,
      conv_idCalled: (): number => newConv2,
      final_nodeCalled: (): string => final_node_ID2,
    },
    {
      description: '[between project]Conv/Stage/Project - conv dinamic, stage/project id',
      conv_id: (): string => `{{conv}}`,
      stage_id: (): string | number => newStage2,
      project_id: (): string | number => newProject2,
      conv_idCalled: (): number => newConv2,
      final_nodeCalled: (): string => final_node_ID2,
    },
    {
      description: '[between project]Dynamic IDs',
      conv_id: (): string => `{{conv}}`,
      stage_id: (): string => `{{stage}}`,
      project_id: (): string => `{{project}}`,
      conv_idCalled: (): number => newConv2,
      final_nodeCalled: (): string => final_node_ID2,
    },
    {
      description: '[between project]Dynamic short names',
      conv_id: (): string => `@{{conv_sname}}`,
      stage_id: (): string => `@{{stage_sname}}`,
      project_id: (): string => `@{{project_sname}}`,
      conv_idCalled: (): number => newConv2,
      final_nodeCalled: (): string => final_node_ID2,
    },
    {
      description: '[between project] IDs from construction',
      conv_id: (): string => `{{conv[${newConvSD1}].ref[ref].conv_id}}`,
      stage_id: (): string => `{{conv[${newConvSD1}].ref[ref].stage_id}}`,
      project_id: (): string => `{{conv[${newConvSD1}].ref[ref].project_id}}`,
      conv_idCalled: (): number => newConv2,
      final_nodeCalled: (): string => final_node_ID2,
    },
    {
      description: '[between project]Short names from construction',
      conv_id: (): string => `@{{conv[${newConvSD1}].ref[ref].alias}}`,
      stage_id: (): string => `@{{conv[${newConvSD1}].ref[ref].stage_short_name}}`,
      project_id: (): string => `@{{conv[${newConvSD1}].ref[ref].project_short_name}}`,
      conv_idCalled: (): number => newConv2,
      final_nodeCalled: (): string => final_node_ID2,
    },
    {
      description: '[between stage]Conv/Stage/Project - direct IDs number',
      conv_id: (): number => newConv3,
      stage_id: (): string | number => newStage3,
      project_id: (): string | number => newProject1,
      conv_idCalled: (): number => newConv3,
      final_nodeCalled: (): string => final_node_ID3,
    },
    {
      description: '[between stage]Conv/Stage/Project - aliases',
      conv_id: (): string => `@${short_name3}`,
      stage_id: (): string => `@${stage_short_name3}`,
      project_id: (): string => `@${project_short_name1}`,
      conv_idCalled: (): number => newConv3,
      final_nodeCalled: (): string => final_node_ID3,
    },
  ];

  describe.each(testCasesPositive)(
    '$description',
    ({ conv_id, stage_id, project_id, conv_idCalled, final_nodeCalled }): void => {
      test(`should copy_task`, async (): Promise<void> => {
        const responseModify = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MODIFY,
            obj: OBJ_TYPE.NODE,
            obj_id: process_node_ID1,
            conv_id: newConv1,
            title: 'process',
            description: '',
            obj_type: 0,
            logics: [
              {
                type: 'api_copy',
                sync: false,
                err_node_id: '',
                conv_id: conv_id(),
                stage_id: stage_id(),
                project_id: project_id(),
                mode: 'create',
                ref: `ref_${Date.now()}`,
                data: {
                  Object: 'Table',
                },
                data_type: { Object: 'string' },
                group: 'all',
              },
              {
                to_node_id: final_node_ID1,
                format: 'json',
                type: 'go',
                index: 1,
                node_title: 'final',
              },
            ],
            semaphors: [],
            version: 22,
          }),
        );
        expect(responseModify.status).toBe(200);

        const responseCommit = await requestConfirm(api, newConv1, company_id);
        expect(responseCommit.status).toBe(200);

        const responseTask = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.TASK,
            conv_id: newConv1,
            data: {
              Id: '123',
              conv: newConv2,
              stage: newStage2,
              project: newProject2,
              conv_sname: short_name2,
              stage_sname: stage_short_name2,
              project_sname: project_short_name2,
            },
            ref: `ref_${Date.now()}`,
          }),
        );
        expect(responseTask.status).toBe(200);
        const task_id = responseTask.body.ops[0].obj_id;
        const ref = responseTask.body.ops[0].ref;

        await new Promise(r => setTimeout(r, 2000));

        const responseShow = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.SHOW,
            obj: OBJ_TYPE.TASK,
            conv_id: newConv1,
            ref_or_obj_id: task_id,
          }),
        );
        expect(responseShow.status).toBe(200);
        expect(responseShow.body.ops[0].data.Id).toBe('123');

        const responseListNode = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.NODE,
            conv_id: conv_idCalled(),
            obj_id: final_nodeCalled(),
            limit: 1,
          }),
        );
        expect(responseListNode.status).toBe(200);
        expect(responseListNode.body.ops[0].proc).toEqual('ok');
        expect(responseListNode.body.ops[0].list[0].data.Id).toBe('123');
        expect(responseListNode.body.ops[0].list[0].data.__copy_conv_id__).toBe(newConv1);
        expect(responseListNode.body.ops[0].list[0].data.__copy_ref_id__).toBe(ref);
        expect(responseListNode.body.ops[0].list[0].data.Object).toBe('Table');
      });

      test(`should Call_process`, async () => {
        const responseModify = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MODIFY,
            obj: OBJ_TYPE.NODE,
            obj_id: process_node_ID1,
            conv_id: newConv1,
            title: 'process',
            description: '',
            obj_type: 0,
            logics: [
              {
                type: 'api_rpc',
                sync: false,
                err_node_id: '',
                conv_id: conv_id(),
                stage_id: stage_id(),
                project_id: project_id(),
                mode: 'create',
                ref: `ref_${Date.now()}`,
                extra: {
                  Object: 'Table',
                },
                extra_type: { Object: 'string' },
                group: 'all',
              },
              {
                to_node_id: final_node_ID1,
                format: 'json',
                type: 'go',
                index: 1,
                node_title: 'final',
              },
            ],
            semaphors: [],
            version: 22,
          }),
        );
        expect(responseModify.status).toBe(200);

        const responseCommit = await requestConfirm(api, newConv1, company_id);
        expect(responseCommit.status).toBe(200);

        const responseTask = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.TASK,
            conv_id: newConv1,
            data: {
              Id: '123',
              conv: newConv2,
              stage: newStage2,
              project: newProject2,
              conv_sname: short_name2,
              stage_sname: stage_short_name2,
              project_sname: project_short_name2,
            },
            ref: `ref_${Date.now()}`,
          }),
        );
        expect(responseTask.status).toBe(200);
        const task_id = responseTask.body.ops[0].obj_id;
        const ref = responseTask.body.ops[0].ref;

        await new Promise(r => setTimeout(r, 2000));

        const responseShow = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.SHOW,
            obj: OBJ_TYPE.TASK,
            conv_id: newConv1,
            ref_or_obj_id: task_id,
          }),
        );
        expect(responseShow.status).toBe(200);
        expect(responseShow.body.ops[0].data.Id).toBe('123');

        const responseListNode = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.NODE,
            conv_id: conv_idCalled(),
            obj_id: final_nodeCalled(),
            limit: 1,
          }),
        );
        expect(responseListNode.status).toBe(200);
        expect(responseListNode.body.ops[0].proc).toEqual('ok');
        expect(responseListNode.body.ops[0].list[0].data.Id).toBe('123');
        expect(responseListNode.body.ops[0].list[0].data.__rpc_conv_id__).toBe(newConv1);
        expect(responseListNode.body.ops[0].list[0].data.__rpc_ref_id__).toBe(ref);
        expect(responseListNode.body.ops[0].list[0].data.Object).toBe('Table');
      });
    },
  );

  describe.each(testCasesNegative)(
    '$description',
    ({ conv_id, stage_id, project_id, return_description, tag }): void => {
      test(`shouldn't copy_task`, async (): Promise<void> => {
        const responseModify = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MODIFY,
            obj: OBJ_TYPE.NODE,
            obj_id: process_node_ID1,
            conv_id: newConv1,
            title: 'process',
            description: '',
            obj_type: 0,
            logics: [
              {
                type: 'api_copy',
                sync: false,
                err_node_id: '',
                conv_id: conv_id(),
                stage_id: stage_id(),
                project_id: project_id(),
                mode: 'create',
                ref: `ref_${Date.now()}`,
                data: {
                  Object: 'Table',
                },
                data_type: { Object: 'string' },
                group: 'all',
              },
              {
                to_node_id: final_node_ID1,
                format: 'json',
                type: 'go',
                index: 1,
                node_title: 'final',
              },
            ],
            semaphors: [],
            version: 22,
          }),
        );
        expect(responseModify.status).toBe(200);

        const responseCommit = await requestConfirm(api, newConv1, company_id);
        expect(responseCommit.status).toBe(200);

        await requestDeleteObj(api, OBJ_TYPE.CONV, newConv2, company_id);

        const responseTask = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.TASK,
            conv_id: newConv1,
            data: {
              Id: '123',
            },
            ref: `ref_${Date.now()}`,
          }),
        );
        expect(responseTask.status).toBe(200);
        const task_id = responseTask.body.ops[0].obj_id;

        await new Promise(r => setTimeout(r, 2000));

        const responseShow = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.SHOW,
            obj: OBJ_TYPE.TASK,
            conv_id: newConv1,
            ref_or_obj_id: task_id,
          }),
        );
        expect(responseShow.status).toBe(200);
        expect(responseShow.body.ops[0].data.__conveyor_copy_task_return_description__).toBe(return_description());
        expect(responseShow.body.ops[0].data.__conveyor_copy_task_return_type_tag__).toBe(tag());
      });
    },
  );

  test(`shouldn't copy_task in not active conv`, async (): Promise<void> => {
    const responseModify = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID1,
        conv_id: newConv1,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api_copy',
            sync: false,
            err_node_id: '',
            conv_id: newConv3,
            stage_id: newStage3,
            project_id: newProject1,
            mode: 'create',
            ref: `ref_${Date.now()}`,
            data: {
              Object: 'Table',
            },
            data_type: { Object: 'string' },
            group: 'all',
          },
          {
            to_node_id: final_node_ID1,
            format: 'json',
            type: 'go',
            index: 1,
            node_title: 'final',
          },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModify.status).toBe(200);

    const responseCommit = await requestConfirm(api, newConv1, company_id);
    expect(responseCommit.status).toBe(200);

    await requestModifyConvStatus(api, newConv3, company_id, 'paused');

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: newConv1,
        data: {
          Id: '123',
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    const task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: newConv1,
        ref_or_obj_id: task_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].data.__conveyor_copy_task_return_description__).toBe(
      `There isn't found process_id: ${newConv3}`,
    );
    expect(responseShow.body.ops[0].data.__conveyor_copy_task_return_type_tag__).toBe('not_found_process');
  });

  test(`should copy_task with privs create task`, async (): Promise<void> => {
    const responsLink = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: newConvKey,
        company_id,
        obj_to: 'user',
        obj_to_id: obj_id_apikey,
        privs: [{ type: 'create', list_obj: ['all'] }],
      }),
    );
    expect(responsLink.status).toBe(200);

    const responseModify = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID1,
        conv_id: newConv1,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api_copy',
            sync: false,
            err_node_id: '',
            conv_id: newConvKey,
            stage_id: newStageKey,
            project_id: newProjectKey,
            mode: 'create',
            ref: `ref_${Date.now()}`,
            data: {
              Object: 'Table',
            },
            data_type: { Object: 'string' },
            group: 'all',
          },
          {
            to_node_id: final_node_ID1,
            format: 'json',
            type: 'go',
            index: 1,
            node_title: 'final',
          },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModify.status).toBe(200);

    const responseCommit = await requestConfirm(api, newConv1, company_id);
    expect(responseCommit.status).toBe(200);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: newConv1,
        data: {
          Id: '123',
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    const task_id = responseTask.body.ops[0].obj_id;
    const ref = responseTask.body.ops[0].ref;

    await new Promise(r => setTimeout(r, 2000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: newConv1,
        ref_or_obj_id: task_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].data.__conveyor_copy_task_result__).toBe(`ok`);

    const responseListNode = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        conv_id: newConvKey,
        obj_id: final_node_IDKey,
        limit: 1,
      }),
    );
    expect(responseListNode.status).toBe(200);
    expect(responseListNode.body.ops[0].proc).toEqual('ok');
    expect(responseListNode.body.ops[0].list[0].data.Id).toBe('123');
    expect(responseListNode.body.ops[0].list[0].data.__copy_conv_id__).toBe(newConv1);
    expect(responseListNode.body.ops[0].list[0].data.__copy_ref_id__).toBe(ref);
    expect(responseListNode.body.ops[0].list[0].data.Object).toBe('Table');
  });

  test(`shouldn't copy_task from Folders to Projects`, async (): Promise<void> => {
    await requestModifyConvStatus(api, newConv3, company_id, 'active');

    const responseModify = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_IDFolders,
        conv_id: newConvFolders,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api_copy',
            sync: false,
            err_node_id: '',
            conv_id: newConv3,
            stage_id: newStage3,
            project_id: newProject1,
            mode: 'create',
            ref: `ref_${Date.now()}`,
            data: {
              Object: 'Table',
            },
            data_type: { Object: 'string' },
            group: 'all',
          },
          {
            to_node_id: final_node_IDFolders,
            format: 'json',
            type: 'go',
            index: 1,
            node_title: 'final',
          },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModify.status).toBe(200);

    const responseCommit = await requestConfirm(api, newConvFolders, company_id);
    expect(responseCommit.status).toBe(200);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,

        conv_id: newConvFolders,
        data: {
          Id: '123',
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    const task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: newConvFolders,
        ref_or_obj_id: task_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].data.__conveyor_copy_task_return_description__).toBe(
      `Process (ID: ${newConv3}) cannot be called from outside a project into a project or from within a project to outside a project`,
    );
  });

  afterAll(
    async (): Promise<void> => {
      const responseDelete1 = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject1, company_id);
      expect(responseDelete1.status).toBe(200);

      const responseDelete2 = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject2, company_id);
      expect(responseDelete2.status).toBe(200);

      const responseDeleteProjectKey = await requestDeleteObj(newApi, OBJ_TYPE.PROJECT, newProjectKey, company_id);
      expect(responseDeleteProjectKey.status).toBe(200);

      const responseDeleteConv = await requestDeleteObj(api, OBJ_TYPE.CONV, newConvFolders, company_id);
      expect(responseDeleteConv.status).toBe(200);

      const responseDeleteKey = await requestDeleteObj(api, OBJ_TYPE.USER, newApiKeyId, company_id);
      expect(responseDeleteKey.status).toBe(200);
    },
  );
});
