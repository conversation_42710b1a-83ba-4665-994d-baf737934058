import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';

describe('Condition', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let node_id_ok: string;
  let node_id_error: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: 'Condition',
        status: 'actived',
      }),
    );
    conv_id = response.body.ops[0].obj_id;

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
      }),
    );
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateNodeOK = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        title: 'OKfinal',
        conv_id,
        obj_type: 2,
        version: 22,
      }),
    );
    node_id_ok = responseCreateNodeOK.body.ops[0].obj_id;

    const responseCreateNodeError = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        title: 'Errorfinal',
        conv_id,
        obj_type: 2,
        version: 22,
      }),
    );
    node_id_error = responseCreateNodeError.body.ops[0].obj_id;

    const responseCreateCondition = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'process',
        obj_type: 0,
        logics: [
          {
            type: 'go_if_const',
            conditions: [{ param: 'a', const: '1', fun: 'more', cast: 'string' }],
            to_node_id: node_id_ok,
          },
          {
            type: 'go_if_const',
            conditions: [{ param: 'a', const: '1', fun: 'less', cast: 'string' }],
            to_node_id: node_id_error,
          },
          { to_node_id: final_node_ID, type: 'go', chTime: 1511868192509 },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateCondition.status).toBe(200);

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        conv_id: conv_id,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');
  });

  test('should create task condition "<",">"', async () => {
    const responseTask1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          a: 2,
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask1.status).toBe(200);
    expect(responseTask1.body.ops[0].proc).toEqual('ok');
    expect(responseTask1.body.ops[0].obj).toEqual('task');

    const responseTask2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          a: 0,
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask2.status).toBe(200);
    expect(responseTask2.body.ops[0].proc).toEqual('ok');
    expect(responseTask2.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 3000));
    const responseListOk = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: node_id_ok,
        conv_id,
        limit: 20,
      }),
    );
    expect(responseListOk.status).toBe(200);
    expect(responseListOk.body.ops[0].proc).toEqual('ok');
    expect(responseListOk.body.ops[0].obj).toEqual('node');
    expect(responseListOk.body.ops[0].list[0].data).toEqual({ a: 2 });

    const responseListError = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: node_id_error,
        conv_id,
        limit: 20,
      }),
    );
    expect(responseListError.status).toBe(200);
    expect(responseListError.body.ops[0].proc).toEqual('ok');
    expect(responseListError.body.ops[0].obj).toEqual('node');
    expect(responseListError.body.ops[0].list[0].data).toEqual({ a: 0 });
  });

  test('should create task condition "<=",">="', async () => {
    const responseCreateCondition = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'process',
        obj_type: 0,
        logics: [
          {
            type: 'go_if_const',
            conditions: [{ param: 'a', const: '1', fun: 'more_or_eq', cast: 'string' }],
            to_node_id: node_id_ok,
          },
          {
            type: 'go_if_const',
            conditions: [{ param: 'b', const: '1', fun: 'less_or_eq', cast: 'string' }],
            to_node_id: node_id_error,
          },
          { to_node_id: final_node_ID, type: 'go', chTime: 1511868192509 },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateCondition.status).toBe(200);

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        conv_id: conv_id,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');

    const responseTask1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          a: 1,
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask1.status).toBe(200);
    expect(responseTask1.body.ops[0].proc).toEqual('ok');
    expect(responseTask1.body.ops[0].obj).toEqual('task');

    const responseTask2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          b: -1,
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask2.status).toBe(200);
    expect(responseTask2.body.ops[0].proc).toEqual('ok');
    expect(responseTask2.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 3000));
    const responseListOk = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: node_id_ok,
        conv_id,
        limit: 1,
      }),
    );
    expect(responseListOk.status).toBe(200);
    expect(responseListOk.body.ops[0].proc).toEqual('ok');
    expect(responseListOk.body.ops[0].obj).toEqual('node');
    expect(responseListOk.body.ops[0].count).toEqual(2);
    expect(responseListOk.body.ops[0].list[0].data).toEqual({ a: 1 });

    const responseListError = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: node_id_error,
        conv_id,
        limit: 1,
      }),
    );
    expect(responseListError.status).toBe(200);
    expect(responseListError.body.ops[0].proc).toEqual('ok');
    expect(responseListError.body.ops[0].obj).toEqual('node');
    expect(responseListError.body.ops[0].count).toEqual(2);
    expect(responseListError.body.ops[0].list[0].data).toEqual({ b: -1 });
  });

  test('should create task condition "=","!="', async () => {
    const responseCreateCondition = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'process',
        obj_type: 0,
        logics: [
          {
            type: 'go_if_const',
            conditions: [{ param: 'c', const: '1', fun: 'eq', cast: 'string' }],
            to_node_id: node_id_ok,
          },
          {
            type: 'go_if_const',
            conditions: [{ param: 'b', const: '1', fun: 'not_eq', cast: 'string' }],
            to_node_id: node_id_error,
          },
          { to_node_id: final_node_ID, type: 'go', chTime: 1511868192509 },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateCondition.status).toBe(200);

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        conv_id: conv_id,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');

    const responseTask1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          c: '1',
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask1.status).toBe(200);
    expect(responseTask1.body.ops[0].proc).toEqual('ok');
    expect(responseTask1.body.ops[0].obj).toEqual('task');

    const responseTask2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          b: 2,
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask2.status).toBe(200);
    expect(responseTask2.body.ops[0].proc).toEqual('ok');
    expect(responseTask2.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 4000));
    const responseListOk = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: node_id_ok,
        conv_id,
        limit: 1,
      }),
    );
    expect(responseListOk.status).toBe(200);
    expect(responseListOk.body.ops[0].proc).toEqual('ok');
    expect(responseListOk.body.ops[0].obj).toEqual('node');
    expect(responseListOk.body.ops[0].count).toEqual(3);
    expect(responseListOk.body.ops[0].list[0].data).toEqual({ c: '1' });

    const responseListError = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: node_id_error,
        conv_id,
        limit: 1,
      }),
    );
    expect(responseListError.status).toBe(200);
    expect(responseListError.body.ops[0].proc).toEqual('ok');
    expect(responseListError.body.ops[0].obj).toEqual('node');
    expect(responseListError.body.ops[0].count).toEqual(3);
    expect(responseListError.body.ops[0].list[0].data).toEqual({ b: 2 });
  });

  test('should create task condition "RegExp", !=condition', async () => {
    const responseCreateCondition = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'process',
        obj_type: 0,
        logics: [
          {
            type: 'go_if_const',
            conditions: [
              { param: 'c', const: '[0-3][0-9]\\.(0|1)[0-9]\\.(19|20)[0-9]{2}', fun: 'regexp', cast: 'string' },
            ],
            to_node_id: node_id_ok,
          },
          {
            type: 'go_if_const',
            conditions: [{ param: 'b', const: '1', fun: 'not_eq', cast: 'string' }],
            to_node_id: node_id_error,
          },
          { to_node_id: final_node_ID, type: 'go', chTime: 1511868192509 },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateCondition.status).toBe(200);

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        conv_id: conv_id,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');

    const responseTask1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          c: '10.12.2023',
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask1.status).toBe(200);
    expect(responseTask1.body.ops[0].proc).toEqual('ok');
    expect(responseTask1.body.ops[0].obj).toEqual('task');

    const responseTask2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          b: 1,
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask2.status).toBe(200);
    expect(responseTask2.body.ops[0].proc).toEqual('ok');
    expect(responseTask2.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 4000));
    const responseListOk = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: node_id_ok,
        conv_id,
        limit: 1,
      }),
    );
    expect(responseListOk.status).toBe(200);
    expect(responseListOk.body.ops[0].proc).toEqual('ok');
    expect(responseListOk.body.ops[0].obj).toEqual('node');
    expect(responseListOk.body.ops[0].count).toEqual(4);
    expect(responseListOk.body.ops[0].list[0].data).toEqual({ c: '10.12.2023' });

    const responseListError = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID,
        conv_id,
        limit: 1,
      }),
    );
    expect(responseListError.status).toBe(200);
    expect(responseListError.body.ops[0].proc).toEqual('ok');
    expect(responseListError.body.ops[0].obj).toEqual('node');
    expect(responseListError.body.ops[0].count).toEqual(1);
    expect(responseListError.body.ops[0].list[0].data).toEqual({ b: 1 });
  });

  test('should create task condition object "<",">"', async () => {
    const responseCreateCondition = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'process',
        obj_type: 0,
        logics: [
          {
            type: 'go_if_const',
            conditions: [{ param: 'a', const: '{"x":2}', fun: 'more', cast: 'object' }],
            to_node_id: node_id_ok,
          },
          {
            type: 'go_if_const',
            conditions: [{ param: 'a', const: '{"x":1}', fun: 'less', cast: 'object' }],
            to_node_id: node_id_error,
          },
          { to_node_id: final_node_ID, type: 'go', chTime: 1511868192509 },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateCondition.status).toBe(200);

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        conv_id: conv_id,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');

    const responseTask1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          a: { x: 3 },
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask1.status).toBe(200);
    expect(responseTask1.body.ops[0].proc).toEqual('ok');
    expect(responseTask1.body.ops[0].obj).toEqual('task');

    const responseTask2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          a: { x: -1 },
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask2.status).toBe(200);
    expect(responseTask2.body.ops[0].proc).toEqual('ok');
    expect(responseTask2.body.ops[0].obj).toEqual('task');

    const responseTask3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          a: { x: 1 },
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask3.status).toBe(200);
    expect(responseTask3.body.ops[0].proc).toEqual('ok');
    expect(responseTask3.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 3000));
    const responseListOk = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: node_id_ok,
        conv_id,
        limit: 1,
      }),
    );
    expect(responseListOk.status).toBe(200);
    expect(responseListOk.body.ops[0].proc).toEqual('ok');
    expect(responseListOk.body.ops[0].obj).toEqual('node');
    expect(responseListOk.body.ops[0].count).toEqual(5);
    expect(responseListOk.body.ops[0].list[0].data).toEqual({ a: { x: 3 } });

    const responseListError = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: node_id_error,
        conv_id,
        limit: 1,
      }),
    );
    expect(responseListError.status).toBe(200);
    expect(responseListError.body.ops[0].proc).toEqual('ok');
    expect(responseListError.body.ops[0].obj).toEqual('node');
    expect(responseListError.body.ops[0].count).toEqual(4);
    expect(responseListError.body.ops[0].list[0].data).toEqual({ a: { x: -1 } });

    const responseListFinal = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID,
        conv_id,
        limit: 1,
      }),
    );
    expect(responseListFinal.status).toBe(200);
    expect(responseListFinal.body.ops[0].proc).toEqual('ok');
    expect(responseListFinal.body.ops[0].obj).toEqual('node');
    expect(responseListFinal.body.ops[0].count).toEqual(2);
    expect(responseListFinal.body.ops[0].list[0].data).toEqual({ a: { x: 1 } });
  });

  test('should create task condition array "<",">"', async () => {
    const responseCreateCondition = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'process',
        obj_type: 0,
        logics: [
          {
            type: 'go_if_const',
            conditions: [{ param: 'a', const: '[{"x":2}]', fun: 'more', cast: 'array' }],
            to_node_id: node_id_ok,
          },
          {
            type: 'go_if_const',
            conditions: [{ param: 'a', const: '[{"x":1}]', fun: 'less', cast: 'array' }],
            to_node_id: node_id_error,
          },
          { to_node_id: final_node_ID, type: 'go', chTime: 1511868192509 },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateCondition.status).toBe(200);

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        conv_id: conv_id,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');

    const responseTask1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          a: [{ x: 3 }],
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask1.status).toBe(200);
    expect(responseTask1.body.ops[0].proc).toEqual('ok');
    expect(responseTask1.body.ops[0].obj).toEqual('task');

    const responseTask2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          a: [{ x: -1 }],
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask2.status).toBe(200);
    expect(responseTask2.body.ops[0].proc).toEqual('ok');
    expect(responseTask2.body.ops[0].obj).toEqual('task');

    const responseTask3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          a: [{ x: 1 }],
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask3.status).toBe(200);
    expect(responseTask3.body.ops[0].proc).toEqual('ok');
    expect(responseTask3.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 3000));
    const responseListOk = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: node_id_ok,
        conv_id,
        limit: 1,
      }),
    );
    expect(responseListOk.status).toBe(200);
    expect(responseListOk.body.ops[0].proc).toEqual('ok');
    expect(responseListOk.body.ops[0].obj).toEqual('node');
    expect(responseListOk.body.ops[0].count).toEqual(6);
    expect(responseListOk.body.ops[0].list[0].data).toEqual({ a: [{ x: 3 }] });

    const responseListError = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: node_id_error,
        conv_id,
        limit: 1,
      }),
    );
    expect(responseListError.status).toBe(200);
    expect(responseListError.body.ops[0].proc).toEqual('ok');
    expect(responseListError.body.ops[0].obj).toEqual('node');
    expect(responseListError.body.ops[0].count).toEqual(5);
    expect(responseListError.body.ops[0].list[0].data).toEqual({ a: [{ x: -1 }] });

    const responseListFinal = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID,
        conv_id,
        limit: 1,
      }),
    );
    expect(responseListFinal.status).toBe(200);
    expect(responseListFinal.body.ops[0].proc).toEqual('ok');
    expect(responseListFinal.body.ops[0].obj).toEqual('node');
    expect(responseListFinal.body.ops[0].count).toEqual(3);
    expect(responseListFinal.body.ops[0].list[0].data).toEqual({ a: [{ x: 1 }] });
  });

  test('should create task condition boolean "<=",">="', async () => {
    const responseCreateCondition = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'process',
        obj_type: 0,
        logics: [
          {
            type: 'go_if_const',
            conditions: [{ param: 'a', const: '1', fun: 'more_or_eq', cast: 'boolean' }],
            to_node_id: node_id_ok,
          },
          {
            type: 'go_if_const',
            conditions: [{ param: 'a', const: '0', fun: 'less_or_eq', cast: 'boolean' }],
            to_node_id: node_id_error,
          },
          { to_node_id: final_node_ID, type: 'go', chTime: 1511868192509 },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateCondition.status).toBe(200);

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        conv_id: conv_id,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');

    const responseTask1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          a: 1,
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask1.status).toBe(200);
    expect(responseTask1.body.ops[0].proc).toEqual('ok');
    expect(responseTask1.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 1000));

    const responseTask2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          a: '0',
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask2.status).toBe(200);
    expect(responseTask2.body.ops[0].proc).toEqual('ok');
    expect(responseTask2.body.ops[0].obj).toEqual('task');

    const responseTask3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          a: true,
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask3.status).toBe(200);
    expect(responseTask3.body.ops[0].proc).toEqual('ok');
    expect(responseTask3.body.ops[0].obj).toEqual('task');

    const responseTask4 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          a: 5,
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask4.status).toBe(200);
    expect(responseTask4.body.ops[0].proc).toEqual('ok');
    expect(responseTask4.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 3000));
    const responseListOk = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: node_id_ok,
        conv_id,
        limit: 2,
      }),
    );
    expect(responseListOk.status).toBe(200);
    expect(responseListOk.body.ops[0].proc).toEqual('ok');
    expect(responseListOk.body.ops[0].obj).toEqual('node');
    expect(responseListOk.body.ops[0].count).toEqual(8);
    expect(responseListOk.body.ops[0].list[0].data).toEqual({ a: true });
    expect(responseListOk.body.ops[0].list[1].data).toEqual({ a: 1 });

    const responseListError = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: node_id_error,
        conv_id,
        limit: 1,
      }),
    );
    expect(responseListError.status).toBe(200);
    expect(responseListError.body.ops[0].proc).toEqual('ok');
    expect(responseListError.body.ops[0].obj).toEqual('node');
    expect(responseListError.body.ops[0].count).toEqual(6);
    expect(responseListError.body.ops[0].list[0].data).toEqual({ a: '0' });

    const responseListFinal = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID,
        conv_id,
        limit: 1,
      }),
    );
    expect(responseListFinal.status).toBe(200);
    expect(responseListFinal.body.ops[0].proc).toEqual('ok');
    expect(responseListFinal.body.ops[0].obj).toEqual('node');
    expect(responseListFinal.body.ops[0].count).toEqual(4);
    expect(responseListFinal.body.ops[0].list[0].data).toEqual({ a: 5 });
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
