import { User } from '../../../../infrastructure/model/User';
import { application } from '../../../../application/Application';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj, requestCreateObjNew } from '../../../../application/api/ApiObj';

describe('Trash auto-cleanup', () => {
  let user: User;
  let cookieUser: any;
  let company_id: any;
  let dashboard_id: number;
  let conv_id: number;
  let sd_id: number;
  let folder_id: number;
  let title_obj: string;
  let titleDeleted_obj: string;
  let createdObjectIds: number[];

  function getFormattedDate(days = 0): string {
    const now = new Date();
    const targetDate = new Date(now);
    targetDate.setDate(now.getDate() + days);

    const day = targetDate
      .getDate()
      .toString()
      .padStart(2, '0');
    const month = (targetDate.getMonth() + 1).toString().padStart(2, '0');
    const year = targetDate.getFullYear().toString();

    return `${day}_${month}_${year}`;
  }

  beforeAll(async () => {
    const DAYS_BACK_FOR_CLEANUP = -2;
    user = await application.getAuthorizedUser();
    company_id = '824a644e-884d-4b4d-b776-83bb48f3345a';
    cookieUser = await application.getApiUserClient(user);
    title_obj = `${getFormattedDate()}`;
    titleDeleted_obj = `${getFormattedDate(DAYS_BACK_FOR_CLEANUP)}`;

    const createConv = await requestCreateObj(cookieUser, OBJ_TYPE.CONV, company_id, title_obj);
    conv_id = createConv.body.ops[0].obj_id;

    const createDashboard = await requestCreateObj(cookieUser, OBJ_TYPE.DASHBOARD, company_id, title_obj);
    dashboard_id = createDashboard.body.ops[0].obj_id;

    const responseFolder = await requestCreateObj(cookieUser, OBJ_TYPE.FOLDER, company_id, title_obj);
    folder_id = responseFolder.body.ops[0].obj_id;

    const createSD = await requestCreateObjNew(cookieUser, OBJ_TYPE.CONV, company_id, title_obj, {
      conv_type: 'state',
      folder_id: 0,
    });
    sd_id = createSD.body.ops[0].obj_id;

    createdObjectIds = [folder_id, conv_id, sd_id, dashboard_id];
  });

  test("should delete the obj's created today", async () => {
    const objectsToDelete = [
      { type: OBJ_TYPE.FOLDER, id: folder_id },
      { type: OBJ_TYPE.CONV, id: conv_id },
      { type: OBJ_TYPE.CONV, id: sd_id },
      { type: OBJ_TYPE.DASHBOARD, id: dashboard_id },
    ];

    for (const obj of objectsToDelete) {
      const deleteResponse = await requestDeleteObj(cookieUser, obj.type, obj.id, company_id);
      expect(deleteResponse.status).toBe(RESP_STATUS.OK);
    }
  });

  test("should auto-delete the obj's from the trash", async () => {
    const listTrash = await cookieUser.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
        filter: 'deleted',
      }),
    );
    expect(listTrash.status).toBe(RESP_STATUS.OK);
    expect(listTrash.body.ops[0].proc).toBe(PROC_STATUS.OK);
    const objsDeletedToday = (listTrash.body.ops[0].list as Array<any>).map(item => item.obj_id);
    expect(objsDeletedToday).toEqual(expect.arrayContaining(createdObjectIds));

    const objsMustBeRemoved = (listTrash.body.ops[0].list as Array<any>).filter(
      item => item.title === titleDeleted_obj,
    );
    expect(objsMustBeRemoved).toHaveLength(0);
  });
});
