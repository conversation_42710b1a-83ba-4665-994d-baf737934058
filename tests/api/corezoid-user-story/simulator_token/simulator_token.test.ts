import { application } from '../../../../application/Application';
import { axiosInstance } from '../../../../application/api/AxiosClient';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import { User } from '../../../../infrastructure/model/User';
import { createAuthUser, Method } from '../../../../utils/request';

describe('Simulator token', () => {
  let token: string;
  let tokenS: string;
  let host: string;
  let hostS: string;
  let folder_id: string;
  let folder_id_copy: string;
  let user: User;
  let cookie: string;
  let userToken: any;
  let cookieUser: any;
  let simulatorToken: any;

  beforeAll(async () => {
    user = await application.getAuthorizedUser({}, 1);
    cookie = user.cookieUser;
    const config = ConfigurationManager.getConfiguration();
    host = config.getApiUrl();
    hostS = config.getSuperadminUrl();
    token = await application.createToken(0);
    userToken = createAuthUser(token, 'token');
    cookieUser = createAuthUser(cookie, 'cookie');
  });

  test('should create request system/conf by simulator_token', async () => {
    const uri = `${host}system/conf`;
    const response = await userToken.request({
      method: Method.GET,
      url: uri,
    });
    expect(response.status).toBe(200);
    expect(response.data.result).toBe('ok');
    expect(response.data.first_day_of_week).toBe('mon');
  });

  test('should create request auth/me by simulator_token', async () => {
    const uri = `${host}auth/me`;
    const response = await userToken.request({
      method: Method.POST,
      url: uri,
      data: { get: 'me' },
    });
    expect(response.status).toBe(200);
    expect(response.data.result).toBe('ok');
    expect(response.data.is_super_user).toBe(false);
  });

  test('should create request create:folder by simulator_token', async () => {
    const uri = `${host}api/2/json`;
    const response = await userToken.request({
      method: Method.POST,
      url: uri,
      data: { ops: [{ type: 'create', obj: 'folder', folder_id: '0', title: 'new', obj_type: 0 }] },
    });
    expect(response.status).toBe(200);
    expect(response.data.ops[0].obj).toBe('folder');
    folder_id = response.data.ops[0].obj_id;
  });

  test('should create request create:folder (api/2/json) by simulator_token', async () => {
    const uri = `${host}api/2/json`;
    const response = await userToken.request({
      method: Method.POST,
      url: uri,
      data: { ops: [{ type: 'create', obj: 'folder', folder_id: '0', title: 'new', obj_type: 0 }] },
    });
    expect(response.status).toBe(200);
    expect(response.data.ops[0].obj).toBe('folder');
    folder_id = response.data.ops[0].obj_id;
  });

  test('should create request list:folder (api/1/json) by simulator_token', async () => {
    const uri = `${host}api/1/json`;
    const response = await userToken.request({
      method: Method.POST,
      url: uri,
      data: { ops: [{ type: 'list', obj: 'folder', obj_id: '0' }] },
    });
    expect(response.status).toBe(200);
    expect(response.data.ops[0].obj).toBe('folder');
  });

  test('should create request download:folder by simulator_token', async () => {
    const uri = `${host}api/2/download`;
    const response = await userToken.request({
      method: Method.POST,
      url: uri,
      data: { ops: [{ obj: 'obj_scheme', obj_id: folder_id, format: 'zip', obj_type: 'folder', async: true }] },
    });
    expect(response.status).toBe(200);
    expect(response.data.ops[0].obj).toBe('obj_scheme');
    expect(response.data.ops[0].download_url).toInclude(`user_downloads/folder`);
  });

  test('should create request copy:folder by simulator_token', async () => {
    const uri = `${host}api/2/copy`;
    const response = await userToken.request({
      method: Method.POST,
      url: uri,
      data: {
        ops: [
          {
            type: 'create',
            obj: 'obj_copy',
            obj_id: folder_id,
            title: 'newf',
            obj_type: 'folder',
            async: false,
            obj_to_id: 0,
            obj_to_type: 'folder',
          },
        ],
      },
    });
    expect(response.status).toBe(200);
    expect(response.data.ops[0].obj).toBe('obj_copy');
    expect(response.data.ops[0].scheme[0].obj_type).toInclude(`folder`);
    folder_id_copy = response.data.ops[0].scheme[0].obj_id;
  });

  test('should not create request create:folder by simulator_token if origin not correct', async () => {
    const uri = `${host}api/2/json`;
    // Для этого теста нужно использовать axiosInstance, так как userToken.request автоматически добавляет правильный Origin
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { ops: [{ type: 'create', obj: 'folder', folder_id: '0', title: 'new', obj_type: 0 }] },
      headers: {
        Authorization: token,
        Origin: 'https://test.com',
      },
    });
    expect(response.status).toBe(403);
    expect(response.data.ops[0].proc).toBe('error');
    expect(response.data.ops[0].description).toBe('cookie or headers are not valid');
  });

  test('should not create request create:folder by simulator_token if token not correct', async () => {
    const uri = `${host}api/2/json`;
    // Для этого теста нужно использовать axiosInstance, так как userToken.request использует правильный токен
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { ops: [{ type: 'create', obj: 'folder', folder_id: '0', title: 'new', obj_type: 0 }] },
      headers: {
        Authorization: 'test',
        Origin: host,
      },
    });
    expect(response.status).toBe(403);
    expect(response.data.ops[0].proc).toBe('error');
    expect(response.data.ops[0].description).toBe('cookie or headers are not valid');
  });

  test('should create simulator_token super', async () => {
    const uri = `${host}system/tests/jwt_token/6/3`;
    const response = await cookieUser.request({
      method: Method.GET,
      url: uri,
    });
    expect(response.status).toBe(200);
    tokenS = response.data.token;
    simulatorToken = createAuthUser(`Simulator ${tokenS}`, 'token');
  });

  test('should create request superadmin by simulator_token', async () => {
    const uri = `${hostS}superadmin/api/1/json`;
    const response = await simulatorToken.request({
      method: Method.POST,
      url: uri,
      data: { type: 'list', obj: 'values' },
    });
    expect(response.status).toBe(200);
    expect(response.data.result).toBe('ok');
  });

  test('should not create request by expaired simulator_token', async () => {
    await new Promise(r => setTimeout(r, 4000));
    const uri = `${hostS}superadmin/api/1/json`;
    const response = await simulatorToken.request({
      method: Method.POST,
      url: uri,
      data: { type: 'list', obj: 'values' },
    });
    expect(response.status).toBe(403);
    expect(response.data.result).toBe('error');
    expect(response.data.message).toBe('Invalid cookie');
  });

  afterAll(async () => {
    const uri = `${host}api/2/json`;
    const response = await userToken.request({
      method: Method.POST,
      url: uri,
      data: { ops: [{ type: 'delete', obj: 'folder', obj_id: folder_id }] },
    });
    expect(response.status).toBe(200);

    const responseCopy = await userToken.request({
      method: Method.POST,
      url: uri,
      data: { ops: [{ type: 'delete', obj: 'folder', obj_id: folder_id_copy }] },
    });
    expect(responseCopy.status).toBe(200);
  });
});
