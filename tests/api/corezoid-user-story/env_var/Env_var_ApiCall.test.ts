import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { User } from '../../../../infrastructure/model/User';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
  generateName,
} from '../../../../utils/corezoidRequest';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObjNew,
  requestListConv,
} from '../../../../application/api/ApiObj';
import { ENV_VAR_TYPE, ENV_VAR_DATA_TYPE, CreateEnvVarResponse } from '../../../../application/api/obj_types/env_var';
import { NODE_TYPE, NODE_LOGIC_TYPE, ModifyNodeResponse } from '../../../../application/api/obj_types/node';

describe('Env_var', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let newOtherStage: string | number;
  let user: User;
  let cookieSAUser: string;
  let cookieSAVar: string | number;
  let dataVarJson: string | number;
  let testHeaderValue: string;
  let testRawBody: string;
  let task_id_api: number;
  let checkCount: number;
  let newVar: string | number;
  let varShortName: string | number;
  let newVarJson: string | number;
  let company_id: any;
  let project_short_name: string;
  let stage_short_name: string;
  let conv_id: number;
  let conv_other_id: number;
  let newApiKeyId: string | number;
  let newApiKeyLogin: number;
  let newApiKeySecret: string | number;
  let process_node_ID: string | number;
  let process_other_node_ID: string | number;
  let final_node_ID: string | number;
  let final_other_node_ID: string | number;
  let conv_type: string;
  let title: string;
  let env_raw_secr: string;
  let env_raw_cook: string;
  let env_json: string;
  let env_data_json: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = generateName(OBJ_TYPE.PROJECT);
    stage_short_name = generateName(OBJ_TYPE.STAGE);
    user = await application.getAuthorizedUser({ company: {} }, 1);
    cookieSAUser = user.cookieUser;
    env_raw_secr = generateName(OBJ_TYPE.OBJS);
    env_raw_cook = generateName(OBJ_TYPE.OBJS);
    env_json = generateName(OBJ_TYPE.OBJS);
    env_data_json = generateName(OBJ_TYPE.OBJS);
    title = generateName(OBJ_TYPE.OBJS);
    conv_type = 'process';

    const CreateKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(RESP_STATUS.OK);
    const user_api = CreateKeyResponse.body;
    const newApiKey = {
      key: `${user_api.ops[0].users[0].logins[0].obj_id}`,
      secret: user_api.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user_api.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newApiKeyId = +newApiKey.id;
    newApiKeyLogin = +newApiKey.key;
    newApiKeySecret = newApiKey.secret;

    const responseProject = await requestCreateObjNew(
      api,
      OBJ_TYPE.PROJECT,
      company_id,
      generateName(OBJ_TYPE.PROJECT),
      {
        short_name: project_short_name,
        stages: [],
      },
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await requestCreateObjNew(api, OBJ_TYPE.STAGE, company_id, generateName(OBJ_TYPE.STAGE), {
      short_name: stage_short_name,
      project_id: newProject,
    });
    newStage = responseStage.body.ops[0].obj_id;

    const responseOtherStage = await requestCreateObjNew(
      api,
      OBJ_TYPE.STAGE,
      company_id,
      generateName(OBJ_TYPE.STAGE),
      {
        short_name: stage_short_name + '1',
        project_id: newProject,
      },
    );
    newOtherStage = responseOtherStage.body.ops[0].obj_id;

    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        company_id,
        obj_to: 'user',
        obj_to_id: newApiKeyId,
        is_need_to_notify: false,
        privs: [
          { type: 'create', list_obj: ['all'] },
          { type: 'modify', list_obj: ['all'] },
          { type: 'delete', list_obj: ['all'] },
          { type: 'view', list_obj: ['all'] },
        ],
      }),
    );
    expect(responseLink.status).toBe(RESP_STATUS.OK);

    const responseConv = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, generateName(OBJ_TYPE.CONV), {
      conv_type,
      stage_id: newStage,
      project_id: newProject,
    });
    conv_id = responseConv.body.ops[0].obj_id;

    const responseOtherConv = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, generateName(OBJ_TYPE.CONV), {
      conv_type,
      stage_id: newOtherStage,
      project_id: newProject,
    });
    conv_other_id = responseOtherConv.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_id, company_id);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === conv_type).obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseOtherList = await requestListConv(api, conv_other_id, company_id);
    expect(responseOtherList.status).toBe(RESP_STATUS.OK);
    process_other_node_ID = (responseOtherList.body.ops[0].list as Array<any>).find(item => item.title === conv_type)
      .obj_id;
    final_other_node_ID = (responseOtherList.body.ops[0].list as Array<any>).find(item => item.title === 'final')
      .obj_id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,
        env_var_type: ENV_VAR_TYPE.Secret,
        data_type: ENV_VAR_DATA_TYPE.RAW,
        short_name: env_raw_secr,
        title,
        value: `${newApiKeySecret}`,
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer', 'url', 'extra_headers'] }],
      }),
    );
    const { ops: opsVarRaw } = response.body as CreateEnvVarResponse;
    const { obj_id: objIdEnvVar, obj: objenvVar } = opsVarRaw[0];
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(objenvVar).toBe(OBJ_TYPE.ENV_VAR);
    newVar = objIdEnvVar;
    varShortName = `env_var[@${env_raw_secr}]`;

    const createVarSACookies = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,
        env_var_type: ENV_VAR_TYPE.Secret,
        data_type: ENV_VAR_DATA_TYPE.RAW,
        short_name: env_raw_cook,
        title,
        value: `${cookieSAUser}`,
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['extra_headers'] }],
      }),
    );
    const { ops: opsVarRawCook } = createVarSACookies.body as CreateEnvVarResponse;
    const { obj_id: objIdEnvVarCook, obj: objenvVarCook } = opsVarRawCook[0];
    expect(createVarSACookies.status).toBe(RESP_STATUS.OK);
    expect(objenvVarCook).toBe(OBJ_TYPE.ENV_VAR);
    cookieSAVar = objIdEnvVarCook;

    const responseJson = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,
        env_var_type: ENV_VAR_TYPE.Visible,
        data_type: ENV_VAR_DATA_TYPE.JSON,
        short_name: env_json,
        title,
        value: JSON.stringify({
          url: ConfigurationManager.getConfiguration().getApiUrl(),
        }),
        project_id: newProject,
        stage_id: newStage,
        scopes: [
          { type: 'api_call', fields: ['cert_pem', 'api_secret_outer', 'url', 'extra_headers', 'extra', 'response'] },
        ],
      }),
    );
    const { ops: opsVarJson } = responseJson.body as CreateEnvVarResponse;
    const { obj_id: objIdopsVarJson, obj: objopsVarJson } = opsVarJson[0];
    expect(responseJson.status).toBe(RESP_STATUS.OK);
    expect(objopsVarJson).toBe(OBJ_TYPE.ENV_VAR);
    newVarJson = objIdopsVarJson;

    testHeaderValue = 'TestHeader';
    const requestDataJson = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,
        env_var_type: ENV_VAR_TYPE.Visible,
        data_type: ENV_VAR_DATA_TYPE.JSON,
        short_name: env_data_json,
        title,
        value: JSON.stringify({
          url: ConfigurationManager.getConfiguration().getUrl(),
          xheader: testHeaderValue,
          bodykey: 'testKey',
          bodyvalue: 'testValue',
        }),
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: ['url', 'extra', 'response'] }],
      }),
    );

    const { ops: opsVarJsonData } = requestDataJson.body as CreateEnvVarResponse;
    const { obj_id: objIdopsVarJsonData, obj: objopsVarJsonData } = opsVarJsonData[0];
    expect(requestDataJson.status).toBe(RESP_STATUS.OK);
    expect(objopsVarJsonData).toBe(OBJ_TYPE.ENV_VAR);
    dataVarJson = objIdopsVarJsonData;
    checkCount = 0;
    testRawBody = JSON.stringify({
      ops: [{ type: 'get', conv_id: conv_id, obj: 'callback_hash' }],
    });
  });

  const testCasesPositivePartOne = [
    {
      description: 'should reading url/key/secret from env_var_id',
      url: (): string => `{{env_var[${newVarJson}].url}}api/2/json/${newApiKeyLogin}`,
      api_secret_outer: (): string => `{{env_var[${newVar}]}}`,
    },
    {
      description: 'should reading key from env_var_short_name',
      url: (): string => `{{env_var[@${env_json}].url}}api/2/json/${newApiKeyLogin}`,
      api_secret_outer: (): string => `{{${varShortName}}}`,
    },
  ];

  describe.each(testCasesPositivePartOne)('$description', ({ url, api_secret_outer }) => {
    test(`env_var in API call`, async () => {
      const responseCreateNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id,
          title: 'LogicCallNew',
          description: '',
          obj_type: NODE_TYPE.Normal,
          logics: [
            {
              type: NODE_LOGIC_TYPE.Api,
              format: 'raw',
              method: 'POST',
              raw_body: testRawBody,
              url: url(),
              extra: {},
              extra_type: {},
              max_threads: 5,
              err_node_id: '',
              extra_headers: {},
              send_sys: true,
              cert_pem: '',
              content_type: 'application/json',
              api_secret_outer: api_secret_outer(),
            },
            { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
          ],
          semaphors: [],
          version: 22,
        }),
      );

      expect(responseCreateNode.status).toBe(RESP_STATUS.OK);
      const { request_proc: procCreateNode } = responseCreateNode.body as ModifyNodeResponse;
      expect(procCreateNode).toBe(PROC_STATUS.OK);

      const responseCommit = await requestConfirm(api, conv_id, company_id);
      expect(responseCommit.status).toBe(RESP_STATUS.OK);
      const { proc: procCommit } = responseCommit.body.ops[0];
      expect(procCommit).toEqual(PROC_STATUS.OK);

      const responseTask = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          company_id,
          conv_id: conv_id,
          data: { one: varShortName, data: 201 },
          ref: `ref_${Date.now()}`,
        }),
      );
      expect(responseTask.status).toBe(RESP_STATUS.OK);
      const { proc: procTask, obj: objTask, obj_id: ObjIdTask } = responseTask.body.ops[0];
      expect(procTask).toEqual(PROC_STATUS.OK);
      expect(objTask).toEqual(OBJ_TYPE.TASK);
      task_id_api = ObjIdTask;

      await new Promise(r => setTimeout(r, 4000));

      const responseShow = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.TASK,
          conv_id: conv_id,
          ref_or_obj_id: task_id_api,
        }),
      );
      expect(responseShow.status).toBe(RESP_STATUS.OK);
      const { proc: prockShowTask, data: dataShowTask } = responseShow.body.ops[0];
      expect(prockShowTask).toEqual(PROC_STATUS.OK);
      const { data: dataShow, one: oneShow, ops: opsShow } = dataShowTask;
      expect(dataShow).toEqual(RESP_STATUS.CREATED);
      expect(oneShow).toEqual(varShortName);
      expect(opsShow[0].proc).toEqual(PROC_STATUS.OK);

      const responseShowApicall = await requestList(api, final_node_ID, conv_id, company_id);
      expect(responseShowApicall.status).toBe(RESP_STATUS.OK);
      const { proc: procShowApi, count: countShowApi } = responseShowApicall.body.ops[0];
      expect(procShowApi).toEqual(PROC_STATUS.OK);
      expect(countShowApi).toBeGreaterThan(checkCount);
      checkCount = countShowApi;
    });
  });

  const testCasesPositivePartTwo = [
    {
      description: 'should reading url/extra_headers/extra from env_var_id',
      bodykey: (): string => `{{env_var[${dataVarJson}].bodykey}}`,
      bodyvalue: (): string => `{{env_var[${dataVarJson}].bodyvalue}}`,
      urlVar: (): string => `{{env_var[${dataVarJson}].url}}`,
      cookie: (): string => `{{env_var[${cookieSAVar}]}}`,
    },
    {
      description: 'should reading url/extra_headers/extra from env_var_short_name',
      bodykey: (): string => `{{env_var[@${env_data_json}].bodykey}}`,
      bodyvalue: (): string => `{{env_var[@${env_data_json}].bodyvalue}}`,
      urlVar: (): string => `{{env_var[@${env_data_json}].url}}`,
      cookie: (): string => `{{env_var[@${env_raw_cook}]}}`,
    },
  ];

  describe.each(testCasesPositivePartTwo)('$description', ({ bodyvalue, urlVar, cookie }) => {
    test(`env_var in API call`, async () => {
      const testBody = { testKey: 'testValue' };
      const responseCreateNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id,
          title: 'LogicCallNew',
          description: '',
          obj_type: NODE_TYPE.Normal,
          logics: [
            {
              type: NODE_LOGIC_TYPE.Api,
              format: 'raw',
              method: 'POST',
              raw_body: JSON.stringify({
                body: {
                  testKey: `${bodyvalue()}`,
                },
              }),
              url: `${urlVar()}system/tests/echo`,
              extra: {},
              extra_type: {},
              max_threads: 5,
              err_node_id: '',
              extra_headers: { Cookie: `${cookie()}`, origin: `{{env_var[@${env_json}]}}` },
              send_sys: true,
              cert_pem: '',
              content_type: 'application/json',
              api_secret_outer: '',
            },
            { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseCreateNode.status).toBe(RESP_STATUS.OK);
      const { request_proc: procCreateNode, ops: opsCreateNode } = responseCreateNode.body as ModifyNodeResponse;
      expect(procCreateNode).toBe(PROC_STATUS.OK);
      const { proc: procOpsCreateNode } = opsCreateNode[0];
      expect(procOpsCreateNode).toEqual(PROC_STATUS.OK);

      const responseCommit = await requestConfirm(api, conv_id, company_id);
      expect(responseCommit.status).toBe(RESP_STATUS.OK);
      const { proc: procCommit } = responseCommit.body.ops[0];
      expect(procCommit).toEqual(PROC_STATUS.OK);

      const responseTask = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          company_id,
          conv_id: conv_id,
          data: {},
          ref: `ref_${Date.now()}`,
        }),
      );
      expect(responseTask.status).toBe(RESP_STATUS.OK);
      const { proc: procTask, obj: objTask, obj_id: ObjIdTask } = responseTask.body.ops[0];
      expect(procTask).toEqual(PROC_STATUS.OK);
      expect(objTask).toEqual(OBJ_TYPE.TASK);
      task_id_api = ObjIdTask;

      await new Promise(r => setTimeout(r, 4000));

      const responseShow = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.TASK,
          conv_id: conv_id,
          ref_or_obj_id: task_id_api,
        }),
      );
      expect(responseShow.status).toBe(RESP_STATUS.OK);
      const { proc: prockShowTask, data: dataShowTask } = responseShow.body.ops[0];
      expect(prockShowTask).toEqual(PROC_STATUS.OK);
      expect(dataShowTask).toEqual(testBody);

      const responseShowApicall = await requestList(api, final_node_ID, conv_id, company_id);
      expect(responseShowApicall.status).toBe(RESP_STATUS.OK);
      const { proc: procShowApi, count: countShowApi } = responseShowApicall.body.ops[0];
      expect(procShowApi).toEqual(PROC_STATUS.OK);
      expect(countShowApi).toBeGreaterThan(checkCount);
      checkCount = countShowApi;
    });
  });

  test(`shouldn't reading env_var in body or header without scope`, async () => {
    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'LogicCallNew',
        description: '',
        obj_type: NODE_TYPE.Normal,
        logics: [
          {
            type: NODE_LOGIC_TYPE.Api,
            format: 'raw',
            method: 'POST',
            raw_body: JSON.stringify({ body: { secr_key: `{{${varShortName}}}` } }),
            url: `{{env_var[${dataVarJson}].url}}system/tests/echo`,
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: '',
            extra_headers: { Cookie: `{{env_var[${cookieSAVar}]}}`, origin: `{{env_var[@${env_json}]}}` },
            send_sys: true,
            cert_pem: '',
            content_type: 'application/json',
            api_secret_outer: '',
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(RESP_STATUS.OK);
    const { request_proc: procCreateNode, ops: opsCreateNode } = responseCreateNode.body as ModifyNodeResponse;
    expect(procCreateNode).toBe(PROC_STATUS.OK);
    const { proc: procOpsCreateNode } = opsCreateNode[0];
    expect(procOpsCreateNode).toEqual(PROC_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);
    const { proc: procCommit } = responseCommit.body.ops[0];
    expect(procCommit).toEqual(PROC_STATUS.OK);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id,
        data: { data: 201 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    const { proc: procTask, obj: objTask, obj_id: ObjIdTask } = responseTask.body.ops[0];
    expect(procTask).toEqual(PROC_STATUS.OK);
    expect(objTask).toEqual(OBJ_TYPE.TASK);
    task_id_api = ObjIdTask;

    await new Promise(r => setTimeout(r, 4000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        ref_or_obj_id: task_id_api,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    const { proc: prockShowTask, data: dataShowTask } = responseShow.body.ops[0];
    expect(prockShowTask).toEqual(PROC_STATUS.OK);
    expect(dataShowTask.secr_key).toBeEmpty();

    const responseShowApicall = await requestList(api, final_node_ID, conv_id, company_id);
    expect(responseShowApicall.status).toBe(RESP_STATUS.OK);
    const { proc: procShowApi, count: countShowApi } = responseShowApicall.body.ops[0];
    expect(procShowApi).toEqual(PROC_STATUS.OK);
    expect(countShowApi).toBeGreaterThan(checkCount);
    checkCount = countShowApi;
  });

  test(`shouldn't reading url/key/secret from another staging`, async () => {
    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_other_node_ID,
        conv_id: conv_other_id,
        title: 'LogicCallNew',
        description: '',
        obj_type: NODE_TYPE.Normal,
        logics: [
          {
            type: NODE_LOGIC_TYPE.Api,
            format: 'raw',
            method: 'POST',
            raw_body: JSON.stringify({
              ops: [{ type: 'get', conv_id: conv_other_id, obj: 'callback_hash' }],
            }),
            url: `{{env_var[${newVarJson}].url}}api/2/json/${newApiKeyLogin}`,
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: '',
            extra_headers: {},
            send_sys: true,
            cert_pem: '',
            content_type: 'application/json',
            api_secret_outer: `{{env_var[${newVar}]}}`,
          },
          { node_title: 'final', to_node_id: final_other_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(RESP_STATUS.OK);
    const { request_proc: procCreateNode, ops: opsCreateNode } = responseCreateNode.body as ModifyNodeResponse;
    expect(procCreateNode).toBe(PROC_STATUS.OK);
    const { proc: procOpsCreateNode } = opsCreateNode[0];
    expect(procOpsCreateNode).toEqual(PROC_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_other_id, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);
    const { proc: procCommit } = responseCommit.body.ops[0];
    expect(procCommit).toEqual(PROC_STATUS.OK);

    const env_var_id = `env_var[${newVar}]`;
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_other_id,
        data: { one: env_var_id, data: 201 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    const { proc: procTask, obj: objTask, obj_id: ObjIdTask } = responseTask.body.ops[0];
    expect(procTask).toEqual(PROC_STATUS.OK);
    expect(objTask).toEqual(OBJ_TYPE.TASK);
    task_id_api = ObjIdTask;

    await new Promise(r => setTimeout(r, 4000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_other_id,
        ref_or_obj_id: task_id_api,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    const { proc: prockShowTask, data: dataShowTask } = responseShow.body.ops[0];
    expect(prockShowTask).toEqual(PROC_STATUS.OK);
    expect(dataShowTask.__conveyor_api_return_type_tag__).toEqual('env_var_not_found');
    expect(dataShowTask.one).toEqual(env_var_id);
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
