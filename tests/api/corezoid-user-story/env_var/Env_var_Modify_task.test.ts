import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestListConv,
  requestCreateObjNew,
  requestCreateTask,
  requestShow,
} from '../../../../application/api/ApiObj';

describe('Env_var Modify_task (positive)', (): void => {
  let api: ApiKeyClient;
  let apikey: ApiKey;

  let newProject: string | number;
  let newStage: string | number;
  let project_short_name: string;
  let stage_short_name: string;
  let company_id: any;

  let newVarVisibleAll: string | number;
  let env_var_visible_short_name2: string;

  let newVarVisible: string | number;
  let env_var_visible_short_name: string;
  let env_var_visible_short_name1: string;

  let newConv: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let newNode: string;
  let task_id: string;

  let newStageModifyTo: string | number;
  let stage_short_name2: string;
  let newConvModifyTo: number;
  let process_node_IDModifyTo: string | number;
  let final_node_IDModifyTo: string | number;

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;
      project_short_name = `project-${Date.now()}`;
      stage_short_name = `stage-${Date.now()}`;
      stage_short_name2 = `stage2-${Date.now()}`;
      env_var_visible_short_name = `varvisible-${Date.now()}`;
      env_var_visible_short_name1 = `varvisible1-${Date.now()}`;
      env_var_visible_short_name2 = `varvisible2-${Date.now()}`;

      const responseProject = await requestCreateObjNew(api, OBJ_TYPE.PROJECT, company_id, `Project_${Date.now()}`, {
        short_name: project_short_name,
        description: 'test',
        stages: [],
      });
      newProject = responseProject.body.ops[0].obj_id;

      const responseStage = await requestCreateObjNew(api, OBJ_TYPE.STAGE, company_id, `Stage_${Date.now()}`, {
        short_name: stage_short_name,
        description: 'test',
        project_id: newProject,
      });
      newStage = responseStage.body.ops[0].obj_id;

      const responseConv = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, `Conv_${Date.now()}`, {
        conv_type: 'process',
        stage_id: newStage,
        project_id: newProject,
      });
      newConv = responseConv.body.ops[0].obj_id;

      const responseNode = await requestCreateObjNew(api, OBJ_TYPE.NODE, company_id, `success`, {
        conv_id: newConv,
        obj_type: 2,
        version: 22,
      });
      newNode = responseNode.body.ops[0].obj_id;

      const responseList = await requestListConv(api, newConv, company_id);
      expect(responseList.status).toBe(200);
      process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
      final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

      const responseStageModifyTo = await requestCreateObjNew(api, OBJ_TYPE.STAGE, company_id, `Stage_${Date.now()}`, {
        short_name: stage_short_name2,
        description: 'test',
        project_id: newProject,
      });
      newStageModifyTo = responseStageModifyTo.body.ops[0].obj_id;

      const responseConvCopyTo = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, `Conv_${Date.now()}`, {
        conv_type: 'process',
        stage_id: newStageModifyTo,
        project_id: newProject,
      });
      newConvModifyTo = responseConvCopyTo.body.ops[0].obj_id;

      const responseListCopyTo = await requestListConv(api, newConvModifyTo, company_id);
      expect(responseListCopyTo.status).toBe(200);
      process_node_IDModifyTo = (responseListCopyTo.body.ops[0].list as Array<any>).find(
        item => item.title === 'process',
      ).obj_id;
      final_node_IDModifyTo = (responseListCopyTo.body.ops[0].list as Array<any>).find(item => item.title === 'final')
        .obj_id;

      const responseModifySync = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_IDModifyTo,
          conv_id: newConvModifyTo,
          title: 'process',
          description: '',
          obj_type: 0,
          logics: [
            { type: 'api_callback', is_sync: true, obj_id_path: 'ref' },
            {
              node_title: 'final',
              to_conv_title: 'post_application/json',
              to_node_title: 'final',
              to_node_id: final_node_IDModifyTo,
              edit: true,
              format: 'json',
              type: 'go',
              conv_title: 'post_application/json',
            },
          ],
          semaphors: [],
          node_location: [50, 180],
          version: 22,
        }),
      );
      expect(responseModifySync.status).toBe(RESP_STATUS.OK);

      const responseCommit2 = await requestConfirm(api, newConvModifyTo, company_id);
      expect(responseCommit2.status).toBe(RESP_STATUS.OK);

      const responseVisible = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ENV_VAR,
          company_id,
          env_var_type: 'visible',
          data_type: 'raw',
          short_name: `${env_var_visible_short_name}`,
          description: 'test',
          title: 'VarVisibleRaw',
          value: 'test_Var_Modify',
          project_id: newProject,
          stage_id: newStage,
          scopes: [{ type: '*', fields: '*' }],
        }),
      );
      expect(responseVisible.status).toBe(RESP_STATUS.OK);
      expect(responseVisible.body.ops[0].obj).toBe('env_var');
      newVarVisibleAll = responseVisible.body.ops[0].obj_id;

      const value = JSON.stringify({
        conv: `${newConvModifyTo}`,
        stage: `${newStageModifyTo}`,
        project: `${newProject}`,
        data: 'dataVar',
      });
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ENV_VAR,
          company_id,
          env_var_type: 'visible',
          data_type: 'json',
          short_name: `${env_var_visible_short_name2}`,
          description: 'test',
          title: 'VarVisible',
          value,
          project_id: newProject,
          stage_id: newStage,
          scopes: [{ type: 'api_copy', fields: ['project_id', 'stage_id', 'conv_id', 'ref', 'data'] }],
        }),
      );
      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].obj).toBe('env_var');
      newVarVisible = response.body.ops[0].obj_id;
    },
  );

  const testCasesPositive = [
    {
      description: 'should reading env_var in Modify_task by id',
      conv_id: (): string => `{{env_var[${newVarVisible}].conv}}`,
      stage_id: (): string => `{{env_var[${newVarVisible}].stage}}`,
      project_id: (): string => `{{env_var[${newVarVisible}].project}}`,
      data: (): string => `{{env_var[${newVarVisible}].data}}`,
      ref: (): string => `{{env_var[${newVarVisibleAll}]}}`,
      scopes: (): { type: string; fields: string[] } => ({
        type: 'api_copy',
        fields: ['project_id', 'stage_id', 'conv_id', 'ref', 'data'],
      }),
    },
    {
      description: 'should reading env_var in Modify_task by short_name',
      conv_id: (): string => `{{env_var[@${env_var_visible_short_name2}].conv}}`,
      stage_id: (): string => `{{env_var[@${env_var_visible_short_name2}].stage}}`,
      project_id: (): string => `{{env_var[@${env_var_visible_short_name2}].project}}`,
      data: (): string => `{{env_var[@${env_var_visible_short_name2}].data}}`,
      ref: (): string => `{{env_var[@${env_var_visible_short_name}]}}`,
      scopes: (): { type: string; fields: string[] } => ({
        type: 'api_copy',
        fields: ['project_id', 'stage_id', 'conv_id', 'ref', 'data'],
      }),
    },
    {
      description: 'should reading env_var in Modify_task by short_name (scopes all)',
      conv_id: (): string => `{{env_var[@${env_var_visible_short_name2}].conv}}`,
      stage_id: (): string => `{{env_var[@${env_var_visible_short_name2}].stage}}`,
      project_id: (): string => `{{env_var[@${env_var_visible_short_name2}].project}}`,
      data: (): string => `{{env_var[@${env_var_visible_short_name2}].data}}`,
      ref: (): string => `{{env_var[@${env_var_visible_short_name}]}}`,
      scopes: (): { type: string; fields: string } => ({ type: '*', fields: '*' }),
    },
    {
      description: 'should reading env_var in Modify_task by short_name (fields all)',
      conv_id: (): string => `{{env_var[@${env_var_visible_short_name2}].conv}}`,
      stage_id: (): string => `{{env_var[@${env_var_visible_short_name2}].stage}}`,
      project_id: (): string => `{{env_var[@${env_var_visible_short_name2}].project}}`,
      data: (): string => `{{env_var[@${env_var_visible_short_name2}].data}}`,
      ref: (): string => `{{env_var[@${env_var_visible_short_name}]}}`,
      scopes: (): { type: string; fields: string } => ({ type: 'api_copy', fields: '*' }),
    },
    {
      description: 'should reading env_var in Modify_task by short_name (selected fields)',
      conv_id: (): string => `{{env_var[@${env_var_visible_short_name2}].conv}}`,
      stage_id: (): string => `{{env_var[@${env_var_visible_short_name2}].stage}}`,
      project_id: (): string => `${newProject}`,
      data: (): string => `dataVar`,
      ref: (): string => `{{env_var[@${env_var_visible_short_name}]}}`,
      scopes: (): { type: string; fields: string[] } => ({ type: 'api_copy', fields: ['stage_id', 'conv_id', 'ref'] }),
    },
  ];

  describe.each(testCasesPositive)('$description', ({ conv_id, stage_id, project_id, data, ref, scopes }): void => {
    let previousCount = 0;
    beforeAll(
      async (): Promise<void> => {
        const initialResponse = await requestList(api, final_node_ID, newConv, company_id);
        expect(initialResponse.status).toBe(200);
        previousCount = initialResponse.body.ops[0]?.count || 0;
      },
    );

    test(`env_var in modify_task`, async (): Promise<void> => {
      const value = JSON.stringify({
        conv: `${newConvModifyTo}`,
        stage: `${newStageModifyTo}`,
        project: `${newProject}`,
        data: 'dataVar',
      });
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.ENV_VAR,
          company_id,
          obj_id: newVarVisible,
          env_var_type: 'visible',
          data_type: 'json',
          short_name: `${env_var_visible_short_name2}`,
          description: 'test',
          title: 'VarVisible',
          value,
          project_id: newProject,
          stage_id: newStage,
          scopes: [scopes()],
        }),
      );
      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].obj).toBe('env_var');

      await requestCreateTask(api, OBJ_TYPE.TASK, company_id, newConvModifyTo, 'test_Var_Modify', {
        a: `new`,
      });

      const responseModifyNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id: newConv,
          title: 'Conditions',
          description: '',
          obj_type: 0,
          logics: [
            {
              type: 'api_copy',
              mode: 'modify',
              conv_id: conv_id(),
              stage_id: stage_id(),
              project_id: project_id(),
              data: { a: data() },
              data_type: { a: 'string' },
              group: '',
              ref: ref(),
              err_node_id: `${newNode}`,
            },
            { node_title: 'final', to_node_id: `${final_node_ID}`, format: 'json', type: 'go' },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseModifyNode.status).toBe(RESP_STATUS.OK);
      expect(responseModifyNode.body.ops[0].proc).toEqual(PROC_STATUS.OK);

      const responseCommit = await requestConfirm(api, newConv, company_id);
      expect(responseCommit.status).toBe(RESP_STATUS.OK);
      expect(responseCommit.body.ops[0].proc).toEqual(PROC_STATUS.OK);

      const responseTask = await requestCreateTask(api, OBJ_TYPE.TASK, company_id, newConv, 'ref', {
        data: 202,
      });
      task_id = responseTask.body.ops[0].obj_id;
      await new Promise(r => setTimeout(r, 3000));

      const responsShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: newConv });
      expect(responsShowTask.status).toBe(RESP_STATUS.OK);
      expect(responsShowTask.body.ops[0].data.data).toEqual(202);
      expect(responsShowTask.body.ops[0].data.__conveyor_copy_task_result__).toEqual(`ok`);

      const responseShow = await requestList(api, final_node_ID, newConv, company_id);
      expect(responseShow.status).toBe(RESP_STATUS.OK);
      const currentCount = responseShow.body.ops[0].count;
      expect(currentCount).toBeGreaterThan(previousCount);
      previousCount = currentCount;

      const responseShowCopyTo = await requestList(api, final_node_IDModifyTo, newConvModifyTo, company_id);
      expect(responseShowCopyTo.status).toBe(RESP_STATUS.OK);
      expect(responseShowCopyTo.body.ops[0].list[0].ref).toEqual(`test_Var_Modify`);
      expect(responseShowCopyTo.body.ops[0].list[0].data.a).toEqual(`dataVar`);
    });
  });

  const testCasesNegative = [
    {
      description: `shouldn't reading env_var in Modify_task (only field conv_id in scope)`,
      conv_id: (): string => `{{env_var[@${env_var_visible_short_name1}].conv}}`,
      stage_id: (): string => `${newStageModifyTo}`,
      project_id: (): string => `${newProject}`,
      data: (): string => `test`,
      ref: (): string => `{{env_var[@${env_var_visible_short_name1}].ref}}`,
      scope: (): Array<{ type: string; fields: string[] }> => [{ type: 'api_copy', fields: ['conv_id'] }],
      resp: (): string =>
        `EnvVariable ${env_var_visible_short_name1} doesn't found in current stage. Maybe it was deleted`,
    },
    {
      description: `shouldn't reading env_var in Modify_task (not correct scope)`,
      conv_id: (): string => `{{env_var[@${env_var_visible_short_name1}].conv}}`,
      stage_id: (): string => `{{env_var[@${env_var_visible_short_name1}].stage}}`,
      project_id: (): string => `{{env_var[@${env_var_visible_short_name1}].project}}`,
      data: (): string => `{{env_var[@${env_var_visible_short_name1}].data}}`,
      ref: (): string => `{{env_var[@${env_var_visible_short_name1}].ref}}`,
      scope: (): Array<{ type: string; fields: string[] }> => [{ type: 'api_call', fields: ['url'] }],
      resp: (): string =>
        `EnvVariable ${env_var_visible_short_name1} doesn't found in current stage. Maybe it was deleted`,
    },
    {
      description: `shouldn't reading env_var in copy_task (not existing env_var)`,
      conv_id: (): string => `{{env_var[@${stage_short_name}].conv}}`,
      stage_id: (): string => `${newStageModifyTo}`,
      project_id: (): string => `${newProject}`,
      data: (): string => `test`,
      ref: (): string => `test`,
      scope: (): Array<{ type: string; fields: string[] }> => [{ type: 'api_copy', fields: ['conv_id'] }],
      resp: (): string => `EnvVariable @${stage_short_name} doesn't found in current stage. Maybe it was deleted`,
    },
  ];

  describe.each(testCasesNegative)(
    '$description',
    ({ conv_id, stage_id, project_id, data, ref, scope, resp }): void => {
      let previousCount = 0;
      beforeAll(
        async (): Promise<void> => {
          const initialResponse = await requestList(api, newNode, newConv, company_id);
          expect(initialResponse.status).toBe(RESP_STATUS.OK);
          previousCount = initialResponse.body.ops[0]?.count || 0;
        },
      );

      test(`env_var in modify_task`, async (): Promise<void> => {
        const value = JSON.stringify({
          conv: `${newConvModifyTo}`,
          stage: `${newStageModifyTo}`,
          project: `${newProject}`,
          data: 'dataVar',
          ref: 'test_Var_Modify',
        });
        const responseVar = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MODIFY,
            obj: OBJ_TYPE.ENV_VAR,
            obj_id: newVarVisible,
            company_id,
            env_var_type: 'visible',
            data_type: 'json',
            short_name: `${env_var_visible_short_name1}`,
            description: 'test',
            title: 'VarVisibleRaw',
            value,
            project_id: newProject,
            stage_id: newStage,
            scopes: scope(),
          }),
        );
        expect(responseVar.status).toBe(RESP_STATUS.OK);
        expect(responseVar.body.ops[0].obj).toBe('env_var');

        await requestCreateTask(api, OBJ_TYPE.TASK, company_id, newConvModifyTo, 'test_Var_Modify', {
          a: `new`,
        });

        const responseModifyNode = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MODIFY,
            obj: OBJ_TYPE.NODE,
            obj_id: process_node_ID,
            conv_id: newConv,
            title: 'Conditions',
            description: '',
            obj_type: 0,
            logics: [
              {
                type: 'api_copy',
                mode: 'modify',
                conv_id: conv_id(),
                stage_id: stage_id(),
                project_id: project_id(),
                data: { a: data() },
                data_type: { a: 'string' },
                group: '',
                ref: ref(),
                err_node_id: `${newNode}`,
              },
              { node_title: 'final', to_node_id: `${final_node_ID}`, format: 'json', type: 'go' },
            ],
            semaphors: [],
            version: 22,
          }),
        );
        expect(responseModifyNode.status).toBe(RESP_STATUS.OK);
        expect(responseModifyNode.body.ops[0].proc).toEqual('ok');

        const responseCommit = await requestConfirm(api, newConv, company_id);
        expect(responseCommit.status).toBe(RESP_STATUS.OK);
        expect(responseCommit.body.ops[0].proc).toEqual(PROC_STATUS.OK);

        const responseTask = await requestCreateTask(api, OBJ_TYPE.TASK, company_id, newConv, 'ref', {
          data: 202,
        });
        task_id = responseTask.body.ops[0].obj_id;
        await new Promise(r => setTimeout(r, 3000));

        const responsShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: newConv });
        expect(responsShowTask.status).toBe(RESP_STATUS.OK);
        expect(responsShowTask.body.ops[0].data.__conveyor_copy_task_return_description__).toEqual(resp());

        const responseShow = await requestList(api, newNode, newConv, company_id);
        expect(responseShow.status).toBe(RESP_STATUS.OK);
        const currentCount = responseShow.body.ops[0].count;
        expect(currentCount).toBeGreaterThan(previousCount);
        previousCount = currentCount;
      });
    },
  );

  afterAll(
    async (): Promise<void> => {
      const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
      expect(response.status).toBe(RESP_STATUS.OK);
    },
  );
});
