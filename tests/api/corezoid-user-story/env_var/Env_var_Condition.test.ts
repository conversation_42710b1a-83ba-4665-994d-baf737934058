import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestListConv,
  requestCreateObjNew,
  requestCreateTask,
} from '../../../../application/api/ApiObj';

describe('Env_var in condition (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;

  let newProject: string | number;
  let newStage: string | number;
  let project_short_name: string;
  let stage_short_name: string;
  let company_id: any;
  let env_var_visible_short_nameAllL: string;

  let newVarVisibleAllF: string | number;
  let env_var_visible_short_nameAllF: string;

  let newVarVisible: string | number;
  let env_var_visible_short_name: string;

  let newConv: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let newNode: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = `project-${Date.now()}`;
    stage_short_name = `stage-${Date.now()}`;
    env_var_visible_short_name = `varvisible-${Date.now()}`;
    env_var_visible_short_nameAllL = `varvisiblealll-${Date.now()}`;
    env_var_visible_short_nameAllF = `varvisibleallf-${Date.now()}`;

    const responseProject = await requestCreateObjNew(api, OBJ_TYPE.PROJECT, company_id, `Project_${Date.now()}`, {
      short_name: project_short_name,
      description: 'test',
      stages: [],
    });
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await requestCreateObjNew(api, OBJ_TYPE.STAGE, company_id, `Stage_${Date.now()}`, {
      short_name: stage_short_name,
      description: 'test',
      project_id: newProject,
    });
    newStage = responseStage.body.ops[0].obj_id;

    const responseConv = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, `Conv_${Date.now()}`, {
      conv_type: 'process',
      stage_id: newStage,
      project_id: newProject,
    });
    newConv = responseConv.body.ops[0].obj_id;

    const responseNode = await requestCreateObjNew(api, OBJ_TYPE.NODE, company_id, `success`, {
      conv_id: newConv,
      obj_type: 2,
      version: 22,
    });
    newNode = responseNode.body.ops[0].obj_id;

    const responseList = await requestListConv(api, newConv, company_id);
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseVisibleAllFields = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,
        env_var_type: 'visible',
        data_type: 'raw',
        short_name: `${env_var_visible_short_nameAllF}`,
        description: 'test',
        title: 'VarVisible',
        value: `varAllFields`,
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'go_if_const', fields: '*' }],
      }),
    );
    expect(responseVisibleAllFields.status).toBe(200);
    expect(responseVisibleAllFields.body.ops[0].obj).toBe('env_var');
    newVarVisibleAllF = responseVisibleAllFields.body.ops[0].obj_id;

    const responseVisibleAllLogics = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,
        env_var_type: 'visible',
        data_type: 'json',
        short_name: `${env_var_visible_short_nameAllL}`,
        description: 'test',
        title: 'VarVisible',
        value: `{\"test\":\"varAllLogics\"}`,
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: '*', fields: '*' }],
      }),
    );
    expect(responseVisibleAllLogics.status).toBe(200);
    expect(responseVisibleAllLogics.body.ops[0].obj).toBe('env_var');

    const responseVisible = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,
        env_var_type: 'visible',
        data_type: 'raw',
        short_name: `${env_var_visible_short_name}`,
        description: 'test',
        title: 'VarVisible',
        value: `var`,
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'api_call', fields: '*' }],
      }),
    );
    expect(responseVisible.status).toBe(200);
    expect(responseVisible.body.ops[0].obj).toBe('env_var');
    newVarVisible = responseVisible.body.ops[0].obj_id;
  });

  const testCasesPositive = [
    {
      description: 'should reading env_var (all fields) in condition by id',
      constr: (): string => `{{env_var[${newVarVisibleAllF}]}}`,
      param: (): { a: string } => ({ a: 'varAllFields' }),
    },
    {
      description: 'should reading env_var (all logics) in condition by short_name',
      constr: (): string => `{{env_var[@${env_var_visible_short_nameAllL}].test}}`,
      param: (): { b: string } => ({ b: 'varAllLogics' }),
    },
    {
      description: 'should reading env_var {{}} in condition by short_name',
      constr: (): string => `{{env_var[@{{name}}].test}}`,
      param: (): { b: string; name: string } => ({ b: 'varAllLogics', name: env_var_visible_short_nameAllL }),
    },
  ];

  describe.each(testCasesPositive)('$description', ({ constr, param }) => {
    let previousCount = 0;
    beforeAll(async () => {
      const initialResponse = await requestList(api, newNode, newConv, company_id);
      expect(initialResponse.status).toBe(200);
      previousCount = initialResponse.body.ops[0]?.count || 0;
    });

    test(`env_var in condition`, async () => {
      const responseCreateNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id: newConv,
          title: 'Conditions',
          description: '',
          obj_type: 0,
          logics: [
            {
              type: 'go_if_const',
              to_node_id: `${newNode}`,
              conditions: [{ param: 'a', const: constr(), fun: 'eq', cast: 'string' }],
            },
            {
              type: 'go_if_const',
              to_node_id: `${newNode}`,
              conditions: [{ param: 'b', const: constr(), fun: 'eq', cast: 'string' }],
            },
            { node_title: 'final', to_node_id: `${final_node_ID}`, format: 'json', type: 'go' },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseCreateNode.status).toBe(200);
      expect(responseCreateNode.body.ops[0].proc).toEqual('ok');

      const responseCommit = await requestConfirm(api, newConv, company_id);
      expect(responseCommit.status).toBe(200);
      expect(responseCommit.body.ops[0].proc).toEqual('ok');

      await requestCreateTask(api, OBJ_TYPE.TASK, company_id, newConv, 'ref', param());
      await new Promise(r => setTimeout(r, 3000));

      const responseShow = await requestList(api, newNode, newConv, company_id);
      expect(responseShow.status).toBe(200);
      expect(responseShow.body.ops[0].list[0].data).toEqual(param());

      const currentCount = responseShow.body.ops[0].count;
      expect(currentCount).toBeGreaterThan(previousCount);
      previousCount = currentCount;
    });
  });

  const testCasesNegative = [
    {
      description: `shouldn't reading env_var another logic in condition`,
      constr: (): string => `{{env_var[${newVarVisible}]}}`,
      param: (): { a: string } => ({ a: 'var' }),
    },
  ];

  describe.each(testCasesNegative)('$description', ({ constr, param }) => {
    let previousCount = 0;
    beforeAll(async () => {
      const initialResponse = await requestList(api, final_node_ID, newConv, company_id);
      expect(initialResponse.status).toBe(200);
      previousCount = initialResponse.body.ops[0]?.count || 0;
    });

    test(`env_var in condition`, async () => {
      const responseCreateNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id: newConv,
          title: 'Conditions',
          description: '',
          obj_type: 0,
          logics: [
            {
              type: 'go_if_const',
              to_node_id: `${newNode}`,
              conditions: [{ param: 'a', const: constr(), fun: 'eq', cast: 'string' }],
            },
            { node_title: 'final', to_node_id: `${final_node_ID}`, format: 'json', type: 'go' },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseCreateNode.status).toBe(200);
      expect(responseCreateNode.body.ops[0].proc).toEqual('ok');

      const responseCommit = await requestConfirm(api, newConv, company_id);
      expect(responseCommit.status).toBe(200);
      expect(responseCommit.body.ops[0].proc).toEqual('ok');

      await requestCreateTask(api, OBJ_TYPE.TASK, company_id, newConv, 'ref', param());
      await new Promise(r => setTimeout(r, 3000));

      const responseShow = await requestList(api, final_node_ID, newConv, company_id);
      expect(responseShow.status).toBe(200);
      expect(responseShow.body.ops[0].list[0].data).toEqual(param());

      const currentCount = responseShow.body.ops[0].count;
      expect(currentCount).toBeGreaterThan(previousCount);
      previousCount = currentCount;
    });
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(response.status).toBe(200);
  });
});
