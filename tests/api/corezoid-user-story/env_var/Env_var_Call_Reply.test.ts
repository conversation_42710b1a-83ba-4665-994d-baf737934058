import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import {
  ENV_VAR_DATA_TYPE,
  CreateEnvVarResponse,
  ModifyEnvVarResponse,
} from '../../../../application/api/obj_types/env_var';
import { NODE_TYPE, NODE_LOGIC_TYPE, ModifyNodeResponse } from '../../../../application/api/obj_types/node';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
  generateName,
} from '../../../../utils/corezoidRequest';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObjNew,
  requestListConv,
} from '../../../../application/api/ApiObj';

describe('Env_var', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStageOne: string | number;
  let newStageTwo: string | number;
  let task_id_call: number;
  let checkCountCall: number;
  let checkCountReply: number;
  let newVar: string | number;
  let testVarValue: string;
  let newVarJson: string | number;
  let jsonVarValue: string;
  let newVarJsonOtherSrage: string | number;
  let company_id: any;
  let project_short_name: string;
  let stage_short_name: string;
  let env_json_os_short_name: string;
  let conv_id_call_process: number;
  let conv_id_reply: number;
  let envCallId: string;
  let envCallSN: string;
  let envReplyId: string;
  let envReplySN: string;
  let process_node_ID_call: string | number;
  let process_node_ID_reply: string | number;
  let final_node_ID_call: string | number;
  let final_node_ID_reply: string | number;
  let title: string;
  let conv_type: string;
  let env_var_type: string;
  let mode: string;
  let fullscope: any;
  let fullScopeApiRpc: any;
  let fullScopeReply: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = generateName(OBJ_TYPE.PROJECT);
    stage_short_name = generateName(OBJ_TYPE.STAGE);
    envReplySN = generateName(OBJ_TYPE.OBJS);
    envCallSN = generateName(OBJ_TYPE.OBJS);
    env_json_os_short_name = generateName(OBJ_TYPE.OBJS);
    title = generateName(OBJ_TYPE.OBJS);
    conv_type = 'process';
    env_var_type = 'visible';
    mode = 'key_value';
    fullscope = [{ type: '*', fields: '*' }];
    fullScopeApiRpc = [{ type: 'api_rpc', fields: ['project_id', 'stage_id', 'conv_id', 'extra'] }];
    fullScopeReply = [{ type: 'api_rpc_reply', fields: ['extra'] }];

    const responseProject = await requestCreateObjNew(
      api,
      OBJ_TYPE.PROJECT,
      company_id,
      generateName(OBJ_TYPE.PROJECT),
      {
        short_name: project_short_name,
        stages: [],
      },
    );
    const { obj_id: objIdProject } = responseProject.body.ops[0];
    newProject = objIdProject;

    const responseStageOne = await requestCreateObjNew(api, OBJ_TYPE.STAGE, company_id, generateName(OBJ_TYPE.STAGE), {
      short_name: stage_short_name,
      project_id: newProject,
    });
    const { obj_id: objIdStageOne } = responseStageOne.body.ops[0];
    newStageOne = objIdStageOne;

    const responseStageTwo = await requestCreateObjNew(api, OBJ_TYPE.STAGE, company_id, generateName(OBJ_TYPE.STAGE), {
      short_name: stage_short_name + '2',
      project_id: newProject,
    });
    const { obj_id: objIdStageTwo } = responseStageTwo.body.ops[0];
    newStageTwo = objIdStageTwo;

    const responseConvCallProcess = await requestCreateObjNew(
      api,
      OBJ_TYPE.CONV,
      company_id,
      generateName(OBJ_TYPE.CONV),
      {
        conv_type,
        stage_id: newStageOne,
        project_id: newProject,
      },
    );
    const { obj_id: objIdConvCallProcess } = responseConvCallProcess.body.ops[0];
    conv_id_call_process = objIdConvCallProcess;

    const responseConvReply = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, generateName(OBJ_TYPE.CONV), {
      conv_type,
      stage_id: newStageTwo,
      project_id: newProject,
    });
    const { obj_id: objIdConvReply } = responseConvReply.body.ops[0];
    conv_id_reply = objIdConvReply;

    const responseListCallProcess = await requestListConv(api, conv_id_call_process, company_id);
    process_node_ID_call = (responseListCallProcess.body.ops[0].list as Array<any>).find(
      item => item.title === conv_type,
    ).obj_id;
    final_node_ID_call = (responseListCallProcess.body.ops[0].list as Array<any>).find(item => item.title === 'final')
      .obj_id;

    const responseListReply = await requestListConv(api, conv_id_reply, company_id);
    process_node_ID_reply = (responseListReply.body.ops[0].list as Array<any>).find(item => item.title === conv_type)
      .obj_id;
    final_node_ID_reply = (responseListReply.body.ops[0].list as Array<any>).find(item => item.title === 'final')
      .obj_id;

    jsonVarValue = JSON.stringify({
      convId: conv_id_reply,
      stageId: newStageTwo,
      projectId: newProject,
    });

    const envVarJson = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,
        env_var_type,
        data_type: ENV_VAR_DATA_TYPE.JSON,
        short_name: envCallSN,
        title,
        value: jsonVarValue,
        project_id: newProject,
        stage_id: newStageOne,
        scopes: fullScopeApiRpc,
      }),
    );
    const { request_proc: procVarJson, ops: opsVarJson } = envVarJson.body as CreateEnvVarResponse;
    expect(procVarJson).toBe(PROC_STATUS.OK);
    expect(opsVarJson.length).toBeGreaterThan(0);
    const { obj_id: objIdEnvVar, obj: objenvVar } = opsVarJson[0];
    expect(envVarJson.status).toBe(RESP_STATUS.OK);
    expect(objenvVar).toBe(OBJ_TYPE.ENV_VAR);
    newVarJson = objIdEnvVar;

    const envJsonOs = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,
        env_var_type,
        data_type: ENV_VAR_DATA_TYPE.JSON,
        short_name: env_json_os_short_name,
        title,
        value: jsonVarValue,
        project_id: newProject,
        stage_id: newStageTwo,
        scopes: fullScopeApiRpc,
      }),
    );
    const { request_proc: procJsonOs, ops: opsJsonOs } = envJsonOs.body as CreateEnvVarResponse;
    expect(procJsonOs).toBe(PROC_STATUS.OK);
    const { obj: objEnvJsonos, obj_id: objIdEnvJsonos } = opsJsonOs[0];
    expect(objEnvJsonos).toBe(OBJ_TYPE.ENV_VAR);
    newVarJsonOtherSrage = objIdEnvJsonos;

    testVarValue = 'testVarValue';
    const varRaw = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,
        env_var_type,
        data_type: ENV_VAR_DATA_TYPE.RAW,
        short_name: envReplySN,
        title,
        value: testVarValue,
        project_id: newProject,
        stage_id: newStageTwo,
        scopes: fullScopeReply,
      }),
    );
    const { request_proc: procVarRaw, ops: opsVarRaw } = varRaw.body as CreateEnvVarResponse;
    expect(procVarRaw).toBe(PROC_STATUS.OK);
    expect(varRaw.status).toBe(RESP_STATUS.OK);
    const { obj: objEnvRaw, obj_id: objIEnvRaw } = opsVarRaw[0];
    expect(objEnvRaw).toBe(OBJ_TYPE.ENV_VAR);
    newVar = objIEnvRaw;

    checkCountCall = 0;
    checkCountReply = 0;
    envCallId = `${newVarJson}`;
    envReplyId = `${newVar}`;
  });

  const testCasesPositive = [
    {
      description: 'should reading env_var_id Call fields -> project_id/stage_id/conv_id, Reply type/fields -> *',
      envCall: (): string => envCallId,
      envReply: (): string => envReplyId,
      scopeCall: fullScopeApiRpc,
      scopeReply: fullscope,
    },
    {
      description: 'should reading env_var_short_name Call type/fields -> *, Reply fields -> extra',
      envCall: (): string => `@${envCallSN}`,
      envReply: (): string => `@${envReplySN}`,
      scopeCall: fullscope,
      scopeReply: fullScopeReply,
    },
    {
      description: 'should reading env_var_id Call fields -> project_id/stage_id/conv_id, Reply fields -> extra',
      envCall: (): string => envCallId,
      envReply: (): string => envReplyId,
      scopeCall: fullScopeApiRpc,
      scopeReply: fullScopeReply,
    },
    {
      description: 'should reading env_var_short_name Call type/fields -> *, Reply type/fields -> *',
      envCall: (): string => `@${envCallSN}`,
      envReply: (): string => `@${envReplySN}`,
      scopeCall: fullscope,
      scopeReply: fullscope,
    },
    {
      description: 'should reading env_var_id/short_name Call fields -> *, Reply fields -> *',
      envCall: (): string => envCallId,
      envReply: (): string => `@${envReplySN}`,
      scopeCall: [{ type: 'api_rpc', fields: '*' }],
      scopeReply: [{ type: 'api_rpc_reply', fields: '*' }],
    },
  ];
  describe.each(testCasesPositive)('$description', ({ envCall, envReply, scopeCall, scopeReply }) => {
    test(`env_var in Call and Reply(Positive)`, async () => {
      const modifyCallVar = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.ENV_VAR,
          company_id,
          obj_id: newVarJson,
          env_var_type,
          data_type: ENV_VAR_DATA_TYPE.JSON,
          short_name: envCallSN,
          title,
          value: jsonVarValue,
          project_id: newProject,
          stage_id: newStageOne,
          scopes: scopeCall,
        }),
      );
      expect(modifyCallVar.status).toBe(RESP_STATUS.OK);
      const { request_proc: procModifyCall, ops: opsModifyCall } = modifyCallVar.body as ModifyEnvVarResponse;
      expect(procModifyCall).toBe(PROC_STATUS.OK);
      const { obj: objEnvCall } = opsModifyCall[0];
      expect(objEnvCall).toBe(OBJ_TYPE.ENV_VAR);

      const modifyReplyVar = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.ENV_VAR,
          company_id,
          obj_id: newVar,
          env_var_type,
          data_type: ENV_VAR_DATA_TYPE.RAW,
          short_name: envReplySN,
          title,
          value: testVarValue,
          project_id: newProject,
          stage_id: newStageTwo,
          scopes: scopeReply,
        }),
      );
      expect(modifyReplyVar.status).toBe(RESP_STATUS.OK);
      const { request_proc: procModifyReplyVar, ops: opsModifyReplyVar } = modifyReplyVar.body as ModifyEnvVarResponse;
      expect(procModifyReplyVar).toBe(PROC_STATUS.OK);
      const { obj: objEnvReply } = opsModifyReplyVar[0];
      expect(objEnvReply).toBe(OBJ_TYPE.ENV_VAR);

      const modifyNodeReply = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID_reply,
          conv_id: conv_id_reply,
          title,
          obj_type: NODE_TYPE.Normal,
          logics: [
            {
              type: NODE_LOGIC_TYPE.ApiRpcReply,
              mode,
              res_data: {
                testKey: `{{env_var[${envReply()}]}}`,
              },
              res_data_type: {
                testKey: 'string',
              },
              throw_exception: false,
            },
            { node_title: 'final', to_node_id: final_node_ID_reply, format: 'json', type: 'go' },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(modifyNodeReply.status).toBe(RESP_STATUS.OK);
      const { request_proc: procModifyReplyNode } = modifyNodeReply.body as ModifyNodeResponse;
      expect(procModifyReplyNode).toBe(PROC_STATUS.OK);

      const CommitReply = await requestConfirm(api, conv_id_reply, company_id);
      expect(CommitReply.status).toBe(RESP_STATUS.OK);
      const { proc: procCommitReply } = CommitReply.body.ops[0];
      expect(procCommitReply).toEqual(PROC_STATUS.OK);

      const modifyNodeCall = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID_call,
          conv_id: conv_id_call_process,
          title,
          obj_type: NODE_TYPE.Normal,
          logics: [
            {
              type: 'api_rpc',
              conv_id: `{{env_var[${envCall()}].convId}}`,
              stage_id: `{{env_var[${envCall()}].stageId}}`,
              project_id: `{{env_var[${envCall()}].projectId}}`,
              extra: { dataExtra: `{{env_var[${envCall()}].convId}}` },
              extra_type: { dataExtra: 'number' },
              group: 'all',
            },
            { node_title: 'final', to_node_id: final_node_ID_call, format: 'json', type: 'go' },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(modifyNodeCall.status).toBe(RESP_STATUS.OK);
      const { request_proc: procModifyCallNode } = modifyNodeCall.body as ModifyNodeResponse;
      expect(procModifyCallNode).toBe(PROC_STATUS.OK);

      const commitCall = await requestConfirm(api, conv_id_call_process, company_id);
      const { proc: procCommitCall } = commitCall.body.ops[0];
      expect(commitCall.status).toBe(RESP_STATUS.OK);
      expect(procCommitCall).toEqual(PROC_STATUS.OK);

      const responceTask = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          company_id,
          conv_id: conv_id_call_process,
          data: { data: 201 },
          ref: `ref_${Date.now()}`,
        }),
      );
      const { proc: procTask, obj: objTask, obj_id: objIdTask } = responceTask.body.ops[0];
      expect(responceTask.status).toBe(RESP_STATUS.OK);
      expect(procTask).toEqual(PROC_STATUS.OK);
      expect(objTask).toEqual(OBJ_TYPE.TASK);
      task_id_call = objIdTask;

      await new Promise(r => setTimeout(r, 4000));

      const responseShow = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.TASK,
          conv_id: conv_id_call_process,
          ref_or_obj_id: task_id_call,
        }),
      );

      const { proc: procShow, data: dataShow } = responseShow.body.ops[0];
      const { data: nData, testKey } = dataShow;
      expect(responseShow.status).toBe(RESP_STATUS.OK);
      expect(procShow).toEqual(PROC_STATUS.OK);
      expect(nData).toEqual(RESP_STATUS.CREATED);
      expect(testKey).toEqual(testVarValue);

      const responceShowCall = await requestList(api, final_node_ID_call, conv_id_call_process, company_id);
      const { proc: procShowCall, count: countShowCall } = responceShowCall.body.ops[0];
      expect(responceShowCall.status).toBe(RESP_STATUS.OK);
      expect(procShowCall).toEqual(PROC_STATUS.OK);
      expect(countShowCall).toBeGreaterThan(checkCountCall);
      checkCountCall = countShowCall;

      const responseShowReply = await requestList(api, final_node_ID_reply, conv_id_reply, company_id);
      const { proc: procShowReply, count: countShowReply, list } = responseShowReply.body.ops[0];
      const { __rpc_conv_id__, dataExtra } = list[0].data;
      expect(responseShowReply.status).toBe(RESP_STATUS.OK);
      expect(procShowReply).toEqual(PROC_STATUS.OK);
      expect(__rpc_conv_id__).toEqual(conv_id_call_process);
      expect(dataExtra).toEqual(conv_id_reply);
      expect(countShowReply).toBeGreaterThan(checkCountReply);
      checkCountReply = countShowReply;
    });
  });

  const testCasesNegative = [
    {
      description: 'shouldn`t reading env_var_id in node Call process from another staging',
      env: (): string => `${newVarJsonOtherSrage}`,
      checkProcces: '',
    },
    {
      description: 'shouldn`t reading env_var_short_name Reply process without scope',
      env: (): string => `@${envCallSN}`,
      checkProcces: '_reply',
    },
  ];

  describe.each(testCasesNegative)('$description', ({ env, checkProcces }) => {
    test(`env_var in Call and Reply(Negative)`, async () => {
      const { status: StatusCreateNodeReply, body: bodyCreateNodeReply } = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID_reply,
          conv_id: conv_id_reply,
          title,
          obj_type: 0,
          logics: [
            {
              type: 'api_rpc_reply',
              mode,
              res_data: {
                testKey: `{{env_var[${newVarJsonOtherSrage}].convId}}`,
              },
              res_data_type: {
                testKey: 'string',
              },
              throw_exception: false,
            },
            { node_title: 'final', to_node_id: final_node_ID_reply, format: 'json', type: 'go' },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(StatusCreateNodeReply).toBe(RESP_STATUS.OK);
      expect(bodyCreateNodeReply.ops[0].proc).toEqual(PROC_STATUS.OK);

      const responseCommitReply = await requestConfirm(api, conv_id_reply, company_id);
      expect(responseCommitReply.status).toBe(RESP_STATUS.OK);
      const { proc: procCommitReply } = responseCommitReply.body.ops[0];
      expect(procCommitReply).toEqual(PROC_STATUS.OK);

      const responseCreateNodeCall = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID_call,
          conv_id: conv_id_call_process,
          title,
          obj_type: 0,
          logics: [
            {
              type: 'api_rpc',
              conv_id: `{{env_var[${env()}].convId}}`,
              stage_id: `{{env_var[${env()}].stageId}}`,
              project_id: `{{env_var[${env()}].projectId}}`,
              extra: { dataExtra: `{{env_var[${env()}].convId}}` },
              extra_type: { dataExtra: 'number' },
              group: 'all',
            },
            { node_title: 'final', to_node_id: final_node_ID_call, format: 'json', type: 'go' },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseCreateNodeCall.status).toBe(RESP_STATUS.OK);
      const { proc: procCreateNodeCall } = responseCreateNodeCall.body.ops[0];
      expect(procCreateNodeCall).toEqual(PROC_STATUS.OK);

      const responseCommit = await requestConfirm(api, conv_id_call_process, company_id);
      expect(responseCommit.status).toBe(RESP_STATUS.OK);
      const { proc: procCommit } = responseCommit.body.ops[0];
      expect(procCommit).toEqual(PROC_STATUS.OK);

      const responceTask = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          company_id,
          conv_id: conv_id_call_process,
          data: { data: 201 },
          ref: `ref_${Date.now()}`,
        }),
      );
      const { proc: procTask, obj: objTask, obj_id: objIdTask } = responceTask.body.ops[0];
      expect(responceTask.status).toBe(RESP_STATUS.OK);
      expect(procTask).toEqual(PROC_STATUS.OK);
      expect(objTask).toEqual(OBJ_TYPE.TASK);
      task_id_call = objIdTask;

      await new Promise(r => setTimeout(r, 4000));

      const responseShowTask = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.TASK,
          conv_id: conv_id_call_process,
          ref_or_obj_id: task_id_call,
        }),
      );
      const { proc: procShowTask, data: dataShowTask } = responseShowTask.body.ops[0];
      expect(responseShowTask.status).toBe(RESP_STATUS.OK);
      expect(procShowTask).toEqual(PROC_STATUS.OK);
      expect(dataShowTask).toHaveProperty(`__conveyor_rpc${checkProcces}_return_type_error__`);
      expect(dataShowTask[`__conveyor_rpc${checkProcces}_return_description__`]).toEqual(
        `EnvVariable ${newVarJsonOtherSrage} doesn't found in current stage. Maybe it was deleted`,
      );
    });
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
