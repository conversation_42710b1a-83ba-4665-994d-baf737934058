import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestListConv,
  requestCreateObjNew,
  requestCreateTask,
  requestShow,
} from '../../../../application/api/ApiObj';

describe('Env_var in db_call (positive)', (): void => {
  let api: ApiKeyClient;
  let apikey: ApiKey;

  let newProject: string | number;
  let newStage: string | number;
  let project_short_name: string;
  let stage_short_name: string;
  let company_id: any;

  let newVarVisibleAllL: string | number;
  let env_var_visible_short_nameAllL: string;
  let env_var_visible_short_nameAllF: string;

  let newVarVisible: string | number;
  let env_var_visible_short_name: string;

  let newConv: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let newNode: string;
  let task_id: string;

  let newInstance: string | number;
  let db_host: string;
  let db_nameMyS: string;
  let db_userMyS: string;
  let db_passMyS: string;

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;
      project_short_name = `project-${Date.now()}`;
      stage_short_name = `stage-${Date.now()}`;
      env_var_visible_short_name = `varvisible-${Date.now()}`;
      env_var_visible_short_nameAllL = `varvisiblealll-${Date.now()}`;
      env_var_visible_short_nameAllF = `varvisibleallf-${Date.now()}`;
      db_host = '**********';
      db_nameMyS = 'COR6783';
      db_userMyS = 'root';
      db_passMyS = 'she2kojiJo';

      const responseProject = await requestCreateObjNew(api, OBJ_TYPE.PROJECT, company_id, `Project_${Date.now()}`, {
        short_name: project_short_name,
        description: 'test',
        stages: [],
      });
      newProject = responseProject.body.ops[0].obj_id;

      const responseStage = await requestCreateObjNew(api, OBJ_TYPE.STAGE, company_id, `Stage_${Date.now()}`, {
        short_name: stage_short_name,
        description: 'test',
        project_id: newProject,
      });
      newStage = responseStage.body.ops[0].obj_id;

      const responseConv = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, `Conv_${Date.now()}`, {
        conv_type: 'process',
        stage_id: newStage,
        project_id: newProject,
      });
      newConv = responseConv.body.ops[0].obj_id;

      const responseNode = await requestCreateObjNew(api, OBJ_TYPE.NODE, company_id, `success`, {
        conv_id: newConv,
        obj_type: 2,
        version: 22,
      });
      newNode = responseNode.body.ops[0].obj_id;

      const responseList = await requestListConv(api, newConv, company_id);
      expect(responseList.status).toBe(200);
      process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
      final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

      const responseInstance = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.INSTANCE,
          company_id,
          instance_type: `db_call`,
          folder_id: newStage,
          title: `Instance_${Date.now()}`,
          data: {
            driver: `mysql`,
            host: db_host,
            port: `3306`,
            username: db_userMyS,
            password: db_passMyS,
            ssl: false,
            database: db_nameMyS,
            timeoutMs: 30000,
          },
        }),
      );
      expect(responseInstance.status).toBe(200);
      expect(responseInstance.body.ops[0].obj).toBe('instance');
      newInstance = responseInstance.body.ops[0].obj_id;

      const responseVisibleAllFields = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ENV_VAR,
          company_id,
          env_var_type: 'visible',
          data_type: 'raw',
          short_name: `${env_var_visible_short_nameAllF}`,
          description: 'test',
          title: 'VarVisible',
          value: `${newInstance}`,
          project_id: newProject,
          stage_id: newStage,
          scopes: [{ type: 'db_call', fields: '*' }],
        }),
      );
      expect(responseVisibleAllFields.status).toBe(200);
      expect(responseVisibleAllFields.body.ops[0].obj).toBe('env_var');

      const responseVisibleAllLogics = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ENV_VAR,
          company_id,
          env_var_type: 'visible',
          data_type: 'raw',
          short_name: `${env_var_visible_short_nameAllL}`,
          description: 'test',
          title: 'VarVisible',
          value: `${newInstance}`,
          project_id: newProject,
          stage_id: newStage,
          scopes: [{ type: '*', fields: '*' }],
        }),
      );
      expect(responseVisibleAllLogics.status).toBe(200);
      expect(responseVisibleAllLogics.body.ops[0].obj).toBe('env_var');
      newVarVisibleAllL = responseVisibleAllLogics.body.ops[0].obj_id;

      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ENV_VAR,
          company_id,
          env_var_type: 'visible',
          data_type: 'json',
          short_name: `${env_var_visible_short_name}`,
          description: 'test',
          title: 'VarVisible',
          value: `{\"instance_id\":\"${newInstance}\"}`,
          project_id: newProject,
          stage_id: newStage,
          scopes: [{ type: 'db_call', fields: ['instance_id'] }],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].obj).toBe('env_var');
      newVarVisible = response.body.ops[0].obj_id;
    },
  );

  const testCasesPositive = [
    {
      description: 'should reading env_var (all logics) in db_call by id',
      instance_id: (): string => `{{env_var[${newVarVisibleAllL}]}}`,
    },
    {
      description: 'should reading env_var (all fields) in db_call by short_name',
      instance_id: (): string => `{{env_var[@${env_var_visible_short_nameAllF}]}}`,
    },
    {
      description: 'should reading env_var in db_call by short_name',
      instance_id: (): string => `{{env_var[@${env_var_visible_short_name}].instance_id}}`,
    },
    {
      description: 'should reading env_var {{}} in db_call by short_name',
      instance_id: (): string => `{{env_var[@{{name}}].instance_id}}`,
    },
  ];

  describe.each(testCasesPositive)('$description', ({ instance_id }): void => {
    let previousCount = 0;
    beforeAll(
      async (): Promise<void> => {
        const initialResponse = await requestList(api, final_node_ID, newConv, company_id);
        expect(initialResponse.status).toBe(200);
        previousCount = initialResponse.body.ops[0]?.count || 0;
      },
    );

    test(`env_var in db_call`, async (): Promise<void> => {
      const responseCreateNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id: newConv,
          title: 'Conditions',
          description: '',
          obj_type: 0,
          logics: [
            {
              type: 'db_call',
              extra: {},
              extra_type: {},
              instance_id: instance_id(),
              query: 'SELECT * from months;',
              err_node_id: `${newNode}`,
            },
            { type: 'go', to_node_id: final_node_ID },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseCreateNode.status).toBe(200);
      expect(responseCreateNode.body.ops[0].proc).toEqual('ok');

      const responseCommit = await requestConfirm(api, newConv, company_id);
      expect(responseCommit.status).toBe(200);
      expect(responseCommit.body.ops[0].proc).toEqual('ok');

      const responseTask = await requestCreateTask(api, OBJ_TYPE.TASK, company_id, newConv, 'ref', {
        b: `123`,
        name: `${env_var_visible_short_name}`,
      });
      task_id = responseTask.body.ops[0].obj_id;
      await new Promise(r => setTimeout(r, 3000));

      const responsShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: newConv });
      expect(responsShowTask.body.ops[0].data.b).toEqual(`123`);
      expect(responsShowTask.body.ops[0].data.__db_call_res__[0].name).toEqual(`January`);
      await new Promise(r => setTimeout(r, 1000));

      const responseList = await requestList(api, final_node_ID, newConv, company_id);
      expect(responseList.status).toBe(200);
      const currentCount = responseList.body.ops[0].count;
      expect(currentCount).toBeGreaterThan(previousCount);
      previousCount = currentCount;
    });
  });
  const testCasesNegative = [
    {
      description: `shouldn't reading env_var another logic in db_call`,
      instance_id: (): string => `{{env_var[${newVarVisible}]}}`,
      scope: (): Array<{ type: string; fields: string }> => [{ type: 'go_if_const', fields: '*' }],
    },
    {
      description: `shouldn't reading env_var another fields in db_call`,
      instance_id: (): string => `{{env_var[${newVarVisible}]}}`,
      scope: (): Array<{ type: string; fields: string[] }> => [{ type: 'api_call', fields: ['url'] }],
    },
  ];

  describe.each(testCasesNegative)('$description', ({ scope, instance_id }): void => {
    let previousCount = 0;
    beforeAll(
      async (): Promise<void> => {
        const initialResponse = await requestList(api, newNode, newConv, company_id);
        expect(initialResponse.status).toBe(200);
        previousCount = initialResponse.body.ops[0]?.count || 0;
      },
    );

    test(`env_var in db_call`, async (): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.ENV_VAR,
          company_id,
          obj_id: newVarVisible,
          env_var_type: 'visible',
          data_type: 'raw',
          short_name: `${env_var_visible_short_name}`,
          description: 'test',
          title: 'VarVisible',
          value: `{\"instance_id\":\"${newInstance}\"}`,
          project_id: newProject,
          stage_id: newStage,
          scopes: scope(),
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].obj).toBe('env_var');

      const responseCreateNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id: newConv,
          title: 'Conditions',
          description: '',
          obj_type: 0,
          logics: [
            {
              type: 'db_call',
              extra: {},
              extra_type: {},
              instance_id: instance_id(),
              query: 'SELECT * from months;',
              err_node_id: `${newNode}`,
            },
            { type: 'go', to_node_id: final_node_ID },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseCreateNode.status).toBe(200);
      expect(responseCreateNode.body.ops[0].proc).toEqual('ok');

      const responseCommit = await requestConfirm(api, newConv, company_id);
      expect(responseCommit.status).toBe(200);
      expect(responseCommit.body.ops[0].proc).toEqual('ok');

      const responseTask = await requestCreateTask(api, OBJ_TYPE.TASK, company_id, newConv, 'ref', {
        b: `123`,
        name: `${env_var_visible_short_name}`,
      });
      task_id = responseTask.body.ops[0].obj_id;

      await new Promise(r => setTimeout(r, 3000));

      const responsShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: newConv });
      expect(responsShowTask.status).toBe(200);
      expect(responsShowTask.body.ops[0].data.__conveyor_db_call_return_description__).toEqual(
        `EnvVariable ${newVarVisible} doesn't found in current stage. Maybe it was deleted`,
      );

      const responseList = await requestList(api, newNode, newConv, company_id);
      expect(responseList.status).toBe(200);
      const currentCount = responseList.body.ops[0].count;
      expect(currentCount).toBeGreaterThan(previousCount);
      previousCount = currentCount;
    });
  });

  afterAll(
    async (): Promise<void> => {
      const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
      expect(response.status).toBe(200);
    },
  );
});
