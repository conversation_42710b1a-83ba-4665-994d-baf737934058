import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';

describe('Env certificate', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: string | null | undefined;
  let conv_id: number;
  let var_id: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let project_short_name: string;
  let stage_short_name: string;
  let project_id: number;
  let stage_id: number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = `project-${Date.now()}`;
    stage_short_name = `stage-${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    project_id = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_id: project_id,
      }),
    );
    stage_id = responseStage.body.ops[0].obj_id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,
        env_var_type: 'secret',
        short_name: 'cert',
        description: 'test',
        title: 'cert',
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        project_id,
        stage_id,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    var_id = response.body.ops[0].obj_id;

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: 'ApiCall',
        status: 'actived',
        project_id,
        stage_id,
        folder_id: stage_id,
      }),
    );
    conv_id = responseConv.body.ops[0].obj_id;

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
      }),
    );
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'LogicCallNew',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'GET',
            raw_body: ``,
            url: `https://key-manager.enigma.dev.middleware.loc/v1/master-keys`,
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: '',
            extra_headers: { [`content-type`]: `application/json`, Accept: `application/json` },
            send_sys: true,
            cert_pem: '',
            content_type: 'application/json',
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);
    expect(responseCreateNode.body.ops[0].proc).toEqual('ok');

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        conv_id: conv_id,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');
  });

  test('should create task without certificate', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id,
        data: { one: `env_var[${var_id}]`, data: 201 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 6000));

    const responseShowApicall = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID,
        company_id,
        conv_id: conv_id,
        limit: 1,
      }),
    );
    expect(responseShowApicall.status).toBe(200);
    expect(responseShowApicall.body.ops[0].proc).toEqual('ok');
    expect(responseShowApicall.body.ops[0].list[0].data.__conveyor_api_return_http_code__).toEqual(403);
    expect(responseShowApicall.body.ops[0].list[0].data.data).toEqual(201);
    expect(responseShowApicall.body.ops[0].list[0].data.one).toEqual(`env_var[${var_id}]`);
  });

  test('should create task with certificate {{env_var[@shortname]}}', async () => {
    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'LogicCallNew',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'GET',
            url: `https://key-manager.enigma.dev.middleware.loc/v1/master-keys`,
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: '',
            extra_headers: { [`content-type`]: `application/json`, Accept: `application/json` },
            send_sys: true,
            cert_pem: `{{env_var[@cert]}}`,
            content_type: 'application/json',
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);
    expect(responseCreateNode.body.ops[0].proc).toEqual('ok');

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        conv_id: conv_id,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id,
        data: { data: 202 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 10000));

    const responseShowConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID,
        company_id,
        conv_id: conv_id,
        limit: 1,
      }),
    );
    expect(responseShowConv.status).toBe(200);
    expect(responseShowConv.body.ops[0].proc).toEqual('ok');
    expect(responseShowConv.body.ops[0].list[0].data.data).toEqual(202);
    expect(responseShowConv.body.ops[0].list[0].data.payload[0].state).toBeNumber;
  });

  test('should create task with certificate {{env_var[@id]}}', async () => {
    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'LogicCallNew',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'GET',
            url: `https://key-manager.enigma.dev.middleware.loc/v1/master-keys`,
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: '',
            extra_headers: { [`content-type`]: `application/json`, Accept: `application/json` },
            send_sys: true,
            cert_pem: `{{env_var[${var_id}]}}`,
            content_type: 'application/json',
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);
    expect(responseCreateNode.body.ops[0].proc).toEqual('ok');

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        conv_id: conv_id,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id,
        data: { data: 203 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 4000));

    const responseShowConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID,
        company_id,
        conv_id: conv_id,
        limit: 1,
      }),
    );
    expect(responseShowConv.status).toBe(200);
    expect(responseShowConv.body.ops[0].proc).toEqual('ok');
    expect(responseShowConv.body.ops[0].list[0].data.data).toEqual(203);
    expect(responseShowConv.body.ops[0].list[0].data.payload[0].state).toBeNumber;
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: project_id,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
