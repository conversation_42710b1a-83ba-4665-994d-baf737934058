import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';

describe('Env secret key', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: string | null | undefined;
  let conv_id: number;
  let var_id: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let conv_id_2: number;
  let final_node_ID_2: string | number;
  let project_short_name: string;
  let stage_short_name: string;
  let project_id: number;
  let stage_id: number;
  let newApiKeyId: string | number;
  let newApiKeyLogin: number;
  let newApiKeySecret: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = `project-${Date.now()}`;
    stage_short_name = `stage-${Date.now()}`;

    const CreateKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(200);
    const user_api = CreateKeyResponse.body;
    const newApiKey = {
      key: `${user_api.ops[0].users[0].logins[0].obj_id}`,
      secret: user_api.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user_api.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newApiKeyId = +newApiKey.id;
    newApiKeyLogin = +newApiKey.key;
    newApiKeySecret = newApiKey.secret;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    project_id = responseProject.body.ops[0].obj_id;

    const responseStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        title: `Stage_${Date.now()}`,
        short_name: stage_short_name,
        description: 'test',
        project_id: project_id,
      }),
    );
    stage_id = responseStage.body.ops[0].obj_id;

    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.PROJECT,
        obj_id: project_id,
        company_id,
        obj_to: 'user',
        obj_to_id: newApiKeyId,
        is_need_to_notify: false,
        privs: [
          { type: 'create', list_obj: ['all'] },
          { type: 'modify', list_obj: ['all'] },
          { type: 'delete', list_obj: ['all'] },
          { type: 'view', list_obj: ['all'] },
        ],
      }),
    );
    expect(responseLink.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,
        env_var_type: 'secret',
        short_name: 'secret',
        description: 'test',
        title: 'secret',
        value: newApiKeySecret,
        project_id,
        stage_id,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    var_id = response.body.ops[0].obj_id;

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: 'ApiCall',
        status: 'actived',
        project_id,
        stage_id,
        folder_id: stage_id,
      }),
    );
    conv_id = responseConv.body.ops[0].obj_id;

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
      }),
    );
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseConv2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: 'NewConv',
        status: 'actived',
        project_id,
        stage_id,
        folder_id: stage_id,
      }),
    );
    conv_id_2 = responseConv2.body.ops[0].obj_id;

    const responseList2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id_2,
        company_id,
      }),
    );
    expect(responseList2.status).toBe(200);
    final_node_ID_2 = (responseList2.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'LogicCallNew',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: 'raw',
            method: 'POST',
            raw_body: `{\"ops\":[{\"type\": \"create\",\"conv_id\": ${conv_id_2},\"obj\": \"task\",\"action\": \"user\",\"data\": {\"code\": {{data}}},\"ref\": \"test\"}]}`,
            url: `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/${newApiKeyLogin}`,
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: '',
            extra_headers: {},
            send_sys: true,
            cert_pem: '',
            content_type: 'application/json',
            api_secret_outer: `{{env_var[${var_id}]}}`,
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);
    expect(responseCreateNode.body.ops[0].proc).toEqual('ok');

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        conv_id: conv_id,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');
  });

  test('should create task env_var[id]', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id,
        data: { one: `env_var[${var_id}]`, data: 201 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 4000));

    const responseShowApicall = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID,
        company_id,
        conv_id: conv_id,
        limit: 1,
      }),
    );
    expect(responseShowApicall.status).toBe(200);
    expect(responseShowApicall.body.ops[0].proc).toEqual('ok');
    expect(responseShowApicall.body.ops[0].list[0].data.ops[0].proc).toEqual('ok');
    expect(responseShowApicall.body.ops[0].list[0].data.data).toEqual(201);
    expect(responseShowApicall.body.ops[0].list[0].data.one).toEqual(`env_var[${var_id}]`);

    const responseShowConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID_2,
        company_id,
        conv_id: conv_id_2,
        limit: 1,
      }),
    );
    expect(responseShowConv.status).toBe(200);
    expect(responseShowConv.body.ops[0].proc).toEqual('ok');
    expect(responseShowConv.body.ops[0].list[0].data.code).toEqual(201);
  });

  test('should create task env_var[@shortname]', async () => {
    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'LogicCallNew',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: 'raw',
            method: 'POST',
            raw_body: `{\"ops\":[{\"type\": \"create\",\"conv_id\": ${conv_id_2},\"obj\": \"task\",\"action\": \"user\",\"data\": {\"code\": {{data}}},\"ref\": \"test\"}]}`,
            url: `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/${newApiKeyLogin}`,
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: '',
            extra_headers: {},
            send_sys: true,
            cert_pem: '',
            content_type: 'application/json',
            api_secret_outer: `{{env_var[@secret]}}`,
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);
    expect(responseCreateNode.body.ops[0].proc).toEqual('ok');

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        conv_id: conv_id,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id,
        data: { one: `env_var[${var_id}]`, data: 202 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 4000));

    const responseShowConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID_2,
        company_id,
        conv_id: conv_id_2,
        limit: 1,
      }),
    );
    expect(responseShowConv.status).toBe(200);
    expect(responseShowConv.body.ops[0].proc).toEqual('ok');
    expect(responseShowConv.body.ops[0].list[0].data.code).toEqual(202);
  });

  test(`should modify ENV_VAR for construction`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: var_id,
        company_id,
        env_var_type: 'secret',
        title: `secret`,
        description: `test`,
        short_name: `secret`,
        data_type: `json`,
        value: `{
            \"login\": {
                \"param\": 324324,
                \"param2\": 23123123
            },
            \"key2\": 12321312,
            \"secret\": [
                {
                    \"data\": \"23b42hj\",
                    \"data2\": \"23423423423\",
                    \"data3\": [
                        12312312,
                        \"${newApiKeySecret}\"
                    ]
                }
            ]
        }`,
        project_id,
        stage_id,
        scopes: [{ type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
  });

  test('should create task env_var[id].key', async () => {
    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'LogicCallNew',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: 'raw',
            method: 'POST',
            raw_body: `{\"ops\":[{\"type\": \"create\",\"conv_id\": ${conv_id_2},\"obj\": \"task\",\"action\": \"user\",\"data\": {\"code\": {{data}}},\"ref\": \"test\"}]}`,
            url: `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/${newApiKeyLogin}`,
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: '',
            extra_headers: {},
            send_sys: true,
            cert_pem: '',
            content_type: 'application/json',
            api_secret_outer: `{{env_var[${var_id}].secret[0].data3[1]}}`,
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);
    expect(responseCreateNode.body.ops[0].proc).toEqual('ok');

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        conv_id: conv_id,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id,
        data: { one: `env_var[${var_id}]`, data: 203 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 4000));

    const responseShowConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID_2,
        company_id,
        conv_id: conv_id_2,
        limit: 1,
      }),
    );
    expect(responseShowConv.status).toBe(200);
    expect(responseShowConv.body.ops[0].proc).toEqual('ok');
    expect(responseShowConv.body.ops[0].list[0].data.code).toEqual(203);
  });

  test('should create task env_var[@shortname].key', async () => {
    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'LogicCallNew',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: 'raw',
            method: 'POST',
            raw_body: `{\"ops\":[{\"type\": \"create\",\"conv_id\": ${conv_id_2},\"obj\": \"task\",\"action\": \"user\",\"data\": {\"code\": {{data}}},\"ref\": \"test\"}]}`,
            url: `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/${newApiKeyLogin}`,
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: '',
            extra_headers: {},
            send_sys: true,
            cert_pem: '',
            content_type: 'application/json',
            api_secret_outer: `{{env_var[@secret].secret[0].data3[1]}}`,
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);
    expect(responseCreateNode.body.ops[0].proc).toEqual('ok');

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        conv_id: conv_id,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id,
        data: { one: `env_var[${var_id}]`, data: 204 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 4000));

    const responseShowConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID_2,
        company_id,
        conv_id: conv_id_2,
        limit: 1,
      }),
    );
    expect(responseShowConv.status).toBe(200);
    expect(responseShowConv.body.ops[0].proc).toEqual('ok');
    expect(responseShowConv.body.ops[0].list[0].data.code).toEqual(204);
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: project_id,
        company_id,
      }),
    );
    expect(response.status).toBe(200);

    const ResponseDeleteApiKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: newApiKeyId,
        company_id,
      }),
    );
    expect(ResponseDeleteApiKey.status).toBe(200);
  });
});
