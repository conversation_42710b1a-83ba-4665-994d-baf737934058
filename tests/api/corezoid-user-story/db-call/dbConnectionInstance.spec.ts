import { ApiUserClient } from '../../../../application/api/ApiUserClient';
import { ConnectionConfig } from '../../../../application/api/db-call/DBCallApi';
import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { User } from '../../../../infrastructure/model/User';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';
import * as data from './dbCallTestData.json';

let api: ApiUserClient;
let user: User;
let obj_id: string | number;
let idArray: any;
let nameDB: string;
let conv_id: string;
let state_id: string;
let task_id: string;
let process_node_ID: string;
let final_node_ID: string;
let newapi: ApiKeyClient;
let instanceNew: string | number;
let company_id: string;
let user0: User;
let apiUserCookie: ApiUserClient;

beforeAll(async () => {
  user0 = await application.getAuthorizedUser();
  apiUserCookie = await application.getApiUserClient(user0);
  company_id = user0.companies[0].id;
  user = await application.getAuthorizedUser({ company: {} }, 2);
  company_id = user.companies[0].id;
  api = await application.getApiUserClient(user);
  idArray = [];

  const data = new URLSearchParams();
  data.append('login', `${user.email}`);
  data.append('password', `${user.password}`);

  const response = await apiUserCookie.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.USER,
      company_id,
      title: 'NewUser',
      logins: [{ type: 'google', login: '<EMAIL>' }],
    }),
  );
  expect(response.status).toBe(200);

  const responseUser = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.USER,
      company_id,
      title: 'APInewOwner',
      logins: [{ type: 'api' }],
    }),
  );
  expect(responseUser.status).toBe(200);
  expect(responseUser.body.ops[0].proc).toBe('ok');
  const userOw = responseUser.body;
  const apikey = {
    key: `${userOw.ops[0].users[0].logins[0].obj_id}`,
    secret: userOw.ops[0].users[0].logins[0].key,
    companies: [],
    title: '',
    id: `${userOw.ops[0].users[0].obj_id}`,
  } as ApiKey;
  newapi = application.getApiKeyClient(apikey);

  const responseConv = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.CONV,
      title: `${OBJ_TYPE.CONV}_${Date.now()}`,
      folder_id: 0,
      conv_type: 'process',
      company_id,
    }),
  );
  conv_id = responseConv.body.ops[0].obj_id;

  const responseList = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.CONV,
      obj_id: conv_id,
      company_id,
    }),
  );
  expect(responseList.status).toBe(200);
  process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
  final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

  const responseState = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.CONV,
      company_id,
      title: `${OBJ_TYPE.CONV}_${Date.now()}`,
      folder_id: 0,
      conv_type: 'state',
    }),
  );
  state_id = responseState.body.ops[0].obj_id;

  const responseTask = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id: state_id,
      data: {
        a: 'select * from Users',
      },
      ref: `test`,
    }),
  );
  expect(responseTask.status).toBe(200);
  expect(responseTask.body.ops[0].proc).toEqual('ok');
  expect(responseTask.body.ops[0].obj).toEqual('task');

  const responseTask1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id: state_id,
      data: {
        key: 'Name',
        test: 'Id',
        key1: 'Users',
      },
      ref: `test1`,
    }),
  );
  expect(responseTask1.status).toBe(200);
  expect(responseTask1.body.ops[0].proc).toEqual('ok');
  expect(responseTask1.body.ops[0].obj).toEqual('task');
});

describe.each([data.postgres1, data.mysql, data.mssql, data.mongo, data.oracle])(
  'Workspace DB Connection Instance',
  config => {
    test(`Check connection with 'valid' config for '${config.driver}'`, async () => {
      const { response } = await api.dbCallApi.checkConnection(config as ConnectionConfig);
      expect(response.status).toEqual(200);
      expect(response.body.request_proc).toEqual('ok');
      expect(response.body.ops[0].proc).toEqual('ok');
    });

    test(`Create connection instance with 'valid' config for '${config.driver}'`, async () => {
      const { response } = await api.dbCallApi.createConnection(config as ConnectionConfig, company_id);
      expect(response.status).toEqual(200);
      expect(response.body.request_proc).toEqual('ok');
      expect(response.body.ops[0].proc).toEqual('ok');
      expect(response.body.ops[0].obj).toEqual('instance');
      expect(response.body.ops[0].instance_type).toEqual('db_call');
      obj_id = response.body.ops[0].obj_id;
      nameDB = config.driver;
      idArray.push({ [nameDB]: obj_id });
    });
  },
);

test(`Modify conv - create logic db_call for postgres and send task`, async () => {
  const responseModify = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        {
          type: 'db_call',
          extra: {},
          extra_type: {},
          instance_id: idArray[0].postgres,
          query: 'select * from Products where id =1;',
        },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModify.status).toEqual(200);
  expect(responseModify.body.request_proc).toEqual('ok');
  expect(responseModify.body.ops[0].proc).toEqual('ok');
  expect(responseModify.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommit = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommit.status).toBe(200);
  expect(responseCommit.body.ops[0].proc).toEqual('ok');
  expect(responseCommit.body.ops[0].obj_id).toEqual(conv_id);

  const responseTask = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTask.status).toBe(200);
  expect(responseTask.body.ops[0].proc).toEqual('ok');
  expect(responseTask.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 3000));
  const responseList = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseList.status).toBe(200);
  expect(responseList.body.ops[0].proc).toEqual('ok');
  expect(responseList.body.ops[0].obj).toEqual('node');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].productcount).toEqual(52000);
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].price).toEqual('999.99');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].productname).toContain('iPhone 12');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].manufacturer).toContain('Apple');
  expect(responseList.body.ops[0].list[0].data.a).toEqual(1);
});

test(`Delete instance db_call for postgres and send task (instance_not_found)/restore instance and send task`, async () => {
  const responseInstDelete = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.DELETE,
      obj: OBJ_TYPE.INSTANCE,
      obj_id: idArray[0].postgres,
    }),
  );
  expect(responseInstDelete.status).toBe(200);
  expect(responseInstDelete.body.ops[0].proc).toBe('ok');

  const responseTask1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTask1.status).toBe(200);
  expect(responseTask1.body.ops[0].proc).toEqual('ok');
  expect(responseTask1.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 3000));
  const responseList1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseList1.status).toBe(200);
  expect(responseList1.body.ops[0].proc).toEqual('ok');
  expect(responseList1.body.ops[0].obj).toEqual('node');
  expect(responseList1.body.ops[0].list[0].data.__conveyor_db_call_return_description__).toEqual('instance_not_found');

  const responseInstRestore = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.RESTORE,
      obj: OBJ_TYPE.INSTANCE,
      obj_id: idArray[0].postgres,
    }),
  );
  expect(responseInstRestore.status).toBe(200);
  expect(responseInstRestore.body.ops[0].proc).toBe('ok');

  const responseTask = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTask.status).toBe(200);
  expect(responseTask.body.ops[0].proc).toEqual('ok');
  expect(responseTask.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 3000));
  const responseList = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseList.status).toBe(200);
  expect(responseList.body.ops[0].proc).toEqual('ok');
  expect(responseList.body.ops[0].obj).toEqual('node');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].productcount).toEqual(52000);
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].price).toEqual('999.99');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].productname).toContain('iPhone 12');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].manufacturer).toContain('Apple');
  expect(responseList.body.ops[0].list[0].data.a).toEqual(1);
});

test(`Modify conv - create logic db_call for mysql and send task`, async () => {
  const responseModify = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        { type: 'db_call', extra: {}, extra_type: {}, instance_id: idArray[1].mysql, query: 'SELECT * from months;' },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModify.status).toEqual(200);
  expect(responseModify.body.request_proc).toEqual('ok');
  expect(responseModify.body.ops[0].proc).toEqual('ok');
  expect(responseModify.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommit = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommit.status).toBe(200);
  expect(responseCommit.body.ops[0].proc).toEqual('ok');
  expect(responseCommit.body.ops[0].obj_id).toEqual(conv_id);

  const responseTask = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTask.status).toBe(200);
  expect(responseTask.body.ops[0].proc).toEqual('ok');
  expect(responseTask.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 3000));
  const responseList = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseList.status).toBe(200);
  expect(responseList.body.ops[0].proc).toEqual('ok');
  expect(responseList.body.ops[0].obj).toEqual('node');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].dependencies).toEqual('{"new": "new1"}');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].createdAt).toEqual('2021-01-19 14:20:57');
  expect(responseList.body.ops[0].list[0].data.a).toEqual(1);
});

test(`Debug logic db_call mysql`, async () => {
  const responseModify = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.CONV,
      obj_id: conv_id,
      folder_id: 0,
      status: 'debug',
    }),
  );
  expect(responseModify.status).toEqual(200);
  expect(responseModify.body.request_proc).toEqual('ok');
  expect(responseModify.body.ops[0].proc).toEqual('ok');
  expect(responseModify.body.ops[0].new_status).toEqual(5);

  const responseTask = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTask.status).toBe(200);
  expect(responseTask.body.ops[0].proc).toEqual('ok');
  expect(responseTask.body.ops[0].obj).toEqual('task');
  task_id = responseTask.body.ops[0].obj_id;

  await new Promise(r => setTimeout(r, 3000));

  const responseStep = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.STEP_NEXT,
      obj: OBJ_TYPE.TASK,
      obj_id: task_id,
      branch: 'logic',
      conv_id,
      folder_id: 0,
      data: { a: 1 },
    }),
  );
  expect(responseStep.status).toBe(200);
  expect(responseStep.body.ops[0].proc).toEqual('ok');
  expect(responseStep.body.ops[0].prev_node_name).toEqual('start');
  expect(responseStep.body.ops[0].data).toEqual({ a: 1 });
  expect(responseStep.body.ops[0].logic[0].type).toEqual('db_call');

  const responseStep1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.STEP_NEXT,
      obj: OBJ_TYPE.TASK,
      obj_id: task_id,
      branch: 'logic',
      conv_id,
      folder_id: 0,
      data: { a: 1 },
    }),
  );
  expect(responseStep1.status).toBe(200);
  expect(responseStep1.body.ops[0].proc).toEqual('ok');
  expect(responseStep1.body.ops[0].prev_node_name).toEqual('process');
  expect(responseStep1.body.ops[0].node_name).toEqual('final');
  expect(responseStep1.body.ops[0].data.a).toEqual(1);
  expect(responseStep1.body.ops[0].data.__db_call_res__[0].name).toEqual('January');

  await new Promise(r => setTimeout(r, 1000));

  const responseList = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseList.status).toBe(200);
  expect(responseList.body.ops[0].proc).toEqual('ok');
  expect(responseList.body.ops[0].obj).toEqual('node');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].dependencies).toEqual('{"new": "new1"}');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].createdAt).toEqual('2021-01-19 14:20:57');
  expect(responseList.body.ops[0].list[0].data.a).toEqual(1);

  const responseModify1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.CONV,
      obj_id: conv_id,
      folder_id: 0,
      status: 'active',
    }),
  );
  expect(responseModify1.status).toEqual(200);
  expect(responseModify1.body.request_proc).toEqual('ok');
  expect(responseModify1.body.ops[0].proc).toEqual('ok');
  expect(responseModify1.body.ops[0].new_status).toEqual(1);
});

test(`Modify conv - added dinamic parameters in query {{}}`, async () => {
  const responseModify = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        {
          type: 'db_call',
          extra: {},
          extra_type: {},
          instance_id: idArray[1].mysql,
          query: `SHOW VARIABLES LIKE '{{test}}';`,
        },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModify.status).toEqual(200);
  expect(responseModify.body.request_proc).toEqual('ok');
  expect(responseModify.body.ops[0].proc).toEqual('ok');
  expect(responseModify.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommit = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommit.status).toBe(200);
  expect(responseCommit.body.ops[0].proc).toEqual('ok');
  expect(responseCommit.body.ops[0].obj_id).toEqual(conv_id);

  const responseTask = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
        test: '%version%',
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTask.status).toBe(200);
  expect(responseTask.body.ops[0].proc).toEqual('ok');
  expect(responseTask.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 3000));
  const responseList = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseList.status).toBe(200);
  expect(responseList.body.ops[0].proc).toEqual('ok');
  expect(responseList.body.ops[0].obj).toEqual('node');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].Variable_name).toEqual('innodb_version');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].Value).toEqual('5.7.32');
  expect(responseList.body.ops[0].list[0].data.a).toEqual(1);

  const responseModify1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        {
          type: 'db_call',
          extra: {},
          extra_type: {},
          instance_id: idArray[1].mysql,
          query: `select {{key}} from Users WHERE Id = {{test}};`,
        },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModify1.status).toEqual(200);
  expect(responseModify1.body.request_proc).toEqual('ok');
  expect(responseModify1.body.ops[0].proc).toEqual('ok');
  expect(responseModify1.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommit1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommit1.status).toBe(200);
  expect(responseCommit1.body.ops[0].proc).toEqual('ok');
  expect(responseCommit1.body.ops[0].obj_id).toEqual(conv_id);

  const responseTask1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
        test: 1,
        key: 'Name',
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTask1.status).toBe(200);
  expect(responseTask1.body.ops[0].proc).toEqual('ok');
  expect(responseTask1.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 3000));
  const responseList1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseList1.status).toBe(200);
  expect(responseList1.body.ops[0].proc).toEqual('ok');
  expect(responseList1.body.ops[0].obj).toEqual('node');
  expect(responseList1.body.ops[0].list[0].data.__db_call_res__[0].Name).toEqual('Komerista');
  expect(responseList1.body.ops[0].list[0].data.a).toEqual(1);
});

test(`Modify conv - added dinamic parameters in query {{test.key[0].a}}`, async () => {
  const responseModify = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        { type: 'db_call', extra: {}, extra_type: {}, instance_id: idArray[1].mysql, query: `{{test.key[0].t}}` },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModify.status).toEqual(200);
  expect(responseModify.body.request_proc).toEqual('ok');
  expect(responseModify.body.ops[0].proc).toEqual('ok');
  expect(responseModify.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommit = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommit.status).toBe(200);
  expect(responseCommit.body.ops[0].proc).toEqual('ok');
  expect(responseCommit.body.ops[0].obj_id).toEqual(conv_id);

  const responseTask = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
        test: { key: [{ t: 'select * from Users;' }] },
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTask.status).toBe(200);
  expect(responseTask.body.ops[0].proc).toEqual('ok');
  expect(responseTask.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 3000));
  const responseList = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseList.status).toBe(200);
  expect(responseList.body.ops[0].proc).toEqual('ok');
  expect(responseList.body.ops[0].obj).toEqual('node');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].name).toEqual('Komerista');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].days).toEqual(25);
  expect(responseList.body.ops[0].list[0].data.a).toEqual(1);

  const responseModify1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        {
          type: 'db_call',
          extra: {},
          extra_type: {},
          instance_id: idArray[1].mysql,
          query: `select {{test.key[0].t}} from Users WHERE Id = {{test.key[0].s}};`,
        },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModify1.status).toEqual(200);
  expect(responseModify1.body.request_proc).toEqual('ok');
  expect(responseModify1.body.ops[0].proc).toEqual('ok');
  expect(responseModify1.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommit1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommit1.status).toBe(200);
  expect(responseCommit1.body.ops[0].proc).toEqual('ok');
  expect(responseCommit1.body.ops[0].obj_id).toEqual(conv_id);

  const responseTask1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
        test: { key: [{ t: 'Name', s: 1 }] },
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTask1.status).toBe(200);
  expect(responseTask1.body.ops[0].proc).toEqual('ok');
  expect(responseTask1.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 3000));
  const responseList1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseList1.status).toBe(200);
  expect(responseList1.body.ops[0].proc).toEqual('ok');
  expect(responseList1.body.ops[0].obj).toEqual('node');
  expect(responseList1.body.ops[0].list[0].data.__db_call_res__[0].Name).toEqual('Komerista');
  expect(responseList1.body.ops[0].list[0].data.a).toEqual(1);
});

test(`Modify conv - added dinamic parameters in query {{conv[].ref.key}}`, async () => {
  const responseModify = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        {
          type: 'db_call',
          extra: {},
          extra_type: {},
          instance_id: idArray[1].mysql,
          query: `{{conv[${state_id}].ref[test].a}}`,
        },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModify.status).toEqual(200);
  expect(responseModify.body.request_proc).toEqual('ok');
  expect(responseModify.body.ops[0].proc).toEqual('ok');
  expect(responseModify.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommit = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommit.status).toBe(200);
  expect(responseCommit.body.ops[0].proc).toEqual('ok');
  expect(responseCommit.body.ops[0].obj_id).toEqual(conv_id);

  const responseTask = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTask.status).toBe(200);
  expect(responseTask.body.ops[0].proc).toEqual('ok');
  expect(responseTask.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 3000));
  const responseList = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseList.status).toBe(200);
  expect(responseList.body.ops[0].proc).toEqual('ok');
  expect(responseList.body.ops[0].obj).toEqual('node');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].name).toEqual('Komerista');
  expect(responseList.body.ops[0].list[0].data.a).toEqual(1);

  const responseModify1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        {
          type: 'db_call',
          extra: {},
          extra_type: {},
          instance_id: idArray[1].mysql,
          query: `select {{conv[${state_id}].ref[test1].key}} from {{conv[${state_id}].ref[test1].key1}} where Id = {{conv[${state_id}].ref[test1].test}}`,
        },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModify1.status).toEqual(200);
  expect(responseModify1.body.request_proc).toEqual('ok');
  expect(responseModify1.body.ops[0].proc).toEqual('ok');
  expect(responseModify1.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommit1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommit1.status).toBe(200);
  expect(responseCommit1.body.ops[0].proc).toEqual('ok');
  expect(responseCommit1.body.ops[0].obj_id).toEqual(conv_id);

  const responseTask1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTask1.status).toBe(200);
  expect(responseTask1.body.ops[0].proc).toEqual('ok');
  expect(responseTask1.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 3000));
  const responseList1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseList1.status).toBe(200);
  expect(responseList1.body.ops[0].proc).toEqual('ok');
  expect(responseList1.body.ops[0].obj).toEqual('node');
  expect(responseList1.body.ops[0].list[0].data.__db_call_res__[0].Name).toEqual('Komerista');
  expect(responseList1.body.ops[0].list[0].data.a).toEqual(1);
});

test(`Modify conv - create logic db_call for mssql and send task`, async () => {
  const responseModify = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        {
          type: 'db_call',
          extra: {},
          extra_type: {},
          instance_id: idArray[2].mssql,
          query: 'SELECT TOP (10) [MsgId],[Payload],[QueueId],[ParentMsgId],[Guid],[TimeStamp] FROM [dbo].[Message]',
        },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModify.status).toEqual(200);
  expect(responseModify.body.request_proc).toEqual('ok');
  expect(responseModify.body.ops[0].proc).toEqual('ok');
  expect(responseModify.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommit = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommit.status).toBe(200);
  expect(responseCommit.body.ops[0].proc).toEqual('ok');
  expect(responseCommit.body.ops[0].obj_id).toEqual(conv_id);

  const responseTask = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTask.status).toBe(200);
  expect(responseTask.body.ops[0].proc).toEqual('ok');
  expect(responseTask.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 3000));
  const responseList = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseList.status).toBe(200);
  expect(responseList.body.ops[0].proc).toEqual('ok');
  expect(responseList.body.ops[0].obj).toEqual('node');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].Payload).toEqual('{ "prop": "1" }');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].ParentMsgId).toEqual(null);
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].Guid).toEqual('C3E8CA21-CBC1-4F20-8EC7-8E3E737B674B');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].TimeStamp).toEqual('2021-12-08T10:35:00.62Z');
  expect(responseList.body.ops[0].list[0].data.a).toEqual(1);
});

test(`Modify conv - create logic db_call (insert/find/delete) for mongo and send task`, async () => {
  const responseModify = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        {
          type: 'db_call',
          extra: {},
          extra_type: {},
          instance_id: idArray[3].mongo,
          query:
            '{"insert":"users", "documents":[{"name": "Spayk", "age": 16, "languages": ["english",{"test":"franch"}],"maried":null,"obj":{"test":"1232"},"bool":true}]}',
        },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModify.status).toEqual(200);
  expect(responseModify.body.request_proc).toEqual('ok');
  expect(responseModify.body.ops[0].proc).toEqual('ok');
  expect(responseModify.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommit = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommit.status).toBe(200);
  expect(responseCommit.body.ops[0].proc).toEqual('ok');
  expect(responseCommit.body.ops[0].obj_id).toEqual(conv_id);

  const responseTask = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTask.status).toBe(200);
  expect(responseTask.body.ops[0].proc).toEqual('ok');
  expect(responseTask.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 6000));
  const responseList = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseList.status).toBe(200);
  expect(responseList.body.ops[0].proc).toEqual('ok');
  expect(responseList.body.ops[0].obj).toEqual('node');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__).toEqual({ n: 1, ok: 1 });
  expect(responseList.body.ops[0].list[0].data.a).toEqual(1);

  const responseModifyFind = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        {
          type: 'db_call',
          extra: {},
          extra_type: {},
          instance_id: idArray[3].mongo,
          query: '{"find": "users","filter":{"name":"Spayk"}}',
        },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModifyFind.status).toEqual(200);
  expect(responseModifyFind.body.request_proc).toEqual('ok');
  expect(responseModifyFind.body.ops[0].proc).toEqual('ok');
  expect(responseModifyFind.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommitFind = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommitFind.status).toBe(200);
  expect(responseCommitFind.body.ops[0].proc).toEqual('ok');
  expect(responseCommitFind.body.ops[0].obj_id).toEqual(conv_id);

  const responseTaskFind = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTaskFind.status).toBe(200);
  expect(responseTaskFind.body.ops[0].proc).toEqual('ok');
  expect(responseTaskFind.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 3000));
  const responseListFind = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseListFind.status).toBe(200);
  expect(responseListFind.body.ops[0].proc).toEqual('ok');
  expect(responseListFind.body.ops[0].obj).toEqual('node');
  expect(responseListFind.body.ops[0].list[0].data.__db_call_res__[0].bool).toEqual(true);
  expect(responseListFind.body.ops[0].list[0].data.__db_call_res__[0].languages).toEqual([
    'english',
    { test: 'franch' },
  ]);
  expect(responseListFind.body.ops[0].list[0].data.__db_call_res__[0].obj).toEqual({ test: '1232' });
  expect(responseListFind.body.ops[0].list[0].data.__db_call_res__[0].maried).toEqual(null);
  expect(responseListFind.body.ops[0].list[0].data.__db_call_res__[0].name).toEqual('Spayk');
  expect(responseListFind.body.ops[0].list[0].data.a).toEqual(1);

  const responseModifyDelete = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        {
          type: 'db_call',
          extra: {},
          extra_type: {},
          instance_id: idArray[3].mongo,
          query: '{"delete": "users","deletes":[{"q":{"name":"Spayk"},"limit":0}]}',
        },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModifyDelete.status).toEqual(200);
  expect(responseModifyDelete.body.request_proc).toEqual('ok');
  expect(responseModifyDelete.body.ops[0].proc).toEqual('ok');
  expect(responseModifyDelete.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommitDelete = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommitDelete.status).toBe(200);
  expect(responseCommitDelete.body.ops[0].proc).toEqual('ok');
  expect(responseCommitDelete.body.ops[0].obj_id).toEqual(conv_id);

  const responseTaskDelete = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTaskDelete.status).toBe(200);
  expect(responseTaskDelete.body.ops[0].proc).toEqual('ok');
  expect(responseTaskDelete.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 6000)); ///
  const responseListDelete = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseListDelete.status).toBe(200);
  expect(responseListDelete.body.ops[0].proc).toEqual('ok');
  expect(responseListDelete.body.ops[0].obj).toEqual('node');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__).toEqual({ n: 1, ok: 1 });
  expect(responseListDelete.body.ops[0].list[0].data.a).toEqual(1);

  const responseModifyFind1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        {
          type: 'db_call',
          extra: {},
          extra_type: {},
          instance_id: idArray[3].mongo,
          query: '{"find": "users","filter":{"name":"Spayk"}}',
        },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModifyFind1.status).toEqual(200);
  expect(responseModifyFind1.body.request_proc).toEqual('ok');
  expect(responseModifyFind1.body.ops[0].proc).toEqual('ok');
  expect(responseModifyFind1.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommitFind1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommitFind1.status).toBe(200);
  expect(responseCommitFind1.body.ops[0].proc).toEqual('ok');
  expect(responseCommitFind1.body.ops[0].obj_id).toEqual(conv_id);

  const responseTaskFind1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTaskFind1.status).toBe(200);
  expect(responseTaskFind1.body.ops[0].proc).toEqual('ok');
  expect(responseTaskFind1.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 3000));
  const responseListFind1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseListFind1.status).toBe(200);
  expect(responseListFind1.body.ops[0].proc).toEqual('ok');
  expect(responseListFind1.body.ops[0].obj).toEqual('node');
  expect(responseListFind1.body.ops[0].list[0].data.__db_call_res__).toEqual([]);
  expect(responseListFind1.body.ops[0].list[0].data.a).toEqual(1);
});

test(`Modify conv - create logic db_call (create/insert/select/drop) for oracle and send task`, async () => {
  const responseModify = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        {
          type: 'db_call',
          extra: {},
          extra_type: {},
          instance_id: idArray[4].oracle,
          query:
            'CREATE TABLE USERS(employee_name CHAR(20) NOT NULL,employee_phone long,employee_id VARCHAR2(20) NOT NULL,employee_id2 number(20) NOT NULL,employee_id3 BINARY_FLOAT,employee_birthday date,employee_addres VARCHAR(100) NOT NULL,employee_married CHAR(20) NOT NULL,PRIMARY KEY(employee_name))',
        },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModify.status).toEqual(200);
  expect(responseModify.body.request_proc).toEqual('ok');
  expect(responseModify.body.ops[0].proc).toEqual('ok');
  expect(responseModify.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommit = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommit.status).toBe(200);
  expect(responseCommit.body.ops[0].proc).toEqual('ok');
  expect(responseCommit.body.ops[0].obj_id).toEqual(conv_id);

  const responseTask = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTask.status).toBe(200);
  expect(responseTask.body.ops[0].proc).toEqual('ok');
  expect(responseTask.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 3000));
  const responseList = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseList.status).toBe(200);
  expect(responseList.body.ops[0].proc).toEqual('ok');
  expect(responseList.body.ops[0].obj).toEqual('node');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__).toEqual([]);
  expect(responseList.body.ops[0].list[0].data.a).toEqual(1);

  const responseModifyInsert = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        {
          type: 'db_call',
          extra: {},
          extra_type: {},
          instance_id: idArray[4].oracle,
          query:
            "INSERT INTO USERS(employee_name, employee_phone, employee_id,employee_id2,employee_id3,employee_birthday, employee_addres,employee_married) VALUES('Juliya', '0993530153', 0,1,1.12,'19 may 1986','{\"city\":\"Dnipro\",\"st\":\"test\"}','true')",
        },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModifyInsert.status).toEqual(200);
  expect(responseModifyInsert.body.request_proc).toEqual('ok');
  expect(responseModifyInsert.body.ops[0].proc).toEqual('ok');
  expect(responseModifyInsert.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommitInsert = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommitInsert.status).toBe(200);
  expect(responseCommitInsert.body.ops[0].proc).toEqual('ok');
  expect(responseCommitInsert.body.ops[0].obj_id).toEqual(conv_id);

  const responseTaskInsert = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTaskInsert.status).toBe(200);
  expect(responseTaskInsert.body.ops[0].proc).toEqual('ok');
  expect(responseTaskInsert.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 3000));
  const responseListInsert = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseListInsert.status).toBe(200);
  expect(responseListInsert.body.ops[0].proc).toEqual('ok');
  expect(responseListInsert.body.ops[0].obj).toEqual('node');
  expect(responseListInsert.body.ops[0].list[0].data.__db_call_res__).toEqual([]);
  expect(responseListInsert.body.ops[0].list[0].data.a).toEqual(1);

  const responseModifySelect = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        { type: 'db_call', extra: {}, extra_type: {}, instance_id: idArray[4].oracle, query: 'SELECT *  FROM USERS' },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModifySelect.status).toEqual(200);
  expect(responseModifySelect.body.request_proc).toEqual('ok');
  expect(responseModifySelect.body.ops[0].proc).toEqual('ok');
  expect(responseModifySelect.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommitSelect = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommitSelect.status).toBe(200);
  expect(responseCommitSelect.body.ops[0].proc).toEqual('ok');
  expect(responseCommitSelect.body.ops[0].obj_id).toEqual(conv_id);

  const responseTaskSelect = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTaskSelect.status).toBe(200);
  expect(responseTaskSelect.body.ops[0].proc).toEqual('ok');
  expect(responseTaskSelect.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 3000));
  const responseListSelect = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseListSelect.status).toBe(200);
  expect(responseListSelect.body.ops[0].proc).toEqual('ok');
  expect(responseListSelect.body.ops[0].obj).toEqual('node');
  expect(responseListSelect.body.ops[0].list[0].data.__db_call_res__[0].EMPLOYEE_NAME).toContain('Juliya');
  expect(responseListSelect.body.ops[0].list[0].data.__db_call_res__[0].EMPLOYEE_PHONE).toContain('0993530153');
  expect(responseListSelect.body.ops[0].list[0].data.__db_call_res__[0].EMPLOYEE_ADDRES).toContain(
    '{"city":"Dnipro","st":"test"',
  );
  expect(responseListSelect.body.ops[0].list[0].data.__db_call_res__[0].EMPLOYEE_MARRIED).toContain(
    'true                ',
  );

  expect(responseListSelect.body.ops[0].list[0].data.a).toEqual(1);

  const responseModifyDrop = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        { type: 'db_call', extra: {}, extra_type: {}, instance_id: idArray[4].oracle, query: 'DROP TABLE USERS' },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModifyDrop.status).toEqual(200);
  expect(responseModifyDrop.body.request_proc).toEqual('ok');
  expect(responseModifyDrop.body.ops[0].proc).toEqual('ok');
  expect(responseModifyDrop.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommitDrop = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommitDrop.status).toBe(200);
  expect(responseCommitDrop.body.ops[0].proc).toEqual('ok');
  expect(responseCommitDrop.body.ops[0].obj_id).toEqual(conv_id);

  const responseTaskDrop = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTaskDrop.status).toBe(200);
  expect(responseTaskDrop.body.ops[0].proc).toEqual('ok');
  expect(responseTaskDrop.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 3000));
  const responseListDrop = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseListDrop.status).toBe(200);
  expect(responseListDrop.body.ops[0].proc).toEqual('ok');
  expect(responseListDrop.body.ops[0].obj).toEqual('node');
  expect(responseListDrop.body.ops[0].list[0].data.__db_call_res__).toEqual([]);
  expect(responseListDrop.body.ops[0].list[0].data.a).toEqual(1);
});

test(`Deploy conv with insnance (user has no rights)`, async () => {
  const responseInstDel = await newapi.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.INSTANCE,
      instance_type: `db_call`,
      folder_id: 0,
      company_id,
      title: `Instance_${Date.now()}`,
      data: {
        driver: `postgres`,
        host: `COR-6938.middleware.loc`,
        port: `5432`,
        username: `postgres`,
        password: `pass`,
        ssl: false,
        database: `dbcall`,
        timeoutMs: 30000,
      },
    }),
  );
  expect(responseInstDel.status).toBe(200);
  expect(responseInstDel.body.ops[0].obj).toBe('instance');
  instanceNew = responseInstDel.body.ops[0].obj_id;

  const responseModify = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        {
          type: 'db_call',
          extra: {},
          extra_type: {},
          instance_id: instanceNew,
          query: 'SELECT TOP (10) [MsgId],[Payload],[QueueId],[ParentMsgId],[Guid],[TimeStamp] FROM [dbo].[Message]',
        },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModify.status).toEqual(200);
  expect(responseModify.body.request_proc).toEqual('ok');
  expect(responseModify.body.ops[0].proc).toEqual('ok');
  expect(responseModify.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommit = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommit.status).toBe(200);
  expect(responseCommit.body.ops[0].proc).toEqual('error');
  expect(responseCommit.body.ops[0].errors).toEqual({ [process_node_ID]: ['User has no rights'] });

  await newapi.request(
    createRequestWithOps({
      type: REQUEST_TYPE.DELETE,
      obj: OBJ_TYPE.INSTANCE,
      obj_id: instanceNew,
    }),
  );
});

test(`Deploy conv with insnance (Invalid dbcall instance)`, async () => {
  const responseInst = await newapi.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.INSTANCE,
      instance_type: `db_call`,
      folder_id: 0,
      company_id,
      title: `Instance_${Date.now()}`,
      data: {
        driver: `postgres`,
        host: `COR-6938.middleware.loc`,
        port: `5432`,
        username: `postgres`,
        password: `pass`,
        ssl: false,
        database: `dbcall`,
        timeoutMs: 30000,
      },
    }),
  );
  expect(responseInst.status).toBe(200);
  expect(responseInst.body.ops[0].obj).toBe('instance');
  instanceNew = responseInst.body.ops[0].obj_id;

  await newapi.request(
    createRequestWithOps({
      type: REQUEST_TYPE.DELETE,
      obj: OBJ_TYPE.INSTANCE,
      obj_id: instanceNew,
    }),
  );

  const responseModify = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        {
          type: 'db_call',
          extra: {},
          extra_type: {},
          instance_id: instanceNew,
          query: 'SELECT TOP (10) [MsgId],[Payload],[QueueId],[ParentMsgId],[Guid],[TimeStamp] FROM [dbo].[Message]',
        },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModify.status).toEqual(200);
  expect(responseModify.body.request_proc).toEqual('ok');
  expect(responseModify.body.ops[0].proc).toEqual('ok');
  expect(responseModify.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommit = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommit.status).toBe(200);
  expect(responseCommit.body.ops[0].proc).toEqual('error');
  expect(responseCommit.body.ops[0].errors).toEqual({ [process_node_ID]: ['Invalid dbcall instance'] });
});

test(`Modify insnance`, async () => {
  const responseInst = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.INSTANCE,
      instance_type: `db_call`,
      folder_id: 0,
      company_id,
      title: `Instance_${Date.now()}`,
      data: {
        driver: 'postgres',
        host: 'abul.db.elephantsql.com',
        port: '5432',
        username: 'fsodcwcj',
        password: 'gxfcdqMhc1uDG-rpzO-Sy_6FeUvn79Yo',
        ssl: 'false',
        database: 'fsodcwcj',
        timeoutMs: '30000',
      },
    }),
  );
  expect(responseInst.status).toBe(200);
  expect(responseInst.body.ops[0].obj).toBe('instance');
  instanceNew = responseInst.body.ops[0].obj_id;

  const responseModify = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        {
          type: 'db_call',
          extra: {},
          extra_type: {},
          instance_id: idArray[0].postgres,
          query: 'select * from Products where id =1;',
        },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModify.status).toEqual(200);
  expect(responseModify.body.request_proc).toEqual('ok');
  expect(responseModify.body.ops[0].proc).toEqual('ok');
  expect(responseModify.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommit = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommit.status).toBe(200);
  expect(responseCommit.body.ops[0].proc).toEqual('ok');
  expect(responseCommit.body.ops[0].obj_id).toEqual(conv_id);

  const responseTask = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTask.status).toBe(200);
  expect(responseTask.body.ops[0].proc).toEqual('ok');
  expect(responseTask.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 3000));
  const responseList = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseList.status).toBe(200);
  expect(responseList.body.ops[0].proc).toEqual('ok');
  expect(responseList.body.ops[0].obj).toEqual('node');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].productcount).toEqual(52000);
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].price).toEqual('999.99');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].productname).toContain('iPhone 12');
  expect(responseList.body.ops[0].list[0].data.__db_call_res__[0].manufacturer).toContain('Apple');
  expect(responseList.body.ops[0].list[0].data.a).toEqual(1);

  const responseModifyInst = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.INSTANCE,
      obj_id: instanceNew,
      instance_type: `db_call`,
      folder_id: 0,
      company_id,
      title: `Instance_${Date.now()}`,
      data: {
        driver: 'mysql',
        host: '**********',
        port: '3306',
        username: 'root',
        password: 'she2kojiJo',
        ssl: 'false',
        database: 'COR6783',
        timeoutMs: '30000',
      },
    }),
  );
  expect(responseModifyInst.status).toBe(200);
  expect(responseModifyInst.body.ops[0].obj).toBe('instance');

  const responseModify1 = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: process_node_ID,
      conv_id,
      folder_id: 0,
      title: 'process',
      obj_type: 0,
      semaphors: [],
      version: 22,
      logics: [
        { type: 'db_call', extra: {}, extra_type: {}, instance_id: idArray[1].mysql, query: 'SELECT * from months;' },
        { type: 'go', to_node_id: final_node_ID },
      ],
    }),
  );
  expect(responseModify1.status).toEqual(200);
  expect(responseModify1.body.request_proc).toEqual('ok');
  expect(responseModify1.body.ops[0].proc).toEqual('ok');
  expect(responseModify1.body.ops[0].obj_id).toEqual(process_node_ID);

  const responseCommitModify = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CONFIRM,
      obj: OBJ_TYPE.COMMIT,
      conv_id: conv_id,
      version: 22,
    }),
  );
  expect(responseCommitModify.status).toBe(200);
  expect(responseCommitModify.body.ops[0].proc).toEqual('ok');
  expect(responseCommitModify.body.ops[0].obj_id).toEqual(conv_id);

  const responseTaskModify = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      conv_id,
      data: {
        a: 1,
      },
      ref: `ref_${Date.now()}`,
    }),
  );
  expect(responseTaskModify.status).toBe(200);
  expect(responseTaskModify.body.ops[0].proc).toEqual('ok');
  expect(responseTaskModify.body.ops[0].obj).toEqual('task');

  await new Promise(r => setTimeout(r, 3000));
  const responseListModify = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.NODE,
      obj_id: final_node_ID,
      conv_id,
      limit: 1,
    }),
  );
  expect(responseListModify.status).toBe(200);
  expect(responseListModify.body.ops[0].proc).toEqual('ok');
  expect(responseListModify.body.ops[0].obj).toEqual('node');
  expect(responseListModify.body.ops[0].list[0].data.__db_call_res__[0].dependencies).toEqual('{"new": "new1"}');
  expect(responseListModify.body.ops[0].list[0].data.__db_call_res__[0].createdAt).toEqual('2021-01-19 14:20:57');
  expect(responseListModify.body.ops[0].list[0].data.a).toEqual(1);

  await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.DELETE,
      obj: OBJ_TYPE.INSTANCE,
      obj_id: instanceNew,
      company_id,
    }),
  );
});

afterAll(async () => {
  const response = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.DELETE,
      obj: OBJ_TYPE.CONV,
      obj_id: conv_id,
    }),
  );
  expect(response.status).toBe(200);

  const responseInstP = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.DELETE,
      obj: OBJ_TYPE.INSTANCE,
      obj_id: idArray[0].postgres,
    }),
  );
  expect(responseInstP.status).toBe(200);

  const responseInstMS = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.DELETE,
      obj: OBJ_TYPE.INSTANCE,
      obj_id: idArray[2].mssql,
    }),
  );
  expect(responseInstMS.status).toBe(200);

  const responseInstMY = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.DELETE,
      obj: OBJ_TYPE.INSTANCE,
      obj_id: idArray[1].mysql,
    }),
  );
  expect(responseInstMY.status).toBe(200);

  const responseInstMO = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.DELETE,
      obj: OBJ_TYPE.INSTANCE,
      obj_id: idArray[3].mongo,
    }),
  );
  expect(responseInstMO.status).toBe(200);

  const responseInstOR = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.DELETE,
      obj: OBJ_TYPE.INSTANCE,
      obj_id: idArray[4].oracle,
    }),
  );
  expect(responseInstOR.status).toBe(200);
});
