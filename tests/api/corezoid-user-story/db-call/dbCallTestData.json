{"postgres": {"driver": "postgres", "host": "abul.db.elephantsql.com", "port": "5432", "username": "fsodcwcj", "password": "gxfcdqMhc1uDG-rpzO-Sy_6FeUvn79Yo", "ssl": false, "database": "fsodcwcj", "timeoutMs": "30000"}, "mysql": {"driver": "mysql", "host": "**********", "port": "3306", "username": "root", "password": "she2kojiJo", "ssl": false, "database": "COR6783", "timeoutMs": "30000"}, "mssql": {"driver": "mssql", "host": "**********", "port": "1434", "username": "sa", "password": "wah3<PERSON><PERSON><PERSON>", "ssl": false, "database": "SUPPORT1230", "timeoutMs": "30000"}, "mongo": {"driver": "mongo", "host": "**********", "port": "27017", "username": "root", "password": "mongodb", "ssl": false, "database": "admin", "timeoutMs": "30000"}, "oracle": {"driver": "oracle", "host": "**********", "port": "1521", "username": "system", "password": "oracledb", "ssl": false, "database": "XE", "timeoutMs": "30000"}, "mysql1": {"driver": "mysql", "host": "COR-6938.middleware.loc", "port": "3306", "username": "root", "password": "pass", "ssl": false, "database": "dbcall", "timeoutMs": "30000"}, "mssql1": {"driver": "mssql", "host": "COR-6938.middleware.loc", "port": "1433", "username": "sa", "password": "Password_1", "ssl": false, "database": "dbcall", "timeoutMs": "30000"}, "postgres1": {"driver": "postgres", "host": "**********", "port": "5432", "username": "test_user", "password": "3sha1cheeH4uoziej1u", "ssl": false, "database": "test", "timeoutMs": "30000"}}