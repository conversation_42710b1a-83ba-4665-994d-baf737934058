import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import faker from 'faker';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
  generateName,
} from '../../../../utils/corezoidRequest';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
} from '../../../../application/api/ApiObj';

describe('Copy from alias', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id_copy: number;
  let conv_id: number;
  let owner_id_c: number;
  let final_node_ID: string | number;
  let process_node_ID_c: string | number;
  let final_node_ID_c: string | number;
  let task_id_c: string | number;
  let title: string;
  let err_node_ID: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    title = generateName(OBJ_TYPE.OBJS);

    const responseCopy = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Conv_Copy`);
    conv_id_copy = responseCopy.body.ops[0].obj_id;

    const responseConv = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Conv_Empty`);
    conv_id = responseConv.body.ops[0].obj_id;

    const responseListCopy = await requestListConv(api, conv_id_copy, company_id);
    expect(responseListCopy.status).toBe(RESP_STATUS.OK);
    const { list: listCopy, owner_id: ownId } = responseListCopy.body.ops[0];
    process_node_ID_c = (listCopy as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID_c = (listCopy as Array<any>).find(item => item.title === 'final').obj_id;
    owner_id_c = ownId;

    const responseCreateNodeError = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        title: 'Errorfinal',
        conv_id: conv_id_copy,
        obj_type: 2,
        version: 22,
      }),
    );
    expect(responseCreateNodeError.status).toBe(RESP_STATUS.OK);
    err_node_ID = responseCreateNodeError.body.ops[0].obj_id;

    const responseListConv = await requestListConv(api, conv_id, company_id);
    expect(responseListConv.status).toBe(RESP_STATUS.OK);
    final_node_ID = (responseListConv.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
  });

  test(`shouldn't Deploy modify conv with ref>255 `, async () => {
    const responseCreateCopy = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID_c,
        conv_id: conv_id_copy,
        title,
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api_copy',
            sync: false,
            err_node_id: '',
            conv_id: `${conv_id}`,
            mode: 'create',
            ref: faker.random.alphaNumeric(256),
            data: {},
            data_type: {},
            group: 'all',
          },
          {
            to_node_id: final_node_ID_c,
            format: 'json',
            type: 'go',
            index: 1,
            node_title: 'final',
            conv_owner_id: owner_id_c,
            conv_owner_name: 'DTPO',
          },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateCopy.status).toBe(RESP_STATUS.OK);

    const responseCommitCopy = await requestConfirm(api, conv_id_copy, company_id);
    expect(responseCommitCopy.status).toBe(RESP_STATUS.OK);
    const { proc: procDeploy, errors: errorDeploy } = responseCommitCopy.body.ops[0];
    expect(procDeploy).toBe(PROC_STATUS.ERROR);
    expect(errorDeploy[`${process_node_ID_c}`][0]).toBe(
      "Key 'ref'. 'Value is not valid. Value's byte_size is more than maximum allowed: 255'",
    );
  });

  test(`shouldn't Send task to conv with ref>255 dinamic `, async () => {
    const responseCreateCopy = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID_c,
        conv_id: conv_id_copy,
        title,
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api_copy',
            sync: false,
            err_node_id: err_node_ID,
            conv_id: `${conv_id}`,
            mode: 'create',
            ref: '{{refDinamic}}',
            data: {},
            data_type: {},
            group: 'all',
          },
          {
            to_node_id: final_node_ID_c,
            format: 'json',
            type: 'go',
            index: 1,
            node_title: 'final',
            conv_owner_id: owner_id_c,
            conv_owner_name: 'DTPO',
          },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateCopy.status).toBe(RESP_STATUS.OK);

    const responseCommitCopy = await requestConfirm(api, conv_id_copy, company_id);
    expect(responseCommitCopy.status).toBe(RESP_STATUS.OK);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id_copy,
        data: { refDinamic: faker.random.alphaNumeric(256) },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    const { obj: objResTask, obj_id: objIdResTask } = responseTask.body.ops[0];
    expect(objResTask).toEqual(OBJ_TYPE.TASK);
    task_id_c = objIdResTask;

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id_copy,
        ref_or_obj_id: task_id_c,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    const { proc: prockShowTask, data: dataShowTask } = responseShow.body.ops[0];
    expect(prockShowTask).toEqual(PROC_STATUS.OK);
    expect(dataShowTask.__conveyor_copy_task_return_description__).toEqual(
      'task  reference has to be less the 255 symbols',
    );

    const responseShowConvFinal = await requestList(api, final_node_ID, conv_id, company_id);
    expect(responseShowConvFinal.status).toBe(RESP_STATUS.OK);
    const { proc: procFinal, list: listFinal } = responseShowConvFinal.body.ops[0];
    expect(procFinal).toEqual(PROC_STATUS.OK);
    expect(listFinal).toBeEmpty();

    const responseShowConvError = await requestList(api, err_node_ID, conv_id_copy, company_id);
    expect(responseShowConvError.status).toBe(RESP_STATUS.OK);
    const { proc: procError, list: listError } = responseShowConvError.body.ops[0];
    expect(procError).toEqual(PROC_STATUS.OK);
    const { data: dataError } = listError[0];
    expect(dataError.__conveyor_copy_task_return_description__).toEqual(
      'task  reference has to be less the 255 symbols',
    );
    expect(dataError.__conveyor_copy_task_return_type_tag__).toEqual('ref_max_size');
  });

  afterAll(async () => {
    const responseDeleteModify = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(responseDeleteModify.status).toBe(RESP_STATUS.OK);
    const responseDeleteCallback = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id_copy, company_id);
    expect(responseDeleteCallback.status).toBe(RESP_STATUS.OK);
  });
});
