# Code Execution Tests

This directory contains tests for executing JavaScript and Erlang code in Corezoid.

## Overview

The tests in this directory verify that various JavaScript and Erlang code snippets execute correctly in the Corezoid environment. The code snippets are stored in a JSON file (`code_samples.json`) and are executed one by one in the test.

## Files

- `CodeExecution.test.ts`: The main test file that executes the code snippets and verifies the results.
- `code_samples.json`: A JSON file containing the code snippets to be tested.

## Structure of `code_samples.json`

The `code_samples.json` file has the following structure:

```json
{
  "javascript": [
    {
      "name": "Test name",
      "code": "JavaScript code to execute",
      "expected": { "key": "expected value" }
    },
    ...
  ],
  "erlang": [
    {
      "name": "Test name",
      "code": "Erlang code to execute",
      "expected": { "key": "expected value" }
    },
    ...
  ]
}
```

Each entry in the `javascript` or `erlang` array has:
- `name`: A descriptive name for the test
- `code`: The code to execute
- `expected`: The expected result of executing the code

## How to Run the Tests

To run the tests, use the following command:

```bash
npx jest -i -c jest.api.config.js tests/api/api_code/CodeExecution.test.ts
```

## How to Add New Tests

To add new tests, simply add new entries to the `javascript` or `erlang` arrays in the `code_samples.json` file. Each entry should have a `name`, `code`, and `expected` field.

### Example: Adding a New JavaScript Test

```json
{
  "name": "New JavaScript Test",
  "code": "data.result = 'New test result';",
  "expected": { "result": "New test result" }
}
```

### Example: Adding a New Erlang Test

```json
{
  "name": "New Erlang Test",
  "code": "Result = \"New test result\", {data, [{result, Result}]}.",
  "expected": { "result": "New test result" }
}
```

## Notes

- The tests create a new process for each test run, execute the code, and then clean up by deleting the process.
- Each code snippet is executed in isolation, so there is no state shared between tests.
- The tests wait for 2 seconds after creating a task to allow the code to execute before checking the result.
- If a test fails, check the error message to see what went wrong. It could be that the code didn't execute correctly, or that the expected result doesn't match the actual result.
