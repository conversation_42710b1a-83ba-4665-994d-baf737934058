import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import {
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
  requestShow,
} from '../../../../application/api/ApiObj';
import * as fs from 'fs';
import * as path from 'path';

const codeSamplesPath = path.join(__dirname, 'code_samples.json');
const codeSamplesData = JSON.parse(fs.readFileSync(codeSamplesPath, 'utf8'));
const codeSamples = codeSamplesData.code_samples;

interface CodeSample {
  name: string;
  lang: 'js' | 'erl';
  code: string;
  expected: Record<string, any>;
  data: Record<string, any>;
}

describe('Optimized Code Execution Tests', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Optimized_Code_Execution_Test`);
    conv_id = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_id, company_id);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
  });

  async function executeCodeSample(sample: CodeSample): Promise<void> {
    const responseModify = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'process',
        obj_type: 0,
        logics: [
          { type: 'api_code', err_node_id: '', lang: sample.lang, src: sample.code },
          { to_node_id: final_node_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModify.status).toBe(RESP_STATUS.OK);

    const responseCompile = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPILE,
        obj: OBJ_TYPE.API_CODE,
        node_id: process_node_ID,
        conv_id,
        lang: sample.lang,
        src: sample.code,
      }),
    );
    expect(responseCompile.status).toBe(RESP_STATUS.OK);

    const responseLoad = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LOAD,
        obj: OBJ_TYPE.API_CODE,
        node_id: process_node_ID,
        conv_id,
        lang: sample.lang,
        src: sample.code,
        env: 'production',
      }),
    );
    expect(responseLoad.status).toBe(RESP_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: sample.data,
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    const task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));

    const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask.status).toBe(RESP_STATUS.OK);

    const actualData = responseShowTask.body.ops[0].data;
    Object.keys(sample.expected).forEach(key => {
      if (sample.expected[key] === '__ANY_VALUE__') {
        expect(actualData).toHaveProperty(key);
      } else if (
        Array.isArray(sample.expected[key]) &&
        sample.expected[key].length === 1 &&
        sample.expected[key][0] === '__ANY_VALUE__'
      ) {
        expect(actualData).toHaveProperty(key);
        expect(Array.isArray(actualData[key])).toBe(true);
      } else {
        expect(actualData[key]).toEqual(sample.expected[key]);
      }
    });
  }

  const jsSamples = codeSamples.filter((sample: CodeSample) => sample.lang === 'js');
  const erlSamples = codeSamples.filter((sample: CodeSample) => sample.lang === 'erl');

  describe('JavaScript Code Execution', () => {
    jsSamples.forEach((sample: CodeSample) => {
      test(`should execute ${sample.name}`, async () => {
        await executeCodeSample(sample);
      });
    });
  });

  describe('Erlang Code Execution', () => {
    erlSamples.forEach((sample: CodeSample) => {
      test(`should execute ${sample.name}`, async () => {
        await executeCodeSample(sample);
      });
    });
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
