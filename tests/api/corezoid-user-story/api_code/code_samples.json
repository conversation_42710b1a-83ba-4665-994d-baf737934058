{"code_samples": [{"name": "Simple variable assignment (JavaScript)", "lang": "js", "code": "var b = 123456789; data.a = b;", "data": {}, "expected": {"a": 123456789}}, {"name": "String manipulation (JavaScript)", "lang": "js", "code": "data.result = 'Hello, ' + 'World!';", "data": {}, "expected": {"result": "Hello, <PERSON>!"}}, {"name": "Array operations (JavaScript)", "lang": "js", "data": {}, "code": "var arr = [1, 2, 3, 4, 5]; data.sum = arr.reduce(function(a, b) { return a + b; }, 0);", "expected": {"sum": 15}}, {"name": "Object manipulation (JavaScript)", "lang": "js", "data": {}, "code": "var obj = { name: '<PERSON>', age: 30 }; data.person = obj; data.person.city = 'New York';", "expected": {"person": {"name": "<PERSON>", "age": 30, "city": "New York"}}}, {"name": "Math operations (JavaScript)", "lang": "js", "data": {}, "code": "data.result = Math.pow(2, 8) + Math.sqrt(16);", "expected": {"result": 260}}, {"name": "Conditional logic (JavaScript)", "lang": "js", "data": {}, "code": "var x = 10; if (x > 5) { data.result = 'greater'; } else { data.result = 'less'; }", "expected": {"result": "greater"}}, {"name": "Loop operations (JavaScript)", "lang": "js", "data": {}, "code": "var sum = 0; for (var i = 1; i <= 10; i++) { sum += i; } data.result = sum;", "expected": {"result": 55}}, {"name": "Date manipulation (JavaScript)", "lang": "js", "data": {}, "code": "var date = new Date('2023-01-01'); data.year = date.getFullYear();", "expected": {"year": 2023}}, {"name": "JSON parsing (JavaScript)", "lang": "js", "data": {}, "code": "var jsonStr = '{\"name\":\"<PERSON>\",\"age\":25}'; data.parsed = JSON.parse(jsonStr);", "expected": {"parsed": {"name": "<PERSON>", "age": 25}}}, {"name": "Regular expressions (JavaScript)", "lang": "js", "data": {}, "code": "var str = 'Hello123World'; data.result = str.match(/\\d+/)[0];", "expected": {"result": "123"}}, {"name": "sha1", "lang": "js", "data": {}, "code": "require(\"libs/sha1.js\");\nvar In = data.paramName;\nvar out = CryptoJS.SHA1(In).toString();\ndata.res = out;", "expected": {"res": "da39a3ee5e6b4b0d3255bfef95601890afd80709"}}, {"name": "md5", "lang": "js", "data": {}, "code": "require(\"libs/md5.js\");\nvar In = data.paramName;\nvar out = CryptoJS.MD5(In).toString();\ndata.res = out;", "expected": {"res": "d41d8cd98f00b204e9800998ecf8427e"}}, {"name": "base64", "lang": "js", "data": {}, "code": "require(\"libs/base64.js\");\nvar In = data.paramName;\nvar words = CryptoJS.enc.Utf8.parse(In);\nvar base64 = CryptoJS.enc.Base64.stringify(words);\ndata.res = base64;", "expected": {"res": "dW5kZWZpbmVk"}}, {"name": "libs/base64", "lang": "js", "data": {"paramName": "VGVzdA=="}, "code": "require(\"libs/base64.js\");\nvar In = data.paramName;\nvar base64 = CryptoJS.enc.Base64.parse(In);\nvar textString = CryptoJS.enc.Utf8.stringify(base64);\ndata.res = textString;", "expected": {"paramName": "VGVzdA==", "res": "Test"}}, {"name": "libs/aes_dec", "lang": "js", "data": {"paramName": "U2FsdGVkX19dfE6jLG/8tCdj3WCH0fPsiiOuw14lY8A="}, "code": "require(\"libs/aes.js\");\nvar In = data.paramName;\nvar passphrase = \"Secret\";\nvar dec = CryptoJS.AES.decrypt(In,passphrase).toString(CryptoJS.enc.Utf8);\ndata.res = dec;", "expected": {"paramName": "U2FsdGVkX19dfE6jLG/8tCdj3WCH0fPsiiOuw14lY8A=", "res": "Test"}}, {"name": "libs/aes_encrypted", "lang": "js", "data": {"paramName": "Test"}, "code": "require(\"libs/tripledes.js\");\nvar In = data.paramName;\nvar passphrase = \"Secret\";\nvar DESencrypted = CryptoJS.DES.encrypt(In, passphrase).toString();\ndata.res = DESencrypted;", "expected": {"res": "__ANY_VALUE__"}}, {"name": "libs/tripledes_decrypted", "lang": "js", "data": {"paramName": "U2FsdGVkX18MalzSjcpHv+VKnDPle7OY"}, "code": "require(\"libs/tripledes.js\");\nvar In = data.paramName;\nvar passphrase = \"Secret\";\nvar DESdecrypted = CryptoJS.DES.decrypt(In, passphrase).toString(CryptoJS.enc.Utf8);\ndata.res = DESdecrypted;", "expected": {"paramName": "U2FsdGVkX18MalzSjcpHv+VKnDPle7OY", "res": ""}}, {"name": "libs/tripledes_encrypted", "lang": "js", "data": {}, "code": "require(\"libs/tripledes.js\");\nvar In = data.paramName;\nvar passphrase = \"Secret\";\nvar DES3encrypted = CryptoJS.TripleDES.encrypt(In, passphrase).toString();\ndata.res = DES3encrypted;", "expected": {"res": "__ANY_VALUE__"}}, {"name": "libs/dateutils", "lang": "js", "data": {}, "code": "require(\"libs/dateutils.js\");\nvar dt = data.paramName;\nvar formatedData = fn_convertDate(dt, \"yyyyMMdd\", \"yyyy-MM-dd\");\ndata.res = formatedData;", "expected": {"res": "1970-01-01"}}, {"name": "libs/dateutils_formatDate", "lang": "js", "data": {"paramName": "2015-02-14"}, "code": "require(\"libs/dateutils.js\");\nvar dt = data.paramName;\nvar formatedData = fn_formatDate(dt,\"yyyyMMdd\");\ndata.res = formatedData;", "expected": {"paramName": "2015-02-14", "res": "20150214"}}, {"name": "instanceof_Array", "lang": "js", "data": {"Wr_resp": {"Result": "OK", "customers": [{"id": 4565464554665}], "users": {"param": 4565464554665}, "test": 123}}, "code": "\tvar checkResponse;\n\tfor (var key in data) {\n\t    if (key == 'Wr_resp') {\n\t        data.is_array = data.Wr_resp.customers instanceof Array;\n            data.is_array_test = data.Wr_resp.test instanceof Array;\n\t        data.is_obj = data.Wr_resp.users instanceof Object;\n\t    } else {\n\t        data.isFullResponse = 0;\n\t    }\n\t}", "expected": {"Wr_resp": {"Result": "OK", "customers": [{"id": 4565464554665}], "users": {"param": 4565464554665}, "test": 123}, "is_array": true, "is_array_test": false, "is_obj": true}}, {"name": "pumb_<PERSON>rray", "lang": "js", "data": {"ops": [{"list": [{"obj_id": "5d9b11c2094bab6cc9003d7d", "title": "Final", "count": 0}]}], "nodes": ["5d89fa0456167c6ecc0019b5"], "item": []}, "code": "var ops1 = data.ops[0].list;for (var i = 0; i < ops1.length; i++) {\n   for (var j = 0; j < ops1.length; j++) {\n       if (data.nodes[j] == ops1[i].obj_id){\n           var obj = {};\n           obj.title = ops1[i].title;\n           obj.item_id = ops1[i].obj_id;\n           obj.count = ops1[i].count;\n           data.item.push(obj);\n       }\n   }\n}data.item.sort(compare);function compare( a, b ) {\n if ( a.count < b.count ){\n   return 1;\n }\n if ( a.count > b.count ){\n   return -1;\n }\n return 0;\n}", "expected": {"item": [], "nodes": ["5d89fa0456167c6ecc0019b5"], "ops": [{"list": [{"obj_id": "5d9b11c2094bab6cc9003d7d", "title": "Final", "count": 0}]}]}}, {"name": "replaceAll", "lang": "js", "data": {}, "code": "const paragraph = \"I think <PERSON>'s dog is cuter than your dog!\";\ndata.a = paragraph.replaceAll('dog', 'monkey');", "expected": {"a": "I think <PERSON>'s monkey is cuter than your monkey!"}}, {"name": "instanceof_Array/Object", "lang": "js", "data": {"Wr_resp": {"customers": [{"id": 4565464554665}], "users": {"param": 4565464554665}}}, "code": "data.is_array = data.Wr_resp.customers instanceof Array;  \ndata.is_obj = data.Wr_resp.users instanceof Object;", "expected": {"Wr_resp": {"customers": [{"id": 4565464554665}], "users": {"param": 4565464554665}}, "is_array": true, "is_obj": true}}, {"name": "break", "lang": "js", "data": {}, "code": "let i = 0;while (i < 6) {if (i === 3) {break;}i = i + 1;\n}data.a = i;\n", "expected": {"a": 3}}, {"name": "libs/moment-timezone", "lang": "js", "data": {"kardcode": "test"}, "code": "require(\"libs/moment-timezone.js\");\ndata.currentTimeUnix = moment().unix();\n\ndata.qr = \"1\" + data.kardcode.toString() +data.currentTimeUnix;", "expected": {"currentTimeUnix": "__ANY_VALUE__", "kardcode": "test", "qr": "__ANY_VALUE__"}}, {"name": "libs/lodash", "lang": "js", "data": {}, "code": "require(\"libs/lodash.js\")\ndata.a = _.chunk(['a', 'b', 'c', 'd'], 2);", "expected": {"a": [["a", "b"], ["c", "d"]]}}, {"name": "fn_TimeZone_dateNow", "lang": "js", "data": {"PartnerName": "Nest Test", "PhoneNumber": "0679795895"}, "code": "var TimeZone = data.TimeZone;\nvar dateNow = new Date();\nvar dayNow = dateNow.getDay();\nvar hoursNow = dateNow.getHours() + data.TimeZone;\nvar StartWorkDay = 9,\n    EndWorkDay = 21;\nif (StartWorkDay <= hoursNow && hoursNow < EndWorkDay) {\n    data.WorkTime = true;\n    data.hoursNow = hoursNow;\n} else {\n    data.WorkTime = false;\n    data.hoursNow = hoursNow;\n    data.Start = StartTime(dateNow, TimeZone);\n\n}\n////////////////////////////////////////////////////////////////\nvar PhoneNumber = data.PhoneNumber.replace(/\\D+/g, '');\ndata.PhoneNumber = PhoneNumber.substr(PhoneNumber.length - 10);\n/////////////////////////////////////////////////////////////////////////////////////\nvar Code = data.PhoneNumber.substring(0, 3);\ndata.Code = Code;\nvar OperatorCode = [{\n    Code: \"068\"\n}, {\n    Code: \"067\"\n}, {\n    Code: \"096\"\n}, {\n    Code: \"097\"\n}, {\n    Code: \"098\"\n}, {\n    Code: \"050\"\n}, {\n    Code: \"066\"\n}, {\n    Code: \"095\"\n}, {\n    Code: \"099\"\n}, {\n    Code: \"063\"\n}, {\n    Code: \"073\"\n}, {\n    Code: \"093\"\n}, {\n    Code: \"091\"\n}, {\n    Code: \"092\"\n}, {\n    Code: \"094\"\n}];\nfor (var i = 0, l = OperatorCode.length; i < l; i++) {\n    if (OperatorCode[i].Code == Code) {\n        ActualPhoneNumber = true;\n        data.ActualPhoneNumber = ActualPhoneNumber;\n        break;\n    } else {\n        ActualPhoneNumber = false;\n        data.ActualPhoneNumber = ActualPhoneNumber;\n    }\n}\n/////////////////////////////////////////////////////////////////////////\ndata.keyboard = JSON.stringify({\n    inline_keyboard: [\n        [{\n            text: \"Контакт не потрібен\",\n            callback_data: \"Контакт не потрібен\"\n        }],\n        [{\n            text: 'Контакт був, все ок',\n            callback_data: \"Контакт був, все ок\"\n\n        }],\n        [{\n            text: 'Щось пішло не так',\n            callback_data: \"Щось пішло не так\"\n        }]\n    ]\n});\n/////////////////////////////////////////////////////////////////////////\nfunction StartTime(dateNow, TimeZone) {\n    var DateToAnalyse = new Date(dateNow);\n    var hourNow = DateToAnalyse.getHours() + TimeZone;\n    var NewDate = new Date(dateNow);\n    if (hourNow > 9) {\n        NewDate.setDate(DateToAnalyse.getDate() + 1);\n    }\n\n    function NewDateToStart(NewDate) {\n        var a = new Date(NewDate);\n\n        var year = a.getFullYear();\n        if (year < 10) year = '0' + year;\n\n        var month = a.getMonth() + 1;\n        if (month < 10) month = '0' + month;\n\n        var date = a.getDate();\n        if (date < 10) date = '0' + date;\n\n        var time = year + '-' + month + '-' + date + \"T\" + \"09:00:01.048Z\";\n        return time;\n    }\n    var StartTask = NewDateToStart(NewDate);\n    var UnixStart = Math.round(new Date(StartTask).getTime() / 1000 - (3600 * TimeZone));\n    return UnixStart;\n}", "expected": {"ActualPhoneNumber": true, "Code": "067", "PartnerName": "Nest Test", "PhoneNumber": "0679795895", "Start": null, "WorkTime": false, "hoursNow": null, "keyboard": "{\"inline_keyboard\":[[{\"text\":\"Контакт не потрібен\",\"callback_data\":\"Контакт не потрібен\"}],[{\"text\":\"Контакт був, все ок\",\"callback_data\":\"Контакт був, все ок\"}],[{\"text\":\"Щось пішло не так\",\"callback_data\":\"Щось пішло не так\"}]]}"}}, {"name": "for_heavy", "lang": "js", "data": {"oktellxmlmapper": {"data": {"@name": "result", "@count": "1", "property_set": {"@name": "execsvcscript"}}}}, "code": "for (var key in data.body) {\n  data.status=data.body[key].status;\n}\n\nfor (var key in data.body) {\n data.operatorID=data.body[key].operatorID;\n    }\n\nfor (var key in data.body) {\n  data.tovar=data.body[key].goods;\n}\n\nfor (var key in data.body) {\n  data.comment=data.body[key].comment;\n}\n\nfor (var key in data.body) {\n  data.adress=data.body[key].region+data.body[key].city+' '+data.body[key].address+' '+data.body[key].house+' '+data.body[key].flat;\n}\nfor (var key in data.body) {\n  data.name=data.body[key].fio;\n}\n\nfor (var key in data.body) {\n  data.postIndex=data.body[key].postIndex;\n}", "expected": {"oktellxmlmapper": {"data": {"@name": "result", "@count": "1", "property_set": {"@name": "execsvcscript"}}}}}, {"name": "heavy_code", "lang": "js", "data": {"borrower": {"id": 122219, "first_name": "test", "last_name": "test", "middle_name": "test"}, "borrowerCross": {"id": 139394, "first_name": "test", "last_name": "test", "middle_name": "test"}}, "code": "data.jaroWinklerNameCoefficient = distance(\n    \"\" + data.borrowerCross.first_name + data.borrowerCross.last_name + data.borrowerCross.middle_name,\n    \"\" + data.borrower.first_name + data.borrower.last_name + data.borrower.middle_name, {\n        caseSensitive: false,\n        onlyCyrillic: true\n    }\n);\n\nfunction extend(a, b) {\n    for (var property in b) {\n        if (b.hasOwnProperty(property)) {\n            a[property] = b[property];\n        }\n    }\n\n    return a;\n}\n\nfunction distance(s1, s2, options) {\n    var m = 0;\n    var defaults = {\n        caseSensitive: true,\n        onlyCyrillic: false,\n    };\n    var settings = extend(defaults, options);\n    var i;\n    var j;\n\n    // Exit early if either are empty.\n    if (s1.length === 0 || s2.length === 0) {\n        return 0;\n    }\n\n    // Convert to upper if case-sensitive is false.\n    if (!settings.caseSensitive) {\n        s1 = s1.toUpperCase();\n        s2 = s2.toUpperCase();\n    }\n\n    // Convert to upper if case-sensitive is false.\n    if (settings.onlyCyrillic) {\n        s1 = s1.replace(/[^а-яА-ЯёЁґєіїҐЄІЇ]/, '');\n        s2 = s2.replace(/[^а-яА-ЯёЁґєіїҐЄІЇ]/, '');\n    }\n\n\n    // Exit early if they're an exact match.\n    if (s1 === s2) {\n        return 1;\n    }\n\n    var range = (Math.floor(Math.max(s1.length, s2.length) / 2)) - 1;\n    var s1Matches = new Array(s1.length);\n    var s2Matches = new Array(s2.length);\n\n    for (i = 0; i < s1.length; i++) {\n        var low = (i >= range) ? i - range : 0;\n        var high = (i + range <= (s2.length - 1)) ? (i + range) : (s2.length - 1);\n\n        for (j = low; j <= high; j++) {\n            if (s1Matches[i] !== true && s2Matches[j] !== true && s1[i] === s2[j]) {\n                ++m;\n                s1Matches[i] = s2Matches[j] = true;\n                break;\n            }\n        }\n    }\n\n    // Exit early if no matches were found.\n    if (m === 0) {\n        return 0;\n    }\n\n    // Count the transpositions.\n    var k = 0;\n    var numTrans = 0;\n\n    for (i = 0; i < s1.length; i++) {\n        if (s1Matches[i] === true) {\n            for (j = k; j < s2.length; j++) {\n                if (s2Matches[j] === true) {\n                    k = j + 1;\n                    break;\n                }\n            }\n\n            if (s1[i] !== s2[j]) {\n                ++numTrans;\n            }\n        }\n    }\n\n    var weight = (m / s1.length + m / s2.length + (m - (numTrans / 2)) / m) / 3;\n    var l = 0;\n    var p = 0.1;\n\n    if (weight > 0.7) {\n        while (s1[l] === s2[l] && l < 4) {\n            ++l;\n        }\n\n        weight = weight + l * p * (1 - weight);\n    }\n\n    return weight;\n}", "expected": {"borrower": {"id": 122219, "first_name": "test", "last_name": "test", "middle_name": "test"}, "borrowerCross": {"id": 139394, "first_name": "test", "last_name": "test", "middle_name": "test"}, "jaroWinklerNameCoefficient": 1}}, {"name": "data=null_negative", "lang": "js", "data": {}, "code": "data=null", "expected": {"__conveyor_code_return_description__": "<<\"null\">>", "__conveyor_code_return_type_error__": "software", "__conveyor_code_return_type_tag__": "code_return_format_error"}}, {"name": "error_heavy_code_negative", "lang": "js", "data": {}, "code": "for (let i = 0; i < 2000000; i++) {\n    data[i] = new Array(1);\n}\n\nlet a = data[10000];\ndata = {};", "expected": {}}, {"name": "cycle_negative", "lang": "js", "data": {}, "code": "{   \n    var Y = \"A\";\n    for (Y = 0; Y < Infinity; Y++)\n        if (Y.toString(14) == Y)\n            out = Y.toString(10);\n }\n", "expected": {"__conveyor_code_return_description__": "__ANY_VALUE__", "__conveyor_code_return_type_error__": "__ANY_VALUE__", "__conveyor_code_return_type_tag__": "__ANY_VALUE__"}}, {"name": "while(true)_negative", "lang": "js", "data": {}, "code": "const a = []; while(true){ a.push(\"aaaaaaaaaaaaaaaaaaaaaa\") }", "expected": {"__conveyor_code_return_description__": "timeout for executing code", "__conveyor_code_return_type_error__": "hardware", "__conveyor_code_return_type_tag__": "code_timeout"}}, {"name": "throw_newError_negative", "lang": "js", "data": {}, "code": "throw new Error();", "expected": {"__conveyor_code_return_description__": "<<\"Error\">>", "__conveyor_code_return_type_error__": "software", "__conveyor_code_return_type_tag__": "code_executing_error"}}, {"name": "throw_Error_negative", "lang": "js", "data": {}, "code": "throw \"Hello world\";", "expected": {"__conveyor_code_return_description__": "<<\"Hello world\">>", "__conveyor_code_return_type_error__": "software", "__conveyor_code_return_type_tag__": "code_executing_error"}}, {"name": "Simple variable assignment (Erlang)", "lang": "erl", "data": {}, "code": "-module(node).\n-export([main/1]).\nmain(Data)->[{<<\"result\">>,123456789}|Data].", "expected": {"result": 123456789}}, {"name": "String manipulation (<PERSON><PERSON><PERSON>)", "lang": "erl", "data": {}, "code": "-module(greeting).\n-export([main/1]).\n\nmain(_Data) ->\n    Result = string:concat(\"Hello, \", \"World!\"),\n    [{<<\"result\">>, list_to_binary(Result)}].", "expected": {"result": "Hello, <PERSON>!"}}, {"name": "Conditional logic (Erlang)", "lang": "erl", "data": {}, "code": "-module(comparison).\n-export([main/1]).\n\nmain(Data) ->\n    X = 10,\n    Result = if \n        X > 5 -> <<\"greater\">>;\n        true -> <<\"less\">>\n    end,\n    [{<<\"result\">>, Result} | Data].", "expected": {"result": "greater"}}, {"name": "List comprehension (Erlang)", "lang": "erl", "data": {}, "code": "-module(sum_calc).\n-export([main/1]).\n\nmain(Data) ->\n    Sum = lists:sum([X || X <- lists:seq(1, 10)]),\n    [{<<\"result\">>, Sum} | Data].", "expected": {"result": 55}}, {"name": "Date manipulation (Erlang)", "lang": "erl", "data": {}, "code": "-module(specific_year).\n-export([main/1]).\n\nmain(Data) ->\n    Year = 2023,\n    [{<<\"result\">>, Year} | Data].", "expected": {"result": 2023}}, {"name": "Map manipulation (Erlang)", "lang": "erl", "data": {}, "code": "-module(person_info).\n-export([main/1]).\n\nmain(_Data) ->\n\n    PersonInfo = [\n        {<<\"name\">>, <<\"John\">>},\n        {<<\"age\">>, 30},\n        {<<\"city\">>, <<\"New York\">>}\n    ],\n    \n    [{<<\"result\">>, {PersonInfo}}].", "expected": {"result": {"name": "<PERSON>", "age": 30, "city": "New York"}}}, {"name": "base64 manipulation (Erlang)", "lang": "erl", "data": {}, "code": "-module(node).\n-export([main/1]).\nmain(Data)->[{<<\"session_salt\">>,base64:encode(crypto:strong_rand_bytes(14))},{<<\"encryption_code\">>,[rand:uniform(10)||_<-lists:seq(1,4)]}|Data].", "expected": {"encryption_code": ["__ANY_VALUE__"], "session_salt": "__ANY_VALUE__"}}, {"name": "Math operations", "code": "-module(node).\n-export([main/1]).\nmain(Data) ->\n    Power = 2 * 2 * 2 * 2 * 2 * 2 * 2 * 2,\n    SquareRoot = 4.0, \n    Result = Power + SquareRoot, \n    [{<<\"result\">>, Result} | Data].", "expected": {"result": 260.0}}, {"name": "Regular expressions(Erlang)", "lang": "erl", "data": {}, "code": "-module(node).\n-export([main/1]).\n\nmain(Data) ->\n    % Строка для поиска\n    Str = \"Hello123World\",\n    \n    % Используем регулярное выражение для поиска цифр\n    {match, [Result]} = re:run(Str, \"\\\\d+\", [{capture, first, list}]),\n    \n    % Используем Result для одного из значений (преобразуем в число)\n    ResultInt = list_to_integer(Result),\n    \n    % Создаем два ключа в ответе, используя Result\n    [{<<\"encryption_code\">>, list_to_binary(\"Alice\")},\n     {<<\"session_salt\">>, ResultInt} | Data].", "expected": {"encryption_code": "<PERSON>", "session_salt": 123}}, {"name": "JSON parsing (Erlang)", "lang": "erl", "data": {}, "code": "-module(node).\n-export([main/1]).\n\nmain(Data) ->\n    Name = <<\"Alice\">>,\n    Age = 25,\n    [{<<\"encryption_code\">>, Name},\n     {<<\"session_salt\">>, Age} | Data].", "expected": {"encryption_code": "<PERSON>", "session_salt": 25}}]}