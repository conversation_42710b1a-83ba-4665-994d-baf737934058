import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';
import { createAuthUser, Method } from '../../../../utils/request';

import { ApiUserClient } from '../../../../application/api/ApiUserClient';
import { User } from '../../../../infrastructure/model/User';

describe('Group (positive)', () => {
  let token: string;
  let userToken: any;
  let hostSS: string;
  let api: ApiKeyClient;
  let company_id: any;
  let obj_idKey: any;
  let newKeyObjId: string | number;
  let newGroup: string | number;
  let conv: string | number;
  let folder: string | number;
  let dashboard: string | number;
  let convDel: string | number;
  let folderDel: string | number;
  let dashboardDel: string | number;
  let newApi: ApiKeyClient;
  let ApiKey2: string | number;
  let newAlias: string | number;
  let newProject: string | number;
  let newProjectDel: string | number;
  let newStage: string | number;
  let newStageDel: string | number;
  let convProject: string | number;
  let aliasProject: string | number;
  let convProjectDel: string | number;
  let folderProject: string | number;
  let folderProjectDel: string | number;
  let dashboardProject: string | number;
  let dashboardProjectDel: string | number;
  let instanceProject: string | number;
  let instanceProjectDel: string | number;
  let newVersion: string | number;
  let newVersionDel: string | number;
  let instance: string | number;
  let instanceDel: string | number;

  let apiUser: ApiUserClient;
  let user0: User;

  beforeAll(async () => {
    await ConfigurationManager.getConfiguration().initialize();
    const config = ConfigurationManager.getConfiguration();
    hostSS = config.getSSUrl();
    token = await application.createToken(0);
    userToken = createAuthUser(token, 'token');
    user0 = await application.getAuthorizedUser();
    apiUser = await application.getApiUserClient(user0);

    const createUserOwnerResponse = await apiUser.requestWithToken(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        title: 'APInewOwner',
        logins: [{ type: 'api' }],
      }),
    );
    expect(createUserOwnerResponse.status).toBe(200);
    expect(createUserOwnerResponse.body.ops[0].proc).toBe('ok');
    const userOw = createUserOwnerResponse.body;

    const apikey = {
      key: `${userOw.ops[0].users[0].logins[0].obj_id}`,
      secret: userOw.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${userOw.ops[0].users[0].obj_id}`,
    } as ApiKey;
    api = application.getApiKeyClient(apikey);
    obj_idKey = +apikey.id;

    const responseCompany = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,
        name: `Company_${Date.now()}`,
        site: 'https://www.corezoid.com',
        description: 'test',
      }),
    );
    company_id = responseCompany.body.ops[0].obj_id;

    const createUserResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        logins: [{ type: 'api' }],
        title: 'APIkeyUser',
        company_id,
      }),
    );
    const user = createUserResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newApi = application.getApiKeyClient(newApiKey);
    ApiKey2 = +newApiKey.id;

    const responseGroup1 = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        title: `Group_${Date.now()}`,
        obj_type: 'admins',
      }),
    );
    newGroup = responseGroup1.body.ops[0].obj_id;

    const responseKey1 = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: `key_${Date.now()}`,
        logins: [{ type: 'api' }],
      }),
    );
    expect(responseKey1.status).toBe(200);
    expect(responseKey1.body.ops[0].proc).toEqual('ok');
    newKeyObjId = responseKey1.body.ops[0].users[0].obj_id;

    const responseConv = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: 0,
        conv_type: 'process',
      }),
    );
    conv = responseConv.body.ops[0].obj_id;

    const responseConvDel = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: 0,
        conv_type: 'process',
      }),
    );
    convDel = responseConvDel.body.ops[0].obj_id;

    const responseAlias = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: `alias-${Date.now()}`,
        description: `aliasDesk-${Date.now()}`,
        title: `aliasTitle-${Date.now()}`,
        project_id: 0,
      }),
    );
    expect(responseAlias.status).toBe(200);
    newAlias = responseAlias.body.ops[0].obj_id;

    const responseFolder = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        title: `${OBJ_TYPE.FOLDER}_${Date.now()}`,
        folder_id: 0,
      }),
    );
    folder = responseFolder.body.ops[0].obj_id;

    const responseFolderDel = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        title: `${OBJ_TYPE.FOLDER}_${Date.now()}`,
        folder_id: 0,
      }),
    );
    folderDel = responseFolderDel.body.ops[0].obj_id;

    const responseDashboard = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        title: `${OBJ_TYPE.DASHBOARD}_${Date.now()}`,
        folder_id: 0,
      }),
    );
    dashboard = responseDashboard.body.ops[0].obj_id;

    const responseDashboardDel = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        title: `${OBJ_TYPE.DASHBOARD}_${Date.now()}`,
        folder_id: 0,
      }),
    );
    dashboardDel = responseDashboardDel.body.ops[0].obj_id;

    const responseInst = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,
        instance_type: `db_call`,
        folder_id: 0,
        title: `Instance_${Date.now()}`,
        data: {
          driver: `postgres`,
          host: `COR-6938.middleware.loc`,
          port: `5432`,
          username: `postgres`,
          password: `pass`,
          ssl: false,
          database: `dbcall`,
          timeoutMs: 30000,
        },
      }),
    );
    expect(responseInst.status).toBe(200);
    expect(responseInst.body.ops[0].obj).toBe('instance');
    instance = responseInst.body.ops[0].obj_id;

    const responseInstDel = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,
        instance_type: `db_call`,
        folder_id: 0,
        title: `Instance_${Date.now()}`,
        data: {
          driver: `postgres`,
          host: `COR-6938.middleware.loc`,
          port: `5432`,
          username: `postgres`,
          password: `pass`,
          ssl: false,
          database: `dbcall`,
          timeoutMs: 30000,
        },
      }),
    );
    expect(responseInstDel.status).toBe(200);
    expect(responseInstDel.body.ops[0].obj).toBe('instance');
    instanceDel = responseInstDel.body.ops[0].obj_id;

    const responseProject = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: `Project_${Date.now()}`,
        short_name: `project-${Date.now()}`,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        title: `Stage_${Date.now()}`,
        short_name: `stage-${Date.now()}`,
        description: 'test',
        project_id: newProject,
      }),
    );
    expect(responseStage.status).toBe(200);
    expect(responseStage.body.ops[0].obj).toBe('stage');
    newStage = responseStage.body.ops[0].obj_id;

    const responseConvProject = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: newStage,
        conv_type: 'process',
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    convProject = responseConvProject.body.ops[0].obj_id;

    const responseFolderProject = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        title: `${OBJ_TYPE.FOLDER}_${Date.now()}`,
        folder_id: newStage,
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    folderProject = responseFolderProject.body.ops[0].obj_id;

    const responseDashboardProject = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        title: `${OBJ_TYPE.DASHBOARD}_${Date.now()}`,
        folder_id: newStage,
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    dashboardProject = responseDashboardProject.body.ops[0].obj_id;

    const responseInstProject = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,
        instance_type: `db_call`,
        folder_id: newStage,
        stage_id: newStage,
        project_id: newProject,
        title: `Instance_${Date.now()}`,
        data: {
          driver: `postgres`,
          host: `COR-6938.middleware.loc`,
          port: `5432`,
          username: `postgres`,
          password: `pass`,
          ssl: false,
          database: `dbcall`,
          timeoutMs: 30000,
        },
      }),
    );
    expect(responseInstProject.status).toBe(200);
    expect(responseInstProject.body.ops[0].obj).toBe('instance');
    instanceProject = responseInstProject.body.ops[0].obj_id;

    const responseFolderProjectDel = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        title: `${OBJ_TYPE.FOLDER}_${Date.now()}`,
        folder_id: newStage,
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    folderProjectDel = responseFolderProjectDel.body.ops[0].obj_id;

    const responseDashboardProjectDel = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        title: `${OBJ_TYPE.DASHBOARD}_${Date.now()}`,
        folder_id: newStage,
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    dashboardProjectDel = responseDashboardProjectDel.body.ops[0].obj_id;

    const responseInstProjectDel = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        company_id,
        instance_type: `db_call`,
        folder_id: newStage,
        stage_id: newStage,
        project_id: newProject,
        title: `Instance_${Date.now()}`,
        data: {
          driver: `postgres`,
          host: `COR-6938.middleware.loc`,
          port: `5432`,
          username: `postgres`,
          password: `pass`,
          ssl: false,
          database: `dbcall`,
          timeoutMs: 30000,
        },
      }),
    );
    expect(responseInstProjectDel.status).toBe(200);
    expect(responseInstProjectDel.body.ops[0].obj).toBe('instance');
    instanceProjectDel = responseInstProjectDel.body.ops[0].obj_id;

    const responseAliasProject = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: `alias-${Date.now()}`,
        description: `aliasDesk-${Date.now()}`,
        title: `aliasTitle-${Date.now()}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseAliasProject.status).toBe(200);
    aliasProject = responseAliasProject.body.ops[0].obj_id;

    const responseVersion = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,
        vsn: `${Date.now()}`,
        stage_id: newStage,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    expect(responseVersion.status).toBe(200);
    expect(responseVersion.body.ops[0].obj).toBe('version');
    newVersion = responseVersion.body.ops[0].obj_id;

    const responseVersionDel = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,
        vsn: `${Date.now()}`,
        stage_id: newStage,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    expect(responseVersionDel.status).toBe(200);
    expect(responseVersionDel.body.ops[0].obj).toBe('version');
    newVersionDel = responseVersionDel.body.ops[0].obj_id;

    const responseConvProjectDel = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: newStage,
        conv_type: 'process',
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    convProjectDel = responseConvProjectDel.body.ops[0].obj_id;

    const responseStageDel = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        title: `StageDel_${Date.now()}`,
        short_name: `stagedel-${Date.now()}`,
        description: 'testdel',
        project_id: newProject,
      }),
    );
    expect(responseStageDel.status).toBe(200);
    expect(responseStageDel.body.ops[0].obj).toBe('stage');
    newStageDel = responseStageDel.body.ops[0].obj_id;

    const responseProjectDel = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: `ProjectDel_${Date.now()}`,
        short_name: `project-${Date.now()}`,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProjectDel = responseProjectDel.body.ops[0].obj_id;

    await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: convDel,
        company_id,
      }),
    );
    await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folderDel,
        company_id,
      }),
    );
    await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: dashboardDel,
        company_id,
      }),
    );
    await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProjectDel,
        company_id,
      }),
    );
    await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStageDel,
        company_id,
      }),
    );
    await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: convProjectDel,
        company_id,
      }),
    );
    await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folderProjectDel,
        company_id,
      }),
    );
    await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: dashboardProjectDel,
        company_id,
      }),
    );
    await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: instanceProjectDel,
        company_id,
      }),
    );
    await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersionDel,
        company_id,
        project_id: newProject,
      }),
    );
    await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: instanceDel,
        company_id,
      }),
    );
  });

  test('should modify owner after delete user', async () => {
    const responseDel = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
        company_id,
      }),
    );
    expect(responseDel.status).toBe(200);
    expect(responseDel.body.request_proc).toBe('ok');
    expect(responseDel.body.ops[0].proc).toBe('ok');
    expect(responseDel.body.ops[0].obj_id).toBe(ApiKey2);

    await new Promise(r => setTimeout(r, 110000));

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.request_proc).toBe('ok');
    expect(responseList.body.ops[0].proc).toBe('ok');
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === conv)?.owner_id).toEqual(
      obj_idKey,
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === folder)?.owner_id).toEqual(
      obj_idKey,
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === dashboard)?.owner_id).toEqual(
      obj_idKey,
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === instance)?.owner_id).toEqual(
      obj_idKey,
    );

    const responseListDel = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
        filter: 'deleted',
      }),
    );
    expect(responseListDel.status).toBe(200);
    expect(responseListDel.body.request_proc).toBe('ok');
    expect(responseListDel.body.ops[0].proc).toBe('ok');
    expect((responseListDel.body.ops[0].list as Array<any>).find(item => item.obj_id === convDel)?.owner_id).toEqual(
      obj_idKey,
    );
    expect((responseListDel.body.ops[0].list as Array<any>).find(item => item.obj_id === folderDel)?.owner_id).toEqual(
      obj_idKey,
    );
    expect(
      (responseListDel.body.ops[0].list as Array<any>).find(item => item.obj_id === dashboardDel)?.owner_id,
    ).toEqual(obj_idKey);
    expect(
      (responseListDel.body.ops[0].list as Array<any>).find(item => item.obj_id === newProjectDel)?.owner_id,
    ).toEqual(obj_idKey);
    expect(
      (responseListDel.body.ops[0].list as Array<any>).find(item => item.obj_id === newStageDel)?.owner_id,
    ).toEqual(obj_idKey);
    expect(
      (responseListDel.body.ops[0].list as Array<any>).find(item => item.obj_id === convProjectDel)?.owner_id,
    ).toEqual(obj_idKey);
    expect(
      (responseListDel.body.ops[0].list as Array<any>).find(item => item.obj_id === instanceDel)?.owner_id,
    ).toEqual(obj_idKey);
    expect(
      (responseListDel.body.ops[0].list as Array<any>).find(item => item.obj_id === folderProjectDel)?.owner_id,
    ).toEqual(obj_idKey);
    expect(
      (responseListDel.body.ops[0].list as Array<any>).find(item => item.obj_id === dashboardProjectDel)?.owner_id,
    ).toEqual(obj_idKey);
    expect(
      (responseListDel.body.ops[0].list as Array<any>).find(item => item.obj_id === instanceProjectDel)?.owner_id,
    ).toEqual(obj_idKey);

    const responseListDelVersion = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.VERSIONS,
        company_id,
        filter: 'deleted',
      }),
    );
    expect(responseListDelVersion.status).toBe(200);
    expect(responseListDelVersion.body.request_proc).toBe('ok');
    expect(responseListDelVersion.body.ops[0].proc).toBe('ok');
    expect(
      (responseListDelVersion.body.ops[0].list as Array<any>).find(item => item.obj_id === newVersionDel)?.owner_id,
    ).toEqual(obj_idKey);

    const responseListProjects = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECTS,
        company_id,
        obj_id: 0,
      }),
    );
    expect(responseListProjects.status).toBe(200);
    expect(responseListProjects.body.request_proc).toBe('ok');
    expect(responseListProjects.body.ops[0].proc).toBe('ok');
    expect(
      (responseListProjects.body.ops[0].list as Array<any>).find(item => item.obj_id === newProject)?.owner_id,
    ).toEqual(obj_idKey);

    const responseListProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        obj_id: newProject,
      }),
    );
    expect(responseListProject.status).toBe(200);
    expect(responseListProject.body.request_proc).toBe('ok');
    expect(responseListProject.body.ops[0].proc).toBe('ok');
    expect(
      (responseListProject.body.ops[0].list as Array<any>).find(item => item.obj_id === newStage)?.owner_id,
    ).toEqual(obj_idKey);

    const responseListStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newStage,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseListStage.status).toBe(200);
    expect(responseListStage.body.request_proc).toBe('ok');
    expect(responseListStage.body.ops[0].proc).toBe('ok');
    expect(
      (responseListStage.body.ops[0].list as Array<any>).find(item => item.obj_id === convProject)?.owner_id,
    ).toEqual(obj_idKey);
    expect(
      (responseListStage.body.ops[0].list as Array<any>).find(item => item.obj_id === dashboardProject)?.owner_id,
    ).toEqual(obj_idKey);
    expect(
      (responseListStage.body.ops[0].list as Array<any>).find(item => item.obj_id === instanceProject)?.owner_id,
    ).toEqual(obj_idKey);
    expect(
      (responseListStage.body.ops[0].list as Array<any>).find(item => item.obj_id === folderProject)?.owner_id,
    ).toEqual(obj_idKey);

    const responseListVersions = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.VERSIONS,
        company_id,
        obj_id: newVersion,
        project_id: newProject,
      }),
    );
    expect(responseListVersions.status).toBe(200);
    expect(responseListVersions.body.request_proc).toBe('ok');
    expect(responseListVersions.body.ops[0].proc).toBe('ok');
    expect(
      (responseListVersions.body.ops[0].list as Array<any>).find(item => item.obj_id === newVersion)?.owner_id,
    ).toEqual(obj_idKey);

    const responseListUser = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        filter: 'api_key',
      }),
    );
    expect(responseListUser.status).toBe(200);
    expect(responseListUser.body.request_proc).toBe('ok');
    expect(responseListUser.body.ops[0].proc).toBe('ok');
    expect(
      (responseListUser.body.ops[0].list as Array<any>).find(item => item.obj_id === newKeyObjId)?.owner_id,
    ).toEqual(obj_idKey);

    const responseListGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        filter: 'group',
      }),
    );
    expect(responseListGroup.status).toBe(200);
    expect(responseListGroup.body.request_proc).toBe('ok');
    expect(responseListGroup.body.ops[0].proc).toBe('ok');
    expect((responseListGroup.body.ops[0].list as Array<any>).find(item => item.obj_id === newGroup)?.owner_id).toEqual(
      obj_idKey,
    );

    const responseListAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        sort: 'title',
        order: 'desc',
      }),
    );
    expect(responseListAlias.status).toBe(200);
    expect(responseListAlias.body.request_proc).toBe('ok');
    expect(responseListAlias.body.ops[0].proc).toBe('ok');
    expect((responseListAlias.body.ops[0].list as Array<any>).find(item => item.obj_id === newAlias)?.owner_id).toEqual(
      obj_idKey,
    );

    const responseListAliasProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        sort: 'title',
        order: 'desc',
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    expect(responseListAliasProject.status).toBe(200);
    expect(responseListAliasProject.body.request_proc).toBe('ok');
    expect(responseListAliasProject.body.ops[0].proc).toBe('ok');
    expect(
      (responseListAliasProject.body.ops[0].list as Array<any>).find(item => item.obj_id === aliasProject)?.owner_id,
    ).toEqual(obj_idKey);
  });

  afterAll(async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        obj_id: newGroup,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: newKeyObjId,
        company_id,
      }),
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
        company_id,
      }),
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv,
        company_id,
      }),
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folder,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: dashboard,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.CONV,
        obj_id: conv,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: instance,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: instance,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProjectDel,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folder,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: dashboard,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.CONV,
        obj_id: convDel,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folderDel,
        company_id,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: dashboardDel,
        company_id,
      }),
    );

    const response = await userToken.request({
      method: Method.DELETE,
      url: `${hostSS}face/api/1/workspaces/${company_id}`,
    });
    expect(response.status).toBe(200);
  });
});
