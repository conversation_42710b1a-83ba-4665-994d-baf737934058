import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';

describe('Group (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let obj_idKey: any;
  let newGroup: string | number;
  let conv: string | number;
  let folder: string | number;
  let dashboard: string | number;
  let convDel: string | number;
  let folderDel: string | number;
  let dashboardDel: string | number;
  let ApiKey2: string | number;
  let instance: string | number;
  let instanceDel: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    obj_idKey = +apikey.id;

    const createUserResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        logins: [{ type: 'api' }],
        title: 'API',
      }),
    );
    const user = createUserResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    const newApi = application.getApiKeyClient(newApiKey);
    ApiKey2 = +newApiKey.id;

    const responseGroup = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        title: `Group_${Date.now()}`,
        obj_type: 'admins',
      }),
    );
    newGroup = responseGroup.body.ops[0].obj_id;

    const responseConv = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: 0,
        conv_type: 'process',
      }),
    );
    conv = responseConv.body.ops[0].obj_id;

    const responseConvDel = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        title: `${OBJ_TYPE.CONV}_${Date.now()}`,
        folder_id: 0,
        conv_type: 'process',
      }),
    );
    convDel = responseConvDel.body.ops[0].obj_id;

    const responseFolder = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        title: `${OBJ_TYPE.FOLDER}_${Date.now()}`,
        folder_id: 0,
      }),
    );
    folder = responseFolder.body.ops[0].obj_id;

    const responseFolderDel = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        title: `${OBJ_TYPE.FOLDER}_${Date.now()}`,
        folder_id: 0,
      }),
    );
    folderDel = responseFolderDel.body.ops[0].obj_id;

    const responseDashboard = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        title: `${OBJ_TYPE.DASHBOARD}_${Date.now()}`,
        folder_id: 0,
      }),
    );
    dashboard = responseDashboard.body.ops[0].obj_id;

    const responseDashboardDel = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        title: `${OBJ_TYPE.DASHBOARD}_${Date.now()}`,
        folder_id: 0,
      }),
    );
    dashboardDel = responseDashboardDel.body.ops[0].obj_id;

    const responseInst = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        instance_type: `db_call`,
        folder_id: 0,
        title: `Instance_${Date.now()}`,
        data: {
          driver: `postgres`,
          host: `COR-6938.middleware.loc`,
          port: `5432`,
          username: `postgres`,
          password: `pass`,
          ssl: false,
          database: `dbcall`,
          timeoutMs: 30000,
        },
      }),
    );
    expect(responseInst.status).toBe(200);
    expect(responseInst.body.ops[0].obj).toBe('instance');
    instance = responseInst.body.ops[0].obj_id;

    const responseInstDel = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.INSTANCE,
        instance_type: `db_call`,
        folder_id: 0,
        title: `Instance_${Date.now()}`,
        data: {
          driver: `postgres`,
          host: `COR-6938.middleware.loc`,
          port: `5432`,
          username: `postgres`,
          password: `pass`,
          ssl: false,
          database: `dbcall`,
          timeoutMs: 30000,
        },
      }),
    );
    expect(responseInstDel.status).toBe(200);
    expect(responseInstDel.body.ops[0].obj).toBe('instance');
    instanceDel = responseInstDel.body.ops[0].obj_id;

    await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: convDel,
      }),
    );
    await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folderDel,
      }),
    );
    await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: dashboardDel,
      }),
    );
    await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: instanceDel,
      }),
    );
  });

  test('should modify owner after delete user', async () => {
    const responseDel = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
      }),
    );
    expect(responseDel.status).toBe(200);
    expect(responseDel.body.request_proc).toBe('ok');
    expect(responseDel.body.ops[0].proc).toBe('ok');
    expect(responseDel.body.ops[0].obj_id).toBe(ApiKey2);

    await new Promise(r => setTimeout(r, 80000));

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.request_proc).toBe('ok');
    expect(responseList.body.ops[0].proc).toBe('ok');
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === conv)?.owner_id).toEqual(
      obj_idKey,
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === folder)?.owner_id).toEqual(
      obj_idKey,
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === dashboard)?.owner_id).toEqual(
      obj_idKey,
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === instance)?.owner_id).toEqual(
      obj_idKey,
    );

    const responseListDel = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        filter: 'deleted',
      }),
    );
    expect(responseListDel.status).toBe(200);
    expect(responseListDel.body.request_proc).toBe('ok');
    expect(responseListDel.body.ops[0].proc).toBe('ok');
    expect((responseListDel.body.ops[0].list as Array<any>).find(item => item.obj_id === convDel)?.owner_id).toEqual(
      obj_idKey,
    );
    expect((responseListDel.body.ops[0].list as Array<any>).find(item => item.obj_id === folderDel)?.owner_id).toEqual(
      obj_idKey,
    );
    expect(
      (responseListDel.body.ops[0].list as Array<any>).find(item => item.obj_id === dashboardDel)?.owner_id,
    ).toEqual(obj_idKey);
    expect(
      (responseListDel.body.ops[0].list as Array<any>).find(item => item.obj_id === instanceDel)?.owner_id,
    ).toEqual(obj_idKey);

    const responseListGroup = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'group',
      }),
    );
    expect(responseListGroup.status).toBe(200);
    expect(responseListGroup.body.request_proc).toBe('ok');
    expect(responseListGroup.body.ops[0].proc).toBe('ok');
    expect((responseListGroup.body.ops[0].list as Array<any>).find(item => item.obj_id === newGroup)?.owner_id).toEqual(
      obj_idKey,
    );
  });

  afterAll(async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        obj_id: newGroup,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: ApiKey2,
      }),
    );
    const responseDel = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv,
      }),
    );
    expect(responseDel.status).toBe(200);
    expect(responseDel.body.request_proc).toBe('ok');
    expect(responseDel.body.ops[0].proc).toBe('ok');

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folder,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: dashboard,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: instance,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.CONV,
        obj_id: conv,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: instance,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folder,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: dashboard,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.CONV,
        obj_id: convDel,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folderDel,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.INSTANCE,
        obj_id: instanceDel,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: dashboardDel,
      }),
    );
  });
});
