import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';

describe('Unixtime&Date Functions', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: string | null | undefined;
  let conv_id: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let task_id: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: 'SetParam',
        status: 'actived',
      }),
    );
    conv_id = response.body.ops[0].obj_id;

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
      }),
    );
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateSetParam = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id: conv_id,
        title: 'process',
        obj_type: 0,
        logics: [
          {
            type: 'set_param',
            extra: {
              unixtime: '$.unixtime()',
              unixtime2: `$.unixtime(%y-%m-%d %h:%i:%s)`,
              unixtime3: `$.unixtime(%y-%m-%d-5 %h:%i+60:%s)`,
              unixtime4: `$.unixtime(2020-05-30 06:22:11)`,
              unixtime5: `$.unixtime(%y-%m-%d-5)`,
              unixtime6: `$.unixtime(%y-%m-%d 02:00:00)`,
              unixtimetz: `$.unixtime().tz('Japan')`,
              unixtimetz2: `$.unixtime(%y-%m-%d %h:%i:%s).tz('Japan')`,
              unixtimetz3: `$.unixtime(%y-%m-%d-2 %h:%i+60:%s).tz('Japan')`,
              unixtimetz4: `$.unixtime(2020-05-30 06:22:11).tz('Japan')`,
              unixtimetz5: `$.unixtime().tz('GMT+2')`,
              unixtimetz6: `$.unixtime().tz('GMT-4')`,
              date: '$.date()',
              date2: `$.date(%y-%m-%d %h:%i:%s)`,
              date3: `$.date(%y-%m-%d-5 %h:%i+60:%s)`,
              date4: `$.date(2020-05-30 06:22:11)`,
              date5: `$.date(%y-%m-%d 02:00:00)`,
            },
            extra_type: {
              unixtime: 'string',
              unixtimetz: 'string',
              unixtime2: 'string',
              unixtime3: 'string',
              unixtime4: 'string',
              unixtime5: 'number',
              unixtime6: 'number',
              unixtimetz2: 'number',
              unixtimetz3: 'string',
              unixtimetz4: 'string',
              unixtimetz5: 'string',
              unixtimetz6: 'string',
              date: 'string',
              date2: 'string',
              date3: 'string',
              date4: 'string',
              date5: 'string',
            },
            err_node_id: '',
          },
          { to_node_id: final_node_ID, type: 'go', node_title: 'final' },
        ],
        semaphors: [
          { type: 'count', value: 10, esc_node_id: final_node_ID },
          { type: 'time', value: 10, dimension: 'sec', to_node_id: final_node_ID },
        ],
        version: 22,
      }),
    );
    expect(responseCreateSetParam.status).toBe(200);
    expect(responseCreateSetParam.body.ops[0].proc).toEqual('ok');

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        conv_id: conv_id,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');
  });

  test('should create task SetParam check unixtime', async () => {
    const unixtime = Math.round(Date.now() / 1000 - 1);
    const unixtime3 = Math.round(Date.now() / 1000 - 428401);
    const date = new Date();
    const dateunix = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 2, 0, 0));
    const unixtime6 = Math.round(dateunix.valueOf() / 1000);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {},
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        ref_or_obj_id: task_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].proc).toEqual('ok');

    expect(responseShow.body.ops[0].data.unixtime).toMatch(`${unixtime.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].data.unixtime2).toMatch(`${unixtime.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].data.unixtime3).toMatch(`${unixtime3.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].data.unixtime4).toEqual(`1590819731`);
    expect(responseShow.body.ops[0].data.unixtime5.toString()).toMatch(`${unixtime.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].data.unixtime6.toString()).toMatch(`${unixtime6.toString().slice(0, -2)}`);
  });

  test('should create task SetParam check unixtimeTZ', async () => {
    const unixtimetz = Math.round(Date.now() / 1000 + 32399);
    const unixtimetz3 = Math.round(Date.now() / 1000 - 136801);
    const unixtimetz5 = Math.round(Date.now() / 1000 + 7200);
    const unixtimetz6 = Math.round(Date.now() / 1000 - 14400);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,

        conv_id: conv_id,
        data: {},
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        ref_or_obj_id: task_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].proc).toEqual('ok');
    expect(responseShow.body.ops[0].data.unixtimetz).toMatch(`${unixtimetz.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].data.unixtimetz2.toString()).toMatch(`${unixtimetz.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].data.unixtimetz3).toMatch(`${unixtimetz3.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].data.unixtimetz4).toEqual(`1590852131`);
    expect(responseShow.body.ops[0].data.unixtimetz5).toMatch(`${unixtimetz5.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].data.unixtimetz6).toMatch(`${unixtimetz6.toString().slice(0, -2)}`);
  });

  test('should create task SetParam check date', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {},
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    const date = new Date();
    const datenow =
      String(date.getFullYear()) +
      `-` +
      String(date.getMonth() + 1).padStart(2, '0') +
      `-` +
      String(date.getDate()).padStart(2, '0') +
      ` ` +
      String(date.getHours()).padStart(2, '0') +
      `:` +
      String(date.getMinutes()).padStart(2, '0') +
      `:` +
      String(date.getSeconds()).padStart(2, '0');
    const datenow5 =
      String(date.getFullYear()) +
      `-` +
      String(date.getMonth() + 1).padStart(2, '0') +
      `-` +
      String(date.getDate()).padStart(2, '0');

    date.setDate(date.getDate() - 5);
    const datenow3 =
      String(date.getFullYear()) +
      `-` +
      String(date.getMonth() + 1).padStart(2, '0') +
      `-` +
      String(date.getDate()).padStart(2, '0') +
      ` ` +
      String(date.getHours() + 1).padStart(2, '0') +
      `:` +
      String(date.getMinutes()).padStart(2, '0') +
      `:` +
      String(date.getSeconds()).padStart(2, '0');

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        ref_or_obj_id: task_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].proc).toEqual('ok');
    expect(responseShow.body.ops[0].data.date).toEqual(datenow);
    expect(responseShow.body.ops[0].data.date2).toEqual(datenow);
    expect(responseShow.body.ops[0].data.date3).toEqual(datenow3);
    expect(responseShow.body.ops[0].data.date4).toEqual(`2020-05-30 06:22:11`);
    expect(responseShow.body.ops[0].data.date5).toEqual(`${datenow5} 02:00:00`);
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
