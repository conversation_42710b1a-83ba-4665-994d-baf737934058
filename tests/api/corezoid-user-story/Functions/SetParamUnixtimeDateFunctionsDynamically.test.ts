import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';

describe('Unixtime&Date Functions Dynamically', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let task_id: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: 'SetParam',
        status: 'actived',
      }),
    );
    conv_id = response.body.ops[0].obj_id;

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
      }),
    );
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateSetParam = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id: conv_id,
        title: 'process',
        obj_type: 0,
        logics: [
          {
            type: 'set_param',
            extra: {
              unixtime: '$.unixtime({{Test}})',
              unixtimetz: `$.unixtime({{Test}}).tz('Japan')`,
              math: `$.math($.unixtime()+{{plus_time}})`,
              date2: `$.date({{Test}})`,
            },
            extra_type: { unixtime: 'string', unixtimetz: 'string', math: 'string', date2: 'string' },
            err_node_id: '',
          },
          { to_node_id: final_node_ID, type: 'go', node_title: 'final' },
        ],
        semaphors: [
          { type: 'count', value: 10, esc_node_id: final_node_ID },
          { type: 'time', value: 10, dimension: 'sec', to_node_id: final_node_ID },
        ],
        version: 22,
      }),
    );
    expect(responseCreateSetParam.status).toBe(200);
    expect(responseCreateSetParam.body.ops[0].proc).toEqual('ok');

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        conv_id: conv_id,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');
  });

  test('should create task `%y-%m-%d %h:%i:%s`', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          Test: '%y-%m-%d %h:%i:%s',
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    const unixtime = Math.round(Date.now() / 1000);
    const unixtimetz = Math.round(Date.now() / 1000 + 32400);

    const date = new Date();
    const datenow =
      String(date.getUTCFullYear()) +
      `-` +
      String(date.getUTCMonth() + 1).padStart(2, '0') +
      `-` +
      String(date.getUTCDate()).padStart(2, '0') +
      ` ` +
      String(date.getUTCHours()).padStart(2, '0') +
      `:` +
      String(date.getUTCMinutes()).padStart(2, '0') +
      `:` +
      String(date.getUTCSeconds()).padStart(2, '0');

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        ref_or_obj_id: task_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].proc).toEqual('ok');
    expect(responseShow.body.ops[0].data.unixtime).toMatch(`${unixtime.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].data.unixtimetz).toMatch(`${unixtimetz.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].data.date2).toEqual(datenow);
  });

  test('should create task `%y-%m-%d-3 %h+1:%i:%s`', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          Test: '%y-%m-%d-3 %h+1:%i:%s',
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    const unixtime = Math.round(Date.now() / 1000 - 255600);
    const unixtimetz = Math.round(Date.now() / 1000 - 223200);

    const date = new Date();
    // Создаем новую дату с вычитанием 3 дней и добавлением 1 часа
    const targetDate = new Date(date.getTime() - 3 * 24 * 60 * 60 * 1000 + 1 * 60 * 60 * 1000);
    const datenow =
      String(targetDate.getUTCFullYear()) +
      `-` +
      String(targetDate.getUTCMonth() + 1).padStart(2, '0') +
      `-` +
      String(targetDate.getUTCDate()).padStart(2, '0') +
      ` ` +
      String(targetDate.getUTCHours()).padStart(2, '0') +
      `:` +
      String(targetDate.getUTCMinutes()).padStart(2, '0') +
      `:` +
      String(targetDate.getUTCSeconds()).padStart(2, '0');

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        ref_or_obj_id: task_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].proc).toEqual('ok');
    expect(responseShow.body.ops[0].data.unixtime).toMatch(`${unixtime.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].data.unixtimetz).toMatch(`${unixtimetz.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].data.date2).toEqual(datenow);
  });

  test('should create task `2020-05-30 06:22:11`', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          Test: '2020-05-30 06:22:11',
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        ref_or_obj_id: task_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].proc).toEqual('ok');
    expect(responseShow.body.ops[0].data.unixtime).toEqual(`1590819731`);
    expect(responseShow.body.ops[0].data.unixtimetz).toEqual(`1590852131`);
    expect(responseShow.body.ops[0].data.date2).toEqual(`2020-05-30 06:22:11`);
  });

  test('should create task `%y-%m-%d 02:00:00`', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          Test: '%y-%m-%d 02:00:00',
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    const date = new Date();
    const datenow =
      String(date.getFullYear()) +
      `-` +
      String(date.getMonth() + 1).padStart(2, '0') +
      `-` +
      String(date.getDate()).padStart(2, '0');

    const dateunix = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 2, 0, 0));
    const unixtime = Math.round(dateunix.valueOf() / 1000);
    const unixtimetz = Math.round(dateunix.valueOf() / 1000 + 32400);

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        ref_or_obj_id: task_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].proc).toEqual('ok');
    expect(responseShow.body.ops[0].data.unixtime).toMatch(`${unixtime.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].data.unixtimetz).toMatch(`${unixtimetz.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].data.date2).toEqual(`${datenow} 02:00:00`);
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
