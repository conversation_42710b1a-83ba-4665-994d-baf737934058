import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';

describe('Unixtime&Date Functions', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: string | null | undefined;
  let conv_id: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let callback_hash: string | number;
  let conv_id2: number;
  let final_node_ID2: string | number;
  let url: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: 'Conv',
        status: 'actived',
      }),
    );
    conv_id2 = responseConv.body.ops[0].obj_id;

    const responseListConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id2,
      }),
    );
    expect(responseListConv.status).toBe(200);
    final_node_ID2 = (responseListConv.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseHash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.CALLBACK_HASH,
        conv_id: conv_id2,
      }),
    );
    expect(responseHash.status).toBe(200);
    callback_hash = responseHash.body.ops[0].callback_hash;
    url = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/${conv_id2}/${callback_hash}/`;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: 'Apicall',
        status: 'actived',
      }),
    );
    conv_id = response.body.ops[0].obj_id;

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
      }),
    );
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'LogicCallNew',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'POST',
            url,
            extra: {
              unixtime: '$.unixtime()',
              unixtime2: `$.unixtime(%y-%m-%d %h:%i:%s)`,
              unixtime3: `$.unixtime(%y-%m-%d-5 %h:%i+60:%s)`,
              unixtime4: `$.unixtime(2020-05-30 06:22:11)`,
              unixtime5: `$.unixtime(%y-%m-%d-5)`,
              unixtime6: `$.unixtime(%y-%m-%d 02:00:00)`,
              unixtime7: `$.unixtime({{unixT}})`,
              unixtime8: `$.unixtime().tz('GMT+3')`,
              unixtime9: `$.unixtime().tz('GMT-2')`,
              date: '$.date()',
              date2: `$.date(%y-%m-%d %h:%i:%s)`,
              date3: `$.date(%y-%m-%d-5 %h:%i+60:%s)`,
              date4: `$.date(2020-05-30 06:22:11)`,
              date5: `$.date(%y-%m-%d 02:00:00)`,
              date6: `$.date({{dateT}})`,
            },
            extra_type: {
              unixtime: 'string',
              unixtime2: 'string',
              unixtime3: 'string',
              unixtime4: 'string',
              unixtime5: 'number',
              unixtime6: 'number',
              unixtime7: 'string',
              unixtime8: 'string',
              unixtime9: 'string',
              date: 'string',
              date2: 'string',
              date3: 'string',
              date4: 'string',
              date5: 'string',
              date6: 'string',
            },
            max_threads: 5,
            err_node_id: '',
            extra_headers: {},
            send_sys: false,
            cert_pem: '',
            content_type: 'application/json',
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);
    expect(responseCreateNode.body.ops[0].proc).toEqual('ok');

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        conv_id: conv_id,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');
  });

  test('should create task api_call check unixtime', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: { unixT: '%y-%m-%d-5 %h:%i+60:%s' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');

    const unixtime = Math.round(Date.now() / 1000);
    const unixtime3 = Math.round(Date.now() / 1000 - 428400);
    const date = new Date();
    const dateunix = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 2, 0, 0));
    const unixtime6 = Math.round(dateunix.valueOf() / 1000);
    const unixtime8 = Math.round(Date.now() / 1000 + 10800);
    const unixtime9 = Math.round(Date.now() / 1000 - 7200);

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID2,
        conv_id: conv_id2,
        limit: 1,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].proc).toEqual('ok');
    expect(responseShow.body.ops[0].list[0].data.unixtime).toMatch(`${unixtime.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].list[0].data.unixtime2).toMatch(`${unixtime.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].list[0].data.unixtime3).toMatch(`${unixtime3.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].list[0].data.unixtime4).toEqual(`1590819731`);
    expect(responseShow.body.ops[0].list[0].data.unixtime5.toString()).toMatch(`${unixtime.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].list[0].data.unixtime6.toString()).toMatch(`${unixtime6.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].list[0].data.unixtime7).toMatch(`${unixtime3.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].list[0].data.unixtime8).toMatch(`${unixtime8.toString().slice(0, -2)}`);
    expect(responseShow.body.ops[0].list[0].data.unixtime9).toMatch(`${unixtime9.toString().slice(0, -2)}`);
  });

  test('should create task api_call check date', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: { dateT: '%y-%m-%d-5 %h:%i+60:%s' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');

    const date = new Date();
    const datenow =
      String(date.getFullYear()) +
      `-` +
      String(date.getMonth() + 1).padStart(2, '0') +
      `-` +
      String(date.getDate()).padStart(2, '0') +
      ` ` +
      String(date.getHours()).padStart(2, '0') +
      `:` +
      String(date.getMinutes()).padStart(2, '0') +
      `:` +
      String(date.getSeconds()).padStart(2, '0');
    const datenow5 =
      String(date.getFullYear()) +
      `-` +
      String(date.getMonth() + 1).padStart(2, '0') +
      `-` +
      String(date.getDate()).padStart(2, '0');

    date.setDate(date.getDate() - 5);
    const datenow3 =
      String(date.getFullYear()) +
      `-` +
      String(date.getMonth() + 1).padStart(2, '0') +
      `-` +
      String(date.getDate()).padStart(2, '0') +
      ` ` +
      String(date.getHours() + 1).padStart(2, '0') +
      `:` +
      String(date.getMinutes()).padStart(2, '0') +
      `:` +
      String(date.getSeconds()).padStart(2, '0');

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID2,
        conv_id: conv_id2,
        limit: 1,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].proc).toEqual('ok');
    expect(responseShow.body.ops[0].list[0].data.date).toEqual(datenow);
    expect(responseShow.body.ops[0].list[0].data.date2).toEqual(datenow);
    expect(responseShow.body.ops[0].list[0].data.date3).toEqual(datenow3);
    expect(responseShow.body.ops[0].list[0].data.date4).toEqual(`2020-05-30 06:22:11`);
    expect(responseShow.body.ops[0].list[0].data.date5).toEqual(`${datenow5} 02:00:00`);
    expect(responseShow.body.ops[0].list[0].data.date6).toEqual(datenow3);
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
      }),
    );
    expect(response.status).toBe(200);

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id2,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
