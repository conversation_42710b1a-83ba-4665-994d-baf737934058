import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import { application } from '../../../../application/Application';
import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { createAuthUser, Method } from '../../../../utils/request';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  maxLength,
  minLength,
  objectTestCases,
} from '../../negativeCases';

describe('Actorizer', () => {
  let apiS: ApiKeyClient;
  let apiSuper: ApiKey;
  let host: string;
  let value: string | number;
  let userToken: any;
  const valuesToSkip: any = ['test'];

  beforeAll(async () => {
    const config = ConfigurationManager.getConfiguration();
    host = config.getActorizerUrl();
    apiSuper = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apiSuper);

    const responseList = await apiS.requestSuperadmin('superadmin/api/1/json', {
      type: 'get',
      obj: 'license',
      obj_type: 'file',
    });
    expect(responseList.status).toBe(200);
    expect(responseList.body.result).toBe('ok');
    value = responseList.body.data.pub_key;
    userToken = createAuthUser(`Bearer ${value}`, 'token');
  });

  test.each(
    [...integerTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't actorize send with invalid entity_id '%s'`, async (input, errors) => {
    const response = await userToken.request({
      method: Method.POST,
      url: `${host}api/1/json`,
      data: {
        data: [{ entity_id: input, entity_type_name: 'user', data: { name: 'test' } }],
      },
    });
    expect(response.status).toBe(200);
    expect(response.data.result).toEqual('error');
    const error = response.data.description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...maxLength].map(({ input }) => [input]))(
    `shouldn't actorize send with invalid entity_id '%s'`,
    async input => {
      const response = await userToken.request({
        method: Method.POST,
        url: `${host}api/1/json`,
        data: {
          data: [{ entity_id: input, entity_type_name: 'user', data: { name: 'test' } }],
        },
      });
      expect(response.status).toBe(200);
      expect(response.data.result).toEqual('error');
      expect(response.data.description).toContain(
        `Value is not valid. Value's byte_size is more than maximum allowed: 200`,
      );
    },
  );

  test.each(
    [...stringTestCases, ...undefinedTestCase, ...minLength, ...maxLength]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't actorize send with invalid entity_type_name '%s'`, async (input, errors) => {
    const response = await userToken.request({
      method: Method.POST,
      url: `${host}api/1/json`,
      data: {
        data: [{ entity_id: 1, entity_type_name: input, data: { name: 'test' } }],
      },
    });
    expect(response.status).toBe(200);
    expect(response.data.result).toEqual('error');
    const error = response.data.description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...objectTestCases, ...undefinedTestCase]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't actorize send with invalid data '%s'`, async (input, errors) => {
    const response = await userToken.request({
      method: Method.POST,
      url: `${host}api/1/json`,
      data: {
        data: [{ entity_id: 1, entity_type_name: 'user', data: input }],
      },
    });
    expect(response.status).toBe(200);
    expect(response.data.result).toEqual('error');
    const error = response.data.description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });
});
