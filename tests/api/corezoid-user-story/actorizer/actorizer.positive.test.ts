import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import { application } from '../../../../application/Application';
import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { createAuthUser, Method } from '../../../../utils/request';

describe('Actorizer', () => {
  let apiS: ApiKeyClient;
  let apiSuper: ApiKey;
  let host: string;
  let value: string | number;
  let userToken: any;
  let invalidUserToken: any;

  beforeAll(async () => {
    const config = ConfigurationManager.getConfiguration();
    host = config.getActorizerUrl();
    apiSuper = await application.getApiKeySuper();
    apiS = application.getApiKeyClient(apiSuper);

    const responseList = await apiS.requestSuperadmin('superadmin/api/1/json', {
      type: 'get',
      obj: 'license',
      obj_type: 'file',
    });
    expect(responseList.status).toBe(200);
    expect(responseList.body.result).toBe('ok');
    value = responseList.body.data.pub_key;
    userToken = createAuthUser(`Bearer ${value}`, 'token');
    invalidUserToken = createAuthUser(`Bearer 1${value}`, 'token');
  });

  test('should create request send to actorizer', async () => {
    const response = await userToken.request({
      method: Method.POST,
      url: `${host}api/1/json`,
      data: {
        data: [{ entity_id: 100, entity_type_name: 'user', data: { name: 'test' } }],
      },
    });
    expect(response.status).toBe(200);
    expect(response.data.request_proc).toBe('ok');
    expect(response.data.result).toBe('ok');
  });

  test(`shouldn't create request send to actorizer (access_denied)`, async () => {
    const response = await invalidUserToken.request({
      method: Method.POST,
      url: `${host}api/1/json`,
      data: {
        data: [{ entity_id: 100, entity_type_name: 'user', data: { name: 'test' } }],
      },
    });
    expect(response.status).toBe(200);
    expect(response.data.description).toBe('access_denied');
    expect(response.data.result).toBe('error');
  });
});
