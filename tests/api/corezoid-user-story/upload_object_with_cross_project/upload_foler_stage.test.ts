import { debug } from '../../../../support/utils/logger';
import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { Api<PERSON>ey } from '../../../../infrastructure/model/ApiKey';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
  generateName,
} from '../../../../utils/corezoidRequest';
import { requestDeleteObj, requestListConv } from '../../../../application/api/ApiObj';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';
import { addMsg } from 'jest-html-reporters/helper';

describe('compare/merge from file', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let project_id: string | number;
  let uploaud_folder_id: string | number;
  let key: string;
  let secret: string;
  let stage_short_name: string;
  let uploadStage_id: string | number;
  let convOtherProj: number;
  let convOtherStage: number;
  let convThisStage: number;
  let convEmpty: number;
  let convSD: number;
  let process1: any;
  let process2: any;
  let process3: any;
  let process4: any;
  let process5: any;
  let process6: any;
  let process7: any;
  let process8: any;
  let process9: any;
  const projFirst = 1019509;
  const stageFirst = 1019511;
  const projSecond = 1019527;
  const stageSecond = 1019529;

  const exec = promisify(execCallback);
  const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
  const scriptPath = 'tests/api/corezoid-api/sh/uploadObjectRootFolder.sh';
  const scriptPathStage = 'tests/api/corezoid-api/sh/uploadObjectInStage.sh';

  function parseStdoutToJson(stdout: any): any {
    const jsonStartIndex = stdout.indexOf('{');
    const jsonString = stdout.slice(jsonStartIndex);
    return JSON.parse(jsonString);
  }

  async function uploadFileToStage(fileName: string): Promise<any> {
    const uploadResult = await exec(scriptPathStage, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        ASYNC: 'false',
        OBJ_TO_ID: `${uploadStage_id}`,
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        VALIDATE_SCHEME: 'false',
        COMPANY_ID: company_id,
        FILE_NAME: fileName,
        SKIP_ROOT: 'false',
        OBJ_TO_TYPE: 'stage',
      },
    });
    debug(uploadResult.stdout);
    const jsonResponse = parseStdoutToJson(uploadResult.stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.ops[0].proc).toBe(PROC_STATUS.OK);
    return jsonResponse;
  }

  async function verifyOtherProjObject(convId: number): Promise<void> {
    const responseListConv = await requestListConv(api, convId, company_id, project_id, uploadStage_id);
    expect(responseListConv.status).toBe(RESP_STATUS.OK);
    const { list } = responseListConv.body.ops[0];

    process1 = (list as Array<any>).find(item => item.title === 'allConstr').logics[0];
    expect(process1.project_id).toEqual('{{proj}}');
    expect(process1.stage_id).toEqual('{{stage}}');
    expect(process1.conv_id).toEqual('@{{alias}}');

    process2 = (list as Array<any>).find(item => item.title === 'p/s/alias').logics[0];
    expect(process2.project_id).toEqual(projSecond);
    expect(process2.stage_id).toEqual(stageSecond);
    expect(process2.conv_id).toEqual('@emptyalotherproj');

    process3 = (list as Array<any>).find(item => item.title === 'p/s/ConstrAl').logics[0];
    expect(process3.project_id).toEqual(projSecond);
    expect(process3.stage_id).toEqual(stageSecond);
    expect(process3.conv_id).toEqual('@{{alias}}');

    process4 = (list as Array<any>).find(item => item.title === 'p/ConstrS/ConstrC').logics[0];
    expect(process4.project_id).toEqual(projSecond);
    expect(process4.stage_id).toEqual('{{stage}}');
    expect(process4.conv_id).toEqual('{{conv}}');

    process5 = (list as Array<any>).find(item => item.title === 'p/s/c').logics[0];
    expect(process5.project_id).toEqual(projSecond);
    expect(process5.stage_id).toEqual(stageSecond);
    expect(process5.conv_id).toEqual(2312662);
  }

  async function verifyOtherStageObject(convId: number, refConvId: number | string): Promise<void> {
    const responseListConv = await requestListConv(api, convId, company_id, project_id, uploadStage_id);
    expect(responseListConv.status).toBe(RESP_STATUS.OK);
    const { list } = responseListConv.body.ops[0];

    process1 = (list as Array<any>).find(item => item.title === 'AllConstr').logics[0];
    expect(process1.project_id).toEqual(`{{conv[${refConvId}].ref[ref0000].project}}`);
    expect(process1.stage_id).toEqual(`{{conv[${refConvId}].ref[ref0000].stageOther}}`);
    expect(process1.conv_id).toEqual(`{{conv[${refConvId}].ref[ref0000].convOther}}`);

    process2 = (list as Array<any>).find(item => item.title === 'p/s/alias').logics[0];
    expect(process2.project_id).toEqual(projFirst);
    expect(process2.stage_id).toEqual(stageFirst);
    expect(process2.conv_id).toEqual('@aliasotherstage');

    process3 = (list as Array<any>).find(item => item.title === 'p/s/c').logics[0];
    expect(process3.project_id).toEqual(projFirst);
    expect(process3.stage_id).toEqual(stageFirst);
    expect(process3.conv_id).toEqual(2312582);

    process4 = (list as Array<any>).find(item => item.title === 'p/ConstrS/ConstrC').logics[0];
    expect(process4.project_id).toEqual(projFirst);
    expect(process4.stage_id).toEqual(`{{conv[${refConvId}].ref[ref0000].stageOther}}`);
    expect(process4.conv_id).toEqual(`{{conv[${refConvId}].ref[ref0000].convOther}}`);
  }

  async function verifyThisStageObject(
    convId: number,
    refConvId: number | string,
    emptyConvId?: number,
  ): Promise<void> {
    const responseListConv = await requestListConv(api, convId, company_id, project_id, uploadStage_id);
    expect(responseListConv.status).toBe(RESP_STATUS.OK);
    const { list } = responseListConv.body.ops[0];

    process1 = (list as Array<any>).find(item => item.title === 'ConstP/ConstrS/ConstrC').logics[0];
    expect(process1.project_id).toEqual(`{{conv[${refConvId}].ref[ref0000].project}}`);
    expect(process1.stage_id).toEqual(`{{conv[${refConvId}].ref[ref0000].stage}}`);
    expect(process1.conv_id).toEqual(`{{conv[${refConvId}].ref[ref0000].conv}}`);

    process2 = (list as Array<any>).find(item => item.title === 'p/s/alias').logics[0];
    expect(process2.conv_id).toEqual('@aliasempty');

    process3 = (list as Array<any>).find(item => item.title === 'p/s/conv').logics[0];
    if (emptyConvId) {
      expect(process3.conv_id).toEqual(emptyConvId);
    }

    process4 = (list as Array<any>).find(item => item.title === 'p/ConstrS/ConstrC').logics[0];
    expect(process4.project_id).toEqual(projFirst);
    expect(process4.stage_id).toEqual(`{{conv[${refConvId}].ref[ref0000].stage}}`);
    expect(process4.conv_id).toEqual(`{{conv[${refConvId}].ref[ref0000].conv}}`);

    process5 = (list as Array<any>).find(item => item.title === 'p/s/ConstrC').logics[0];
    expect(process5.conv_id).toEqual(`{{conv[${refConvId}].ref[ref0000].conv}}`);

    process6 = (list as Array<any>).find(item => item.title === 'AllDinamic').logics[0];
    expect(process6.project_id).toEqual(`{{proj}}`);
    expect(process6.stage_id).toEqual(`{{stage}}`);
    expect(process6.conv_id).toEqual(`{{conv}}`);

    process7 = (list as Array<any>).find(item => item.title === 'p/ConstrS/ConstrA').logics[0];
    expect(process7.project_id).toEqual(projFirst);
    expect(process7.stage_id).toEqual(`{{conv[${refConvId}].ref[ref0000].stage}}`);
    expect(process7.conv_id).toEqual(`@{{conv[${refConvId}].ref[ref0000].alias}}`);

    process8 = (list as Array<any>).find(item => item.title === 'p/s/ConstrA').logics[0];
    expect(process8.conv_id).toEqual(`@{{conv[${refConvId}].ref[ref0000].alias}}`);

    process9 = (list as Array<any>).find(item => item.title === 'p/s/DinamicC').logics[0];
    expect(process9.conv_id).toEqual(`{{conv}}`);
  }

  beforeAll(async () => {
    apikey = {
      key: '119155',
      secret: 'Wz1nNW8a51H1KZrDUBK3MOriRfPmswMjkXlO7CbwD6MiULBO8q',
      companies: [{ id: 'i738314881' }],
      id: '118338',
      title: 'user5',
    };
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key = apikey.key;
    secret = apikey.secret;
    stage_short_name = generateName(OBJ_TYPE.STAGE);

    const { stdout } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        VALIDATE_SCHEME: 'false',
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        COMPANY_ID: company_id,
        FILE_NAME: 'project_autotestCompareMerge.zip',
        ASYNC: 'true',
      },
    });
    debug(stdout);
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.request_proc).toBe(`ok`);

    await new Promise(r => setTimeout(r, 10000));

    const projectsList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECTS,
        obj_id: 0,
        company_id,
        id: company_id,
        order: 'asc',
        sort: 'date',
      }),
    );
    expect(projectsList.status).toBe(200);
    const { list: listSetParam } = projectsList.body.ops[0];
    project_id = (listSetParam as Array<any>).find(item => item.short_name === 'firstproj-1').project_id;

    const createStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        project_id,
        immutable: false,
        company_id,
        title: stage_short_name,
        short_name: stage_short_name,
      }),
    );
    expect(createStage.status).toBe(200);
    uploadStage_id = createStage.body.ops[0].obj_id;
  });

  test(`upload(create) Stage from the file`, async () => {
    await uploadFileToStage('stage_dev_autotestCompareMerge.zip');

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        company_id,
        obj_id: uploadStage_id,
        project_id,
      }),
    );
    expect(responseList.status).toBe(RESP_STATUS.OK);
    const { proc, list } = responseList.body.ops[0];
    expect(proc).toEqual(PROC_STATUS.OK);

    convOtherProj = (list as Array<any>).find(item => item.title === 'copyOtherProj').obj_id;
    convOtherStage = (list as Array<any>).find(item => item.title === 'copyOtherStage').obj_id;
    convThisStage = (list as Array<any>).find(item => item.title === 'copyThisStage').obj_id;
    convEmpty = (list as Array<any>).find(item => item.title === 'empty').obj_id;
    convSD = (list as Array<any>).find(item => item.title === 'SD1').obj_id;

    await verifyOtherProjObject(convOtherProj);
    await verifyOtherStageObject(convOtherStage, convSD);
    await verifyThisStageObject(convThisStage, convSD, convEmpty);
  });

  test(`upload(create) Folder from the file`, async () => {
    await uploadFileToStage('folder_upload_cross-project.zip');

    const responseListStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: uploadStage_id,
        project_id,
      }),
    );
    expect(responseListStage.status).toBe(RESP_STATUS.OK);
    const { proc, list: listS } = responseListStage.body.ops[0];
    expect(proc).toEqual(PROC_STATUS.OK);
    uploaud_folder_id = (listS as Array<any>).find(item => item.title === 'autotestUploadCrossProject').obj_id;

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: uploaud_folder_id,
        project_id,
      }),
    );
    expect(responseListFolder.status).toBe(RESP_STATUS.OK);
    const { proc: procF, list } = responseListFolder.body.ops[0];
    expect(procF).toEqual(PROC_STATUS.OK);
    convOtherProj = (list as Array<any>).find(item => item.title === 'copyOtherProj').obj_id;
    convOtherStage = (list as Array<any>).find(item => item.title === 'copyOtherStage').obj_id;
    convThisStage = (list as Array<any>).find(item => item.title === 'copyThisStage').obj_id;

    await verifyOtherProjObject(convOtherProj);
    await verifyOtherStageObject(convOtherStage, 2312581);
    await verifyThisStageObject(convThisStage, 2312581, 2312579);
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, project_id, company_id);
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
