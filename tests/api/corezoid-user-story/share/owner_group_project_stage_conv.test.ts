import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { User } from '../../../../infrastructure/model/User';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  generateName,
} from '../../../../utils/corezoidRequest';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import { requestDeleteObj, requestCreateObjNew } from '../../../../application/api/ApiObj';
import { createAuthUser, Method } from '../../../../utils/request';
import { getArrayPermissionsCombinations } from '../../../../utils/generationPermission';
import { ApiClientResponse } from '../../../../application/api/ApiUserClient';

describe('Sharing', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let user: User;
  let groupId: number;
  let company_id: any;
  let project_short_name: string;
  let stage_short_name: string;
  let newConv: number;
  let newApiKeyId: string | number;
  let conv_type: string;
  let urlPrivs: string;
  let newCookie: any;
  const arrayPermission: {
    description: string;
    privsFirstObject: { type: string; list_obj: string[] }[];
    privsSecondObject: { type: string; list_obj: string[] }[];
    privsProc: { type: string; list_obj: string[] }[];
    expectFirstObject: { create: boolean; view: boolean; modify: boolean; delete: boolean };
    expectSecondObject: { create: boolean; view: boolean; modify: boolean; delete: boolean };
    expectConvPerm: { create: boolean; view: boolean; modify: boolean; delete: boolean };
  }[] = getArrayPermissionsCombinations('project');

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = generateName(OBJ_TYPE.PROJECT);
    stage_short_name = generateName(OBJ_TYPE.STAGE);
    user = await application.getAuthorizedUser({ company: {} }, 1);
    newCookie = createAuthUser(user.cookieUser, 'cookie');
    conv_type = 'process';
    urlPrivs = `${ConfigurationManager.getConfiguration().getUrl()}system/tests/privs_by_folder`;

    const CreateKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(RESP_STATUS.OK);
    const { body: bodyCKR } = CreateKeyResponse as ApiClientResponse;
    newApiKeyId = bodyCKR.ops[0].users[0].obj_id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_type: 'admins',
        title: 'groupShare',
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    const { body: bodyCG } = response as ApiClientResponse;
    groupId = bodyCG.ops[0].obj_id;

    const responseLincUser = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: newApiKeyId,
        company_id,
        group_id: groupId,
        level: 1,
      }),
    );
    expect(responseLincUser.status).toBe(RESP_STATUS.OK);

    const responseChangeGroupOwner = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.OWNER,
        obj_id: groupId,
        obj_type: OBJ_TYPE.GROUP,
        obj_to_id: newApiKeyId,
        save_src_privs: false,
      }),
    );
    expect(responseChangeGroupOwner.status).toBe(RESP_STATUS.OK);

    const responseProject = await requestCreateObjNew(
      api,
      OBJ_TYPE.PROJECT,
      company_id,
      generateName(OBJ_TYPE.PROJECT),
      {
        short_name: project_short_name,
        stages: [],
      },
    );
    newProject = responseProject.obj_id;

    const responseStage = await requestCreateObjNew(api, OBJ_TYPE.STAGE, company_id, generateName(OBJ_TYPE.STAGE), {
      short_name: stage_short_name,
      project_id: newProject,
    });
    newStage = responseStage.obj_id;

    const responseConv = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, generateName(OBJ_TYPE.CONV), {
      conv_type,
      stage_id: newStage,
      project_id: newProject,
    });
    newConv = responseConv.obj_id;
  });

  describe.each(arrayPermission)(
    '$description',
    ({ privsFirstObject, privsSecondObject, privsProc, expectFirstObject, expectSecondObject, expectConvPerm }) => {
      test('for Group and checking the permissions of the group owner', async () => {
        const sendRequest = async (obj: OBJ_TYPE, obj_id: string | number, privs: any): Promise<void> => {
          const response = await api.request(
            createRequestWithOps({
              type: REQUEST_TYPE.LINK,
              obj,
              obj_id,
              company_id,
              obj_to: OBJ_TYPE.GROUP,
              obj_to_id: groupId,
              is_need_to_notify: false,
              privs,
            }),
          );
          expect(response.status).toBe(RESP_STATUS.OK);
        };

        await sendRequest(OBJ_TYPE.PROJECT, newProject, privsFirstObject);
        await sendRequest(OBJ_TYPE.STAGE, newStage, privsSecondObject);
        await sendRequest(OBJ_TYPE.CONV, newConv, privsProc);

        const getPermissionProject = await newCookie.request({
          url: `${urlPrivs}/${newProject}/${newApiKeyId}`,
          method: Method.GET,
        });
        const permissionsProj = (getPermissionProject.data as Array<any>).find(item => item.obj_id === newProject);
        expect(permissionsProj.create).toBe(expectFirstObject.create);
        expect(permissionsProj.view).toBe(expectFirstObject.view);
        expect(permissionsProj.modify).toBe(expectFirstObject.modify);
        expect(permissionsProj.delete).toBe(expectFirstObject.delete);
        const permissionsStage = (getPermissionProject.data as Array<any>).find(item => item.obj_id === newStage);
        expect(permissionsStage.create).toBe(expectSecondObject.create);
        expect(permissionsStage.view).toBe(expectSecondObject.view);
        expect(permissionsStage.modify).toBe(expectSecondObject.modify);
        expect(permissionsStage.delete).toBe(expectSecondObject.delete);
        const permissionsConv = (getPermissionProject.data as Array<any>).find(item => item.obj_id === newConv);
        expect(permissionsConv.create).toBe(expectConvPerm.create);
        expect(permissionsConv.view).toBe(expectConvPerm.view);
        expect(permissionsConv.modify).toBe(expectConvPerm.modify);
        expect(permissionsConv.delete).toBe(expectConvPerm.delete);
      });
    },
  );

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(response.status).toBe(RESP_STATUS.OK);
    const responseDelSecondtUser = await requestDeleteObj(api, OBJ_TYPE.USER, newApiKeyId, company_id);
    expect(responseDelSecondtUser.status).toBe(RESP_STATUS.OK);
    const responseDelGroup = await requestDeleteObj(api, OBJ_TYPE.GROUP, groupId, company_id);
    expect(responseDelGroup.status).toBe(RESP_STATUS.OK);
  });
});
