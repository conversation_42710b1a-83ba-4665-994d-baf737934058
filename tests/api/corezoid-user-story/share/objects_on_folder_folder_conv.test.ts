import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { User } from '../../../../infrastructure/model/User';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  generateName,
} from '../../../../utils/corezoidRequest';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
const { addMsg } = require('jest-html-reporters/helper');
import { requestDeleteObj, requestCreateObjNew } from '../../../../application/api/ApiObj';
import { createAuthUser, Method } from '../../../../utils/request';
import { getArrayPermissionsCombinations } from '../../../../utils/generationPermission';
import { ApiClientResponse } from '../../../../application/api/ApiUserClient';

describe('Sharing', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let user: User;
  let groupId: number;
  let company_id: any;
  let title: string;
  let newConv: number;
  let newApiKeyId: string | number;
  let newUserId: string | number;
  let conv_type: string;
  let urlPrivs: string;
  let newCookie: any;
  let arrayPermission: {
    description: string;
    privsFirstObject: { type: string; list_obj: string[] }[];
    privsSecondObject: { type: string; list_obj: string[] }[];
    privsProc: { type: string; list_obj: string[] }[];
    expectFirstObject: { create: boolean; view: boolean; modify: boolean; delete: boolean };
    expectSecondObject: { create: boolean; view: boolean; modify: boolean; delete: boolean };
    expectConvPerm: { create: boolean; view: boolean; modify: boolean; delete: boolean };
  }[] = [];
  let testCases: [OBJ_TYPE, string | number, string | number][] = [];
  let newFirstFolder: string | number;
  let newSecondFolder: string | number;

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;
      title = generateName(OBJ_TYPE.PROJECT);
      user = await application.getAuthorizedUser({ company: {} }, 1);
      newCookie = createAuthUser(user.cookieUser, 'cookie');
      conv_type = 'process';
      urlPrivs = `${ConfigurationManager.getConfiguration().getUrl()}system/tests/privs_by_folder`;

      const CreateUserResponse = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.USER,
          company_id,
          title: 'API',
          logins: [{ type: 'api' }],
        }),
      );
      expect(CreateUserResponse.status).toBe(RESP_STATUS.OK);
      const { body: bodyCUR } = CreateUserResponse as ApiClientResponse;
      newUserId = bodyCUR.ops[0].users[0].obj_id;

      const CreateKeyResponse = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.USER,
          company_id,
          title: 'API',
          logins: [{ type: 'api' }],
        }),
      );
      expect(CreateKeyResponse.status).toBe(RESP_STATUS.OK);
      const { body: bodyCKR } = CreateKeyResponse as ApiClientResponse;
      newApiKeyId = bodyCKR.ops[0].users[0].obj_id;

      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.GROUP,
          company_id,
          obj_type: 'admins',
          title: 'groupShare',
        }),
      );
      expect(response.status).toBe(RESP_STATUS.OK);
      const { body: bodyCG } = response as ApiClientResponse;
      groupId = bodyCG.ops[0].obj_id;

      const responseLincUser = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.USER,
          obj_id: newApiKeyId,
          company_id,
          group_id: groupId,
          level: 1,
        }),
      );
      expect(responseLincUser.status).toBe(RESP_STATUS.OK);

      const responseCrFirstFolder = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.FOLDER,
          company_id,
          folder_id: 0,
          obj_type: 0,
          status: 'active',
          title,
        }),
      );
      expect(responseCrFirstFolder.status).toBe(RESP_STATUS.OK);
      const { body: bodyCFF } = responseCrFirstFolder as ApiClientResponse;
      newFirstFolder = bodyCFF.ops[0].obj_id;

      const responseCrSecondFolder = await requestCreateObjNew(
        api,
        OBJ_TYPE.FOLDER,
        company_id,
        generateName(OBJ_TYPE.CONV),
        {
          folder_id: newFirstFolder,
          obj_type: 0,
          status: 'active',
        },
      );
      expect(responseCrSecondFolder.status).toBe(RESP_STATUS.OK);
      newSecondFolder = responseCrSecondFolder.obj_id;

      const responseConv = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, generateName(OBJ_TYPE.CONV), {
        conv_type,
        folder_id: newSecondFolder,
        create_mode: 'without_nodes',
        status: 'active',
      });
      newConv = responseConv.obj_id;
    },
  );

  arrayPermission = getArrayPermissionsCombinations('folder');

  describe.each(arrayPermission)(
    '$description',
    ({
      privsFirstObject,
      privsSecondObject,
      privsProc,
      expectFirstObject,
      expectSecondObject,
      expectConvPerm,
    }): void => {
      const testCases = [
        {
          obj_to: OBJ_TYPE.GROUP,
          obj_to_id: () => groupId,
          object_id: () => newApiKeyId,
        },
        {
          obj_to: OBJ_TYPE.USER,
          obj_to_id: () => newUserId,
          object_id: () => newUserId,
        },
      ];

      const sendRequest = async (
        obj: OBJ_TYPE,
        obj_id: string | number,
        privs: any,
        obj_to: string,
        obj_to_id: Function,
      ): Promise<void> => {
        const response = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LINK,
            obj,
            obj_id,
            company_id,
            obj_to,
            obj_to_id: obj_to_id(),
            is_need_to_notify: false,
            privs,
          }),
        );
        expect(response.status).toBe(RESP_STATUS.OK);
      };

      test.each(testCases)(
        'for $obj_to',
        async ({ obj_to, obj_to_id, object_id }): Promise<void> => {
          await sendRequest(OBJ_TYPE.FOLDER, newFirstFolder, privsFirstObject, obj_to, obj_to_id);
          await sendRequest(OBJ_TYPE.FOLDER, newSecondFolder, privsSecondObject, obj_to, obj_to_id);
          await sendRequest(OBJ_TYPE.CONV, newConv, privsProc, obj_to, obj_to_id);

          const getPermission = await newCookie.request({
            url: `${urlPrivs}/${newFirstFolder}/${object_id()}`,
            method: Method.GET,
          });
          const permissionsFirsFolder = (getPermission.data as Array<any>).find(item => item.obj_id === newFirstFolder);
          expect(permissionsFirsFolder.create).toBe(expectFirstObject.create);
          expect(permissionsFirsFolder.view).toBe(expectFirstObject.view);
          expect(permissionsFirsFolder.modify).toBe(expectFirstObject.modify);
          expect(permissionsFirsFolder.delete).toBe(expectFirstObject.delete);
          const permissionsSecondFolder = (getPermission.data as Array<any>).find(
            item => item.obj_id === newSecondFolder,
          );
          expect(permissionsSecondFolder.create).toBe(expectSecondObject.create);
          expect(permissionsSecondFolder.view).toBe(expectSecondObject.view);
          expect(permissionsSecondFolder.modify).toBe(expectSecondObject.modify);
          expect(permissionsSecondFolder.delete).toBe(expectSecondObject.delete);
          const permissionsConv = (getPermission.data as Array<any>).find(item => item.obj_id === newConv);
          expect(permissionsConv.create).toBe(expectConvPerm.create);
          expect(permissionsConv.view).toBe(expectConvPerm.view);
          expect(permissionsConv.modify).toBe(expectConvPerm.modify);
          expect(permissionsConv.delete).toBe(expectConvPerm.delete);
        },
      );

      afterAll(
        async (): Promise<void> => {
          for (const { obj_to, obj_to_id } of testCases) {
            await sendRequest(OBJ_TYPE.FOLDER, newFirstFolder, [], obj_to, obj_to_id);
            await sendRequest(OBJ_TYPE.FOLDER, newSecondFolder, [], obj_to, obj_to_id);
            await sendRequest(OBJ_TYPE.CONV, newConv, [], obj_to, obj_to_id);
          }
        },
      );
    },
  );

  describe.each(arrayPermission)(
    '$description',
    ({
      privsFirstObject,
      privsSecondObject,
      privsProc,
      expectFirstObject,
      expectSecondObject,
      expectConvPerm,
    }): void => {
      test(`for Group and User`, async (): Promise<void> => {
        const sendRequest = async (obj: OBJ_TYPE, obj_id: string | number, privs: any): Promise<void> => {
          const obj_to = Math.random() < 0.5 ? OBJ_TYPE.USER : OBJ_TYPE.GROUP;
          const obj_to_id = obj_to === OBJ_TYPE.USER ? newApiKeyId : groupId;

          const response = await api.request(
            createRequestWithOps({
              type: REQUEST_TYPE.LINK,
              obj,
              obj_id,
              company_id,
              obj_to,
              obj_to_id,
              is_need_to_notify: false,
              privs,
            }),
          );

          expect(response.status).toBe(RESP_STATUS.OK);
        };

        await sendRequest(OBJ_TYPE.FOLDER, newFirstFolder, privsFirstObject);
        await sendRequest(OBJ_TYPE.FOLDER, newSecondFolder, privsSecondObject);
        await sendRequest(OBJ_TYPE.CONV, newConv, privsProc);

        const getPermission = await newCookie.request({
          url: `${urlPrivs}/${newFirstFolder}/${newApiKeyId}`,
          method: Method.GET,
        });
        const permissionsFirstFolder = (getPermission.data as Array<any>).find(item => item.obj_id === newFirstFolder);
        expect(permissionsFirstFolder.create).toBe(expectFirstObject.create);
        expect(permissionsFirstFolder.view).toBe(expectFirstObject.view);
        expect(permissionsFirstFolder.modify).toBe(expectFirstObject.modify);
        expect(permissionsFirstFolder.delete).toBe(expectFirstObject.delete);
        const permissionsSecondFolder = (getPermission.data as Array<any>).find(
          item => item.obj_id === newSecondFolder,
        );
        expect(permissionsSecondFolder.create).toBe(expectSecondObject.create);
        expect(permissionsSecondFolder.view).toBe(expectSecondObject.view);
        expect(permissionsSecondFolder.modify).toBe(expectSecondObject.modify);
        expect(permissionsSecondFolder.delete).toBe(expectSecondObject.delete);
        const permissionsConv = (getPermission.data as Array<any>).find(item => item.obj_id === newConv);
        expect(permissionsConv.create).toBe(expectConvPerm.create);
        expect(permissionsConv.view).toBe(expectConvPerm.view);
        expect(permissionsConv.modify).toBe(expectConvPerm.modify);
        expect(permissionsConv.delete).toBe(expectConvPerm.delete);

        const arr = [
          {
            type: REQUEST_TYPE.LINK,
            obj: OBJ_TYPE.FOLDER,
            obj_id: newFirstFolder,
            obj_to: OBJ_TYPE.USER,
            obj_to_id: newApiKeyId,
          },
          {
            type: REQUEST_TYPE.LINK,
            obj: OBJ_TYPE.FOLDER,
            obj_id: newFirstFolder,
            obj_to: OBJ_TYPE.GROUP,
            obj_to_id: groupId,
          },
          {
            type: REQUEST_TYPE.LINK,
            obj: OBJ_TYPE.FOLDER,
            obj_id: newSecondFolder,
            obj_to: OBJ_TYPE.USER,
            obj_to_id: newApiKeyId,
          },
          {
            type: REQUEST_TYPE.LINK,
            obj: OBJ_TYPE.FOLDER,
            obj_id: newSecondFolder,
            obj_to: OBJ_TYPE.GROUP,
            obj_to_id: groupId,
          },
          {
            type: REQUEST_TYPE.LINK,
            obj: OBJ_TYPE.CONV,
            obj_id: newConv,
            obj_to: OBJ_TYPE.USER,
            obj_to_id: newApiKeyId,
          },
          {
            type: REQUEST_TYPE.LINK,
            obj: OBJ_TYPE.CONV,
            obj_id: newConv,
            obj_to: OBJ_TYPE.GROUP,
            obj_to_id: groupId,
          },
        ];
        await Promise.all(
          arr.map(
            async (item): Promise<void> => {
              const responseLink = await api.request(
                createRequestWithOps({
                  type: item.type,
                  obj: item.obj,
                  obj_id: item.obj_id,
                  company_id,
                  obj_to: item.obj_to,
                  obj_to_id: item.obj_to_id,
                  is_need_to_notify: false,
                  privs: [],
                }),
              );
              expect(responseLink.status).toBe(RESP_STATUS.OK);
            },
          ),
        );
      });
    },
  );

  afterAll(
    async (): Promise<void> => {
      const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newFirstFolder, company_id);
      expect(response.status).toBe(RESP_STATUS.OK);
      const responseDelFirstUser = await requestDeleteObj(api, OBJ_TYPE.USER, newUserId, company_id);
      expect(responseDelFirstUser.status).toBe(RESP_STATUS.OK);
      const responseDelSecondtUser = await requestDeleteObj(api, OBJ_TYPE.USER, newApiKeyId, company_id);
      expect(responseDelSecondtUser.status).toBe(RESP_STATUS.OK);
      const responseDelGroup = await requestDeleteObj(api, OBJ_TYPE.GROUP, groupId, company_id);
      expect(responseDelGroup.status).toBe(RESP_STATUS.OK);
    },
  );
});
