import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { User } from '../../../../infrastructure/model/User';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  generateName,
} from '../../../../utils/corezoidRequest';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import { requestDeleteObj, requestCreateObjNew } from '../../../../application/api/ApiObj';
import { createAuthUser, Method } from '../../../../utils/request';
import { getArrayPermissionsCombinations } from '../../../../utils/generationPermission';
import { ApiClientResponse } from '../../../../application/api/ApiUserClient';

describe('Sharing', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let user: User;
  let groupId: number;
  let company_id: any;
  let project_short_name: string;
  let stage_short_name: string;
  let newConv: number;
  let newApiKeyId: string | number;
  let newUserId: string | number;
  let conv_type: string;
  let urlPrivs: string;
  let cookie: any;
  let arrayPermission: {
    description: string;
    privsFirstObject: { type: string; list_obj: string[] }[];
    privsSecondObject: { type: string; list_obj: string[] }[];
    privsProc: { type: string; list_obj: string[] }[];
    expectFirstObject: { create: boolean; view: boolean; modify: boolean; delete: boolean };
    expectSecondObject: { create: boolean; view: boolean; modify: boolean; delete: boolean };
    expectConvPerm: { create: boolean; view: boolean; modify: boolean; delete: boolean };
  }[] = [];

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = generateName(OBJ_TYPE.PROJECT);
    stage_short_name = generateName(OBJ_TYPE.STAGE);
    user = await application.getAuthorizedUser({ company: {} }, 1);
    cookie = createAuthUser(user.cookieUser, 'cookie');
    conv_type = 'process';
    urlPrivs = `${ConfigurationManager.getConfiguration().getUrl()}system/tests/privs_by_folder`;

    const CreateUserResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateUserResponse.status).toBe(RESP_STATUS.OK);
    const { body: bodyCUR } = CreateUserResponse as ApiClientResponse;
    newUserId = bodyCUR.ops[0].users[0].obj_id;

    const CreateKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(RESP_STATUS.OK);
    const { body: bodyCKR } = CreateKeyResponse as ApiClientResponse;
    newApiKeyId = bodyCKR.ops[0].users[0].obj_id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_type: 'admins',
        title: 'groupShare',
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    const { body: bodyCG } = response as ApiClientResponse;
    groupId = bodyCG.ops[0].obj_id;

    const responseLincUser = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: newApiKeyId,
        company_id,
        group_id: groupId,
        level: 1,
      }),
    );
    expect(responseLincUser.status).toBe(RESP_STATUS.OK);

    const responseProject = await requestCreateObjNew(
      api,
      OBJ_TYPE.PROJECT,
      company_id,
      generateName(OBJ_TYPE.PROJECT),
      {
        short_name: project_short_name,
        stages: [],
      },
    );
    newProject = responseProject.obj_id;

    const responseStage = await requestCreateObjNew(api, OBJ_TYPE.STAGE, company_id, generateName(OBJ_TYPE.STAGE), {
      short_name: stage_short_name,
      project_id: newProject,
    });
    newStage = responseStage.obj_id;

    const responseConv = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, generateName(OBJ_TYPE.CONV), {
      conv_type,
      stage_id: newStage,
      project_id: newProject,
    });
    newConv = responseConv.obj_id;
  });

  arrayPermission = getArrayPermissionsCombinations('project');

  describe.each(arrayPermission)(
    '$description',
    ({ privsFirstObject, privsSecondObject, privsProc, expectFirstObject, expectSecondObject, expectConvPerm }) => {
      const testCases = [
        {
          obj_to: OBJ_TYPE.GROUP,
          obj_to_id: (): string | number => groupId,
          object_id: (): string | number => newApiKeyId,
        },
        {
          obj_to: OBJ_TYPE.USER,
          obj_to_id: (): string | number => newUserId,
          object_id: (): string | number => newUserId,
        },
      ];

      const sendRequest = async (
        obj: OBJ_TYPE,
        obj_id: string | number,
        privs: any,
        obj_to: string,
        obj_to_id: Function,
      ): Promise<void> => {
        const response = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LINK,
            obj,
            obj_id,
            company_id,
            obj_to,
            obj_to_id: obj_to_id(),
            is_need_to_notify: false,
            privs,
          }),
        );
        expect(response.status).toBe(RESP_STATUS.OK);
      };
      test.each(testCases)('for $obj_to', async ({ obj_to, obj_to_id, object_id }) => {
        await sendRequest(OBJ_TYPE.PROJECT, newProject, privsFirstObject, obj_to, obj_to_id);
        await sendRequest(OBJ_TYPE.STAGE, newStage, privsSecondObject, obj_to, obj_to_id);
        await sendRequest(OBJ_TYPE.CONV, newConv, privsProc, obj_to, obj_to_id);

        const getPermissionProject = await cookie.request({
          url: `${urlPrivs}/${newProject}/${object_id()}`,
          method: Method.GET,
        });
        const permissionsProj = (getPermissionProject.data as Array<any>).find(item => item.obj_id === newProject);
        expect(permissionsProj.create).toBe(expectFirstObject.create);
        expect(permissionsProj.view).toBe(expectFirstObject.view);
        expect(permissionsProj.modify).toBe(expectFirstObject.modify);
        expect(permissionsProj.delete).toBe(expectFirstObject.delete);
        const permissionsStage = (getPermissionProject.data as Array<any>).find(item => item.obj_id === newStage);
        expect(permissionsStage.create).toBe(expectSecondObject.create);
        expect(permissionsStage.view).toBe(expectSecondObject.view);
        expect(permissionsStage.modify).toBe(expectSecondObject.modify);
        expect(permissionsStage.delete).toBe(expectSecondObject.delete);
        const permissionsConv = (getPermissionProject.data as Array<any>).find(item => item.obj_id === newConv);
        expect(permissionsConv.create).toBe(expectConvPerm.create);
        expect(permissionsConv.view).toBe(expectConvPerm.view);
        expect(permissionsConv.modify).toBe(expectConvPerm.modify);
        expect(permissionsConv.delete).toBe(expectConvPerm.delete);
      });

      afterAll(async () => {
        for (const { obj_to, obj_to_id } of testCases) {
          await sendRequest(OBJ_TYPE.PROJECT, newProject, [], obj_to, obj_to_id);
          await sendRequest(OBJ_TYPE.STAGE, newStage, [], obj_to, obj_to_id);
          await sendRequest(OBJ_TYPE.CONV, newConv, [], obj_to, obj_to_id);
        }
      });
    },
  );

  describe.each(arrayPermission)(
    '$description',
    ({ privsFirstObject, privsSecondObject, privsProc, expectFirstObject, expectSecondObject, expectConvPerm }) => {
      test(`for Group and User`, async (): Promise<void> => {
        const sendRequest = async (obj: OBJ_TYPE, obj_id: string | number, privs: any): Promise<void> => {
          const obj_to = Math.random() < 0.5 ? OBJ_TYPE.USER : OBJ_TYPE.GROUP;
          const obj_to_id = obj_to === OBJ_TYPE.USER ? newApiKeyId : groupId;

          const response = await api.request(
            createRequestWithOps({
              type: REQUEST_TYPE.LINK,
              obj,
              obj_id,
              company_id,
              obj_to,
              obj_to_id,
              is_need_to_notify: false,
              privs,
            }),
          );

          expect(response.status).toBe(RESP_STATUS.OK);
        };

        await sendRequest(OBJ_TYPE.PROJECT, newProject, privsFirstObject);
        await sendRequest(OBJ_TYPE.STAGE, newStage, privsSecondObject);
        await sendRequest(OBJ_TYPE.CONV, newConv, privsProc);

        const getPermissionProject = await cookie.request({
          url: `${urlPrivs}/${newProject}/${newApiKeyId}`,
          method: Method.GET,
        });
        const permissionsProj = (getPermissionProject.data as Array<any>).find(item => item.obj_id === newProject);
        expect(permissionsProj.create).toBe(expectFirstObject.create);
        expect(permissionsProj.view).toBe(expectFirstObject.view);
        expect(permissionsProj.modify).toBe(expectFirstObject.modify);
        expect(permissionsProj.delete).toBe(expectFirstObject.delete);
        const permissionsStage = (getPermissionProject.data as Array<any>).find(item => item.obj_id === newStage);
        expect(permissionsStage.create).toBe(expectSecondObject.create);
        expect(permissionsStage.view).toBe(expectSecondObject.view);
        expect(permissionsStage.modify).toBe(expectSecondObject.modify);
        expect(permissionsStage.delete).toBe(expectSecondObject.delete);
        const permissionsConv = (getPermissionProject.data as Array<any>).find(item => item.obj_id === newConv);
        expect(permissionsConv.create).toBe(expectConvPerm.create);
        expect(permissionsConv.view).toBe(expectConvPerm.view);
        expect(permissionsConv.modify).toBe(expectConvPerm.modify);
        expect(permissionsConv.delete).toBe(expectConvPerm.delete);

        const arr = [
          {
            type: REQUEST_TYPE.LINK,
            obj: OBJ_TYPE.PROJECT,
            obj_id: newProject,
            obj_to: OBJ_TYPE.USER,
            obj_to_id: newApiKeyId,
          },
          {
            type: REQUEST_TYPE.LINK,
            obj: OBJ_TYPE.PROJECT,
            obj_id: newProject,
            obj_to: OBJ_TYPE.GROUP,
            obj_to_id: groupId,
          },
          {
            type: REQUEST_TYPE.LINK,
            obj: OBJ_TYPE.STAGE,
            obj_id: newStage,
            obj_to: OBJ_TYPE.USER,
            obj_to_id: newApiKeyId,
          },
          {
            type: REQUEST_TYPE.LINK,
            obj: OBJ_TYPE.STAGE,
            obj_id: newStage,
            obj_to: OBJ_TYPE.GROUP,
            obj_to_id: groupId,
          },
          {
            type: REQUEST_TYPE.LINK,
            obj: OBJ_TYPE.CONV,
            obj_id: newConv,
            obj_to: OBJ_TYPE.USER,
            obj_to_id: newApiKeyId,
          },
          {
            type: REQUEST_TYPE.LINK,
            obj: OBJ_TYPE.CONV,
            obj_id: newConv,
            obj_to: OBJ_TYPE.GROUP,
            obj_to_id: groupId,
          },
        ];
        await Promise.all(
          arr.map(async item => {
            const responseLink = await api.request(
              createRequestWithOps({
                type: item.type,
                obj: item.obj,
                obj_id: item.obj_id,
                company_id,
                obj_to: item.obj_to,
                obj_to_id: item.obj_to_id,
                is_need_to_notify: false,
                privs: [],
              }),
            );
            expect(responseLink.status).toBe(RESP_STATUS.OK);
          }),
        );
      });
    },
  );

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(response.status).toBe(RESP_STATUS.OK);
    const responseDelFirstUser = await requestDeleteObj(api, OBJ_TYPE.USER, newUserId, company_id);
    expect(responseDelFirstUser.status).toBe(RESP_STATUS.OK);
    const responseDelSecondtUser = await requestDeleteObj(api, OBJ_TYPE.USER, newApiKeyId, company_id);
    expect(responseDelSecondtUser.status).toBe(RESP_STATUS.OK);
    const responseDelGroup = await requestDeleteObj(api, OBJ_TYPE.GROUP, groupId, company_id);
    expect(responseDelGroup.status).toBe(RESP_STATUS.OK);
  });
});
