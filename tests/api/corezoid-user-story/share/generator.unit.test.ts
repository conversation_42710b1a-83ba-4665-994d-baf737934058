import { getArrayPermissionsCombinations } from '../../../../utils/generationPermission';

const expectedNames = ['No Rights', 'View', 'Task', 'View + Task', 'View + Modify', 'View + Task + Modify'];

describe('getArrayPermissionsCombinations', () => {
  describe('Full Mode', () => {
    test('should return 216 combinations for type "project"', () => {
      const results = getArrayPermissionsCombinations('project', 'full');
      expect(results).toHaveLength(216);
    });

    test('should correctly form the description and expected permissions for the combination "View, View, View" (project)', () => {
      const results = getArrayPermissionsCombinations('project', 'full');
      const firstCombination = results[0];
      expect(firstCombination.description).toBe('Testing combination: Project: View, Stage: View, Process: View');
      const expectedPerm = { create: false, view: true, modify: false, delete: false };

      expect(firstCombination.expectFirstObject).toEqual(expectedPerm);
      expect(firstCombination.expectSecondObject).toEqual(expectedPerm);
      expect(firstCombination.expectConvPerm).toEqual(expectedPerm);
    });

    test('should handle mixed combination "Task, No Rights, View + Modify"', () => {
      const results = getArrayPermissionsCombinations('project', 'full');
      const combination = results.find(
        r => r.description === 'Testing combination: Project: Task, Stage: No Rights, Process: View + Modify',
      );
      expect(combination).toBeDefined();
      const expectedFirst = { create: true, view: false, modify: false, delete: false };
      const expectedSecond = { create: true, view: false, modify: false, delete: false };
      const expectedProc = { create: true, view: true, modify: true, delete: true };

      expect(combination?.expectFirstObject).toEqual(expectedFirst);
      expect(combination?.expectSecondObject).toEqual(expectedSecond);
      expect(combination?.expectConvPerm).toEqual(expectedProc);
    });
  });

  describe('Pairwise Mode', () => {
    test('should cover exactly 40 unique pairs of permission combinations and find missing pairs if any', () => {
      const results = getArrayPermissionsCombinations('project');
      expect(results.length).toBe(40);

      const projectStagePairs = new Set<string>();
      const projectProcessPairs = new Set<string>();
      const stageProcessPairs = new Set<string>();

      const regex = /Project:\s*([^,]+),\s*Stage:\s*([^,]+),\s*Process:\s*(.+)$/;

      results.forEach(result => {
        const match = result.description.match(regex);
        if (!match) {
          throw new Error(`Description does not match expected pattern: ${result.description}`);
        }
        const [, projectName, stageName, processName] = match;
        projectStagePairs.add(`${projectName.trim()}|${stageName.trim()}`);
        projectProcessPairs.add(`${projectName.trim()}|${processName.trim()}`);
        stageProcessPairs.add(`${stageName.trim()}|${processName.trim()}`);
      });

      expectedNames.forEach(first => {
        expectedNames.forEach(second => {
          expect(projectStagePairs.has(`${first}|${second}`)).toBe(true);

          expect(projectProcessPairs.has(`${first}|${second}`)).toBe(true);

          expect(stageProcessPairs.has(`${first}|${second}`)).toBe(true);
        });
      });
    });
  });

  describe('Labels', () => {
    test('should use the correct labels for type "folder"', () => {
      const results = getArrayPermissionsCombinations('folder', 'full');
      const firstCombination = results[0];
      expect(firstCombination.description).toContain('Folder:');
      expect(firstCombination.description).toContain('Process');
    });

    describe('Parameter Validation', () => {
      test('should throw an error if firstType is missing', () => {
        expect(() => getArrayPermissionsCombinations(undefined as any, 'pairwise')).toThrow(
          "Invalid parameters: 'firstType' and 'mode' are required.",
        );
      });

      test('should throw an error if mode is missing', () => {
        expect(() => getArrayPermissionsCombinations('project', null as any)).toThrow(
          "Invalid parameters: 'firstType' and 'mode' are required.",
        );
      });

      test('should throw an error if both firstType and mode are missing', () => {
        expect(() => getArrayPermissionsCombinations(undefined as any, undefined as any)).toThrow(
          "Invalid parameters: 'firstType' and 'mode' are required.",
        );
      });
    });
  });
});
