# Compare/Merge Functionality Checklist

This checklist covers various scenarios and tests for the Compare/Merge functionality based on the existing tests.

## Source Types for Compare/Merge

- [x] Compare/Merge from Stage to Stage with empty target (stage_version_to_empty_stage_matrix.test.ts, stage_cross_to_empty_stage_matrix.test.ts)
- [x] Compare/Merge from Stage to Stage with populated target (stage_version_to_stage_*_param_matrix.test.ts)
- [x] Compare/Merge from Version to Stage with empty target (stage_version_to_empty_stage_matrix.test.ts)
- [x] Compare/Merge from Version to Stage with populated target (stage_version_to_stage_*_param_matrix.test.ts)
- [x] Compare/Merge from File to Stage (file_to_stage_parameters_sheme_with_id.test.ts)
- [x] Compare/Merge with Cross-Project references (compare_merge_from_file_cross-project.test.ts)

## Parameter Handling

### Critical Parameters
All tests verify correct handling of three critical parameters:
- [x] stage_id
- [x] project_id
- [x] conv_id

### Stage Parameters Scenarios

#### Numbers-based Parameters
- [x] All parameters as numbers (stage_id, project_id, conv_id as numeric IDs) (stage_version_to_stage_id_param_matrix.test.ts)
- [x] Verify parameters correctly update in prod stage after merge
- [x] Verify references maintain integrity after multiple merges
- [x] Missing stage_id/project_id with numeric conv_id (stage_version_to_stage_numeric_conv_param_matrix.test.ts)

#### Mixed Parameters
- [x] stage_id/project_id as numbers, but conv_id as alias (stage_version_to_stage_alias_param_matrix.test.ts)
- [x] Verify alias resolution works correctly in prod environment
- [x] Verify numeric parameters properly update while maintaining alias reference
- [x] Missing stage_id/project_id with alias conv_id (stage_version_to_stage_alias_conv_param_matrix.test.ts)

#### Dynamic Construction Parameters
- [x] Conv_id using dynamic construction syntax (stage_version_to_stage_dynamic_conv_numeric_param_matrix.test.ts)
- [x] Conv_id/stage_id using dynamic construction syntax (stage_version_to_stage_dynamic_convstage_param_matrix.test.ts)
- [x] All parameters using dynamic construction syntax (stage_version_to_stage_dynamic_param_matrix.test.ts)
- [x] Verify construction correctly resolves in the target environment
- [x] Verify dynamic construction maintains proper references after merge

#### Cross-Stage References
- [x] Cross-stage references in the same project (stage_cross_stage_reference_id.test.ts)
  - [x] Cross-stage reference by direct ID in API Copy logic
  - [x] Cross-stage reference by alias in API Copy logic
  - [x] Cross-stage reference through dynamic expression in API Copy logic
  - [x] Cross-stage reference with dynamic stage_id in API Copy logic
  - [x] Cross-stage reference with all parameters as dynamic expressions
- [x] Cross-stage references with dynamic configuration in production (stage_cross_stage_reference_dinamic.test.ts)
- [x] Cross-stage references with empty production to dynamic configuration transition (stage_cross_stage_reference_empty_to_dynamic.test.ts)
- [x] Cross-stage references to a separate reference stage (stage_cross_stage_reference_ref2.test.ts)

#### Matrix of Parameter Combinations
- [x] ID-based parameters for all fields (stage_version_to_stage_id_param_matrix.test.ts)
- [x] Alias for conv_id, IDs for stage_id/project_id (stage_version_to_stage_alias_param_matrix.test.ts)
- [x] Dynamic construction for conv_id, IDs for stage_id/project_id (stage_version_to_stage_dynamic_conv_numeric_param_matrix.test.ts)
- [x] Dynamic construction for conv_id/stage_id, IDs for project_id (stage_version_to_stage_dynamic_convstage_param_matrix.test.ts)
- [x] All fields as dynamic construction (stage_version_to_stage_dynamic_param_matrix.test.ts)
- [x] Conv_id as alias without stage_id/project_id (stage_version_to_stage_alias_conv_param_matrix.test.ts)
- [x] Conv_id as ID without stage_id/project_id (stage_version_to_stage_numeric_conv_param_matrix.test.ts)
- [x] Conv_id as dynamic construction without stage_id/project_id (stage_version_to_stage_dynamic_param_matrix.test.ts)

## Special Test Cases

- [x] Parameter replacement testing (replacement.test.ts)
- [x] Combined parameters testing (combined_parameters.test.ts)
- [x] Task parameters during copying (copy_task_parameters.test.ts)
- [x] Parameters during file to stage merge (file_to_stage_parameters_sheme_with_id.test.ts)
- [x] Structural elements testing (construct.test.ts)
- [x] Rollback functionality after merge (rollback_after_merge_from_file_cross-project.test.ts, rollback_after_merge_using_systemAPI.test.ts)

## Production Stage Configurations

- [x] Empty production environment - testing first merge scenario (stage_version_to_empty_stage_matrix.test.ts)
- [x] Pre-configured production with static parameters (all matrix tests)
- [x] Pre-configured production with dynamic parameters (stage_cross_stage_reference_dinamic.test.ts)
- [x] Transition from empty to dynamic production configuration (stage_cross_stage_reference_empty_to_dynamic.test.ts)
- [x] Production with references to different stages (stage_cross_stage_reference_ref2.test.ts)

## Data Integrity Verification

- [x] Object status verification after comparison (added/changed/deleted)
- [x] Change statistics verification (num_stat)
- [x] Correct parent_id verification for objects
- [x] Verification of alias and object relationship preservation
- [x] Verification of API Copy logic operation after merge
- [x] Cross-stage reference integrity verification
- [x] Cross-project reference integrity verification

## Merge Behavior

- [x] Stage → Stage merge with empty target stage
- [x] Stage → Stage merge with populated target stage
- [x] Version → Stage merge with empty target stage
- [x] Version → Stage merge with populated target stage
- [x] Stage → Stage merge with cross-stage references
- [x] Version → Stage merge with cross-stage references
- [x] Async parameter verification during merge (false)
- [x] Apply_mode parameter verification during merge (true)
- [x] Rollback capabilities after merge

## Edge Cases

- [x] Handle non-existent referenced objects
- [x] Handle circular references 
- [x] Handle deleted and restored objects
- [x] Handle empty or null parameter values (stage_version_to_stage_numeric_conv_param_matrix.test.ts, stage_version_to_stage_alias_conv_param_matrix.test.ts)
- [x] Handle invalid construction syntax
- [x] Handle cross-project references 
- [x] Handle transitions between configuration types (empty to dynamic)

## Testing Workflow

For each parameter combination scenario:
1. [x] Set up initial environment with project, stages, processes, and aliases
2. [x] Configure API Copy logic parameters according to scenario
3. [x] Create a version of development stage for version testing
4. [x] Perform Compare operation between source and target
5. [x] Verify Compare results match expected status and statistics
6. [x] Perform Merge operation from source to target
7. [x] Verify parameters were correctly transformed in target environment
8. [x] Use enhanced verification helpers to check complex alias and dynamic parameter resolutions

## Implementation Support

- [x] API endpoints correctly process and transform parameters
- [x] Alias resolution works across different stages
- [x] Construction syntax properly resolves in all contexts
- [x] Different parameter types (numbers, strings, aliases, constructions) are all supported
- [x] Error handling for invalid parameters
- [x] Proper data validation before operations
- [x] Support for cross-stage and cross-project references

## Test Environment Management

- [x] Correct test data initialization with controlled environment
- [x] Proper cleanup after tests through project deletion
- [x] Correct immutable flag management for stages
- [x] Isolated test execution with unique generated names
- [x] Consistent utility functions for environment preparation and verification