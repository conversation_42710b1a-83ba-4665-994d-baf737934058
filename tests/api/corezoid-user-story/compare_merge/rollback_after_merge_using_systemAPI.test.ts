import { debug } from '../../../../support/utils/logger';
import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../../infrastructure/model/ApiKey';
import { NODE_TYPE, NODE_LOGIC_TYPE } from '../../../../application/api/obj_types/node';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { requestDeleteObj, requestListConv } from '../../../../application/api/ApiObj';
import { User } from '../../../../infrastructure/model/User';
import { createAuthUser, Method } from '../../../../utils/request';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';
import { addMsg } from 'jest-html-reporters/helper';

describe('should get a merge error', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let project_id: string | number;
  let stageDevelop_id: string | number;
  let stageOther_id: string | number;
  let prodEmpty_id: string | number;
  let listOS: any;
  let listTS: any;
  let key: string;
  let secret: string;
  let convOtherStage: number;
  let convThisStage: number;
  let convEmpty: number;
  let processCOS1: string | number;
  let processCOS2: string | number;
  let processCOSFinal: string | number;
  let processCTS1: string | number;
  let processCTS2: string | number;
  let processCTSFinal: string | number;
  let cookieSA: any;
  let user: User;
  const systemAPI = `${ConfigurationManager.getConfiguration().getUrl()}system/tests/force_immutable_stage`;

  function parseStdoutToJson(stdout: any): any {
    const jsonStartIndex = stdout.indexOf('{');
    const jsonString = stdout.slice(jsonStartIndex);
    return JSON.parse(jsonString);
  }

  async function modifyNode(
    api: ApiKeyClient,
    nodeId: string | number,
    convId: number,
    toNodeId: string | number,
    copyToConvId: string | number | string,
    stageId: string | number,
  ): Promise<void> {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeId,
        conv_id: convId,
        obj_type: NODE_TYPE.Normal,
        logics: [
          {
            type: NODE_LOGIC_TYPE.ApiCopy,
            mode: 'create',
            data: {},
            data_type: {},
            group: 'all',
            ref: '1',
            conv_id: copyToConvId,
            stage_id: stageId,
            project_id,
            err_node_id: '',
          },
          { to_node_id: toNodeId, type: NODE_LOGIC_TYPE.Go, node_title: 'final' },
        ],
        semaphors: [],
        version: 2,
      }),
    );

    expect(response.status).toBe(RESP_STATUS.OK);
  }

  async function copyObject(
    api: ApiKeyClient,
    sourceObjectId: string | number,
    targetStageId: string | number,
    title: string,
    companyId: any,
  ): Promise<any> {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: sourceObjectId,
        obj_type: OBJ_TYPE.CONV,
        obj_to_id: targetStageId,
        title: title,
        ignore_errors: true,
        obj_to_type: OBJ_TYPE.STAGE,
        async: true,
        from_company_id: companyId,
        to_company_id: companyId,
      }),
    );

    expect(response.status).toBe(RESP_STATUS.OK);
    return response;
  }

  async function callSystemAPI(cookieSA: any, stageId: string | number, convIds: string): Promise<any> {
    const response = await cookieSA.request({
      url: `${systemAPI}/${stageId}/${convIds}`,
      method: Method.GET,
    });
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.result).toBe(PROC_STATUS.OK);
    return response;
  }

  async function performRollback(api: ApiKeyClient, stageId: string | number): Promise<any> {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        obj_id: stageId,
        obj_type: OBJ_TYPE.STAGE,
        version: 2,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);
    return response;
  }

  async function requestStageContent(
    api: ApiKeyClient,
    stageId: string | number,
    projectId: string | number,
    companyId: any,
  ): Promise<any> {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        company_id: companyId,
        obj_id: stageId,
        project_id: projectId,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0]).toHaveProperty('proc', PROC_STATUS.OK);
    return response;
  }

  async function verifyRollbackResults(
    api: ApiKeyClient,
    convOtherStage: number,
    convThisStage: number,
    listOS: any,
    listTS: any,
    companyId: any,
    projectId: string | number,
    stageId: string | number,
  ): Promise<void> {
    const responseListConvOSAfterRollback = await requestListConv(api, convOtherStage, companyId, projectId, stageId);
    expect(responseListConvOSAfterRollback.status).toBe(RESP_STATUS.OK);
    expect(responseListConvOSAfterRollback.body.ops[0]).toHaveProperty('list');
    const listOSAfterRollback = responseListConvOSAfterRollback.body.ops[0].list;

    const responseListConvTSAfterRollback = await requestListConv(api, convThisStage, companyId, projectId, stageId);
    expect(responseListConvTSAfterRollback.status).toBe(RESP_STATUS.OK);
    expect(responseListConvTSAfterRollback.body.ops[0]).toHaveProperty('list');
    const listTSAfterRollback = responseListConvTSAfterRollback.body.ops[0].list;

    expect(listOSAfterRollback).toHaveLength(listOS.length);
    expect(listTSAfterRollback).toHaveLength(listTS.length);

    const sortedListOS = [...listOS].sort((firstNode: any, secondNode: any) =>
      firstNode.obj_id.localeCompare(secondNode.obj_id),
    );
    const sortedListOSAfterRollback = [...listOSAfterRollback].sort((firstNode: any, secondNode: any) =>
      firstNode.obj_id.localeCompare(secondNode.obj_id),
    );

    const sortedListTS = [...listTS].sort((firstNode: any, secondNode: any) =>
      firstNode.obj_id.localeCompare(secondNode.obj_id),
    );
    const sortedListTSAfterRollback = [...listTSAfterRollback].sort((firstNode: any, secondNode: any) =>
      firstNode.obj_id.localeCompare(secondNode.obj_id),
    );

    expect(sortedListOS).toEqual(sortedListOSAfterRollback);
    expect(sortedListTS).toEqual(sortedListTSAfterRollback);
  }

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;
      key = apikey.key;
      secret = apikey.secret;
      user = await application.getAuthorizedUser({ company: {} }, 1);
      cookieSA = createAuthUser(user.cookieUser, 'cookie');

      const exec = promisify(execCallback);
      const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
      const scriptPath = 'tests/api/corezoid-api/sh/uploadObjectRootFolder.sh';

      const { stdout } = await exec(scriptPath, {
        env: {
          API_KEY_LOGIN: key,
          API_KEY_SECRET: secret,
          API_URL: baseUrl + 'api/2/upload',
          VALIDATE_SCHEME: 'false',
          REWRITE_ALIAS: 'false',
          WITH_ALIAS: 'true',
          COMPANY_ID: company_id,
          FILE_NAME: 'project_test_Rollback.zip',
          ASYNC: 'true',
        },
      });
      debug(stdout);
      const jsonResponse = parseStdoutToJson(stdout);
      addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
      expect(jsonResponse.request_proc).toBe(`ok`);

      await new Promise(r => setTimeout(r, 10000));

      const projectsList = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.PROJECTS,
          obj_id: 0,
          company_id,
          id: company_id,
          order: 'asc',
          sort: 'date',
        }),
      );
      expect(projectsList.status).toBe(200);
      const { list: listSetParam } = projectsList.body.ops[0];
      project_id = (listSetParam as Array<any>).find(item => item.short_name === 'projfortestrollback').project_id;

      const stageList = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.PROJECT,
          obj_id: project_id,
          company_id,
          id: company_id,
          order: 'asc',
          sort: 'date',
        }),
      );
      expect(stageList.status).toBe(200);
      const { list: stagesList } = stageList.body.ops[0];
      stageDevelop_id = (stagesList as Array<any>).find(item => item.short_name === 'develop').obj_id;
      stageOther_id = (stagesList as Array<any>).find(item => item.short_name === 'other').obj_id;
      prodEmpty_id = (stagesList as Array<any>).find(item => item.short_name === 'prodempty').obj_id;

      const stageContentList = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: stageDevelop_id,
          project_id,
        }),
      );
      expect(stageContentList.status).toBe(RESP_STATUS.OK);
      const { proc, list } = stageContentList.body.ops[0];
      expect(proc).toEqual(PROC_STATUS.OK);
      convEmpty = (list as Array<any>).find(item => item.title === 'empty').obj_id;
      convOtherStage = (list as Array<any>).find(item => item.title === 'copyOtherStage').obj_id;
      convThisStage = (list as Array<any>).find(item => item.title === 'copyThisStage').obj_id;

      const responseListConvOS = await requestListConv(api, convOtherStage, company_id, project_id, stageDevelop_id);
      expect(responseListConvOS.status).toBe(RESP_STATUS.OK);
      listOS = responseListConvOS.body.ops[0].list;
      processCOS1 = (listOS as Array<any>).find(item => item.title === 'p/s/c').obj_id;
      processCOS2 = (listOS as Array<any>).find(item => item.title === 'p/s/alias').obj_id;
      processCOSFinal = (listOS as Array<any>).find(item => item.title === 'Final').obj_id;

      const responseListConvTS = await requestListConv(api, convThisStage, company_id, project_id, stageDevelop_id);
      expect(responseListConvTS.status).toBe(RESP_STATUS.OK);
      listTS = responseListConvTS.body.ops[0].list;
      processCTS1 = (listTS as Array<any>).find(item => item.title === 'p/s/conv').obj_id;
      processCTS2 = (listTS as Array<any>).find(item => item.title === 'p/s/alias').obj_id;
      processCTSFinal = (listTS as Array<any>).find(item => item.title === 'Final').obj_id;
    },
  );

  test('modify nodes in Develop stage -> using systemAPI before rollback', async (): Promise<void> => {
    await modifyNode(api, processCOS1, convOtherStage, processCOSFinal, convEmpty, stageDevelop_id);
    await modifyNode(api, processCOS2, convOtherStage, processCOSFinal, '@aliasotherstage', stageOther_id);
    await modifyNode(api, processCTS1, convThisStage, processCTSFinal, convEmpty, stageDevelop_id);
    await modifyNode(api, processCTS2, convThisStage, processCTSFinal, '@aliasempty', stageDevelop_id);

    await callSystemAPI(cookieSA, stageDevelop_id, `${convOtherStage},${convThisStage}`);
    await performRollback(api, stageDevelop_id);

    const responseList = await requestStageContent(api, stageDevelop_id, project_id, company_id);
    const { list } = responseList.body.ops[0];
    convOtherStage = (list as Array<any>).find(item => item.title === 'copyOtherStage').obj_id;
    convThisStage = (list as Array<any>).find(item => item.title === 'copyThisStage').obj_id;

    await verifyRollbackResults(
      api,
      convOtherStage,
      convThisStage,
      listOS,
      listTS,
      company_id,
      project_id,
      stageDevelop_id,
    );
  });

  test('modify nodes in Empty stage -> using systemAPI before rollback', async (): Promise<void> => {
    const responseModifyStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.STAGE,
        obj_id: prodEmpty_id,
        immutable: false,
      }),
    );
    expect(responseModifyStage.status).toBe(RESP_STATUS.OK);
    expect(responseModifyStage.body.ops[0].obj).toBe(OBJ_TYPE.STAGE);

    await copyObject(api, convEmpty, prodEmpty_id, 'empty', company_id);
    await copyObject(api, convOtherStage, prodEmpty_id, 'copyOtherStage', company_id);
    await copyObject(api, convThisStage, prodEmpty_id, 'copyThisStage', company_id);

    await new Promise(r => setTimeout(r, 3000));

    const stageContentList = await requestStageContent(api, prodEmpty_id, project_id, company_id);
    const { list } = stageContentList.body.ops[0];
    convEmpty = (list as Array<any>).find(item => item.title === 'empty').obj_id;
    convOtherStage = (list as Array<any>).find(item => item.title === 'copyOtherStage').obj_id;
    convThisStage = (list as Array<any>).find(item => item.title === 'copyThisStage').obj_id;

    const responseListConvOS = await requestListConv(api, convOtherStage, company_id, project_id, prodEmpty_id);
    expect(responseListConvOS.status).toBe(RESP_STATUS.OK);
    listOS = responseListConvOS.body.ops[0].list;
    processCOS1 = (listOS as Array<any>).find(item => item.title === 'p/s/c').obj_id;
    processCOS2 = (listOS as Array<any>).find(item => item.title === 'p/s/alias').obj_id;
    processCOSFinal = (listOS as Array<any>).find(item => item.title === 'Final').obj_id;

    const responseListConvTS = await requestListConv(api, convThisStage, company_id, project_id, prodEmpty_id);
    expect(responseListConvTS.status).toBe(RESP_STATUS.OK);
    listTS = responseListConvTS.body.ops[0].list;
    processCTS1 = (listTS as Array<any>).find(item => item.title === 'p/s/conv').obj_id;
    processCTS2 = (listTS as Array<any>).find(item => item.title === 'p/s/alias').obj_id;
    processCTSFinal = (listTS as Array<any>).find(item => item.title === 'Final').obj_id;

    await modifyNode(api, processCOS1, convOtherStage, processCOSFinal, convEmpty, stageDevelop_id);
    await modifyNode(api, processCOS2, convOtherStage, processCOSFinal, '@aliasotherstage', stageOther_id);
    await modifyNode(api, processCTS1, convThisStage, processCTSFinal, convEmpty, stageDevelop_id);
    await modifyNode(api, processCTS2, convThisStage, processCTSFinal, '@aliasempty', stageDevelop_id);

    await callSystemAPI(cookieSA, prodEmpty_id, `${convOtherStage},${convThisStage}`);
    await performRollback(api, prodEmpty_id);

    const emptyContentAfter = await requestStageContent(api, prodEmpty_id, project_id, company_id);
    const { list: listAfter } = emptyContentAfter.body.ops[0];
    convOtherStage = (listAfter as Array<any>).find(item => item.title === 'copyOtherStage').obj_id;
    convThisStage = (listAfter as Array<any>).find(item => item.title === 'copyThisStage').obj_id;

    await verifyRollbackResults(
      api,
      convOtherStage,
      convThisStage,
      listOS,
      listTS,
      company_id,
      project_id,
      prodEmpty_id,
    );
  });

  afterAll(
    async (): Promise<void> => {
      const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, project_id, company_id);
      expect(response.status).toBe(RESP_STATUS.OK);
    },
  );
});
