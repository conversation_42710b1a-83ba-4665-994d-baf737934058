import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
  generateName,
} from '../../../../utils/corezoidRequest';
import {
  requestConfirm,
  requestDeleteObj,
  requestListConv,
  requestCreateObjNew,
} from '../../../../application/api/ApiObj';
import { NODE_TYPE, NODE_LOGIC_TYPE } from '../../../../application/api/obj_types/node';
import { CreateAliasResponse, LinkAliasResponse } from '../../../../application/api/obj_types/alias';
import { CompareResponse, MergeResponse } from '../../../../application/api/obj_types/obj_scheme';
import { verifyApiCopyParameters } from './utils/compare_merge_utils';

describe('Compare/Merge Stage and Version Matrix', (): void => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let newConvCopyProd: number;
  let newSDProd: number;
  let project_id: string | number;
  let stageDev_id: string | number;
  let stageProd_id1: string | number;
  let stageProd_id2: string | number;
  let stageRef_id: string | number;
  let project_short_name: string;
  let title: string;
  let conv_type: string;
  let copy_process: number;
  let diagram_process: number;
  let process_node_ID_copy: string | number;
  let final_node_ID_copy: string | number;
  let short_name_al_sd: string | number;
  let alias_sd: string | number;
  let referens: string;
  let version_id: string | number;
  let processCopy: string;
  let stateDiagram: string;

  // Reference stage processes (targets for cross-stage references)
  let ref_short_name_alias: string | number;
  let ref_alias_id: string | number;
  let ref_state_process: number;

  // Helper function to clean production stage
  async function cleanProductionStage(stageId: string | number): Promise<void> {
    // First, set production stage to non-immutable
    const responseStageModify = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.STAGE,
        obj_id: stageId,
        company_id,
        project_id,
        immutable: false,
      }),
    );
    expect(responseStageModify.status).toBe(RESP_STATUS.OK);

    // Get list of aliases in production stage
    const responseAliases = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id,
        stage_id: stageId,
      }),
    );

    // Delete each alias found in production stage
    if (responseAliases.status === RESP_STATUS.OK) {
      const aliasList = responseAliases.body.ops[0].list;

      if (aliasList && Array.isArray(aliasList)) {
        for (const item of aliasList) {
          await api.request(
            createRequestWithOps({
              type: REQUEST_TYPE.DELETE,
              obj: OBJ_TYPE.ALIAS,
              obj_id: item.obj_id,
              company_id,
              project_id,
              stage_id: stageId,
            }),
          );
        }
      }
    }

    // Get list of objects in production stage
    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: stageId,
        project_id,
        stage_id: stageId,
      }),
    );
    if (responseList.status === RESP_STATUS.OK) {
      const list = responseList.body.ops[0].list;

      // Delete each object found in production stage
      if (list && Array.isArray(list)) {
        for (const item of list) {
          await api.request(
            createRequestWithOps({
              type: REQUEST_TYPE.DELETE,
              obj: OBJ_TYPE.CONV,
              obj_id: item.obj_id,
              company_id,
              project_id,
              stage_id: stageId,
            }),
          );
        }
      }
    }

    // Set production stage back to immutable
    const responseStageModifyBack = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.STAGE,
        obj_id: stageId,
        company_id,
        project_id,
        immutable: true,
      }),
    );
    expect(responseStageModifyBack.status).toBe(RESP_STATUS.OK);
  }

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;
      project_short_name = generateName(OBJ_TYPE.PROJECT);
      title = generateName(OBJ_TYPE.OBJS);
      short_name_al_sd = generateName(OBJ_TYPE.ALIAS);
      ref_short_name_alias = generateName(OBJ_TYPE.ALIAS);
      conv_type = 'process';
      referens = 'ref0000';
      processCopy = generateName(OBJ_TYPE.OBJS);
      stateDiagram = generateName(OBJ_TYPE.OBJS);

      // Create project with two production stages and a develop stage
      const responseProject = await requestCreateObjNew(
        api,
        OBJ_TYPE.PROJECT,
        company_id,
        generateName(OBJ_TYPE.PROJECT),
        {
          short_name: project_short_name,
          stages: [
            { title: 'production1', immutable: true },
            { title: 'develop', immutable: false },
            { title: 'production2', immutable: true },
            { title: 'reference', immutable: false },
          ],
        },
      );
      const { obj_id: objIdProject, stages: arrStages } = responseProject.body.ops[0];
      expect(responseProject.status).toBe(RESP_STATUS.OK);
      project_id = objIdProject;
      stageProd_id1 = arrStages[0];
      stageDev_id = arrStages[1];
      stageProd_id2 = arrStages[2];
      stageRef_id = arrStages[3];

      // Create a process in develop stage
      const responseCopy = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, processCopy, {
        conv_type,
        stage_id: stageDev_id,
        project_id,
      });
      copy_process = responseCopy.obj_id;
      const responseListSetParam = await requestListConv(api, copy_process, company_id);
      const { list: listSetParam } = responseListSetParam.body.ops[0];
      process_node_ID_copy = (listSetParam as Array<any>).find((item): boolean => item.title === conv_type).obj_id;
      final_node_ID_copy = (listSetParam as Array<any>).find((item): boolean => item.title === 'final').obj_id;

      // Create a state diagram in develop stage
      const responseDiagram = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, stateDiagram, {
        conv_type: 'state',
        stage_id: stageDev_id,
        project_id,
      });
      diagram_process = responseDiagram.obj_id;

      // Create alias in develop stage
      const responseAliasSD = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          short_name: short_name_al_sd,
          title,
          project_id,
          stage_id: stageDev_id,
        }),
      );
      expect(responseAliasSD.status).toBe(RESP_STATUS.OK);
      const { ops: opsCreateAliasSD } = responseAliasSD.body as CreateAliasResponse;
      alias_sd = opsCreateAliasSD[0].obj_id;

      const responseLinkSD = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.ALIAS,
          obj_id: alias_sd,
          company_id,
          link: true,
          obj_to_type: OBJ_TYPE.CONV,
          obj_to_id: diagram_process,
          project_id,
          stage_id: stageDev_id,
        }),
      );
      expect(responseLinkSD.status).toBe(RESP_STATUS.OK);
      const { ops: opsLinkAliasSD } = responseLinkSD.body as LinkAliasResponse;
      expect(opsLinkAliasSD[0].proc).toEqual(PROC_STATUS.OK);

      // Create a state diagram in ref stage
      const responseDiagramRef = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, stateDiagram, {
        conv_type: 'state',
        stage_id: stageRef_id,
        project_id,
      });
      ref_state_process = responseDiagramRef.obj_id;

      // Create alias in develop stage
      const responseAliasSDRef = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          short_name: ref_short_name_alias,
          title,
          project_id,
          stage_id: stageRef_id,
        }),
      );
      expect(responseAliasSDRef.status).toBe(RESP_STATUS.OK);
      const { ops: opsCreateAliasSDRef } = responseAliasSDRef.body as CreateAliasResponse;
      ref_alias_id = opsCreateAliasSDRef[0].obj_id;

      const responseLinkSDRef = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.ALIAS,
          obj_id: ref_alias_id,
          company_id,
          link: true,
          obj_to_type: OBJ_TYPE.CONV,
          obj_to_id: ref_state_process,
          project_id,
          stage_id: stageRef_id,
        }),
      );
      expect(responseLinkSDRef.status).toBe(RESP_STATUS.OK);
      const { ops: opsLinkAliasSDRef } = responseLinkSDRef.body as LinkAliasResponse;
      expect(opsLinkAliasSDRef[0].proc).toEqual(PROC_STATUS.OK);
    },
  );

  // Define test scenarios for different parameter types
  const testScenarios = [
    {
      name: 'All numeric IDs',
      conv_id: (): string | number => ref_state_process,
      stage_id: (): string | number => stageRef_id,
      project_id: (): string | number => project_id,
      newConv_id: (): string | number => ref_state_process,
      newStage_id1: (): string | number => stageRef_id,
      newStage_id2: (): string | number => stageRef_id,
      newProject_id: (): string | number => project_id,
      num_statConv: (): { added: number; changed: number; deleted: number } => ({ added: 1, changed: 0, deleted: 0 }),
      num_statAlias: (): { added: number; changed: number; deleted: number } => ({ added: 1, changed: 0, deleted: 0 }),
      num_statDiagram: (): { added: number; changed: number; deleted: number } => ({
        added: 1,
        changed: 0,
        deleted: 0,
      }),
      statusConv: (): string => 'added',
      statusAlias: (): string => 'added',
      statusDiagram: (): string => 'added',
    },
    {
      name: 'Alias for conv_id, numeric stage_id/project_id',
      conv_id: (): string => `@${ref_short_name_alias}`,
      stage_id: (): string | number => stageRef_id,
      project_id: (): string | number => project_id,
      newConv_id: (): string => `@${ref_short_name_alias}`,
      newStage_id1: (): string | number => stageRef_id,
      newStage_id2: (): string | number => stageRef_id,
      newProject_id: (): string | number => project_id,
      num_statConv: (): { added: number; changed: number; deleted: number } => ({ added: 1, changed: 0, deleted: 0 }),
      num_statAlias: (): { added: number; changed: number; deleted: number } => ({ added: 1, changed: 0, deleted: 0 }),
      num_statDiagram: (): { added: number; changed: number; deleted: number } => ({
        added: 1,
        changed: 0,
        deleted: 0,
      }),
      statusConv: (): string => 'added',
      statusAlias: (): string => 'added',
      statusDiagram: (): string => 'added',
    },
    {
      name: 'Dynamic conv_id, numeric stage_id/project_id',
      conv_id: (): string => `{{conv[${diagram_process}].ref[${referens}].conv}}`,
      stage_id: (): string | number => stageRef_id,
      project_id: (): string | number => project_id,
      newConv_id: (): string => `{{conv[${newSDProd}].ref[${referens}].conv}}`,
      newStage_id1: (): string | number => stageRef_id,
      newStage_id2: (): string | number => stageRef_id,
      newProject_id: (): string | number => project_id,
      num_statConv: (): { added: number; changed: number; deleted: number } => ({ added: 1, changed: 0, deleted: 0 }),
      num_statAlias: (): { added: number; changed: number; deleted: number } => ({ added: 1, changed: 0, deleted: 0 }),
      num_statDiagram: (): { added: number; changed: number; deleted: number } => ({
        added: 1,
        changed: 0,
        deleted: 0,
      }),
      statusConv: (): string => 'added',
      statusAlias: (): string => 'added',
      statusDiagram: (): string => 'added',
    },
  ];

  describe.each(testScenarios)('Testing with $name', (scenario): void => {
    beforeAll(
      async (): Promise<void> => {
        const responseModifCopy = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MODIFY,
            obj: OBJ_TYPE.NODE,
            obj_id: process_node_ID_copy,
            conv_id: copy_process,
            title: 'proccesCopy',
            obj_type: NODE_TYPE.Normal,
            logics: [
              {
                type: NODE_LOGIC_TYPE.ApiCopy,
                mode: 'create',
                data: {},
                data_type: {},
                group: 'all',
                ref: '',
                send_parent_data: false,
                conv_id: scenario.conv_id(),
                stage_id: scenario.stage_id(),
                project_id: scenario.project_id(),
                err_node_id: '',
              },
              { to_node_id: final_node_ID_copy, type: NODE_LOGIC_TYPE.Go, node_title: 'final' },
            ],
            semaphors: [],
            version: 22,
          }),
        );
        expect(responseModifCopy.status).toBe(RESP_STATUS.OK);

        const responseCommitCopy = await requestConfirm(api, copy_process, company_id);
        expect(responseCommitCopy.status).toBe(RESP_STATUS.OK);

        // Create a version in the develop stage
        const responseVersion = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.VERSION,
            company_id,
            project_id,
            stage_id: stageDev_id,
            stage_from_id: stageDev_id,
            vsn: '0.0.1',
          }),
        );
        expect(responseVersion.status).toBe(RESP_STATUS.OK);
        version_id = responseVersion.body.ops[0].obj_id;
      },
    );

    // Test cases for Stage to Stage comparison and merge
    const testCasesStageToStage = [
      {
        description: 'dev -> prod1',
        obj_to_type: OBJ_TYPE.STAGE,
        obj_to_id: (): string | number => stageDev_id,
        obj_id: (): string | number => stageProd_id1,
      },
    ];

    describe.each(testCasesStageToStage)('$description', ({ obj_to_type, obj_id, obj_to_id }): void => {
      test('should successfully compare stage to stage', async (): Promise<void> => {
        await cleanProductionStage(obj_id());
        const response = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.COMPARE,
            obj: OBJ_TYPE.OBJ_SCHEME,
            company_id,
            obj_id: obj_id(),
            obj_type: OBJ_TYPE.STAGE,
            obj_to_id: obj_to_id(),
            obj_to_type,
            project_id,
            num_stat: true,
            diff_status: true,
          }),
        );
        expect(response.status).toBe(RESP_STATUS.OK);

        const { ops: opsCompare } = response.body as CompareResponse;
        const { proc: procCompare, list: listCompare } = opsCompare[0];
        expect(procCompare).toEqual(PROC_STATUS.OK);

        const aliasItem = (listCompare as Array<any>).filter((item): boolean => item.obj_type === 'alias');
        aliasItem.forEach((alias): void => {
          expect(alias).toBeDefined();
          expect(alias.__num_stat).toEqual(scenario.num_statAlias());
          expect(alias.__status).toEqual(scenario.statusAlias());
          expect(alias.parent_id).toEqual(obj_id());
        });

        const convItem = (listCompare as Array<any>).filter((item): boolean => item.title === processCopy);
        convItem.forEach((conv): void => {
          expect(conv).toBeDefined();
          expect(conv.__num_stat).toEqual(scenario.num_statConv());
          expect(conv.__status).toEqual(scenario.statusConv());
          expect(conv.parent_id).toEqual(obj_id());
        });

        const diagramItem = (listCompare as Array<any>).filter((item): boolean => item.title === stateDiagram);
        diagramItem.forEach((conv): void => {
          expect(conv).toBeDefined();
          expect(conv.__num_stat).toEqual(scenario.num_statDiagram());
          expect(conv.__status).toEqual(scenario.statusDiagram());
          expect(conv.parent_id).toEqual(obj_id());
        });
      });

      test('should successfully merge stage to stage', async (): Promise<void> => {
        const response = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MERGE,
            obj: OBJ_TYPE.OBJ_SCHEME,
            company_id,
            obj_id: obj_id(),
            obj_type: OBJ_TYPE.STAGE,
            obj_to_id: obj_to_id(),
            obj_to_type,
            project_id,
            apply_mode: true,
            async: false,
          }),
        );
        expect(response.status).toBe(RESP_STATUS.OK);

        const { ops: opsMerge } = response.body as MergeResponse;
        const { proc: prockMerge, diff: diffMerge } = opsMerge[0];
        expect(prockMerge).toEqual(PROC_STATUS.OK);
        expect(diffMerge.obj_id).toEqual(obj_id());

        const responseList = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.STAGE,
            company_id,
            obj_id: obj_id(),
            project_id,
          }),
        );
        expect(responseList.status).toBe(RESP_STATUS.OK);

        const { proc: proc, list: list } = responseList.body.ops[0];
        expect(proc).toEqual(PROC_STATUS.OK);
        expect(list).toBeArrayOfSize(2);

        newConvCopyProd = (list as Array<any>).find((item): boolean => item.title === processCopy).obj_id;
        newSDProd = (list as Array<any>).find((item): boolean => item.title === stateDiagram).obj_id;

        const responseListConv = await requestListConv(api, newConvCopyProd, company_id, project_id, obj_id());
        expect(responseListConv.status).toBe(RESP_STATUS.OK);

        const processCopyNode = (responseListConv.body.ops[0].list as Array<any>).find(
          (item): boolean => item.title === 'proccesCopy',
        );
        expect(processCopyNode).toBeDefined();

        const apiCopyLogic = processCopyNode.logics.find(
          (logic: { type: string }): boolean => logic.type === 'api_copy',
        );
        expect(apiCopyLogic).toBeDefined();

        await verifyApiCopyParameters(
          api,
          apiCopyLogic,
          scenario,
          obj_id(),
          company_id,
          project_id,
          'newStage_id1',
          'newConv_id',
          'newProject_id',
        );
        const responseAlias = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.ALIASES,
            company_id,
            project_id,
            stage_id: obj_id(),
          }),
        );
        expect(responseAlias.status).toBe(RESP_STATUS.OK);

        const aliasList = responseAlias.body.ops[0].list;
        const mergedAlias = (aliasList as Array<any>).find(
          (alias: { short_name: string | number }): boolean => alias.short_name === short_name_al_sd,
        );

        expect(mergedAlias).toBeDefined();

        expect(mergedAlias.obj_to_type).toBe('conv');
        expect(mergedAlias.obj_to_id).toBe(newSDProd);
      });
    });

    // Test cases for Version to Stage comparison and merge
    const testCasesVersionToStage = [
      {
        description: 'version -> prod2',
        obj_to_type: OBJ_TYPE.VERSION,
        obj_to_id: (): string | number => version_id,
        obj_id: (): string | number => stageProd_id2,
      },
    ];

    describe.each(testCasesVersionToStage)('$description', ({ obj_to_type, obj_id, obj_to_id }): void => {
      test('should successfully compare version to stage', async (): Promise<void> => {
        await cleanProductionStage(obj_id());
        const response = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.COMPARE,
            obj: OBJ_TYPE.OBJ_SCHEME,
            company_id,
            obj_id: obj_id(),
            obj_type: OBJ_TYPE.STAGE,
            obj_to_id: obj_to_id(),
            obj_to_type,
            project_id,
            num_stat: true,
            diff_status: true,
          }),
        );
        expect(response.status).toBe(RESP_STATUS.OK);

        const { ops: opsCompare } = response.body as CompareResponse;
        const { proc: procCompare, list: listCompare } = opsCompare[0];
        expect(procCompare).toEqual(PROC_STATUS.OK);

        const aliasItem = (listCompare as Array<any>).filter((item): boolean => item.obj_type === 'alias');
        aliasItem.forEach((alias): void => {
          expect(alias).toBeDefined();
          expect(alias.__num_stat).toEqual(scenario.num_statAlias());
          expect(alias.__status).toEqual(scenario.statusAlias());
          expect(alias.parent_id).toEqual(obj_id());
        });

        const convItem = (listCompare as Array<any>).filter((item): boolean => item.title === processCopy);
        convItem.forEach((conv): void => {
          expect(conv).toBeDefined();
          expect(conv.__num_stat).toEqual(scenario.num_statConv());
          expect(conv.__status).toEqual(scenario.statusConv());
          expect(conv.parent_id).toEqual(obj_id());
        });

        const diagramItem = (listCompare as Array<any>).filter((item): boolean => item.title === stateDiagram);
        diagramItem.forEach((conv): void => {
          expect(conv).toBeDefined();
          expect(conv.__num_stat).toEqual(scenario.num_statDiagram());
          expect(conv.__status).toEqual(scenario.statusDiagram());
          expect(conv.parent_id).toEqual(obj_id());
        });
      });

      test('should successfully merge version to stage', async (): Promise<void> => {
        const response = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MERGE,
            obj: OBJ_TYPE.OBJ_SCHEME,
            company_id,
            obj_id: obj_id(),
            obj_type: OBJ_TYPE.STAGE,
            obj_to_id: obj_to_id(),
            obj_to_type,
            project_id,
            apply_mode: true,
            async: false,
          }),
        );
        expect(response.status).toBe(RESP_STATUS.OK);

        const { ops: opsMerge } = response.body as MergeResponse;
        const { proc: prockMerge, diff: diffMerge } = opsMerge[0];
        expect(prockMerge).toEqual(PROC_STATUS.OK);
        expect(diffMerge.obj_id).toEqual(obj_id());

        const responseList = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.STAGE,
            company_id,
            obj_id: obj_id(),
            project_id,
          }),
        );
        expect(responseList.status).toBe(RESP_STATUS.OK);

        const { proc: proc, list: list } = responseList.body.ops[0];
        expect(proc).toEqual(PROC_STATUS.OK);
        expect(list).toBeArrayOfSize(2);

        newConvCopyProd = (list as Array<any>).find((item): boolean => item.title === processCopy).obj_id;
        newSDProd = (list as Array<any>).find((item): boolean => item.title === stateDiagram).obj_id;

        const responseListConv = await requestListConv(api, newConvCopyProd, company_id, project_id, obj_id());
        expect(responseListConv.status).toBe(RESP_STATUS.OK);

        const processCopyNode = (responseListConv.body.ops[0].list as Array<any>).find(
          (item): boolean => item.title === 'proccesCopy',
        );
        expect(processCopyNode).toBeDefined();

        const apiCopyLogic = processCopyNode.logics.find(
          (logic: { type: string }): boolean => logic.type === 'api_copy',
        );
        expect(apiCopyLogic).toBeDefined();

        await verifyApiCopyParameters(
          api,
          apiCopyLogic,
          scenario,
          obj_id(),
          company_id,
          project_id,
          'newStage_id2',
          'newConv_id',
          'newProject_id',
        );

        const responseAlias = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.ALIASES,
            company_id,
            project_id,
            stage_id: obj_id(),
          }),
        );
        expect(responseAlias.status).toBe(RESP_STATUS.OK);

        const aliasList = responseAlias.body.ops[0].list;
        const mergedAlias = (aliasList as Array<any>).find(
          (alias: { short_name: string | number }): boolean => alias.short_name === short_name_al_sd,
        );

        expect(mergedAlias).toBeDefined();

        expect(mergedAlias.obj_to_type).toBe('conv');
        expect(mergedAlias.obj_to_id).toBe(newSDProd);
      });
    });

    afterAll(
      async (): Promise<void> => {
        await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.DELETE,
            obj: OBJ_TYPE.VERSION,
            obj_id: version_id,
            project_id,
            company_id,
          }),
        );
        await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.DESTROY,
            obj: OBJ_TYPE.VERSION,
            obj_id: version_id,
            project_id,
            company_id,
          }),
        );
      },
    );
  });

  afterAll(
    async (): Promise<void> => {
      const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, project_id, company_id);
      expect(response.status).toBe(RESP_STATUS.OK);
    },
  );
});
