import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
  generateName,
} from '../../../../utils/corezoidRequest';
import {
  requestConfirm,
  requestDeleteObj,
  requestListConv,
  requestCreateObjNew,
} from '../../../../application/api/ApiObj';
import { NODE_TYPE, NODE_LOGIC_TYPE } from '../../../../application/api/obj_types/node';
import { CreateAliasResponse, LinkAliasResponse } from '../../../../application/api/obj_types/alias';
import { CompareResponse, MergeResponse } from '../../../../application/api/obj_types/obj_scheme';
import { updateProdProcessNode, createDynamicConvConfig, verifyApiCopyParameters } from './utils/compare_merge_utils';

describe('Compare/Merge with Cross-Stage References', (): void => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let project_id: string | number;
  let stageDev_id: string | number;
  let stageProd1_id: number;
  let stageProd2_id: number;
  let stageRef_id: string | number;
  let project_short_name: string;
  let title: string;
  let conv_type: string;

  // Process in development stage
  let copy_process_dev: number;
  let process_node_ID_copy_dev: string | number;
  let final_node_ID_copy_dev: string | number;
  let processCopy_dev: string;

  // State diagram and alias in development stage
  let dev_state_process: number;
  let dev_short_name_alias: string | number;
  let dev_alias_id: string | number;
  let dev_state_process_name: string;

  // Processes in prod stages (initially with local references)
  let copy_process_prod1: number;
  let dev_state_process_prod1: number;

  let copy_process_prod2: number;
  let dev_state_process_prod2: number;

  // Reference stage processes (targets for cross-stage references)
  let ref_short_name_alias: string | number;
  let ref_alias_id: string | number;
  let ref_referens: string;
  let ref_state_process: number;
  let ref_state_process_name: string;

  let version_id: string | number;

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;
      project_short_name = generateName(OBJ_TYPE.PROJECT);
      title = generateName(OBJ_TYPE.OBJS);
      conv_type = 'process';
      ref_referens = 'ref0000';
      processCopy_dev = generateName(OBJ_TYPE.OBJS);
      ref_state_process_name = generateName(OBJ_TYPE.OBJS);
      ref_short_name_alias = generateName(OBJ_TYPE.ALIAS);
      dev_state_process_name = generateName(OBJ_TYPE.OBJS);
      dev_short_name_alias = generateName(OBJ_TYPE.ALIAS);

      // Create project with four stages: two production, one development, and one reference
      const responseProject = await requestCreateObjNew(
        api,
        OBJ_TYPE.PROJECT,
        company_id,
        generateName(OBJ_TYPE.PROJECT),
        {
          short_name: project_short_name,
          stages: [
            { title: 'production1', immutable: true },
            { title: 'develop', immutable: false },
            { title: 'production2', immutable: true },
            { title: 'reference', immutable: false },
          ],
        },
      );
      const { obj_id: objIdProject, stages: arrStages } = responseProject.body.ops[0];
      expect(responseProject.status).toBe(RESP_STATUS.OK);
      project_id = objIdProject;
      stageProd1_id = arrStages[0];
      stageDev_id = arrStages[1];
      stageProd2_id = arrStages[2];
      stageRef_id = arrStages[3];

      // Create a state diagram in reference stage
      const responseRefState = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, ref_state_process_name, {
        conv_type: 'state',
        stage_id: stageRef_id,
        project_id,
      });
      ref_state_process = responseRefState.obj_id;

      // Create alias in reference stage
      const responseRefAlias = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          short_name: ref_short_name_alias,
          title,
          project_id,
          stage_id: stageRef_id,
        }),
      );
      expect(responseRefAlias.status).toBe(RESP_STATUS.OK);
      const { ops: opsCreateRefAlias } = responseRefAlias.body as CreateAliasResponse;
      ref_alias_id = opsCreateRefAlias[0].obj_id;

      // Link alias to state diagram in reference stage
      const responseLinkRef = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.ALIAS,
          obj_id: ref_alias_id,
          company_id,
          link: true,
          obj_to_type: OBJ_TYPE.CONV,
          obj_to_id: ref_state_process,
          project_id,
          stage_id: stageRef_id,
        }),
      );
      expect(responseLinkRef.status).toBe(RESP_STATUS.OK);
      const { ops: opsLinkRefAlias } = responseLinkRef.body as LinkAliasResponse;
      expect(opsLinkRefAlias[0].proc).toEqual(PROC_STATUS.OK);

      // 2. Create a state diagram in development stage
      const responseDevState = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, dev_state_process_name, {
        conv_type: 'state',
        stage_id: stageDev_id,
        project_id,
      });
      dev_state_process = responseDevState.obj_id;

      // Create alias in development stage
      const responseDevAlias = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          short_name: dev_short_name_alias,
          title,
          project_id,
          stage_id: stageDev_id,
        }),
      );
      expect(responseDevAlias.status).toBe(RESP_STATUS.OK);
      const { ops: opsCreateDevAlias } = responseDevAlias.body as CreateAliasResponse;
      dev_alias_id = opsCreateDevAlias[0].obj_id;

      // Link alias to state diagram in development stage
      const responseLinkDev = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.ALIAS,
          obj_id: dev_alias_id,
          company_id,
          link: true,
          obj_to_type: OBJ_TYPE.CONV,
          obj_to_id: dev_state_process,
          project_id,
          stage_id: stageDev_id,
        }),
      );
      expect(responseLinkDev.status).toBe(RESP_STATUS.OK);
      const { ops: opsLinkDevAlias } = responseLinkDev.body as LinkAliasResponse;
      expect(opsLinkDevAlias[0].proc).toEqual(PROC_STATUS.OK);

      // 3. Create process in development stage
      const responseCopyDev = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, processCopy_dev, {
        conv_type,
        stage_id: stageDev_id,
        project_id,
      });
      copy_process_dev = responseCopyDev.obj_id;
      const responseListSetParamDev = await requestListConv(api, copy_process_dev, company_id);
      const { list: listSetParamDev } = responseListSetParamDev.body.ops[0];
      process_node_ID_copy_dev = (listSetParamDev as Array<any>).find(item => item.title === conv_type).obj_id;
      final_node_ID_copy_dev = (listSetParamDev as Array<any>).find(item => item.title === 'final').obj_id;

      // Configure the API Copy logic in development with LOCAL reference to itself (same stage)
      const responseModifCopyInitDev = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID_copy_dev,
          conv_id: copy_process_dev,
          title: 'processCopy',
          obj_type: NODE_TYPE.Normal,
          logics: [
            {
              type: NODE_LOGIC_TYPE.ApiCopy,
              mode: 'create',
              data: {},
              data_type: {},
              group: 'all',
              ref: '',
              send_parent_data: false,
              conv_id: `{{conv[${dev_state_process}].ref[${ref_referens}].conv}}`,
              stage_id: stageDev_id,
              project_id,
              err_node_id: '',
            },
            { to_node_id: final_node_ID_copy_dev, type: NODE_LOGIC_TYPE.Go, node_title: 'final' },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseModifCopyInitDev.status).toBe(RESP_STATUS.OK);

      const responseCommitInitDev = await requestConfirm(api, copy_process_dev, company_id);
      expect(responseCommitInitDev.status).toBe(RESP_STATUS.OK);

      // 4. Merge development stage to production stages first to establish initial state
      const responseMerge1 = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MERGE,
          obj: OBJ_TYPE.OBJ_SCHEME,
          company_id,
          obj_id: stageProd1_id,
          obj_type: OBJ_TYPE.STAGE,
          obj_to_id: stageDev_id,
          obj_to_type: OBJ_TYPE.STAGE,
          project_id,
          apply_mode: true,
          async: false,
        }),
      );
      expect(responseMerge1.status).toBe(RESP_STATUS.OK);

      const responseList1 = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: stageProd1_id,
          project_id,
        }),
      );
      expect(responseList1.status).toBe(RESP_STATUS.OK);

      const { list: list1 } = responseList1.body.ops[0];
      const convCopyProd1Item = (list1 as Array<any>).find(item => item.title === processCopy_dev);
      copy_process_prod1 = convCopyProd1Item?.obj_id;
      const devStateProd1Item = (list1 as Array<any>).find(item => item.title === dev_state_process_name);
      dev_state_process_prod1 = devStateProd1Item?.obj_id;

      const responseListConvProd1 = await requestListConv(
        api,
        copy_process_prod1,
        company_id,
        project_id,
        stageProd1_id,
      );
      expect(responseListConvProd1.status).toBe(RESP_STATUS.OK);

      const responseMerge2 = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MERGE,
          obj: OBJ_TYPE.OBJ_SCHEME,
          company_id,
          obj_id: stageProd2_id,
          obj_type: OBJ_TYPE.STAGE,
          obj_to_id: stageDev_id,
          obj_to_type: OBJ_TYPE.STAGE,
          project_id,
          apply_mode: true,
          async: false,
        }),
      );
      expect(responseMerge2.status).toBe(RESP_STATUS.OK);

      const responseList2 = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: stageProd2_id,
          project_id,
        }),
      );
      expect(responseList2.status).toBe(RESP_STATUS.OK);

      const { list: list2 } = responseList2.body.ops[0];
      const convCopyProd2Item = (list2 as Array<any>).find(item => item.title === processCopy_dev);
      copy_process_prod2 = convCopyProd2Item?.obj_id;

      const devStateProd2Item = (list2 as Array<any>).find(item => item.title === dev_state_process_name);
      dev_state_process_prod2 = devStateProd2Item?.obj_id;

      const responseListConvProd2 = await requestListConv(
        api,
        copy_process_prod2,
        company_id,
        project_id,
        stageProd2_id,
      );
      expect(responseListConvProd2.status).toBe(RESP_STATUS.OK);
    },
  );

  // Define test scenarios for different cross-stage reference types in dev stage
  const testScenarios = [
    {
      name: 'All numeric IDs',
      conv_id: (): string | number => ref_state_process,
      stage_id: (): string | number => stageRef_id,
      project_id: (): string | number => project_id,
      newConv_id1: (): string | number => ref_state_process,
      newConv_id2: (): string | number => ref_state_process,
      newStage_id1: (): string | number => stageRef_id,
      newStage_id2: (): string | number => stageRef_id,
      newProject_id1: (): string | number => project_id,
      newProject_id2: (): string | number => project_id,
      num_statConv: (): { added: number; changed: number; deleted: number } => ({ added: 2, changed: 2, deleted: 0 }),
      num_statAlias: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 0, deleted: 0 }),
      num_statDiagram: (): { added: number; changed: number; deleted: number } => ({
        added: 1,
        changed: 0,
        deleted: 0,
      }),
      statusConv: (): string => 'changed',
      statusAlias: (): string => '',
      statusDiagram: (): string => 'changed',
    },
    {
      name: 'Alias for conv_id, numeric stage_id/project_id',
      conv_id: (): string => `@${ref_short_name_alias}`,
      stage_id: (): string | number => stageRef_id,
      project_id: (): string | number => project_id,
      newConv_id1: (): string => `@${ref_short_name_alias}`,
      newConv_id2: (): string => `@${ref_short_name_alias}`,
      newStage_id1: (): string | number => stageRef_id,
      newStage_id2: (): string | number => stageRef_id,
      newProject_id1: (): string | number => project_id,
      newProject_id2: (): string | number => project_id,
      num_statConv: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 1, deleted: 0 }),
      num_statAlias: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 0, deleted: 0 }),
      num_statDiagram: (): { added: number; changed: number; deleted: number } => ({
        added: 0,
        changed: 0,
        deleted: 0,
      }),
      statusConv: (): string => 'changed',
      statusAlias: (): string => '',
      statusDiagram: (): string => 'changed',
    },
    {
      name: 'Dynamic conv_id, numeric stage_id/project_id',
      conv_id: (): string => `{{conv[${dev_state_process}].ref[${ref_referens}].conv}}`,
      stage_id: (): string | number => stageRef_id,
      project_id: (): string | number => project_id,
      newConv_id1: (): string => `{{conv[${dev_state_process_prod1}].ref[${ref_referens}].conv}}`,
      newConv_id2: (): string => `{{conv[${dev_state_process_prod2}].ref[${ref_referens}].conv}}`,
      newStage_id1: (): string | number => stageRef_id,
      newStage_id2: (): string | number => stageRef_id,
      newProject_id1: (): string | number => project_id,
      newProject_id2: (): string | number => project_id,
      num_statConv: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 1, deleted: 0 }),
      num_statAlias: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 0, deleted: 0 }),
      num_statDiagram: (): { added: number; changed: number; deleted: number } => ({
        added: 0,
        changed: 0,
        deleted: 0,
      }),
      statusConv: (): string => 'changed',
      statusAlias: (): string => '',
      statusDiagram: (): string => 'changed',
    },
    {
      name: 'conv_id id parameters, without stage_id/project_id',
      conv_id: (): string | number => ref_state_process,
      stage_id: (): undefined => undefined,
      project_id: (): undefined => undefined,
      newConv_id1: (): string | number => ref_state_process,
      newConv_id2: (): string | number => ref_state_process,
      newStage_id1: (): string | number => stageRef_id,
      newStage_id2: (): string | number => stageRef_id,
      newProject_id1: (): string | number => project_id,
      newProject_id2: (): string | number => project_id,
      num_statConv: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 1, deleted: 2 }),
      num_statAlias: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 0, deleted: 0 }),
      num_statDiagram: (): { added: number; changed: number; deleted: number } => ({
        added: 0,
        changed: 0,
        deleted: 0,
      }),
      statusConv: (): string => 'changed',
      statusAlias: (): string => '',
      statusDiagram: (): string => 'changed',
    },
  ];
  describe.each(testScenarios)('Testing with $name', (scenario): void => {
    beforeAll(
      async (): Promise<void> => {
        // Configure the API Copy logic with the test scenario parameters
        const responseModifCopy = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MODIFY,
            obj: OBJ_TYPE.NODE,
            obj_id: process_node_ID_copy_dev,
            conv_id: copy_process_dev,
            title: 'proccesCopy',
            obj_type: NODE_TYPE.Normal,
            logics: [
              {
                type: NODE_LOGIC_TYPE.ApiCopy,
                mode: 'create',
                data: {},
                data_type: {},
                group: 'all',
                ref: '',
                send_parent_data: false,
                conv_id: scenario.conv_id(),
                stage_id: scenario.stage_id(),
                project_id: scenario.project_id(),
                err_node_id: '',
              },
              { to_node_id: final_node_ID_copy_dev, type: NODE_LOGIC_TYPE.Go, node_title: 'final' },
            ],
            semaphors: [],
            version: 22,
          }),
        );
        expect(responseModifCopy.status).toBe(RESP_STATUS.OK);

        const responseCommitCopy = await requestConfirm(api, copy_process_dev, company_id);
        expect(responseCommitCopy.status).toBe(RESP_STATUS.OK);

        // Create a version in the develop stage
        const responseVersion = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.VERSION,
            company_id,
            project_id,
            stage_id: stageDev_id,
            stage_from_id: stageDev_id,
            vsn: '0.0.1',
          }),
        );
        expect(responseVersion.status).toBe(RESP_STATUS.OK);
        version_id = responseVersion.body.ops[0].obj_id;
      },
    );

    // Test cases for Stage to Stage comparison and merge with populated production stage
    const testCasesStageToStage = [
      {
        description: 'dev -> populated prod1',
        obj_to_type: OBJ_TYPE.STAGE,
        obj_to_id: (): string | number => stageDev_id,
        obj_id: (): string | number => stageProd1_id,
        prod_conv_copy: (): number => copy_process_prod1,
        prod_sd: (): number => dev_state_process_prod1,
      },
    ];

    describe.each(testCasesStageToStage)(
      '$description',
      ({ obj_to_type, obj_id, obj_to_id, prod_conv_copy, prod_sd }): void => {
        // Update production stage configuration before each test
        beforeEach(
          async (): Promise<void> => {
            const config = createDynamicConvConfig(
              api,
              obj_id(),
              prod_conv_copy(),
              prod_sd(),
              company_id,
              project_id,
              conv_type,
            );
            await updateProdProcessNode(config);
          },
        );

        test('should successfully compare stage to populated stage', async (): Promise<void> => {
          const response = await api.request(
            createRequestWithOps({
              type: REQUEST_TYPE.COMPARE,
              obj: OBJ_TYPE.OBJ_SCHEME,
              company_id,
              obj_id: obj_id(),
              obj_type: OBJ_TYPE.STAGE,
              obj_to_id: obj_to_id(),
              obj_to_type,
              project_id,
              num_stat: true,
              diff_status: true,
            }),
          );
          expect(response.status).toBe(RESP_STATUS.OK);

          const { ops: opsCompare } = response.body as CompareResponse;
          const { proc: procCompare, list: listCompare } = opsCompare[0];
          expect(procCompare).toEqual(PROC_STATUS.OK);

          // Verify the process comparison - should show changes because of the different parameters
          const convItem = (listCompare as Array<any>).filter((item): boolean => item.title === processCopy_dev);
          if (convItem.length > 0) {
            expect(convItem[0].__num_stat).toEqual(scenario.num_statConv());
            expect(convItem[0].__status).toEqual(scenario.statusConv());
          }

          const aliasItem = (listCompare as Array<any>).filter(
            (item): boolean => item.obj_type === 'alias' && item.short_name === dev_short_name_alias,
          );
          if (aliasItem.length > 0) {
            expect(aliasItem[0].__status).toEqual(scenario.statusAlias());
          }

          const diagramItem = (listCompare as Array<any>).filter((item): boolean => item.title === dev_state_process);
          if (diagramItem.length > 0) {
            expect(diagramItem[0].__status).toEqual(scenario.statusDiagram());
          }
        });

        test('should successfully merge stage to populated stage', async (): Promise<void> => {
          const response = await api.request(
            createRequestWithOps({
              type: REQUEST_TYPE.MERGE,
              obj: OBJ_TYPE.OBJ_SCHEME,
              company_id,
              obj_id: obj_id(),
              obj_type: OBJ_TYPE.STAGE,
              obj_to_id: obj_to_id(),
              obj_to_type,
              project_id,
              apply_mode: true,
              async: false,
            }),
          );
          expect(response.status).toBe(RESP_STATUS.OK);

          const { ops: opsMerge } = response.body as MergeResponse;
          const { proc: prockMerge, diff: diffMerge } = opsMerge[0];
          expect(prockMerge).toEqual(PROC_STATUS.OK);
          expect(diffMerge.obj_id).toEqual(obj_id());

          // Verify the process node has been updated with the scenario parameters
          const responseListConv = await requestListConv(api, prod_conv_copy(), company_id, project_id, obj_id());
          expect(responseListConv.status).toBe(RESP_STATUS.OK);

          const processCopyNode = (responseListConv.body.ops[0].list as Array<any>).find(
            (item): boolean => item.title === 'proccesCopy',
          );
          expect(processCopyNode).toBeDefined();

          const apiCopyLogic = processCopyNode.logics.find(
            (logic: { type: string }): boolean => logic.type === 'api_copy',
          );
          expect(apiCopyLogic).toBeDefined();

          await verifyApiCopyParameters(
            api,
            apiCopyLogic,
            scenario,
            obj_id(),
            company_id,
            project_id,
            'newStage_id1',
            'newConv_id1',
            'newProject_id1',
          );
        });
      },
    );

    // Test cases for Version to Stage comparison and merge with populated production stage
    const testCasesVersionToStage = [
      {
        description: 'version -> populated prod2',
        obj_to_type: OBJ_TYPE.VERSION,
        obj_to_id: (): string | number => version_id,
        obj_id: (): string | number => stageProd2_id,
        prod_conv_copy: (): number => copy_process_prod2,
        prod_sd: (): number => dev_state_process_prod2,
      },
    ];

    describe.each(testCasesVersionToStage)(
      '$description',
      ({ obj_to_type, obj_id, obj_to_id, prod_conv_copy, prod_sd }): void => {
        beforeEach(
          async (): Promise<void> => {
            const config = createDynamicConvConfig(
              api,
              obj_id(),
              prod_conv_copy(),
              prod_sd(),
              company_id,
              project_id,
              conv_type,
            );
            await updateProdProcessNode(config);
          },
        );

        test('should successfully compare version to populated stage', async (): Promise<void> => {
          const response = await api.request(
            createRequestWithOps({
              type: REQUEST_TYPE.COMPARE,
              obj: OBJ_TYPE.OBJ_SCHEME,
              company_id,
              obj_id: obj_id(),
              obj_type: OBJ_TYPE.STAGE,
              obj_to_id: obj_to_id(),
              obj_to_type,
              project_id,
              num_stat: true,
              diff_status: true,
            }),
          );
          expect(response.status).toBe(RESP_STATUS.OK);

          const { ops: opsCompare } = response.body as CompareResponse;
          const { proc: procCompare, list: listCompare } = opsCompare[0];
          expect(procCompare).toEqual(PROC_STATUS.OK);

          // Verify the process comparison - should show changes because of the different parameters
          const convItem = (listCompare as Array<any>).filter((item): boolean => item.title === processCopy_dev);
          if (convItem.length > 0) {
            expect(convItem[0].__num_stat).toEqual(scenario.num_statConv());
            expect(convItem[0].__status).toEqual(scenario.statusConv());
          }

          const aliasItem = (listCompare as Array<any>).filter(
            (item): boolean => item.obj_type === 'alias' && item.short_name === dev_short_name_alias,
          );
          if (aliasItem.length > 0) {
            expect(aliasItem[0].__status).toEqual(scenario.statusAlias());
          }

          const diagramItem = (listCompare as Array<any>).filter((item): boolean => item.title === dev_state_process);
          if (diagramItem.length > 0) {
            expect(diagramItem[0].__status).toEqual(scenario.statusDiagram());
          }
        });

        test('should successfully merge version to populated stage', async (): Promise<void> => {
          const response = await api.request(
            createRequestWithOps({
              type: REQUEST_TYPE.MERGE,
              obj: OBJ_TYPE.OBJ_SCHEME,
              company_id,
              obj_id: obj_id(),
              obj_type: OBJ_TYPE.STAGE,
              obj_to_id: obj_to_id(),
              obj_to_type,
              project_id,
              apply_mode: true,
              async: false,
            }),
          );
          expect(response.status).toBe(RESP_STATUS.OK);

          const { ops: opsMerge } = response.body as MergeResponse;
          const { proc: prockMerge, diff: diffMerge } = opsMerge[0];
          expect(prockMerge).toEqual(PROC_STATUS.OK);
          expect(diffMerge.obj_id).toEqual(obj_id());

          const responseListConv = await requestListConv(api, prod_conv_copy(), company_id, project_id, obj_id());
          expect(responseListConv.status).toBe(RESP_STATUS.OK);

          const processCopyNode = (responseListConv.body.ops[0].list as Array<any>).find(
            (item): boolean => item.title === 'proccesCopy',
          );
          expect(processCopyNode).toBeDefined();

          const apiCopyLogic = processCopyNode.logics.find(
            (logic: { type: string }): boolean => logic.type === 'api_copy',
          );
          expect(apiCopyLogic).toBeDefined();

          await verifyApiCopyParameters(
            api,
            apiCopyLogic,
            scenario,
            obj_id(),
            company_id,
            project_id,
            'newStage_id2',
            'newConv_id2',
            'newProject_id2',
          );
        });
      },
    );

    afterAll(
      async (): Promise<void> => {
        await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.DELETE,
            obj: OBJ_TYPE.VERSION,
            obj_id: version_id,
            project_id,
            company_id,
          }),
        );
        await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.DESTROY,
            obj: OBJ_TYPE.VERSION,
            obj_id: version_id,
            project_id,
            company_id,
          }),
        );
      },
    );
  });

  afterAll(
    async (): Promise<void> => {
      const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, project_id, company_id);
      expect(response.status).toBe(RESP_STATUS.OK);
    },
  );
});
