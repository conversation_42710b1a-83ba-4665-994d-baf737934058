import { debug, error } from '../../../../support/utils/logger';
import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { Api<PERSON>ey } from '../../../../infrastructure/model/ApiKey';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
  generateName,
} from '../../../../utils/corezoidRequest';
import {
  requestConfirm,
  requestDeleteObj,
  requestListConv,
  requestCreateObjNew,
} from '../../../../application/api/ApiObj';
import { NODE_TYPE, NODE_LOGIC_TYPE } from '../../../../application/api/obj_types/node';
import { CompareResponse, MergeResponse } from '../../../../application/api/obj_types/obj_scheme';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';

describe('Api_copy parameters in compare_merge (file to stage)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let project_id: string | number;
  let stageDev_id: string | number;
  let stageProd_id: string | number;
  let project_short_name: string;
  let source_process: number;
  let target_process: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let source_process_prod: number;
  let target_process_prod: number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = generateName(OBJ_TYPE.PROJECT);

    const responseProject = await requestCreateObjNew(
      api,
      OBJ_TYPE.PROJECT,
      company_id,
      generateName(OBJ_TYPE.PROJECT),
      {
        short_name: project_short_name,
        stages: [
          { title: 'production', immutable: true },
          { title: 'develop', immutable: false },
        ],
      },
    );
    const { obj_id: objIdProject, stages: arrStages } = responseProject.body.ops[0];
    expect(responseProject.status).toBe(RESP_STATUS.OK);
    project_id = objIdProject;
    stageDev_id = arrStages[1];
    stageProd_id = arrStages[0];

    const sourceProcessName = generateName(OBJ_TYPE.OBJS) + '_source';
    const responseSource = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, sourceProcessName, {
      conv_type: 'process',
      stage_id: stageDev_id,
      project_id,
    });
    expect(responseSource.status).toBe(RESP_STATUS.OK);
    source_process = responseSource.obj_id;

    const targetProcessName = generateName(OBJ_TYPE.OBJS) + '_target';
    const responseTarget = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, targetProcessName, {
      conv_type: 'process',
      stage_id: stageDev_id,
      project_id,
    });
    expect(responseTarget.status).toBe(RESP_STATUS.OK);
    target_process = responseTarget.obj_id;

    const responseListSource = await requestListConv(api, source_process, company_id);
    expect(responseListSource.status).toBe(RESP_STATUS.OK);
    const { list: listSource } = responseListSource.body.ops[0];
    process_node_ID = (listSource as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (listSource as Array<any>).find(item => item.title === 'final').obj_id;

    const responseModify = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id: source_process,
        title: 'ApiCopyNode',
        description: '',
        obj_type: NODE_TYPE.Normal,
        logics: [
          {
            type: NODE_LOGIC_TYPE.ApiCopy,
            mode: 'create',
            conv_id: target_process,
            stage_id: stageDev_id,
            project_id: project_id,
            data: {},
            data_type: {},
            group: 'all',
            ref: '',
            send_parent_data: false,
            err_node_id: final_node_ID,
          },
          { to_node_id: '', type: NODE_LOGIC_TYPE.Go, node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModify.status).toBe(RESP_STATUS.OK);

    const responseCommit = await requestConfirm(api, source_process, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);
  });

  test('should process api_copy parameters in compare/merge operations using file upload', async () => {
    const exec = promisify(execCallback);

    const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
    const scriptPath = 'tests/api/corezoid-api/sh/uploadObjectInScheme.sh';

    const { stdout, stderr } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: apikey.key,
        API_KEY_SECRET: apikey.secret,
        API_URL: baseUrl + 'api/2/upload',
        COMPANY_ID: String(company_id),
        TYPE: 'create',
        OBJ: 'obj_scheme',
        FILE_NAME: 'stage_file_for_merge.zip',
        ASYNC: 'true',
        SKIP_ROOT: 'false',
        WITH_ALIAS: 'true',
        REWRITE_ALIAS: 'true',
        APPLY_MODE: 'false',
      },
    });

    if (stderr) {
      error('Upload script error:', stderr);
      throw new Error('Failed to upload object via shell script');
    }

    debug('Upload response:', stdout);

    let schemeId = null;
    try {
      const uploadResult = stdout.replace('Result: ', '');
      const uploadResponse = JSON.parse(uploadResult);

      debug('Full upload response:', JSON.stringify(uploadResponse, null, 2));

      if (uploadResponse.ops && uploadResponse.ops[0]) {
        if (uploadResponse.ops[0].scheme_id) {
          schemeId = uploadResponse.ops[0].scheme_id;
          debug('Got scheme_id:', schemeId);
        } else if (uploadResponse.ops[0].hash) {
          schemeId = uploadResponse.ops[0].hash;
          debug('Got hash as scheme_id:', schemeId);
        }
      }

      if (!schemeId) {
        throw new Error('Failed to extract scheme_id or hash from upload response');
      }
    } catch (error) {
      error('Error parsing upload response:', error);
      throw error;
    }

    // Проверяем наличие scheme_id
    expect(schemeId).toBeDefined();
    debug('Using scheme_id for compare:', schemeId);

    const compareResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPARE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        company_id,
        obj_id: stageProd_id,
        obj_type: OBJ_TYPE.STAGE,
        obj_to_id: schemeId,
        obj_to_type: 'scheme',
        project_id,
        num_stat: true,
        diff_status: true,
      }),
    );

    expect(compareResponse.status).toBe(RESP_STATUS.OK);
    const { ops: opsCompare } = compareResponse.body as CompareResponse;
    expect(opsCompare[0].proc).toEqual(PROC_STATUS.OK);

    const mergeResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        company_id,
        obj_id: stageProd_id,
        obj_type: OBJ_TYPE.STAGE,
        obj_to_id: schemeId,
        obj_to_type: 'scheme',
        project_id,
        apply_mode: true,
        async: false,
      }),
    );

    expect(mergeResponse.status).toBe(RESP_STATUS.OK);
    const { ops: opsMerge } = mergeResponse.body as MergeResponse;
    expect(opsMerge[0].proc).toEqual(PROC_STATUS.OK);

    const listProdResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        company_id,
        obj_id: stageProd_id,
        project_id,
      }),
    );

    expect(listProdResponse.status).toBe(RESP_STATUS.OK);
    const { list: prodStageList } = listProdResponse.body.ops[0];

    source_process_prod = (prodStageList as Array<any>).find(item => item.title.includes('_source')).obj_id;

    target_process_prod = (prodStageList as Array<any>).find(item => item.title.includes('_target')).obj_id;

    const listSourceProdResponse = await requestListConv(api, source_process_prod, company_id);
    expect(listSourceProdResponse.status).toBe(RESP_STATUS.OK);

    const { list: sourceNodesList } = listSourceProdResponse.body.ops[0];
    const apiCopyNode = (sourceNodesList as Array<any>).find(item => item.title === 'ApiCopyNode');
    expect(apiCopyNode).toBeDefined();

    const finalNodeProdId = (sourceNodesList as Array<any>).find(item => item.title === 'final').obj_id;
    expect(finalNodeProdId).toBeDefined();

    const apiCopyLogic = apiCopyNode.logics.find((logic: { type: string }) => logic.type === 'api_copy');
    expect(apiCopyLogic).toBeDefined();

    expect(apiCopyLogic.conv_id).toBe(target_process_prod);
    expect(apiCopyLogic.stage_id).toBe(stageProd_id);
    expect(apiCopyLogic.project_id).toBe(project_id);

    const setImmutableFalseResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.STAGE,
        obj_id: stageProd_id,
        project_id,
        company_id,
        immutable: false,
      }),
    );

    expect(setImmutableFalseResponse.status).toBe(RESP_STATUS.OK);

    const modifyProdResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: apiCopyNode.obj_id,
        conv_id: source_process_prod,
        title: 'ApiCopyNode',
        description: '',
        obj_type: NODE_TYPE.Normal,
        logics: [
          {
            type: NODE_LOGIC_TYPE.ApiCopy,
            mode: 'create',
            conv_id: target_process_prod,
            data: {},
            data_type: {},
            group: 'all',
            ref: `ref_${Date.now()}_file_to_stage`,
            send_parent_data: false,
            err_node_id: '',
          },
          { to_node_id: finalNodeProdId, type: NODE_LOGIC_TYPE.Go, node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );

    expect(modifyProdResponse.status).toBe(RESP_STATUS.OK);

    const commitProdResponse = await requestConfirm(api, source_process_prod, company_id);
    expect(commitProdResponse.status).toBe(RESP_STATUS.OK);

    const verifyModificationResponse = await requestListConv(api, source_process_prod, company_id);
    expect(verifyModificationResponse.status).toBe(RESP_STATUS.OK);

    const { list: modifiedNodesList } = verifyModificationResponse.body.ops[0];
    const modifiedApiCopyNode = (modifiedNodesList as Array<any>).find(item => item.title === 'ApiCopyNode');
    expect(modifiedApiCopyNode).toBeDefined();

    const modifiedApiCopyLogic = modifiedApiCopyNode.logics.find(
      (logic: { type: string }) => logic.type === 'api_copy',
    );
    expect(modifiedApiCopyLogic).toBeDefined();

    expect(modifiedApiCopyLogic.conv_id).toBe(target_process_prod);
    expect(modifiedApiCopyLogic.project_id).toBeUndefined();
    expect(modifiedApiCopyLogic.stage_id).toBeUndefined();

    const setImmutableTrueResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.STAGE,
        obj_id: stageProd_id,
        project_id,
        company_id,
        immutable: true,
      }),
    );

    expect(setImmutableTrueResponse.status).toBe(RESP_STATUS.OK);

    const secondCompareResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPARE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        company_id,
        obj_id: stageProd_id,
        obj_type: OBJ_TYPE.STAGE,
        obj_to_id: schemeId,
        obj_to_type: 'scheme',
        project_id,
        num_stat: true,
        diff_status: true,
      }),
    );

    expect(secondCompareResponse.status).toBe(RESP_STATUS.OK);
    const { ops: secondOpsCompare } = secondCompareResponse.body as CompareResponse;
    expect(secondOpsCompare[0].proc).toEqual(PROC_STATUS.OK);

    const compareList = secondOpsCompare[0].list as Array<any>;
    const sourceProcessInCompare = compareList.find(
      item => typeof item.title === 'string' && item.title.includes('_source'),
    );

    expect(sourceProcessInCompare).toBeDefined();
    expect(sourceProcessInCompare.__status).toEqual('changed');

    const secondMergeResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        company_id,
        obj_id: stageProd_id,
        obj_type: OBJ_TYPE.STAGE,
        obj_to_id: schemeId,
        obj_to_type: 'scheme',
        project_id,
        apply_mode: true,
        async: false,
      }),
    );

    expect(secondMergeResponse.status).toBe(RESP_STATUS.OK);
    const { ops: secondOpsMerge } = secondMergeResponse.body as MergeResponse;
    expect(secondOpsMerge[0].proc).toEqual(PROC_STATUS.OK);

    const finalVerificationResponse = await requestListConv(api, source_process_prod, company_id);
    expect(finalVerificationResponse.status).toBe(RESP_STATUS.OK);

    const { list: finalNodesList } = finalVerificationResponse.body.ops[0];
    const finalApiCopyNode = (finalNodesList as Array<any>).find(item => item.title === 'ApiCopyNode');
    expect(finalApiCopyNode).toBeDefined();

    const finalApiCopyLogic = finalApiCopyNode.logics.find((logic: { type: string }) => logic.type === 'api_copy');
    expect(finalApiCopyLogic).toBeDefined();

    expect(finalApiCopyLogic.conv_id).toBe(target_process_prod);
    expect(finalApiCopyLogic.project_id).toBe(project_id);
    expect(finalApiCopyLogic.stage_id).toBe(stageProd_id);
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, project_id, company_id);
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
