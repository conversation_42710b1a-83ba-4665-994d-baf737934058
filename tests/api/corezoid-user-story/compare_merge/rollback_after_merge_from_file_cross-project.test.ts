import { debug } from '../../../../support/utils/logger';
import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { Api<PERSON>ey } from '../../../../infrastructure/model/ApiKey';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { requestDeleteObj, requestListConv } from '../../../../application/api/ApiObj';
import { MergeResponse } from '../../../../application/api/obj_types/obj_scheme';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';
import { addMsg } from 'jest-html-reporters/helper';

describe('should get a merge error', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let project_id: string | number;
  let prodOldType_id: string | number;
  let prodEmpty_id: string | number;
  let listOS: any;
  let listTS: any;
  let stage_schema_id: string;
  let key: string;
  let secret: string;
  let convOtherStage: number;
  let convThisStage: number;

  function parseStdoutToJson(stdout: any): any {
    const jsonStartIndex = stdout.indexOf('{');
    const jsonString = stdout.slice(jsonStartIndex);
    return JSON.parse(jsonString);
  }

  async function performMergeWithError(api: ApiKeyClient, stageId: string | number, schemaId: string): Promise<any> {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        company_id,
        obj_id: stageId,
        obj_type: OBJ_TYPE.STAGE,
        obj_to_id: schemaId,
        obj_to_type: OBJ_TYPE.SCHEME,
        project_id,
        apply_mode: true,
        async: false,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    const { ops: opsMerge } = response.body as MergeResponse;
    const { proc: procMerge, errors: errorMerge } = opsMerge[0];
    expect(procMerge).toBe(PROC_STATUS.ERROR);
    expect(errorMerge[0]).toHaveProperty('obj_id', stageId);
    return response;
  }

  async function performRollback(api: ApiKeyClient, stageId: string | number): Promise<any> {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        obj_id: stageId,
        obj_type: OBJ_TYPE.STAGE,
        version: 2,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);
    return response;
  }

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;
      key = apikey.key;
      secret = apikey.secret;

      const exec = promisify(execCallback);
      const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
      const scriptPath = 'tests/api/corezoid-api/sh/uploadObjectRootFolder.sh';

      const { stdout } = await exec(scriptPath, {
        env: {
          API_KEY_LOGIN: key,
          API_KEY_SECRET: secret,
          API_URL: baseUrl + 'api/2/upload',
          VALIDATE_SCHEME: 'false',
          REWRITE_ALIAS: 'false',
          WITH_ALIAS: 'true',
          COMPANY_ID: company_id,
          FILE_NAME: 'project_test_Rollback.zip',
          ASYNC: 'true',
        },
      });
      debug(stdout);
      const jsonResponse = parseStdoutToJson(stdout);
      addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
      expect(jsonResponse.request_proc).toBe(`ok`);

      await new Promise(r => setTimeout(r, 10000));

      const projectsList = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.PROJECTS,
          obj_id: 0,
          company_id,
          id: company_id,
          order: 'asc',
          sort: 'date',
        }),
      );
      expect(projectsList.status).toBe(200);
      const { list: listSetParam } = projectsList.body.ops[0];
      project_id = (listSetParam as Array<any>).find(item => item.short_name === 'projfortestrollback').project_id;

      const stageList = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.PROJECT,
          obj_id: project_id,
          company_id,
          id: company_id,
          order: 'asc',
          sort: 'date',
        }),
      );
      expect(stageList.status).toBe(200);
      const { list: stagesList } = stageList.body.ops[0];
      prodOldType_id = (stagesList as Array<any>).find(item => item.short_name === 'prodoldtype').obj_id;
      prodEmpty_id = (stagesList as Array<any>).find(item => item.short_name === 'prodempty').obj_id;

      const stageContentList = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: prodOldType_id,
          project_id,
        }),
      );
      expect(stageContentList.status).toBe(RESP_STATUS.OK);
      const { proc, list } = stageContentList.body.ops[0];
      expect(proc).toEqual(PROC_STATUS.OK);
      convOtherStage = (list as Array<any>).find(item => item.title === 'copyOtherStage').obj_id;
      convThisStage = (list as Array<any>).find(item => item.title === 'copyThisStage').obj_id;

      const responseListConvOS = await requestListConv(api, convOtherStage, company_id, project_id, prodOldType_id);
      expect(responseListConvOS.status).toBe(RESP_STATUS.OK);
      listOS = responseListConvOS.body.ops[0].list;

      const responseListConvTS = await requestListConv(api, convThisStage, company_id, project_id, prodOldType_id);
      expect(responseListConvTS.status).toBe(RESP_STATUS.OK);
      listTS = responseListConvTS.body.ops[0].list;

      const scriptPathStage = 'tests/api/corezoid-api/sh/uploadObjectCompareMerge.apply_mode.sh';
      const uploadStage = await exec(scriptPathStage, {
        env: {
          API_KEY_LOGIN: key,
          API_KEY_SECRET: secret,
          API_URL: baseUrl + 'api/2/upload',
          REWRITE_ALIAS: 'true',
          WITH_ALIAS: 'true',
          VALIDATE_SCHEME: 'false',
          FILE_NAME: 'stage_test_Rollback.zip',
          COMPANY_ID: company_id,
          SKIP_ROOT: 'false',
        },
      });
      debug(uploadStage.stdout);
      const jsonResponseStage = parseStdoutToJson(uploadStage.stdout);
      addMsg({ message: 'RES:' + JSON.stringify(jsonResponseStage, null, 2), context: '' });
      expect(jsonResponseStage.ops[0].proc).toBe(PROC_STATUS.OK);
      stage_schema_id = jsonResponseStage.ops[0].scheme_id;
    },
  );

  test('prodOldType - rollback', async (): Promise<void> => {
    const stage_prod_id = prodOldType_id;

    await performMergeWithError(api, stage_prod_id, stage_schema_id);
    await performRollback(api, stage_prod_id);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        company_id,
        obj_id: stage_prod_id,
        project_id,
      }),
    );
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0]).toHaveProperty('proc', PROC_STATUS.OK);
    const { list } = responseList.body.ops[0];
    convOtherStage = (list as Array<any>).find(item => item.title === 'copyOtherStage').obj_id;
    convThisStage = (list as Array<any>).find(item => item.title === 'copyThisStage').obj_id;

    const responseListConvOSAfterRollback = await requestListConv(
      api,
      convOtherStage,
      company_id,
      project_id,
      stage_prod_id,
    );
    expect(responseListConvOSAfterRollback.status).toBe(RESP_STATUS.OK);
    expect(responseListConvOSAfterRollback.body.ops[0]).toHaveProperty('list');
    const listOSAfterRollback = responseListConvOSAfterRollback.body.ops[0].list;

    const responseListConvTSAfterRollback = await requestListConv(
      api,
      convThisStage,
      company_id,
      project_id,
      stage_prod_id,
    );
    expect(responseListConvTSAfterRollback.status).toBe(RESP_STATUS.OK);
    expect(responseListConvTSAfterRollback.body.ops[0]).toHaveProperty('list');
    const listTSAfterRollback = responseListConvTSAfterRollback.body.ops[0].list;

    expect(listOSAfterRollback).toHaveLength(listOS.length);
    expect(listTSAfterRollback).toHaveLength(listTS.length);

    const sortedListOS = [...listOS].sort((firstNode: any, secondNode: any) =>
      firstNode.obj_id.localeCompare(secondNode.obj_id),
    );
    const sortedListOSAfterRollback = [...listOSAfterRollback].sort((firstNode: any, secondNode: any) =>
      firstNode.obj_id.localeCompare(secondNode.obj_id),
    );

    const sortedListTS = [...listTS].sort((firstNode: any, secondNode: any) =>
      firstNode.obj_id.localeCompare(secondNode.obj_id),
    );
    const sortedListTSAfterRollback = [...listTSAfterRollback].sort((firstNode: any, secondNode: any) =>
      firstNode.obj_id.localeCompare(secondNode.obj_id),
    );

    expect(sortedListOS).toEqual(sortedListOSAfterRollback);
    expect(sortedListTS).toEqual(sortedListTSAfterRollback);
  });

  test('prodEmpty - rollback', async (): Promise<void> => {
    const stage_prod_id = prodEmpty_id;

    await performMergeWithError(api, stage_prod_id, stage_schema_id);
    await performRollback(api, stage_prod_id);

    const emptyContentAfter = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        company_id,
        obj_id: stage_prod_id,
        project_id,
      }),
    );
    expect(emptyContentAfter.status).toBe(RESP_STATUS.OK);
    expect(emptyContentAfter.body.ops[0]).toHaveProperty('proc', PROC_STATUS.OK);
    const { list: listAfter } = emptyContentAfter.body.ops[0];
    expect(listAfter).toHaveLength(0);
  });

  afterAll(
    async (): Promise<void> => {
      const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, project_id, company_id);
      expect(response.status).toBe(RESP_STATUS.OK);
    },
  );
});
