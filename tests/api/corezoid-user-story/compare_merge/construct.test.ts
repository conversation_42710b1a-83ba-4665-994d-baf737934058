import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
  generateName,
} from '../../../../utils/corezoidRequest';
import { addMsg } from 'jest-html-reporters/helper';
import {
  requestConfirm,
  requestDeleteObj,
  requestListConv,
  requestCreateObjNew,
} from '../../../../application/api/ApiObj';
import { NODE_TYPE, NODE_LOGIC_TYPE } from '../../../../application/api/obj_types/node';
import { CreateTaskResponse, DeleteTaskResponse } from '../../../../application/api/obj_types/task';
import { CreateAliasResponse, LinkAliasResponse } from '../../../../application/api/obj_types/alias';
import { CompareResponse, MergeResponse } from '../../../../application/api/obj_types/obj_scheme';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import { debug } from '../../../../support/utils/logger';
const scriptPath = 'tests/api/corezoid-api/sh/uploadObjectCompareMerge.apply_mode.sh';

function parseStdoutToJson(stdout: any): any {
  const jsonStartIndex = stdout.indexOf('{');
  const jsonString = stdout.slice(jsonStartIndex);
  return JSON.parse(jsonString);
}

describe('Using construct', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let secret: string;
  let key: string;
  let taskID: string | number;
  let company_id: any;
  let newConvSumProd: number;
  let newConvSetProd: number;
  let newSDProd: number;
  let process_node_IDSD: string | number;
  let project_id: string | number;
  let stageDev_id: string | number;
  let stageProd_id: string | number;
  let project_short_name: string;
  let title: string;
  let conv_type: string;
  let set_param_process: number;
  let sum_process: number;
  let diagram_process: number;
  let start_node_ID_set_param: string | number;
  let process_node_ID_set_param: string | number;
  let final_node_ID_set_param: string | number;
  let process_node_ID_sum: string | number;
  let final_node_ID_sum: string | number;
  let short_name_al_prco_sum: string | number;
  let short_name_al_sd: string | number;
  let alias_proc: string | number;
  let alias_sd: string | number;
  let referens: string;
  let nodeSum_id: string | number;
  let dataSD: any;
  let version_id: string | number;
  let process_node_IDSD_prod: string | number;
  let taskIDSD: string | number;
  let processSet: string;
  let processSum: string;
  let schema_id: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    secret = apikey.secret;
    key = apikey.key;
    company_id = apikey.companies[0].id;
    project_short_name = generateName(OBJ_TYPE.PROJECT);
    title = generateName(OBJ_TYPE.OBJS);
    short_name_al_prco_sum = generateName(OBJ_TYPE.ALIAS);
    short_name_al_sd = generateName(OBJ_TYPE.ALIAS);
    conv_type = 'process';
    referens = 'ref0000';
    processSet = generateName(OBJ_TYPE.OBJS);
    processSum = generateName(OBJ_TYPE.OBJS);
    dataSD = {
      obj: {
        keyObj: 'valueObjPROD',
      },
      arr: [
        'test',
        {
          keyArr: 'valueArrPROD',
        },
      ],
    };

    const responseProject = await requestCreateObjNew(
      api,
      OBJ_TYPE.PROJECT,
      company_id,
      generateName(OBJ_TYPE.PROJECT),
      {
        short_name: project_short_name,
        stages: [
          { title: 'production', immutable: true },
          { title: 'develop', immutable: false },
        ],
      },
    );
    const { obj_id: objIdProject, stages: arrStages } = responseProject.body.ops[0];
    expect(responseProject.status).toBe(RESP_STATUS.OK);
    project_id = objIdProject;
    stageDev_id = arrStages[1];
    stageProd_id = arrStages[0];

    const responseSetParam = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, processSet, {
      conv_type,
      stage_id: stageDev_id,
      project_id,
    });
    set_param_process = responseSetParam.obj_id;
    const responseListSetParam = await requestListConv(api, set_param_process, company_id);
    const { list: listSetParam } = responseListSetParam.body.ops[0];
    process_node_ID_set_param = (listSetParam as Array<any>).find(item => item.title === conv_type).obj_id;
    final_node_ID_set_param = (listSetParam as Array<any>).find(item => item.title === 'final').obj_id;
    start_node_ID_set_param = (listSetParam as Array<any>).find(item => item.title === 'start').obj_id;

    const responseSum = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, processSum, {
      conv_type,
      stage_id: stageDev_id,
      project_id,
    });
    sum_process = responseSum.obj_id;

    const responseListSum = await requestListConv(api, sum_process, company_id);
    const { list: listSum } = responseListSum.body.ops[0];
    process_node_ID_sum = (listSum as Array<any>).find(item => item.title === conv_type).obj_id;
    final_node_ID_sum = (listSum as Array<any>).find(item => item.title === 'final').obj_id;

    const responseDiagram = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, generateName(OBJ_TYPE.CONV), {
      conv_type: 'state',
      stage_id: stageDev_id,
      project_id,
    });
    diagram_process = responseDiagram.obj_id;

    const responseListSD = await requestListConv(api, diagram_process, company_id, project_id, stageDev_id);
    expect(responseListSD.status).toBe(RESP_STATUS.OK);
    process_node_IDSD = (responseListSD.body.ops[0].list as Array<any>).find(item => item.title === 'New user').obj_id;

    const responseAliasProc = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: short_name_al_prco_sum,
        title,
        project_id,
        stage_id: stageDev_id,
      }),
    );
    expect(responseAliasProc.status).toBe(RESP_STATUS.OK);
    const { ops: opsCreateAliasProc } = responseAliasProc.body as CreateAliasResponse;
    alias_proc = opsCreateAliasProc[0].obj_id;

    const responseLinkProc = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: alias_proc,
        company_id,
        link: true,
        obj_to_type: OBJ_TYPE.CONV,
        obj_to_id: sum_process,
        project_id,
        stageDev_id,
      }),
    );
    expect(responseLinkProc.status).toBe(RESP_STATUS.OK);
    const { ops: opsLinkAliasProc } = responseAliasProc.body as LinkAliasResponse;
    expect(opsLinkAliasProc[0].proc).toEqual(PROC_STATUS.OK);

    const responseAliasSD = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: short_name_al_sd,
        title,
        project_id,
        stage_id: stageDev_id,
      }),
    );
    expect(responseAliasSD.status).toBe(RESP_STATUS.OK);
    const { ops: opsCreateAliasSD } = responseAliasSD.body as CreateAliasResponse;
    alias_sd = opsCreateAliasSD[0].obj_id;

    const responseLinkSD = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: alias_sd,
        company_id,
        link: true,
        obj_to_type: OBJ_TYPE.CONV,
        obj_to_id: diagram_process,
        project_id,
        stageDev_id,
      }),
    );
    expect(responseLinkSD.status).toBe(RESP_STATUS.OK);
    const { ops: opsLinkAliasSD } = responseLinkSD.body as LinkAliasResponse;
    expect(opsLinkAliasSD[0].proc).toEqual(PROC_STATUS.OK);

    const responseTaskSD = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: diagram_process,
        ref: referens,
        data: {
          obj: {
            keyObj: 'valueObj',
          },
          arr: [
            'test',
            {
              keyArr: 'valueArr',
            },
          ],
        },
      }),
    );
    expect(responseTaskSD.status).toBe(RESP_STATUS.OK);
    const { ops: opsCTaskSD } = responseTaskSD.body as CreateTaskResponse;
    expect(opsCTaskSD[0].proc).toEqual(PROC_STATUS.OK);

    const responseModifSum = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID_sum,
        conv_id: sum_process,
        title: 'proccesSum',
        obj_type: NODE_TYPE.Normal,
        logics: [
          {
            type: NODE_LOGIC_TYPE.ApiSum,
            err_node_id: '',
            extra: [{ id: '1234567890123', name: 'countSum', value: '1' }],
          },
          { to_node_id: final_node_ID_sum, type: NODE_LOGIC_TYPE.Go, node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifSum.status).toBe(RESP_STATUS.OK);

    const responseCommitSum = await requestConfirm(api, sum_process, company_id);
    expect(responseCommitSum.status).toBe(RESP_STATUS.OK);

    for (let i = 0; i < 5; i++) {
      const responseTaskSum = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          conv_id: sum_process,
          data: {},
        }),
      );
      expect(responseTaskSum.status).toBe(RESP_STATUS.OK);
      const { ops: opsCTaskSum } = responseTaskSum.body as CreateTaskResponse;
      expect(opsCTaskSum[0].proc).toEqual(PROC_STATUS.OK);
    }

    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        conv_id: set_param_process,
        title,
        obj_type: 0,
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(RESP_STATUS.OK);
    nodeSum_id = responseCreateNode.body.ops[0].obj_id;

    const responseModifySum = await await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeSum_id,
        conv_id: set_param_process,
        title,
        obj_type: NODE_TYPE.Normal,
        logics: [
          {
            type: NODE_LOGIC_TYPE.ApiSum,
            err_node_id: '',
            extra: [{ id: '1234567890124', name: 'counter', value: '1' }],
          },
          { to_node_id: process_node_ID_set_param, type: NODE_LOGIC_TYPE.Go, node_title: conv_type },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifySum.status).toBe(RESP_STATUS.OK);

    const responseModifyNodeSet = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID_set_param,
        conv_id: set_param_process,
        title,
        obj_type: NODE_TYPE.Normal,
        logics: [
          {
            type: 'set_param',
            err_node_id: '',
            extra: {
              arr: `{{conv[${diagram_process}].ref[${referens}].arr}}`,
              arrAlSn: `{{conv[@${short_name_al_sd}].ref[${referens}].arr}}`,
              arrKey: `{{conv[${diagram_process}].ref[${referens}].arr[1].keyArr}}`,
              arrKeyAlSn: `{{conv[@${short_name_al_sd}].ref[${referens}].arr[1].keyArr}}`,
              convNodeSum: `{{conv[${sum_process}].node[${process_node_ID_sum}].1234567890123}}`,
              convNodeSumAl: `{{conv[@${short_name_al_prco_sum}].node[${process_node_ID_sum}].1234567890123}}`,
              count: `{{conv[${diagram_process}].node[${process_node_IDSD}].count}}`,
              countAlSn: `{{conv[@${short_name_al_sd}].node[${process_node_IDSD}].count}}`,
              finalSuccess: `{{node[${final_node_ID_set_param}].count}}`,
              nodeSum: `{{node[${nodeSum_id}].1234567890124}}`,
              obj: `{{conv[${diagram_process}].ref[${referens}].obj}}`,
              objAlSn: `{{conv[@${short_name_al_sd}].ref[${referens}].obj}}`,
              objKey: `{{conv[${diagram_process}].ref[${referens}].obj.keyObj}}`,
              objKeyAlSn: `{{conv[@${short_name_al_sd}].ref[${referens}].obj.keyObj}}`,
            },
            extra_type: {
              count: 'number',
              arr: 'array',
              obj: 'object',
              arrKey: 'string',
              objKey: 'string',
              nodeSum: 'number',
              convNodeSum: 'number',
              convNodeSumAl: 'number',
              finalSuccess: 'number',
              countAlSn: 'number',
              arrAlSn: 'array',
              objAlSn: 'object',
              arrKeyAlSn: 'string',
              objKeyAlSn: 'string',
            },
          },
          { to_node_id: final_node_ID_set_param, type: NODE_LOGIC_TYPE.Go, node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifyNodeSet.status).toBe(RESP_STATUS.OK);

    const responseModifyStart = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: start_node_ID_set_param,
        conv_id: set_param_process,
        title: 'Start',
        obj_type: NODE_TYPE.Start,
        logics: [{ to_node_id: nodeSum_id, type: NODE_LOGIC_TYPE.Go }],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifyStart.status).toBe(RESP_STATUS.OK);

    const responseCommitSet = await requestConfirm(api, set_param_process, company_id);
    expect(responseCommitSet.status).toBe(RESP_STATUS.OK);

    const responseTaskSet = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: set_param_process,
        data: {},
      }),
    );
    expect(responseTaskSet.status).toBe(RESP_STATUS.OK);
    const { ops: opsCTaskSet } = responseTaskSet.body as CreateTaskResponse;
    expect(opsCTaskSet[0].proc).toEqual(PROC_STATUS.OK);

    const responseVersion = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,
        project_id,
        stage_id: stageDev_id,
        stage_from_id: stageDev_id,
        vsn: '0.0.1',
      }),
    );
    expect(responseVersion.status).toBe(RESP_STATUS.OK);
    version_id = responseVersion.body.ops[0].obj_id;

    const exec = promisify(execCallback);
    const { stdout } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/upload`,
        REWRITE_ALIAS: 'true',
        WITH_ALIAS: 'true',
        FILE_NAME: 'stage_compare_merge.zip',
        VALIDATE_SCHEME: 'false',
        COMPANY_ID: company_id,
        SKIP_ROOT: 'false',
      },
    });
    debug(stdout);
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.ops[0].proc).toBe(PROC_STATUS.OK);
    schema_id = jsonResponse.ops[0].scheme_id;
  });

  const testCasesPositiveCompare = [
    {
      description: 'dev -> prod',
      obj_to_type: OBJ_TYPE.STAGE,
      obj_id: (): string | number => stageDev_id,
    },
    {
      description: 'version -> prod',
      obj_to_type: OBJ_TYPE.VERSION,
      obj_id: (): string | number => version_id,
    },
  ];

  describe.each(testCasesPositiveCompare)('$description', ({ obj_to_type, obj_id }): void => {
    test('should compare ', async (): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPARE,
          obj: OBJ_TYPE.OBJ_SCHEME,
          company_id,
          obj_id: stageProd_id,
          obj_type: OBJ_TYPE.STAGE,
          obj_to_id: obj_id(),
          obj_to_type,
          project_id,
          num_stat: true,
          diff_status: true,
        }),
      );
      expect(response.status).toBe(RESP_STATUS.OK);
      const { ops: opsCompare } = response.body as CompareResponse;
      const { proc: procCompare, list: listCompare } = opsCompare[0];
      expect(procCompare).toEqual(PROC_STATUS.OK);
      const aliasArr = [alias_sd, alias_proc];
      const aliasItems = (listCompare as Array<any>).filter((item): boolean => item.obj_type === 'alias');
      aliasItems.forEach((alias): void => {
        expect(alias).toBeDefined();
        expect(alias.__num_stat).toEqual({ added: 1, changed: 0, deleted: 0 });
        expect(alias.__status).toEqual('added');
        expect(aliasArr).toContain(alias.obj_id);
        expect(alias.parent_id).toEqual(stageProd_id);
      });
      const convArr = [set_param_process, sum_process, diagram_process];
      const convItems = (listCompare as Array<any>).filter((item): boolean => item.obj_type === 'conv');
      convItems.forEach((conv): void => {
        expect(conv).toBeDefined();
        expect(conv.__num_stat).toEqual({ added: 1, changed: 0, deleted: 0 });
        expect(conv.__status).toEqual('added');
        expect(convArr).toContain(conv.obj_id);
        expect(conv.parent_id).toEqual(stageProd_id);
      });
    });
  });

  const testCasesPositiveMerge = [
    {
      description: 'dev -> prod',
      obj_to_type: OBJ_TYPE.STAGE,
      obj_id: (): string | number => stageDev_id,
    },
    {
      description: 'version -> prod',
      obj_to_type: OBJ_TYPE.VERSION,
      obj_id: (): string | number => version_id,
    },
  ];

  describe.each(testCasesPositiveMerge)('$description', ({ obj_to_type, obj_id }): void => {
    test('should merge', async (): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MERGE,
          obj: OBJ_TYPE.OBJ_SCHEME,
          company_id,
          obj_id: stageProd_id,
          obj_type: OBJ_TYPE.STAGE,
          obj_to_id: obj_id(),
          obj_to_type,
          project_id,
          apply_mode: true,
          async: false,
        }),
      );
      expect(response.status).toBe(RESP_STATUS.OK);
      const { ops: opsMerge } = response.body as MergeResponse;
      const { proc: prockMerge, diff: diffMerge } = opsMerge[0];
      expect(prockMerge).toEqual(PROC_STATUS.OK);
      expect(diffMerge.obj_id).toEqual(stageProd_id);

      const responseList = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: stageProd_id,
          project_id,
        }),
      );
      expect(responseList.status).toBe(RESP_STATUS.OK);
      const { proc: proc, list: list } = responseList.body.ops[0];
      expect(proc).toEqual(PROC_STATUS.OK);
      expect(list).toBeArrayOfSize(3);
      newConvSetProd = (list as Array<any>).find(item => item.title === processSet).obj_id;
      newConvSumProd = (list as Array<any>).find(item => item.title === processSum).obj_id;
      newSDProd = (list as Array<any>).find(item => item.conv_type === 'state').obj_id;

      const responseListConv = await requestListConv(api, newSDProd, company_id, project_id, stageProd_id);
      expect(responseListConv.status).toBe(RESP_STATUS.OK);
      process_node_IDSD_prod = (responseListConv.body.ops[0].list as Array<any>).find(item => item.title === 'New user')
        .obj_id;

      const responseTaskSD = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          conv_id: newSDProd,
          ref: referens,
          data: dataSD,
        }),
      );
      expect(responseTaskSD.status).toBe(RESP_STATUS.OK);
      const { ops: opsCTaskSD } = responseTaskSD.body as CreateTaskResponse;
      const { proc: procTaskSD, obj_id: ObjIdTaskSD } = opsCTaskSD[0];
      expect(procTaskSD).toEqual(PROC_STATUS.OK);
      taskIDSD = ObjIdTaskSD;

      for (let i = 0; i < 3; i++) {
        const responseTaskSum = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.TASK,
            conv_id: newConvSumProd,
            data: {},
          }),
        );
        expect(responseTaskSum.status).toBe(RESP_STATUS.OK);
        const { ops: opsCTaskSum } = responseTaskSum.body as CreateTaskResponse;
        expect(opsCTaskSum[0].proc).toEqual(PROC_STATUS.OK);
      }

      await new Promise(r => setTimeout(r, 2000));

      const responseTaskSet = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          conv_id: newConvSetProd,
          data: {},
        }),
      );
      expect(responseTaskSet.status).toBe(RESP_STATUS.OK);
      const { ops: opsCTaskSet } = responseTaskSet.body as CreateTaskResponse;
      const { proc: procTask, obj: objTask, obj_id: ObjIdTask } = opsCTaskSet[0];
      expect(procTask).toEqual(PROC_STATUS.OK);
      expect(objTask).toEqual(OBJ_TYPE.TASK);
      taskID = ObjIdTask;

      await new Promise(r => setTimeout(r, 3000));

      const responseShow = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.TASK,
          conv_id: newConvSetProd,
          ref_or_obj_id: taskID,
        }),
      );
      expect(responseShow.status).toBe(RESP_STATUS.OK);
      const { proc: prockShowTask, data: dataShowTask } = responseShow.body.ops[0];
      expect(prockShowTask).toEqual(PROC_STATUS.OK);
      expect(dataShowTask.arr).toEqual(dataSD.arr);
      expect(dataShowTask.arrAlSn).toEqual(dataSD.arr);
      expect(dataShowTask.arrKey).toEqual(dataSD.arr[1].keyArr);
      expect(dataShowTask.arrKeyAlSn).toEqual('valueArrPROD');
      expect(dataShowTask.convNodeSum).toBeOneOf([3, 6]);
      expect(dataShowTask.convNodeSumAl).toBeOneOf([3, 6]);
      expect(dataShowTask.count).toEqual(1);
      expect(dataShowTask.countAlSn).toEqual(1);
      expect(dataShowTask.finalSuccess).toBeOneOf([0, 1]);
      expect(dataShowTask.nodeSum).toBeOneOf([0, 1]);
      expect(dataShowTask.obj).toEqual(dataSD.obj);
      expect(dataShowTask.objAlSn).toEqual(dataSD.obj);
      expect(dataShowTask.objKey).toEqual(dataSD.obj.keyObj);
      expect(dataShowTask.objKeyAlSn).toEqual(dataSD.obj.keyObj);

      const responseDeleteTask = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.TASK,
          company_id,
          conv_id: newSDProd,
          obj_id: taskIDSD,
          node_id: process_node_IDSD_prod,
        }),
      );
      expect(responseDeleteTask.status).toBe(RESP_STATUS.OK);
      const { ops: opsDTask } = responseDeleteTask.body as DeleteTaskResponse;
      expect(opsDTask[0].proc).toEqual(PROC_STATUS.OK);
    });
  });

  test(`upload file and compare`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPARE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        company_id,
        obj_id: stageProd_id,
        obj_type: OBJ_TYPE.STAGE,
        obj_to_id: schema_id,
        obj_to_type: OBJ_TYPE.SCHEME,
        project_id,
        num_stat: true,
        diff_status: true,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    const { ops: opsCompare } = response.body as CompareResponse;
    const { proc: procCompare, list: listCompare } = opsCompare[0];
    expect(procCompare).toEqual(PROC_STATUS.OK);
    const aliasArr = [218619, 218620];
    const aliasItems = (listCompare as Array<any>).filter(
      (item): boolean => item.obj_type === 'alias' && item.__status === 'added',
    );
    aliasItems.forEach((alias): void => {
      expect(alias).toBeDefined();
      expect(alias.__num_stat).toEqual({ added: 1, changed: 0, deleted: 0 });
      expect(aliasArr).toContain(alias.obj_id);
      expect(alias.parent_id).toEqual(stageProd_id);
    });
    const convArr = [2190183, 2190184, 2190182];
    const convItems = (listCompare as Array<any>).filter(
      (item): boolean => item.obj_type === 'conv' && item.__status === 'added',
    );
    convItems.forEach((conv): void => {
      expect(conv).toBeDefined();
      expect(conv.__num_stat).toEqual({ added: 1, changed: 0, deleted: 0 });
      expect(convArr).toContain(conv.obj_id);
      expect(conv.parent_id).toEqual(stageProd_id);
    });
  });

  test('should first merge file -> prod and second merge file -> prod', async (): Promise<void> => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        company_id,
        obj_id: stageProd_id,
        obj_type: OBJ_TYPE.STAGE,
        obj_to_id: schema_id,
        obj_to_type: OBJ_TYPE.SCHEME,
        project_id,
        apply_mode: true,
        async: false,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    const { ops: opsMerge } = response.body as MergeResponse;
    const { proc: prockMerge, diff: diffMerge } = opsMerge[0];
    expect(prockMerge).toEqual(PROC_STATUS.OK);
    expect(diffMerge.obj_id).toEqual(stageProd_id);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        company_id,
        obj_id: stageProd_id,
        project_id,
      }),
    );
    expect(responseList.status).toBe(RESP_STATUS.OK);
    const { proc: proc, list: list } = responseList.body.ops[0];
    expect(proc).toEqual(PROC_STATUS.OK);
    expect(list).toBeArrayOfSize(3);

    newConvSetProd = (list as Array<any>).find(item => item.title === 'Set').obj_id;
    newConvSumProd = (list as Array<any>).find(item => item.title === 'Sum').obj_id;
    newSDProd = (list as Array<any>).find(item => item.title === 'SD').obj_id;

    const responseListConv = await requestListConv(api, newSDProd, company_id, project_id, stageProd_id);
    expect(responseListConv.status).toBe(RESP_STATUS.OK);
    process_node_IDSD_prod = (responseListConv.body.ops[0].list as Array<any>).find(item => item.title === 'New user')
      .obj_id;

    for (let i = 0; i < 3; i++) {
      const responseTaskSum = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          conv_id: newConvSumProd,
          data: {},
        }),
      );
      expect(responseTaskSum.status).toBe(RESP_STATUS.OK);
      const { ops: opsCTaskSum } = responseTaskSum.body as CreateTaskResponse;
      expect(opsCTaskSum[0].proc).toEqual(PROC_STATUS.OK);
    }

    await new Promise(r => setTimeout(r, 2000));

    const responseSecondMerge = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        company_id,
        obj_id: stageProd_id,
        obj_type: OBJ_TYPE.STAGE,
        obj_to_id: schema_id,
        obj_to_type: OBJ_TYPE.SCHEME,
        project_id,
        apply_mode: true,
        async: false,
      }),
    );
    expect(responseSecondMerge.status).toBe(RESP_STATUS.OK);
    const { ops: opsSMerge } = response.body as MergeResponse;
    const { proc: prockMergeS, diff: diffMergeS } = opsSMerge[0];
    expect(prockMergeS).toEqual(PROC_STATUS.OK);
    expect(diffMergeS.obj_id).toEqual(stageProd_id);

    const responseTaskSD = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: newSDProd,
        ref: referens,
        data: dataSD,
      }),
    );
    expect(responseTaskSD.status).toBe(RESP_STATUS.OK);
    const { ops: opsCTaskSD } = responseTaskSD.body as CreateTaskResponse;
    const { proc: procTaskSD, obj_id: ObjIdTaskSD } = opsCTaskSD[0];
    expect(procTaskSD).toEqual(PROC_STATUS.OK);
    taskIDSD = ObjIdTaskSD;

    for (let i = 0; i < 4; i++) {
      const responseTaskSum = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          conv_id: newConvSumProd,
          data: {},
        }),
      );
      expect(responseTaskSum.status).toBe(RESP_STATUS.OK);
      const { ops: opsCTaskSum } = responseTaskSum.body as CreateTaskResponse;
      expect(opsCTaskSum[0].proc).toEqual(PROC_STATUS.OK);
    }

    await new Promise(r => setTimeout(r, 2000));

    const responseTaskSet = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: newConvSetProd,
        data: {},
      }),
    );
    expect(responseTaskSet.status).toBe(RESP_STATUS.OK);
    const { ops: opsCTaskSet } = responseTaskSet.body as CreateTaskResponse;
    const { proc: procTask, obj: objTask, obj_id: ObjIdTask } = opsCTaskSet[0];
    expect(procTask).toEqual(PROC_STATUS.OK);
    expect(objTask).toEqual(OBJ_TYPE.TASK);
    taskID = ObjIdTask;

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: newConvSetProd,
        ref_or_obj_id: taskID,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    const { proc: prockShowTask, data: dataShowTask } = responseShow.body.ops[0];
    expect(prockShowTask).toEqual(PROC_STATUS.OK);
    expect(dataShowTask.arr).toEqual(dataSD.arr);
    expect(dataShowTask.arrAlSn).toEqual(dataSD.arr);
    expect(dataShowTask.arrKey).toEqual(dataSD.arr[1].keyArr);
    expect(dataShowTask.arrKeyAlSn).toEqual('valueArrPROD');
    expect(dataShowTask.convNodeSum).toEqual(7);
    expect(dataShowTask.convNodeSumAl).toEqual(7);
    expect(dataShowTask.count).toEqual(1);
    expect(dataShowTask.countAlSn).toEqual(1);
    expect(dataShowTask.finalSuccess).toEqual(0);
    expect(dataShowTask.nodeSum).toEqual(0);
    expect(dataShowTask.obj).toEqual(dataSD.obj);
    expect(dataShowTask.objAlSn).toEqual(dataSD.obj);
    expect(dataShowTask.objKey).toEqual(dataSD.obj.keyObj);
    expect(dataShowTask.objKeyAlSn).toEqual(dataSD.obj.keyObj);
  });

  afterAll(
    async (): Promise<void> => {
      const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, project_id, company_id);
      expect(response.status).toBe(RESP_STATUS.OK);
    },
  );
});
