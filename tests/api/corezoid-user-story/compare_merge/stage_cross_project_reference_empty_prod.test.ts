import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
  generateName,
} from '../../../../utils/corezoidRequest';
import {
  requestConfirm,
  requestDeleteObj,
  requestListConv,
  requestCreateObjNew,
} from '../../../../application/api/ApiObj';
import { NODE_TYPE, NODE_LOGIC_TYPE } from '../../../../application/api/obj_types/node';
import { CreateAliasResponse } from '../../../../application/api/obj_types/alias';
import { CompareResponse, MergeResponse } from '../../../../application/api/obj_types/obj_scheme';
import { verifyApiCopyParameters } from './utils/compare_merge_utils';

describe('Cross-Project Reference with Empty Prod Testing', () => {
  // Helper function to clean production stage
  async function cleanProductionStage(
    api: ApiKeyClient,
    stageId: string | number,
    projectId: string | number,
    companyId: any,
  ): Promise<void> {
    const responseStageModify = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.STAGE,
        obj_id: stageId,
        company_id: companyId,
        project_id: projectId,
        immutable: false,
      }),
    );
    expect(responseStageModify.status).toBe(RESP_STATUS.OK);

    const responseAliases = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id: companyId,
        project_id: projectId,
        stage_id: stageId,
      }),
    );

    if (responseAliases.status === RESP_STATUS.OK) {
      const aliasList = responseAliases.body.ops[0].list;

      if (aliasList && Array.isArray(aliasList)) {
        for (const item of aliasList) {
          await api.request(
            createRequestWithOps({
              type: REQUEST_TYPE.DELETE,
              obj: OBJ_TYPE.ALIAS,
              obj_id: item.obj_id,
              company_id: companyId,
              project_id: projectId,
              stage_id: stageId,
            }),
          );
        }
      }
    }

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id: companyId,
        obj_id: stageId,
        project_id: projectId,
        stage_id: stageId,
      }),
    );
    if (responseList.status === RESP_STATUS.OK) {
      const list = responseList.body.ops[0].list;

      if (list && Array.isArray(list)) {
        for (const item of list) {
          await api.request(
            createRequestWithOps({
              type: REQUEST_TYPE.DELETE,
              obj: OBJ_TYPE.CONV,
              obj_id: item.obj_id,
              company_id: companyId,
              project_id: projectId,
              stage_id: stageId,
            }),
          );
        }
      }
    }

    const responseStageModifyBack = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.STAGE,
        obj_id: stageId,
        company_id: companyId,
        project_id: projectId,
        immutable: true,
      }),
    );
    expect(responseStageModifyBack.status).toBe(RESP_STATUS.OK);
  }
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;

  let project1_id: string | number;
  let project1_short_name: string;
  let project1_stage_dev_id: string | number;
  let project1_stage_prod_id: string | number;
  let project1_conv_process_id: string | number;
  let project1_conv_sd_id: string | number;
  let project1_alias_id: string | number;
  let project1_alias_short_name: string;
  let project1_process_node_id: string | number;
  let project1_final_node_id: string | number;

  let project2_id: string | number;
  let project2_short_name: string;
  let project2_stage_dev_id: string | number;
  let project2_conv_process_id: string | number;
  let project2_alias_id: string | number;
  let project2_alias_short_name: string;

  const conv_type = 'process';
  let title: string;
  let processCopy_dev: string;
  let dev_state_process_name: string;
  let version_id: string | number;

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;

      project1_short_name = generateName(OBJ_TYPE.PROJECT);
      project2_short_name = generateName(OBJ_TYPE.PROJECT);
      project1_alias_short_name = generateName(OBJ_TYPE.ALIAS);
      project2_alias_short_name = generateName(OBJ_TYPE.ALIAS);
      title = generateName(OBJ_TYPE.OBJS);
      processCopy_dev = generateName(OBJ_TYPE.OBJS);
      dev_state_process_name = generateName(OBJ_TYPE.OBJS);

      const responseProject1 = await requestCreateObjNew(
        api,
        OBJ_TYPE.PROJECT,
        company_id,
        generateName(OBJ_TYPE.PROJECT),
        {
          short_name: project1_short_name,
          stages: [
            { title: 'production', immutable: true },
            { title: 'develop', immutable: false },
          ],
        },
      );
      expect(responseProject1.status).toBe(RESP_STATUS.OK);
      const { obj_id: objIdProject1, stages: arrStages1 } = responseProject1.body.ops[0];
      project1_id = objIdProject1;
      project1_stage_prod_id = arrStages1[0];
      project1_stage_dev_id = arrStages1[1];

      const responseProject2 = await requestCreateObjNew(
        api,
        OBJ_TYPE.PROJECT,
        company_id,
        generateName(OBJ_TYPE.PROJECT),
        {
          short_name: project2_short_name,
          stages: [{ title: 'develop', immutable: false }],
        },
      );
      expect(responseProject2.status).toBe(RESP_STATUS.OK);
      const { obj_id: objIdProject2, stages: arrStages2 } = responseProject2.body.ops[0];
      project2_id = objIdProject2;
      project2_stage_dev_id = arrStages2[0];

      const responseProcess2 = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, 'Process2', {
        conv_type,
        stage_id: project2_stage_dev_id,
        project_id: project2_id,
      });
      expect(responseProcess2.status).toBe(RESP_STATUS.OK);
      project2_conv_process_id = responseProcess2.obj_id;

      const responseAlias2 = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          short_name: project2_alias_short_name,
          title: 'Alias2',
          project_id: project2_id,
          stage_id: project2_stage_dev_id,
        }),
      );
      expect(responseAlias2.status).toBe(RESP_STATUS.OK);
      const { ops: opsCreateAlias2 } = responseAlias2.body as CreateAliasResponse;
      project2_alias_id = opsCreateAlias2[0].obj_id;

      const responseLinkAlias2 = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.ALIAS,
          obj_id: project2_alias_id,
          company_id,
          link: true,
          obj_to_type: OBJ_TYPE.CONV,
          obj_to_id: project2_conv_process_id,
          project_id: project2_id,
          stage_id: project2_stage_dev_id,
        }),
      );
      expect(responseLinkAlias2.status).toBe(RESP_STATUS.OK);

      const responseSD1 = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, dev_state_process_name, {
        conv_type: 'state',
        stage_id: project1_stage_dev_id,
        project_id: project1_id,
      });
      expect(responseSD1.status).toBe(RESP_STATUS.OK);
      project1_conv_sd_id = responseSD1.obj_id;

      const responseAlias1 = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.ALIAS,
          company_id,
          short_name: project1_alias_short_name,
          title,
          project_id: project1_id,
          stage_id: project1_stage_dev_id,
        }),
      );
      expect(responseAlias1.status).toBe(RESP_STATUS.OK);
      const { ops: opsCreateAlias1 } = responseAlias1.body as CreateAliasResponse;
      project1_alias_id = opsCreateAlias1[0].obj_id;

      const responseLinkAlias1 = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.ALIAS,
          obj_id: project1_alias_id,
          company_id,
          link: true,
          obj_to_type: OBJ_TYPE.CONV,
          obj_to_id: project1_conv_sd_id,
          project_id: project1_id,
          stage_id: project1_stage_dev_id,
        }),
      );
      expect(responseLinkAlias1.status).toBe(RESP_STATUS.OK);

      const responseProcess1 = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, processCopy_dev, {
        conv_type,
        stage_id: project1_stage_dev_id,
        project_id: project1_id,
      });
      expect(responseProcess1.status).toBe(RESP_STATUS.OK);
      project1_conv_process_id = responseProcess1.obj_id;

      const responseListProcess1 = await requestListConv(api, project1_conv_process_id, company_id);
      const { list: listProcess1 } = responseListProcess1.body.ops[0];
      project1_process_node_id = (listProcess1 as Array<any>).find((item): boolean => item.title === conv_type).obj_id;
      project1_final_node_id = (listProcess1 as Array<any>).find((item): boolean => item.title === 'final').obj_id;
    },
  );

  const testScenarios = [
    {
      name: 'All numeric IDs',
      conv_id: (): string | number => project2_conv_process_id,
      stage_id: (): string | number => project2_stage_dev_id,
      project_id: (): string | number => project2_id,
      newConv_id: (): string | number => project2_conv_process_id,
      newStage_id: (): string | number => project2_stage_dev_id,
      newProject_id: (): string | number => project2_id,
      num_statConv: (): { added: number; changed: number; deleted: number } => ({ added: 1, changed: 0, deleted: 0 }),
      num_statAlias: (): { added: number; changed: number; deleted: number } => ({ added: 1, changed: 0, deleted: 0 }),
      num_statDiagram: (): { added: number; changed: number; deleted: number } => ({
        added: 1,
        changed: 0,
        deleted: 0,
      }),
      statusConv: (): string => 'added',
      statusAlias: (): string => 'added',
      statusDiagram: (): string => 'added',
    },
    {
      name: 'Alias for conv_id, numeric stage_id/project_id',
      conv_id: (): string => `@${project2_alias_short_name}`,
      stage_id: (): string | number => project2_stage_dev_id,
      project_id: (): string | number => project2_id,
      newConv_id: (): string => `@${project2_alias_short_name}`,
      newStage_id: (): string | number => project2_stage_dev_id,
      newProject_id: (): string | number => project2_id,
      num_statConv: (): { added: number; changed: number; deleted: number } => ({ added: 1, changed: 0, deleted: 0 }),
      num_statAlias: (): { added: number; changed: number; deleted: number } => ({ added: 1, changed: 0, deleted: 0 }),
      num_statDiagram: (): { added: number; changed: number; deleted: number } => ({
        added: 1,
        changed: 0,
        deleted: 0,
      }),
      statusConv: (): string => 'added',
      statusAlias: (): string => 'added',
      statusDiagram: (): string => 'added',
    },
  ];

  describe.each(testScenarios)('Testing with $name', (scenario): void => {
    beforeEach(
      async (): Promise<void> => {
        // Clean up production stage before each test to ensure it's empty
        await cleanProductionStage(api, project1_stage_prod_id, project1_id, company_id);

        const responseModifCopy = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MODIFY,
            obj: OBJ_TYPE.NODE,
            obj_id: project1_process_node_id,
            conv_id: project1_conv_process_id,
            title: 'processCopy',
            obj_type: NODE_TYPE.Normal,
            logics: [
              {
                type: NODE_LOGIC_TYPE.ApiCopy,
                mode: 'create',
                data: {},
                data_type: {},
                group: 'all',
                ref: '',
                send_parent_data: false,
                conv_id: scenario.conv_id(),
                stage_id: scenario.stage_id(),
                project_id: scenario.project_id(),
                err_node_id: '',
              },
              { to_node_id: project1_final_node_id, type: NODE_LOGIC_TYPE.Go, node_title: 'final' },
            ],
            semaphors: [],
            version: 22,
          }),
        );
        expect(responseModifCopy.status).toBe(RESP_STATUS.OK);

        const responseCommitCopy = await requestConfirm(api, project1_conv_process_id, company_id);
        expect(responseCommitCopy.status).toBe(RESP_STATUS.OK);

        const responseVersion = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.VERSION,
            company_id,
            project_id: project1_id,
            stage_id: project1_stage_dev_id,
            stage_from_id: project1_stage_dev_id,
            vsn: `0.0.${Math.floor(Math.random() * 1000)}`,
          }),
        );
        expect(responseVersion.status).toBe(RESP_STATUS.OK);
        version_id = responseVersion.body.ops[0].obj_id;
      },
    );

    test('should successfully compare stage to empty stage', async (): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPARE,
          obj: OBJ_TYPE.OBJ_SCHEME,
          company_id,
          obj_id: project1_stage_prod_id,
          obj_type: OBJ_TYPE.STAGE,
          obj_to_id: project1_stage_dev_id,
          obj_to_type: OBJ_TYPE.STAGE,
          project_id: project1_id,
          num_stat: true,
          diff_status: true,
        }),
      );
      expect(response.status).toBe(RESP_STATUS.OK);

      const { ops: opsCompare } = response.body as CompareResponse;
      const { proc: procCompare, list: listCompare } = opsCompare[0];
      expect(procCompare).toEqual(PROC_STATUS.OK);

      const convItem = (listCompare as Array<any>).filter((item): boolean => item.title === processCopy_dev);
      if (convItem.length > 0) {
        expect(convItem[0].__num_stat).toEqual(scenario.num_statConv());
        expect(convItem[0].__status).toEqual(scenario.statusConv());
      }

      const aliasItem = (listCompare as Array<any>).filter(
        (item): boolean => item.obj_type === 'alias' && item.short_name === project1_alias_short_name,
      );
      if (aliasItem.length > 0) {
        expect(aliasItem[0].__status).toEqual(scenario.statusAlias());
      }

      const diagramItem = (listCompare as Array<any>).filter((item): boolean => item.title === dev_state_process_name);
      if (diagramItem.length > 0) {
        expect(diagramItem[0].__status).toEqual(scenario.statusDiagram());
      }
    });

    test('should successfully merge stage to empty stage', async (): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MERGE,
          obj: OBJ_TYPE.OBJ_SCHEME,
          company_id,
          obj_id: project1_stage_prod_id,
          obj_type: OBJ_TYPE.STAGE,
          obj_to_id: project1_stage_dev_id,
          obj_to_type: OBJ_TYPE.STAGE,
          project_id: project1_id,
          apply_mode: true,
          async: false,
        }),
      );
      expect(response.status).toBe(RESP_STATUS.OK);

      const { ops: opsMerge } = response.body as MergeResponse;
      const { proc: procMerge, diff: diffMerge } = opsMerge[0];
      expect(procMerge).toEqual(PROC_STATUS.OK);
      expect(diffMerge.obj_id).toEqual(project1_stage_prod_id);

      const responseList = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: project1_stage_prod_id,
          project_id: project1_id,
        }),
      );
      expect(responseList.status).toBe(RESP_STATUS.OK);

      const { list: listStage } = responseList.body.ops[0];
      const mergedProcess = (listStage as Array<any>).find((item): boolean => item.title === processCopy_dev);
      expect(mergedProcess).toBeDefined();

      const responseListProcess = await requestListConv(
        api,
        mergedProcess.obj_id,
        company_id,
        project1_id,
        project1_stage_prod_id,
      );
      expect(responseListProcess.status).toBe(RESP_STATUS.OK);

      const { list: listProcess } = responseListProcess.body.ops[0];
      const processNode = (listProcess as Array<any>).find((item): boolean => item.title === 'processCopy');
      expect(processNode).toBeDefined();

      const apiCopyLogic = processNode.logics.find((logic: any): boolean => logic.type === NODE_LOGIC_TYPE.ApiCopy);
      expect(apiCopyLogic).toBeDefined();

      await verifyApiCopyParameters(
        api,
        apiCopyLogic,
        scenario,
        project1_stage_prod_id,
        company_id,
        project1_id,
        'newStage_id',
        'newConv_id',
        'newProject_id',
      );
    });

    test('should successfully compare version to empty stage', async (): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPARE,
          obj: OBJ_TYPE.OBJ_SCHEME,
          company_id,
          obj_id: project1_stage_prod_id,
          obj_type: OBJ_TYPE.STAGE,
          obj_to_id: version_id,
          obj_to_type: OBJ_TYPE.VERSION,
          project_id: project1_id,
          num_stat: true,
          diff_status: true,
        }),
      );
      expect(response.status).toBe(RESP_STATUS.OK);

      const { ops: opsCompare } = response.body as CompareResponse;
      const { proc: procCompare, list: listCompare } = opsCompare[0];
      expect(procCompare).toEqual(PROC_STATUS.OK);

      const convItem = (listCompare as Array<any>).filter((item): boolean => item.title === processCopy_dev);
      if (convItem.length > 0) {
        expect(convItem[0].__num_stat).toEqual(scenario.num_statConv());
        expect(convItem[0].__status).toEqual(scenario.statusConv());
      }

      const aliasItem = (listCompare as Array<any>).filter(
        (item): boolean => item.obj_type === 'alias' && item.short_name === project1_alias_short_name,
      );
      if (aliasItem.length > 0) {
        expect(aliasItem[0].__status).toEqual(scenario.statusAlias());
      }

      const diagramItem = (listCompare as Array<any>).filter((item): boolean => item.title === dev_state_process_name);
      if (diagramItem.length > 0) {
        expect(diagramItem[0].__status).toEqual(scenario.statusDiagram());
      }
    });

    test('should successfully merge version to empty stage', async (): Promise<void> => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MERGE,
          obj: OBJ_TYPE.OBJ_SCHEME,
          company_id,
          obj_id: project1_stage_prod_id,
          obj_type: OBJ_TYPE.STAGE,
          obj_to_id: version_id,
          obj_to_type: OBJ_TYPE.VERSION,
          project_id: project1_id,
          apply_mode: true,
          async: false,
        }),
      );
      expect(response.status).toBe(RESP_STATUS.OK);

      const { ops: opsMerge } = response.body as MergeResponse;
      const { proc: procMerge, diff: diffMerge } = opsMerge[0];
      expect(procMerge).toEqual(PROC_STATUS.OK);
      expect(diffMerge.obj_id).toEqual(project1_stage_prod_id);

      const responseList = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: project1_stage_prod_id,
          project_id: project1_id,
        }),
      );
      expect(responseList.status).toBe(RESP_STATUS.OK);

      const { list: listStage } = responseList.body.ops[0];
      const mergedProcess = (listStage as Array<any>).find((item): boolean => item.title === processCopy_dev);
      expect(mergedProcess).toBeDefined();

      const responseListProcess = await requestListConv(
        api,
        mergedProcess.obj_id,
        company_id,
        project1_id,
        project1_stage_prod_id,
      );
      expect(responseListProcess.status).toBe(RESP_STATUS.OK);

      const { list: listProcess } = responseListProcess.body.ops[0];
      const processNode = (listProcess as Array<any>).find((item): boolean => item.title === 'processCopy');
      expect(processNode).toBeDefined();

      const apiCopyLogic = processNode.logics.find((logic: any): boolean => logic.type === NODE_LOGIC_TYPE.ApiCopy);
      expect(apiCopyLogic).toBeDefined();

      await verifyApiCopyParameters(
        api,
        apiCopyLogic,
        scenario,
        project1_stage_prod_id,
        company_id,
        project1_id,
        'newStage_id',
        'newConv_id',
        'newProject_id',
      );
    });

    afterAll(
      async (): Promise<void> => {
        await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.DELETE,
            obj: OBJ_TYPE.VERSION,
            obj_id: version_id,
            project_id: project1_id,
            company_id,
          }),
        );
        await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.DESTROY,
            obj: OBJ_TYPE.VERSION,
            obj_id: version_id,
            project_id: project1_id,
            company_id,
          }),
        );
      },
    );
  });

  afterAll(
    async (): Promise<void> => {
      const responseDelete1 = await requestDeleteObj(api, OBJ_TYPE.PROJECT, project1_id, company_id);
      expect(responseDelete1.status).toBe(RESP_STATUS.OK);

      const responseDelete2 = await requestDeleteObj(api, OBJ_TYPE.PROJECT, project2_id, company_id);
      expect(responseDelete2.status).toBe(RESP_STATUS.OK);
    },
  );
});
