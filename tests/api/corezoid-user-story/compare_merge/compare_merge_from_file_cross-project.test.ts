import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { requestDeleteObj, requestListConv } from '../../../../application/api/ApiObj';
import { MergeResponse } from '../../../../application/api/obj_types/obj_scheme';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';
import { addMsg } from 'jest-html-reporters/helper';
import { debug } from '../../../../support/utils/logger';

describe('should compare/merge', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let project_id: string | number;
  let prodEmpty_id: string | number;
  let prodOldType_id: string | number;
  let prodNewType_id: string | number;
  let stage_schema_id: string;
  let key: string;
  let secret: string;
  let changedConvOtherProj: any;
  let changedConvOtherStage: any;
  let changedConvThisStage: any;
  let convOtherProj: number;
  let convOtherStage: number;
  let convThisStage: number;
  let convEmpty: number;
  let convSD: number;
  let processCOP1: any;
  let processCOP2: any;
  let processCOP3: any;
  let processCOP4: any;
  let processCOP5: any;
  let processCOP6: any;
  let processCOP7: any;
  let processCOP8: any;
  let processCOP9: any;
  const projFirst = 1019509;
  const stageFirst = 1019511;
  const projSecond = 1019527;
  const stageSecond = 1019529;

  function parseStdoutToJson(stdout: any): any {
    const jsonStartIndex = stdout.indexOf('{');
    const jsonString = stdout.slice(jsonStartIndex);
    return JSON.parse(jsonString);
  }

  beforeAll(async () => {
    apikey = {
      key: '119155',
      secret: 'Wz1nNW8a51H1KZrDUBK3MOriRfPmswMjkXlO7CbwD6MiULBO8q',
      companies: [{ id: 'i738314881' }],
      id: '118338',
      title: 'user5',
    };
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key = apikey.key;
    secret = apikey.secret;

    const exec = promisify(execCallback);
    const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
    const scriptPath = 'tests/api/corezoid-api/sh/uploadObjectRootFolder.sh';

    const { stdout } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        VALIDATE_SCHEME: 'false',
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        COMPANY_ID: company_id,
        FILE_NAME: 'project_autotestCompareMerge.zip',
        ASYNC: 'true',
      },
    });
    debug(stdout);
    const jsonResponse = parseStdoutToJson(stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponse, null, 2), context: '' });
    expect(jsonResponse.request_proc).toBe(`ok`);

    await new Promise(r => setTimeout(r, 10000));

    const projectsList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECTS,
        obj_id: 0,
        company_id,
        id: company_id,
        order: 'asc',
        sort: 'date',
      }),
    );
    expect(projectsList.status).toBe(200);
    const { list: listSetParam } = projectsList.body.ops[0];
    project_id = (listSetParam as Array<any>).find(item => item.short_name === 'firstproj-1').project_id;

    const stageList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: project_id,
        company_id,
        id: company_id,
        order: 'asc',
        sort: 'date',
      }),
    );
    expect(stageList.status).toBe(200);
    const { list: stagesList } = stageList.body.ops[0];
    prodEmpty_id = (stagesList as Array<any>).find(item => item.short_name === 'prodempty').obj_id;
    prodOldType_id = (stagesList as Array<any>).find(item => item.short_name === 'prodoldtype').obj_id;
    prodNewType_id = (stagesList as Array<any>).find(item => item.short_name === 'prodnewtype').obj_id;

    const scriptPathStage = 'tests/api/corezoid-api/sh/uploadObjectCompareMerge.apply_mode.sh';
    const uploadStage = await exec(scriptPathStage, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        REWRITE_ALIAS: 'true',
        WITH_ALIAS: 'true',
        VALIDATE_SCHEME: 'false',
        FILE_NAME: 'stage_dev_autotestCompareMerge.zip',
        COMPANY_ID: company_id,
        SKIP_ROOT: 'false',
      },
    });
    debug(uploadStage.stdout);
    const jsonResponseStage = parseStdoutToJson(uploadStage.stdout);
    addMsg({ message: 'RES:' + JSON.stringify(jsonResponseStage, null, 2), context: '' });
    expect(jsonResponseStage.ops[0].proc).toBe(PROC_STATUS.OK);
    stage_schema_id = jsonResponseStage.ops[0].scheme_id;
  });

  const prods = [
    {
      description: 'prodNewType',
      stage_prod_id: (): string | number => prodNewType_id,
      copyThisStage: {
        added: 0,
      },
      copyOtherStage: {
        added: 0,
        changed: 7,
      },
      copyOtherProj: {
        added: 0,
      },
    },
    {
      description: 'prodOldType',
      stage_prod_id: (): string | number => prodOldType_id,
      copyThisStage: {
        added: 8,
      },
      copyOtherStage: {
        added: 10,
        changed: 2,
      },
      copyOtherProj: {
        added: 0,
      },
    },
    {
      description: 'prodEmpty',
      stage_prod_id: (): string | number => prodEmpty_id,
      copyThisStage: {
        added: 1,
      },
      copyOtherStage: {
        added: 1,
        changed: 0,
      },
      copyOtherProj: {
        added: 1,
      },
    },
  ];

  describe.each(prods)('file -> $description', ({ stage_prod_id, copyThisStage, copyOtherStage, copyOtherProj }) => {
    test(`compare`, async () => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPARE,
          obj: OBJ_TYPE.OBJ_SCHEME,
          company_id,
          obj_id: stage_prod_id(),
          obj_type: OBJ_TYPE.STAGE,
          obj_to_id: stage_schema_id,
          obj_to_type: OBJ_TYPE.SCHEME,
          project_id,
          num_stat: true,
          diff_status: true,
        }),
      );
      expect(response.status).toBe(RESP_STATUS.OK);

      const { ops: opsCompare } = response.body;
      const { proc: procCompare, list: listCompare } = opsCompare[0];
      expect(procCompare).toEqual(PROC_STATUS.OK);
      changedConvThisStage = (listCompare as Array<any>).find(item => item.title === 'copyThisStage').__num_stat;
      expect(changedConvThisStage.added).toEqual(copyThisStage.added);
      expect(changedConvThisStage.changed).toEqual(0);
      expect(changedConvThisStage.deleted).toEqual(0);
      changedConvOtherStage = (listCompare as Array<any>).find(item => item.title === 'copyOtherStage').__num_stat;
      expect(changedConvOtherStage.added).toEqual(copyOtherStage.added);
      expect(changedConvOtherStage.changed).toEqual(copyOtherStage.changed);
      expect(changedConvOtherStage.deleted).toEqual(0);
      changedConvOtherProj = (listCompare as Array<any>).find(item => item.title === 'copyOtherProj').__num_stat;
      expect(changedConvOtherProj.added).toEqual(copyOtherProj.added);
      expect(changedConvOtherProj.changed).toEqual(0);
      expect(changedConvOtherProj.deleted).toEqual(0);
    });
  });

  describe.each(prods)('file -> $description', ({ stage_prod_id }) => {
    test('merge', async () => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MERGE,
          obj: OBJ_TYPE.OBJ_SCHEME,
          company_id,
          obj_id: stage_prod_id(),
          obj_type: OBJ_TYPE.STAGE,
          obj_to_id: stage_schema_id,
          obj_to_type: OBJ_TYPE.SCHEME,
          project_id,
          apply_mode: true,
          async: false,
        }),
      );
      expect(response.status).toBe(RESP_STATUS.OK);
      const { ops: opsMerge } = response.body as MergeResponse;
      const { proc: prockMerge, diff: diffMerge } = opsMerge[0];
      expect(prockMerge).toEqual(PROC_STATUS.OK);
      expect(diffMerge.obj_id).toEqual(stage_prod_id());

      const responseList = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.STAGE,
          company_id,
          obj_id: stage_prod_id(),
          project_id,
        }),
      );
      expect(responseList.status).toBe(RESP_STATUS.OK);
      const { proc: proc, list: list } = responseList.body.ops[0];
      expect(proc).toEqual(PROC_STATUS.OK);

      convOtherProj = (list as Array<any>).find(item => item.title === 'copyOtherProj').obj_id;
      convOtherStage = (list as Array<any>).find(item => item.title === 'copyOtherStage').obj_id;
      convThisStage = (list as Array<any>).find(item => item.title === 'copyThisStage').obj_id;
      convEmpty = (list as Array<any>).find(item => item.title === 'empty').obj_id;
      convSD = (list as Array<any>).find(item => item.title === 'SD1').obj_id;

      const responseListConvOP = await requestListConv(api, convOtherProj, company_id, project_id, stage_prod_id());
      expect(responseListConvOP.status).toBe(RESP_STATUS.OK);
      const { list: listOP } = responseListConvOP.body.ops[0];
      processCOP1 = (listOP as Array<any>).find(item => item.title === 'allConstr').logics[0];
      expect(processCOP1.project_id).toEqual('{{proj}}');
      expect(processCOP1.stage_id).toEqual('{{stage}}');
      expect(processCOP1.conv_id).toEqual('@{{alias}}');
      processCOP2 = (listOP as Array<any>).find(item => item.title === 'p/s/alias').logics[0];
      expect(processCOP2.project_id).toEqual(projSecond);
      expect(processCOP2.stage_id).toEqual(stageSecond);
      expect(processCOP2.conv_id).toEqual('@emptyalotherproj');
      processCOP3 = (listOP as Array<any>).find(item => item.title === 'p/s/ConstrAl').logics[0];
      expect(processCOP3.project_id).toEqual(projSecond);
      expect(processCOP3.stage_id).toEqual(stageSecond);
      expect(processCOP3.conv_id).toEqual('@{{alias}}');
      processCOP4 = (listOP as Array<any>).find(item => item.title === 'p/ConstrS/ConstrC').logics[0];
      expect(processCOP4.project_id).toEqual(projSecond);
      expect(processCOP4.stage_id).toEqual('{{stage}}');
      expect(processCOP4.conv_id).toEqual('{{conv}}');
      processCOP5 = (listOP as Array<any>).find(item => item.title === 'p/s/c').logics[0];
      expect(processCOP5.project_id).toEqual(projSecond);
      expect(processCOP5.stage_id).toEqual(stageSecond);
      expect(processCOP5.conv_id).toEqual(2312662);

      const responseListConvOS = await requestListConv(api, convOtherStage, company_id, project_id, stage_prod_id());
      expect(responseListConvOS.status).toBe(RESP_STATUS.OK);
      const { list: listOS } = responseListConvOS.body.ops[0];
      processCOP1 = (listOS as Array<any>).find(item => item.title === 'AllConstr').logics[0];
      expect(processCOP1.project_id).toEqual(`{{conv[${convSD}].ref[ref0000].project}}`);
      expect(processCOP1.stage_id).toEqual(`{{conv[${convSD}].ref[ref0000].stageOther}}`);
      expect(processCOP1.conv_id).toEqual(`{{conv[${convSD}].ref[ref0000].convOther}}`);
      processCOP2 = (listOS as Array<any>).find(item => item.title === 'p/s/alias').logics[0];
      expect(processCOP2.project_id).toEqual(projFirst);
      expect(processCOP2.stage_id).toEqual(stageFirst);
      expect(processCOP2.conv_id).toEqual('@aliasotherstage');
      processCOP3 = (listOS as Array<any>).find(item => item.title === 'p/s/c').logics[0];
      expect(processCOP3.project_id).toEqual(projFirst);
      expect(processCOP3.stage_id).toEqual(stageFirst);
      expect(processCOP3.conv_id).toEqual(2312582);
      processCOP4 = (listOS as Array<any>).find(item => item.title === 'p/ConstrS/ConstrC').logics[0];
      expect(processCOP4.project_id).toEqual(project_id);
      expect(processCOP4.stage_id).toEqual(`{{conv[${convSD}].ref[ref0000].stageOther}}`);
      expect(processCOP4.conv_id).toEqual(`{{conv[${convSD}].ref[ref0000].convOther}}`);

      const responseListConvTS = await requestListConv(api, convThisStage, company_id, project_id, stage_prod_id());
      expect(responseListConvTS.status).toBe(RESP_STATUS.OK);
      const { list: listTS } = responseListConvTS.body.ops[0];
      processCOP1 = (listTS as Array<any>).find(item => item.title === 'ConstP/ConstrS/ConstrC').logics[0];
      expect(processCOP1.project_id).toEqual(`{{conv[${convSD}].ref[ref0000].project}}`);
      expect(processCOP1.stage_id).toEqual(`{{conv[${convSD}].ref[ref0000].stage}}`);
      expect(processCOP1.conv_id).toEqual(`{{conv[${convSD}].ref[ref0000].conv}}`);
      processCOP2 = (listTS as Array<any>).find(item => item.title === 'p/s/alias').logics[0];
      expect(processCOP2.conv_id).toEqual('@aliasempty');
      processCOP3 = (listTS as Array<any>).find(item => item.title === 'p/s/conv').logics[0];
      expect(processCOP3.conv_id).toEqual(convEmpty);
      processCOP4 = (listTS as Array<any>).find(item => item.title === 'p/ConstrS/ConstrC').logics[0];
      expect(processCOP4.project_id).toEqual(project_id);
      expect(processCOP4.stage_id).toEqual(`{{conv[${convSD}].ref[ref0000].stage}}`);
      expect(processCOP4.conv_id).toEqual(`{{conv[${convSD}].ref[ref0000].conv}}`);
      processCOP5 = (listTS as Array<any>).find(item => item.title === 'p/s/ConstrC').logics[0];
      expect(processCOP5.conv_id).toEqual(`{{conv[${convSD}].ref[ref0000].conv}}`);
      processCOP6 = (listTS as Array<any>).find(item => item.title === 'AllDinamic').logics[0];
      expect(processCOP6.project_id).toEqual(`{{proj}}`);
      expect(processCOP6.stage_id).toEqual(`{{stage}}`);
      expect(processCOP6.conv_id).toEqual(`{{conv}}`);
      processCOP7 = (listTS as Array<any>).find(item => item.title === 'p/ConstrS/ConstrA').logics[0];
      expect(processCOP7.project_id).toEqual(project_id);
      expect(processCOP7.stage_id).toEqual(`{{conv[${convSD}].ref[ref0000].stage}}`);
      expect(processCOP7.conv_id).toEqual(`@{{conv[${convSD}].ref[ref0000].alias}}`);
      processCOP8 = (listTS as Array<any>).find(item => item.title === 'p/s/ConstrA').logics[0];
      expect(processCOP8.conv_id).toEqual(`@{{conv[${convSD}].ref[ref0000].alias}}`);
      processCOP9 = (listTS as Array<any>).find(item => item.title === 'p/s/DinamicC').logics[0];
      expect(processCOP9.conv_id).toEqual(`{{conv}}`);

      const responseShowConv = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.CONV,
          obj_id: convThisStage,
        }),
      );
      expect(responseShowConv.status).toBe(RESP_STATUS.OK);
      const { proc: procSC, project_id: convProjId, stage_id: convStageId } = responseShowConv.body.ops[0];
      expect(procSC).toEqual(PROC_STATUS.OK);
      expect(convProjId).toEqual(project_id);
      expect(convStageId).toEqual(stage_prod_id());

      const responseListAliasses = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.ALIASES,
          company_id,
          project_id,
          stage_id: stage_prod_id(),
        }),
      );
      expect(responseListAliasses.status).toBe(RESP_STATUS.OK);
      const { proc: procLA, list: listLA } = responseListAliasses.body.ops[0];
      expect(procLA).toEqual(PROC_STATUS.OK);
      expect(listLA[0].short_name).toEqual('aliasempty');
    });
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, project_id, company_id);
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
