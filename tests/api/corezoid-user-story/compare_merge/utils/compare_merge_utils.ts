import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS } from '../../../../../utils/corezoidRequest';
import { requestConfirm, requestListConv } from '../../../../../application/api/ApiObj';
import { NODE_TYPE, NODE_LOGIC_TYPE } from '../../../../../application/api/obj_types/node';
import { debug, warn } from '../../../../../support/utils/logger';

/**
 * Sets a stage's immutable property
 * @param api ApiKeyClient instance
 * @param stageId Stage ID to modify
 * @param immutable Boolean value to set immutable property
 * @param company_id Company ID
 * @param project_id Project ID
 * @returns API response
 */
export async function setStageImmutable(
  api: any,
  stageId: string | number,
  immutable: boolean,
  company_id: any,
  project_id: any,
): Promise<any> {
  const responseStageModify = await api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.STAGE,
      obj_id: stageId,
      company_id,
      project_id,
      immutable,
    }),
  );
  expect(responseStageModify.status).toBe(RESP_STATUS.OK);
  return responseStageModify;
}

/**
 * Parameter configuration for updateProdProcessNode
 */
export interface ProcessNodeConfig {
  // Required parameters
  api: any;
  stageId: string | number;
  prodConvId: number;
  company_id: any;
  project_id: any;

  // Parameter configuration
  convId: string | number; // Can be number, alias string or dynamic construction
  stageId_param?: string | number; // Optional, defaults to stageId
  projectId_param?: string | number; // Optional, defaults to project_id

  // Optional parameters
  convTitle?: string; // Title to search for, defaults to 'process'
}

/**
 * Universal function to update process node in production stage with various parameter types
 * @param config Configuration object
 * @returns void
 */
export async function updateProdProcessNode(config: ProcessNodeConfig): Promise<void> {
  const {
    api,
    stageId,
    prodConvId,
    company_id,
    project_id,
    convId,
    stageId_param = stageId,
    projectId_param = project_id,
    convTitle = 'process',
  } = config;

  // First, make the production stage mutable
  await setStageImmutable(api, stageId, false, company_id, project_id);

  // Validate inputs
  if (!prodConvId) {
    warn('No process ID provided, skipping update');
    return;
  }

  // Get the list of nodes in the process
  const responseListProdProcess = await requestListConv(api, prodConvId, company_id, project_id, stageId);

  // Check for errors in response
  if (responseListProdProcess.body.ops[0].proc === 'error') {
    warn(`Error in process list response: ${responseListProdProcess.body.ops[0].description}`);
    return;
  }

  // Check if list exists
  if (!responseListProdProcess.body.ops[0].list) {
    warn('No list found in response');
    return;
  }

  // Get the list of nodes
  const list = responseListProdProcess.body.ops[0].list as Array<any>;
  debug(
    'Available nodes:',
    list.map(item => item.title),
  );

  // Find the process node by title
  const processCopyNode =
    list.find(item => item.title === 'proccesCopy') ||
    list.find(item => item.title === convTitle || item.title === 'process');

  if (processCopyNode) {
    // Update the process node with the provided parameters
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: processCopyNode.obj_id,
        conv_id: prodConvId,
        title: 'proccesCopy',
        obj_type: NODE_TYPE.Normal,
        logics: [
          {
            type: NODE_LOGIC_TYPE.ApiCopy,
            mode: 'create',
            data: {},
            data_type: {},
            group: 'all',
            ref: '',
            send_parent_data: false,
            conv_id: convId,
            stage_id: stageId_param,
            project_id: projectId_param,
            err_node_id: '',
          },
          { to_node_id: processCopyNode.logics[1].to_node_id, type: NODE_LOGIC_TYPE.Go, node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );

    // Commit the changes
    await requestConfirm(api, prodConvId, company_id);
  }

  // Make production stage immutable again
  await setStageImmutable(api, stageId, true, company_id, project_id);
}

/**
 * Helper function to create a process node configuration for numeric IDs
 */
export function createNumericConfig(
  api: any,
  stageId: string | number,
  prodConvId: number,
  prodSDId: number,
  company_id: any,
  project_id: any,
  convTitle = 'process',
): ProcessNodeConfig {
  return {
    api,
    stageId,
    prodConvId,
    company_id,
    project_id,
    convId: prodSDId,
    convTitle,
  };
}

/**
 * Helper function to create a process node configuration with alias for conv_id
 */
export function createAliasConfig(
  api: any,
  stageId: string | number,
  prodConvId: number,
  aliasShortName: string | number,
  company_id: any,
  project_id: any,
  convTitle = 'process',
): ProcessNodeConfig {
  return {
    api,
    stageId,
    prodConvId,
    company_id,
    project_id,
    convId: `@${aliasShortName}`,
    convTitle,
  };
}

/**
 * Helper function to create a process node configuration with dynamic construction for conv_id
 */
export function createDynamicConvConfig(
  api: any,
  stageId: string | number,
  prodConvId: number,
  convRefId: number,
  referens: string,
  company_id: any,
  project_id: any,
  convTitle = 'process',
): ProcessNodeConfig {
  return {
    api,
    stageId,
    prodConvId,
    company_id,
    project_id,
    convId: `{{conv[${convRefId}].ref[${referens}].conv}}`,
    convTitle,
  };
}

/**
 * Helper function to create a process node configuration with dynamic construction for conv_id and stage_id but numeric project_id
 */
export function createDynamicConvStageConfig(
  api: any,
  stageId: string | number,
  prodConvId: number,
  convRefId: number,
  referens: string,
  company_id: any,
  project_id: any,
  convTitle = 'process',
): ProcessNodeConfig {
  return {
    api,
    stageId,
    prodConvId,
    company_id,
    project_id,
    convId: `{{conv[${convRefId}].ref[${referens}].conv}}`,
    stageId_param: `{{conv[${convRefId}].ref[${referens}].stage}}`,
    projectId_param: project_id,
    convTitle,
  };
}

/**
 * Helper function to create a process node configuration with numeric conv_id and undefined stage_id/project_id
 */
export function createNumericConvConfig(
  api: any,
  stageId: string | number,
  prodConvId: number,
  convRefId: number,
  referens: string,
  company_id: any,
  project_id: any,
  convTitle = 'process',
): ProcessNodeConfig {
  return {
    api,
    stageId,
    prodConvId,
    company_id,
    project_id,
    convId: convRefId,
    stageId_param: undefined,
    projectId_param: undefined,
    convTitle,
  };
}

/**
 * Helper function to create a process node configuration with alias for conv_id and undefined stage_id/project_id
 */
export function createAliasConvConfig(
  api: any,
  stageId: string | number,
  prodConvId: number,
  aliasShortName: string | number,
  referens: string,
  company_id: any,
  project_id: any,
  convTitle = 'process',
): ProcessNodeConfig {
  return {
    api,
    stageId,
    prodConvId,
    company_id,
    project_id,
    convId: `@${aliasShortName}`,
    stageId_param: undefined,
    projectId_param: undefined,
    convTitle,
  };
}

/**
 * Helper function to create a process node configuration with dynamic construction for all parameters
 */
export function createFullyDynamicConfig(
  api: any,
  stageId: string | number,
  prodConvId: number,
  convRefId: number,
  referens: string,
  company_id: any,
  project_id: any,
  convTitle = 'process',
): ProcessNodeConfig {
  return {
    api,
    stageId,
    prodConvId,
    company_id,
    project_id,
    convId: `{{conv[${convRefId}].ref[${referens}].conv}}`,
    stageId_param: `{{conv[${convRefId}].ref[${referens}].stage}}`,
    projectId_param: `{{conv[${convRefId}].ref[${referens}].project}}`,
    convTitle,
  };
}

/**
 * Helper function to verify parameters in API Copy logic
 *
 * @param api ApiKeyClient instance
 * @param apiCopyLogic The api_copy logic object from the process node
 * @param scenario The test scenario with parameter configuration
 * @param stageId The stage ID where the verification is performed
 * @param company_id The company ID
 * @param project_id The project ID
 * @param stageIdParamName The name of the stage ID parameter in the scenario (newStage_id1 or newStage_id2)
 */
export async function verifyApiCopyParameters(
  api: any,
  apiCopyLogic: any,
  scenario: any,
  stageId: string | number,
  company_id: any,
  project_id: any,
  stageIdParamName: 'newStage_id1' | 'newStage_id2' | 'newStage_id',
  convIdParamName: 'newConv_id1' | 'newConv_id2' | 'newConv_id' = 'newConv_id',
  projectIdParamName: 'newProject_id1' | 'newProject_id2' | 'newProject_id' = 'newProject_id',
): Promise<void> {
  // Get expected parameter values from scenario
  const expectedConvId = scenario[convIdParamName]();
  const expectedStageId = scenario[stageIdParamName]();
  const expectedProjectId = scenario[projectIdParamName]();

  // Always check conv_id
  expect(apiCopyLogic.conv_id).toBe(expectedConvId);

  // If stage_id is dynamic, both stage_id and project_id must be present in api_copy
  const isDynamicStageId =
    typeof scenario.stage_id() === 'string' && String(scenario.stage_id()).indexOf('{{conv') >= 0;

  if (isDynamicStageId) {
    expect(apiCopyLogic.stage_id).toBeDefined();
    expect(apiCopyLogic.project_id).toBeDefined();
  }

  // Verify the values if fields are present in api_copy
  if (apiCopyLogic.stage_id !== undefined) {
    expect(apiCopyLogic.stage_id).toBe(expectedStageId);
  }
  if (apiCopyLogic.project_id !== undefined) {
    expect(apiCopyLogic.project_id).toBe(expectedProjectId);
  }

  // For non-dynamic parameters when they're undefined in api_copy,
  // verify them through additional API calls
  if (!isDynamicStageId && (apiCopyLogic.stage_id === undefined || apiCopyLogic.project_id === undefined)) {
    // If conv_id is an alias, check via aliases, then show conv
    if (typeof apiCopyLogic.conv_id === 'string' && apiCopyLogic.conv_id.startsWith('@')) {
      const aliasName = apiCopyLogic.conv_id.substring(1); // Remove '@'

      // Get the alias details
      const responseAliasList = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.ALIASES,
          company_id,
          project_id,
          stage_id: stageId,
        }),
      );
      expect(responseAliasList.status).toBe(RESP_STATUS.OK);

      // Find the alias matching our conv_id
      const targetAlias = responseAliasList.body.ops[0].list.find((a: any) => a.short_name === aliasName);
      expect(targetAlias).toBeDefined();

      // Get the referenced conv details
      if (targetAlias.obj_to_type === 'conv' && targetAlias.obj_to_id) {
        const responseShowConv = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.SHOW,
            obj: OBJ_TYPE.CONV,
            obj_id: targetAlias.obj_to_id,
            company_id,
            project_id,
            stage_id: stageId,
          }),
        );
        expect(responseShowConv.status).toBe(RESP_STATUS.OK);

        // Verify the stage_id and project_id from the conv details
        const convDetails = responseShowConv.body.ops[0];
        if (apiCopyLogic.stage_id === undefined && convDetails.stage_id !== undefined) {
          expect(convDetails.stage_id.toString()).toBe(expectedStageId.toString());
        }
        if (apiCopyLogic.project_id === undefined && convDetails.project_id !== undefined) {
          expect(convDetails.project_id.toString()).toBe(expectedProjectId.toString());
        }
      }
    } else {
      // If conv_id is numeric, check directly via show conv
      const convId = apiCopyLogic.conv_id;
      const responseShowConv = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.CONV,
          obj_id: convId,
          company_id,
          project_id,
          stage_id: stageId,
        }),
      );

      if (responseShowConv.status === RESP_STATUS.OK) {
        const convDetails = responseShowConv.body.ops[0];
        if (apiCopyLogic.stage_id === undefined && convDetails.stage_id !== undefined) {
          expect(convDetails.stage_id.toString()).toBe(expectedStageId.toString());
        }
        if (apiCopyLogic.project_id === undefined && convDetails.project_id !== undefined) {
          expect(convDetails.project_id.toString()).toBe(expectedProjectId.toString());
        }
      }
    }
  }
}
