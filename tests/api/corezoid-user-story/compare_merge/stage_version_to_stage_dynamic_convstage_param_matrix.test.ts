import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
  generateName,
} from '../../../../utils/corezoidRequest';
import {
  requestConfirm,
  requestDeleteObj,
  requestListConv,
  requestCreateObjNew,
} from '../../../../application/api/ApiObj';
import { NODE_TYPE, NODE_LOGIC_TYPE } from '../../../../application/api/obj_types/node';
import { CreateTaskResponse } from '../../../../application/api/obj_types/task';
import { CreateAliasResponse, LinkAliasResponse } from '../../../../application/api/obj_types/alias';
import { CompareResponse, MergeResponse } from '../../../../application/api/obj_types/obj_scheme';
import {
  updateProdProcessNode,
  createDynamicConvStageConfig,
  verifyApiCopyParameters,
} from './utils/compare_merge_utils';

describe('Compare/Merge Stage and Version to Populated Stage with Dynamic Conv ID/Stage ID and Numeric Project ID', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let newConvCopyProd1: number;
  let newConvCopyProd2: number;
  let newSDProd1: number;
  let newSDProd2: number;
  let project_id: string | number;
  let stageDev_id: string | number;
  let stageProd_id1: string | number;
  let stageProd_id2: string | number;
  let project_short_name: string;
  let title: string;
  let conv_type: string;
  let copy_process: number;
  let diagram_process: number;
  let process_node_ID_copy: string | number;
  let final_node_ID_copy: string | number;
  let short_name_al_sd: string | number;
  let alias_sd: string | number;
  let referens: string;
  let version_id: string | number;
  let processCopy: string;
  let stateDiagram: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = generateName(OBJ_TYPE.PROJECT);
    title = generateName(OBJ_TYPE.OBJS);
    short_name_al_sd = generateName(OBJ_TYPE.ALIAS);
    conv_type = 'process';
    referens = 'ref0000';
    processCopy = generateName(OBJ_TYPE.OBJS);
    stateDiagram = generateName(OBJ_TYPE.OBJS);

    // Create project with two production stages and a develop stage
    const responseProject = await requestCreateObjNew(
      api,
      OBJ_TYPE.PROJECT,
      company_id,
      generateName(OBJ_TYPE.PROJECT),
      {
        short_name: project_short_name,
        stages: [
          { title: 'production1', immutable: true },
          { title: 'develop', immutable: false },
          { title: 'production2', immutable: true },
        ],
      },
    );
    const { obj_id: objIdProject, stages: arrStages } = responseProject.body.ops[0];
    expect(responseProject.status).toBe(RESP_STATUS.OK);
    project_id = objIdProject;
    stageProd_id1 = arrStages[0];
    stageDev_id = arrStages[1];
    stageProd_id2 = arrStages[2];

    // Create a process in develop stage
    const responseCopy = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, processCopy, {
      conv_type,
      stage_id: stageDev_id,
      project_id,
    });
    copy_process = responseCopy.obj_id;
    const responseListSetParam = await requestListConv(api, copy_process, company_id);
    const { list: listSetParam } = responseListSetParam.body.ops[0];
    process_node_ID_copy = (listSetParam as Array<any>).find((item): boolean => item.title === conv_type).obj_id;
    final_node_ID_copy = (listSetParam as Array<any>).find((item): boolean => item.title === 'final').obj_id;

    // Create a state diagram in develop stage
    const responseDiagram = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, stateDiagram, {
      conv_type: 'state',
      stage_id: stageDev_id,
      project_id,
    });
    diagram_process = responseDiagram.obj_id;

    // Create alias in develop stage
    const responseAliasSD = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: short_name_al_sd,
        title,
        project_id,
        stage_id: stageDev_id,
      }),
    );
    expect(responseAliasSD.status).toBe(RESP_STATUS.OK);
    const { ops: opsCreateAliasSD } = responseAliasSD.body as CreateAliasResponse;
    alias_sd = opsCreateAliasSD[0].obj_id;

    // Link alias to state diagram
    const responseLinkSD = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: alias_sd,
        company_id,
        link: true,
        obj_to_type: OBJ_TYPE.CONV,
        obj_to_id: diagram_process,
        project_id,
        stage_id: stageDev_id,
      }),
    );
    expect(responseLinkSD.status).toBe(RESP_STATUS.OK);
    const { ops: opsLinkAliasSD } = responseLinkSD.body as LinkAliasResponse;
    expect(opsLinkAliasSD[0].proc).toEqual(PROC_STATUS.OK);

    // Create a task in state diagram
    const responseTaskSD = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: diagram_process,
        ref: referens,
        data: {
          project: project_id,
          stage: stageDev_id,
          conv: diagram_process,
          alias: short_name_al_sd,
        },
      }),
    );
    expect(responseTaskSD.status).toBe(RESP_STATUS.OK);
    const { ops: opsCTaskSD } = responseTaskSD.body as CreateTaskResponse;
    expect(opsCTaskSD[0].proc).toEqual(PROC_STATUS.OK);

    // Configure the API Copy logic for numeric parameters
    const responseModifCopyNumeric = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID_copy,
        conv_id: copy_process,
        title: 'proccesCopy',
        obj_type: NODE_TYPE.Normal,
        logics: [
          {
            type: NODE_LOGIC_TYPE.ApiCopy,
            mode: 'create',
            data: {},
            data_type: {},
            group: 'all',
            ref: '',
            send_parent_data: false,
            conv_id: diagram_process,
            stage_id: stageDev_id,
            project_id,
            err_node_id: '',
          },
          { to_node_id: final_node_ID_copy, type: NODE_LOGIC_TYPE.Go, node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifCopyNumeric.status).toBe(RESP_STATUS.OK);

    const responseCommitCopy = await requestConfirm(api, copy_process, company_id);
    expect(responseCommitCopy.status).toBe(RESP_STATUS.OK);

    // Perform initial merge from develop stage to production 1 stage
    const responseMerge1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        company_id,
        obj_id: stageProd_id1,
        obj_type: OBJ_TYPE.STAGE,
        obj_to_id: stageDev_id,
        obj_to_type: OBJ_TYPE.STAGE,
        project_id,
        apply_mode: true,
        async: false,
      }),
    );
    expect(responseMerge1.status).toBe(RESP_STATUS.OK);

    // Get the IDs of the merged objects in production 1
    const responseList1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        company_id,
        obj_id: stageProd_id1,
        project_id,
      }),
    );
    expect(responseList1.status).toBe(RESP_STATUS.OK);

    const { list: list1 } = responseList1.body.ops[0];
    newConvCopyProd1 = (list1 as Array<any>).find(item => item.title === processCopy)?.obj_id;
    newSDProd1 = (list1 as Array<any>).find(item => item.title === stateDiagram)?.obj_id;

    const responseMerge2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        company_id,
        obj_id: stageProd_id2,
        obj_type: OBJ_TYPE.STAGE,
        obj_to_id: stageDev_id,
        obj_to_type: OBJ_TYPE.STAGE,
        project_id,
        apply_mode: true,
        async: false,
      }),
    );
    expect(responseMerge2.status).toBe(RESP_STATUS.OK);

    // Get the IDs of the merged objects in production 2
    const responseList2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        company_id,
        obj_id: stageProd_id2,
        project_id,
      }),
    );
    expect(responseList2.status).toBe(RESP_STATUS.OK);

    const { list: list2 } = responseList2.body.ops[0];
    newConvCopyProd2 = (list2 as Array<any>).find(item => item.title === processCopy)?.obj_id;
    newSDProd2 = (list2 as Array<any>).find(item => item.title === stateDiagram)?.obj_id;
  });

  // Define test scenarios for different parameter types
  const testScenarios = [
    {
      name: 'All numeric IDs',
      conv_id: (): string | number => diagram_process,
      stage_id: (): string | number => stageDev_id,
      project_id: (): string | number => project_id,
      newConv_id1: (): string | number => newSDProd1,
      newConv_id2: (): string | number => newSDProd2,
      newStage_id1: (): string | number => stageProd_id1,
      newStage_id2: (): string | number => stageProd_id2,
      newProject_id1: (): string | number => project_id,
      newProject_id2: (): string | number => project_id,
      num_statConv: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 1, deleted: 2 }),
      num_statAlias: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 0, deleted: 0 }),
      num_statDiagram: (): { added: number; changed: number; deleted: number } => ({
        added: 0,
        changed: 0,
        deleted: 0,
      }),
      statusConv: (): string => 'changed',
      statusAlias: (): string => '',
      statusDiagram: (): string => 'changed',
    },
    {
      name: 'Alias for conv_id, numeric stage_id/project_id',
      conv_id: (): string => `@${short_name_al_sd}`,
      stage_id: (): string | number => stageDev_id,
      project_id: (): string | number => project_id,
      newConv_id1: (): string => `@${short_name_al_sd}`,
      newConv_id2: (): string => `@${short_name_al_sd}`,
      newStage_id1: (): string | number => stageProd_id1,
      newStage_id2: (): string | number => stageProd_id2,
      newProject_id1: (): string | number => project_id,
      newProject_id2: (): string | number => project_id,
      num_statConv: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 1, deleted: 2 }),
      num_statAlias: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 0, deleted: 0 }),
      num_statDiagram: (): { added: number; changed: number; deleted: number } => ({
        added: 0,
        changed: 0,
        deleted: 0,
      }),
      statusConv: (): string => 'changed',
      statusAlias: (): string => '',
      statusDiagram: (): string => 'changed',
    },
    {
      name: 'Dynamic conv_id, numeric stage_id/project_id',
      conv_id: (): string => `{{conv[${diagram_process}].ref[${referens}].conv}}`,
      stage_id: (): string | number => stageDev_id,
      project_id: (): string | number => project_id,
      newConv_id1: (): string => `{{conv[${newSDProd1}].ref[${referens}].conv}}`,
      newConv_id2: (): string => `{{conv[${newSDProd2}].ref[${referens}].conv}}`,
      newStage_id1: (): string | number => stageProd_id1,
      newStage_id2: (): string | number => stageProd_id2,
      newProject_id1: (): string | number => project_id,
      newProject_id2: (): string | number => project_id,
      num_statConv: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 0, deleted: 2 }),
      num_statAlias: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 0, deleted: 0 }),
      num_statDiagram: (): { added: number; changed: number; deleted: number } => ({
        added: 0,
        changed: 0,
        deleted: 0,
      }),
      statusConv: (): string => 'changed',
      statusAlias: (): string => '',
      statusDiagram: (): string => 'changed',
    },
    {
      name: 'Dynamic conv_id/stage_id, numeric project_id',
      conv_id: (): string => `{{conv[${diagram_process}].ref[${referens}].conv}}`,
      stage_id: (): string => `{{conv[${diagram_process}].ref[${referens}].stage}}`,
      project_id: (): string | number => project_id,
      newConv_id1: (): string => `{{conv[${newSDProd1}].ref[${referens}].conv}}`,
      newConv_id2: (): string => `{{conv[${newSDProd2}].ref[${referens}].conv}}`,
      newStage_id1: (): string => `{{conv[${newSDProd1}].ref[${referens}].stage}}`,
      newStage_id2: (): string => `{{conv[${newSDProd2}].ref[${referens}].stage}}`,
      newProject_id1: (): string | number => project_id,
      newProject_id2: (): string | number => project_id,
      num_statConv: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 0, deleted: 0 }),
      num_statAlias: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 0, deleted: 0 }),
      num_statDiagram: (): { added: number; changed: number; deleted: number } => ({
        added: 0,
        changed: 0,
        deleted: 0,
      }),
      statusConv: (): string => '',
      statusAlias: (): string => '',
      statusDiagram: (): string => 'changed',
    },
    {
      name: 'All dynamic parameters',
      conv_id: (): string => `{{conv[${diagram_process}].ref[${referens}].conv}}`,
      stage_id: (): string => `{{conv[${diagram_process}].ref[${referens}].stage}}`,
      project_id: (): string => `{{conv[${diagram_process}].ref[${referens}].project}}`,
      newConv_id1: (): string => `{{conv[${newSDProd1}].ref[${referens}].conv}}`,
      newConv_id2: (): string => `{{conv[${newSDProd2}].ref[${referens}].conv}}`,
      newStage_id1: (): string => `{{conv[${newSDProd1}].ref[${referens}].stage}}`,
      newStage_id2: (): string => `{{conv[${newSDProd2}].ref[${referens}].stage}}`,
      newProject_id1: (): string => `{{conv[${newSDProd1}].ref[${referens}].project}}`,
      newProject_id2: (): string => `{{conv[${newSDProd2}].ref[${referens}].project}}`,
      num_statConv: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 1, deleted: 0 }),
      num_statAlias: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 0, deleted: 0 }),
      num_statDiagram: (): { added: number; changed: number; deleted: number } => ({
        added: 0,
        changed: 0,
        deleted: 0,
      }),
      statusConv: (): string => 'changed',
      statusAlias: (): string => '',
      statusDiagram: (): string => 'changed',
    },
    {
      name: 'conv_id dynamic parameters, without stage_id/project_id',
      conv_id: (): string => `{{conv[${diagram_process}].ref[${referens}].conv}}`,
      stage_id: (): undefined => undefined,
      project_id: (): undefined => undefined,
      newConv_id1: (): string => `{{conv[${newSDProd1}].ref[${referens}].conv}}`,
      newConv_id2: (): string => `{{conv[${newSDProd2}].ref[${referens}].conv}}`,
      newStage_id1: (): undefined => undefined,
      newStage_id2: (): undefined => undefined,
      newProject_id1: (): undefined => undefined,
      newProject_id2: (): undefined => undefined,
      num_statConv: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 0, deleted: 2 }),
      num_statAlias: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 0, deleted: 0 }),
      num_statDiagram: (): { added: number; changed: number; deleted: number } => ({
        added: 0,
        changed: 0,
        deleted: 0,
      }),
      statusConv: (): string => 'changed',
      statusAlias: (): string => '',
      statusDiagram: (): string => 'changed',
    },
    {
      name: 'conv_id alias parameters, without stage_id/project_id',
      conv_id: (): string => `@${short_name_al_sd}`,
      stage_id: (): undefined => undefined,
      project_id: (): undefined => undefined,
      newConv_id1: (): string => `@${short_name_al_sd}`,
      newConv_id2: (): string => `@${short_name_al_sd}`,
      newStage_id1: (): string | number => stageProd_id1,
      newStage_id2: (): string | number => stageProd_id2,
      newProject_id1: (): string | number => project_id,
      newProject_id2: (): string | number => project_id,
      num_statConv: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 1, deleted: 2 }),
      num_statAlias: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 0, deleted: 0 }),
      num_statDiagram: (): { added: number; changed: number; deleted: number } => ({
        added: 0,
        changed: 0,
        deleted: 0,
      }),
      statusConv: (): string => 'changed',
      statusAlias: (): string => '',
      statusDiagram: (): string => 'changed',
    },
    {
      name: 'conv_id id parameters, without stage_id/project_id',
      conv_id: (): string | number => diagram_process,
      stage_id: (): undefined => undefined,
      project_id: (): undefined => undefined,
      newConv_id1: (): string | number => newSDProd1,
      newConv_id2: (): string | number => newSDProd2,
      newStage_id1: (): string | number => stageProd_id1,
      newStage_id2: (): string | number => stageProd_id2,
      newProject_id1: (): string | number => project_id,
      newProject_id2: (): string | number => project_id,
      num_statConv: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 1, deleted: 2 }),
      num_statAlias: (): { added: number; changed: number; deleted: number } => ({ added: 0, changed: 0, deleted: 0 }),
      num_statDiagram: (): { added: number; changed: number; deleted: number } => ({
        added: 0,
        changed: 0,
        deleted: 0,
      }),
      statusConv: (): string => 'changed',
      statusAlias: (): string => '',
      statusDiagram: (): string => 'changed',
    },
  ];

  describe.each(testScenarios)('Testing with $name', (scenario): void => {
    beforeAll(
      async (): Promise<void> => {
        // Configure the API Copy logic with the test scenario parameters
        const responseModifCopy = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MODIFY,
            obj: OBJ_TYPE.NODE,
            obj_id: process_node_ID_copy,
            conv_id: copy_process,
            title: 'proccesCopy',
            obj_type: NODE_TYPE.Normal,
            logics: [
              {
                type: NODE_LOGIC_TYPE.ApiCopy,
                mode: 'create',
                data: {},
                data_type: {},
                group: 'all',
                ref: '',
                send_parent_data: false,
                conv_id: scenario.conv_id(),
                stage_id: scenario.stage_id(),
                project_id: scenario.project_id(),
                err_node_id: '',
              },
              { to_node_id: final_node_ID_copy, type: NODE_LOGIC_TYPE.Go, node_title: 'final' },
            ],
            semaphors: [],
            version: 22,
          }),
        );
        expect(responseModifCopy.status).toBe(RESP_STATUS.OK);

        const responseCommitCopy = await requestConfirm(api, copy_process, company_id);
        expect(responseCommitCopy.status).toBe(RESP_STATUS.OK);

        // Create a version in the develop stage
        const responseVersion = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.VERSION,
            company_id,
            project_id,
            stage_id: stageDev_id,
            stage_from_id: stageDev_id,
            vsn: '0.0.1',
          }),
        );
        expect(responseVersion.status).toBe(RESP_STATUS.OK);
        version_id = responseVersion.body.ops[0].obj_id;
      },
    );

    // Test cases for Stage to Stage comparison and merge with populated production stage
    const testCasesStageToStage = [
      {
        description: 'dev -> populated prod1',
        obj_to_type: OBJ_TYPE.STAGE,
        obj_to_id: (): string | number => stageDev_id,
        obj_id: (): string | number => stageProd_id1,
        prod_conv_copy: (): number => newConvCopyProd1,
        prod_sd: (): number => newSDProd1,
      },
    ];

    describe.each(testCasesStageToStage)(
      '$description',
      ({ obj_to_type, obj_id, obj_to_id, prod_conv_copy, prod_sd }): void => {
        // Update production stage configuration before each test
        beforeEach(
          async (): Promise<void> => {
            // Update process node in production with dynamic conv_id/stage_id and numeric project_id
            const config = createDynamicConvStageConfig(
              api,
              obj_id(),
              prod_conv_copy(),
              prod_sd(),
              referens,
              company_id,
              project_id,
              conv_type,
            );
            await updateProdProcessNode(config);
          },
        );

        test('should successfully compare stage to populated stage', async (): Promise<void> => {
          const response = await api.request(
            createRequestWithOps({
              type: REQUEST_TYPE.COMPARE,
              obj: OBJ_TYPE.OBJ_SCHEME,
              company_id,
              obj_id: obj_id(),
              obj_type: OBJ_TYPE.STAGE,
              obj_to_id: obj_to_id(),
              obj_to_type,
              project_id,
              num_stat: true,
              diff_status: true,
            }),
          );
          expect(response.status).toBe(RESP_STATUS.OK);

          const { ops: opsCompare } = response.body as CompareResponse;
          const { proc: procCompare, list: listCompare } = opsCompare[0];
          expect(procCompare).toEqual(PROC_STATUS.OK);

          // Verify the process comparison - should show changes because of the different parameters
          const convItem = (listCompare as Array<any>).filter((item): boolean => item.title === processCopy);
          if (convItem.length > 0) {
            expect(convItem[0].__num_stat).toEqual(scenario.num_statConv());
            expect(convItem[0].__status).toEqual(scenario.statusConv());
          }

          // Alias and diagram should be unchanged since they're already in prod
          const aliasItem = (listCompare as Array<any>).filter(
            (item): boolean => item.obj_type === 'alias' && item.short_name === short_name_al_sd,
          );
          if (aliasItem.length > 0) {
            expect(aliasItem[0].__status).toEqual(scenario.statusAlias());
          }

          const diagramItem = (listCompare as Array<any>).filter((item): boolean => item.title === stateDiagram);
          if (diagramItem.length > 0) {
            expect(diagramItem[0].__status).toEqual(scenario.statusDiagram());
          }
        });

        test('should successfully merge stage to populated stage', async (): Promise<void> => {
          const response = await api.request(
            createRequestWithOps({
              type: REQUEST_TYPE.MERGE,
              obj: OBJ_TYPE.OBJ_SCHEME,
              company_id,
              obj_id: obj_id(),
              obj_type: OBJ_TYPE.STAGE,
              obj_to_id: obj_to_id(),
              obj_to_type,
              project_id,
              apply_mode: true,
              async: false,
            }),
          );
          expect(response.status).toBe(RESP_STATUS.OK);

          const { ops: opsMerge } = response.body as MergeResponse;
          const { proc: prockMerge, diff: diffMerge } = opsMerge[0];
          expect(prockMerge).toEqual(PROC_STATUS.OK);
          expect(diffMerge.obj_id).toEqual(obj_id());

          // Verify the process node has been updated with the scenario parameters
          const responseListConv = await requestListConv(api, prod_conv_copy(), company_id, project_id, obj_id());
          expect(responseListConv.status).toBe(RESP_STATUS.OK);

          const processCopyNode = (responseListConv.body.ops[0].list as Array<any>).find(
            (item): boolean => item.title === 'proccesCopy',
          );
          expect(processCopyNode).toBeDefined();

          // Verify that the api_copy logic has updated parameters matching the scenario
          const apiCopyLogic = processCopyNode.logics.find(
            (logic: { type: string }): boolean => logic.type === 'api_copy',
          );
          expect(apiCopyLogic).toBeDefined();

          // Use the utility function to verify all parameters
          await verifyApiCopyParameters(
            api,
            apiCopyLogic,
            scenario,
            obj_id(),
            company_id,
            project_id,
            'newStage_id1',
            'newConv_id1',
            'newProject_id1',
          );
        });
      },
    );

    // Test cases for Version to Stage comparison and merge with populated production stage
    const testCasesVersionToStage = [
      {
        description: 'version -> populated prod2',
        obj_to_type: OBJ_TYPE.VERSION,
        obj_to_id: (): string | number => version_id,
        obj_id: (): string | number => stageProd_id2,
        prod_conv_copy: (): number => newConvCopyProd2,
        prod_sd: (): number => newSDProd2,
      },
    ];

    describe.each(testCasesVersionToStage)(
      '$description',
      ({ obj_to_type, obj_id, obj_to_id, prod_conv_copy, prod_sd }): void => {
        // Update production stage configuration before each test
        beforeEach(
          async (): Promise<void> => {
            // Update process node in production with dynamic conv_id/stage_id and numeric project_id
            const config = createDynamicConvStageConfig(
              api,
              obj_id(),
              prod_conv_copy(),
              prod_sd(),
              referens,
              company_id,
              project_id,
              conv_type,
            );
            await updateProdProcessNode(config);
          },
        );

        test('should successfully compare version to populated stage', async (): Promise<void> => {
          const response = await api.request(
            createRequestWithOps({
              type: REQUEST_TYPE.COMPARE,
              obj: OBJ_TYPE.OBJ_SCHEME,
              company_id,
              obj_id: obj_id(),
              obj_type: OBJ_TYPE.STAGE,
              obj_to_id: obj_to_id(),
              obj_to_type,
              project_id,
              num_stat: true,
              diff_status: true,
            }),
          );
          expect(response.status).toBe(RESP_STATUS.OK);

          const { ops: opsCompare } = response.body as CompareResponse;
          const { proc: procCompare, list: listCompare } = opsCompare[0];
          expect(procCompare).toEqual(PROC_STATUS.OK);

          // Verify the process comparison - should show changes because of the different parameters
          const convItem = (listCompare as Array<any>).filter((item): boolean => item.title === processCopy);
          if (convItem.length > 0) {
            expect(convItem[0].__num_stat).toEqual(scenario.num_statConv());
            expect(convItem[0].__status).toEqual(scenario.statusConv());
          }

          // Alias and diagram should be unchanged since they're already in prod
          const aliasItem = (listCompare as Array<any>).filter(
            (item): boolean => item.obj_type === 'alias' && item.short_name === short_name_al_sd,
          );
          if (aliasItem.length > 0) {
            expect(aliasItem[0].__status).toEqual(scenario.statusAlias());
          }

          const diagramItem = (listCompare as Array<any>).filter((item): boolean => item.title === stateDiagram);
          if (diagramItem.length > 0) {
            expect(diagramItem[0].__status).toEqual(scenario.statusDiagram());
          }
        });

        test('should successfully merge version to populated stage', async (): Promise<void> => {
          const response = await api.request(
            createRequestWithOps({
              type: REQUEST_TYPE.MERGE,
              obj: OBJ_TYPE.OBJ_SCHEME,
              company_id,
              obj_id: obj_id(),
              obj_type: OBJ_TYPE.STAGE,
              obj_to_id: obj_to_id(),
              obj_to_type,
              project_id,
              apply_mode: true,
              async: false,
            }),
          );
          expect(response.status).toBe(RESP_STATUS.OK);

          const { ops: opsMerge } = response.body as MergeResponse;
          const { proc: prockMerge, diff: diffMerge } = opsMerge[0];
          expect(prockMerge).toEqual(PROC_STATUS.OK);
          expect(diffMerge.obj_id).toEqual(obj_id());

          // Verify the process node has been updated with the scenario parameters
          const responseListConv = await requestListConv(api, prod_conv_copy(), company_id, project_id, obj_id());
          expect(responseListConv.status).toBe(RESP_STATUS.OK);

          const processCopyNode = (responseListConv.body.ops[0].list as Array<any>).find(
            (item): boolean => item.title === 'proccesCopy',
          );
          expect(processCopyNode).toBeDefined();

          // Verify that the api_copy logic has updated parameters matching the scenario
          const apiCopyLogic = processCopyNode.logics.find(
            (logic: { type: string }): boolean => logic.type === 'api_copy',
          );
          expect(apiCopyLogic).toBeDefined();

          // Use the utility function to verify all parameters
          await verifyApiCopyParameters(
            api,
            apiCopyLogic,
            scenario,
            obj_id(),
            company_id,
            project_id,
            'newStage_id2',
            'newConv_id2',
            'newProject_id2',
          );
        });
      },
    );

    afterAll(
      async (): Promise<void> => {
        await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.DELETE,
            obj: OBJ_TYPE.VERSION,
            obj_id: version_id,
            project_id,
            company_id,
          }),
        );
        await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.DESTROY,
            obj: OBJ_TYPE.VERSION,
            obj_id: version_id,
            project_id,
            company_id,
          }),
        );
      },
    );
  });

  afterAll(
    async (): Promise<void> => {
      const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, project_id, company_id);
      expect(response.status).toBe(RESP_STATUS.OK);
    },
  );
});
