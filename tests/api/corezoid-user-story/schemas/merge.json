{"type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["obj", "proc", "diff"], "additionalProperties": false, "properties": {"obj": {"type": "string"}, "proc": {"type": "string"}, "diff": {"type": "object", "required": ["obj_id", "obj_type"], "additionalProperties": false, "properties": {"obj_id": {"type": "integer"}, "obj_type": {"type": "string"}}}}}, "uniqueItems": true}}}