{"type": "object", "required": ["result", "request_proc", "ops"], "additionalProperties": false, "properties": {"result": {"type": "string"}, "request_proc": {"type": "string"}, "ops": {"type": "object", "required": ["proc", "obj", "data"], "additionalProperties": false, "properties": {"proc": {"type": "string"}, "obj": {"type": "string"}, "data": {"type": "object", "required": ["user_id", "user_name", "user_login", "id", "name", "attr_type", "default_value", "description", "change_time"], "additionalProperties": false, "properties": {"user_id": {"type": "integer"}, "user_name": {"type": "string"}, "user_login": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "attr_type": {"type": "string"}, "default_value": {"type": "boolean"}, "description": {"type": "string"}, "change_time": {"type": "integer"}}}}}}}