{"type": "object", "required": ["result", "request_proc", "ops"], "additionalProperties": false, "properties": {"result": {"type": "string"}, "request_proc": {"type": "string"}, "ops": {"type": "object", "required": ["proc", "obj", "data"], "additionalProperties": false, "properties": {"proc": {"type": "string"}, "obj": {"type": "string"}, "data": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "name", "type", "description", "default_value", "active", "user_id", "change_time", "user_name", "user_login"], "additionalProperties": false, "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "type": {"type": "string"}, "description": {"type": ["string", "null"]}, "default_value": {"type": ["boolean", "string", "null", "integer"]}, "active": {"type": "boolean"}, "user_id": {"type": "integer"}, "change_time": {"type": "integer"}, "user_name": {"type": "string"}, "user_login": {"type": "string"}}}, "uniqueItems": true}}}}}