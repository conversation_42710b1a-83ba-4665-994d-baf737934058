import { ApiKeyClient } from '../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../infrastructure/model/ApiKey';
import { User } from '../../../../../infrastructure/model/User';
import { ConfigurationManager } from '../../../../../infrastructure/config/ConfigurationManager';
import { createAuthUser, Method } from '../../../../../utils/request';
import faker from 'faker';

describe('Superadmin (negative)', () => {
  let api_s: ApiKeyClient;
  let apikey_s: ApiKey;
  let user: User;
  let host: string;
  let obj_id: number;
  let cookieUser: string;
  let cookieUserAuth: any;
  let name: string;

  beforeAll(async () => {
    const config = ConfigurationManager.getConfiguration();
    host = config.getSuperadminUrl();
    name = `attr_test_${Date.now()}`;

    user = await application.getAuthorizedUser({ company: {} }, 1);
    cookieUser = user.cookieUser;
    cookieUserAuth = createAuthUser(cookieUser, 'cookie');

    apikey_s = await application.getApiKeySuper();
    api_s = application.getApiKeyClient(apikey_s);

    const responseCreate = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}superadmin/api/1/json`,
      data: { type: 'create', obj: 'license_attr', name, description: name, attr_type: 'string', default_value: '123' },
    });
    expect(responseCreate.status).toBe(200);
    expect(responseCreate.data.ops.proc).toBe('ok');
    expect(responseCreate.data.ops.obj).toBe('license_attr');
    obj_id = responseCreate.data.ops.data.id;
    expect(responseCreate.data.ops.data.attr_type).toBe('string');
  });

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"string\\\">>,<<\\\"integer\\\">>,<<\\\"boolean\\\">>]\">>`,
    ],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't create license_attr with invalid attr_type '%s'`, async (attr_type, reason) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'create',
      obj: 'license_attr',
      name: 'attr_test',
      description: 'test',
      attr_type,
      default_value: '1',
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    ['12', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    ['123', `Value '123' is not valid. Validation with regexp '^[a-z][a-z_0-9]+$' failed`],
    ['qweq#$#%$', `Value 'qweq#$#%$' is not valid. Validation with regexp '^[a-z][a-z_0-9]+$' failed`],
    [faker.random.alphaNumeric(256), `Value is not valid. Value's byte_size is more than maximum allowed: 255`],
  ])(`shouldn't create license_attr with invalid name '%s'`, async (name, reason) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'create',
      obj: 'license_attr',
      name,
      description: 'test',
      attr_type: 'string',
      default_value: '1',
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([undefined])(`shouldn't create license_attr with invalid name '%s'`, async name => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'create',
      obj: 'license_attr',
      name,
      description: 'test',
      attr_type: 'string',
      default_value: '1',
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toEqual('error');
    expect(response.body.message).toEqual(`Key 'name' is required`);
  });

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    ['12', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    ,
    [faker.random.alphaNumeric(256), `Value is not valid. Value's byte_size is more than maximum allowed: 255`],
  ])(`shouldn't create license_attr with invalid description '%s'`, async (description, reason) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'create',
      obj: 'license_attr',
      name: 'test',
      description,
      attr_type: 'string',
      default_value: '1',
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([[undefined, `Key 'description' is required`]])(
    `shouldn't create license_attr without description '%s'`,
    async (description, reason) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'create',
        obj: 'license_attr',
        name: 'test',
        description,
        attr_type: 'string',
        default_value: '1',
      });
      expect(response.status).toBe(400);
      expect(response.body.result).toEqual('error');
      expect(response.body.message).toEqual(reason);
    },
  );

  test.each([[undefined, `Key 'default_value' is required`]])(
    `shouldn't create license_attr without default_value (attr_type:string) '%s'`,
    async (default_value, reason) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'create',
        obj: 'license_attr',
        name: 'test1',
        description: 'test',
        attr_type: 'string',
        default_value,
      });
      expect(response.status).toBe(400);
      expect(response.body.result).toEqual('error');
      expect(response.body.message).toContain(reason);
    },
  );

  test.each([
    [null, `Value 'null' is not valid. Type of value is not '[integer,binary,boolean]'`],
    [{}, `Value '{[]}' is not valid. Type of value is not '[integer,binary,boolean]'`],
    [[], `Value '[]' is not valid. Type of value is not '[integer,binary,boolean]'`],
    [faker.random.alphaNumeric(256), `Value is not valid`],
  ])(
    `shouldn't create license_attr with invalid default_value (attr_type:string) '%s'`,
    async (default_value, reason) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'create',
        obj: 'license_attr',
        name: 'test1',
        description: 'test',
        attr_type: 'string',
        default_value,
      });
      expect(response.status).toBe(400);
      expect(response.body.result).toEqual('error');
      expect(response.body.description).toContain(reason);
    },
  );

  test.each([
    [null, `Value 'null' is not valid. Type of value is not '[integer,binary,boolean]'`],
    [true, `Value 'true' is not valid. Type of value is not 'integer'`],
    [{}, `Value '{[]}' is not valid. Type of value is not '[integer,binary,boolean]'`],
    [[], `Value '[]' is not valid. Type of value is not '[integer,binary,boolean]'`],
    ['test', `Value 'test' is not valid. Type of value is not 'integer'`],
    [faker.random.alphaNumeric(256), `is not valid. Type of value is not 'integer'`],
    [
      2700600500000000000000,
      `Value '2700600500000000049152.0000' is not valid. Type of value is not '[integer,binary,boolean]'`,
    ],
  ])(
    `shouldn't create license_attr with invalid default_value (attr_type:integer)'%s'`,
    async (default_value, reason) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'create',
        obj: 'license_attr',
        name: 'test',
        description: 'test',
        attr_type: 'integer',
        default_value,
      });
      expect(response.status).toBe(400);
      expect(response.body.result).toEqual('error');
      expect(response.body.description).toContain(reason);
    },
  );

  test.each([
    [null, `Value 'null' is not valid. Type of value is not '[integer,binary,boolean]'`],
    [{}, `Value '{[]}' is not valid. Type of value is not '[integer,binary,boolean]'`],
    [[], `Value '[]' is not valid. Type of value is not '[integer,binary,boolean]'`],
    [-1, `Value '-1' is not valid. Type of value is not 'boolean'`],
    [1, `Value '1' is not valid. Type of value is not 'boolean'`],
    [0, `Value '0' is not valid. Type of value is not 'boolean'`],
    ['te', `Value 'te' is not valid. Type of value is not 'boolean'`],
  ])(
    `shouldn't create license_attr with invalid default_value (attr_type:bool) '%s'`,
    async (default_value, reason) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'create',
        obj: 'license_attr',
        name: 'test',
        description: 'test',
        attr_type: 'boolean',
        default_value,
      });
      expect(response.status).toBe(400);
      expect(response.body.result).toEqual('error');
      expect(response.body.description).toEqual(reason);
    },
  );

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    ['12', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    ['123', `Value '123' is not valid. Validation with regexp '^[a-z][a-z_0-9]+$' failed`],
    ['qweq#$#%$', `Value 'qweq#$#%$' is not valid. Validation with regexp '^[a-z][a-z_0-9]+$' failed`],
    [faker.random.alphaNumeric(256), `Value is not valid. Value's byte_size is more than maximum allowed: 255`],
  ])(`shouldn't update license_attr with invalid name '%s'`, async (name, reason) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'update',
      obj: 'license_attr',
      name,
      description: 'test',
      attr_type: 'string',
      default_value: '1',
      id: obj_id,
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
    ['12', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    ['', `Value is not valid. Value's byte_size is less than minimum allowed: 3`],
    [faker.random.alphaNumeric(256), `Value is not valid. Value's byte_size is more than maximum allowed: 255`],
  ])(`shouldn't update license_attr with invalid description '%s'`, async (description, reason) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'update',
      obj: 'license_attr',
      name: 'test',
      description,
      attr_type: 'string',
      default_value: '1',
      id: obj_id,
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [
      'test',
      `Value '<<\"test\">>' is not valid. Value is not in allowed list <<\"[<<\\\"string\\\">>,<<\\\"integer\\\">>,<<\\\"boolean\\\">>]\">>`,
    ],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't update license_attr with invalid attr_type '%s'`, async (attr_type, reason) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'update',
      obj: 'license_attr',
      name: 'attr_test',
      description: 'test',
      attr_type,
      default_value: '1',
      id: obj_id,
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'integer'`],
    [true, `Value 'true' is not valid. Type of value is not 'integer'`],
    ['test', `Value 'test' is not valid. Type of value is not 'integer'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'integer'`],
    [[], `Value '[]' is not valid. Type of value is not 'integer'`],
  ])(`shouldn't update license_attr with invalid id '%s'`, async (id, reason) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'update',
      obj: 'license_attr',
      name: 'attr_test',
      description: 'test',
      attr_type: 'string',
      default_value: '1',
      id,
    });
    expect(response.body.result).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [-1, `not_found`],
    [0, `not_found`],
  ])(`shouldn't update license_attr with invalid id '%s'`, async (id, reason) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'update',
      obj: 'license_attr',
      name: 'attr_test',
      description: 'test',
      attr_type: 'string',
      default_value: '1',
      id,
    });
    expect(response.body.result).toEqual('ok');
    expect(response.body.description).toEqual(reason);
  });

  test.each([[undefined, `Key 'id' is required`]])(
    `shouldn't update license_attr without id '%s'`,
    async (id, reason) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'update',
        obj: 'license_attr',
        name: 'attr_test',
        description: 'test',
        attr_type: 'string',
        default_value: '1',
        id,
      });
      expect(response.body.result).toEqual('error');
      expect(response.body.message).toEqual(reason);
    },
  );

  test.each([
    [null, `Value 'null' is not valid. Type of value is not '[integer,binary,boolean]'`],
    [{}, `Value '{[]}' is not valid. Type of value is not '[integer,binary,boolean]'`],
    [[], `Value '[]' is not valid. Type of value is not '[integer,binary,boolean]'`],
    [faker.random.alphaNumeric(256), `Value is not valid`],
  ])(
    `shouldn't update license_attr with invalid default_value (attr_type:string) '%s'`,
    async (default_value, reason) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'update',
        obj: 'license_attr',
        name: 'test',
        description: 'test',
        attr_type: 'string',
        default_value,
        id: obj_id,
      });
      expect(response.status).toBe(400);
      expect(response.body.result).toEqual('error');
      expect(response.body.description).toContain(reason);
    },
  );

  test.each([
    [null, `Value 'null' is not valid. Type of value is not '[integer,binary,boolean]'`],
    [true, `Value 'true' is not valid. Type of value is not 'integer'`],
    [{}, `Value '{[]}' is not valid. Type of value is not '[integer,binary,boolean]'`],
    [[], `Value '[]' is not valid. Type of value is not '[integer,binary,boolean]'`],
    ['test', `Value 'test' is not valid. Type of value is not 'integer'`],
    [faker.random.alphaNumeric(256), `is not valid. Type of value is not 'integer'`],
    [
      2700600500000000000000,
      `Value '2700600500000000049152.0000' is not valid. Type of value is not '[integer,binary,boolean]'`,
    ],
  ])(
    `shouldn't update license_attr with invalid default_value (attr_type:integer)'%s'`,
    async (default_value, reason) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'update',
        obj: 'license_attr',
        name: 'test',
        description: 'test',
        attr_type: 'integer',
        default_value,
        id: obj_id,
      });
      expect(response.status).toBe(400);
      expect(response.body.result).toEqual('error');
      expect(response.body.description).toContain(reason);
    },
  );

  test.each([
    [null, `Value 'null' is not valid. Type of value is not '[integer,binary,boolean]'`],
    [{}, `Value '{[]}' is not valid. Type of value is not '[integer,binary,boolean]'`],
    [[], `Value '[]' is not valid. Type of value is not '[integer,binary,boolean]'`],
    [-1, `Value '-1' is not valid. Type of value is not 'boolean'`],
    [1, `Value '1' is not valid. Type of value is not 'boolean'`],
    [0, `Value '0' is not valid. Type of value is not 'boolean'`],
    ['te', `Value 'te' is not valid. Type of value is not 'boolean'`],
  ])(
    `shouldn't update license_attr with invalid default_value (attr_type:bool) '%s'`,
    async (default_value, reason) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'update',
        obj: 'license_attr',
        name: 'test',
        description: 'test',
        attr_type: 'boolean',
        default_value,
        id: obj_id,
      });
      expect(response.status).toBe(400);
      expect(response.body.result).toEqual('error');
      expect(response.body.description).toEqual(reason);
    },
  );

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'integer'`],
    [true, `Value 'true' is not valid. Type of value is not 'integer'`],
    ['test', `Value 'test' is not valid. Type of value is not 'integer'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'integer'`],
    [[], `Value '[]' is not valid. Type of value is not 'integer'`],
  ])(`shouldn't view license_attr with invalid id '%s'`, async (id, reason) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'view',
      obj: 'license_attr',
      id,
    });
    expect(response.body.result).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [-1, `not_found`],
    [0, `not_found`],
  ])(`shouldn't view license_attr with invalid id '%s'`, async (id, reason) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'view',
      obj: 'license_attr',
      id,
    });
    expect(response.body.result).toEqual('ok');
    expect(response.body.description).toEqual(reason);
  });

  test.each([[undefined, `Key 'id' is required`]])(
    `shouldn't view license_attr in conv without id '%s'`,
    async (id, reason) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'view',
        obj: 'license_attr',
        id,
      });
      expect(response.body.result).toEqual('error');
      expect(response.body.message).toEqual(reason);
    },
  );

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'integer'`],
    [true, `Value 'true' is not valid. Type of value is not 'integer'`],
    ['test', `Value 'test' is not valid. Type of value is not 'integer'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'integer'`],
    [[], `Value '[]' is not valid. Type of value is not 'integer'`],
  ])(`shouldn't delete license_attr with invalid id '%s'`, async (id, reason) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'delete',
      obj: 'license_attr',
      id,
    });
    expect(response.body.result).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [-1, `License attr already deleted or not found`],
    [0, `License attr already deleted or not found`],
  ])(`shouldn't delete license_attr with invalid id '%s'`, async (id, reason) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'delete',
      obj: 'license_attr',
      id,
    });
    expect(response.body.result).toEqual('ok');
    expect(response.body.description).toEqual(reason);
  });

  test.each([[undefined, `Key 'id' is required`]])(
    `shouldn't delete license_attr in conv without id '%s'`,
    async (id, reason) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'delete',
        obj: 'license_attr',
        id,
      });
      expect(response.body.result).toEqual('error');
      expect(response.body.message).toEqual(reason);
    },
  );

  test.each([
    [null, `Value 'null' is not valid. Type of value is not 'integer'`],
    [true, `Value 'true' is not valid. Type of value is not 'integer'`],
    ['test', `Value 'test' is not valid. Type of value is not 'integer'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'integer'`],
    [[], `Value '[]' is not valid. Type of value is not 'integer'`],
  ])(`shouldn't restore license_attr with invalid id '%s'`, async (id, reason) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'restore',
      obj: 'license_attr',
      id,
    });
    expect(response.body.result).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  test.each([
    [-1, `License attr already active or not found`],
    [0, `License attr already active or not found`],
  ])(`shouldn't restore license_attr with invalid id '%s'`, async (id, reason) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'restore',
      obj: 'license_attr',
      id,
    });
    expect(response.body.result).toEqual('ok');
    expect(response.body.description).toEqual(reason);
  });

  test.each([[undefined, `Key 'id' is required`]])(
    `shouldn't restore license_attr in conv without id '%s'`,
    async (id, reason) => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'restore',
        obj: 'license_attr',
        id,
      });
      expect(response.body.result).toEqual('error');
      expect(response.body.message).toEqual(reason);
    },
  );

  test.each([
    [true, `Value 'true' is not valid. Type of value is not 'binary'`],
    [null, `Value 'null' is not valid. Type of value is not 'binary'`],
    [{}, `Value '{[]}' is not valid. Type of value is not 'binary'`],
    [[], `Value '[]' is not valid. Type of value is not 'binary'`],
    [-1, `Value '-1' is not valid. Type of value is not 'binary'`],
    [1, `Value '1' is not valid. Type of value is not 'binary'`],
    [0, `Value '0' is not valid. Type of value is not 'binary'`],
  ])(`shouldn't list license_attr with invalid filter '%s'`, async (filter, reason) => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'list',
      obj: 'license_attrs',
      filter,
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toEqual('error');
    expect(response.body.description).toEqual(reason);
  });

  afterAll(async () => {
    const responseDelete = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}superadmin/api/1/json`,
      data: { type: 'delete', obj: 'license_attr', id: obj_id },
    });
    expect(responseDelete.status).toBe(200);

    const responseDestroy = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}license/api/2`,
      data: { type: 'destroy', obj: 'license_attr', id: obj_id },
    });
    expect(responseDestroy.status).toBe(200);
  });
});
