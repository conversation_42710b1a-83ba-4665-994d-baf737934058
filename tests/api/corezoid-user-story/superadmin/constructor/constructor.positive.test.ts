import { ApiKeyClient } from '../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../infrastructure/model/ApiKey';
import { User } from '../../../../../infrastructure/model/User';
import { ConfigurationManager } from '../../../../../infrastructure/config/ConfigurationManager';
import { SchemaValidator } from '../../../../../application/api/SchemaValidator';
import { createAuthUser, Method } from '../../../../../utils/request';
import createSchema from '../../schemas/constructorS/createAttr.json';
import updateSchema from '../../schemas/constructorS/updateAttr.json';
import viewSchema from '../../schemas/constructorS/viewAttr.json';
import deleterestoreSchema from '../../schemas/constructorS/deleteAttr.json';
import listSchema from '../../schemas/constructorS/listAttr.json';

describe('Superadmin (positive)', () => {
  let apiK: ApiKeyClient;
  let apikey: ApiKey;
  let api_s: ApiKeyClient;
  let apikey_s: ApiKey;
  let user: User;
  let host: string;
  let obj_id: number;
  let obj_id1: number;
  let cookieUser: string;
  let cookieUserAuth: any;
  let name: string;
  let nameModify: string;

  beforeAll(async () => {
    apikey_s = await application.getApiKeySuper();
    api_s = application.getApiKeyClient(apikey_s);

    apikey = await application.getApiKey();
    apiK = application.getApiKeyClient(apikey);

    name = `attr_test_${Date.now()}`;
    nameModify = `modify_attr_test_${Date.now()}`;

    const config = ConfigurationManager.getConfiguration();
    host = config.getSuperadminUrl();

    user = await application.getAuthorizedUser({ company: {} }, 1);
    cookieUser = user.cookieUser;
    cookieUserAuth = createAuthUser(cookieUser, 'cookie');
  });

  test(`Create license_attr`, async () => {
    const responseCreate = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}superadmin/api/1/json`,
      data: { type: 'create', obj: 'license_attr', name, description: name, attr_type: 'string', default_value: '1' },
    });
    expect(responseCreate.status).toBe(200);
    expect(responseCreate.data.ops.proc).toBe('ok');
    expect(responseCreate.data.ops.obj).toBe('license_attr');
    obj_id = responseCreate.data.ops.data.id;
    expect(responseCreate.data.ops.data.attr_type).toBe('string');
    SchemaValidator.validate(createSchema, responseCreate.data);
  });

  test(`Update license_attr`, async () => {
    const responseUpdate = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}superadmin/api/1/json`,
      data: {
        type: 'update',
        obj: 'license_attr',
        id: obj_id,
        name: nameModify,
        description: nameModify,
        attr_type: 'boolean',
        default_value: 'true',
      },
    });
    expect(responseUpdate.status).toBe(200);
    expect(responseUpdate.data.ops.proc).toBe('ok');
    expect(responseUpdate.data.ops.obj).toBe('license_attr');
    expect(responseUpdate.data.ops.data.attr_type).toBe('boolean');
    SchemaValidator.validate(updateSchema, responseUpdate.data);
  });

  test(`View license_attr`, async () => {
    const responseView = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}superadmin/api/1/json`,
      data: { type: 'view', obj: 'license_attr', id: obj_id },
    });
    expect(responseView.status).toBe(200);
    expect(responseView.data.ops.proc).toBe('ok');
    expect(responseView.data.ops.obj).toBe('license_attr');
    expect(responseView.data.ops.data.type).toBe('boolean');
    expect(responseView.data.ops.data.active).toBe(true);
    SchemaValidator.validate(viewSchema, responseView.data);
  });

  test(`Delete license_attr`, async () => {
    const responseDelete = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}superadmin/api/1/json`,
      data: { type: 'delete', obj: 'license_attr', id: obj_id },
    });
    expect(responseDelete.status).toBe(200);
    expect(responseDelete.data.ops.proc).toBe('ok');
    expect(responseDelete.data.ops.obj).toBe('license_attr');
    SchemaValidator.validate(deleterestoreSchema, responseDelete.data);
  });

  test(`View license_attr after delete`, async () => {
    const responseView = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}superadmin/api/1/json`,
      data: { type: 'view', obj: 'license_attr', id: obj_id },
    });
    expect(responseView.status).toBe(200);
    expect(responseView.data.ops.proc).toBe('ok');
    expect(responseView.data.ops.obj).toBe('license_attr');
    expect(responseView.data.ops.data.type).toBe('boolean');
    expect(responseView.data.ops.data.active).toBe(false);
  });

  test(`Restore license_attr`, async () => {
    const responseRestore = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}superadmin/api/1/json`,
      data: { type: 'restore', obj: 'license_attr', id: obj_id },
    });
    expect(responseRestore.status).toBe(200);
    expect(responseRestore.data.ops.proc).toBe('ok');
    expect(responseRestore.data.ops.obj).toBe('license_attr');
    SchemaValidator.validate(deleterestoreSchema, responseRestore.data);
  });

  test(`View license_attr after restore`, async () => {
    const responseView = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}superadmin/api/1/json`,
      data: { type: 'view', obj: 'license_attr', id: obj_id },
    });
    expect(responseView.status).toBe(200);
    expect(responseView.data.ops.proc).toBe('ok');
    expect(responseView.data.ops.obj).toBe('license_attr');
    expect(responseView.data.ops.data.type).toBe('boolean');
    expect(responseView.data.ops.data.active).toBe(true);
  });

  test(`List license_attr after restore`, async () => {
    const responseList = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}superadmin/api/1/json`,
      data: { type: 'list', obj: 'license_attrs' },
    });
    expect(responseList.status).toBe(200);
    expect(responseList.data.ops.proc).toBe('ok');
    expect(responseList.data.ops.obj).toBe('license_attrs');
    expect((responseList.data.ops.data as Array<any>).find(item => item.id === obj_id).active).toEqual(true);
    SchemaValidator.validate(listSchema, responseList.data);
  });

  test(`Create/Delete attr api_keyS`, async () => {
    const responseCreate = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'create',
      obj: 'license_attr',
      name,
      description: name,
      attr_type: 'string',
      default_value: '1',
    });
    expect(responseCreate.status).toBe(200);
    expect(responseCreate.body.ops.proc).toBe('ok');
    expect(responseCreate.body.ops.obj).toBe('license_attr');
    obj_id1 = responseCreate.body.ops.data.id;
    expect(responseCreate.body.ops.data.attr_type).toBe('string');
    SchemaValidator.validate(createSchema, responseCreate.body);

    const responseDelete = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'delete',
      obj: 'license_attr',
      id: obj_id1,
    });
    expect(responseDelete.status).toBe(200);
    expect(responseDelete.body.ops.proc).toBe('ok');
    expect(responseDelete.body.ops.obj).toBe('license_attr');
    SchemaValidator.validate(deleterestoreSchema, responseDelete.body);

    const responseDestroy = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}license/api/2`,
      data: { type: 'destroy', obj: 'license_attr', id: obj_id1 },
    });
    expect(responseDestroy.status).toBe(200);
  });

  test(`Create attr api_key_user (access denied)`, async () => {
    const responseCreate = await apiK.requestSuperadmin('superadmin/api/1/json', {
      type: 'create',
      obj: 'license_attr',
      name,
      description: name,
      attr_type: 'string',
      default_value: '1',
    });
    expect(responseCreate.status).toBe(400);
    expect(responseCreate.body.message).toBe('access denied');

    const responseDelete = await apiK.requestSuperadmin('superadmin/api/1/json', {
      type: 'delete',
      obj: 'license_attr',
      id: obj_id,
    });
    expect(responseDelete.status).toBe(400);
    expect(responseDelete.body.message).toBe('access denied');
  });

  afterAll(async () => {
    const responseDelete = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}superadmin/api/1/json`,
      data: { type: 'delete', obj: 'license_attr', id: obj_id },
    });
    expect(responseDelete.status).toBe(200);

    const responseDestroy = await cookieUserAuth.request({
      method: Method.POST,
      url: `${host}license/api/2`,
      data: { type: 'destroy', obj: 'license_attr', id: obj_id },
    });
    expect(responseDestroy.status).toBe(200);
  });
});
