import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { User } from '../../../../infrastructure/model/User';

describe('Superadmin (positive)', () => {
  let api_s: ApiKeyClient;
  let apikey_s: ApiKey;
  let vsn: string;
  let user: User;
  let obj_id: number;

  beforeAll(async () => {
    user = await application.getAuthorizedUser({ company: {} }, 0);
    apikey_s = await application.getApiKeySuper();
    api_s = application.getApiKeyClient(apikey_s);
    vsn = `${Date.now()}`;
  });

  test(`Set version`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'set',
      obj: 'versions',
      component: 'Autotest',
      ip: '***********',
      vsn,
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
  });

  test(`Set version2`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'set',
      obj: 'versions',
      component: 'Autotest2',
      ip: '***********',
      vsn,
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
  });

  test(`Get version after set`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'get',
      obj: 'versions',
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    expect((response.body.versions as Array<any>).find(item => item.vsn === vsn).ip).toEqual('***********');
  });

  test(`Delete all version`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'delete',
      obj: 'versions',
      data: [
        {
          component: 'Autotest',
          ip: '***********',
          vsn,
        },
        {
          component: 'Autotest2',
          ip: '***********',
          vsn,
        },
      ],
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
  });

  test(`Get version after delete`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'get',
      obj: 'versions',
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    expect(response.body.versions).not.toEqual(expect.arrayContaining([expect.objectContaining({ vsn: vsn })]));
  });

  test(`List values`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'list',
      obj: 'values',
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    expect(response.body.list).toEqual(
      expect.arrayContaining([expect.objectContaining({ key: `capi_register_event_conv` })]),
    );
  });

  test.skip(`List user limit=1`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'list',
      obj: 'user',
      order: 'asc',
      order_by: 'id',
      obj_type: 'users',
      name: '',
      limit: 1,
      offset: 0,
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    expect(response.body.data[0].obj_id).toBeNumber();
  });

  test.skip(`List user name=`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'list',
      obj: 'user',
      order: 'asc',
      order_by: 'id',
      obj_type: 'users',
      name: `${user.email}`,
      limit: 1,
      offset: 0,
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    expect(response.body.data[0].obj_id).toBeNumber();
  });

  test.skip(`List user obj_type=supers`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'list',
      obj: 'user',
      order: 'asc',
      order_by: 'id',
      obj_type: 'users',
      name: `${user.email}`,
      limit: 1,
      offset: 0,
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    expect(response.body.data[0].obj_id).toBeNumber();
    obj_id = response.body.data[0].obj_id;
  });

  test.skip(`Set shared session`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'set',
      obj: 'shared_session',
      obj_id,
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    expect(response.body.data.pub_key).toBeString();
  });

  test.skip(`Delete shared session`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'delete',
      obj: 'shared_session',
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
  });

  test.skip(`List providers`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'list',
      obj: 'auth_providers',
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ type: `corezoid_auth` })]),
    );
  });

  test.skip(`Show providers`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'show',
      obj: 'auth_providers',
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
  });

  test(`List limits`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'list',
      obj: 'limits',
      with_breadcrumb: true,
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    expect(response.body.list).toBeArray();
  });

  test.skip(`List limits with pattern`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'list',
      obj: 'limits',
      limit: 1,
      user_id: null,
      company_id: null,
      filter: 'account',
      pattern: `${user.email}`,
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    expect(response.body.list[0].titles).toEqual(expect.arrayContaining([expect.objectContaining(`${user.email}`)]));
  });

  test.skip(`List limits with filter=api_key`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'list',
      obj: 'limits',
      limit: 1,
      user_id: null,
      company_id: null,
      filter: 'api_key',
      pattern: `key`,
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    expect(response.body.list[0].titles).toEqual(expect.arrayContaining([expect.objectContaining(`key`)]));
  });

  test.skip(`Set limits`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'set',
      obj: 'limits',
      obj_id,
      obj_type: 'account',
      company_id: null,
      user_id: obj_id,
      force: `false`,
      limits: [
        {
          type: 'rps',
          value: 1000,
          edit: true,
        },
      ],
    });
    expect(response.status).toBe(200);
    expect(response.body.result).toBe('ok');
    expect(response.body.list[0].titles).toEqual(expect.arrayContaining([expect.objectContaining(`key`)]));
  });
});
