import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';

describe('Superadmin (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let api_s: ApiKeyClient;
  let apikey_s: ApiKey;
  let vsn: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);

    apikey_s = await application.getApiKeySuper();
    api_s = application.getApiKeyClient(apikey_s);

    vsn = `${Date.now()}`;

    const response = await api_s.requestSuperadmin('license/api/2', {
      type: 'list',
      obj: 'licenses',
      limit: 1,
      offset: 0,
    });
    expect(response.status).toBe(200);
    expect(response.body[0].status).toBe('active');
  });

  test(`Set the version as a standard user - access denied`, async () => {
    const response = await api.requestSuperadmin('superadmin/api/1/json', {
      type: 'set',
      obj: 'versions',
      component: 'Autotest',
      ip: '***********',
      vsn,
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toBe('error');
    expect(response.body.message).toBe('access denied');
  });

  test.each(['te', 0, true, 1234, [], {}, null, -1, undefined, 1])(
    `shouldn't set version with invalid component '%s'`,
    async component => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'set',
        obj: 'versions',
        component,
        ip: '***********',
        vsn,
      });
      expect(response.status).toBe(400);
      expect(response.body.result).toEqual('error');
      expect(response.body.message).toEqual('invalid_version_object');
    },
  );

  test.each(['te', 0, true, 1234, [], {}, null, -1, undefined, 1])(
    `shouldn't set version with invalid ip '%s'`,
    async ip => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'set',
        obj: 'versions',
        component: 'Autotest',
        ip,
        vsn,
      });
      expect(response.status).toBe(400);
      expect(response.body.result).toEqual('error');
      expect(response.body.message).toEqual('invalid_version_object');
    },
  );

  test.each(['te', 0, true, 1234, [], {}, null, -1, undefined, 1])(
    `shouldn't set version with invalid vsn '%s'`,
    async vsn => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'set',
        obj: 'versions',
        component: 'Autotest',
        ip: '***********',
        vsn,
      });
      expect(response.status).toBe(400);
      expect(response.body.result).toEqual('error');
      expect(response.body.message).toEqual('invalid_version_object');
    },
  );

  test(`Delete the version as a standard user - access denied`, async () => {
    const response = await api.requestSuperadmin('superadmin/api/1/json', {
      type: 'delete',
      obj: 'versions',
      data: [
        {
          component: 'Autotest',
          ip: '***********',
          vsn,
        },
      ],
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toBe('error');
    expect(response.body.message).toBe('access denied');
  });

  test.each(['te', 0, true, 1234, [], {}, null, -1, undefined, 1])(
    `shouldn't delete version with invalid component '%s'`,
    async component => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'delete',
        obj: 'versions',
        data: [
          {
            component,
            ip: '***********',
            vsn,
          },
        ],
      });
      expect(response.status).toBe(400);
      expect(response.body.result).toEqual('error');
      expect(response.body.message).toEqual('invalid_version_object');
    },
  );

  test.each(['te', 0, true, 1234, [], {}, null, -1, undefined, 1])(
    `shouldn't delete version with invalid ip '%s'`,
    async ip => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'delete',
        obj: 'versions',
        data: [
          {
            component: 'Autotest',
            ip,
            vsn,
          },
        ],
      });
      expect(response.status).toBe(400);
      expect(response.body.result).toEqual('error');
      expect(response.body.message).toEqual('invalid_version_object');
    },
  );

  test.each(['te', 0, true, 1234, [], {}, null, -1, undefined, 1])(
    `shouldn't delete version with invalid vsn '%s'`,
    async vsn => {
      const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
        type: 'delete',
        obj: 'versions',
        data: [
          {
            component: 'Autotest',
            ip: '***********',
            vsn,
          },
        ],
      });
      expect(response.status).toBe(400);
      expect(response.body.result).toEqual('error');
      expect(response.body.message).toEqual('invalid_version_object');
    },
  );

  test.each(['te', 0, true, 1234, null, -1, 1])(`shouldn't delete version with invalid data '%s'`, async data => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'delete',
      obj: 'versions',
      data,
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toEqual('error');
    expect(response.body.description).toEqual(`Value '${data}' is not valid. Type of value is not 'list'`);
  });

  test(`shouldn't delete version with invalid data 'undefined'`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'delete',
      obj: 'versions',
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toEqual('error');
    expect(response.body.message).toEqual(`Key 'data' is required`);
  });

  test(`shouldn't delete version with invalid data '{}'`, async () => {
    const response = await api_s.requestSuperadmin('superadmin/api/1/json', {
      type: 'delete',
      obj: 'versions',
      data: {},
    });
    expect(response.status).toBe(400);
    expect(response.body.result).toEqual('error');
    expect(response.body.description).toEqual(`Value '{[]}' is not valid. Type of value is not 'list'`);
  });
});
