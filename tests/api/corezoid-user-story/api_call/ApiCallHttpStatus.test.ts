import { requestConfirm, requestShow } from '../../../../application/api/ApiObj';
import {
  createRequestWithOps,
  REQUEST_TYPE,
  OBJ_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import { application } from '../../../../application/Application';
import { ApiCallTestSetup, setupApiCallTest, cleanupApiCallTest } from './utils/apiCallTestUtils';

describe('API Call HTTP Status Tests (Echo Service)', (): void => {
  let testSetup: ApiCallTestSetup;
  let task_id: string | number;
  let cookieSAUser: string;
  const echoUrl = `${ConfigurationManager.getConfiguration().getUrl()}system/tests/echo`;

  beforeAll(
    async (): Promise<void> => {
      const user = await application.getAuthorizedUser({ company: {} }, 1);
      cookieSAUser = user.cookieUser;

      testSetup = await setupApiCallTest('ApiCall_HTTP_Status_Echo_Tests');
    },
  );

  const statusCodes = [
    { code: 200, description: 'OK' },
    { code: 201, description: 'Created' },
    { code: 400, description: 'Bad Request' },
    { code: 401, description: 'Unauthorized' },
    { code: 403, description: 'Forbidden' },
    { code: 404, description: 'Not Found' },
    { code: 500, description: 'Server Error' },
  ];

  describe.each(statusCodes)('HTTP Status: $code $description', ({ code, description }): void => {
    test(`should correctly handle ${code} ${description} response using echo service`, async (): Promise<void> => {
      const { api, conv_id, process_node_ID, final_node_ID, company_id } = testSetup;

      const apiLogic = {
        type: 'api',
        format: 'raw',
        method: 'POST',
        url: echoUrl,
        extra: {},
        extra_type: {},
        max_threads: 5,
        err_node_id: '',
        extra_headers: {
          'Content-Type': 'application/json',
          Cookie: cookieSAUser,
        },
        send_sys: false,
        content_type: 'application/json',
        raw_body: JSON.stringify({
          code: code,
          body: '',
          headers: {
            'Content-Type': 'application/json',
          },
        }),
      };

      const logics = [apiLogic, { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' }];

      const responseCreateNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id,
          title: `ApiCall_Status_Echo_${code}`,
          description: `Test API call with ${code} ${description} response using echo service`,
          obj_type: 0,
          logics,
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseCreateNode.status).toBe(RESP_STATUS.OK);
      expect(responseCreateNode.body.ops[0].proc).toEqual(PROC_STATUS.OK);

      const responseCommit = await requestConfirm(api, conv_id, company_id);
      expect(responseCommit.status).toBe(RESP_STATUS.OK);
      expect(responseCommit.body.ops[0].proc).toEqual(PROC_STATUS.OK);

      const responseTask = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          conv_id,
          data: { test_data: `Test ${code} ${description} response` },
          ref: `ref_${Date.now()}`,
        }),
      );
      expect(responseTask.status).toBe(RESP_STATUS.OK);
      expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
      expect(responseTask.body.ops[0].obj).toEqual('task');
      task_id = responseTask.body.ops[0].obj_id;

      await new Promise(r => setTimeout(r, 3500));

      const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
      expect(responseShowTask.status).toBe(RESP_STATUS.OK);
      expect(responseShowTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);

      const taskData = responseShowTask.body.ops[0].data;
      expect(taskData.__conveyor_api_return_http_code__).toBe(code);
    });
  });

  afterAll(
    async (): Promise<void> => {
      await cleanupApiCallTest(testSetup);
    },
  );
});
