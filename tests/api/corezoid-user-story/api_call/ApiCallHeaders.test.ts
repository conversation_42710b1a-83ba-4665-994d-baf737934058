import { requestConfirm, requestShow } from '../../../../application/api/ApiObj';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { ApiCallTestSetup, setupApiCallTest, cleanupApiCallTest } from './utils/apiCallTestUtils';

describe('API Call Headers Tests', (): void => {
  let testSetup: ApiCallTestSetup;
  const url = 'https://postman-echo.com/headers';

  beforeAll(
    async (): Promise<void> => {
      testSetup = await setupApiCallTest('ApiCall_Headers_Tests');
    },
  );

  const authCases = [
    {
      authType: 'Basic',
      value: 'Basic dXNlcm5hbWU6cGFzc3dvcmQ=',
      description: 'Basic Authentication',
    },
    {
      authType: 'Bearer',
      value: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9',
      description: 'Bearer Token Authentication',
    },
  ];

  describe.each(authCases)('Authorization Header Tests - $description', ({ authType, value, description }): void => {
    test('should correctly handle authorization header', async (): Promise<void> => {
      const { api, conv_id, process_node_ID, final_node_ID, company_id } = testSetup;

      const headerType = 'Authorization';
      const headerValue = value;

      const apiLogic = {
        type: 'api',
        format: '',
        method: 'GET',
        url,
        extra: {},
        extra_type: {},
        max_threads: 5,
        err_node_id: '',
        extra_headers: { [headerType]: headerValue },
        send_sys: false,
        content_type: 'application/json',
      };

      const logics = [apiLogic, { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' }];

      const responseCreateNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id,
          title: `LogicCall_Header_${authType}_Auth`,
          description: `Test API call with ${description} header`,
          obj_type: 0,
          logics,
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseCreateNode.status).toBe(RESP_STATUS.OK);
      expect(responseCreateNode.body.ops[0].proc).toEqual(PROC_STATUS.OK);

      const responseCommit = await requestConfirm(api, conv_id, company_id);
      expect(responseCommit.status).toBe(RESP_STATUS.OK);
      expect(responseCommit.body.ops[0].proc).toEqual(PROC_STATUS.OK);

      const responseTask = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          conv_id,
          data: { test_data: `Test ${authType} Auth` },
          ref: `ref_${Date.now()}`,
        }),
      );
      expect(responseTask.status).toBe(RESP_STATUS.OK);
      expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
      expect(responseTask.body.ops[0].obj).toEqual('task');
      const task_id = responseTask.body.ops[0].obj_id;

      await new Promise(r => setTimeout(r, 5000));

      const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
      expect(responseShowTask.status).toBe(RESP_STATUS.OK);
      expect(responseShowTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
      const taskData = responseShowTask.body.ops[0].data;

      expect(taskData.headers.authorization).toContain(headerValue);
      expect(taskData.test_data).toBe(`Test ${authType} Auth`);
    });
  });

  test('should correctly handle multiple custom headers', async (): Promise<void> => {
    const { api, conv_id, process_node_ID, final_node_ID, company_id } = testSetup;

    const primaryHeader = 'X-Primary-Header';
    const primaryValue = 'primary-value';

    const apiLogic = {
      type: 'api',
      format: '',
      method: 'GET',
      url,
      extra: {},
      extra_type: {},
      max_threads: 5,
      err_node_id: '',
      extra_headers: {
        [primaryHeader]: primaryValue,
      },
      send_sys: false,
      content_type: 'application/json',
    };

    const logics = [apiLogic, { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' }];

    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'LogicCall_Multiple_Headers',
        description: 'Test API call with multiple headers',
        obj_type: 0,
        logics,
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(RESP_STATUS.OK);
    expect(responseCreateNode.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);
    expect(responseCommit.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: { test_data: 'Test Multiple Headers' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');

    const task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 5000));

    const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask.status).toBe(RESP_STATUS.OK);
    expect(responseShowTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    const taskData = responseShowTask.body.ops[0].data;

    expect(taskData.headers[primaryHeader.toLowerCase()]).toContain(primaryValue);
    expect(taskData.test_data).toBe('Test Multiple Headers');
  });

  afterAll(
    async (): Promise<void> => {
      await cleanupApiCallTest(testSetup);
    },
  );
});
