import { requestConfirm, requestShow } from '../../../../application/api/ApiObj';
import {
  createRequestWithOps,
  REQUEST_TYPE,
  OBJ_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { ApiCallTestSetup, setupApiCallTest, cleanupApiCallTest } from './utils/apiCallTestUtils';

describe('API Call Debug Info Tests', (): void => {
  let testSetup: ApiCallTestSetup;
  let task_id: string | number;
  const url = 'https://postman-echo.com/get';

  beforeAll(
    async (): Promise<void> => {
      testSetup = await setupApiCallTest('ApiCall_DebugInfo_Tests');
    },
  );

  const testCases = [
    {
      debugInfo: true,
      description: 'API Call с debug_info=true',
    },
    {
      debugInfo: false,
      description: 'API Call с debug_info=false',
    },
  ];

  describe.each(testCases)('debug_info: $debugInfo', ({ debugInfo, description }): void => {
    test(`${description}`, async (): Promise<void> => {
      const { api, conv_id, process_node_ID, final_node_ID, company_id } = testSetup;

      const apiLogic: any = {
        type: 'api',
        format: '',
        method: 'GET',
        url,
        extra: {},
        extra_type: {},
        max_threads: 5,
        extra_headers: {},
        send_sys: false,
        cert_pem: '',
        content_type: 'application/json',
        debug_info: debugInfo,
      };

      const logics = [apiLogic, { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' }];

      const responseCreateNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id,
          title: `LogicCall_DebugInfo_${debugInfo}`,
          description: `Test API call with debug_info=${debugInfo}`,
          obj_type: 0,
          logics,
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseCreateNode.status).toBe(RESP_STATUS.OK);

      const responseCommit = await requestConfirm(api, conv_id, company_id);
      expect(responseCommit.status).toBe(RESP_STATUS.OK);

      const responseTask = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          conv_id,
          data: {},
          ref: `ref_${Date.now()}`,
        }),
      );
      expect(responseTask.status).toBe(RESP_STATUS.OK);
      expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
      task_id = responseTask.body.ops[0].obj_id;

      await new Promise(r => setTimeout(r, 4000));

      const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
      expect(responseShowTask.status).toBe(RESP_STATUS.OK);

      const taskData = responseShowTask.body.ops[0].data;

      if (debugInfo) {
        expect(taskData.__conveyor_api_debug__).toBeDefined();
        expect(taskData.__conveyor_api_debug__.http_exec_time).toBeDefined();
        expect(taskData.__conveyor_api_debug__.http_res_code).toBeDefined();
      } else {
        expect(taskData.__conveyor_api_debug__).toBeUndefined();
      }
    });
  });

  afterAll(
    async (): Promise<void> => {
      await cleanupApiCallTest(testSetup);
    },
  );
});
