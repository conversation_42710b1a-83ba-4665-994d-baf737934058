import { requestConfirm, requestList } from '../../../../application/api/ApiObj';
import {
  createRequestWithOps,
  REQUEST_TYPE,
  OBJ_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { ApiCallTestSetup, setupApiCallTest, cleanupApiCallTest } from './utils/apiCallTestUtils';

describe('API Call Response Parameters Tests', (): void => {
  let testSetup: ApiCallTestSetup;
  const url = 'https://postman-echo.com/post';

  beforeAll(
    async (): Promise<void> => {
      testSetup = await setupApiCallTest('ApiCall_ResponseParams_Tests');
    },
  );

  test('should correctly handle and convert different parameter types in one request', async (): Promise<void> => {
    const { api, conv_id, process_node_ID, final_node_ID, company_id } = testSetup;

    const response = {
      header: '{{header}}',
      body: '{{body}}',
      string_param: 'test string value',
      number_param: '12345',
      boolean_param: 'true',
      array_param: '["123", 123]',
    };

    const response_type = {
      header: 'object',
      body: 'object',
      string_param: 'string',
      number_param: 'number',
      boolean_param: 'boolean',
      array_param: 'array',
    };

    const expectedValues = {
      string_param: 'test string value',
      number_param: 12345,
      boolean_param: true,
      array_param: ['123', 123],
    };

    const apiLogic = {
      type: 'api',
      format: '',
      method: 'POST',
      url,
      extra: {},
      extra_type: {},
      response,
      response_type,
      max_threads: 5,
      err_node_id: '',
      customize_response: true,
      extra_headers: { 'Content-Type': 'application/json' },
      send_sys: false,
      content_type: 'application/json',
    };

    const logics = [apiLogic, { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' }];

    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'ApiCall_Response_Param_Types',
        description: 'Test API call with all response parameter types',
        obj_type: 0,
        logics,
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(RESP_STATUS.OK);
    expect(responseCreateNode.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);
    expect(responseCommit.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: { test_data: 'Test all parameter types' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 5000));

    const responseFinalNodeList = await requestList(api, final_node_ID, conv_id, company_id, 1);
    expect(responseFinalNodeList.status).toBe(RESP_STATUS.OK);
    expect(responseFinalNodeList.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    const taskData = responseFinalNodeList.body.ops[0].list[0].data;

    expect(taskData.test_data).toBe('Test all parameter types');

    expect(taskData.string_param).toBe(expectedValues.string_param);
    expect(typeof taskData.string_param).toBe('string');

    expect(taskData.number_param).toBe(expectedValues.number_param);
    expect(typeof taskData.number_param).toBe('number');

    expect(taskData.boolean_param).toBe(expectedValues.boolean_param);
    expect(typeof taskData.boolean_param).toBe('boolean');

    expect(taskData.array_param).toEqual(expectedValues.array_param);
  });

  afterAll(
    async (): Promise<void> => {
      await cleanupApiCallTest(testSetup);
    },
  );
});
