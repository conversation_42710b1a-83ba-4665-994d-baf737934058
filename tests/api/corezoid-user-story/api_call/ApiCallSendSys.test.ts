import { requestConfirm, requestShow } from '../../../../application/api/ApiObj';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS } from '../../../../utils/corezoidRequest';
import { ApiCallTestSetup, setupApiCallTest, cleanupApiCallTest } from './utils/apiCallTestUtils';

describe('API Call Send System Parameters Tests', () => {
  let testSetup: ApiCallTestSetup;
  let task_id: string | number;
  const url = 'https://postman-echo.com/post';

  beforeAll(async () => {
    testSetup = await setupApiCallTest('ApiCall_SendSys_Tests');
  });

  test('should send request with send_sys=true', async () => {
    const { api, conv_id, process_node_ID, final_node_ID, company_id } = testSetup;

    const apiLogic = {
      type: 'api',
      format: '',
      method: 'POST',
      url,
      extra: {},
      extra_type: {},
      max_threads: 5,
      err_node_id: '',
      extra_headers: { 'Content-Type': 'application/json' },
      send_sys: true,
      content_type: 'application/json',
    };

    const logics = [apiLogic, { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' }];

    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: `ApiCall_SendSys_True`,
        description: `Test API call with send_sys=true`,
        obj_type: 0,
        logics,
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(RESP_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: {},
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 6000));

    const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask.status).toBe(RESP_STATUS.OK);

    const taskData = responseShowTask.body.ops[0].data;

    expect(taskData.json.sys).toBeDefined();
    expect(taskData.json.sys.conv_id).toBe(conv_id);
    expect(taskData.json.sys.node_id).toBe(process_node_ID);

    expect(taskData.url).toContain(`conv_id=${conv_id}`);
    expect(taskData.url).toContain('conv_signature=');
    expect(taskData.url).toContain('conv_time=');
  });

  test('should send request with send_sys=false', async () => {
    const { api, conv_id, process_node_ID, final_node_ID, company_id } = testSetup;

    const apiLogic = {
      type: 'api',
      format: '',
      method: 'POST',
      url,
      extra: {},
      extra_type: {},
      max_threads: 5,
      err_node_id: '',
      extra_headers: { 'Content-Type': 'application/json' },
      send_sys: false,
      content_type: 'application/json',
    };

    const logics = [apiLogic, { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' }];

    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: `ApiCall_SendSys_False`,
        description: `Test API call with send_sys=false`,
        obj_type: 0,
        logics,
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(RESP_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: {},
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 4000));

    const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask.status).toBe(RESP_STATUS.OK);
    const taskData = responseShowTask.body.ops[0].data;
    expect(taskData.json).toBeNull();
  });

  afterAll(async () => {
    await cleanupApiCallTest(testSetup);
  });
});
