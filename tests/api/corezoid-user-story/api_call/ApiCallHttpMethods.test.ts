import { requestConfirm, requestShow } from '../../../../application/api/ApiObj';
import {
  createRequestWithOps,
  REQUEST_TYPE,
  OBJ_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { ApiCallTestSetup, setupApiCallTest, cleanupApiCallTest } from './utils/apiCallTestUtils';

describe('API Call HTTP Methods Tests', (): void => {
  let testSetup: ApiCallTestSetup;
  let task_id: string | number;
  const url = 'https://postman-echo.com';

  beforeAll(
    async (): Promise<void> => {
      testSetup = await setupApiCallTest('ApiCall_HTTP_Methods_Tests');
    },
  );

  const testCases = [
    {
      method: 'GET',
      endpoint: 'get',
      requestFormat: 'Default',
    },
    {
      method: 'POST',
      endpoint: 'post',
      requestFormat: 'Default',
    },
    {
      method: 'POST',
      endpoint: 'post',
      requestFormat: 'Raw',
      requestBody: { field1: 'value1', field2: 123 },
    },
    {
      method: 'PUT',
      endpoint: 'put',
      requestFormat: 'Default',
    },
    {
      method: 'PUT',
      endpoint: 'put',
      requestFormat: 'Raw',
      requestBody: { updated_field: 'updated_value' },
    },
    {
      method: 'DELETE',
      endpoint: 'delete',
      requestFormat: 'Default',
    },
    {
      method: 'PATCH',
      endpoint: 'patch',
      requestFormat: 'Default',
    },
    {
      method: 'PATCH',
      endpoint: 'patch',
      requestFormat: 'Raw',
      requestBody: { patched_field: 'patched_value' },
    },
    {
      method: 'OPTIONS',
      endpoint: 'get',
      requestFormat: 'Default',
    },
    {
      method: 'HEAD',
      endpoint: 'get',
      requestFormat: 'Default',
    },
  ];

  describe.each(testCases)(
    'Method: $method, Endpoint: $endpoint, Format: $requestFormat',
    ({ method, endpoint, requestFormat, requestBody }): void => {
      test(`should correctly handle ${method} request with ${requestFormat} format`, async (): Promise<void> => {
        const { api, conv_id, process_node_ID, final_node_ID, company_id } = testSetup;

        const apiLogic: any = {
          type: 'api',
          format: '',
          method,
          url: `${url}/${endpoint}`,
          extra: {},
          extra_type: {},
          max_threads: 5,
          err_node_id: '',
          extra_headers: { 'Content-Type': 'application/json' },
          send_sys: false,
          cert_pem: '',
          content_type: 'application/json',
        };

        if (requestFormat === 'Raw' && requestBody && ['PUT', 'POST', 'PATCH', 'DELETE'].includes(method)) {
          apiLogic.raw_body = JSON.stringify(requestBody);
        }

        const logics = [apiLogic, { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' }];

        const responseCreateNode = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MODIFY,
            obj: OBJ_TYPE.NODE,
            obj_id: process_node_ID,
            conv_id,
            title: `LogicCall_${method}_${requestFormat}`,
            description: `Test ${method} method with ${requestFormat} format`,
            obj_type: 0,
            logics,
            semaphors: [],
            version: 22,
          }),
        );
        expect(responseCreateNode.status).toBe(RESP_STATUS.OK);

        const responseCommit = await requestConfirm(api, conv_id, company_id);
        expect(responseCommit.status).toBe(RESP_STATUS.OK);

        const responseTask = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.TASK,
            conv_id,
            data: { test_data: `Test ${method} method` },
            ref: `ref_${Date.now()}`,
          }),
        );
        expect(responseTask.status).toBe(RESP_STATUS.OK);
        expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
        expect(responseTask.body.ops[0].obj).toEqual('task');
        task_id = responseTask.body.ops[0].obj_id;

        await new Promise(r => setTimeout(r, 5000));

        const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
        expect(responseShowTask.status).toBe(RESP_STATUS.OK);

        const taskData = responseShowTask.body.ops[0].data;
        expect(taskData).toBeTruthy();
        expect(taskData.test_data).toBe(`Test ${method} method`);
      });
    },
  );

  afterAll(
    async (): Promise<void> => {
      await cleanupApiCallTest(testSetup);
    },
  );
});
