import { requestConfirm, requestShow } from '../../../../application/api/ApiObj';
import { application } from '../../../../application/Application';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import {
  createRequestWithOps,
  REQUEST_TYPE,
  OBJ_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { ApiCallTestSetup, setupApiCallTest, cleanupApiCallTest } from './utils/apiCallTestUtils';

describe('API Call RFC Format Tests', (): void => {
  let testSetup: ApiCallTestSetup;
  let err_node_ID: string | number;
  let task_id: string | number;
  let cookieSAUser: string;
  const url = `${ConfigurationManager.getConfiguration().getUrl()}system/tests/echo`;

  beforeAll(
    async (): Promise<void> => {
      const user = await application.getAuthorizedUser({ company: {} }, 1);
      cookieSAUser = user.cookieUser;

      testSetup = await setupApiCallTest('ApiCall_RFC_Format_Tests');

      const { api, company_id, conv_id } = testSetup;
      const responseCreateNodeError = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.NODE,
          company_id,
          title: 'Errorfinal',
          conv_id,
          obj_type: 2,
          version: 22,
        }),
      );
      err_node_ID = responseCreateNodeError.body.ops[0].obj_id;
    },
  );

  const testCases = [
    {
      rfcFormat: true,
      description: 'API Call с rfc_format=true',
      getExpectedNodeId: (): string | number => testSetup.final_node_ID,
    },
    {
      rfcFormat: false,
      description: 'API Call с rfc_format=false',
      getExpectedNodeId: (): string | number => err_node_ID,
    },
  ];

  describe.each(testCases)('rfc_format: $rfcFormat', ({ rfcFormat, getExpectedNodeId, description }): void => {
    test(`${description}`, async (): Promise<void> => {
      const { api, conv_id, process_node_ID, final_node_ID, company_id } = testSetup;

      const apiLogic: any = {
        type: 'api',
        format: 'raw',
        method: 'POST',
        url,
        extra: {},
        extra_type: {},
        max_threads: 5,
        err_node_id: err_node_ID,
        extra_headers: {
          'Content-Type': 'application/json',
          Cookie: cookieSAUser,
        },
        send_sys: false,
        cert_pem: '',
        content_type: 'application/json',
        debug_info: true,
        rfc_format: rfcFormat,
        raw_body: JSON.stringify({
          code: 202,
          body: '',
          headers: {
            _X_Custome_Header: 'Test',
            'Content-Type': 'application/json',
          },
        }),
      };

      const logics = [apiLogic, { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' }];

      const responseCreateNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id,
          title: `LogicCall_RfcFormat_${rfcFormat}`,
          description: `Test API call with rfc_format=${rfcFormat}`,
          obj_type: 0,
          logics,
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseCreateNode.status).toBe(RESP_STATUS.OK);

      const responseCommit = await requestConfirm(api, conv_id, company_id);
      expect(responseCommit.status).toBe(RESP_STATUS.OK);

      const responseTask = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          conv_id,
          data: {},
          ref: `ref_${Date.now()}`,
        }),
      );
      expect(responseTask.status).toBe(RESP_STATUS.OK);
      expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
      task_id = responseTask.body.ops[0].obj_id;

      await new Promise(r => setTimeout(r, 2000));

      const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
      expect(responseShowTask.status).toBe(RESP_STATUS.OK);
      expect(responseShowTask.body.ops[0].node_id).toBe(getExpectedNodeId());
    });
  });

  afterAll(
    async (): Promise<void> => {
      await cleanupApiCallTest(testSetup);
    },
  );
});
