import { request } from '../../../../application/api/ApiGWUserClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
} from '../../../../application/api/ApiObj';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';

describe('ApiCallHttpMethodsUseApiGW (positive)', (): void => {
  let api_cor: ApiKeyClient;
  let apikey: Api<PERSON>ey;
  let endpoint_id: number;
  let company_id: any;
  let host: string;
  let process_api_node_ID: string | number;
  let final_api_node_ID: string | number;
  let process_gw_node_ID: string | number;
  let final_gw_node_ID: string | number;
  let api_call_conv_id: number;
  let call_gw_conv_id: number;
  let newApiKeyId: string | number;
  let newApiKeyLogin: number;
  let protocol: string;
  let project_short_name: string;
  let project_id: number;
  let stage_id: number;
  let currentPath: string;
  let err_node_ID: string | number;

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api_cor = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;
      const parsedUrl = new URL(`${ConfigurationManager.getConfiguration().getApiGWUrl()}`);
      protocol = parsedUrl.protocol;
      project_short_name = `project-${Date.now()}`;

      const CreateKeyResponse = await api_cor.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.USER,
          company_id,
          title: 'API',
          logins: [{ type: 'api' }],
        }),
      );
      expect(CreateKeyResponse.status).toBe(200);
      const user_api = CreateKeyResponse.body;
      const newApiKey = {
        key: `${user_api.ops[0].users[0].logins[0].obj_id}`,
        secret: user_api.ops[0].users[0].logins[0].key,
        companies: [],
        title: '',
        id: `${user_api.ops[0].users[0].obj_id}`,
      } as ApiKey;
      newApiKeyId = +newApiKey.id;
      newApiKeyLogin = +newApiKey.key;

      const responseProject = await api_cor.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.PROJECT,
          company_id,
          title: project_short_name,
          short_name: project_short_name,
          description: 'test',
          stages: [
            {
              title: `develop`,
              immutable: false,
            },
          ],
          status: 'active',
        }),
      );
      project_id = responseProject.body.ops[0].obj_id;
      stage_id = responseProject.body.ops[0].stages[0];

      const CreateApiCall = await requestCreateObj(
        api_cor,
        OBJ_TYPE.CONV,
        company_id,
        `Api_Call`,
        stage_id,
        'process',
        project_id,
        stage_id,
      );
      api_call_conv_id = CreateApiCall.body.ops[0].obj_id;

      const responseListApi = await requestListConv(api_cor, api_call_conv_id, company_id);
      expect(responseListApi.status).toBe(200);
      process_api_node_ID = (responseListApi.body.ops[0].list as Array<any>).find(item => item.title === 'process')
        .obj_id;
      final_api_node_ID = (responseListApi.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

      const responseCallGWConv = await requestCreateObj(
        api_cor,
        OBJ_TYPE.CONV,
        company_id,
        `ApiCallGW`,
        stage_id,
        'process',
        project_id,
        stage_id,
      );
      call_gw_conv_id = responseCallGWConv.body.ops[0].obj_id;

      const responseListCallGW = await requestListConv(api_cor, call_gw_conv_id, company_id);
      expect(responseListCallGW.status).toBe(200);
      process_gw_node_ID = (responseListCallGW.body.ops[0].list as Array<any>).find(item => item.title === 'process')
        .obj_id;
      final_gw_node_ID = (responseListCallGW.body.ops[0].list as Array<any>).find(item => item.title === 'final')
        .obj_id;

      const responseCreateNodeError = await api_cor.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.NODE,
          company_id,
          title: 'Errorfinal',
          conv_id: call_gw_conv_id,
          obj_type: 2,
          version: 22,
        }),
      );
      err_node_ID = responseCreateNodeError.body.ops[0].obj_id;

      const responseCreateNode = await api_cor.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_api_node_ID,
          conv_id: api_call_conv_id,
          title: 'ApiGW',
          description: '',
          obj_type: 0,
          logics: [
            {
              type: 'api',
              format: '',
              method: 'POST',
              url: '{{__callback_url}}',
              extra: { proxy: '{{code}}' },
              extra_type: { proxy: 'string' },
              max_threads: 5,
              err_node_id: '',
              extra_headers: { ['content-type']: 'application/json; charset=utf-8' },
              debug_info: false,
              send_sys: true,
              rfc_format: true,
              cert_pem: '',
              content_type: 'application/json',
              version: 2,
              response: {
                header: '{{header}}',
                body: '{{body}}',
              },
              response_type: {
                header: 'object',
                body: 'object',
              },
            },
            { node_title: 'final', to_node_id: final_api_node_ID, format: 'json', type: 'go' },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseCreateNode.status).toBe(200);

      const responseCommit = await requestConfirm(api_cor, api_call_conv_id, company_id);
      expect(responseCommit.status).toBe(200);

      const responseLink = await api_cor.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.PROJECT,
          obj_id: project_id,
          company_id,
          obj_to: 'user',
          obj_to_id: newApiKeyId,
          is_need_to_notify: false,
          privs: [
            { type: 'create', list_obj: ['all'] },
            { type: 'modify', list_obj: ['all'] },
            { type: 'delete', list_obj: ['all'] },
            { type: 'view', list_obj: ['all'] },
          ],
        }),
      );
      expect(responseLink.status).toBe(200);

      const response = await request('post', `apis`, { name: 'auto_test', description: '' });
      expect(response.status).toBe(200);
      endpoint_id = response.body.api.id;
      host = response.body.api.host;
    },
  );

  const httpMethods = [
    {
      method: 'GET',
      path: '/get/path/',
      format: '',
    },
    {
      method: 'POST',
      path: '/post/path',
      format: '',
    },
    {
      method: 'POST',
      path: '/post/path',
      format: 'raw',
    },
    {
      method: 'PUT',
      path: '/put/path/',
      format: '',
    },
    {
      method: 'PUT',
      path: '/put/path/',
      format: 'raw',
    },
    {
      method: 'PATCH',
      path: '/patch/path',
      format: '',
    },
    {
      method: 'PATCH',
      path: '/patch/path',
      format: 'raw',
    },
    {
      method: 'DELETE',
      path: '/delete/path/',
      format: '',
    },
    {
      method: 'DELETE',
      path: '/delete/path/',
      format: 'raw',
    },
  ];

  describe.each(httpMethods)('Testing API GW $method method', (methodInfo): void => {
    test(`with format=${methodInfo.format}`, async (): Promise<void> => {
      if (methodInfo.format != 'raw') {
        const responsePath = await request('post', `apis/${endpoint_id}/paths`, {
          path: methodInfo.path,
          method: methodInfo.method,
          timeout: 29,
          async: false,
          proxy_headers: false,
          proxy_raw_body: false,
          public: { api_login: newApiKeyLogin },
          process_id: api_call_conv_id,
        });
        expect(responsePath.status).toBe(200);
        expect(responsePath.body.path.api_id).toBe(endpoint_id);
        currentPath = responsePath.body.path.path;
      }

      await new Promise(r => setTimeout(r, 1800));

      const responseModNodeGW = await api_cor.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_gw_node_ID,
          conv_id: call_gw_conv_id,
          title: 'ApiallGW',
          description: '',
          obj_type: 0,
          logics: [
            {
              type: 'api',
              format: methodInfo.format,
              method: methodInfo.method,
              url: `${protocol}//${host}${currentPath}`,
              extra: {},
              extra_type: {},
              max_threads: 5,
              err_node_id: err_node_ID,
              extra_headers: {},
              debug_info: false,
              send_sys: true,
              cert_pem: '',
              content_type: 'application/json',
              version: 2,
              response: {},
              response_type: {},
            },
            { node_title: 'final', to_node_id: final_gw_node_ID, format: 'json', type: 'go' },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseModNodeGW.status).toBe(200);

      const responseCommit = await requestConfirm(api_cor, call_gw_conv_id, company_id);
      expect(responseCommit.status).toBe(200);

      await api_cor.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          company_id,
          conv_id: call_gw_conv_id,
          data: {},
          ref: `ref_${Date.now()}`,
        }),
      );

      await new Promise(r => setTimeout(r, 2200));

      const responseList = await requestList(api_cor, final_api_node_ID, api_call_conv_id, company_id);
      expect(responseList.body.ops[0].list[0].data.__request.path).toEqual(currentPath);
      expect(responseList.body.ops[0].list[0].data.__request.method).toEqual(methodInfo.method);

      const responseListCallGW = await requestList(api_cor, final_gw_node_ID, call_gw_conv_id, company_id);
      expect(responseListCallGW.body.ops[0].list[0].data.sys.conv_id).toEqual(api_call_conv_id);
    });
  });

  afterAll(
    async (): Promise<void> => {
      const response = await requestDeleteObj(api_cor, OBJ_TYPE.PROJECT, project_id, company_id);
      expect(response.status).toBe(200);
      const ResponseDeleteApiKey = await requestDeleteObj(api_cor, OBJ_TYPE.USER, newApiKeyId, company_id);
      expect(ResponseDeleteApiKey.status).toBe(200);
    },
  );
});
