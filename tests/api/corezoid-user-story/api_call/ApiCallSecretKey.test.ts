import { application } from '../../../../application/Application';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { requestConfirm, requestShow } from '../../../../application/api/ApiObj';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import { ApiCallTestSetup, setupApiCallTest, cleanupApiCallTest } from './utils/apiCallTestUtils';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';

describe('API Call Secret Key Tests', (): void => {
  let testSetup: ApiCallTestSetup;
  let newApiKeyId: string | number;
  let newApiKeyLogin: number;
  let newApiKeySecret: string | number;
  let task_id: string | number;

  beforeAll(
    async (): Promise<void> => {
      const apikey = await application.getApiKey();
      const api = application.getApiKeyClient(apikey);
      const company_id = apikey.companies[0].id;

      const CreateKeyResponse = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.USER,
          company_id,
          title: 'API',
          logins: [{ type: 'api' }],
        }),
      );
      expect(CreateKeyResponse.status).toBe(RESP_STATUS.OK);
      const user_api = CreateKeyResponse.body;
      const newApiKey = {
        key: `${user_api.ops[0].users[0].logins[0].obj_id}`,
        secret: user_api.ops[0].users[0].logins[0].key,
        companies: [],
        title: '',
        id: `${user_api.ops[0].users[0].obj_id}`,
      } as ApiKey;
      newApiKeyId = +newApiKey.id;
      newApiKeyLogin = +newApiKey.key;
      newApiKeySecret = newApiKey.secret;

      testSetup = await setupApiCallTest('ApiCall_SecretKey_Tests');

      const { conv_id } = testSetup;
      const responseLink = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.CONV,
          obj_id: conv_id,
          company_id,
          obj_to: 'user',
          obj_to_id: newApiKeyId,
          is_need_to_notify: false,
          privs: [
            { type: 'create', list_obj: ['all'] },
            { type: 'modify', list_obj: ['all'] },
            { type: 'delete', list_obj: ['all'] },
            { type: 'view', list_obj: ['all'] },
          ],
        }),
      );
      expect(responseLink.status).toBe(RESP_STATUS.OK);

      const { process_node_ID, final_node_ID } = testSetup;
      const responseCreateNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id,
          title: 'LogicCallNew',
          description: '',
          obj_type: 0,
          logics: [
            {
              type: 'api',
              format: 'raw',
              method: 'POST',
              raw_body: `{\"ops\":[{\"type\":\"get\",\"obj\":\"callback_hash\",\"conv_id\":\"${conv_id}\",\"company_id\":\"${company_id}\"}]}`,
              url: `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/${newApiKeyLogin}`,
              extra: {},
              extra_type: {},
              max_threads: 5,
              err_node_id: '',
              extra_headers: {},
              send_sys: true,
              cert_pem: '',
              content_type: 'application/json',
              api_secret_outer: `${newApiKeySecret}`,
            },
            { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseCreateNode.status).toBe(RESP_STATUS.OK);
      expect(responseCreateNode.body.ops[0].proc).toEqual(PROC_STATUS.OK);

      const responseCommit = await requestConfirm(api, conv_id, company_id);
      expect(responseCommit.status).toBe(RESP_STATUS.OK);
      const { proc: procCommit } = responseCommit.body.ops[0];
      expect(procCommit).toEqual(PROC_STATUS.OK);
    },
  );

  test('should create API call with api_secret_outer', async (): Promise<void> => {
    const { api, conv_id } = testSetup;

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: {},
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 3500));

    const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask.status).toBe(RESP_STATUS.OK);
    expect(responseShowTask.body.ops[0].data.ops[0].proc).toEqual(PROC_STATUS.OK);
  });

  const secretKeyTestCases = [
    {
      description: 'invalid secret key',
      apiSecretOuter: 'incorectKey',
    },
    {
      description: 'no secret key',
      apiSecretOuter: undefined,
    },
  ];

  test.each(secretKeyTestCases)(
    'should process request with $description',
    async ({ apiSecretOuter }): Promise<void> => {
      const { api, conv_id, process_node_ID, final_node_ID, company_id } = testSetup;

      const apiLogic: any = {
        type: 'api',
        format: 'raw',
        method: 'POST',
        raw_body: `{\"ops\":[{\"type\":\"get\",\"obj\":\"callback_hash\",\"conv_id\":\"${conv_id}\",\"company_id\":\"${company_id}\"}]}`,
        url: `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/${newApiKeyLogin}`,
        extra: {},
        extra_type: {},
        max_threads: 5,
        err_node_id: '',
        extra_headers: {},
        send_sys: true,
        cert_pem: '',
        content_type: 'application/json',
        api_secret_outer: apiSecretOuter,
      };

      const responseCreateNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id,
          obj_type: 0,
          logics: [apiLogic, { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' }],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseCreateNode.status).toBe(RESP_STATUS.OK);

      const responseCommit = await requestConfirm(api, conv_id, company_id);
      expect(responseCommit.status).toBe(RESP_STATUS.OK);

      const responseTask = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          conv_id,
          data: {},
          ref: `ref_${Date.now()}`,
        }),
      );
      expect(responseTask.status).toBe(RESP_STATUS.OK);
      task_id = responseTask.body.ops[0].obj_id;

      await new Promise(r => setTimeout(r, 3500));

      const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
      expect(responseShowTask.status).toBe(RESP_STATUS.OK);

      const taskData = responseShowTask.body.ops[0].data;
      expect(taskData.ops[0].proc).toBe(PROC_STATUS.ERROR);
      expect(taskData.ops[0].description).toContain('not correct sign');
    },
  );

  afterAll(
    async (): Promise<void> => {
      const { api, company_id } = testSetup;

      await cleanupApiCallTest(testSetup);
      const responseDelKey = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.USER,
          obj_id: newApiKeyId,
          company_id,
        }),
      );
      expect(responseDelKey.status).toBe(RESP_STATUS.OK);
    },
  );
});
