import { requestConfirm, requestShow } from '../../../../application/api/ApiObj';
import {
  createRequestWithOps,
  REQUEST_TYPE,
  OBJ_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../../utils/corezoidRequest';
import { ApiCallTestSetup, setupApiCallTest, cleanupApiCallTest } from './utils/apiCallTestUtils';

describe('API Call Content Types Tests', (): void => {
  let testSetup: ApiCallTestSetup;
  let task_id: string | number;
  const url = 'https://postman-echo.com/post';

  beforeAll(
    async (): Promise<void> => {
      testSetup = await setupApiCallTest('ApiCall_ContentTypes_Tests');
    },
  );

  const testCases = [
    {
      contentType: 'application/json',
      requestBody: JSON.stringify({ field1: 'value1', field2: 123 }),
      description: 'Отправка JSON объекта',
    },
    {
      contentType: 'text/xml',
      requestBody: '<?xml version="1.0" encoding="UTF-8"?><root><item>Значение</item></root>',
      description: 'Отправка XML данных',
    },
    {
      contentType: 'application/x-www-form-urlencoded',
      requestBody: 'param1=value1&param2=value2&param3=value3',
      description: 'Отправка данных формы',
    },
  ];

  describe.each(testCases)('Content-Type: $contentType', ({ contentType, requestBody, description }): void => {
    test(`should correctly handle ${contentType} content type - ${description}`, async (): Promise<void> => {
      const { api, conv_id, process_node_ID, final_node_ID, company_id } = testSetup;

      const apiLogic: any = {
        type: 'api',
        format: '',
        method: 'POST',
        url,
        extra: {},
        extra_type: {},
        max_threads: 5,
        err_node_id: '',
        extra_headers: { 'Content-Type': contentType },
        send_sys: false,
        cert_pem: '',
        content_type: contentType,
        raw_body: requestBody,
      };

      const logics = [apiLogic, { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' }];

      const responseCreateNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id,
          title: `LogicCall_ContentType_${contentType.replace('/', '_')}`,
          description: `Test API call with ${contentType} content type`,
          obj_type: 0,
          logics,
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseCreateNode.status).toBe(RESP_STATUS.OK);

      const responseCommit = await requestConfirm(api, conv_id, company_id);
      expect(responseCommit.status).toBe(RESP_STATUS.OK);

      const responseTask = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          conv_id,
          data: { test_data: `Test ${contentType} content type` },
          ref: `ref_${Date.now()}`,
        }),
      );
      expect(responseTask.status).toBe(RESP_STATUS.OK);
      expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
      expect(responseTask.body.ops[0].obj).toEqual('task');
      task_id = responseTask.body.ops[0].obj_id;

      await new Promise(r => setTimeout(r, 10000));

      const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
      expect(responseShowTask.status).toBe(RESP_STATUS.OK);

      const taskData = responseShowTask.body.ops[0].data;
      expect(taskData).toBeTruthy();
      expect(taskData.test_data).toBe(`Test ${contentType} content type`);
      expect(taskData.headers['content-type']).toContain(contentType);
    });
  });

  afterAll(
    async (): Promise<void> => {
      await cleanupApiCallTest(testSetup);
    },
  );
});
