# API Call Node Testing Checklist (for parameterized tests)

## 1. Basic Tests of HTTP Methods with JSON Format

### Test Cases for All Methods and Request Formats

| HTTP Method | Request Format | Test | Status |
| ---------- | -------------- | -------------------------------------- | ------ |
| GET | Default | Request with parameters in URL | [x] |
| GET | Raw | Request with parameters in URL | [x] |
| POST | Default | Sending data via extra parameters | [x] |
| POST | Raw | Sending JSON in request body | [x] |
| PUT | Default | Update via extra parameters | [x] |
| PUT | Raw | Update via JSON in body | [x] |
| DELETE | Default | Delete with parameters in URL | [x] |
| DELETE | Raw | Delete with data in body | [x] |
| PATCH | Default | Partial update via extra | [x] |
| PATCH | Raw | Partial update via JSON in body | [x] |
| OPTIONS | Default | Check available methods | [x] |
| HEAD | Default | Get headers only | [x] |

## 2. Tests for Different Data Formats

### Tests with Different Data Formats

| HTTP Method | Request Format | Data Format | Test | Status |
| ---------- | -------------- | --------------------------------- | --------------------- | ------ |
| POST | Raw | application/json | Send JSON object | [x] |
| POST | Raw | text/xml | Send XML data | [x] |
| POST | Raw | application/x-www-form-urlencoded | Send form data | [x] |

## 3. Response Handling Tests

### HTTP Status Verification

| HTTP Status | Test | Status |
| ---------------- | --------------------------------------- | ------ |
| 200 OK | Check successful response | [x] |
| 201 Created | Check resource creation | [x] |
| 204 No Content | Check empty response | [x] |
| 400 Bad Request | Check client error handling | [x] |
| 401 Unauthorized | Check authorization error | [x] |
| 403 Forbidden | Check access to forbidden resource | [x] |
| 404 Not Found | Check nonexistent resource | [x] |
| 500 Server Error | Check server error handling | [x] |

## 4. Other Parameters (Additional API Call Node Parameters)

### 4.1. Header Parameters (extra_headers)

| Header Type | Test | Status |
| ---------------- | --------------------------------------- | ------ |
| Content-Type | Setting different content types | [x] |
| Authorization | Testing different authorization schemes | [x] |
| Custom headers | Adding custom headers | [x] |
| Multiple headers | Testing multiple headers | [x] |

### 4.2. Customize Response Parameters (response, response_type)

| Response Parameter | response_type | Test | Status |
| ----------------- | ----------------- | ------------------------------- | ------ |
| field1 | string | Override string field | [x] |
| field2 | number | Override numeric field | [x] |
| field3 | boolean | Override boolean field | [x] |
| nested.field | object | Override nested field | [x] |
| array_field | array | Override array | [x] |

### 4.3. System Parameters (send_sys)

| send_sys Value | Test | Status |
| ----------------- | ----------------------------------------- | ------ |
| true | Add system parameters to request | [x] |
| false | Send request without system parameters | [x] |

### 4.4. Request Signing (api_secret_outer)

| Scenario | Test | Status |
| ------------------ | --------------------------------------------- | ------ |
| Valid secret key | Sign request with correct secret key | [x] |
| Invalid secret key | Error when using incorrect key | [x] |
| No key | Behavior without request signature | [x] |

### 4.5. RFC Standard Response (rfc_format)

| Value | Test | Status |
| -------- | ------------------------------------------ | ------ |
| true | Check response compliance with RFC standard | [x] |
| false | Check standard response format | [x] |

### 4.6. Debug Information (debug_info)

| Value | Test | Status |
| -------- | -------------------------------- | ------ |
| true | Enable debug information | [x] |
| false | Disable debug information | [x] |

### 4.7. Certificate Signing (cert_pem)

| Scenario | Test | Status |
| ------------------- | ---------------------------------------------- | ------ |
| Valid certificate | Sign request with valid certificate | [x] |
| Invalid certificate | Error when using invalid certificate | [x] |