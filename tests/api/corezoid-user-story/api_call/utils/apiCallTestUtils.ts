import { ApiKeyClient } from '../../../../../application/api/ApiKeyClient';
import { requestDeleteObj, requestCreateObj, requestListConv } from '../../../../../application/api/ApiObj';
import { application } from '../../../../../application/Application';
import { ApiKey } from '../../../../../infrastructure/model/ApiKey';
import { OBJ_TYPE, RESP_STATUS } from '../../../../../utils/corezoidRequest';

export interface ApiCallTestSetup {
  api: ApiKeyClient;
  apikey: ApiKey;
  company_id: any;
  conv_id: number;
  process_node_ID: string | number;
  final_node_ID: string | number;
}

export async function setupApiCallTest(conveyorName: string): Promise<ApiCallTestSetup> {
  const apikey = await application.getApiKey();
  const api = application.getApiKeyClient(apikey);
  const company_id = apikey.companies[0].id;

  const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, conveyorName);
  expect(response.status).toBe(RESP_STATUS.OK);
  const conv_id = response.body.ops[0].obj_id;

  const responseList = await requestListConv(api, conv_id, company_id);
  expect(responseList.status).toBe(RESP_STATUS.OK);

  const processNode = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process');
  if (!processNode) throw new Error('Process node not found');
  const process_node_ID = processNode.obj_id;

  const finalNode = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final');
  if (!finalNode) throw new Error('Final node not found');
  const final_node_ID = finalNode.obj_id;

  return {
    api,
    apikey,
    company_id,
    conv_id,
    process_node_ID,
    final_node_ID,
  };
}

export async function cleanupApiCallTest(setup: ApiCallTestSetup): Promise<void> {
  const { api, conv_id, company_id } = setup;

  const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
  expect(response.status).toBe(RESP_STATUS.OK);
}
