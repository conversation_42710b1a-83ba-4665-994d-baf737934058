import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../utils/corezoidRequest';
import { SchemaValidator } from '../../../../application/api/SchemaValidator';
import mergeSchema from '../schemas/merge.json';
import deleteCommitSchema from '../schemas/delete_commit.json';

describe('Delete_commit', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let project_short_name: string;
  let newStageProd: string | number;
  let newStageDev: string | number;
  let stage_short_nameDev: string;
  let stage_short_nameProd: string;
  let newConv1: string | number;
  let newConv2: string | number;
  let newConv3: string | number;
  let nodeFinal: string;
  let nodeGo: string;
  let newAlias1: string | number;
  let newAlias2: string | number;
  let newAlias3: string | number;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = `project-${Date.now()}`;
    stage_short_nameProd = `stageprod-${Date.now()}`;
    stage_short_nameDev = `stagedev-${Date.now()}`;

    const responseProject = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: `Project_${Date.now()}`,
        short_name: project_short_name,
        description: 'test',
        stages: [],
        status: 'active',
      }),
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStageDev = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        title: `StageDev_${Date.now()}`,
        short_name: stage_short_nameDev,
        description: 'test',
        project_id: newProject,
      }),
    );
    newStageDev = responseStageDev.body.ops[0].obj_id;

    const responseStageProd = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        title: `StageProd_${Date.now()}`,
        short_name: stage_short_nameProd,
        description: 'test',
        immutable: true,
        project_id: newProject,
      }),
    );
    newStageProd = responseStageProd.body.ops[0].obj_id;

    const responseConv1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `Conv1_${Date.now()}`,
        conv_type: 'process',
        stage_id: newStageDev,
        project_id: newProject,
      }),
    );
    newConv1 = responseConv1.body.ops[0].obj_id;

    const responseConv2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `Conv2_${Date.now()}`,
        conv_type: 'process',
        stage_id: newStageDev,
        project_id: newProject,
      }),
    );
    newConv2 = responseConv2.body.ops[0].obj_id;

    const responseAlias1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: `alias1-${Date.now()}`,
        title: 'Alias1',
        description: 'test',
        project_id: newProject,
        stage_id: newStageDev,
      }),
    );
    newAlias1 = responseAlias1.body.ops[0].obj_id;

    const responseAlias2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: `alias2-${Date.now()}`,
        title: 'Alias2',
        description: 'test',
        project_id: newProject,
        stage_id: newStageDev,
      }),
    );
    newAlias2 = responseAlias2.body.ops[0].obj_id;

    const responseAlias3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: `alias3-${Date.now()}`,
        title: 'Alias3',
        description: 'test',
        project_id: newProject,
        stage_id: newStageDev,
      }),
    );
    newAlias3 = responseAlias3.body.ops[0].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        link: true,
        obj_id: newAlias1,
        obj_to_type: OBJ_TYPE.CONV,
        obj_to_id: newConv1,
      }),
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        link: true,
        obj_id: newAlias2,
        obj_to_type: OBJ_TYPE.CONV,
        obj_to_id: newConv2,
      }),
    );

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        link: true,
        obj_id: newAlias3,
        obj_to_type: OBJ_TYPE.CONV,
        obj_to_id: newConv2,
      }),
    );
  });

  test(`should merge objects to prod`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        company_id,
        project_id: newProject,
        obj_id: newStageProd,
        obj_type: 'stage',
        obj_to_id: newStageDev,
        obj_to_type: OBJ_TYPE.STAGE,
        apply_mode: true,
        async: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].diff.obj_id).toBe(newStageProd);
    expect(response.body.ops[0].diff.obj_type).toBe('stage');
    SchemaValidator.validate(mergeSchema, response.body);

    const responseListStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        company_id,
        project_id: newProject,
        obj_id: newStageProd,
      }),
    );
    expect(responseListStage.status).toBe(200);
    expect(responseListStage.body.ops[0].list[0].obj_type).toBe('conv');
    expect(responseListStage.body.ops[0].list[1].obj_type).toBe('conv');
    expect(responseListStage.body.ops[0].list[0].is_deployed).toBe(true);
    expect(responseListStage.body.ops[0].list[1].is_deployed).toBe(true);

    const responseListAliases = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id: newProject,
        stage_id: newStageProd,
        sort: 'title',
        order: 'desc',
      }),
    );
    expect(responseListAliases.status).toBe(200);
    expect(responseListAliases.body.ops[0].list[0].obj_type).toBe('alias');
    expect(responseListAliases.body.ops[0].list[1].obj_type).toBe('alias');
    expect(responseListAliases.body.ops[0].list[2].obj_type).toBe('alias');
    expect(responseListAliases.body.ops[0].list[0].title).toBe('Alias3');
    expect(responseListAliases.body.ops[0].list[1].title).toBe('Alias2');
    expect(responseListAliases.body.ops[0].list[2].title).toBe('Alias1');
    expect(responseListAliases.body.ops[0].list[0].obj_to_title).toContain('Conv2_');
    expect(responseListAliases.body.ops[0].list[1].obj_to_title).toContain('Conv2_');
    expect(responseListAliases.body.ops[0].list[2].obj_to_title).toContain('Conv1_');
    expect(responseListAliases.body.ops[0].list).toBeArrayOfSize(3);
  });

  test.skip(`should merge objects to prod - with errors`, async () => {
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        company_id,
        obj_id: newConv2,
        stage_id: newStageDev,
        project_id: newProject,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newAlias2,
        stage_id: newStageDev,
        project_id: newProject,
      }),
    );
    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        obj_id: newAlias3,
        stage_id: newStageDev,
        project_id: newProject,
      }),
    );

    const responseConv3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `Conv3_${Date.now()}`,
        conv_type: 'process',
        stage_id: newStageDev,
        project_id: newProject,
      }),
    );
    newConv3 = responseConv3.body.ops[0].obj_id;

    const responseListConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv3,
      }),
    );
    nodeGo = responseListConv.body.ops[0].list[1].obj_id;
    nodeFinal = responseListConv.body.ops[0].list[2].obj_id;

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeGo,
        conv_id: newConv3,
        obj_type: 0,
        logics: [
          {
            type: 'api_copy',
            sync: false,
            err_node_id: '',
            conv_id: newConv1,
            mode: 'create',
            group: 'all',
            ref: '0aeoket56d0et1a',
            data: { Object: 'Table' },
            data_type: { Object: 'string' },
          },
          {
            to_node_id: nodeFinal,
            format: 'json',
            type: 'go',
            index: 1,
            node_title: 'final',
          },
        ],
        title: 'Copy',
        semaphors: [],
        version: 1,
      }),
    );

    await new Promise(r => setTimeout(r, 10000));
    const responseConfirm = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        obj_id: newConv3,
        obj_type: 'conv',
        version: 1,
      }),
    );
    expect(responseConfirm.status).toBe(200);

    await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV,
        obj_id: newConv1,
        status: 'paused',
      }),
    );

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        company_id,
        project_id: newProject,
        obj_id: newStageProd,
        obj_type: 'stage',
        obj_to_id: newStageDev,
        obj_to_type: OBJ_TYPE.STAGE,
        apply_mode: true,
        async: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].errors[0].obj).toBe('stage');
    expect(response.body.ops[0].errors[0].count).toBe(1);
    expect(response.body.ops[0].errors[0].destinations[0].obj).toBe('conv');
    expect(response.body.ops[0].errors[0].destinations[0].count).toBe(1);

    const responseListStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        company_id,
        project_id: newProject,
        obj_id: newStageProd,
      }),
    );
    expect(responseListStage.status).toBe(200);
    expect(responseListStage.body.ops[0].list[0].obj_type).toBe('conv');
    expect(responseListStage.body.ops[0].list[1].obj_type).toBe('conv');
    expect(responseListStage.body.ops[0].list[2].obj_type).toBe('conv');
    expect(responseListStage.body.ops[0].list).toBeArrayOfSize(3);
    expect(responseListStage.body.ops[0].list[0].is_deployed).toBe(true);
    expect(responseListStage.body.ops[0].list[1].is_deployed).toBe(true);
    expect(responseListStage.body.ops[0].list[2].is_deployed).toBe(false);

    const responseListAliases = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id: newProject,
        stage_id: newStageProd,
        sort: 'date',
        order: 'desc',
      }),
    );
    expect(responseListAliases.status).toBe(200);
    expect(responseListAliases.body.ops[0].list[0].obj_type).toBe('alias');
    expect(responseListAliases.body.ops[0].list[1].obj_type).toBe('alias');
    expect(responseListAliases.body.ops[0].list[2].obj_type).toBe('alias');
    expect(responseListAliases.body.ops[0].list).toBeArrayOfSize(3);
  });

  test(`should delete commit prod`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.COMMIT,
        company_id,
        version: 2,
        obj_id: newStageProd,
        obj_type: 'stage',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj_type).toBe('stage');
    expect(response.body.ops[0].version).toBe(2);
    SchemaValidator.validate(deleteCommitSchema, response.body);

    const responseListStage = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        company_id,
        project_id: newProject,
        obj_id: newStageProd,
      }),
    );
    expect(responseListStage.status).toBe(200);
    expect(responseListStage.body.ops[0].list[0].obj_type).toBe('conv');
    expect(responseListStage.body.ops[0].list[1].obj_type).toBe('conv');
    expect(responseListStage.body.ops[0].list).toBeArrayOfSize(2);
    expect(responseListStage.body.ops[0].list[0].is_deployed).toBe(true);
    expect(responseListStage.body.ops[0].list[1].is_deployed).toBe(true);

    const responseListAliases = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id: newProject,
        stage_id: newStageProd,
        sort: 'date',
        order: 'desc',
      }),
    );
    expect(responseListAliases.status).toBe(200);
    expect(responseListAliases.body.ops[0].list[0].obj_type).toBe('alias');
    expect(responseListAliases.body.ops[0].list[1].obj_type).toBe('alias');
    expect(responseListAliases.body.ops[0].list[2].obj_type).toBe('alias');
    expect(responseListAliases.body.ops[0].list).toBeArrayOfSize(3);
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
