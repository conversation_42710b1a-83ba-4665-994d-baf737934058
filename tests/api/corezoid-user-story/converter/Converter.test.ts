import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
  generateName,
} from '../../../../utils/corezoidRequest';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import {
  requestConfirm,
  requestDeleteObj,
  requestCreateObjNew,
  requestListConv,
} from '../../../../application/api/ApiObj';
import { NODE_TYPE, NODE_LOGIC_TYPE, ModifyNodeResponse } from '../../../../application/api/obj_types/node';
import { exec } from 'child_process';
import { error } from '../../../../support/utils/logger';

describe('Converter', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let testRawBody: string;
  let task_id_api: number;
  let company_id: string | null | undefined;
  let project_short_name: string;
  let stage_short_name: string;
  let conv_id: number;
  let newApiKeyId: string | number;
  let newApiKeyLogin: number;
  let newApiKeySecret: string | number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let conv_type: string;
  let url: string;
  let currentDate: string;
  let callback_hash: string;
  let data: string;
  let create_time: number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    project_short_name = generateName(OBJ_TYPE.PROJECT);
    stage_short_name = generateName(OBJ_TYPE.STAGE);
    conv_type = 'process';
    data = 'testdata';

    const now = new Date();
    currentDate = now.toISOString().split('T')[0];

    const responseProject = await requestCreateObjNew(
      api,
      OBJ_TYPE.PROJECT,
      company_id,
      generateName(OBJ_TYPE.PROJECT),
      {
        short_name: project_short_name,
        stages: [],
      },
    );
    newProject = responseProject.body.ops[0].obj_id;

    const responseStage = await requestCreateObjNew(api, OBJ_TYPE.STAGE, company_id, generateName(OBJ_TYPE.STAGE), {
      short_name: stage_short_name,
      project_id: newProject,
    });
    newStage = responseStage.body.ops[0].obj_id;

    const CreateKeyResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(RESP_STATUS.OK);
    const user_api = CreateKeyResponse.body;
    const newApiKey = {
      key: `${user_api.ops[0].users[0].logins[0].obj_id}`,
      secret: user_api.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user_api.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newApiKeyId = +newApiKey.id;
    newApiKeyLogin = +newApiKey.key;
    newApiKeySecret = newApiKey.secret;

    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        company_id,
        obj_to: 'user',
        obj_to_id: newApiKeyId,
        is_need_to_notify: false,
        privs: [
          { type: 'create', list_obj: ['all'] },
          { type: 'modify', list_obj: ['all'] },
          { type: 'delete', list_obj: ['all'] },
          { type: 'view', list_obj: ['all'] },
        ],
      }),
    );
    expect(responseLink.status).toBe(RESP_STATUS.OK);

    const responseConv = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, generateName(OBJ_TYPE.CONV), {
      conv_type,
      stage_id: newStage,
      project_id: newProject,
    });
    conv_id = responseConv.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_id, company_id);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === conv_type).obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    testRawBody = JSON.stringify({
      ops: [{ type: 'get', conv_id: conv_id, obj: 'callback_hash' }],
    });
    url = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/${newApiKeyLogin}`;

    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'LogicCallNew',
        description: '',
        obj_type: NODE_TYPE.Normal,
        logics: [
          {
            type: NODE_LOGIC_TYPE.Api,
            format: 'raw',
            method: 'POST',
            raw_body: testRawBody,
            url,
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: '',
            extra_headers: {},
            send_sys: true,
            cert_pem: '',
            content_type: 'application/json',
            api_secret_outer: `${newApiKeySecret}`,
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );

    expect(responseCreateNode.status).toBe(RESP_STATUS.OK);
    const { request_proc: procCreateNode } = responseCreateNode.body as ModifyNodeResponse;
    expect(procCreateNode).toBe(PROC_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);
    const { proc: procCommit } = responseCommit.body.ops[0];
    expect(procCommit).toEqual(PROC_STATUS.OK);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id,
        data: { a: 'testdata' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    const { obj_id: ObjIdTask } = responseTask.body.ops[0];
    task_id_api = ObjIdTask;

    await new Promise(r => setTimeout(r, 4000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        ref_or_obj_id: task_id_api,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    const { data: dataShowTask } = responseShow.body.ops[0];
    callback_hash = dataShowTask.ops[0].callback_hash;
    create_time = responseShow.body.ops[0].create_time;
  });

  const searchFieldsArchivePositive = [
    {
      description: 'should search by task_id',
      field: (): string => `task_id:*${task_id_api}*`,
    },
    {
      description: 'should search by node',
      field: (): string => `node_id:*${final_node_ID}*`,
    },
    {
      description: 'should search by conv_id',
      field: (): string => `conv_id:${conv_id}`,
    },
    {
      description: 'should search by data',
      field: (): string => `data:*${data}*`,
    },
    {
      description: 'should search by create_time',
      field: (): string => `create_time:*${create_time}*`,
    },
  ];

  describe.each(searchFieldsArchivePositive)('$description', ({ field }) => {
    test(`should search task converter(archive)`, async (): Promise<void> => {
      const curlCommand = `curl http://************:9200/pre_capi_process_${conv_id}/_search?q=${field()} -XGET -H 'Content-Type: application/json'`;
      exec(curlCommand, (err, stdout) => {
        if (err) {
          error(`Error executing curl: ${err}`);
          return;
        }
        const elasticsearchResponse = JSON.parse(stdout);
        const callbackHashFromElastic = JSON.parse(elasticsearchResponse.hits.hits[0]._source.data).ops[0]
          .callback_hash;
        expect(callback_hash).toEqual(callbackHashFromElastic);
      });
    });
  });

  const searchFieldsHttpPositive = [
    {
      description: 'should search by task_id',
      field: (): string => `task_id:*${task_id_api}*`,
    },
    {
      description: 'should search by request_body',
      field: (): string => `request_body:*${conv_id}*`,
    },
    {
      description: 'should search by conv_id',
      field: (): string => `conv_id:${conv_id}`,
    },
    {
      description: 'should search by url',
      field: (): string => `\"https://admin-pre.corezoid.com/api/2/json/${newApiKeyLogin}\"`,
    },
  ];

  describe.each(searchFieldsHttpPositive)('$description', ({ field }) => {
    test(`should search task converter(HTTP)`, async (): Promise<void> => {
      const curlCommand = `curl http://************:9200/prehttp_logs${currentDate}/_search?q=${field()} -XGET -H 'Content-Type: application/json'`;
      exec(curlCommand, (err, stdout) => {
        if (err) {
          error(`Error executing curl: ${err}`);
          return;
        }
        const elasticsearchResponse = JSON.parse(stdout);
        const callbackHashFromElastic = JSON.parse(elasticsearchResponse.hits.hits[0]._source.response_body).ops[0]
          .callback_hash;
        expect(callback_hash).toEqual(callbackHashFromElastic);
      });
    });
  });

  test(`should search task converter by task_id (arhive/http) after delete task`, async () => {
    const responseDel = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        obj_id: task_id_api,
        node_id: final_node_ID,
      }),
    );
    expect(responseDel.status).toBe(RESP_STATUS.OK);
    const { proc: procDel } = responseDel.body.ops[0];
    expect(procDel).toEqual(PROC_STATUS.OK);

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        ref_or_obj_id: task_id_api,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].description).toBe('task not found');

    const curlCommand = `curl http://************:9200/pre_capi_process_${conv_id}/_search?q=\"task_id:*${task_id_api}*\" -XGET -H 'Content-Type: application/json'`;
    exec(curlCommand, (err, stdout) => {
      if (err) {
        error(`Error executing curl: ${err}`);
        return;
      }
      const elasticsearchResponse = JSON.parse(stdout);
      const callbackHashFromElastic = JSON.parse(elasticsearchResponse.hits.hits[0]._source.data).ops[0].callback_hash;
      expect(callback_hash).toEqual(callbackHashFromElastic);

      const curlCommand = `curl http://************:9200/prehttp_logs${currentDate}/_search?q=\"task_id:*${task_id_api}*\" -XGET -H 'Content-Type: application/json'`;
      exec(curlCommand, (err, stdout) => {
        if (err) {
          error(`Error executing curl: ${err}`);
          return;
        }
        const elasticsearchResponse = JSON.parse(stdout);
        const callbackHashFromElastic = JSON.parse(elasticsearchResponse.hits.hits[0]._source.response_body).ops[0]
          .callback_hash;
        expect(callback_hash).toEqual(callbackHashFromElastic);
      });
    });
  });

  test(`should search task converter by task_id (arhive/http) after modify save_arhive_task:false`, async () => {
    const responseModify = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        company_id,
        obj_id: final_node_ID,
        conv_id,
        obj_type: 2,
        options: { save_task: false },
        logics: [],
        semaphors: [],
        position: [640, 400],
        version: 22,
      }),
    );
    expect(responseModify.status).toBe(RESP_STATUS.OK);
    const { proc: procModify } = responseModify.body.ops[0];
    expect(procModify).toEqual(PROC_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);
    const { proc: procCommit } = responseCommit.body.ops[0];
    expect(procCommit).toEqual(PROC_STATUS.OK);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id,
        data: { a: 'testdata' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    const { obj_id: ObjIdTask } = responseTask.body.ops[0];
    task_id_api = ObjIdTask;

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        ref_or_obj_id: task_id_api,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].description).toBe('task not found');

    const curlCommand = `curl http://************:9200/pre_capi_process_${conv_id}/_search?q=\"task_id:*${task_id_api}*\" -XGET -H 'Content-Type: application/json'`;
    exec(curlCommand, (err, stdout) => {
      if (err) {
        error(`Error executing curl: ${err}`);
        return;
      }
      const elasticsearchResponse = JSON.parse(stdout);
      const callbackHashFromElastic = JSON.parse(elasticsearchResponse.hits.hits[0]._source.data).ops[0].callback_hash;
      expect(callback_hash).toEqual(callbackHashFromElastic);

      const curlCommand = `curl http://************:9200/prehttp_logs${currentDate}/_search?q=\"task_id:*${task_id_api}*\" -XGET -H 'Content-Type: application/json'`;
      exec(curlCommand, (err, stdout) => {
        if (err) {
          error(`Error executing curl: ${err}`);
          return;
        }
        const elasticsearchResponse = JSON.parse(stdout);
        const callbackHashFromElastic = JSON.parse(elasticsearchResponse.hits.hits[0]._source.response_body).ops[0]
          .callback_hash;
        expect(callback_hash).toEqual(callbackHashFromElastic);
      });
    });
  });

  const searchFieldsNegativeSearch = [
    {
      description: `shouldn't search by task_id with not valid search`,
      field: (): string => `task_id:*111*`,
    },
    {
      description: `shouldn't search by conv_id with not valid search`,
      field: (): string => `conv_id:111`,
    },
  ];

  describe.each(searchFieldsNegativeSearch)('$description', ({ field }) => {
    test(`shouldn't search task converter(archive/http)`, async () => {
      const curlCommand = `curl http://************:9200/pre_capi_process_${conv_id}/_search?q=${field()} -XGET -H 'Content-Type: application/json'`;
      exec(curlCommand, (err, stdout) => {
        if (err) {
          error(`Error executing curl: ${err}`);
          return;
        }
        const elasticsearchResponse = JSON.parse(stdout);
        expect(elasticsearchResponse.hits.hits).toBeArrayOfSize(0);

        const curlCommand = `curl http://************:9200/prehttp_logs${currentDate}/_search?q=${field()} -XGET -H 'Content-Type: application/json'`;
        exec(curlCommand, (err, stdout) => {
          if (err) {
            error(`Error executing curl: ${err}`);
            return;
          }
          const elasticsearchResponse = JSON.parse(stdout);
          expect(elasticsearchResponse.hits.hits).toBeArrayOfSize(0);
        });
      });
    });
  });

  const searchFieldsNegativeIndex = [
    {
      description: `shouldn't search by task_id with not valid index`,
      field: (): string => `task_id:*${task_id_api}*`,
      key: (): string | number => newStage,
    },
  ];

  describe.each(searchFieldsNegativeIndex)('$description', ({ field, key }) => {
    test(`shouldn't search task converter(archive/http)`, async () => {
      const curlCommand = `curl http://************:9200/pre_capi_process_${key()}/_search?q=${field()} -XGET -H 'Content-Type: application/json'`;
      exec(curlCommand, (err, stdout) => {
        if (err) {
          error(`Error executing curl: ${err}`);
          return;
        }
        const elasticsearchResponse = JSON.parse(stdout);
        expect(elasticsearchResponse.error.root_cause[0].type).toBe('index_not_found_exception');

        const curlCommand = `curl http://************:9200/prehttp_logs${key()}/_search?q=${field()} -XGET -H 'Content-Type: application/json'`;
        exec(curlCommand, (err, stdout) => {
          if (err) {
            error(`Error executing curl: ${err}`);
            return;
          }
          const elasticsearchResponse = JSON.parse(stdout);
          expect(elasticsearchResponse.error.root_cause[0].type).toBe('index_not_found_exception');
        });
      });
    });
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(response.status).toBe(RESP_STATUS.OK);

    const responseKey = await requestDeleteObj(api, OBJ_TYPE.USER, newApiKeyId, company_id);
    expect(responseKey.status).toBe(RESP_STATUS.OK);
  });
});
