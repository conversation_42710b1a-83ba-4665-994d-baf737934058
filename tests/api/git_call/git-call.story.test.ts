import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import faker from 'faker';
import { WebSocketClient } from '../../../application/api/websocketUtils';
import { User } from '../../../infrastructure/model/User';
import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';
import * as data from './git_callTestData.json';
import { requestDeleteObj, requestCreateObj, requestListConv } from '../../../application/api/ApiObj';
import { createAuthUser, Method } from '../../../utils/request';
import { debug, error } from '../../../support/utils/logger';

describe('Git_call story (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newConv: number;
  let newConv1: number;
  let companyId: any;
  let newNodeFinal: string;
  let process_node_ID: string;
  let final_node_ID: string;
  let process_node_ID1: string;
  let final_node_ID1: string;
  let start_node_ID: string;
  let user_id: number;
  let wsClient: WebSocketClient;
  let cookie: string;
  let cookieUserAuth: any;
  let user: User;
  let origin: string;
  let ws_url: string;
  let ws_path: string;
  let task_id: string;
  let cancel: any;
  let nodeLogic: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    companyId = apikey.companies[0].id;
    origin = ConfigurationManager.getConfiguration().getApiUrl();
    [ws_url, ws_path] = ConfigurationManager.getConfiguration().getWsUrl();

    user = await application.getAuthorizedUser({ company: {} }, 6);
    cookie = user.cookieUser;
    cookieUserAuth = createAuthUser(cookie, 'cookie');
    user_id = user.id;

    wsClient = new WebSocketClient(ws_url, ws_path, cookie, origin);
    await wsClient.connect();

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, companyId, `Conv`);
    newConv = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, newConv, companyId);
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
    start_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'start').obj_id;

    const addUserInCompany = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id: companyId,
        title: 'NewUser',
        logins: [{ type: 'google', login: '<EMAIL>' }],
      }),
    );
    expect(addUserInCompany.status).toBe(200);

    const responseNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id: companyId,
        obj_type: 2,
        description: 'final',
        conv_id: newConv,
        title: `Node_Final`,
        version: 22,
      }),
    );
    newNodeFinal = responseNode.body.ops[0].obj_id;

    const responseCreateConv = await cookieUserAuth.request({
      method: Method.POST,
      url: `${origin}api/2/json`,
      data: {
        ops: [
          {
            type: 'create',
            obj: 'conv',
            conv_type: 'process',
            obj_type: 0,
            status: 'active',
            title: 'NewProcessUser1',
            description: '',
            folder_id: 0,
            company_id: companyId,
          },
        ],
      },
    });
    expect(responseCreateConv.status).toBe(200);
    expect(responseCreateConv.data.ops[0].proc).toBe('ok');
    newConv1 = responseCreateConv.data.ops[0].obj_id;

    const responseListConv = await cookieUserAuth.request({
      method: Method.POST,
      url: `${origin}api/2/json`,
      data: {
        ops: [
          {
            type: 'list',
            obj: 'conv',
            obj_id: newConv1,
            company_id: companyId,
          },
        ],
      },
    });
    expect(responseListConv.status).toBe(200);
    expect(responseListConv.data.ops[0].proc).toBe('ok');
    process_node_ID1 = (responseListConv.data.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID1 = (responseListConv.data.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
  });

  test(`Check git_call http`, async () => {
    const obj_id = faker.random.alphaNumeric(10);
    const responseModify = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        company_id: companyId,

        conv_id: newConv,
        obj_type: 0,
        title: 'git-call',
        description: 'git-call',
        version: 22,
        logics: [
          {
            type: 'git_call',
            version: 2,
            lang: `js`,
            err_node_id: newNodeFinal,
            code: `module.exports = (data) => {data.a = 1234; return data;};`,
            repo: ``,
            commit: ``,
            script: ``,
          },
          {
            type: 'go',
            to_node_id: final_node_ID,
          },
        ],
        semaphors: [
          {
            type: 'time',
            value: 600,
            dimension: 'min',
            to_node_id: newNodeFinal,
          },
        ],
      }),
    );
    expect(responseModify.status).toBe(200);

    const responseCreate = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GIT_CALL,
        company_id: companyId,

        conv_id: newConv,
        obj_type: 'function_build',
        obj_id,
        node_id: process_node_ID,
        version: 2,
        lang: `js`,
        code: `module.exports = (data) => {data.a = 1234; return data;};`,
        repo: ``,
        commit: ``,
        script: ``,
      }),
    );
    expect(responseCreate.status).toBe(200);
    expect(responseCreate.body.ops[0].proc).toEqual('ok');

    const responseGet = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.GIT_CALL,
        company_id: companyId,

        conv_id: newConv,
        obj_type: 'function_build',
        node_id: process_node_ID,
        version: 2,
        obj_id,
      }),
    );
    expect(responseGet.status).toBe(200);
    expect(responseGet.body.ops[0].proc).toEqual('ok');

    await new Promise(r => setTimeout(r, 25000));

    const responseGet2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.GIT_CALL,
        company_id: companyId,

        conv_id: newConv,
        obj_type: 'function_build',
        node_id: process_node_ID,
        version: 2,
        obj_id,
      }),
    );
    expect(responseGet2.status).toBe(200);
    expect(responseGet2.body.ops[0].proc).toEqual('ok');

    const responseCompile = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPILE,
        obj: OBJ_TYPE.GIT_CALL,
        company_id: companyId,

        conv_id: newConv,
        obj_type: 'function',
        node_id: process_node_ID,
        version: 2,
        lang: `js`,
        code: `module.exports = (data) => {data.a = 1234; return data;};`,
        repo: ``,
        commit: ``,
        script: ``,
      }),
    );
    expect(responseCompile.status).toBe(200);
    expect(responseCompile.body.ops[0].proc).toEqual('ok');

    const responseCommit = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CONFIRM,
        obj: OBJ_TYPE.COMMIT,
        company_id: companyId,
        conv_id: newConv,
        version: 22,
      }),
    );
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].version).toBe(22);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        data: { s: 'new' },
        ref: faker.random.alphaNumeric(10),
        action: 'user',
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 25000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id: companyId,
        conv_id: newConv,
        ref_or_obj_id: task_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].proc).toEqual('ok');
    expect(responseShow.body.ops[0].data).toEqual({ a: 1234, s: 'new' });
  });

  describe.each([
    data.js,
    data.python,
    data.php,
    data.java,
    data.clojure,
    data.prolog,
    data.golang,
    data.golang_path,
    data.dockerfile_path,
    data.dockerfile_repo,
    data.js_async,
    data.js_crypto,
    data.java_repo,
    data.golang_repo,
    data.php_repo,
    data.python_repo,
    data.python_repo_path,
    data.prolog_repo,
    data.prolog_build_command,
  ])('Git_call', config => {
    test(`Check git_call with lang '${config.name}'`, async () => {
      const responseNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.NODE,
          company_id: companyId,
          obj_type: 0,
          description: 'logic',
          conv_id: newConv,
          title: `Node_Logic`,
          version: 22,
        }),
      );
      nodeLogic = responseNode.body.ops[0].obj_id;

      const responseModifyStart = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: start_node_ID,
          company_id: companyId,

          conv_id: newConv,
          obj_type: 1,
          version: 22,
          logics: [
            {
              type: 'go',
              to_node_id: nodeLogic,
            },
          ],
          semaphors: [],
          options: null,
        }),
      );
      expect(responseModifyStart.status).toBe(200);

      const responseModify = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: nodeLogic,
          company_id: companyId,

          conv_id: newConv,
          obj_type: 0,
          title: 'git-call',
          description: 'git-call',
          version: 22,
          logics: [
            {
              type: 'git_call',
              version: 2,
              lang: `${config.lang}`,
              err_node_id: newNodeFinal,
              code: `${config.code}`,
              repo: `${config.repo}`,
              path: `${config.path}`,
              commit: `${config.commit}`,
              script: `${config.script}`,
            },
            {
              type: 'go',
              to_node_id: final_node_ID,
            },
          ],
          semaphors: [
            {
              type: 'time',
              value: 600,
              dimension: 'min',
              to_node_id: newNodeFinal,
            },
          ],
        }),
      );
      expect(responseModify.status).toBe(200);

      const message = {
        ops: [
          {
            type: 'monitor_show',
            obj: 'git_call',
            obj_type: 'function_build',
            conv_id: `${newConv}`,
            obj_id: faker.random.alphaNumeric(10),
            user_id: user_id,
            lang: `${config.lang}`,
            node_id: `${nodeLogic}`,
            code: `${config.code}`,
            repo: `${config.repo}`,
            commit: `${config.commit}`,
            path: `${config.path}`,
            script: `${config.script}`,
            status: 'on',
          },
        ],
      };

      await wsClient.sendMessage(message);

      const responseCompile = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPILE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id: companyId,

          conv_id: newConv,
          obj_type: 'function',
          node_id: nodeLogic,
          version: 2,
          lang: `${config.lang}`,
          code: `${config.code}`,
          repo: `${config.repo}`,
          path: `${config.path}`,
          commit: `${config.commit}`,
          script: `${config.script}`,
        }),
      );
      expect(responseCompile.status).toBe(200);
      expect(responseCompile.body.ops[0].obj_type).toEqual('function');

      const responseCommit = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CONFIRM,
          obj: OBJ_TYPE.COMMIT,
          company_id: companyId,
          conv_id: newConv,
          version: 22,
        }),
      );
      expect(responseCommit.status).toBe(200);
      expect(responseCommit.body.ops[0].version).toBe(22);

      const responseTask = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          company_id: companyId,
          conv_id: newConv,
          data: { s: 'new' },
          ref: faker.random.alphaNumeric(10),
          action: 'user',
        }),
      );
      expect(responseTask.status).toBe(200);
      expect(responseTask.body.ops[0].proc).toEqual('ok');
      expect(responseTask.body.ops[0].obj).toEqual('task');
      task_id = responseTask.body.ops[0].obj_id;

      await new Promise(r => setTimeout(r, 28000));

      const responseShow = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.TASK,
          company_id: companyId,
          conv_id: newConv,
          ref_or_obj_id: task_id,
        }),
      );
      expect(responseShow.status).toBe(200);
      expect(responseShow.body.ops[0].proc).toEqual('ok');
      expect(responseShow.body.ops[0].data).toEqual(config.response);
    });
  });

  describe.each([
    data.js_async_negative,
    data.js_async_max_size,
    data.js_error_reject,
    data.js_error_throw,
    data.clojure_error_throw_repo,
    data.js_timeout,
    data.js_network_policy,
  ])('Git_call', config => {
    test(`Check git_call with lang '${config.name}'`, async () => {
      const responseNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.NODE,
          company_id: companyId,
          obj_type: 0,
          description: 'logic',
          conv_id: newConv,
          title: `Node_Logic`,
          version: 22,
        }),
      );
      nodeLogic = responseNode.body.ops[0].obj_id;

      const responseModifyStart = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: start_node_ID,
          company_id: companyId,

          conv_id: newConv,
          obj_type: 1,
          version: 22,
          logics: [
            {
              type: 'go',
              to_node_id: nodeLogic,
            },
          ],
          semaphors: [],
          options: null,
        }),
      );
      expect(responseModifyStart.status).toBe(200);

      const responseModify = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: nodeLogic,
          company_id: companyId,

          conv_id: newConv,
          obj_type: 0,
          title: 'git-call',
          description: 'git-call',
          version: 22,
          logics: [
            {
              type: 'git_call',
              version: 2,
              lang: `${config.lang}`,
              err_node_id: newNodeFinal,
              code: `${config.code}`,
              repo: `${config.repo}`,
              commit: `${config.commit}`,
              script: `${config.script}`,
            },
            {
              type: 'go',
              to_node_id: final_node_ID,
            },
          ],
          semaphors: [
            {
              type: 'time',
              value: 600,
              dimension: 'min',
              to_node_id: newNodeFinal,
            },
          ],
        }),
      );
      expect(responseModify.status).toBe(200);

      const message = {
        ops: [
          {
            type: 'monitor_show',
            obj: 'git_call',
            obj_type: 'function_build',
            conv_id: `${newConv}`,
            obj_id: faker.random.alphaNumeric(10),
            user_id: user_id,
            lang: `${config.lang}`,
            node_id: `${nodeLogic}`,
            code: `${config.code}`,
            repo: `${config.repo}`,
            commit: `${config.commit}`,
            script: `${config.script}`,
            status: 'on',
          },
        ],
      };

      await wsClient.sendMessage(message);

      const responseCompile = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPILE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id: companyId,

          conv_id: newConv,
          obj_type: 'function',
          node_id: nodeLogic,
          version: 2,
          lang: `${config.lang}`,
          code: `${config.code}`,
          repo: `${config.repo}`,
          commit: `${config.commit}`,
          script: `${config.script}`,
        }),
      );
      expect(responseCompile.status).toBe(200);
      expect(responseCompile.body.ops[0].obj_type).toEqual('function');

      const responseCommit = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CONFIRM,
          obj: OBJ_TYPE.COMMIT,
          company_id: companyId,
          conv_id: newConv,
          version: 22,
        }),
      );
      expect(responseCommit.status).toBe(200);
      expect(responseCommit.body.ops[0].version).toBe(22);

      const responseTask = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          company_id: companyId,
          conv_id: newConv,
          data: { s: 'new' },
          ref: faker.random.alphaNumeric(10),
          action: 'user',
        }),
      );
      expect(responseTask.status).toBe(200);
      expect(responseTask.body.ops[0].proc).toEqual('ok');
      expect(responseTask.body.ops[0].obj).toEqual('task');
      task_id = responseTask.body.ops[0].obj_id;

      await new Promise(r => setTimeout(r, 45000));
      await new Promise(r => setTimeout(r, 45000));

      const responseShow = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.TASK,
          company_id: companyId,
          conv_id: newConv,
          ref_or_obj_id: task_id,
        }),
      );
      expect(responseShow.status).toBe(200);
      expect(responseShow.body.ops[0].proc).toEqual('ok');
      expect(responseShow.body.ops[0].data).toEqual(config.response);
    }, 300000);
  });

  describe.each([
    data.js_moment,
    data.js_repo,
    data.js_moment_timezone,
    data.python_crypto,
    data.php_http,
    data.clojure_build_command,
    data.java_http,
    data.python_tmp,
    data.dockerfile_code,
    data.js_esm_path_import,
    data.js_esm_path_module,
    data.js_ESM,
  ])('Git_call', config => {
    test(`Check git_call with lang '${config.name}'`, async () => {
      const responseNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.NODE,
          company_id: companyId,
          obj_type: 0,
          description: 'logic',
          conv_id: newConv,
          title: `Node_Logic`,
          version: 22,
        }),
      );
      nodeLogic = responseNode.body.ops[0].obj_id;

      const responseModifyStart = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: start_node_ID,
          company_id: companyId,

          conv_id: newConv,
          obj_type: 1,
          version: 22,
          logics: [
            {
              type: 'go',
              to_node_id: nodeLogic,
            },
          ],
          semaphors: [],
          options: null,
        }),
      );
      expect(responseModifyStart.status).toBe(200);

      const responseModify = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: nodeLogic,
          company_id: companyId,
          conv_id: newConv,
          obj_type: 0,
          title: 'git-call',
          description: 'git-call',
          version: 22,
          logics: [
            {
              type: 'git_call',
              version: 2,
              lang: `${config.lang}`,
              err_node_id: newNodeFinal,
              code: `${config.code}`,
              repo: `${config.repo}`,
              commit: `${config.commit}`,
              script: `${config.script}`,
              path: `${config.path}`,
            },
            {
              type: 'go',
              to_node_id: final_node_ID,
            },
          ],
          semaphors: [
            {
              type: 'time',
              value: 600,
              dimension: 'min',
              to_node_id: newNodeFinal,
            },
          ],
        }),
      );
      expect(responseModify.status).toBe(200);

      const message = {
        ops: [
          {
            type: 'monitor_show',
            obj: 'git_call',
            obj_type: 'function_build',
            conv_id: `${newConv}`,
            obj_id: faker.random.alphaNumeric(10),
            user_id: user_id,
            lang: `${config.lang}`,
            node_id: `${nodeLogic}`,
            code: `${config.code}`,
            repo: `${config.repo}`,
            commit: `${config.commit}`,
            script: `${config.script}`,
            path: `${config.path}`,
            status: 'on',
          },
        ],
      };

      await wsClient.sendMessage(message);

      const responseCompile = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPILE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id: companyId,
          conv_id: newConv,
          obj_type: 'function',
          node_id: nodeLogic,
          version: 2,
          lang: `${config.lang}`,
          code: `${config.code}`,
          repo: `${config.repo}`,
          commit: `${config.commit}`,
          script: `${config.script}`,
          path: `${config.path}`,
        }),
      );
      expect(responseCompile.status).toBe(200);
      expect(responseCompile.body.ops[0].obj_type).toEqual('function');

      const responseCommit = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CONFIRM,
          obj: OBJ_TYPE.COMMIT,
          company_id: companyId,
          conv_id: newConv,
          version: 22,
        }),
      );
      expect(responseCommit.status).toBe(200);
      expect(responseCommit.body.ops[0].version).toBe(22);

      await new Promise(r => setTimeout(r, 9000));
      await new Promise(r => setTimeout(r, 3000));

      const responseTask = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          company_id: companyId,
          conv_id: newConv,
          data: { s: 'new' },
          ref: faker.random.alphaNumeric(10),
          action: 'user',
        }),
      );
      expect(responseTask.status).toBe(200);
      expect(responseTask.body.ops[0].proc).toEqual('ok');
      expect(responseTask.body.ops[0].obj).toEqual('task');
      task_id = responseTask.body.ops[0].obj_id;

      await new Promise(r => setTimeout(r, 25000));

      const responseShow = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.TASK,
          company_id: companyId,
          conv_id: newConv,
          ref_or_obj_id: task_id,
        }),
      );
      expect(responseShow.status).toBe(200);
      expect(responseShow.body.ops[0].proc).toEqual('ok');
      expect(responseShow.body.ops[0].data).toHaveProperty(config.response);
    });
  });

  describe.each([data.js_ssh, data.java_ssh])('Git_call', config => {
    test(`Check git_call with lang '${config.name}'`, async () => {
      const responseModify = await cookieUserAuth.request({
        method: Method.POST,
        url: `${origin}api/2/json`,
        data: {
          ops: [
            {
              type: 'modify',
              obj: 'node',
              obj_id: process_node_ID1,
              company_id: companyId,
              conv_id: newConv1,
              obj_type: 0,
              title: 'git-call',
              description: 'git-call',
              version: 22,
              logics: [
                {
                  type: 'git_call',
                  version: 2,
                  lang: `${config.lang}`,
                  err_node_id: final_node_ID1,
                  code: `${config.code}`,
                  repo: `${config.repo}`,
                  commit: `${config.commit}`,
                  script: `${config.script}`,
                },
                {
                  type: 'go',
                  to_node_id: final_node_ID1,
                },
              ],
              semaphors: [
                {
                  type: 'time',
                  value: 600,
                  dimension: 'min',
                  to_node_id: final_node_ID1,
                },
              ],
            },
          ],
        },
      });
      expect(responseModify.status).toBe(200);

      const responseGetSsh = await cookieUserAuth.request({
        method: Method.POST,
        url: `${origin}api/2/json`,
        data: {
          ops: [
            {
              type: 'get',
              obj: 'git_call',
              node_id: process_node_ID1,
              company_id: companyId,
              conv_id: newConv1,
              obj_type: 'ssh_key',
              version: 2,
            },
          ],
        },
      });

      expect(responseGetSsh.status).toBe(200);

      const message = {
        ops: [
          {
            type: 'monitor_show',
            obj: 'git_call',
            obj_type: 'function_build',
            conv_id: `${newConv1}`,
            obj_id: faker.random.alphaNumeric(10),
            user_id: user_id,
            lang: `${config.lang}`,
            node_id: `${process_node_ID1}`,
            code: `${config.code}`,
            repo: `${config.repo}`,
            commit: `${config.commit}`,
            script: `${config.script}`,
            status: 'on',
          },
        ],
      };

      await wsClient.sendMessage(message);

      const responseCompile = await cookieUserAuth.request({
        method: Method.POST,
        url: `${origin}api/2/json`,
        data: {
          ops: [
            {
              type: 'compile',
              obj: 'git_call',
              company_id: companyId,
              conv_id: newConv1,
              obj_type: 'function',
              node_id: process_node_ID1,
              version: 2,
              lang: `${config.lang}`,
              code: `${config.code}`,
              repo: `${config.repo}`,
              commit: `${config.commit}`,
              script: `${config.script}`,
            },
          ],
        },
      });

      expect(responseCompile.status).toBe(200);
      expect(responseCompile.data.ops[0].obj_type).toEqual('function');

      const responseCommit = await cookieUserAuth.request({
        method: Method.POST,
        url: `${origin}api/2/json`,
        data: {
          ops: [{ type: 'confirm', obj: 'commit', company_id: companyId, conv_id: newConv1, version: 22 }],
        },
      });

      expect(responseCommit.status).toBe(200);
      expect(responseCommit.data.ops[0].version).toBe(22);

      await new Promise(r => setTimeout(r, 3000));

      const responseTask = await cookieUserAuth.request({
        method: Method.POST,
        url: `${origin}api/2/json`,
        data: {
          ops: [
            {
              type: 'create',
              obj: 'task',
              company_id: companyId,
              conv_id: newConv1,
              data: { s: 'new' },
              ref: faker.random.alphaNumeric(10),
              action: 'user',
            },
          ],
        },
      });

      expect(responseTask.status).toBe(200);
      expect(responseTask.data.ops[0].proc).toEqual('ok');
      expect(responseTask.data.ops[0].obj).toEqual('task');
      task_id = responseTask.data.ops[0].obj_id;

      await new Promise(r => setTimeout(r, 25000));

      const responseShow = await cookieUserAuth.request({
        method: Method.POST,
        url: `${origin}api/2/json`,
        data: {
          ops: [{ type: 'show', obj: 'task', company_id: companyId, conv_id: newConv1, ref_or_obj_id: task_id }],
        },
      });

      expect(responseShow.status).toBe(200);
      expect(responseShow.data.ops[0].proc).toEqual('ok');
      expect(responseShow.data.ops[0].data).toEqual(config.response);
    }, 300000);
  });

  describe.each([data.lisp])('Git_call', config => {
    test(`Check git_call with lang '${config.name}' and build in the same node`, async () => {
      const responseModify = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: nodeLogic,
          company_id: companyId,

          conv_id: newConv,
          obj_type: 0,
          title: 'git-call',
          description: 'git-call',
          version: 22,
          logics: [
            {
              type: 'git_call',
              version: 2,
              lang: `${config.lang}`,
              err_node_id: newNodeFinal,
              code: `${config.code}`,
              repo: `${config.repo}`,
              commit: `${config.commit}`,
              script: `${config.script}`,
            },
            {
              type: 'go',
              to_node_id: final_node_ID,
            },
          ],
          semaphors: [
            {
              type: 'time',
              value: 600,
              dimension: 'min',
              to_node_id: newNodeFinal,
            },
          ],
        }),
      );
      expect(responseModify.status).toBe(200);

      const message = {
        ops: [
          {
            type: 'monitor_show',
            obj: 'git_call',
            obj_type: 'function_build',
            conv_id: `${newConv}`,
            obj_id: faker.random.alphaNumeric(10),
            user_id: user_id,
            lang: `${config.lang}`,
            node_id: `${nodeLogic}`,
            code: `${config.code}`,
            repo: `${config.repo}`,
            commit: `${config.commit}`,
            script: `${config.script}`,
            status: 'on',
          },
        ],
      };

      await wsClient.sendMessage(message);

      await new Promise(r => setTimeout(r, 25000));
      await new Promise(r => setTimeout(r, 10000));

      const responseCompile = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.COMPILE,
          obj: OBJ_TYPE.GIT_CALL,
          company_id: companyId,

          conv_id: newConv,
          obj_type: 'function',
          node_id: nodeLogic,
          version: 2,
          lang: `${config.lang}`,
          code: `${config.code}`,
          repo: `${config.repo}`,
          commit: `${config.commit}`,
          script: `${config.script}`,
        }),
      );
      expect(responseCompile.status).toBe(200);
      expect(responseCompile.body.ops[0].obj_type).toEqual('function');

      await new Promise(r => setTimeout(r, 25000));

      const responseCommit = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CONFIRM,
          obj: OBJ_TYPE.COMMIT,
          company_id: companyId,
          conv_id: newConv,
          version: 22,
        }),
      );
      expect(responseCommit.status).toBe(200);
      expect(responseCommit.body.ops[0].version).toBe(22);

      await new Promise(r => setTimeout(r, 25000));
      await new Promise(r => setTimeout(r, 25000));

      const responseTask = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          company_id: companyId,
          conv_id: newConv,
          data: { s: 'new' },
          ref: faker.random.alphaNumeric(10),
          action: 'user',
        }),
      );
      expect(responseTask.status).toBe(200);
      expect(responseTask.body.ops[0].proc).toEqual('ok');
      expect(responseTask.body.ops[0].obj).toEqual('task');
      task_id = responseTask.body.ops[0].obj_id;

      await new Promise(r => setTimeout(r, 30000));
      await new Promise(r => setTimeout(r, 30000));

      const responseShow = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.TASK,
          company_id: companyId,
          conv_id: newConv,
          ref_or_obj_id: task_id,
        }),
      );
      expect(responseShow.status).toBe(200);
      expect(responseShow.body.ops[0].proc).toEqual('ok');
      expect(responseShow.body.ops[0].data).toEqual(config.response);
    });
  });

  afterAll(async () => {
    jest.setTimeout(300000);
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);

    if (wsClient) {
      wsClient.close();
      debug('WebSocket connection closed');
    }

    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);

    try {
      const responseDeleteConv = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, companyId);
      expect(responseDeleteConv.status).toBe(200);

      const responseDeleteConv1 = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv1, companyId);
      expect(responseDeleteConv1.status).toBe(200);
      debug('Deleted conversation');
    } catch (err) {
      error('Error in afterAll hook:', err);
    }

    if (cancel) cancel();

    await new Promise(resolve => setTimeout(resolve, 10000));
  });
});
