{"js": {"name": "js", "lang": "js", "code": "module.exports = (data) => {data.a = 1234; return data;};", "repo": "", "commit": "", "script": "", "path": "", "response": {"a": 1234, "s": "new"}}, "python": {"name": "python", "lang": "python", "code": "def handle(data):data[\"hello\"] = \"Hello world!\"; return data", "repo": "", "path": "", "commit": "", "script": "", "response": {"hello": "Hello world!", "s": "new"}}, "php": {"name": "php", "lang": "php", "code": "<?php function handle($data) {$data['hello'] = \"Hello world!\"; return $data;}", "repo": "", "path": "", "commit": "", "script": "", "response": {"hello": "Hello world!", "s": "new"}}, "java": {"name": "java", "lang": "java", "code": "package com.corezoid.usercode;import com.corezoid.gitcall.runner.api.UsercodeHandler;import java.util.Map;\npublic class Usercode implements UsercodeHandler<Map<String, String>,Map<String, String>> {@java.lang.Override public Map<String, String> handle(Map<String, String> data) throws Exception {data.put(\"hello\", \"Hello world!\");return data;}}", "repo": "", "path": "", "commit": "", "script": "", "response": {"hello": "Hello world!", "s": "new"}}, "clojure": {"name": "clojure", "lang": "clojure", "code": "(ns usercode.usercode) (defn handle [data] (assoc data :clojure \"pass\"))", "repo": "", "path": "", "commit": "", "script": "", "response": {"clojure": "pass", "s": "new"}}, "prolog": {"name": "prolog", "lang": "prolog", "code": ":- module(usercode, [handle/2]). handle(Data, Result) :- put_dict(prolog, Data, \"pass!\", Result).", "repo": "", "path": "", "commit": "", "script": "", "response": {"prolog": "pass!", "s": "new"}}, "golang": {"name": "golang", "lang": "golang", "code": "package main\nimport (\n\t\"context\"\n\t\"github.com/corezoid/gitcall-go-runner/gitcall\")\nfunc usercode(_ context.Context, data map[string]interface{}) error {data[\"hello\"] = \"Hello world!\"\n\treturn nil}\nfunc main() {gitcall.Handle(usercode)}", "repo": "", "commit": "", "path": "", "script": "", "response": {"hello": "Hello world!", "s": "new"}}, "js_ESM": {"name": "js_ESM", "lang": "js", "code": "import fetch from 'node-fetch';export default async (data) => { const response = await fetch('https://jsonplaceholder.typicode.com/posts/1'); data.result = await response.json(); return data;};", "repo": "", "commit": "", "script": "npm install node-fetch", "path": "", "response": "result"}, "js_async": {"name": "js_async", "lang": "js", "code": "module.exports = async (data) => {await new Promise(r => setTimeout(r, 500));data.hello = \"Hello World!\";return data;};", "repo": "", "path": "", "commit": "", "script": "", "response": {"hello": "Hello World!", "s": "new"}}, "js_crypto": {"name": "js_crypto", "lang": "js", "code": "module.exports = (data) => {var sha1 = require(\"crypto-js/sha1\");data.res = sha1(data.in); return data;};", "repo": "", "path": "", "commit": "", "script": "npm install axios crypto-js", "response": {"res": {"sigBytes": 20, "words": [-633756690, 1584089869, 844480495, -1788864368, -1344796919]}, "s": "new"}}, "java_repo": {"name": "java_repo", "lang": "java", "code": "", "repo": "https://github.com/juliyakomeristaya/testgit_calljava.git", "commit": "master", "path": "", "script": "", "response": {"hello": "Hello world", "s": "new"}}, "golang_repo": {"name": "golang_repo", "lang": "golang", "code": "", "repo": "https://github.com/juliyakomeristaya/testgit_calljava.git", "commit": "master", "path": "", "script": "", "response": {"hello": "Go:Hello world", "s": "new"}}, "golang_path": {"name": "golang_path", "lang": "golang", "code": "", "repo": "https://github.com/corezoid/gitcall-examples", "path": "go/hello_world", "commit": "master", "script": "", "response": {"hello": "Hello world!", "s": "new"}}, "js_esm_path_module": {"name": "js_esm_path_module", "lang": "js", "code": "", "repo": "https://github.com/corezoid/gitcall-examples", "path": "js/esm_module", "commit": "master", "script": "", "response": "random"}, "js_esm_path_import": {"name": "js_esm_path_import", "lang": "js", "code": "", "repo": "https://github.com/corezoid/gitcall-examples", "path": "js/esm_import", "commit": "master", "script": "", "response": "random"}, "python_repo": {"name": "python_repo", "lang": "python", "code": "", "repo": "https://github.com/juliyakomeristaya/testgit_calljava.git", "commit": "master", "script": "", "path": "", "response": {"hello": "Py:Hello world", "s": "new"}}, "python_repo_path": {"name": "python_repo_path", "lang": "python", "code": "", "repo": "https://github.com/juliyakomeristaya/testgit_calljava", "commit": "4c050e915d71af37efd9d0b8f7fe266a904305bf", "script": "", "path": "python", "response": {"hello": "Hello world!", "s": "new"}}, "php_repo": {"name": "php_repo", "lang": "php", "code": "", "repo": "https://github.com/juliyakomeristaya/testgit_calljava.git", "commit": "master", "path": "", "script": "", "response": {"hello": "Php:Hello world", "s": "new"}}, "prolog_repo": {"name": "prolog_repo", "lang": "prolog", "code": "", "repo": "https://github.com/juliyakomeristaya/testgit_calljava.git", "commit": "master", "path": "", "script": "", "response": {"hello": "Prolog:Hello world", "s": "new"}}, "js_async_negative": {"name": "js_async_negative", "lang": "js", "code": "module.exports = async (data) => {await new Promise(r => setTimeout(r, 65000));data.hello = \"Hello World!\";return data;};", "repo": "", "path": "", "commit": "", "script": "", "response": {"__conveyor_git_call_return_description__": "usercode: timeout", "__conveyor_git_call_return_type_error__": "software", "__conveyor_git_call_return_type_tag__": "git_call_executing_error", "s": "new"}}, "js_async_max_size": {"name": "js_async_max_size", "lang": "js", "code": "module.exports = (data) => {data.bigString ='a'.repeat(2097152);return data;};", "repo": "", "path": "", "commit": "", "script": "", "response": {"__conveyor_git_call_return_description__": "usercode: result reached maximum size: 2097152 bytes", "__conveyor_git_call_return_type_error__": "software", "__conveyor_git_call_return_type_tag__": "git_call_executing_error", "s": "new"}}, "js_error_reject": {"name": "js_error_reject", "lang": "js", "code": "module.exports = (data) => {return new Promise((resolve, reject) => {reject(\"My custom user error in reject\");});};", "repo": "", "path": "", "commit": "", "script": "", "response": {"__conveyor_git_call_return_description__": "usercode: response has error: code: 1, message: My custom user error in reject", "__conveyor_git_call_return_type_error__": "software", "__conveyor_git_call_return_type_tag__": "git_call_executing_error", "s": "new"}}, "js_error_throw": {"name": "js__error_throw", "lang": "js", "code": "module.exports = (data) => {throw \"My custom user error\"};", "repo": "", "path": "", "commit": "", "script": "", "response": {"__conveyor_git_call_return_description__": "usercode: response has error: code: 1, message: My custom user error", "__conveyor_git_call_return_type_error__": "software", "__conveyor_git_call_return_type_tag__": "git_call_executing_error", "s": "new"}}, "clojure_error_throw_repo": {"name": "clojure_error_throw_repo", "lang": "clojure", "code": "", "repo": "https://github.com/juliyakomeristaya/clojure.git", "commit": "main", "script": "", "path": "", "response": {"__conveyor_git_call_return_description__": "usercode: response has error: code: 1, message: my custom error", "__conveyor_git_call_return_type_error__": "software", "__conveyor_git_call_return_type_tag__": "git_call_executing_error", "s": "new"}}, "js_moment": {"name": "js_moment", "lang": "js", "code": "module.exports = (data) => {const moment = require('moment');data.now = moment().format('MMMM Do YYYY, h:mm:ss a'); return data;};", "repo": "", "commit": "", "path": "", "script": "npm install axios moment", "response": "now"}, "js_repo": {"name": "js_repo", "lang": "js", "code": "module.exports = (data) => {const { v4 : uuidv4 } = require('./uuid.js'); data.uuid1 = uuidv4(); return data;};", "repo": "https://github.com/uuidjs/uuid.git", "commit": "3f44acd0e722e965c14af816e2f658361a6b15f9", "script": "", "path": "", "response": "uuid1"}, "js_ssh": {"name": "js_ssh", "lang": "js", "code": "", "repo": "**************:juliyakomeristaya/private_gitcall.git", "commit": "master", "path": "", "script": "", "response": {"hello": "Hello World", "s": "new"}}, "java_ssh": {"name": "java_ssh", "lang": "java", "code": "", "repo": "ssh://**************/juliyakomeristaya/testgit_calljava.git", "commit": "da4697d3973d2a31745c4d89fcff01e0e00b8b1d", "script": "", "path": "", "response": {"hello": "Hello world", "s": "new"}}, "lisp": {"name": "lisp", "lang": "lisp", "code": "(defpackage #:usercode (:use #:cl) (:export :handle)) (in-package #:usercode) (defun handle (data) (setf (gethash 'lisp data) 'pass) data)", "repo": "", "commit": "", "script": "", "path": "", "response": {"LISP": "PASS", "s": "new"}}, "js_moment_timezone": {"name": "js_moment_timezone_with_package_json", "lang": "js", "code": "", "repo": "https://github.com/juliyakomeristaya/testgit_calljava.git", "commit": "master", "script": "", "path": "", "response": "ukraine_time"}, "python_crypto": {"name": "python_crypto_with_requirements_txt", "lang": "python", "code": "", "repo": "https://github.com/juliyakomeristaya/test_repo_git_call.git", "commit": "main", "script": "", "path": "", "response": "token"}, "php_http": {"name": "php_http_with_composer_json", "lang": "php", "code": "", "repo": "https://github.com/juliyakomeristaya/test_repo_git_call.git", "commit": "main", "script": "", "path": "", "response": "res"}, "java_http": {"name": "java_http_repo+commit+build_command", "lang": "java", "code": "", "repo": "https://github.com/juliyakomeristaya/git_call_java_https.git", "commit": "main", "script": "chmod +x ./gradlew && ./gradlew build", "path": "", "response": "res"}, "prolog_build_command": {"name": "prolog_code+build_command", "lang": "prolog", "code": ":- module(usercode, [\n    handle/2\n]).\n:- use_module(library(matrix)).\n        \nhandle(Data, Result) :-\n    determinant([[2,-1,0],[-1,2,-1],[0,-1,2]],M),\n    put_dict(matrix, Data, M, Result).", "repo": "", "path": "", "commit": "", "script": "swipl -g \"pack_install(matrix, [interactive(false)]).\"", "response": {"matrix": 3.999999999999999, "s": "new"}}, "clojure_build_command": {"name": "clojure_code+build_command", "lang": "clojure", "code": "(ns usercode.usercode\n    (:require [clj-uuid :as uuid]))\n(defn handle [data]\n    (assoc data :uuid (uuid/v1)))", "repo": "", "commit": "", "path": "", "script": "lein change :dependencies conj '[dan<PERSON>z/clj-uuid \"0.1.9\"]' && lein install", "response": "uuid"}, "js_network_policy": {"name": "js_network_policy", "lang": "js", "code": "const url = 'http://apigw-apigw-app-service.apigw-pre.svc.cluster.local:8050/metrics';\n\nmodule.exports = (data) => {\nreturn new Promise((resolve, reject) => {\nfetch(url)\n.then(async (r) => {\ndata.metrics = await r.text(); \nresolve(data);\n})\n.catch(() => reject(\"OK! can't fetch metrics\"));\n});\n};", "repo": "", "commit": "", "script": "", "path": "", "response": {"__conveyor_git_call_return_description__": "usercode: response has error: code: 1, message: OK! can't fetch metrics", "__conveyor_git_call_return_type_error__": "software", "__conveyor_git_call_return_type_tag__": "git_call_executing_error", "s": "new"}}, "js_timeout": {"name": "js_timeout", "lang": "js", "code": "module.exports = (data) => {\nreturn new Promise(async (resolve, reject) => {\np1 = Promise.reject(new Error(\"Rejected!\")); \n const [res1] = await Promise.all([p1]);\n resolve(data);\n});\n};", "repo": "", "commit": "", "script": "", "path": "", "response": {"__conveyor_git_call_return_description__": "usercode: timeout", "__conveyor_git_call_return_type_error__": "software", "__conveyor_git_call_return_type_tag__": "git_call_executing_error", "s": "new"}}, "dockerfile_code": {"name": "dockerfile_code", "lang": "dockerfile", "code": "FROM node:22-alpine as builder\nR<PERSON> apk add git\nWORKDIR /app\nRUN git clone https://github.com/corezoid/gitcall-examples .\n\nWORKDIR /app/dockerfile/js\n\nRUN addgroup --gid 501 usercode && \\\n    adduser --disabled-password \\\n    --gecos \"\" \\\n    --shell /usr/sbin/nologin \\\n    --ingroup usercode \\\n    --uid 501 \\\n    usercode\nUSER usercode\n\nENTRYPOINT [\"node\", \"/app/dockerfile/js/src/main.js\"]", "repo": "", "commit": "", "path": "", "script": "", "response": "type"}, "dockerfile_repo": {"name": "dockerfile_repo", "lang": "dockerfile", "code": "", "repo": "https://github.com/juliyakomeristaya/php_dockerfile", "commit": "main", "path": "", "script": "", "response": {"php": "Hello, world!", "s": "new"}}, "dockerfile_path": {"name": "dockerfile_path", "lang": "dockerfile", "code": "", "repo": "https://github.com/juliyakomeristaya/testgit_calljava", "commit": "master", "path": "dockerfile/js", "script": "", "response": {"js": "Hello, world!", "s": "new"}}, "python_tmp": {"name": "python_tmp", "lang": "python", "code": "import tempfile\nfrom pathlib import Path\n\ndef handle(data):\n\ttemp_dir = Path(\"/tmp\")\n\tfile_name = temp_dir / \"test.txt\"\n\tfile_name.write_text(\"bla bla bla\")\n\n\tdata[\"tmp\"] = tempfile.TemporaryDirectory().name\n\tdata[\"file\"] = file_name.read_text()\n\tdata[\"lang\"] = \"python\"\n\treturn data", "repo": "", "path": "", "commit": "", "script": "", "response": "file"}}