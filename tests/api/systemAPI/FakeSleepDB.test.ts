import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';
import { User } from '../../../infrastructure/model/User';
import { application } from '../../../application/Application';
import { ApiUserClient } from '../../../application/api/ApiUserClient';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import { createAuthUser, Method } from '../../../utils/request';
import { requestConfirm, requestDeleteObj } from '../../../application/api/ApiObj';

describe('Upload_info (positive)', () => {
  let apikey: ApiKey;
  let user: User;
  let apiUserCookie: ApiUserClient;
  let userCookie: any;
  let company_id: any;
  let conv_id: string | number;
  let newProject: string | number;
  let newStage: string | number;
  let task_id: string | number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let expectedError: string;
  let errorCount: number;
  let randomIterations: number;
  const baseUrl = ConfigurationManager.getConfiguration().getUrl();

  beforeAll(async () => {
    apikey = await application.getApiKey();
    company_id = apikey.companies[0].id;

    user = await application.getAuthorizedUser({ company: {} }, 7);
    apiUserCookie = await application.getApiUserClient(user);
    userCookie = createAuthUser(user.cookieUser, 'cookie');
    expectedError = 'timeout of query execution';

    const response = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: `Project_${Date.now()}`,
        short_name: `project${Date.now()}`,
        description: 'test',
        stages: [{ title: 'develop', immutable: false }],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;
    newStage = response.body.ops[0].stages[0];

    const responseConv = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `Conv_${Date.now()}`,
        conv_type: 'process',
        stage_id: newStage,
        project_id: newProject,
      }),
    );
    conv_id = responseConv.body.ops[0].obj_id;

    const responseList = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
      }),
    );
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
  });

  test('shouldn`t send task after active fake sleep db mode', async () => {
    const responseTask = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: {},
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 1000));
    const responseShow = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id,
        obj_id: task_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].obj_id).toEqual(task_id);
    expect(responseShow.body.ops[0].node_id).toEqual(final_node_ID);

    randomIterations = ~~(Math.random() * 11) + 5;
    const sleepActiove = await userCookie.request({
      method: Method.GET,
      url: `${baseUrl}system/tests/fake_sleep_db/300/${randomIterations}/update`,
    });
    expect(sleepActiove.status).toBe(200);

    errorCount = 0;
    for (let i = 1; i <= randomIterations * 2 + 1; i++) {
      await new Promise(r => setTimeout(r, 200));

      const responseModifySetParam = await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id: conv_id,
          title: `process+${i}`,
          obj_type: 0,
          logics: [
            {
              type: 'set_param',
              extra: { key: '{{object}}' },
              extra_type: { key: 'object' },
              err_node_id: '',
            },
            { to_node_id: final_node_ID, type: 'go', node_title: 'final' },
          ],
          version: 22,
        }),
      );
      expect(responseModifySetParam.status).toBe(RESP_STATUS.OK);
      expect(responseModifySetParam.body.ops[0].proc).toBe(PROC_STATUS.OK);

      const responseCommit = await requestConfirm(apiUserCookie, conv_id, company_id);
      expect(responseCommit.status).toBe(RESP_STATUS.OK);
      const { proc, description } = responseCommit.body.ops[0];
      expect([PROC_STATUS.OK, PROC_STATUS.ERROR]).toContain(proc);
      if (proc === PROC_STATUS.ERROR) {
        errorCount++;
        expect(description).toEqual(expectedError);
      }
    }

    await new Promise(r => setTimeout(r, 1000));

    const sleepDeactiove = await userCookie.request({
      method: Method.GET,
      url: `${baseUrl}system/tests/fake_sleep_db/false`,
    });
    expect(sleepDeactiove.status).toBe(200);
    expect(errorCount).toBeGreaterThan(0);
  });

  afterAll(async () => {
    const response = await requestDeleteObj(apiUserCookie, OBJ_TYPE.PROJECT, newProject, company_id);
    expect(response.status).toBe(200);

    const sleepDeactiove = await userCookie.request({
      method: Method.GET,
      url: `${baseUrl}system/tests/fake_sleep_db/false`,
    });
    expect(sleepDeactiove.status).toBe(200);
  });
});
