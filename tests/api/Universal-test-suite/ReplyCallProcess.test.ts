import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
} from '../../../application/api/ApiObj';

describe('ReplyCallProcess', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;

  let company_id: any;
  let conv_idReply: number;
  let processReply_node_ID: string | number;
  let finalReply_node_ID: string | number;
  let conv_idCall: number;
  let processCall_node_ID: string | number;
  let finalCall_node_ID: string | number;
  let task_id1: string | number;
  let task_id2: string | number;
  let task_id3: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);

    company_id = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Reply`);
    conv_idReply = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_idReply, company_id);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    processReply_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    finalReply_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateQueue = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: processReply_node_ID,
        conv_id: conv_idReply,
        title: 'process',
        obj_type: 0,
        logics: [
          {
            type: 'api_rpc_reply',
            mode: 'key_value',
            throw_exception: false,
            exception_reason: '',
            err_node_id: '',
            res_data: { Id: '7777777', ekbId: '5555555' },
            res_data_type: { Id: 'string', ekbId: 'string' },
          },
          { to_node_id: finalReply_node_ID, type: 'go', node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateQueue.status).toBe(RESP_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_idReply, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);

    const responseGet = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Call Process`);
    conv_idCall = responseGet.body.ops[0].obj_id;

    const responseListGet = await requestListConv(api, conv_idCall, company_id);
    expect(responseListGet.status).toBe(RESP_STATUS.OK);
    processCall_node_ID = (responseListGet.body.ops[0].list as Array<any>).find(item => item.title === 'process')
      .obj_id;
    finalCall_node_ID = (responseListGet.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateQueueGet = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: processCall_node_ID,
        conv_id: conv_idCall,
        title: 'Call',
        obj_type: 0,
        logics: [
          {
            type: 'api_rpc',
            err_node_id: '',
            extra: { ekbId: '{{ekbId}}', Id: '{{Id}}' },
            extra_type: { ekbId: 'string', Id: 'string' },
            conv_id: conv_idReply,
          },
          { to_node_id: finalCall_node_ID, type: 'go', node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateQueueGet.status).toBe(RESP_STATUS.OK);

    const responseCommitGet = await requestConfirm(api, conv_idCall, company_id);
    expect(responseCommitGet.status).toBe(RESP_STATUS.OK);
  });

  test('should create task Call Process (Reply mode:key_value)', async () => {
    const responseTask1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_idCall,
        data: { ekbId: 12345, Id: '54321' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask1.status).toBe(RESP_STATUS.OK);
    expect(responseTask1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask1.body.ops[0].obj).toEqual('task');
    task_id1 = responseTask1.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_idCall,
        ref_or_obj_id: task_id1,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].data).toEqual({ Id: '7777777', ekbId: '5555555' });

    const responseList = await requestList(api, finalCall_node_ID, conv_idCall, company_id, 10);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(1);
    expect(responseList.body.ops[0].list[0].data).toEqual({ Id: '7777777', ekbId: '5555555' });

    const responseListReply = await requestList(api, finalReply_node_ID, conv_idReply, company_id, 10);
    expect(responseListReply.status).toBe(RESP_STATUS.OK);
    expect(responseListReply.body.ops[0].count).toEqual(1);
    expect(responseListReply.body.ops[0].list[0].data.ekbId).toEqual('12345');
    expect(responseListReply.body.ops[0].list[0].data.Id).toEqual('54321');
    expect(responseListReply.body.ops[0].list[0].data.__rpc_conv_id__).toEqual(conv_idCall);
  });

  test('should create task Call Process (Reply Throw exception)', async () => {
    const responseCreateQueue = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: processReply_node_ID,
        conv_id: conv_idReply,
        title: 'process',
        obj_type: 0,
        logics: [
          {
            type: 'api_rpc_reply',
            mode: 'key_value',
            throw_exception: true,
            exception_reason: 'test_error',
            err_node_id: '',
            res_data: { Id: '7777777', ekbId: '5555555' },
            res_data_type: { Id: 'string', ekbId: 'string' },
          },
          { to_node_id: finalReply_node_ID, type: 'go', node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateQueue.status).toBe(RESP_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_idReply, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);

    const responseTask1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_idCall,
        data: { ekbId: 12345, Id: '54321' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask1.status).toBe(RESP_STATUS.OK);
    expect(responseTask1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask1.body.ops[0].obj).toEqual('task');
    task_id2 = responseTask1.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_idCall,
        ref_or_obj_id: task_id2,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].data.Id).toEqual('7777777');
    expect(responseShow.body.ops[0].data.ekbId).toEqual('5555555');
    expect(responseShow.body.ops[0].data.__conveyor_rpc_reply_return_type_tag__).toEqual('rpc_reply_exception');
    expect(responseShow.body.ops[0].data.__conveyor_rpc_reply_return_description__).toEqual('test_error');

    const responseList = await requestList(api, finalCall_node_ID, conv_idCall, company_id, 10);
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].count).toEqual(2);

    const responseListReply = await requestList(api, finalReply_node_ID, conv_idReply, company_id, 10);
    expect(responseListReply.status).toBe(RESP_STATUS.OK);
    expect(responseListReply.body.ops[0].count).toEqual(2);
    expect(responseListReply.body.ops[0].list[0].data.ekbId).toEqual('12345');
    expect(responseListReply.body.ops[0].list[0].data.Id).toEqual('54321');
    expect(responseListReply.body.ops[0].list[0].data.__rpc_conv_id__).toEqual(conv_idCall);
    expect(responseListReply.body.ops[0].list[1].data.ekbId).toEqual('12345');
    expect(responseListReply.body.ops[0].list[1].data.Id).toEqual('54321');
    expect(responseListReply.body.ops[0].list[1].data.__rpc_conv_id__).toEqual(conv_idCall);
  });

  test('should create task Call Process (Reply mode: keys)', async () => {
    const responseCreateQueue = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: processReply_node_ID,
        conv_id: conv_idReply,
        title: 'process',
        obj_type: 0,
        logics: [
          {
            type: 'api_rpc_reply',
            mode: 'keys',
            err_node_id: '',
            res_data: ['Id', 'ekbId'],
            res_data_type: { Id: 'string', ekbId: 'string' },
          },
          { to_node_id: finalReply_node_ID, type: 'go', node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateQueue.status).toBe(RESP_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_idReply, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);

    const responseTask1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_idCall,
        data: { ekbId: 12345, Id: '54321' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask1.status).toBe(RESP_STATUS.OK);
    expect(responseTask1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask1.body.ops[0].obj).toEqual('task');
    task_id3 = responseTask1.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_idCall,
        ref_or_obj_id: task_id3,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].data.Id).toEqual('54321');
    expect(responseShow.body.ops[0].data.ekbId).toEqual('12345');

    const responseList = await requestList(api, finalCall_node_ID, conv_idCall, company_id, 10);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(3);

    const responseListReply = await requestList(api, finalReply_node_ID, conv_idReply, company_id, 10);
    expect(responseListReply.status).toBe(RESP_STATUS.OK);
    expect(responseListReply.body.ops[0].count).toEqual(3);
    expect(responseListReply.body.ops[0].list[0].data.ekbId).toEqual('12345');
    expect(responseListReply.body.ops[0].list[0].data.Id).toEqual('54321');
    expect(responseListReply.body.ops[0].list[0].data.__rpc_conv_id__).toEqual(conv_idCall);
    expect(responseListReply.body.ops[0].list[1].data.ekbId).toEqual('12345');
    expect(responseListReply.body.ops[0].list[1].data.Id).toEqual('54321');
    expect(responseListReply.body.ops[0].list[1].data.__rpc_conv_id__).toEqual(conv_idCall);
    expect(responseListReply.body.ops[0].list[2].data.ekbId).toEqual('12345');
    expect(responseListReply.body.ops[0].list[2].data.Id).toEqual('54321');
    expect(responseListReply.body.ops[0].list[2].data.__rpc_conv_id__).toEqual(conv_idCall);
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_idReply, company_id);
    expect(response.status).toBe(RESP_STATUS.OK);

    const responseD = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_idCall, company_id);
    expect(responseD.status).toBe(RESP_STATUS.OK);
  });
});
