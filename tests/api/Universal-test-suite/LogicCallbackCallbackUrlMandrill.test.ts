import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import { axiosInstance } from '../../../application/api/AxiosClient';
import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';
import { addMsg } from 'jest-html-reporters/helper';
import { error } from '../../../support/utils/logger';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
} from '../../../application/api/ApiObj';

describe('LogicCallback URL Mandrill', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id_callback: number;
  let hash: string | number;
  let process_node_ID_c: string | number;
  let final_node_ID_c: string | number;
  let task_id_c: string | number;

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;

      const responseCallback = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Callback`);
      conv_id_callback = responseCallback.body.ops[0].obj_id;
      hash = responseCallback.body.ops[0].hash;

      const responseListCallback = await requestListConv(api, conv_id_callback, company_id);
      expect(responseListCallback.status).toBe(200);
      process_node_ID_c = (responseListCallback.body.ops[0].list as Array<any>).find(item => item.title === 'process')
        .obj_id;
      final_node_ID_c = (responseListCallback.body.ops[0].list as Array<any>).find(item => item.title === 'final')
        .obj_id;

      const responseCreateCallback = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID_c,
          conv_id: conv_id_callback,
          title: 'process',
          description: '',
          obj_type: 0,
          logics: [
            { type: 'api_callback', obj_id_path: '' },
            {
              node_title: 'final',
              to_conv_title: 'post_application/json',
              to_node_title: 'final',
              to_node_id: final_node_ID_c,
              edit: true,
              format: 'json',
              type: 'go',
              conv_title: 'post_application/json',
            },
          ],
          semaphors: [],
          node_location: [50, 180],
          version: 22,
        }),
      );
      expect(responseCreateCallback.status).toBe(RESP_STATUS.OK);

      const responseModifyCallback = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID_c,
          conv_id: conv_id_callback,
          title: 'process1',
          description: 'new',
          obj_type: 0,
          logics: [
            { type: 'api_callback', obj_id_path: 'task_id' },
            {
              node_title: 'final',
              to_conv_title: 'post_application/json',
              to_node_title: 'final',
              to_node_id: final_node_ID_c,
              edit: true,
              format: 'json',
              type: 'go',
              conv_title: 'post_application/json',
            },
          ],
          semaphors: [],
          node_location: [50, 180],
          version: 22,
        }),
      );
      expect(responseModifyCallback.status).toBe(RESP_STATUS.OK);

      const responseCommit = await requestConfirm(api, conv_id_callback, company_id);
      expect(responseCommit.status).toBe(RESP_STATUS.OK);
    },
  );

  test(`shouldn't create task conv Callback direct_url_2`, async (): Promise<void> => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/public/${conv_id_callback}/${hash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { a: 1 },
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.ops.proc).toEqual(PROC_STATUS.OK);
    expect(response.data.ops.obj).toEqual('task');

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await requestList(api, process_node_ID_c, conv_id_callback, company_id, 10);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    task_id_c = responseShow.body.ops[0].list[0].obj_id;
  });

  test(`shouldn't create task callback url`, async (): Promise<void> => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/${process_node_ID_c}/${hash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { task_id: `${task_id_c}`, b: '2' },
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.data.ops[0].obj).toEqual('task');
  });

  test(`shouldn't create task callback url task_id_' '`, async (): Promise<void> => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/${process_node_ID_c}/${hash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { task_id: `' '`, b: '2' },
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.proc).toEqual(PROC_STATUS.ERROR);
    expect(response.data.description).toEqual(`Value is not valid. Value's byte_size is less than minimum allowed: 24`);

    await new Promise(r => setTimeout(r, 2000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id_callback,
        ref_or_obj_id: task_id_c,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].data.a).toBe(1);
    expect(responseShow.body.ops[0].data.b).toBe('2');
    expect(responseShow.body.ops[0].data.task_id).toBe(`${task_id_c}`);
  });

  test(`shouldn't create task conv Callback direct_url_2`, async (): Promise<void> => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/public/${conv_id_callback}/${hash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { a: 1 },
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.ops.proc).toEqual(PROC_STATUS.OK);
    expect(response.data.ops.obj).toEqual('task');

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await requestList(api, process_node_ID_c, conv_id_callback, company_id, 10);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    task_id_c = responseShow.body.ops[0].list[0].obj_id;
  });

  test(`shouldn't create task callback url task id`, async (): Promise<void> => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/${process_node_ID_c}/${hash}/${task_id_c}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { b: '2' },
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.data.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await requestList(api, process_node_ID_c, conv_id_callback, company_id, 10);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
  });

  test(`shouldn't check Callback result task id in url`, async (): Promise<void> => {
    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id_callback,
        ref_or_obj_id: task_id_c,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].data.a).toBe(1);
    expect(responseShow.body.ops[0].data.b).toBe('2');
  });

  test(`shouldn't create task conv Callback direct_url_1`, async (): Promise<void> => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/public/${conv_id_callback}/${hash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { new: 4545 },
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.ops.proc).toEqual(PROC_STATUS.OK);
    expect(response.data.ops.obj).toEqual('task');

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await requestList(api, process_node_ID_c, conv_id_callback, company_id, 10);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    task_id_c = responseShow.body.ops[0].list[0].obj_id;
  });

  test(`shouldn't modify task mandrill`, async (): Promise<void> => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/plugins/mandrill/${process_node_ID_c}/${hash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { task_id: `${task_id_c}`, c: 'test' },
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.ops.proc).toEqual(PROC_STATUS.OK);
    expect(response.data.ops.obj).toEqual('task');

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id_callback,
        obj_id: task_id_c,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].data.c).toBe('test');
    expect(responseShow.body.ops[0].data.new).toBe(4545);
    expect(responseShow.body.ops[0].data.task_id).toBe(`${task_id_c}`);
  });

  test(`shouldn't create task conv Callback direct url 1`, async (): Promise<void> => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/public/${conv_id_callback}/${hash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { new: 4545 },
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.ops.proc).toEqual(PROC_STATUS.OK);
    expect(response.data.ops.obj).toEqual('task');

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await requestList(api, process_node_ID_c, conv_id_callback, company_id, 10);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    task_id_c = responseShow.body.ops[0].list[0].obj_id;
  });

  test(`shouldn't modify task mandrill events`, async (): Promise<void> => {
    try {
      const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/plugins/mandrill/${process_node_ID_c}/${hash}`;
      const response = await axiosInstance({
        method: 'POST',
        url: uri,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        data: `mandrill_events=[{ "task_id": "${task_id_c}", "c": "test" }]`,
      });
      await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.data.ops.proc).toEqual(PROC_STATUS.OK);
      expect(response.data.ops.obj).toEqual('task');
    } catch (err) {
      error(err);
    }

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id_callback,
        obj_id: task_id_c,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].data.c).toBe('test');
    expect(responseShow.body.ops[0].data.new).toBe(4545);
    expect(responseShow.body.ops[0].data.task_id).toBe(`${task_id_c}`);
  });

  test(`shouldn't list node Callback FinalNode`, async (): Promise<void> => {
    const responseShow = await requestList(api, final_node_ID_c, conv_id_callback, company_id, 10);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].list[0].data.c).toBe('test');
    expect(responseShow.body.ops[0].list[0].data.new).toBe(4545);
    expect(responseShow.body.ops[0].list[0].data.task_id).toBe(`${task_id_c}`);
    expect(responseShow.body.ops[0].list[1].data.c).toBe('test');
    expect(responseShow.body.ops[0].list[1].data.new).toBe(4545);
    expect(responseShow.body.ops[0].count).toEqual(4);
  });

  test(`shouldn't list node Callback Callbacknode`, async (): Promise<void> => {
    const responseShow = await requestList(api, process_node_ID_c, conv_id_callback, company_id, 10);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].count).toEqual(0);
  });

  test(`shouldn't create empty task conv Callback direct url`, async (): Promise<void> => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/public/${conv_id_callback}/${hash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: {},
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(200);
    expect(response.data.ops.obj).toEqual('task');
    expect(response.data.ops.obj_id).not.toEqual('');
  });

  test(`shouldn't create task conv data:[]`, async (): Promise<void> => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/public/${conv_id_callback}/${hash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: [],
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(RESP_STATUS.BAD_REQUEST);
    expect(response.data.proc).toEqual(PROC_STATUS.ERROR);
    expect(response.data.description).toEqual('Incorrect body');
  });

  afterAll(
    async (): Promise<void> => {
      const responseDeleteCallback = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id_callback, company_id);
      expect(responseDeleteCallback.status).toBe(RESP_STATUS.OK);
    },
  );
});
