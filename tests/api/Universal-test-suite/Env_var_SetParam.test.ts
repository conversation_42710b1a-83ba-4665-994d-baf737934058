import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';
import { requestList, requestConfirm, requestDeleteObj, requestListConv } from '../../../application/api/ApiObj';

describe('Env_var (positive)', (): void => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let newVar: string | number;
  let company_id: any;
  let project_short_name: string;
  let stage_short_name: string;
  let conv_id: number;
  let newApiKeyId: string | number;
  let newApiKeyLogin: number;
  let newApiKeySecret: string | number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;
      project_short_name = `project-${Date.now()}`;
      stage_short_name = `stage-${Date.now()}`;

      const CreateKeyResponse = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.USER,
          company_id,
          title: 'API',
          logins: [{ type: 'api' }],
        }),
      );
      expect(CreateKeyResponse.status).toBe(200);
      const user_api = CreateKeyResponse.body;
      const newApiKey = {
        key: `${user_api.ops[0].users[0].logins[0].obj_id}`,
        secret: user_api.ops[0].users[0].logins[0].key,
        companies: [],
        title: '',
        id: `${user_api.ops[0].users[0].obj_id}`,
      } as ApiKey;
      newApiKeyId = +newApiKey.id;
      newApiKeyLogin = +newApiKey.key;
      newApiKeySecret = newApiKey.secret;

      const responseProject = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.PROJECT,
          company_id,

          title: `Project_${Date.now()}`,
          short_name: project_short_name,
          description: 'test',
          stages: [],
          status: 'active',
        }),
      );
      newProject = responseProject.body.ops[0].obj_id;

      const responseStage = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.STAGE,
          company_id,

          title: `Stage_${Date.now()}`,
          short_name: stage_short_name,
          description: 'test',
          project_id: newProject,
        }),
      );
      newStage = responseStage.body.ops[0].obj_id;

      const responseLink = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.PROJECT,
          obj_id: newProject,
          company_id,

          obj_to: 'user',
          obj_to_id: newApiKeyId,
          is_need_to_notify: false,
          privs: [
            { type: 'create', list_obj: ['all'] },
            { type: 'modify', list_obj: ['all'] },
            { type: 'delete', list_obj: ['all'] },
            { type: 'view', list_obj: ['all'] },
          ],
        }),
      );
      expect(responseLink.status).toBe(200);

      const responseConv = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.CONV,
          company_id,
          title: 'Set_param',
          status: 'actived',
          project_id: newProject,
          stage_id: newStage,
        }),
      );
      conv_id = responseConv.body.ops[0].obj_id;

      const responseList = await requestListConv(api, conv_id, company_id);
      expect(responseList.status).toBe(200);
      process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
      final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
    },
  );

  test(`should create ENV_VAR data_type raw`, async (): Promise<void> => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'visible',
        data_type: 'raw',
        short_name: 'envraw',
        description: 'test',
        title: 'Var',
        value: 'test_Var_SetParameter',
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: 'set_param', fields: ['extra'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].fingerprints[0].algo).toBe('md5');
    expect(response.body.ops[0].fingerprints[1].algo).toBe('sha256');
    expect(response.body.ops[0].fingerprints[0].value).not.toBeEmpty;
    expect(response.body.ops[0].fingerprints[1].value).not.toBeEmpty;
    newVar = response.body.ops[0].obj_id;
  });

  test(`should reading key set_param from env_var_id`, async (): Promise<void> => {
    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,

        conv_id,
        title: 'Set_Param',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'set_param',
            extra: { key: `{{env_var[${newVar}]}}` },
            extra_type: { key: 'string' },
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);
    expect(responseCreateNode.body.ops[0].proc).toEqual('ok');

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        conv_id: conv_id,
        data: { one: `env_var[${newVar}]`, data: 201 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 4000));

    const responseShow = await requestList(api, final_node_ID, conv_id, company_id);
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].list[0].data.key).toEqual('test_Var_SetParameter');
    expect(responseShow.body.ops[0].list[0].data.one).toEqual(`env_var[${newVar}]`);
  });

  test(`should reading key set_param from env_var_short_name`, async (): Promise<void> => {
    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,

        conv_id,
        title: 'Set_param',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'set_param',
            extra: { key: `{{env_var[@envraw]}}` },
            extra_type: { key: 'string' },
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);
    expect(responseCreateNode.body.ops[0].proc).toEqual('ok');

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        conv_id: conv_id,
        data: { one: `env_var[@envraw]`, data: 201 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 4000));

    const responseShow = await requestList(api, final_node_ID, conv_id, company_id);
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].proc).toEqual('ok');
    expect(responseShow.body.ops[0].list[0].data.key).toEqual('test_Var_SetParameter');
    expect(responseShow.body.ops[0].list[0].data.one).toEqual(`env_var[@envraw]`);
  });

  test(`should reading key Set_param from env_var_short_name json (type: set_parameter/api_call)`, async (): Promise<
    void
  > => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'visible',
        data_type: 'json',
        short_name: 'envjson',
        description: 'test',
        title: 'Var',
        value: `{\"key\":\"${newApiKeySecret}\"}`,
        project_id: newProject,
        stage_id: newStage,
        scopes: [
          { type: 'set_param', fields: '*' },
          { type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] },
        ],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    newVar = response.body.ops[0].obj_id;

    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,

        conv_id,
        title: 'Set_param',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'set_param',
            extra: { key: `{{env_var[@envjson].key}}` },
            extra_type: { key: 'string' },
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);
    expect(responseCreateNode.body.ops[0].proc).toEqual('ok');

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        conv_id: conv_id,
        data: { one: `env_var[@envjson]`, data: 201 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 4000));

    const responseShow = await requestList(api, final_node_ID, conv_id, company_id);
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].proc).toEqual('ok');
    expect(responseShow.body.ops[0].list[0].data.key).toEqual(newApiKeySecret);
    expect(responseShow.body.ops[0].list[0].data.one).toEqual(`env_var[@envjson]`);
  });

  test(`should сreate task set_param after delete ENV_VAR`, async (): Promise<void> => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].obj_id).toBe(newVar);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        conv_id: conv_id,
        data: { one: `env_var[${newVar}]`, data: 201 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 1000));

    const responseShowApicall = await requestList(api, final_node_ID, conv_id, company_id);
    expect(responseShowApicall.status).toBe(200);
    expect(responseShowApicall.body.ops[0].proc).toEqual('ok');
    expect(responseShowApicall.body.ops[0].list[0].data.__conveyor_set_param_return_description__).toEqual(
      `EnvVariable @envjson doesn't found in current stage. Maybe it was deleted`,
    );
    expect(responseShowApicall.body.ops[0].list[0].data.__conveyor_set_param_return_type_tag__).toEqual(
      'env_var_not_found_or_deleted',
    );
  });

  test(`should list ENV_VAR after create`, async (): Promise<void> => {
    const responseCreate = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ENV_VAR,
        company_id,

        env_var_type: 'visible',
        data_type: 'json',
        short_name: 'envjson',
        description: 'test',
        title: 'Var',
        value: `{\"key\":\"${newApiKeySecret}\"}`,
        project_id: newProject,
        stage_id: newStage,
        scopes: [
          { type: 'set_param', fields: ['extra'] },
          { type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] },
        ],
      }),
    );
    expect(responseCreate.status).toBe(200);
    expect(responseCreate.body.ops[0].obj).toBe('env_var');
    newVar = responseCreate.body.ops[0].obj_id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list[0].title).toBe('Var');
    expect(response.body.ops[0].list[0].description).toBe('test');
    expect(response.body.ops[0].list[0].short_name).toContain('envjson');
    expect(response.body.ops[0].list[0].obj_id).toBe(newVar);
    expect(response.body.ops[0].list[0].data_type).toBe('json');
    expect(response.body.ops[0].list[0].value).toEqual(`{\"key\":\"${newApiKeySecret}\"}`);
    expect(response.body.ops[0].list[0].scopes).toEqual([
      { type: 'set_param', fields: ['extra'] },
      { type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] },
    ]);
  });

  test(`should modify and list ENV_VAR without scope`, async (): Promise<void> => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'visible',
        title: `newName`,
        description: `modify`,
        short_name: `modify`,
        data_type: 'json',
        value: `{\"test\":\"Modify_value\"}`,
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].obj_id).toBe(newVar);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[0].title).toBe('newName');
    expect(responseList.body.ops[0].list[0].description).toBe('modify');
    expect(responseList.body.ops[0].list[0].short_name).toContain('modify');
    expect(responseList.body.ops[0].list[0].obj_id).toBe(newVar);
    expect(responseList.body.ops[0].list[0].data_type).toBe('json');
    expect(responseList.body.ops[0].list[0].scopes).toEqual([
      { type: 'set_param', fields: ['extra'] },
      { type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] },
    ]);
  });

  test(`should modify and list ENV_VAR without scope and value`, async (): Promise<void> => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'visible',
        title: `newName`,
        description: `modify`,
        short_name: `modify`,
        data_type: 'json',
        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].obj_id).toBe(newVar);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[0].title).toBe('newName');
    expect(responseList.body.ops[0].list[0].description).toBe('modify');
    expect(responseList.body.ops[0].list[0].short_name).toContain('modify');
    expect(responseList.body.ops[0].list[0].obj_id).toBe(newVar);
    expect(responseList.body.ops[0].list[0].data_type).toBe('json');
    expect(responseList.body.ops[0].list[0].value).toEqual('{"test":"Modify_value"}');
    expect(responseList.body.ops[0].list[0].scopes).toEqual([
      { type: 'set_param', fields: ['extra'] },
      { type: 'api_call', fields: ['cert_pem', 'api_secret_outer'] },
    ]);
  });

  test(`should reading key from env_var_id in api_call`, async (): Promise<void> => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        env_var_type: 'visible',
        title: `newName`,
        description: `modify`,
        short_name: `modify`,
        data_type: 'json',
        value: `{\"key\":\"${newApiKeySecret}\"}`,
        project_id: newProject,
        stage_id: newStage,
        scopes: [{ type: '*', fields: '*' }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].obj_id).toBe(newVar);

    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,

        conv_id,
        title: 'LogicCallNew',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: 'raw',
            method: 'POST',
            raw_body: `{\"ops\":[{\"type\": \"get\",\"conv_id\": ${conv_id},\"obj\": \"callback_hash\"}]}`,
            url: `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/${newApiKeyLogin}`,
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: '',
            extra_headers: {},
            send_sys: true,
            cert_pem: '',
            content_type: 'application/json',
            api_secret_outer: `{{env_var[${newVar}].key}}`,
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);
    expect(responseCreateNode.body.ops[0].proc).toEqual('ok');

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual('ok');

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        conv_id: conv_id,
        data: { one: `env_var[${newVar}]`, data: 201 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 4000));

    const responseShowApicall = await requestList(api, final_node_ID, conv_id, company_id);
    expect(responseShowApicall.status).toBe(200);
    expect(responseShowApicall.body.ops[0].proc).toEqual('ok');
    expect(responseShowApicall.body.ops[0].list[0].data.ops[0].proc).toEqual('ok');
    expect(responseShowApicall.body.ops[0].list[0].data.data).toEqual(201);
    expect(responseShowApicall.body.ops[0].list[0].data.one).toEqual(`env_var[${newVar}]`);
    expect(responseShowApicall.body.ops[0].list[0].data.ops[0].proc).toEqual('ok');
  });

  test(`should delete ENV_VAR and create task to api_call`, async (): Promise<void> => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ENV_VAR,
        obj_id: newVar,
        company_id,

        project_id: newProject,
        stage_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('env_var');
    expect(response.body.ops[0].obj_id).toBe(newVar);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        conv_id: conv_id,
        data: { one: `env_var[${newVar}]`, data: 201 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 4000));

    const responseShowApicall = await requestList(api, final_node_ID, conv_id, company_id);
    expect(responseShowApicall.status).toBe(200);
    expect(responseShowApicall.body.ops[0].proc).toEqual('ok');
    expect(responseShowApicall.body.ops[0].list[0].data.__conveyor_api_return_description__).toEqual(
      `Environment variable with id or alias - ${newVar} doesn't exists`,
    );
    expect(responseShowApicall.body.ops[0].list[0].data.__conveyor_api_return_type_tag__).toEqual('env_var_not_found');
  });

  afterAll(
    async (): Promise<void> => {
      const response = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject, company_id);
      expect(response.status).toBe(200);
    },
  );
});
