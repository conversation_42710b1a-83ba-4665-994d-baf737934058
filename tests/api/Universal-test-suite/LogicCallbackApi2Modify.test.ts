import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
} from '../../../application/api/ApiObj';

describe('LogicCallbackApi2+Modify', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id_modify: number;
  let conv_id_callback: number;
  let owner_id_m: number;
  let process_node_ID_m: string | number;
  let final_node_ID_m: string | number;
  let process_node_ID_c: string | number;
  let final_node_ID_c: string | number;
  let callback_node: string | number;
  let task_id: string | number;
  let task_id_c: string | number;
  let ref_task_id: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseModify = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Copy`);
    conv_id_modify = responseModify.body.ops[0].obj_id;

    const responseCallback = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Callback`);
    conv_id_callback = responseCallback.body.ops[0].obj_id;

    const responseListModify = await requestListConv(api, conv_id_modify, company_id);
    expect(responseListModify.status).toBe(RESP_STATUS.OK);
    process_node_ID_m = (responseListModify.body.ops[0].list as Array<any>).find(item => item.title === 'process')
      .obj_id;
    final_node_ID_m = (responseListModify.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
    owner_id_m = responseListModify.body.ops[0].owner_id;

    const responseListCallback = await requestListConv(api, conv_id_callback, company_id);
    expect(responseListCallback.status).toBe(RESP_STATUS.OK);
    process_node_ID_c = (responseListCallback.body.ops[0].list as Array<any>).find(item => item.title === 'process')
      .obj_id;
    final_node_ID_c = (responseListCallback.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateModify = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID_m,
        conv_id: conv_id_modify,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api_copy',
            sync: true,
            err_node_id: '',
            conv_id: conv_id_callback,
            mode: 'modify',
            ref: 'chupakabra',
            data: {
              Object: 'Table',
              Age: '999',
              Boolean: 'true',
              omnonmnom: '[{"a":"aaa","b":["sad","asd","sad","as","d"]}]',
            },
            data_type: { Object: 'string', Age: 'number', Boolean: 'boolean', omnonmnom: 'array' },
            group: 'all',
          },
          {
            to_node_id: final_node_ID_m,
            format: 'json',
            type: 'go',
            index: 1,
            node_title: 'final',
            conv_owner_id: owner_id_m,
            conv_owner_name: 'DTPO',
          },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateModify.status).toBe(RESP_STATUS.OK);

    const responseCommitModify = await requestConfirm(api, conv_id_modify, company_id);
    expect(responseCommitModify.status).toBe(RESP_STATUS.OK);

    const responseCreateCallback = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID_c,
        conv_id: conv_id_callback,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          { type: 'api_callback', obj_id_path: '' },
          {
            node_title: 'final',
            to_conv_title: 'post_application/json',
            to_node_title: 'final',
            to_node_id: final_node_ID_c,
            edit: true,
            format: 'json',
            type: 'go',
            conv_title: 'post_application/json',
          },
        ],
        semaphors: [],
        node_location: [50, 180],
        version: 22,
      }),
    );
    expect(responseCreateCallback.status).toBe(RESP_STATUS.OK);

    const responseModifyCallback = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID_c,

        conv_id: conv_id_callback,
        title: 'process1',
        description: 'new',
        obj_type: 0,
        logics: [
          { type: 'api_callback', obj_id_path: '' },
          {
            node_title: 'final',
            to_conv_title: 'post_application/json',
            to_node_title: 'final',
            to_node_id: final_node_ID_c,
            edit: true,
            format: 'json',
            type: 'go',
            conv_title: 'post_application/json',
          },
        ],
        semaphors: [],
        node_location: [50, 180],
        version: 22,
      }),
    );
    expect(responseModifyCallback.status).toBe(RESP_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_id_callback, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);

    const responseModifySync = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID_c,
        conv_id: conv_id_callback,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          { type: 'api_callback', is_sync: true, obj_id_path: '' },
          {
            node_title: 'final',
            to_conv_title: 'post_application/json',
            to_node_title: 'final',
            to_node_id: final_node_ID_c,
            edit: true,
            format: 'json',
            type: 'go',
            conv_title: 'post_application/json',
          },
        ],
        semaphors: [],
        node_location: [50, 180],
        version: 22,
      }),
    );
    expect(responseModifySync.status).toBe(RESP_STATUS.OK);

    const responseCommit2 = await requestConfirm(api, conv_id_callback, company_id);
    expect(responseCommit2.status).toBe(RESP_STATUS.OK);
    const responseListCallback2 = await requestListConv(api, conv_id_callback, company_id);
    expect(responseListCallback2.status).toBe(RESP_STATUS.OK);
    callback_node = (responseListCallback2.body.ops[0].list as Array<any>).find(item => item.title === 'process')
      .obj_id;
  });

  test('should Send task to conv with Callback ', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id_callback,
        data: {},
        ref: `chupakabra`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id_c = responseTask.body.ops[0].obj_id;
  });

  test('should Send task to conv with Modify ', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id_modify,
        data: { a: 'qwerty' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id_modify,
        ref_or_obj_id: task_id,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].data.__conveyor_copy_task_result__).toBe('ok');
    expect(responseShow.body.ops[0].data.a).toBe('qwerty');
  });

  test('should check Callback result FinalNode obj ', async () => {
    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id_callback,
        obj_id: task_id_c,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].data.Object).toBe('Table');
    expect(responseShow.body.ops[0].data.a).toBe('qwerty');
    expect(responseShow.body.ops[0].data.Age).toBe(999);
    expect(responseShow.body.ops[0].data.Boolean).toBe(true);
    expect(responseShow.body.ops[0].data.omnonmnom[0].a).toBe('aaa');
  });

  test('should check Callback result', async () => {
    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id_callback,
        ref_or_obj_id: task_id_c,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].data.Object).toBe('Table');
    expect(responseShow.body.ops[0].data.a).toBe('qwerty');
    expect(responseShow.body.ops[0].data.Age).toBe(999);
    expect(responseShow.body.ops[0].data.Boolean).toBe(true);
    expect(responseShow.body.ops[0].data.omnonmnom[0].a).toBe('aaa');
  });

  test('should Send task to to conv with Callback', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id_callback,
        data: { new: 4545 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id_c = responseTask.body.ops[0].obj_id;
    ref_task_id = responseTask.body.ops[0].ref;
  });

  test('should Modify task to to conv with Callback', async () => {
    await new Promise(r => setTimeout(r, 2000));
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id_callback,
        data: { new: 45456789 },
        ref: ref_task_id,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id_c = responseTask.body.ops[0].obj_id;
  });

  test('should check Callback result Callbacknode obj', async () => {
    await new Promise(r => setTimeout(r, 2000));
    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id_callback,
        obj_id: task_id_c,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].data.new).toBe(45456789);
  });

  test('should Send task to to conv with Callback', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id_callback,
        data: { new: 4545 },
        ref: `tasktest`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id_c = responseTask.body.ops[0].obj_id;
  });

  test('should check Callback result Callbacknode obj', async () => {
    await new Promise(r => setTimeout(r, 2000));
    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id_callback,
        obj_id: task_id_c,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].data.new).toBe(4545);
  });

  test('should check Callback result Callbacknode ref or obj', async () => {
    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id_callback,
        ref_or_obj_id: 'tasktest',
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].data.new).toBe(4545);
  });

  test('should check Callback result FinalNode ref_or_obj', async () => {
    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id_callback,
        ref_or_obj_id: 'chupakabra',
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].data.Object).toBe('Table');
    expect(responseShow.body.ops[0].data.a).toBe('qwerty');
    expect(responseShow.body.ops[0].data.Age).toBe(999);
    expect(responseShow.body.ops[0].data.Boolean).toBe(true);
    expect(responseShow.body.ops[0].data.omnonmnom[0].a).toBe('aaa');
  });

  test('should check Callback result FinalNode ref', async () => {
    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id_callback,
        ref: 'chupakabra',
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].data.Object).toBe('Table');
    expect(responseShow.body.ops[0].data.a).toBe('qwerty');
    expect(responseShow.body.ops[0].data.Age).toBe(999);
    expect(responseShow.body.ops[0].data.Boolean).toBe(true);
    expect(responseShow.body.ops[0].data.omnonmnom[0].a).toBe('aaa');
  });

  test('should List_node_Callback_FinalNode', async () => {
    const responseShow = await requestList(api, final_node_ID_c, conv_id_callback, company_id, 10);
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].list[1].data.Object).toBe('Table');
    expect(responseShow.body.ops[0].list[1].data.a).toBe('qwerty');
    expect(responseShow.body.ops[0].list[1].data.Age).toBe(999);
    expect(responseShow.body.ops[0].list[1].data.Boolean).toBe(true);
    expect(responseShow.body.ops[0].list[1].data.omnonmnom[0].a).toBe('aaa');
  });

  test('should List_node_Callback Callbacknode', async () => {
    const responseShow = await requestList(api, callback_node, conv_id_callback, company_id, 10);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].list[0].data.new).toBe(4545);
  });

  afterAll(async () => {
    const responseDeleteModify = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id_modify, company_id);
    expect(responseDeleteModify.status).toBe(RESP_STATUS.OK);

    const responseDeleteCallback = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id_callback, company_id);
    expect(responseDeleteCallback.status).toBe(RESP_STATUS.OK);
  });
});
