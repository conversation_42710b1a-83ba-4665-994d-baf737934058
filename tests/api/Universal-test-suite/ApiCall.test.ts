import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
  requestShow,
} from '../../../application/api/ApiObj';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';

describe('ApiCall', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let task_id: string | number;

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;

      const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Api_Call`);
      conv_id = response.body.ops[0].obj_id;

      const responseList = await requestListConv(api, conv_id, company_id);
      expect(responseList.status).toBe(RESP_STATUS.OK);
      process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
      final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

      const responseCreateNode = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,

          conv_id,
          title: 'LogicCallNew',
          description: '',
          obj_type: 0,
          logics: [
            {
              type: 'api',
              format: '',
              method: 'POST',
              url: 'https://api.rabota.ua/vacancy/search',
              extra: { ukrainian: 'true', cityId: '1', keyWords: 'director' },
              extra_type: { ukrainian: 'boolean', cityId: 'string', keyWords: 'string' },
              max_threads: 5,
              err_node_id: '',
              extra_headers: { 'x-api-key': 'aNo1qb3mxC6zu5KEsHKM37wfDICXUUAaTv515TIi' },
              send_sys: false,
              cert_pem: '',
              content_type: 'application/json',
            },
            { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseCreateNode.status).toBe(RESP_STATUS.OK);

      const responseCommit = await requestConfirm(api, conv_id, company_id);
      expect(responseCommit.status).toBe(RESP_STATUS.OK);
    },
  );

  test('should create task ApiCall', async (): Promise<void> => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: { stateid: 7003 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));

    const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask.status).toBe(RESP_STATUS.OK);
    expect(responseShowTask.body.ops[0].obj).toEqual('task');
    expect(responseShowTask.body.ops[0].task_id).toEqual(task_id);
    expect(responseShowTask.body.ops[0].data.stateid).toBe(7003);

    const responseList = await requestList(api, final_node_ID, conv_id, company_id, 1);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(1);
    expect(responseList.body.ops[0].list[0].data.documents[0].cityId).toBe(1);
  });

  test('should search id node', async (): Promise<void> => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,
        company_id,
        filter: 'conv',
        pattern: process_node_ID,
        limit: 20,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].list[0].title).toEqual('Api_Call');
  });

  test('should search url', async (): Promise<void> => {
    await new Promise(r => setTimeout(r, 8000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,
        company_id,
        filter: 'conv',
        pattern: 'https://api.rabota.ua',
        limit: 20,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].list[0].title).toEqual('Api_Call');
  });

  test('should create task ApiCall `https://api.turbosms.ua`', async (): Promise<void> => {
    const responseModifyNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,

        conv_id,
        title: 'LogicCallNew',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'GET',
            url: 'https://api.turbosms.ua',
            extra: {},
            extra_type: {},
            max_threads: 5,
            err_node_id: '',
            extra_headers: {},
            send_sys: false,
            cert_pem: '',
            content_type: 'application/json',
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifyNode.status).toBe(RESP_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: {},
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));

    const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask.status).toBe(RESP_STATUS.OK);
    expect(responseShowTask.body.ops[0].obj).toEqual('task');
    expect(responseShowTask.body.ops[0].task_id).toEqual(task_id);
    expect(responseShowTask.body.ops[0].data.response_code).toBe(103);

    const responseList = await requestList(api, final_node_ID, conv_id, company_id, 1);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(2);
  });

  afterAll(
    async (): Promise<void> => {
      const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
      expect(response.status).toBe(RESP_STATUS.OK);
    },
  );
});
