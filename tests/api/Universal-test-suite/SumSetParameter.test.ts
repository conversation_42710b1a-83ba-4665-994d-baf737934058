import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
  requestShow,
} from '../../../application/api/ApiObj';

describe('ReplyCallProcess', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;

  let company_id: any;
  let conv_id: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let start_node_ID: string | number;
  let nodeSum_id: string | number;

  let task_id1: string | number;
  let task_id2: string | number;
  let task_id3: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);

    company_id = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `SumSetParameter`);
    conv_id = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_id, company_id);
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
    start_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'start').obj_id;

    const responseModify = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id: conv_id,
        title: 'process',
        obj_type: 0,
        logics: [
          {
            type: 'set_param',
            err_node_id: '',
            extra: { sum: '{{test}}' },
            extra_type: { sum: 'string' },
          },
          { to_node_id: final_node_ID, type: 'go', node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModify.status).toBe(RESP_STATUS.OK);

    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        conv_id,
        title: 'Sum',
        obj_type: 0,
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(RESP_STATUS.OK);
    nodeSum_id = responseCreateNode.body.ops[0].obj_id;

    const responseModifySum = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: nodeSum_id,
        conv_id: conv_id,
        title: 'Sum',
        obj_type: 0,
        logics: [
          {
            type: 'api_sum',
            err_node_id: '',
            extra: [{ id: '1576142953138', name: 'test', value: '{{a}}' }],
          },
          { to_node_id: process_node_ID, type: 'go', node_title: 'process' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifySum.status).toBe(RESP_STATUS.OK);

    const responseModifyStart = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: start_node_ID,
        conv_id: conv_id,
        title: 'Start',
        obj_type: 1,
        logics: [{ to_node_id: nodeSum_id, type: 'go' }],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifyStart.status).toBe(RESP_STATUS.OK);

    const responseModifySet = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id: conv_id,
        title: 'process',
        obj_type: 0,
        logics: [
          {
            type: 'set_param',
            err_node_id: '',
            extra: { sum: `{{node[${nodeSum_id}].1576142953138}}` },
            extra_type: { sum: 'string' },
          },
          { to_node_id: final_node_ID, type: 'go', node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifySet.status).toBe(RESP_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);
  });

  test('should create task', async () => {
    const responseTask1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: { a: 35 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask1.status).toBe(RESP_STATUS.OK);
    expect(responseTask1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask1.body.ops[0].obj).toEqual('task');
    task_id1 = responseTask1.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 1000));

    const responseTask2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: { a: 55 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask2.status).toBe(RESP_STATUS.OK);
    expect(responseTask2.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask2.body.ops[0].obj).toEqual('task');
    task_id2 = responseTask2.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 1000));

    const responseTask3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: { a: 5 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask3.status).toBe(RESP_STATUS.OK);
    expect(responseTask3.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask3.body.ops[0].obj).toEqual('task');
    task_id3 = responseTask3.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 1000));

    const responseShowTask1 = await requestShow(api, OBJ_TYPE.TASK, task_id1, { conv_id });
    expect(responseShowTask1.body.ops[0].obj).toEqual('task');
    expect(responseShowTask1.body.ops[0].task_id).toEqual(task_id1);
    expect(responseShowTask1.body.ops[0].data).toEqual({ a: 35, sum: '0' });

    const responseShowTask2 = await requestShow(api, OBJ_TYPE.TASK, task_id2, { conv_id });
    expect(responseShowTask2.body.ops[0].obj).toEqual('task');
    expect(responseShowTask2.body.ops[0].task_id).toEqual(task_id2);
    expect(responseShowTask2.body.ops[0].data).toEqual({ a: 55, sum: '35' });

    const responseShowTask3 = await requestShow(api, OBJ_TYPE.TASK, task_id3, { conv_id });
    expect(responseShowTask3.body.ops[0].obj).toEqual('task');
    expect(responseShowTask3.body.ops[0].task_id).toEqual(task_id3);
    expect(responseShowTask3.body.ops[0].data).toEqual({ a: 5, sum: '90' });

    await new Promise(r => setTimeout(r, 1000));

    const responseList = await requestList(api, final_node_ID, conv_id, company_id, 10);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(3);
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
