import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
} from '../../../application/api/ApiObj';

describe('Sync_api', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;

  let company_id: any;
  let conv_id: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let start_node_ID: string | number;
  let delay_node_id: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);

    company_id = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Sync_api`);
    conv_id = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_id, company_id);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
    start_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'start').obj_id;

    const responseModify = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'process',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'POST',
            url: '{{__callback_url}}',
            extra: {},
            extra_type: {},
            max_threads: '5',
            err_node_id: '',
            extra_headers: {},
            send_sys: false,
            cert_pem: '',
            content_type: 'application/json',
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModify.status).toBe(RESP_STATUS.OK);

    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        id: process_node_ID,
        conv_id,
        title: 'api_sync',
        obj_type: 0,
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(RESP_STATUS.OK);
    delay_node_id = responseCreateNode.body.ops[0].obj_id;

    const responseModifyStart = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: start_node_ID,
        conv_id,
        title: 'start',
        obj_type: 1,
        logics: [{ to_node_id: delay_node_id, type: 'go' }],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifyStart.status).toBe(RESP_STATUS.OK);

    const responseModifySemafor = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: delay_node_id,
        conv_id,
        title: 'Process',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'GET',
            url: 'https://www.w3schools.com/php/books.xml',
            extra: { a: 'test' },
            extra_type: { a: 'string' },
            max_threads: '5',
            err_node_id: '',
            extra_headers: { 'x-api-key': 'aNo1qb3mxC6zu5KEsHKM37wfDICXUUAaTv515TIi' },
            send_sys: false,
            cert_pem: '',
            content_type: 'text/xml',
          },
          { node_title: 'final', to_node_id: process_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifySemafor.status).toBe(RESP_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);
  });

  test('should create task sync_api', async () => {
    const responseTask = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: { test: '{{body.ops[0].data.a}}' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    await new Promise(r => setTimeout(r, 2000));

    const responseList = await requestList(api, final_node_ID, conv_id, company_id, 10);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(1);
    expect(responseList.body.ops[0].list[0].data.bookstore.book).toBeArray;
    expect(responseList.body.ops[0].list[0].data.__callback_url).toContain(`api/1/plugins/callback/`);
  });

  test('should create task sync_api data{}', async () => {
    const responseTask = await api.requestSyncApi(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: {},
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    await new Promise(r => setTimeout(r, 2000));

    const responseList = await requestList(api, final_node_ID, conv_id, company_id, 10);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(2);
    expect(responseList.body.ops[0].list[0].data.bookstore.book).toBeArray;
    expect(responseList.body.ops[0].list[0].data.__callback_url).toContain(`api/1/plugins/callback/`);
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
