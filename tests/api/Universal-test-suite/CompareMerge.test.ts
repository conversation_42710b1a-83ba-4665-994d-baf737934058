import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS } from '../../../utils/corezoidRequest';
import {
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
  requestShow,
} from '../../../application/api/ApiObj';

describe('Alias', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let taskID: string | number;
  let company_id: any;
  let newConv: number;
  let newConvProd: number;
  let newAlias: string | number;
  let short_name: string | number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let newSD: number;
  let newSDProd: number;
  let project_id: string | number;
  let stageDev_id: string | number;
  let stageProd_id: string | number;
  let short_nameProject: string | number;
  let folder_id: number | string;
  let ref: number | string;
  let ref2: number | string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    short_name = `alias${Date.now()}`;
    short_nameProject = `project${Date.now()}`;
    ref = `ref${Date.now()}`;
    ref2 = `ref2${Date.now()}`;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,

        title: `Project_${Date.now()}`,
        short_name: short_nameProject,
        description: 'test',
        stages: [
          { title: 'production', immutable: true },
          { title: 'develop', immutable: false },
        ],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    project_id = response.body.ops[0].obj_id;
    stageDev_id = response.body.ops[0].stages[1];
    stageProd_id = response.body.ops[0].stages[0];
    folder_id = response.body.ops[0].stages[1];

    const responseConv = await requestCreateObj(
      api,
      OBJ_TYPE.CONV,
      company_id,
      `Conv`,
      folder_id,
      'process',
      project_id,
      stageDev_id,
    );
    newConv = responseConv.body.ops[0].obj_id;

    const responseListConv = await requestListConv(api, newConv, company_id, project_id, stageDev_id);
    expect(responseListConv.status).toBe(200);
    process_node_ID = (responseListConv.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseListConv.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseSD = await requestCreateObj(
      api,
      OBJ_TYPE.CONV,
      company_id,
      `SD`,
      folder_id,
      'state',
      project_id,
      stageDev_id,
    );
    newSD = responseSD.body.ops[0].obj_id;

    const responseAlias = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        short_name,
        description: `aliasDesk-${Date.now()}`,
        title: `aliasTitle-${Date.now()}`,
        project_id,
        stage_id: stageDev_id,
      }),
    );
    expect(responseAlias.status).toBe(200);
    newAlias = responseAlias.body.ops[0].obj_id;

    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,

        link: true,
        obj_to_type: 'conv',
        obj_to_id: newSD,
        project_id,
        stageDev_id,
      }),
    );
    expect(responseLink.status).toBe(200);
    expect(responseLink.body.ops[0].proc).toEqual('ok');

    const responseModifyNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,

        conv_id: newConv,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api_copy',
            sync: false,
            err_node_id: '',
            conv_id: `@${short_name}`,
            mode: 'create',
            ref,
            data: {
              Object: 'Table',
              Age: '999',
              Boolean: 'true',
            },
            data_type: { Object: 'string', Age: 'number', Boolean: 'boolean' },
            group: 'all',
          },
          {
            to_node_id: final_node_ID,
            format: 'json',
            type: 'go',
            index: 1,
            node_title: 'final',
          },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifyNode.status).toBe(200);

    const responseCommitCopy = await requestConfirm(api, newConv, company_id);
    expect(responseCommitCopy.status).toBe(200);
  });

  test('should compare stage dev/stage prod', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPARE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        company_id,

        obj_id: stageProd_id,
        obj_type: 'stage',
        obj_to_id: stageDev_id,
        obj_to_type: 'stage',
        project_id,
        num_stat: true,
        diff_status: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_type === 'alias').__num_stat).toEqual({
      added: 1,
      changed: 0,
      deleted: 0,
    });
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_type === 'alias').__status).toEqual('added');
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_type === 'alias').obj_id).toEqual(newAlias);
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_type === 'alias').parent_id).toEqual(
      stageProd_id,
    );
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'SD').__num_stat).toEqual({
      added: 1,
      changed: 0,
      deleted: 0,
    });
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'SD').__status).toEqual('added');
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'SD').obj_id).toEqual(newSD);
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'SD').parent_id).toEqual(stageProd_id);
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'Conv').__num_stat).toEqual({
      added: 1,
      changed: 0,
      deleted: 0,
    });
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'Conv').__status).toEqual('added');
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'Conv').obj_id).toEqual(newConv);
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'Conv').parent_id).toEqual(
      stageProd_id,
    );
  });

  test('should merge stage dev/stage prod', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        company_id,

        obj_id: stageProd_id,
        obj_type: 'stage',
        obj_to_id: stageDev_id,
        obj_to_type: 'stage',
        project_id,
        apply_mode: true,
        async: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].diff.obj_id).toEqual(stageProd_id);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        company_id,

        obj_id: stageProd_id,
        project_id,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list).toBeArrayOfSize(2);
    newConvProd = (responseList.body.ops[0].list as Array<any>).find(item => item.conv_type === 'process').obj_id;
    newSDProd = (responseList.body.ops[0].list as Array<any>).find(item => item.conv_type === 'state').obj_id;

    const responseListConv = await requestListConv(api, newConvProd, company_id, project_id, stageProd_id);
    expect(responseListConv.status).toBe(200);

    expect(
      (responseListConv.body.ops[0].list as Array<any>).find(item => item.logics[0]?.type === 'api_copy').logics[0]
        ?.mode,
    ).toEqual('create');
    expect(
      (responseListConv.body.ops[0].list as Array<any>).find(item => item.logics[0]?.type === 'api_copy').logics[0]
        ?.conv_id,
    ).toEqual(`@${short_name}`);
    expect(
      (responseListConv.body.ops[0].list as Array<any>).find(item => item.logics[0]?.type === 'api_copy').logics[0]
        ?.data,
    ).toEqual({ Age: '999', Boolean: 'true', Object: 'Table' });

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        conv_id: newConvProd,
        action: 'user',
        data: { c: 1 },
        ref: short_name,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');
    expect(responseTask.body.ops[0].ref).toEqual(short_name);
    taskID = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 5000));

    const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, taskID, { conv_id: newConvProd });
    expect(responseShowTask.status).toBe(RESP_STATUS.OK);
    expect(responseShowTask.body.ops[0].obj).toEqual('task');
    expect(responseShowTask.body.ops[0].task_id).toEqual(taskID);
    expect(responseShowTask.body.ops[0].data).toEqual({ __conveyor_copy_task_result__: 'ok', c: 1 });

    const responseShowTaskSD = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,

        conv_id: newSDProd,
        ref,
      }),
    );
    expect(responseShowTaskSD.status).toBe(200);
    expect(responseShowTaskSD.body.ops[0].proc).toEqual('ok');
    expect(responseShowTaskSD.body.ops[0].obj).toEqual('task');
    expect(responseShowTaskSD.body.ops[0].ref).toEqual(ref);
    expect(responseShowTaskSD.body.ops[0].data.__copy_conv_id__).toEqual(newConvProd);
    expect(responseShowTaskSD.body.ops[0].data.c).toEqual(1);
    expect(responseShowTaskSD.body.ops[0].data.Boolean).toEqual(true);
  });

  test('should compare stage dev/stage prod (modify logic copy)', async () => {
    const responseModifyNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,

        conv_id: newConv,
        title: 'processCopy',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api_copy',
            sync: false,
            err_node_id: '',
            conv_id: newSD,
            mode: 'create',
            ref: ref2,
            data: {
              test: '1',
            },
            data_type: { test: 'string' },
            group: 'all',
          },
          {
            to_node_id: final_node_ID,
            format: 'json',
            type: 'go',
            index: 1,
            node_title: 'final',
          },
        ],
        semaphors: [
          { type: 'count', value: '30', esc_node_id: final_node_ID },
          { type: 'time', value: '30', to_node_id: final_node_ID, dimension: 'sec' },
        ],
        version: 22,
      }),
    );
    expect(responseModifyNode.status).toBe(200);

    const responseCommitCopy = await requestConfirm(api, newConv, company_id);

    expect(responseCommitCopy.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPARE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        company_id,

        obj_id: stageProd_id,
        obj_type: 'stage',
        obj_to_id: stageDev_id,
        obj_to_type: 'stage',
        project_id,
        num_stat: true,
        diff_status: true,
      }),
    );
    expect(response.status).toBe(200);

    expect(response.body.ops[0].proc).toEqual('ok');
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_type === 'alias').__num_stat).toEqual({
      added: 0,
      changed: 0,
      deleted: 0,
    });
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_type === 'alias').__status).toEqual('');
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_type === 'alias').parent_id).toEqual(
      stageProd_id,
    );
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'SD').__num_stat).toEqual({
      added: 1,
      changed: 0,
      deleted: 0,
    });
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'SD').__status).toEqual('changed');
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'SD').obj_id).toEqual(newSDProd);
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'SD').parent_id).toEqual(stageProd_id);
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'Conv').__num_stat).toEqual({
      added: 2,
      changed: 5,
      deleted: 0,
    });
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'Conv').__status).toEqual('changed');
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'Conv').obj_id).toEqual(newConvProd);
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'Conv').parent_id).toEqual(
      stageProd_id,
    );
  });

  test('should merge stage dev/stage prod (modify logic copy)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        company_id,

        obj_id: stageProd_id,
        obj_type: 'stage',
        obj_to_id: stageDev_id,
        obj_to_type: 'stage',
        project_id,
        apply_mode: true,
        async: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].diff.obj_id).toEqual(stageProd_id);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        company_id,

        obj_id: stageProd_id,
        project_id,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list).toBeArrayOfSize(2);
    newConvProd = (responseList.body.ops[0].list as Array<any>).find(item => item.conv_type === 'process').obj_id;
    newSDProd = (responseList.body.ops[0].list as Array<any>).find(item => item.conv_type === 'state').obj_id;

    const responseListConv = await requestListConv(api, newConvProd, company_id, project_id, stageProd_id);
    expect(responseListConv.status).toBe(200);

    expect(
      (responseListConv.body.ops[0].list as Array<any>).find(item => item.logics[0]?.type === 'api_copy').logics[0]
        ?.mode,
    ).toEqual('create');
    expect(
      (responseListConv.body.ops[0].list as Array<any>).find(item => item.logics[0]?.type === 'api_copy').logics[0]
        ?.conv_id,
    ).toEqual(newSDProd);
    expect(
      (responseListConv.body.ops[0].list as Array<any>).find(item => item.logics[0]?.type === 'api_copy').logics[0]
        ?.data,
    ).toEqual({ test: '1' });

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        conv_id: newConvProd,
        action: 'user',
        data: { c: 1 },
        ref: short_nameProject,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');
    expect(responseTask.body.ops[0].ref).toEqual(short_nameProject);
    taskID = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 5000));

    const responseShowTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        obj_id: taskID,
        company_id,

        conv_id: newConvProd,
      }),
    );
    expect(responseShowTask.status).toBe(200);
    expect(responseShowTask.body.ops[0].proc).toEqual('ok');
    expect(responseShowTask.body.ops[0].obj).toEqual('task');
    expect(responseShowTask.body.ops[0].task_id).toEqual(taskID);
    expect(responseShowTask.body.ops[0].data).toEqual({ __conveyor_copy_task_result__: 'ok', c: 1 });

    const responseShowTaskSD = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,

        conv_id: newSDProd,
        ref: ref2,
      }),
    );
    expect(responseShowTaskSD.status).toBe(200);
    expect(responseShowTaskSD.body.ops[0].proc).toEqual('ok');
    expect(responseShowTaskSD.body.ops[0].obj).toEqual('task');
    expect(responseShowTaskSD.body.ops[0].ref).toEqual(ref2);
    expect(responseShowTaskSD.body.ops[0].data.__copy_conv_id__).toEqual(newConvProd);
    expect(responseShowTaskSD.body.ops[0].data.c).toEqual(1);
    expect(responseShowTaskSD.body.ops[0].data.test).toEqual('1');
  });

  test('should compare stage dev/stage prod (add logic code/delete sd)', async () => {
    const responseModifyNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,

        conv_id: newConv,
        title: 'processCode',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api_code',
            lang: 'js',
            src: 'data.a = 123;',
          },
          {
            to_node_id: final_node_ID,
            format: 'json',
            type: 'go',
            index: 1,
            node_title: 'final',
          },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifyNode.status).toBe(200);

    const responseCommit = await requestConfirm(api, newConv, company_id);
    expect(responseCommit.status).toBe(200);

    const responseDeleteSD = await requestDeleteObj(api, OBJ_TYPE.CONV, newSD, company_id);
    expect(responseDeleteSD.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPARE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        company_id,

        obj_id: stageProd_id,
        obj_type: 'stage',
        obj_to_id: stageDev_id,
        obj_to_type: 'stage',
        project_id,
        num_stat: true,
        diff_status: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_type === 'alias').__num_stat).toEqual({
      added: 0,
      changed: 3,
      deleted: 0,
    });
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_type === 'alias').__status).toEqual(
      'changed',
    );
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_type === 'alias').parent_id).toEqual(
      stageProd_id,
    );
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'SD').__num_stat).toEqual({
      added: 0,
      changed: 0,
      deleted: 1,
    });
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'SD').__status).toEqual('deleted');
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'SD').obj_id).toEqual(newSDProd);
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'SD').parent_id).toEqual(stageProd_id);
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'Conv').__num_stat).toEqual({
      added: 2,
      changed: 2,
      deleted: 10,
    });
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'Conv').__status).toEqual('changed');
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'Conv').obj_id).toEqual(newConvProd);
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'Conv').parent_id).toEqual(
      stageProd_id,
    );
  });

  test('should merge stage dev/stage prod (add logic code/delete sd)', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MERGE,
        obj: OBJ_TYPE.OBJ_SCHEME,
        company_id,

        obj_id: stageProd_id,
        obj_type: 'stage',
        obj_to_id: stageDev_id,
        obj_to_type: 'stage',
        project_id,
        apply_mode: true,
        async: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].diff.obj_id).toEqual(stageProd_id);

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.STAGE,
        company_id,

        obj_id: stageProd_id,
        project_id,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list).toBeArrayOfSize(1);
    newConvProd = (responseList.body.ops[0].list as Array<any>).find(item => item.conv_type === 'process').obj_id;

    const responseListConv = await requestListConv(api, newConvProd, company_id, project_id, stageProd_id);
    expect(responseListConv.status).toBe(200);

    expect(
      (responseListConv.body.ops[0].list as Array<any>).find(item => item.logics[0]?.type === 'api_code').logics[0]
        ?.src,
    ).toEqual('data.a = 123;');
    expect(
      (responseListConv.body.ops[0].list as Array<any>).find(item => item.logics[0]?.type === 'api_code').logics[0]
        ?.lang,
    ).toEqual('js');

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        conv_id: newConvProd,
        action: 'user',
        data: { c: 1 },
        ref: short_nameProject,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');
    expect(responseTask.body.ops[0].ref).toEqual(short_nameProject);
    taskID = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 5000));

    const responseShowTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        obj_id: taskID,
        company_id,

        conv_id: newConvProd,
      }),
    );
    expect(responseShowTask.status).toBe(200);
    expect(responseShowTask.body.ops[0].proc).toEqual('ok');
    expect(responseShowTask.body.ops[0].obj).toEqual('task');
    expect(responseShowTask.body.ops[0].task_id).toEqual(taskID);
    expect(responseShowTask.body.ops[0].data).toEqual({ a: 123, c: 1 });
  });

  afterAll(async () => {
    const responseDeleteConv = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, company_id);
    expect(responseDeleteConv.status).toBe(200);

    const responseDeleteAlias = await requestDeleteObj(api, OBJ_TYPE.ALIAS, newAlias, company_id);
    expect(responseDeleteAlias.status).toBe(200);

    const responseDeleteProject = await requestDeleteObj(api, OBJ_TYPE.PROJECT, project_id, company_id);
    expect(responseDeleteProject.status).toBe(200);
  });
});
