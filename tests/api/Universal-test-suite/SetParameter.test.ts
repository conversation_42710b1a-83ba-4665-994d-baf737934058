import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
  requestShow,
} from '../../../application/api/ApiObj';

describe('SetParameter', (): void => {
  let api: ApiKeyClient;
  let apikey: ApiKey;

  let company_id: any;
  let conv_id: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let task_id: string | number;

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);

      company_id = apikey.companies[0].id;

      const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `SetParam`);
      conv_id = response.body.ops[0].obj_id;

      const responseList = await requestListConv(api, conv_id, company_id);
      expect(responseList.status).toBe(200);
      process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
      final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

      const responseCreateSetParam = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id: conv_id,
          title: 'process',
          obj_type: 0,
          logics: [
            {
              type: 'set_param',
              extra: { key: '{{object}}' },
              extra_type: { key: 'object' },
              err_node_id: '',
            },
            { to_node_id: final_node_ID, type: 'go', node_title: 'final' },
          ],
          semaphors: [
            { type: 'count', value: 30, esc_node_id: final_node_ID },
            { type: 'time', value: 30, dimension: 'sec', to_node_id: final_node_ID },
          ],
          version: 22,
        }),
      );
      expect(responseCreateSetParam.status).toBe(RESP_STATUS.OK);
      expect(responseCreateSetParam.body.ops[0].proc).toEqual('ok');

      const responseCommit = await requestConfirm(api, conv_id, company_id);
      expect(responseCommit.status).toBe(200);
      expect(responseCommit.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    },
  );

  test('should create task SetParam', async (): Promise<void> => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          object: {
            Pascal: [
              { Name: 'Pascal Made Simple', price: 700 },
              { Name: 'Guide to Pascal', price: 400 },
            ],
            Scala: [
              { Name: 'Scala for the Impatient', price: 1000 },
              { Name: 'Scala in Depth', price: 1300 },
            ],
          },
          xxx: 'industrial',
          obj: [1354, 12, 984],
          num: 77777,
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 3000));

    const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask.status).toBe(RESP_STATUS.OK);
    expect(responseShowTask.body.ops[0].obj).toEqual('task');
    expect(responseShowTask.body.ops[0].task_id).toEqual(task_id);
    expect(responseShowTask.body.ops[0].data.xxx).toEqual('industrial');
    expect(responseShowTask.body.ops[0].data.num).toEqual(77777);
    expect(responseShowTask.body.ops[0].data.obj).toEqual([1354, 12, 984]);
    expect(responseShowTask.body.ops[0].data.key).toEqual({
      Pascal: [
        { Name: 'Pascal Made Simple', price: 700 },
        { Name: 'Guide to Pascal', price: 400 },
      ],
      Scala: [
        { Name: 'Scala for the Impatient', price: 1000 },
        { Name: 'Scala in Depth', price: 1300 },
      ],
    });
    expect(responseShowTask.body.ops[0].data.object).toEqual({
      Pascal: [
        { Name: 'Pascal Made Simple', price: 700 },
        { Name: 'Guide to Pascal', price: 400 },
      ],
      Scala: [
        { Name: 'Scala for the Impatient', price: 1000 },
        { Name: 'Scala in Depth', price: 1300 },
      ],
    });

    const responseListFinal = await requestList(api, final_node_ID, conv_id, company_id, 10);
    expect(responseListFinal.status).toBe(RESP_STATUS.OK);
    expect(responseListFinal.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseListFinal.body.ops[0].count).toEqual(1);
    expect(responseListFinal.body.ops[0].list[0].data.xxx).toEqual('industrial');
    expect(responseListFinal.body.ops[0].list[0].data.num).toEqual(77777);
    expect(responseListFinal.body.ops[0].list[0].data.obj).toEqual([1354, 12, 984]);
    expect(responseListFinal.body.ops[0].list[0].data.key).toEqual({
      Pascal: [
        { Name: 'Pascal Made Simple', price: 700 },
        { Name: 'Guide to Pascal', price: 400 },
      ],
      Scala: [
        { Name: 'Scala for the Impatient', price: 1000 },
        { Name: 'Scala in Depth', price: 1300 },
      ],
    });
    expect(responseListFinal.body.ops[0].list[0].data.object).toEqual({
      Pascal: [
        { Name: 'Pascal Made Simple', price: 700 },
        { Name: 'Guide to Pascal', price: 400 },
      ],
      Scala: [
        { Name: 'Scala for the Impatient', price: 1000 },
        { Name: 'Scala in Depth', price: 1300 },
      ],
    });
  });

  test(`should list conv get_counter`, async (): Promise<void> => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
        get_counter: true,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.request_proc).toBe(PROC_STATUS.OK);
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'final').count).toEqual(1);
  });

  test(`should reset node`, async (): Promise<void> => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESET,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID,
        company_id,
        conv_id,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.request_proc).toBe(PROC_STATUS.OK);
    expect(response.body.ops[0].obj).toEqual('node');

    const responseListFinal = await requestList(api, final_node_ID, conv_id, company_id, 10);
    expect(responseListFinal.status).toBe(200);
    expect(responseListFinal.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseListFinal.body.ops[0].count).toEqual(0);
    expect(responseListFinal.body.ops[0].list[0].data.key).toEqual({
      Pascal: [
        { Name: 'Pascal Made Simple', price: 700 },
        { Name: 'Guide to Pascal', price: 400 },
      ],
      Scala: [
        { Name: 'Scala for the Impatient', price: 1000 },
        { Name: 'Scala in Depth', price: 1300 },
      ],
    });
    expect(responseListFinal.body.ops[0].list[0].data.object).toEqual({
      Pascal: [
        { Name: 'Pascal Made Simple', price: 700 },
        { Name: 'Guide to Pascal', price: 400 },
      ],
      Scala: [
        { Name: 'Scala for the Impatient', price: 1000 },
        { Name: 'Scala in Depth', price: 1300 },
      ],
    });
  });

  afterAll(
    async (): Promise<void> => {
      const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
      expect(response.status).toBe(RESP_STATUS.OK);
    },
  );
});
