import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';
import { addMsg } from 'jest-html-reporters/helper';
import { requestDeleteObj, requestCreateObj } from '../../../application/api/ApiObj';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';

describe('Mult and search', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let key: string;
  let secret: string;
  let folder_id: number;
  let company_id: any;
  let conv_id: number;
  let conv_id_copy: string | number;
  const baseUrl: string | number = ConfigurationManager.getConfiguration().getApiUrl();
  const scriptPath = 'tests/api/corezoid-api/sh/uploadObject.sh';

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key = apikey.key;
    secret = apikey.secret;

    const responseFolder = await requestCreateObj(api, OBJ_TYPE.FOLDER, company_id, `TestSeach`);
    folder_id = responseFolder.body.ops[0].obj_id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `TestSeach`, folder_id);
    conv_id = response.body.ops[0].obj_id;
  });

  test('should Copy conv', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: conv_id,
        company_id,
        obj_type: 'conv',
        folder_id: 0,
        title: 'TestSeach123',
        ignore_errors: true,
        async: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].obj).toEqual('obj_copy');
    conv_id_copy = response.body.ops[0].scheme[0].obj_id;
  });

  test('should Download conv', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: conv_id,
        company_id,
        obj_type: 'conv',
        async: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].obj).toEqual('obj_scheme');
    expect(response.body.ops[0].download_url).toInclude(`/user_downloads/`);
  });

  const exec = promisify(execCallback);

  test(`upload`, async () => {
    const { stdout, stderr } = await exec(scriptPath, {
      env: {
        API_KEY_LOGIN: key,
        API_KEY_SECRET: secret,
        API_URL: baseUrl + 'api/2/upload',
        FOLDER_ID: `${folder_id}`,
        REWRITE_ALIAS: 'false',
        WITH_ALIAS: 'true',
        VALIDATE_SCHEME: 'false',
        COMPANY_ID: company_id,
        FILE_NAME: 'folder_for_test_upload_univer.suite_70conv.zip',
        ASYNC: 'false',
      },
    });
    expect(stderr).toBe(``);
    console.debug(stdout);
    console.debug(stderr);
    expect(stdout).toContain(`{"request_proc":"ok","ops":[{"obj":"obj_scheme","proc":"ok","scheme":`);
    await addMsg({ message: JSON.stringify(stdout), context: '' });
  });

  test('should find folder after upload', async () => {
    await new Promise(r => setTimeout(r, 10000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folder_id,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: 'for_test_upload' })]),
    );
  });

  test('should search title (3 characters)', async () => {
    await new Promise(r => setTimeout(r, 2000));
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,
        company_id,
        filter: 'conv',
        pattern: 'TestSeach',
        limit: 20,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: 'TestSeach' })]),
    );
  });

  test('should search id', async () => {
    await new Promise(r => setTimeout(r, 5000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: 'TestSeach',
        status: 'actived',
        folder_id,
      }),
    );
    conv_id = response.body.ops[0].obj_id;
    await new Promise(r => setTimeout(r, 2000));

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,
        company_id,
        filter: 'conv',
        pattern: `${conv_id}`,
        limit: 20,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toEqual('ok');
    expect(responseList.body.ops[0].list[0].title).toEqual('TestSeach');
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(response.status).toBe(200);

    const response2 = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id_copy, company_id);
    expect(response2.status).toBe(200);

    const response3 = await requestDeleteObj(api, OBJ_TYPE.FOLDER, folder_id, company_id);
    expect(response3.status).toBe(200);
  });
});
