import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import {
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
  requestList,
} from '../../../application/api/ApiObj';

describe('Cross Company Call Process', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let process1_id: number;
  let process1_node_id: string | number;
  let final_node_id_process1: string | number;
  let process2_id: number;
  let process2_node_id: string | number;
  let final_node_id_process2: string | number;
  let state_diagram_id: number;
  let task_ref: string;
  let task_id_process2: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    task_ref = 'TEST';

    const process1Response = await requestCreateObj(api, OBJ_TYPE.CONV, null, `Process1_Reply_v1.0_${Date.now()}`);
    expect(process1Response.status).toBe(RESP_STATUS.OK);
    process1_id = process1Response.body.ops[0].obj_id;

    const process1NodeResponse = await requestListConv(api, process1_id, null);
    expect(process1NodeResponse.status).toBe(RESP_STATUS.OK);
    process1_node_id = (process1NodeResponse.body.ops[0].list as Array<any>).find(item => item.title === 'process')
      .obj_id;
    final_node_id_process1 = (process1NodeResponse.body.ops[0].list as Array<any>).find(item => item.title === 'final')
      .obj_id;

    const modifyProcess1Response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process1_node_id,
        conv_id: process1_id,
        company_id: null,
        title: 'Process1_Node',
        obj_type: 0,
        logics: [
          {
            type: 'api_rpc_reply',
            mode: 'key_value',
            throw_exception: false,
            exception_reason: '',
            err_node_id: '',
            res_data: {
              result: 'success',
              payload: '{{payload}}',
            },
            res_data_type: {
              result: 'string',
              payload: 'object',
            },
          },
          {
            to_node_id: final_node_id_process1,
            type: 'go',
            node_title: 'final',
          },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(modifyProcess1Response.status).toBe(RESP_STATUS.OK);

    const commitProcess1Response = await requestConfirm(api, process1_id, null);
    expect(commitProcess1Response.status).toBe(RESP_STATUS.OK);

    const stateDiagramResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `State_Diagram_v1.0_${Date.now()}`,
        description: 'State diagram for process tracking',
        folder_id: 0,
        obj_type: 0,
        status: 'active',
        conv_type: 'state',
      }),
    );
    expect(stateDiagramResponse.status).toBe(RESP_STATUS.OK);
    state_diagram_id = stateDiagramResponse.body.ops[0].obj_id;

    const stateTaskResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: state_diagram_id,
        ref: task_ref,
        data: {
          conv: process1_id,
        },
      }),
    );
    expect(stateTaskResponse.status).toBe(RESP_STATUS.OK);

    const process2Response = await requestCreateObj(
      api,
      OBJ_TYPE.CONV,
      company_id,
      `Process2_CallProcess_v1.0_${Date.now()}`,
    );
    expect(process2Response.status).toBe(RESP_STATUS.OK);
    process2_id = process2Response.body.ops[0].obj_id;

    const process2NodeResponse = await requestListConv(api, process2_id, company_id);
    expect(process2NodeResponse.status).toBe(RESP_STATUS.OK);
    process2_node_id = (process2NodeResponse.body.ops[0].list as Array<any>).find(item => item.title === 'process')
      .obj_id;
    final_node_id_process2 = (process2NodeResponse.body.ops[0].list as Array<any>).find(item => item.title === 'final')
      .obj_id;

    const modifyProcess2Response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process2_node_id,
        conv_id: process2_id,
        company_id,
        title: 'Process2_Node',
        obj_type: 0,
        logics: [
          {
            type: 'api_rpc',
            conv_id: `{{conv[${state_diagram_id}].ref[${task_ref}].conv}}`,
            extra: {
              source_process: `${process2_id}`,
            },
            extra_type: {
              source_process: 'number',
            },
            group: 'all',
          },
          {
            to_node_id: final_node_id_process2,
            type: 'go',
            node_title: 'final',
          },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(modifyProcess2Response.status).toBe(RESP_STATUS.OK);

    const commitProcess2Response = await requestConfirm(api, process2_id, company_id);
    expect(commitProcess2Response.status).toBe(RESP_STATUS.OK);
  });

  test('should successfully call process from company to my corezoid', async () => {
    const createTaskResponse = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: process2_id,
        data: {
          data: 'Test data from Process2',
          payload: { a: 1 },
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(createTaskResponse.status).toBe(RESP_STATUS.OK);
    expect(createTaskResponse.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    task_id_process2 = createTaskResponse.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 3000));

    const taskProcess2Response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: process2_id,
        ref_or_obj_id: task_id_process2,
      }),
    );
    expect(taskProcess2Response.status).toBe(RESP_STATUS.OK);
    expect(taskProcess2Response.body.ops[0].proc).toBe(PROC_STATUS.OK);
    expect(taskProcess2Response.body.ops[0].data.result).toBe('success');

    const finalNodeProcess1Response = await requestList(api, final_node_id_process1, process1_id, null, 10);
    expect(finalNodeProcess1Response.status).toBe(RESP_STATUS.OK);

    expect(finalNodeProcess1Response.body.ops[0].count).toBeGreaterThan(0);

    const finalNodeTasks = finalNodeProcess1Response.body.ops[0].list || [];
    if (finalNodeTasks.length > 0) {
      const task = finalNodeTasks[0].data;
      expect(task).toHaveProperty('source_process');
      expect(task.source_process).toBe(process2_id);
      expect(task.__rpc_conv_id__).toBe(process2_id);
    }
  });

  afterAll(async () => {
    if (process1_id) {
      await requestDeleteObj(api, OBJ_TYPE.CONV, process1_id, null);
    }

    if (process2_id) {
      await requestDeleteObj(api, OBJ_TYPE.CONV, process2_id, company_id);
    }

    if (state_diagram_id) {
      await requestDeleteObj(api, OBJ_TYPE.CONV, state_diagram_id, company_id);
    }
  });
});
