import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS } from '../../../utils/corezoidRequest';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestListConv,
  requestCreateObj,
  requestShow,
} from '../../../application/api/ApiObj';

describe('ApiCallConstr', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: number;
  let conv_id_sd: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let task_id: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseSD = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: 'SDiagram',
        status: 'actived',
        obj_type: 0,
        conv_type: 'state',
      }),
    );
    conv_id_sd = responseSD.body.ops[0].obj_id;

    const responseTaskSD = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id_sd,
        data: { key: 'https://api.rabota.ua/vacancy/search' },
        ref: 'testApi',
      }),
    );
    expect(responseTaskSD.status).toBe(200);

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Api_Call`);
    conv_id = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_id, company_id);
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'POST',
            url: `{{conv[${conv_id_sd}].ref[testApi].key}}`,
            extra: { ukrainian: 'true', cityId: '1', keyWords: 'director' },
            extra_type: { ukrainian: 'boolean', cityId: 'string', keyWords: 'string' },
            max_threads: 5,
            err_node_id: '',
            extra_headers: { 'x-api-key': 'aNo1qb3mxC6zu5KEsHKM37wfDICXUUAaTv515TIi' },
            send_sys: false,
            cert_pem: '',
            content_type: 'application/json',
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateNode.status).toBe(200);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(200);
  });

  test('should create task {{conv[${conv_id_sd}].ref[testApi].key}} Api', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: { stateid: 7003 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 3000));

    const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask.status).toBe(RESP_STATUS.OK);
    expect(responseShowTask.body.ops[0].obj).toEqual('task');
    expect(responseShowTask.body.ops[0].task_id).toEqual(task_id);
    expect(responseShowTask.body.ops[0].data.stateid).toBe(7003);

    const responseList = await requestList(api, final_node_ID, conv_id, company_id);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(1);
    expect(responseList.body.ops[0].list[0].data.documents[0].cityId).toBe(1);
  });

  test('should create task2 {{conv[{{SD}}].ref[{{refSD}}].key}} Api', async () => {
    const responseModifyNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'POST',
            url: `{{conv[{{SD}}].ref[{{refSD}}].key}}`,
            extra: { ukrainian: 'true', cityId: '1', keyWords: 'director' },
            extra_type: { ukrainian: 'boolean', cityId: 'string', keyWords: 'string' },
            max_threads: '5',
            err_node_id: '',
            extra_headers: { 'x-api-key': 'aNo1qb3mxC6zu5KEsHKM37wfDICXUUAaTv515TIi' },
            send_sys: false,
            cert_pem: '',
            content_type: 'application/json',
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifyNode.status).toBe(200);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(200);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: { SD: conv_id_sd, refSD: 'testApi' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 3000));

    const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask.status).toBe(RESP_STATUS.OK);
    expect(responseShowTask.body.ops[0].obj).toEqual('task');
    expect(responseShowTask.body.ops[0].task_id).toEqual(task_id);
    expect(responseShowTask.body.ops[0].data.documents[0].cityId).toBe(1);
    expect(responseShowTask.body.ops[0].status).toBe('processed');

    const responseList = await requestList(api, final_node_ID, conv_id, company_id);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(2);
    expect(responseList.body.ops[0].list[0].data.documents[0].cityId).toBe(1);
  });

  test('should create task3 {{conv[${id_sd}].ref[testApi].{{key}}}} Api', async () => {
    const responseModifyNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            format: '',
            method: 'POST',
            url: `{{conv[${conv_id_sd}].ref[testApi].{{key}}}}`,
            extra: { ukrainian: 'true', cityId: '1', keyWords: 'director' },
            extra_type: { ukrainian: 'boolean', cityId: 'string', keyWords: 'string' },
            max_threads: 5,
            err_node_id: '',
            extra_headers: { 'x-api-key': 'aNo1qb3mxC6zu5KEsHKM37wfDICXUUAaTv515TIi' },
            send_sys: false,
            cert_pem: '',
            content_type: 'application/json',
          },
          { node_title: 'final', to_node_id: final_node_ID, format: 'json', type: 'go' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifyNode.status).toBe(200);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(200);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: { key: 'key' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 3000));

    const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask.status).toBe(RESP_STATUS.OK);
    expect(responseShowTask.body.ops[0].obj).toEqual('task');
    expect(responseShowTask.body.ops[0].task_id).toEqual(task_id);
    expect(responseShowTask.body.ops[0].data.documents[0].cityId).toBe(1);
    expect(responseShowTask.body.ops[0].status).toBe('processed');

    const responseList = await requestList(api, final_node_ID, conv_id, company_id);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(3);
    expect(responseList.body.ops[0].list[0].data.documents[0].cityId).toBe(1);
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(response.status).toBe(200);

    const response2 = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id_sd, company_id);
    expect(response2.status).toBe(200);
  });
});
