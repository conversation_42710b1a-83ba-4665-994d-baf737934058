import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import { requestDeleteObj } from '../../../application/api/ApiObj';

describe('ApiKey and Group', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let titleKey: string;
  let company_id: any;
  let newKeyLoginId: string | number;
  let newKeyObjId: string | number;
  let newKeyObjId2: string | number;
  let newGroupId: string | number;
  let titleGroup: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
  });

  test(`should create api key`, async () => {
    const responseKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: `key_${Date.now()}`,
        logins: [{ type: 'api' }],
      }),
    );
    expect(responseKey.status).toBe(200);
    expect(responseKey.body.ops[0].proc).toEqual('ok');
    newKeyLoginId = responseKey.body.ops[0].users[0].logins[0].obj_id;
    newKeyObjId = responseKey.body.ops[0].users[0].obj_id;
    titleKey = responseKey.body.ops[0].users[0].title;
  });

  test(`should list api key`, async () => {
    await new Promise(r => setTimeout(r, 4000));

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        name: `${titleKey}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].list[0].title).toEqual(`${titleKey}`);
  });

  test(`should create Group`, async () => {
    titleGroup = `Group_${Date.now()}`;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        title: titleGroup,
        obj_type: 'admins',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    newGroupId = response.body.ops[0].obj_id;
  });

  test(`should list group`, async () => {
    await new Promise(r => setTimeout(r, 4000));

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        name: titleGroup,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].list[0].title).toEqual(`${titleGroup}`);
  });

  test(`should link api key to group`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        obj_id: newKeyObjId,
        company_id,
        group_id: newGroupId,
        level: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].logins[0].obj_id).toEqual(newKeyLoginId);
  });

  test(`should create api key in group`, async () => {
    const responseKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: `key_${Date.now()}`,
        logins: [{ type: 'api' }],
        group_id: newGroupId,
      }),
    );
    expect(responseKey.status).toBe(200);
    expect(responseKey.body.ops[0].proc).toEqual('ok');
    newKeyObjId2 = responseKey.body.ops[0].users[0].obj_id;
    titleKey = responseKey.body.ops[0].users[0].title;
  });

  test(`should list group`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: newGroupId,
        company_id,
        sort: 'title',
        order: 'asc',
        list_obj: 'user',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newKeyObjId })]),
    );
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newKeyObjId2 })]),
    );
  });

  test(`should delete api key`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: newKeyObjId,
        company_id,
        group_id: 0,
        level: '',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test(`should delete api key group`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: newKeyObjId2,
        company_id,
        group_id: 0,
        level: '',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });

  test(`should delete group`, async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.GROUP, newGroupId, company_id);
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });
});
