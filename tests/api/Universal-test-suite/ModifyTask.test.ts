import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import { requestShow } from '../../../application/api/ApiObj';

describe('Modify Task API 1/2', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: number;
  let task_id: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: 'SD',
        status: 'actived',
        conv_type: 'state',
        obj_type: 0,
      }),
    );
    conv_id = response.body.ops[0].obj_id;

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          key: 'https://api.rabota.ua/vacancy/search',
        },
        ref: `testModify`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
  });

  test('should modify task api2 int', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: { key: 12345 },
        ref: `testModify`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 3000));

    const responseShowTask1 = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask1.body.ops[0].obj).toEqual('task');
    expect(responseShowTask1.body.ops[0].task_id).toEqual(task_id);
    expect(responseShowTask1.body.ops[0].data.key).toEqual(12345);
  });

  test('should modify task api2 string', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.TASK,
        conv_id: `${conv_id}`,
        data: { key: '12345' },
        ref: `testModify`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 3000));

    const responseShowTask1 = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask1.body.ops[0].obj).toEqual('task');
    expect(responseShowTask1.body.ops[0].task_id).toEqual(task_id);
    expect(responseShowTask1.body.ops[0].data.key).toEqual('12345');
  });

  test('should modify task api1 int', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: { key: 98765 },
        ref: `testModify`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 3000));

    const responseShowTask1 = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask1.body.ops[0].obj).toEqual('task');
    expect(responseShowTask1.body.ops[0].task_id).toEqual(task_id);
    expect(responseShowTask1.body.ops[0].data.key).toEqual(98765);
  });

  test('should modify task api1 string', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.TASK,
        conv_id: `${conv_id}`,
        data: { key: 'newparam' },
        ref: `testModify`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 3000));

    const responseShowTask1 = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask1.body.ops[0].obj).toEqual('task');
    expect(responseShowTask1.body.ops[0].task_id).toEqual(task_id);
    expect(responseShowTask1.body.ops[0].data.key).toEqual('newparam');
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
