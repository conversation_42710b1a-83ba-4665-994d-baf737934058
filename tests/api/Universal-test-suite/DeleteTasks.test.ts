import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
  requestShow,
} from '../../../application/api/ApiObj';

describe('Delete tasks', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let task_id: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Delay`);
    conv_id = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_id, company_id);
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
  });

  test('should delete task in final node', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: { a: 1 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));

    const responseList = await requestList(api, final_node_ID, conv_id, company_id, 10);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(1);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.TASK,
        obj_id: task_id,
        node_id: final_node_ID,
        conv_id,
      }),
    );
    expect(response.status).toBe(200);
    await new Promise(r => setTimeout(r, 1000));

    const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask.body.ops[0].proc).toEqual('error');
    expect(responseShowTask.body.ops[0].description).toEqual('task not found');

    const responseListNode = await requestList(api, final_node_ID, conv_id, company_id, 10);
    expect(responseListNode.status).toBe(RESP_STATUS.OK);
    expect(responseListNode.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseListNode.body.ops[0].count).toEqual(0);
    expect(responseListNode.body.ops[0].list).toBeArrayOfSize(0);
  });

  test(`should list conv get_counter after delete task final_node`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
        get_counter: true,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.request_proc).toBe(PROC_STATUS.OK);
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'final').count).toEqual(0);
  });

  test(`should delete task after reset node`, async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: { a: 1 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));

    const responseReset = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESET,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID,
        company_id,
        conv_id,
      }),
    );
    expect(responseReset.status).toBe(RESP_STATUS.OK);
    expect(responseReset.body.request_proc).toBe(PROC_STATUS.OK);
    expect(responseReset.body.ops[0].obj).toEqual('node');

    const responseList = await requestList(api, final_node_ID, conv_id, company_id, 10);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(0);
    expect(responseList.body.ops[0].list).toBeArrayOfSize(1);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.TASK,
        obj_id: task_id,
        node_id: final_node_ID,
        conv_id,
      }),
    );
    expect(response.status).toBe(200);
    await new Promise(r => setTimeout(r, 2000));

    const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask.body.ops[0].proc).toEqual('error');
    expect(responseShowTask.body.ops[0].description).toEqual('task not found');

    const responseListNode = await requestList(api, final_node_ID, conv_id, company_id, 10);
    expect(responseListNode.status).toBe(RESP_STATUS.OK);
    expect(responseListNode.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseListNode.body.ops[0].count).toEqual(0);
    expect(responseListNode.body.ops[0].list).toBeArrayOfSize(0);
  });

  test('should delete task in process node', async () => {
    const responseCreateDelay = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id: conv_id,
        title: 'process',
        obj_type: 0,
        logics: [],
        semaphors: [{ type: 'time', value: 10, dimension: 'sec', to_node_id: final_node_ID }],
        version: 22,
      }),
    );
    expect(responseCreateDelay.status).toBe(RESP_STATUS.OK);
    expect(responseCreateDelay.body.ops[0].proc).toEqual('ok');

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(200);
    expect(responseCommit.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: { a: 1 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));

    const responseList = await requestList(api, process_node_ID, conv_id, company_id, 10);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(1);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.TASK,
        obj_id: task_id,
        node_id: process_node_ID,
        conv_id,
      }),
    );
    expect(response.status).toBe(200);
    await new Promise(r => setTimeout(r, 2000));

    const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask.body.ops[0].proc).toEqual('error');
    expect(responseShowTask.body.ops[0].description).toEqual('task not found');

    const responseListNode = await requestList(api, process_node_ID, conv_id, company_id, 10);
    expect(responseListNode.status).toBe(RESP_STATUS.OK);
    expect(responseListNode.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseListNode.body.ops[0].count).toEqual(0);
    expect(responseListNode.body.ops[0].list).toBeArrayOfSize(0);
  });

  test(`should list conv get_counter after delete task process_node`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
        get_counter: true,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.request_proc).toBe(PROC_STATUS.OK);
    expect((response.body.ops[0].list as Array<any>).find(item => item.title === 'process').count).toEqual(0);
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
