import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS } from '../../../utils/corezoidRequest';
import {
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
  requestShow,
} from '../../../application/api/ApiObj';

describe('Cross-company task copy', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;

  let conv_id_company: number;
  let process_node_ID_company: string | number;
  let final_node_ID_company: string | number;

  let conv_id_mycorezoid: number;
  let final_node_ID_mycorezoid: string | number;

  let task_id: string | number;
  let ref: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseConvCompany = await requestCreateObj(
      api,
      OBJ_TYPE.CONV,
      company_id,
      `Process1_Company_${Date.now()}`,
    );
    conv_id_company = responseConvCompany.body.ops[0].obj_id;

    const responseConvMyCorezoid = await requestCreateObj(
      api,
      OBJ_TYPE.CONV,
      null,
      `Process2_MyCorezoid_${Date.now()}`,
    );
    conv_id_mycorezoid = responseConvMyCorezoid.body.ops[0].obj_id;

    const responseListCompany = await requestListConv(api, conv_id_company, company_id);
    expect(responseListCompany.status).toBe(200);
    process_node_ID_company = (responseListCompany.body.ops[0].list as Array<any>).find(
      item => item.title === 'process',
    ).obj_id;
    final_node_ID_company = (responseListCompany.body.ops[0].list as Array<any>).find(item => item.title === 'final')
      .obj_id;

    const responseListMyCorezoid = await requestListConv(api, conv_id_mycorezoid, null);
    expect(responseListMyCorezoid.status).toBe(200);
    (responseListMyCorezoid.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID_mycorezoid = (responseListMyCorezoid.body.ops[0].list as Array<any>).find(
      item => item.title === 'final',
    ).obj_id;

    const responseModify = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID_company,
        conv_id: conv_id_company,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api_copy',
            sync: false,
            err_node_id: '',
            conv_id: '{{conv}}',
            mode: 'create',
            ref: `ref_${Date.now()}`,
            data: {
              source_process: 'company_process',
              timestamp: `${Date.now()}`,
              test_data: 'cross_company_copy_test',
            },
            data_type: {
              source_process: 'string',
              timestamp: 'number',
              test_data: 'string',
            },
            group: 'all',
          },
          {
            to_node_id: final_node_ID_company,
            format: 'json',
            type: 'go',
            index: 1,
            node_title: 'final',
          },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModify.status).toBe(RESP_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_id_company, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);
  });

  test('should copy task from company process to my corezoid process', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id_company,
        data: {
          test_id: '123456',
          conv: conv_id_mycorezoid,
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;
    ref = responseTask.body.ops[0].ref;

    await new Promise(r => setTimeout(r, 3000));

    const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id: conv_id_company });
    expect(responseShowTask.status).toBe(RESP_STATUS.OK);
    expect(responseShowTask.body.ops[0].obj).toEqual('task');
    expect(responseShowTask.body.ops[0].task_id).toEqual(task_id);
    expect(responseShowTask.body.ops[0].data.test_id).toBe('123456');
    expect(responseShowTask.body.ops[0].data.__conveyor_copy_task_result__).toBe('ok');

    const responseListMyCorezoid = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.NODE,
        conv_id: conv_id_mycorezoid,
        obj_id: final_node_ID_mycorezoid,
        limit: 1,
        company_id: null,
      }),
    );
    expect(responseListMyCorezoid.status).toBe(RESP_STATUS.OK);
    expect(responseListMyCorezoid.body.ops[0].count).toBeGreaterThan(0);
    const copiedTask = responseListMyCorezoid.body.ops[0].list[0];
    expect(copiedTask.data.test_id).toBe('123456');
    expect(copiedTask.data.source_process).toBe('company_process');
    expect(copiedTask.data.test_data).toBe('cross_company_copy_test');
    expect(copiedTask.data.__copy_conv_id__).toBe(conv_id_company);
    expect(copiedTask.data.__copy_ref_id__).toBe(`${ref}`);
  });

  afterAll(async () => {
    const responseDeleteCompany = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id_company, company_id);
    expect(responseDeleteCompany.status).toBe(RESP_STATUS.OK);

    const responseDeleteMyCorezoid = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id_mycorezoid, null);
    expect(responseDeleteMyCorezoid.status).toBe(RESP_STATUS.OK);
  });
});
