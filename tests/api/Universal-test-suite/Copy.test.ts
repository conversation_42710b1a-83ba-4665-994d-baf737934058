import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS } from '../../../utils/corezoidRequest';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
  requestShow,
} from '../../../application/api/ApiObj';

describe('Copy from alias', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id_copy: number;
  let conv_id: number;
  let owner_id_c: number;
  let final_node_ID: string | number;
  let process_node_ID_c: string | number;
  let final_node_ID_c: string | number;
  let task_id_c: string | number;
  let ref: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseCopy = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Logic_API_Copy_Create`);
    conv_id_copy = responseCopy.body.ops[0].obj_id;

    const responseConv = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Conv`);
    conv_id = responseConv.body.ops[0].obj_id;

    const responseListCopy = await requestListConv(api, conv_id_copy, company_id);
    expect(responseListCopy.status).toBe(200);
    process_node_ID_c = (responseListCopy.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID_c = (responseListCopy.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
    owner_id_c = responseListCopy.body.ops[0].owner_id;

    const responseListConv = await requestListConv(api, conv_id, company_id);
    expect(responseListConv.status).toBe(200);
    final_node_ID = (responseListConv.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateCopy = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID_c,

        conv_id: conv_id_copy,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api_copy',
            sync: false,
            err_node_id: '',
            conv_id: `${conv_id}`,
            mode: 'create',
            ref: `ref_${Date.now()}`,
            data: {
              Object: 'Table',
              Age: '999',
              Boolean: 'true',
              omnonmnom: '[{"a":"aaa","b":["sad","asd","sad","as","d"]}]',
            },
            data_type: { Object: 'string', Age: 'number', Boolean: 'boolean', omnonmnom: 'array' },
            group: 'all',
          },
          {
            to_node_id: final_node_ID_c,
            format: 'json',
            type: 'go',
            index: 1,
            node_title: 'final',
            conv_owner_id: owner_id_c,
            conv_owner_name: 'DTPO',
          },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateCopy.status).toBe(RESP_STATUS.OK);

    const responseCommitCopy = await requestConfirm(api, conv_id_copy, company_id);
    expect(responseCommitCopy.status).toBe(RESP_STATUS.OK);
  });

  test('should Send task to conv with Code ', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id_copy,
        data: { ekbId: '1999999051', Id: '123' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id_c = responseTask.body.ops[0].obj_id;
    ref = responseTask.body.ops[0].ref;
  });

  test('should check Code ', async () => {
    await new Promise(r => setTimeout(r, 2000));

    const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id_c, { conv_id: conv_id_copy });
    expect(responseShowTask.status).toBe(RESP_STATUS.OK);
    expect(responseShowTask.body.ops[0].obj).toEqual('task');
    expect(responseShowTask.body.ops[0].task_id).toEqual(task_id_c);
    expect(responseShowTask.body.ops[0].data.Id).toBe('123');
    expect(responseShowTask.body.ops[0].data.__conveyor_copy_task_result__).toBe('ok');
    expect(responseShowTask.body.ops[0].data.ekbId).toBe('1999999051');

    const responseList = await requestList(api, final_node_ID, conv_id_copy, company_id, 1);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(1);
  });

  test('should check Conv', async () => {
    const responseShow = await requestList(api, final_node_ID, conv_id, company_id, 10);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].count).toBe(1);
    expect(responseShow.body.ops[0].list[0].data.ekbId).toBe('1999999051');
    expect(responseShow.body.ops[0].list[0].data.__copy_conv_id__).toBe(conv_id_copy);
    expect(responseShow.body.ops[0].list[0].data.__copy_ref_id__).toBe(`${ref}`);
  });

  afterAll(async () => {
    const responseDeleteModify = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(responseDeleteModify.status).toBe(RESP_STATUS.OK);

    const responseDeleteCallback = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id_copy, company_id);
    expect(responseDeleteCallback.status).toBe(RESP_STATUS.OK);
  });
});
