import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';
import { axiosInstance } from '../../../application/api/AxiosClient';
import { addMsg } from 'jest-html-reporters/helper';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
  requestShow,
} from '../../../application/api/ApiObj';

describe('Alias in Company', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let taskID: string | number;
  let taskID2: string | number;
  let company_id: any;
  let newConv: number;
  let newSD: number;
  let newAlias: string | number;
  let short_name: string | number;
  let callback_hash: string | number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let process_node_IDSD: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    short_name = `alias${Date.now()}`;

    const responseConv = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Conv`);
    newConv = responseConv.body.ops[0].obj_id;

    const responseListConv = await requestListConv(api, newConv, company_id);
    expect(responseListConv.status).toBe(200);
    process_node_ID = (responseListConv.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseListConv.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseSD = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `SD`, 0, 'state');
    newSD = responseSD.body.ops[0].obj_id;

    const responseListSD = await requestListConv(api, newSD, company_id);
    expect(responseListSD.status).toBe(200);
    process_node_IDSD = (responseListSD.body.ops[0].list as Array<any>).find(item => item.title === 'New user').obj_id;

    const responseTaskToSD = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: newSD,
        action: 'user',
        data: { a: 'test_alias', url: 'https://www.w3schools.com/php/books.xml' },
        ref: short_name,
      }),
    );
    expect(responseTaskToSD.status).toBe(200);
  });

  test('should create alias', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,

        short_name,
        description: `aliasDesk-${Date.now()}`,
        title: `aliasTitle-${Date.now()}`,
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    newAlias = response.body.ops[0].obj_id;
  });

  test('should create callback_hash alias', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CALLBACK_HASH,
        company_id,

        alias_id: newAlias,
        obj_type: 'alias',
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    callback_hash = response.body.ops[0].callback_hash;
  });

  test('should link alias process true', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
        link: true,
        obj_to_type: OBJ_TYPE.CONV,
        obj_to_id: newConv,
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].obj).toEqual('alias');
  });

  test('should create task by direct url by alias', async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/@${short_name}/0/${company_id}/${callback_hash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { a: 'test' },
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(200);
    expect(response.data.ops.proc).toEqual('ok');
    expect(response.data.ops.obj).toEqual('task');
    expect(response.data.ops.ref).toEqual(null);
    taskID = response.data.ops.obj_id;

    await new Promise(r => setTimeout(r, 5000));

    const responseTask = await requestShow(api, OBJ_TYPE.TASK, taskID, { conv_id: newConv });
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    expect(responseTask.body.ops[0].task_id).toEqual(taskID);

    const responseList = await requestList(api, final_node_ID, newConv, company_id);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(1);
  });

  test('should create task {} by direct url by alias', async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/@${short_name}/0/${company_id}/${callback_hash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: {},
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(200);
    expect(response.data.ops.proc).toEqual('ok');
    expect(response.data.ops.obj).toEqual('task');
    expect(response.data.ops.ref).toEqual(null);
    taskID2 = response.data.ops.obj_id;

    await new Promise(r => setTimeout(r, 5000));

    const responseList = await requestList(api, final_node_ID, newConv, company_id);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(2);
    expect(responseList.body.ops[0].list[0].data).toEqual({});

    const responseTask = await requestShow(api, OBJ_TYPE.TASK, taskID2, { conv_id: newConv });
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    expect(responseTask.body.ops[0].task_id).toEqual(taskID2);
    expect(responseTask.body.ops[0].data).toEqual({});
  });

  test('should create task [] by direct url by alias', async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/@${short_name}/0/${company_id}/${callback_hash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: [],
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(400);
    expect(response.data.ops[0].proc).toEqual('error');
    expect(response.data.ops[0].description).toEqual('Incorrect body');
  });

  test('should create task Copy by alias', async () => {
    const responseLink = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,

        link: true,
        obj_to_type: 'conv',
        obj_to_id: newSD,
        project_id: 0,
      }),
    );
    expect(responseLink.status).toBe(200);
    expect(responseLink.body.ops[0].proc).toEqual('ok');
    expect(responseLink.body.ops[0].obj).toEqual('alias');

    const responseModifyNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,

        conv_id: newConv,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api_copy',
            sync: false,
            err_node_id: '',
            conv_id: `@${short_name}`,
            mode: 'create',
            ref: `ref_${Date.now()}`,
            data: {
              Object: 'Table',
              Age: '999',
              Boolean: 'true',
            },
            data_type: { Object: 'string', Age: 'number', Boolean: 'boolean' },
            group: 'all',
          },
          {
            to_node_id: final_node_ID,
            format: 'json',
            type: 'go',
            index: 1,
            node_title: 'final',
          },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifyNode.status).toBe(200);

    const responseCommitCopy = await requestConfirm(api, newConv, company_id);
    expect(responseCommitCopy.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        conv_id: newConv,
        action: 'user',
        data: { a: 1 },
        ref: short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].obj).toEqual('task');
    expect(response.body.ops[0].ref).toEqual(short_name);
    taskID = response.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 5000));

    const responseTask = await requestShow(api, OBJ_TYPE.TASK, taskID, { conv_id: newConv });
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    expect(responseTask.body.ops[0].task_id).toEqual(taskID);
    expect(responseTask.body.ops[0].data).toEqual({ __conveyor_copy_task_result__: 'ok', a: 1 });

    const responseList = await requestList(api, process_node_IDSD, newSD, company_id, 1);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(2);
    expect(responseList.body.ops[0].list[0].data.Age).toEqual(999);
    expect(responseList.body.ops[0].list[0].data.__copy_conv_id__).toEqual(newConv);
    expect(responseList.body.ops[0].list[0].data.a).toEqual(1);
  });

  test('should read task from SD by alias in Set_Parameter', async () => {
    const responseModifySetParameter = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,

        conv_id: newConv,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'set_param',
            err_node_id: '',
            extra: {
              param: `{{conv[@${short_name}].ref[${short_name}].a}}`,
            },
            extra_type: { param: 'string' },
          },
          {
            to_node_id: final_node_ID,
            format: 'json',
            type: 'go',
            index: 1,
            node_title: 'final',
          },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifySetParameter.status).toBe(200);

    const responseCommit = await requestConfirm(api, newConv, company_id);
    expect(responseCommit.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        conv_id: newConv,
        action: 'user',
        data: { c: 1 },
        ref: short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].obj).toEqual('task');
    expect(response.body.ops[0].ref).toEqual(short_name);
    taskID = response.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 5000));

    const responseTask = await requestShow(api, OBJ_TYPE.TASK, taskID, { conv_id: newConv });
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    expect(responseTask.body.ops[0].task_id).toEqual(taskID);
    expect(responseTask.body.ops[0].data).toEqual({ param: 'test_alias', c: 1 });

    const responseList = await requestList(api, final_node_ID, newConv, company_id, 1);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(4);
    expect(responseList.body.ops[0].list[0].data.param).toEqual('test_alias');
    expect(responseList.body.ops[0].list[0].data.c).toEqual(1);
  });

  test('should read task from SD by alias in Api_call', async () => {
    const responseModifySetParameter = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,

        conv_id: newConv,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api',
            is_migrate: true,
            rfc_format: true,
            content_type: 'application/xml',
            method: 'GET',
            extra: {},
            err_node_id: '',
            url: `{{conv[@${short_name}].ref[${short_name}].url}}`,
            extra_type: {},
            extra_headers: { 'content-type': 'application/xml; charset=utf-8' },
            max_threads: 5,
          },
          {
            to_node_id: final_node_ID,
            format: 'json',
            type: 'go',
            index: 1,
            node_title: 'final',
          },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifySetParameter.status).toBe(200);

    const responseCommit = await requestConfirm(api, newConv, company_id);
    expect(responseCommit.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,

        conv_id: newConv,
        action: 'user',
        data: { c: 1 },
        ref: short_name,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].obj).toEqual('task');
    expect(response.body.ops[0].ref).toEqual(short_name);
    taskID = response.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 5000));

    const responseTask = await requestShow(api, OBJ_TYPE.TASK, taskID, { conv_id: newConv });
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    expect(responseTask.body.ops[0].task_id).toEqual(taskID);
    expect(responseTask.body.ops[0].data.bookstore.book[0].author).toEqual({ '#value': 'Giada De Laurentiis' });

    const responseList = await requestList(api, final_node_ID, newConv, company_id, 1);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(5);
    expect(responseList.body.ops[0].list[0].data.bookstore.book[0].author).toEqual({ '#value': 'Giada De Laurentiis' });
  });

  test('should delete alias', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.ALIAS,
        obj_id: newAlias,
        company_id,
        project_id: 0,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].obj).toEqual('alias');
  });

  afterAll(async () => {
    const responseDeleteConv = await requestDeleteObj(api, OBJ_TYPE.CONV, newConv, company_id);
    expect(responseDeleteConv.status).toBe(200);

    const responseDeleteAlias = await requestDeleteObj(api, OBJ_TYPE.ALIAS, newAlias, company_id);
    expect(responseDeleteAlias.status).toBe(200);

    const responseDeleteSD = await requestDeleteObj(api, OBJ_TYPE.CONV, newSD, company_id);
    expect(responseDeleteSD.status).toBe(200);
  });
});
