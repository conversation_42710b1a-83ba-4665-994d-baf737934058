import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
  requestShow,
} from '../../../application/api/ApiObj';

describe('Code', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let task_id: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Logic_API_Copy_Create`);
    conv_id = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_id, company_id);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateCode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,

        conv_id,
        title: 'process',
        obj_type: 0,
        logics: [
          { type: 'api_code', err_node_id: '', lang: 'js', src: 'var b = *********;data.a = b;' },
          { to_node_id: final_node_ID, format: 'json', type: 'go', node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateCode.status).toBe(RESP_STATUS.OK);

    const responseCompile = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.COMPILE,
        obj: OBJ_TYPE.API_CODE,
        node_id: process_node_ID,
        conv_id,
        lang: 'js',
        src: 'var b = *********;data.a = b;',
      }),
    );
    expect(responseCompile.status).toBe(RESP_STATUS.OK);

    const responseLoad = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LOAD,
        obj: OBJ_TYPE.API_CODE,
        node_id: process_node_ID,
        conv_id,
        lang: 'js',
        src: 'var b = *********;data.a = b;',
        env: 'sandbox',
      }),
    );
    expect(responseLoad.status).toBe(RESP_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);
  });

  test('should create task code', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: { stateid: 7003 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));

    const responseShowTask = await requestShow(api, OBJ_TYPE.TASK, task_id, { conv_id });
    expect(responseShowTask.status).toBe(RESP_STATUS.OK);
    expect(responseShowTask.body.ops[0].obj).toEqual('task');
    expect(responseShowTask.body.ops[0].task_id).toEqual(task_id);
    expect(responseShowTask.body.ops[0].data).toEqual({ a: *********, stateid: 7003 });

    const responseList = await requestList(api, final_node_ID, conv_id, company_id, 1);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(1);
  });

  test('should code get', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.API_CODE,
        company_id,
        conv_id,
        node_id: process_node_ID,
        env: `production`,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.body.ops[0].src).toEqual('var b = *********;data.a = b;');

    const response2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.API_CODE,
        company_id,
        conv_id,
        node_id: process_node_ID,
        env: `sandbox`,
      }),
    );
    expect(response2.status).toBe(RESP_STATUS.OK);
    expect(response2.body.ops[0].proc).toEqual('error');
    expect(response2.body.ops[0].obj).toEqual('api_code');
    expect(response2.body.ops[0].description).toEqual('not_found');
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
