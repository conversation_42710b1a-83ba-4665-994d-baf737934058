import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
  requestShow,
} from '../../../application/api/ApiObj';

describe('ReplyCallProcess', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let task_id1: string | number;
  let task_id2: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `SetParameter`);
    conv_id = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_id, company_id);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseModify = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id: conv_id,
        title: 'process',
        obj_type: 0,
        logics: [
          {
            type: 'set_param',
            err_node_id: '',
            extra: { task: 'newtest' },
            extra_type: { task: 'string' },
          },
          { to_node_id: final_node_ID, type: 'go', node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModify.status).toBe(RESP_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);
  });

  test('should create task1', async () => {
    const modifyParam = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV_PARAMS,
        obj_id: conv_id,
        company_id,
        params: [
          { name: 'a', descr: 'aa', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'b', descr: 'bb', type: 'number', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'c', descr: 'cc', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'r.d', descr: 'rr', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 's.f', descr: 'f', type: 'string', flags: [], regex: '', regex_error_text: '' },
          { name: 'g', descr: 'g', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'arr[0].v', descr: 'g', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
        ],
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(modifyParam.status).toBe(RESP_STATUS.OK);
    expect(modifyParam.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(modifyParam.body.ops[0].obj).toEqual('conv_params');

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          a: '123',
          b: 456,
          c: '5168755504343308',
          'r.d': '897',
          s: { f: 567, r: { t: 9870 } },
          arr: [{ v: 567 }, { w: 5676 }, 678, { q: [{ e: 'e' }] }],
          g: '2345',
        },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id1 = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 6000));

    const responseShowTask1 = await requestShow(api, OBJ_TYPE.TASK, task_id1, { conv_id });
    expect(responseShowTask1.body.ops[0].obj).toEqual('task');
    expect(responseShowTask1.body.ops[0].task_id).toEqual(task_id1);
    expect(responseShowTask1.body.ops[0].data).toEqual({
      a: '123',
      arr: [{ v: 567 }, { w: 5676 }, 678, { q: [{ e: 'e' }] }],
      b: 456,
      c: '5168755504343308',
      g: '2345',
      'r.d': '897',
      s: { f: 567, r: { t: 9870 } },
      task: 'newtest',
    });

    const responseList = await requestList(api, final_node_ID, conv_id, company_id, 10);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(1);

    const modifyParam2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV_PARAMS,
        obj_id: conv_id,
        company_id,
        params: [
          { name: 'a', descr: 'aa', type: 'string', flags: ['input', 'auto-clear'], regex: '', regex_error_text: '' },
          { name: 'b', descr: 'bb', type: 'number', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'c', descr: 'cc', type: 'string', flags: ['input', 'auto-clear'], regex: '', regex_error_text: '' },
          { name: 'r.d', descr: 'rr', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 's.f', descr: 'f', type: 'string', flags: [], regex: '', regex_error_text: '' },
          { name: 's.r.t', descr: 'f', type: 'string', flags: ['auto-clear'], regex: '', regex_error_text: '' },
          { name: 'g', descr: 'g', type: 'string', flags: ['input', 'auto-clear'], regex: '', regex_error_text: '' },
          { name: 'arr[0].v', descr: 'g', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'arr[2]', descr: 'g', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          {
            name: 'arr[3].q[0].e',
            descr: 'g',
            type: 'string',
            flags: ['input', 'auto-clear'],
            regex: '',
            regex_error_text: '',
          },
        ],
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(modifyParam2.status).toBe(RESP_STATUS.OK);
    expect(modifyParam2.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(modifyParam2.body.ops[0].obj).toEqual('conv_params');

    await new Promise(r => setTimeout(r, 3000));

    const responseShowTask2 = await requestShow(api, OBJ_TYPE.TASK, task_id1, { conv_id });
    expect(responseShowTask2.body.ops[0].obj).toEqual('task');
    expect(responseShowTask2.body.ops[0].task_id).toEqual(task_id1);
    expect(responseShowTask2.body.ops[0].data).toEqual({
      a: '***',
      arr: [{ v: 567 }, { w: 5676 }, 678, { q: [{ e: '***' }] }],
      b: 456,
      c: '***',
      g: '***',
      'r.d': '897',
      s: { f: 567, r: { t: '***' } },
      task: 'newtest',
    });
  });

  test.skip('should create task2', async () => {
    const modifyParam2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV_PARAMS,
        obj_id: conv_id,
        company_id,
        params: [
          { name: 'a', descr: 'aa', type: 'string', flags: ['input', 'auto-clear'], regex: '', regex_error_text: '' },
          { name: 'b', descr: 'bb', type: 'number', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'c', descr: 'cc', type: 'string', flags: ['input', 'auto-clear'], regex: '', regex_error_text: '' },
          { name: 'r.d', descr: 'rr', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 's.f', descr: 'f', type: 'string', flags: [], regex: '', regex_error_text: '' },
          { name: 's.r.t', descr: 'f', type: 'string', flags: ['auto-clear'], regex: '', regex_error_text: '' },
          { name: 'g', descr: 'g', type: 'string', flags: ['input', 'auto-clear'], regex: '', regex_error_text: '' },
          { name: 'arr[0].v', descr: 'g', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          { name: 'arr[2]', descr: 'g', type: 'string', flags: ['input'], regex: '', regex_error_text: '' },
          {
            name: 'arr[3].q[0].e',
            descr: 'g',
            type: 'string',
            flags: ['input', 'auto-clear'],
            regex: '',
            regex_error_text: '',
          },
          { name: 'array[].test[].qw', descr: 'g', type: 'string', flags: [], regex: '', regex_error_text: '' },
          { name: 'array1[].test', descr: 'g', type: 'string', flags: [], regex: '', regex_error_text: '' },
        ],
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(modifyParam2.status).toBe(RESP_STATUS.OK);
    expect(modifyParam2.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(modifyParam2.body.ops[0].obj).toEqual('conv_params');

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_id,
        data: {
          array1: [{ test: 4567 }, { test: 987 }, { test: 123 }],
          array: [{ test: [{ qw: 4567 }] }, { test: [{ qw: 987 }] }, 6781, { test: [{ qw: 123 }] }],
        },
        ref: `123-3`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id2 = responseTask.body.ops[0].obj_id;

    const responseShowTask2 = await requestShow(api, OBJ_TYPE.TASK, task_id2, { conv_id });
    expect(responseShowTask2.body.ops[0].obj).toEqual('task');
    expect(responseShowTask2.body.ops[0].task_id).toEqual(task_id2);
    expect(responseShowTask2.body.ops[0].data).toEqual({
      array: [{ test: [{ qw: 4567 }] }, { test: [{ qw: 987 }] }, 6781, { test: [{ qw: 123 }] }],
      array1: [{ test: 4567 }, { test: 987 }, { test: 123 }],
      task: 'newtest',
    });
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
