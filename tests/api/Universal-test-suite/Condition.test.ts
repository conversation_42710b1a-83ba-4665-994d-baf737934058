import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';
import { axiosInstance } from '../../../application/api/AxiosClient';
import { addMsg } from 'jest-html-reporters/helper';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
} from '../../../application/api/ApiObj';

describe('Condition', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let node_id_ok: string | number;
  let hash: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Condition`);
    conv_id = response.body.ops[0].obj_id;
    hash = response.body.ops[0].hash;

    const responseList = await requestListConv(api, conv_id, company_id);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateNode = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        title: 'OKfinal',
        conv_id,
        obj_type: 2,
        version: 22,
      }),
    );
    node_id_ok = responseCreateNode.body.ops[0].obj_id;

    const responseCreateQueue = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,

        conv_id,
        title: 'process',
        obj_type: 0,
        logics: [
          {
            type: 'go_if_const',
            conditions: [{ param: 'a', const: '1', fun: 'eq', cast: 'string' }],
            to_node_id: node_id_ok,
          },
          {
            type: 'go_if_const',
            conditions: [{ param: 'a', const: '4', fun: 'more', cast: 'string' }],
            to_node_id: node_id_ok,
          },
          {
            type: 'go_if_const',
            conditions: [{ param: 'b', const: `(?i)(\W|^)(test|test2)(\W|$)`, fun: 'regexp', cast: 'string' }],
            to_node_id: node_id_ok,
          },
          { to_node_id: final_node_ID, type: 'go', chTime: 1511868192509 },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateQueue.status).toBe(200);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(RESP_STATUS.OK);
  });

  test('should create task =condition', async () => {
    const responseTask1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: { a: 1 },
        ref: `chupakabra`,
      }),
    );
    expect(responseTask1.status).toBe(RESP_STATUS.OK);
    expect(responseTask1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask1.body.ops[0].obj).toEqual('task');
  });

  test('should create task !=condition', async () => {
    await new Promise(r => setTimeout(r, 3000));
    const responseTask1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: { a: 2 },
        ref: `chupakabra`,
      }),
    );
    expect(responseTask1.status).toBe(RESP_STATUS.OK);
    expect(responseTask1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask1.body.ops[0].obj).toEqual('task');
  });

  test('should create task >condition', async () => {
    await new Promise(r => setTimeout(r, 3000));
    const responseTask1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: { a: 5 },
        ref: `chupakabra5`,
      }),
    );
    expect(responseTask1.status).toBe(RESP_STATUS.OK);
    expect(responseTask1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask1.body.ops[0].obj).toEqual('task');
  });

  test('should create task RegExp condition', async () => {
    await new Promise(r => setTimeout(r, 3000));
    const responseTask1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: { b: 'test2' },
        ref: `chupakabra2`,
      }),
    );
    expect(responseTask1.status).toBe(RESP_STATUS.OK);
    expect(responseTask1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask1.body.ops[0].obj).toEqual('task');

    const responseTask2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id,
        data: { b: 'test3' },
        ref: `chupakabra3`,
      }),
    );
    expect(responseTask2.status).toBe(RESP_STATUS.OK);
    expect(responseTask2.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask2.body.ops[0].obj).toEqual('task');
  });

  test('should check task data', async () => {
    await new Promise(r => setTimeout(r, 3000));

    const responseList = await requestList(api, node_id_ok, conv_id, company_id, 10);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseList.body.ops[0].obj).toEqual('node');
    expect(responseList.body.ops[0].list[2].ref).toEqual('chupakabra');
    expect(responseList.body.ops[0].list[2].status).toEqual('processed');
    expect(responseList.body.ops[0].list[2].data.a).toEqual(1);

    await new Promise(r => setTimeout(r, 2000));

    const responseList1 = await requestList(api, node_id_ok, conv_id, company_id, 10);
    expect(responseList1.status).toBe(RESP_STATUS.OK);
    expect(responseList1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseList1.body.ops[0].obj).toEqual('node');
    expect(responseList1.body.ops[0].list[0].ref).toEqual('chupakabra2');
    expect(responseList1.body.ops[0].list[0].status).toEqual('processed');
    expect(responseList1.body.ops[0].list[0].data.b).toEqual('test2');

    const responseList2 = await requestList(api, final_node_ID, conv_id, company_id, 10);
    expect(responseList2.status).toBe(RESP_STATUS.OK);
    expect(responseList2.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseList2.body.ops[0].obj).toEqual('node');
    expect(responseList2.body.ops[0].list[1].ref).toEqual('chupakabra');
    expect(responseList2.body.ops[0].list[1].status).toEqual('processed');
    expect(responseList2.body.ops[0].list[1].data.a).toEqual(2);

    const responseList3 = await requestList(api, node_id_ok, conv_id, company_id, 10);
    expect(responseList3.status).toBe(RESP_STATUS.OK);
    expect(responseList3.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseList3.body.ops[0].obj).toEqual('node');
    expect(responseList3.body.ops[0].count).toEqual(3);
    expect(responseList3.body.ops[0].list[1].ref).toEqual('chupakabra5');
    expect(responseList3.body.ops[0].list[1].status).toEqual('processed');
    expect(responseList3.body.ops[0].list[1].data.a).toEqual(5);

    const responseList4 = await requestList(api, final_node_ID, conv_id, company_id, 10);
    expect(responseList4.status).toBe(RESP_STATUS.OK);
    expect(responseList4.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseList4.body.ops[0].obj).toEqual('node');
    expect(responseList4.body.ops[0].count).toEqual(2);
    expect(responseList4.body.ops[0].list[0].ref).toEqual('chupakabra3');
    expect(responseList4.body.ops[0].list[0].status).toEqual('processed');
    expect(responseList4.body.ops[0].list[0].data.b).toEqual('test3');
  });

  test('should create task {} by direct url', async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/${conv_id}/${hash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: {},
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.ops.proc).toEqual(PROC_STATUS.OK);
    expect(response.data.ops.obj).toEqual('task');
    expect(response.data.ops.ref).toEqual(null);
  });

  test('should check task data {}', async () => {
    await new Promise(r => setTimeout(r, 3000));

    const response = await requestList(api, final_node_ID, conv_id, company_id, 10);
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.body.ops[0].count).toEqual(3);
    expect(response.body.ops[0].list[0].data).toEqual({});
  });

  test('should create task data a:2 by direct url', async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/${conv_id}/${hash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { a: 2, ref: 'chupakabra' },
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.ops.proc).toEqual(PROC_STATUS.OK);
    expect(response.data.ops.obj).toEqual('task');
    expect(response.data.ops.ref).toEqual(null);
  });

  test('should check task data a:2', async () => {
    await new Promise(r => setTimeout(r, 3000));

    const response = await requestList(api, final_node_ID, conv_id, company_id, 10);
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.body.ops[0].count).toEqual(4);
    expect(response.body.ops[0].list[0].data.a).toEqual(2);
    expect(response.body.ops[0].list[0].data.ref).toEqual('chupakabra');
  });

  test('should create task data [] by direct url', async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/2/json/public/${conv_id}/${hash}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: [],
    });
    await addMsg({ message: JSON.stringify(response.data, null, 2), context: '' });
    expect(response.status).toBe(RESP_STATUS.BAD_REQUEST);
    expect(response.data.ops[0].proc).toEqual(PROC_STATUS.ERROR);
    expect(response.data.ops[0].description).toEqual('Incorrect body');
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
