import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import { requestConfirm, requestDeleteObj, requestCreateObj, requestListConv } from '../../../application/api/ApiObj';

describe('Debag', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let start_node_ID: string | number;
  let delay_node_id: string | number;
  let task_id: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Conv`);
    conv_id = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_id, company_id);
    expect(responseList.status).toBe(200);
    process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;
    start_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'start').obj_id;

    const responseCreateSetParam = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: process_node_ID,
        conv_id,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          { type: 'set_param', extra: { object: '{{object}}' }, extra_type: { object: 'string' }, err_node_id: '' },
          { to_node_id: final_node_ID, type: 'go', chTime: 1511868192509 },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateSetParam.status).toBe(200);

    const responseCreateDelay = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.NODE,
        company_id,
        conv_id,
        title: 'delay',
        id: process_node_ID,
        obj_type: 0,
        version: 22,
      }),
    );
    delay_node_id = responseCreateDelay.body.ops[0].obj_id;

    const responseModifyDelay = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: start_node_ID,
        conv_id,
        title: 'start',
        obj_type: 1,
        logics: [{ to_node_id: delay_node_id, type: 'go' }],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseModifyDelay.status).toBe(200);

    const responseModifySemaphors = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: delay_node_id,
        conv_id,
        title: 'Process',
        obj_type: 0,
        logics: [],
        semaphors: [{ type: 'time', value: 30, dimension: 'sec', to_node_id: process_node_ID }],
        version: 22,
      }),
    );
    expect(responseModifySemaphors.status).toBe(200);

    const responseCommit = await requestConfirm(api, conv_id, company_id);
    expect(responseCommit.status).toBe(200);
  });

  test('should Delay to debag', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        status: 'debug',
        title: 'Conv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].new_status).toEqual(5);
    expect(response.body.ops[0].old_status).toEqual(1);
  });

  test('should Delay Send task to conv with Set param ', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id,
        data: { s: 'new' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(200);
    expect(responseTask.body.ops[0].proc).toEqual('ok');
    expect(responseTask.body.ops[0].obj).toEqual('task');
    task_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id,
        ref_or_obj_id: task_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].proc).toEqual('ok');
    expect(responseShow.body.ops[0].data.s).toBe('new');
  });

  test('should Delay Next step logic to delay node', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STEP_NEXT,
        obj: OBJ_TYPE.TASK,
        obj_id: task_id,
        company_id,
        conv_id,
        data: { a: 'test' },
        branch: 'logic',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data.a).toBe('test');
    expect(response.body.ops[0].prev_node_name).toBe('start');
  });

  test('should Delay Next step timer to set param', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STEP_NEXT,
        obj: OBJ_TYPE.TASK,
        obj_id: task_id,
        company_id,
        conv_id,
        data: { a: 'test' },
        branch: 'timer',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data.a).toBe('test');
    expect(response.body.ops[0].prev_semaphor[0].type).toBe('time');
    expect(response.body.ops[0].prev_semaphor[0].proc).toBe('ok');
  });

  test('should Delay Step prev to delay', async () => {
    await new Promise(r => setTimeout(r, 4000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STEP_PREV,
        obj: OBJ_TYPE.TASK,
        obj_id: task_id,
        company_id,
        conv_id,
        data: { a: 'test' },
        branch: 'logic',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data.a).toBe('test');
    expect(response.body.ops[0].prev_node_name).toBe('process');
    expect(response.body.ops[0].history).toBeArray();
  });

  test('should Delay Next step to set param', async () => {
    await new Promise(r => setTimeout(r, 4000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STEP_NEXT,
        obj: OBJ_TYPE.TASK,
        obj_id: task_id,
        company_id,
        conv_id,
        data: { a: 'test' },
        branch: 'timer',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data.a).toBe('test');
    expect(response.body.ops[0].prev_node_name).toBe('Process');
    expect(response.body.ops[0].logic[0].type).toBe('set_param');
  });

  test('should Delay Next step to final', async () => {
    await new Promise(r => setTimeout(r, 4000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STEP_NEXT,
        obj: OBJ_TYPE.TASK,
        obj_id: task_id,
        company_id,
        conv_id,
        data: { a: 'test' },
        branch: 'logic',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data.a).toBe('test');
    expect(response.body.ops[0].node_name).toBe('final');
    expect(response.body.ops[0].data.object).toBe('');
  });

  test('should Delay Send task to conv', async () => {
    await new Promise(r => setTimeout(r, 2000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id,
        data: { s: 'new' },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].obj).toEqual('task');
    task_id = response.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id,
        ref_or_obj_id: task_id,
      }),
    );
    expect(responseShow.status).toBe(200);
    expect(responseShow.body.ops[0].proc).toEqual('ok');
    expect(responseShow.body.ops[0].data.s).toBe('new');
  });

  test('should Delay Step goto api', async () => {
    await new Promise(r => setTimeout(r, 4000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STEP_GOTO,
        obj: OBJ_TYPE.TASK,
        obj_id: task_id,
        company_id,
        conv_id,
        data: { a: 'test' },
        node_id: process_node_ID,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data.a).toBe('test');
    expect(response.body.ops[0].prev_node_name).toBe('start');
    expect(response.body.ops[0].logic[0].type).toBe('set_param');
  });

  test('should Delay Next step final', async () => {
    await new Promise(r => setTimeout(r, 4000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.STEP_NEXT,
        obj: OBJ_TYPE.TASK,
        obj_id: task_id,
        company_id,
        conv_id,
        data: { a: 'test' },
        branch: 'logic',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].data.a).toBe('test');
    expect(response.body.ops[0].node_name).toBe('final');
    expect(response.body.ops[0].data.object).toBe('');
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(response.status).toBe(200);
  });
});
