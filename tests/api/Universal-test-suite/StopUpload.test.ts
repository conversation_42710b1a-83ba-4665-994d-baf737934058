import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import { SchemaValidator } from '../../../application/api/SchemaValidator';
import stopUpload from '../../api/corezoid-api/schemas/v2/download-upload/stopUpload.Schema.json';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';

describe('Stop Upload ', () => {
  let api: ApiKeyClient;
  let apikey: Api<PERSON>ey;
  let key: string;
  let secret: string;
  let hashUploadObject: string;
  let company_id: any;
  let deleted_proj_title: number;
  let obj_list: any;
  const baseUrl = ConfigurationManager.getConfiguration().getApiUrl();
  const scriptPath = 'tests/api/corezoid-api/sh/uploadObjectRootFolder.sh';
  const exec = promisify(execCallback);

  async function clearTrash(): Promise<void> {
    const responseListDelObj = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
        filter: 'deleted',
      }),
    );
    expect(responseListDelObj.status).toBe(RESP_STATUS.OK);
    obj_list = responseListDelObj.body.ops[0].list;

    for (const item in obj_list) {
      const responseDestroy = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DESTROY,
          obj: obj_list[item].obj_type,
          obj_id: obj_list[item].obj_id,
          company_id,
        }),
      );
      expect(responseDestroy.status).toBe(200);
    }
  }
  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
    key = apikey.key;
    secret = apikey.secret;

    await clearTrash();
  });

  const testCasesPositive = [
    {
      description: 'folder',
      file_name: 'folder_for_test_upload_univer.suite_70conv.zip',
      title: 'for_test_upload',
    },
    {
      description: 'project',
      file_name: 'project_for_test_upload.zip',
      title: 'New Project',
    },
  ];

  describe.each(testCasesPositive)('$description', ({ description, file_name, title }) => {
    test('should be in trash ', async () => {
      const { stdout } = await exec(scriptPath, {
        env: {
          API_KEY_LOGIN: key,
          API_KEY_SECRET: secret,
          API_URL: baseUrl + 'api/2/upload',
          VALIDATE_SCHEME: 'false',
          REWRITE_ALIAS: 'false',
          WITH_ALIAS: 'true',
          COMPANY_ID: company_id,
          FILE_NAME: file_name,
          ASYNC: 'true',
        },
      });
      hashUploadObject = stdout.slice(76, 76 + 146);
      expect(stdout).toContain(`{"request_proc":"ok","ops":[{"obj":"obj_scheme","proc":"ok","hash"`);

      await new Promise(r => setTimeout(r, 3000));

      const responseStop = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.STOP,
          obj: OBJ_TYPE.UPLOAD,
          company_id,
          hash: hashUploadObject,
          obj_type: 'obj_scheme',
        }),
      );
      expect(responseStop.status).toBe(RESP_STATUS.OK);
      const { proc, obj } = responseStop.body.ops[0];
      expect(proc).toBe(PROC_STATUS.OK);
      expect(obj).toBe(OBJ_TYPE.UPLOAD);
      SchemaValidator.validate(stopUpload, responseStop.body);

      await new Promise(r => setTimeout(r, 1000));

      const responseDeletedObject = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.FOLDER,
          obj_id: 0,
          company_id,
          filter: 'deleted',
        }),
      );
      expect(responseDeletedObject.status).toBe(RESP_STATUS.OK);
      deleted_proj_title = (responseDeletedObject.body.ops[0].list as Array<any>).find(
        item => item.obj_type === description,
      ).title;
      expect(deleted_proj_title).toBe(title);
    });
  });

  afterAll(async () => {
    await clearTrash();
  });
});
