import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import {
  requestDeleteObj,
  requestLinkObj,
  requestCreateObjNew,
  requestListConv,
  requestConfirm,
  requestCreateTask,
} from '../../../application/api/ApiObj';

describe('Projects_collaboration', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;

  let newProject1: string | number;
  let newProject2: string | number;
  let newStage1: string | number;
  let newStage2: string | number;
  let newConv1: number;
  let newConv2: number;
  let newAlias1: string | number;
  let newAlias2: string | number;

  let newConvSD1: string | number;
  let newAliasSD: string | number;
  let short_nameSD1: string;

  let short_name1: string;
  let project_short_name1: string;
  let stage_short_name1: string;

  let short_name2: string;
  let project_short_name2: string;
  let stage_short_name2: string;
  let process_node_ID1: string;
  let final_node_ID1: string;
  let final_node_ID2: string;

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;

      project_short_name1 = `project1-${Date.now()}`;
      stage_short_name1 = `stage1-${Date.now()}`;
      short_name1 = `alias1-${Date.now()}`;
      short_nameSD1 = `aliassd-${Date.now()}`;

      project_short_name2 = `project2-${Date.now()}`;
      stage_short_name2 = `stage2-${Date.now()}`;
      short_name2 = `alias2-${Date.now()}`;

      const responseProject1 = await requestCreateObjNew(api, OBJ_TYPE.PROJECT, company_id, `Project1_${Date.now()}`, {
        short_name: project_short_name1,
        description: 'test',
        stages: [],
      });
      newProject1 = responseProject1.body.ops[0].obj_id;

      const responseStage1 = await requestCreateObjNew(api, OBJ_TYPE.STAGE, company_id, `Stage1_${Date.now()}`, {
        short_name: stage_short_name1,
        description: 'test',
        project_id: newProject1,
      });
      newStage1 = responseStage1.body.ops[0].obj_id;

      const responseConv1 = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, `Conv1_${Date.now()}`, {
        conv_type: 'process',
        stage_id: newStage1,
        project_id: newProject1,
      });
      newConv1 = responseConv1.body.ops[0].obj_id;

      const responseAlias1 = await requestCreateObjNew(api, OBJ_TYPE.ALIAS, company_id, `Alias1_${Date.now()}`, {
        short_name: short_name1,
        project_id: newProject1,
        stage_id: newStage1,
      });
      newAlias1 = responseAlias1.body.ops[0].obj_id;

      await requestLinkObj(api, OBJ_TYPE.ALIAS, company_id, newAlias1, true, newConv1, 'conv');

      const responseListConv1 = await requestListConv(api, newConv1, company_id);
      process_node_ID1 = (responseListConv1.body.ops[0].list as Array<any>).find(item => item.title === 'process')
        .obj_id;
      final_node_ID1 = (responseListConv1.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

      const responseConvSD1 = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, `ConvSD_${Date.now()}`, {
        conv_type: 'state',
        stage_id: newStage1,
        project_id: newProject1,
      });
      newConvSD1 = responseConvSD1.body.ops[0].obj_id;

      const responseAliasSD1 = await requestCreateObjNew(api, OBJ_TYPE.ALIAS, company_id, `AliasSD_${Date.now()}`, {
        short_name: short_nameSD1,
        project_id: newProject1,
        stage_id: newStage1,
      });
      newAliasSD = responseAliasSD1.body.ops[0].obj_id;

      await requestLinkObj(api, OBJ_TYPE.ALIAS, company_id, newAliasSD, true, newConvSD1, 'conv');

      const responseProject2 = await requestCreateObjNew(api, OBJ_TYPE.PROJECT, company_id, `Project2_${Date.now()}`, {
        short_name: project_short_name2,
        description: 'test',
        stages: [],
      });
      newProject2 = responseProject2.body.ops[0].obj_id;

      const responseStage2 = await requestCreateObjNew(api, OBJ_TYPE.STAGE, company_id, `Stage2_${Date.now()}`, {
        short_name: stage_short_name2,
        description: 'test',
        project_id: newProject2,
      });
      newStage2 = responseStage2.body.ops[0].obj_id;

      const responseConv2 = await requestCreateObjNew(api, OBJ_TYPE.CONV, company_id, `Conv2_${Date.now()}`, {
        conv_type: 'process',
        stage_id: newStage2,
        project_id: newProject2,
      });
      newConv2 = responseConv2.body.ops[0].obj_id;

      const responseAlias2 = await requestCreateObjNew(api, OBJ_TYPE.ALIAS, company_id, `Alias2_${Date.now()}`, {
        short_name: short_name2,
        project_id: newProject2,
        stage_id: newStage2,
      });
      newAlias2 = responseAlias2.body.ops[0].obj_id;

      await requestLinkObj(api, OBJ_TYPE.ALIAS, company_id, newAlias2, true, newConv2, 'conv');

      const responseListConv2 = await requestListConv(api, newConv2, company_id);
      final_node_ID2 = (responseListConv2.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

      const responseModifyConv2 = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID1,
          conv_id: newConv2,
          title: 'process',
          obj_type: 0,
          logics: [
            {
              type: 'api_rpc_reply',
              mode: 'key_value',
              throw_exception: false,
              res_data: {},
              res_data_type: {},
            },
            {
              to_node_id: final_node_ID2,
              type: 'go',
            },
          ],
          semaphors: [],
          version: 22,
        }),
      );
      expect(responseModifyConv2.status).toBe(200);

      const responseCommit = await requestConfirm(api, newConv2, company_id);
      expect(responseCommit.status).toBe(200);

      await requestCreateTask(api, OBJ_TYPE.TASK, company_id, newConvSD1, 'ref', {
        conv_id: `${newConv2}`,
        stage_id: `${newStage2}`,
        project_id: `${newProject2}`,
        alias: `${short_name2}`,
        stage_short_name: `${stage_short_name2}`,
        project_short_name: `${project_short_name2}`,
      });
    },
  );

  const testCasesPositive = [
    {
      description: 'Conv/Stage/Project - direct IDs number',
      conv_id: (): string | number => newConv2,
      stage_id: (): string | number => newStage2,
      project_id: (): string | number => newProject2,
      conv_idCalled: (): string | number => newConv2,
      final_nodeCalled: (): string => final_node_ID2,
    },
    {
      description: '[between project]Conv/Stage/Project - aliases',
      conv_id: (): string => `@${short_name2}`,
      stage_id: (): string => `@${stage_short_name2}`,
      project_id: (): string => `@${project_short_name2}`,
      conv_idCalled: (): string | number => newConv2,
      final_nodeCalled: (): string => final_node_ID2,
    },
    {
      description: '[between project]Dynamic short names',
      conv_id: (): string => `@{{conv_sname}}`,
      stage_id: (): string => `@{{stage_sname}}`,
      project_id: (): string => `@{{project_sname}}`,
      conv_idCalled: (): string | number => newConv2,
      final_nodeCalled: (): string => final_node_ID2,
    },
    {
      description: '[between project] IDs from construction',
      conv_id: (): string => `{{conv[${newConvSD1}].ref[ref].conv_id}}`,
      stage_id: (): string => `{{conv[${newConvSD1}].ref[ref].stage_id}}`,
      project_id: (): string => `{{conv[${newConvSD1}].ref[ref].project_id}}`,
      conv_idCalled: (): string | number => newConv2,
      final_nodeCalled: (): string => final_node_ID2,
    },
  ];

  describe.each(testCasesPositive)(
    '$description',
    ({ conv_id, stage_id, project_id, conv_idCalled, final_nodeCalled }): void => {
      test(`should copy_task`, async (): Promise<void> => {
        const responseModify = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MODIFY,
            obj: OBJ_TYPE.NODE,
            obj_id: process_node_ID1,
            conv_id: newConv1,
            title: 'process',
            description: '',
            obj_type: 0,
            logics: [
              {
                type: 'api_copy',
                sync: false,
                err_node_id: '',
                conv_id: conv_id(),
                stage_id: stage_id(),
                project_id: project_id(),
                mode: 'create',
                ref: `ref_${Date.now()}`,
                data: {
                  Object: 'Table',
                },
                data_type: { Object: 'string' },
                group: 'all',
              },
              {
                to_node_id: final_node_ID1,
                format: 'json',
                type: 'go',
                index: 1,
                node_title: 'final',
              },
            ],
            semaphors: [],
            version: 22,
          }),
        );
        expect(responseModify.status).toBe(200);

        const responseCommit = await requestConfirm(api, newConv1, company_id);
        expect(responseCommit.status).toBe(200);

        const responseTask = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.TASK,
            conv_id: newConv1,
            data: {
              Id: '123',
              conv: newConv2,
              stage: newStage2,
              project: newProject2,
              conv_sname: short_name2,
              stage_sname: stage_short_name2,
              project_sname: project_short_name2,
            },
            ref: `ref_${Date.now()}`,
          }),
        );
        expect(responseTask.status).toBe(200);
        const task_id = responseTask.body.ops[0].obj_id;
        const ref = responseTask.body.ops[0].ref;

        await new Promise(r => setTimeout(r, 2000));

        const responseShow = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.SHOW,
            obj: OBJ_TYPE.TASK,
            conv_id: newConv1,
            ref_or_obj_id: task_id,
          }),
        );
        expect(responseShow.status).toBe(200);
        expect(responseShow.body.ops[0].data.Id).toBe('123');

        const responseListNode = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.NODE,
            conv_id: conv_idCalled(),
            obj_id: final_nodeCalled(),
            limit: 1,
          }),
        );
        expect(responseListNode.status).toBe(200);
        expect(responseListNode.body.ops[0].proc).toEqual('ok');
        expect(responseListNode.body.ops[0].list[0].data.Id).toBe('123');
        expect(responseListNode.body.ops[0].list[0].data.__copy_conv_id__).toBe(newConv1);
        expect(responseListNode.body.ops[0].list[0].data.__copy_ref_id__).toBe(ref);
        expect(responseListNode.body.ops[0].list[0].data.Object).toBe('Table');
      });

      test(`should Call_process`, async (): Promise<void> => {
        const responseModify = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MODIFY,
            obj: OBJ_TYPE.NODE,
            obj_id: process_node_ID1,
            conv_id: newConv1,
            title: 'process',
            description: '',
            obj_type: 0,
            logics: [
              {
                type: 'api_rpc',
                sync: false,
                err_node_id: '',
                conv_id: conv_id(),
                stage_id: stage_id(),
                project_id: project_id(),
                mode: 'create',
                ref: `ref_${Date.now()}`,
                extra: {
                  Object: 'Table',
                },
                extra_type: { Object: 'string' },
                group: 'all',
              },
              {
                to_node_id: final_node_ID1,
                format: 'json',
                type: 'go',
                index: 1,
                node_title: 'final',
              },
            ],
            semaphors: [],
            version: 22,
          }),
        );
        expect(responseModify.status).toBe(200);

        const responseCommit = await requestConfirm(api, newConv1, company_id);
        expect(responseCommit.status).toBe(200);

        const responseTask = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.TASK,
            conv_id: newConv1,
            data: {
              Id: '123',
              conv: newConv2,
              stage: newStage2,
              project: newProject2,
              conv_sname: short_name2,
              stage_sname: stage_short_name2,
              project_sname: project_short_name2,
            },
            ref: `ref_${Date.now()}`,
          }),
        );
        expect(responseTask.status).toBe(200);
        const task_id = responseTask.body.ops[0].obj_id;
        const ref = responseTask.body.ops[0].ref;

        await new Promise(r => setTimeout(r, 2000));

        const responseShow = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.SHOW,
            obj: OBJ_TYPE.TASK,
            conv_id: newConv1,
            ref_or_obj_id: task_id,
          }),
        );
        expect(responseShow.status).toBe(200);
        expect(responseShow.body.ops[0].data.Id).toBe('123');

        const responseListNode = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LIST,
            obj: OBJ_TYPE.NODE,
            conv_id: conv_idCalled(),
            obj_id: final_nodeCalled(),
            limit: 1,
          }),
        );
        expect(responseListNode.status).toBe(200);
        expect(responseListNode.body.ops[0].proc).toEqual('ok');
        expect(responseListNode.body.ops[0].list[0].data.Id).toBe('123');
        expect(responseListNode.body.ops[0].list[0].data.__rpc_conv_id__).toBe(newConv1);
        expect(responseListNode.body.ops[0].list[0].data.__rpc_ref_id__).toBe(ref);
        expect(responseListNode.body.ops[0].list[0].data.Object).toBe('Table');
      });
    },
  );

  afterAll(
    async (): Promise<void> => {
      const responseDelete1 = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject1, company_id);
      expect(responseDelete1.status).toBe(200);

      const responseDelete2 = await requestDeleteObj(api, OBJ_TYPE.PROJECT, newProject2, company_id);
      expect(responseDelete2.status).toBe(200);
    },
  );
});
