import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
} from '../../../application/api/ApiObj';

describe('Delay', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: number;
  let process_node_ID: string | number;
  let final_node_ID: string | number;
  let task_id1: string | number;

  beforeAll(
    async (): Promise<void> => {
      apikey = await application.getApiKey();
      api = application.getApiKeyClient(apikey);
      company_id = apikey.companies[0].id;

      const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Delay`);
      conv_id = response.body.ops[0].obj_id;

      const responseList = await requestListConv(api, conv_id, company_id);
      expect(responseList.status).toBe(RESP_STATUS.OK);
      process_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
      final_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

      const responseCreateQueue = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.NODE,
          obj_id: process_node_ID,
          conv_id: conv_id,
          title: 'process',
          obj_type: 0,
          logics: [],
          semaphors: [{ type: 'time', value: 30, dimension: 'sec', to_node_id: final_node_ID }],
          version: 22,
        }),
      );
      expect(responseCreateQueue.status).toBe(RESP_STATUS.OK);

      const responseCommit = await requestConfirm(api, conv_id, company_id);
      expect(responseCommit.status).toBe(RESP_STATUS.OK);
    },
  );

  test('should create task Delay', async (): Promise<void> => {
    const responseTask1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id,
        data: { task: 1 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask1.status).toBe(RESP_STATUS.OK);
    expect(responseTask1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask1.body.ops[0].obj).toEqual('task');
    task_id1 = responseTask1.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 25000));

    const responseTask2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id,
        data: { task: 2 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask2.status).toBe(RESP_STATUS.OK);
    expect(responseTask2.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask2.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 4000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_id,
        ref_or_obj_id: task_id1,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].data).toEqual({ task: 1 });

    const responseList = await requestList(api, process_node_ID, conv_id, company_id);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(2);

    await new Promise(r => setTimeout(r, 2500));

    const responseListDelay = await requestList(api, process_node_ID, conv_id, company_id);
    expect(responseListDelay.status).toBe(RESP_STATUS.OK);
    expect(responseListDelay.body.ops[0].list[0].data).toEqual({ task: 2 });
    expect(responseListDelay.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseListDelay.body.ops[0].count).toEqual(1);

    const responseListFinal = await requestList(api, final_node_ID, conv_id, company_id);
    expect(responseListFinal.status).toBe(RESP_STATUS.OK);
    expect(responseListFinal.body.ops[0].list[0].data).toEqual({ task: 1 });
    expect(responseListFinal.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseListFinal.body.ops[0].count).toEqual(1);
  });

  test(`should download csv`, async (): Promise<void> => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.NODE,
        obj_id: final_node_ID,
        company_id,
        conv_id: conv_id,
        select: 'custom',
        offset: 0,
        limit: 1,
        format: 'csv',
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.body.request_proc).toBe(PROC_STATUS.OK);
    expect(response.body.ops[0].obj).toBe('node');
    expect(response.body.ops[0].download_url).toContain(`/user_downloads/${conv_id}`);
    expect(response.body.ops[0].download_url).toContain(`csv`);
    expect(response.body.ops[0].statistics_id).toBeString();
  });

  afterAll(
    async (): Promise<void> => {
      const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
      expect(response.status).toBe(RESP_STATUS.OK);
    },
  );
});
