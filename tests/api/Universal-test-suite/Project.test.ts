import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, createRequestWithObj, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';

describe('Project', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let newProject: string | number;
  let newStage: string | number;
  let newStage2: string | number;
  let company_id: any;
  let newKeyLoginId: string | number;
  let newKeyObjId: string | number;
  let newConv: string | number;
  let newFolder: string | number;
  let newAlias: string | number;
  let newVersion: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: `key_${Date.now()}`,
        logins: [{ type: 'api' }],
      }),
    );
    newKeyLoginId = responseKey.body.ops[0].users[0].obj_id;
    newKeyObjId = responseKey.body.ops[0].users[0].obj_id;
  });

  test(`should create Project stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        company_id,
        title: `Project_${Date.now()}`,
        short_name: `project${Date.now()}`,
        description: 'test',
        stages: [
          { title: 'production', immutable: true },
          { title: 'develop', immutable: false },
        ],
        status: 'active',
      }),
    );
    expect(response.status).toBe(200);
    newProject = response.body.ops[0].obj_id;
    newStage = response.body.ops[0].stages[1];
  });

  test(`should list_project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECTS,
        company_id,
        sort: 'title',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newProject })]),
    );
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newProject)?.is_owner).toEqual(true);
  });

  test(`should list_project stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj_id: newStage })]));
    expect((response.body.ops[0].list as Array<any>).find(item => item.obj_id === newStage)?.is_owner).toEqual(true);
  });

  test(`should show_project (stages)`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].stages).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newStage })]),
    );
    expect(response.body.ops[0].stages).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_short_name: 'production' })]),
    );
  });

  test(`should link_project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        obj_to: 'user',
        obj_to_id: newKeyLoginId,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].action_type).toEqual('link');
  });

  test(`should list_path_to_folder_project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PATH_TO_FOLDER,
        obj_id: newProject,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].obj).toEqual('path_to_folder');
  });

  test(`should get_object_project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.OBJECT,
        obj_id: newProject,
        company_id,
        obj_type: 'project',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].project_id).toEqual(newProject);
  });

  test(`should create Stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.STAGE,
        company_id,
        title: `Stage_${Date.now()}`,
        short_name: `stage-${Date.now()}`,
        description: 'test',
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');
    newStage2 = response.body.ops[0].obj_id;

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        project_id: newProject,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[0].uniq_undeployed_versions).toBe(0);
    expect(responseList.body.ops[0].list[0].uniq_undeployed_versions_detail).toBeArray;
    expect(responseList.body.ops[0].list[0].undeployed).toBe(0);
    expect(responseList.body.ops[0].list[0].obj_type).toBe('stage');
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newStage2 })]),
    );
  });

  test(`should modify Project`, async () => {
    const short_name = `project-modify${Date.now()}`;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        title: `ProjectModify`,
        short_name,
        description: 'testModify',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('project');

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECTS,
        company_id,
        sort: 'title',
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[0].obj_type).toBe('project');
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newProject })]),
    );
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: 'ProjectModify' })]),
    );
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ short_name: short_name })]),
    );
  });

  test(`should create obj in stage`, async () => {
    const responseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        project_id: newProject,
        stage_id: newStage2,
        title: `Folder_${Date.now()}`,
      }),
    );
    expect(responseFolder.status).toBe(200);
    newFolder = responseFolder.body.ops[0].obj_id;

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        project_id: newProject,
        stage_id: newStage2,
        title: `Folder_${Date.now()}`,
      }),
    );
    expect(responseConv.status).toBe(200);
    newConv = responseConv.body.ops[0].obj_id;
  });

  test(`should create Alias`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.ALIAS,
        company_id,
        short_name: `${Date.now()}`,
        title: 'Alias',
        description: 'test',
        project_id: newProject,
        stage_id: newStage2,
        obj_to_id: newConv,
        obj_to_type: 'conv',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('alias');
    newAlias = response.body.ops[0].obj_id;

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.ALIASES,
        company_id,
        project_id: newProject,
        stage_id: newStage2,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[0].obj_type).toBe('alias');
    expect(responseList.body.ops[0].list[0].obj_id).toBe(newAlias);
  });

  test('should copy conv', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newConv,
        obj_type: 'conv',
        folder_id: newFolder,
        title: 'copyConv',
        ignore_errors: true,
        project_id: newProject,
        stage_id: newStage2,
        async: false,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].scheme[0].old_obj_id).toBe(newConv);
    expect(response.body.ops[0].scheme[0].obj_type).toBe('conv');
    expect(response.body.ops[0].scheme[0].title).toBe('copyConv');
  });

  test(`should create Version`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.VERSION,
        company_id,
        vsn: `${Date.now()}`,
        stage_id: newStage2,
        changelog: 'test',
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('version');
    newVersion = response.body.ops[0].obj_id;

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.VERSIONS,
        company_id,
        project_id: newProject,
        sort: 'vsn',
        order: 'asc',
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[0].obj_type).toBe('version');
    expect(responseList.body.ops[0].list[0].obj_id).toBe(newVersion);
  });

  test(`should GET Version`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.GET,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersion,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].project_id).toBe(newProject);
    expect(response.body.ops[0].obj_id).toBe(newVersion);
    expect(response.body.ops[0].stage_id).toBe(newStage2);
  });

  test(`should modify Version`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersion,
        company_id,
        vsn: `111.111.111`,
        changelog: `modify`,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('version');

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.VERSIONS,
        company_id,
        project_id: newProject,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[0].obj_type).toBe('version');
    expect(responseList.body.ops[0].list[0].obj_id).toBe(newVersion);
    expect(responseList.body.ops[0].list[0].changelog).toBe('modify');
  });

  test('should download version', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newVersion,
        company_id,
        obj_type: 'version',
        format: 'zip',
        with_alias: true,
        async: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].download_url).toInclude(`/user_downloads/`);
  });

  test(`should modify Stage`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage2,
        company_id,
        title: `StageModify`,
        short_name: `stage-modify`,
        description: 'testModify',
        project_id: newProject,
        immutable: true,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('stage');

    const responseList = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
        project_id: newProject,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].list[0].obj_type).toBe('stage');
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ short_name: 'stage-modify' })]),
    );
    expect(responseList.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newStage2 })]),
    );
  });

  test('should upload Version', async () => {
    const response = await api.request(
      createRequestWithObj({
        type: REQUEST_TYPE.UPLOAD,
        obj: OBJ_TYPE.VERSION,
        company_id,
        version_id: newVersion,
        project_id: newProject,
        stage_to_id: newStage,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
    expect(response.body.ops[0].obj).toEqual('version');
  });

  test('should delete Version', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.VERSION,
        obj_id: newVersion,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newVersion);
    expect(response.body.ops[0].obj).toBe('version');
  });

  test(`should list Version after deleted`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.VERSIONS,
        company_id,
        project_id: newProject,
        sort: 'vsn',
        order: 'asc',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('versions');
    expect(response.body.ops[0].list).toBeArray();
  });

  test('should copy stage', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: newStage2,
        obj_type: 'stage',
        folder_id: newProject,
        title: 'project titlecopy',
        short_name: 'projecttitlecopy',
        ignore_errors: true,
        async: false,
        from_company_id: company_id,
        to_company_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].scheme[0].old_obj_id).toBe(newStage2);
    expect(response.body.ops[0].scheme[0].obj_type).toBe('stage');
    expect(response.body.ops[0].scheme[0].title).toBe('project titlecopy');
  });

  test('should download stage', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newStage2,
        company_id,
        obj_type: 'stage',
        format: 'zip',
        with_alias: true,
        async: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].download_url).toInclude(`/user_downloads/`);
    expect(response.body.ops[0].statistics_id).toBeString();
  });

  test('should download project', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newProject,
        company_id,
        obj_type: 'project',
        format: 'zip',
        with_alias: true,
        async: false,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('obj_scheme');
    expect(response.body.ops[0].download_url).toInclude(`/user_downloads/`);
    expect(response.body.ops[0].statistics_id).toBeString();
  });

  test('should delete stage', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newStage);
    expect(response.body.ops[0].obj).toBe('stage');
  });

  test('should destroy stage', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.STAGE,
        obj_id: newStage,
        company_id,
        project_id: newProject,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_type).toBe('stage');
    expect(response.body.ops[0].obj_id).toBe(newStage);
  });

  test(`should delete`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newProject);
  });

  test(`should restore project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newProject);
    expect(response.body.ops[0].obj).toBe('project');
  });

  test(`should delete project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newProject);
  });

  test(`should list folder filter deleted`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
        filter: 'deleted',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj).toBe('folder');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newProject })]),
    );
  });

  test(`should destroy project`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProject,
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].obj_id).toBe(newProject);
  });

  afterAll(async () => {
    const responseKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: newKeyObjId,
        company_id,
        group_id: 0,
        level: '',
      }),
    );
    expect(responseKey.status).toBe(200);
  });
});
