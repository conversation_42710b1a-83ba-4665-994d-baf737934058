import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import {
  requestList,
  requestConfirm,
  requestDeleteObj,
  requestCreateObj,
  requestListConv,
} from '../../../application/api/ApiObj';

describe('QueueGetFromQueue', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_idQ: number;
  let processQ_node_ID: string | number;
  let finalQ_node_ID: string | number;
  let conv_idGet: number;
  let processGet_node_ID: string | number;
  let finalGet_node_ID: string | number;
  let taskQ_id1: string | number;
  let taskGet_id: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `Queue`);
    conv_idQ = response.body.ops[0].obj_id;

    const responseList = await requestListConv(api, conv_idQ, company_id);
    expect(responseList.status).toBe(200);
    processQ_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    finalQ_node_ID = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateQueue = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: processQ_node_ID,
        conv_id: conv_idQ,
        title: 'process',
        obj_type: 0,
        logics: [
          { type: 'api_queue', err_node_id: '', data: { test: '123' }, data_type: { test: 'string' } },
          { to_node_id: finalQ_node_ID, type: 'go', node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateQueue.status).toBe(RESP_STATUS.OK);

    const responseCommit = await requestConfirm(api, conv_idQ, company_id);
    expect(responseCommit.status).toBe(200);

    const responseGet = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `GetFromQueue`);
    conv_idGet = responseGet.body.ops[0].obj_id;

    const responseListGet = await requestListConv(api, conv_idGet, company_id);
    expect(responseListGet.status).toBe(200);
    processGet_node_ID = (responseListGet.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
    finalGet_node_ID = (responseListGet.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

    const responseCreateQueueGet = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: processGet_node_ID,
        conv_id: conv_idGet,
        title: 'processGetFromQueue',
        obj_type: 0,
        logics: [
          { type: 'api_get_task', err_node_id: '', order_by: 'ASC', conv_id: conv_idQ, node_id: processQ_node_ID },
          { to_node_id: finalGet_node_ID, type: 'go', node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateQueueGet.status).toBe(RESP_STATUS.OK);

    const responseCommitGet = await requestConfirm(api, conv_idGet, company_id);
    expect(responseCommitGet.status).toBe(RESP_STATUS.OK);
  });

  test('should create task Queue', async () => {
    const responseTask1 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_idQ,
        data: { task: 1 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask1.status).toBe(RESP_STATUS.OK);
    expect(responseTask1.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask1.body.ops[0].obj).toEqual('task');
    taskQ_id1 = responseTask1.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 2000));

    const responseTask2 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_idQ,
        data: { task: 2 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask2.status).toBe(RESP_STATUS.OK);
    expect(responseTask2.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask2.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 2000));

    const responseTask3 = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_idQ,
        data: { task: 3 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask3.status).toBe(RESP_STATUS.OK);
    expect(responseTask3.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask3.body.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 2000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_idQ,
        ref_or_obj_id: taskQ_id1,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].data).toEqual({ task: 1 });

    const responseList = await requestList(api, processQ_node_ID, conv_idQ, company_id, 10);
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseList.body.ops[0].count).toEqual(3);
  });

  test('should create task GetFromQueueASC', async () => {
    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_idGet,
        data: { new: 1 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    taskGet_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 6000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_idGet,
        ref_or_obj_id: taskGet_id,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].data.__queue_task_data__).toEqual({ task: 1, test: '123' });
    expect(responseShow.body.ops[0].data.new).toEqual(1);

    const responseListQ = await requestList(api, processQ_node_ID, conv_idQ, company_id, 10);
    expect(responseListQ.status).toBe(RESP_STATUS.OK);
    expect(responseListQ.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseListQ.body.ops[0].list[0].data).toEqual({ task: 3 });
    expect(responseListQ.body.ops[0].list[1].data).toEqual({ task: 2 });
    expect(responseListQ.body.ops[0].count).toEqual(2);

    const responseListFinalQ = await requestList(api, finalQ_node_ID, conv_idQ, company_id, 10);
    expect(responseListFinalQ.status).toBe(RESP_STATUS.OK);
    expect(responseListFinalQ.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseListFinalQ.body.ops[0].list[0].data).toEqual({ task: 1 });
    expect(responseListFinalQ.body.ops[0].count).toEqual(1);
  });

  test('should create task GetFromQueueDESC', async () => {
    const responseCreateQueueGet = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: processGet_node_ID,
        conv_id: conv_idGet,
        title: 'processGetFromQueue',
        obj_type: 0,
        logics: [
          { type: 'api_get_task', err_node_id: '', order_by: 'DESC', conv_id: conv_idQ, node_id: processQ_node_ID },
          { to_node_id: finalGet_node_ID, type: 'go', node_title: 'final' },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(responseCreateQueueGet.status).toBe(RESP_STATUS.OK);

    const responseCommitGet = await requestConfirm(api, conv_idGet, company_id);
    expect(responseCommitGet.status).toBe(RESP_STATUS.OK);

    const responseTask = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.TASK,
        company_id,
        conv_id: conv_idGet,
        data: { new: 1 },
        ref: `ref_${Date.now()}`,
      }),
    );
    expect(responseTask.status).toBe(RESP_STATUS.OK);
    expect(responseTask.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseTask.body.ops[0].obj).toEqual('task');
    taskGet_id = responseTask.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 6000));

    const responseShow = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.TASK,
        conv_id: conv_idGet,
        ref_or_obj_id: taskGet_id,
      }),
    );
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].data.__queue_task_data__).toEqual({ task: 3, test: '123' });
    expect(responseShow.body.ops[0].data.new).toEqual(1);

    const responseListQ = await requestList(api, processQ_node_ID, conv_idQ, company_id, 10);
    expect(responseListQ.status).toBe(RESP_STATUS.OK);
    expect(responseListQ.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseListQ.body.ops[0].list[0].data).toEqual({ task: 2 });
    expect(responseListQ.body.ops[0].count).toEqual(1);

    const responseListFinalQ = await requestList(api, finalQ_node_ID, conv_idQ, company_id, 10);
    expect(responseListFinalQ.status).toBe(RESP_STATUS.OK);
    expect(responseListFinalQ.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseListFinalQ.body.ops[0].list[0].data).toEqual({ task: 3 });
    expect(responseListFinalQ.body.ops[0].count).toEqual(2);
  });

  afterAll(async () => {
    const response = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_idQ, company_id);
    expect(response.status).toBe(RESP_STATUS.OK);

    const responseD = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_idGet, company_id);
    expect(responseD.status).toBe(RESP_STATUS.OK);
  });
});
