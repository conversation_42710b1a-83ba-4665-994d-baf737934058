import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import { requestDeleteObj, requestCreateObj } from '../../../application/api/ApiObj';

describe('Condition', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let conv_id: number;
  let newfolder_id: number;
  let movefolder_id: number;
  let folder_id: string | number;
  let dash_id: string | number;
  let newKeyObjId: string | number;
  let folder_id_copy: string | number;
  let dash_id_copy: string | number;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const responseKey = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: `key_${Date.now()}`,
        logins: [{ type: 'api' }],
      }),
    );
    expect(responseKey.status).toBe(200);
    expect(responseKey.body.ops[0].proc).toEqual('ok');
    newKeyObjId = responseKey.body.ops[0].users[0].obj_id;

    const responseNewFolder = await requestCreateObj(api, OBJ_TYPE.FOLDER, company_id, `NewFolder`);
    newfolder_id = responseNewFolder.body.ops[0].obj_id;

    const responseMoveFolder = await requestCreateObj(api, OBJ_TYPE.FOLDER, company_id, `MoveFolder`);
    movefolder_id = responseMoveFolder.body.ops[0].obj_id;

    const responseConv = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, `NewConv`, newfolder_id);
    conv_id = responseConv.body.ops[0].obj_id;

    const responseDash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.DASHBOARD,
        company_id,
        title: 'NewDashboard',
        description: 'new',
        folder_id: newfolder_id,
      }),
    );
    dash_id = responseDash.body.ops[0].obj_id;

    const responseFolder = await requestCreateObj(api, OBJ_TYPE.FOLDER, company_id, `NewFolder`, newfolder_id);
    folder_id = responseFolder.body.ops[0].obj_id;
  });

  test('should list folder', async () => {
    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newfolder_id,
        sort: 'date',
        order: 'desc',
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.ops[0].proc).toEqual('ok');
    expect(responseListFolder.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: conv_id })]),
    );
    expect(responseListFolder.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: dash_id })]),
    );
    expect(responseListFolder.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: folder_id })]),
    );
  });

  test('should Download object', async () => {
    const responseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: newfolder_id,
        company_id,
        obj_type: 'folder',
        async: false,
        format: 'zip',
      }),
    );
    expect(responseFolder.status).toBe(200);
    expect(responseFolder.body.ops[0].proc).toEqual('ok');
    expect(responseFolder.body.ops[0].obj).toEqual('obj_scheme');
    expect(responseFolder.body.ops[0].download_url).toInclude(`user_downloads`);

    const responseDash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: dash_id,
        company_id,
        obj_type: 'dashboard',
        async: false,
        format: 'zip',
      }),
    );
    expect(responseDash.status).toBe(200);
    expect(responseDash.body.ops[0].proc).toEqual('ok');
    expect(responseDash.body.ops[0].obj).toEqual('obj_scheme');
    expect(responseDash.body.ops[0].download_url).toInclude(`user_downloads/`);

    const responseConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DOWNLOAD,
        obj: OBJ_TYPE.OBJ_SCHEME,
        obj_id: conv_id,
        company_id,
        obj_type: 'conv',
        async: false,
        format: 'zip',
      }),
    );
    expect(responseConv.status).toBe(200);
    expect(responseConv.body.ops[0].proc).toEqual('ok');
    expect(responseConv.body.ops[0].obj).toEqual('obj_scheme');
    expect(responseConv.body.ops[0].download_url).toInclude(`user_downloads/`);
  });

  test('should list folder after copy object', async () => {
    const responseFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: folder_id,
        company_id,
        obj_type: 'folder',
        async: false,
        format: 'zip',
        obj_to_id: newfolder_id,
        title: 'copyFolder',
        ignore_errors: true,
        obj_to_type: 'folder',
      }),
    );
    expect(responseFolder.status).toBe(200);
    expect(responseFolder.body.ops[0].proc).toEqual('ok');
    expect(responseFolder.body.ops[0].obj).toEqual('obj_copy');
    folder_id_copy = responseFolder.body.ops[0].scheme[0].obj_id;

    const responseDash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.OBJ_COPY,
        obj_id: dash_id,
        company_id,
        obj_type: 'dashboard',
        async: false,
        format: 'zip',
        obj_to_id: newfolder_id,
        title: 'copyDash',
        ignore_errors: true,
        obj_to_type: 'folder',
      }),
    );
    expect(responseDash.status).toBe(200);
    expect(responseDash.body.ops[0].proc).toEqual('ok');
    expect(responseDash.body.ops[0].obj).toEqual('obj_copy');
    dash_id_copy = responseDash.body.ops[0].scheme[0].obj_id;

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: newfolder_id,
        sort: 'date',
        order: 'desc',
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.ops[0].proc).toEqual('ok');
    expect(responseListFolder.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: conv_id })]),
    );
    expect(responseListFolder.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: dash_id })]),
    );
    expect(responseListFolder.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: folder_id })]),
    );
    expect(responseListFolder.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: folder_id_copy })]),
    );
    expect(responseListFolder.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: dash_id_copy })]),
    );

    const responseDelFolder = await requestDeleteObj(api, OBJ_TYPE.FOLDER, folder_id_copy, company_id);
    expect(responseDelFolder.status).toBe(200);

    const responseDelDash = await requestDeleteObj(api, OBJ_TYPE.DASHBOARD, dash_id_copy, company_id);
    expect(responseDelDash.status).toBe(200);
  });

  test('should list folder after add star', async () => {
    const responseFavoriteFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folder_id,
        company_id,
        favorite: true,
      }),
    );
    expect(responseFavoriteFolder.status).toBe(200);
    expect(responseFavoriteFolder.body.ops[0].proc).toEqual('ok');

    const responseFavoriteConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
        favorite: true,
      }),
    );
    expect(responseFavoriteConv.status).toBe(200);
    expect(responseFavoriteConv.body.ops[0].proc).toEqual('ok');

    const responseFavoriteDash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.FAVORITE,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: dash_id,
        company_id,
        favorite: true,
      }),
    );
    expect(responseFavoriteDash.status).toBe(200);
    expect(responseFavoriteDash.body.ops[0].proc).toEqual('ok');

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newfolder_id,
        company_id,
        sort: 'date',
        order: 'desc',
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.ops[0].proc).toEqual('ok');
    expect((responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === conv_id).favorite).toEqual(
      true,
    );
    expect((responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === dash_id).favorite).toEqual(
      true,
    );
    expect(
      (responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === folder_id).favorite,
    ).toEqual(true);
  });

  test('should list folder after share', async () => {
    const responseLinkFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folder_id,
        company_id,
        obj_to: 'user',
        obj_to_id: newKeyObjId,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(responseLinkFolder.status).toBe(200);
    expect(responseLinkFolder.body.ops[0].proc).toEqual('ok');
    expect(responseLinkFolder.body.ops[0].action_type).toEqual('link');

    const responseLinkConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
        obj_to: 'user',
        obj_to_id: newKeyObjId,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(responseLinkConv.status).toBe(200);
    expect(responseLinkConv.body.ops[0].proc).toEqual('ok');

    const responseLinkDash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: dash_id,
        company_id,
        obj_to: 'user',
        obj_to_id: newKeyObjId,
        privs: [{ type: 'view', list_obj: ['all'] }],
      }),
    );
    expect(responseLinkDash.status).toBe(200);
    expect(responseLinkDash.body.ops[0].proc).toEqual('ok');

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newfolder_id,
        company_id,
        sort: 'date',
        order: 'desc',
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.ops[0].proc).toEqual('ok');
    expect((responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === conv_id).is_shared).toEqual(
      true,
    );
    expect((responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === dash_id).is_shared).toEqual(
      true,
    );
    expect(
      (responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === folder_id).is_shared,
    ).toEqual(true);
  });

  test('should list folder after move', async () => {
    const responseMoveFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folder_id,
        company_id,
        obj_type: 'folder',
        folder_id: movefolder_id,
        parent_id: newfolder_id,
      }),
    );
    expect(responseMoveFolder.status).toBe(200);
    expect(responseMoveFolder.body.ops[0].proc).toEqual('ok');
    expect(responseMoveFolder.body.ops[0].action_type).toEqual('move');

    const responseMoveConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: conv_id,
        company_id,
        obj_type: 'conv',
        folder_id: movefolder_id,
        parent_id: newfolder_id,
      }),
    );
    expect(responseMoveConv.status).toBe(200);
    expect(responseMoveConv.body.ops[0].proc).toEqual('ok');
    expect(responseMoveConv.body.ops[0].action_type).toEqual('move');

    const responseMoveDash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: dash_id,
        company_id,
        obj_type: 'dashboard',
        folder_id: movefolder_id,
        parent_id: newfolder_id,
      }),
    );
    expect(responseMoveDash.status).toBe(200);
    expect(responseMoveDash.body.ops[0].proc).toEqual('ok');
    expect(responseMoveDash.body.ops[0].action_type).toEqual('move');

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: movefolder_id,
        company_id,
        sort: 'date',
        order: 'desc',
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.ops[0].proc).toEqual('ok');
    expect(responseListFolder.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: conv_id })]),
    );
    expect(responseListFolder.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: dash_id })]),
    );
    expect(responseListFolder.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: folder_id })]),
    );

    const responseMoveFolderBack = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folder_id,
        company_id,
        obj_type: 'folder',
        folder_id: newfolder_id,
        parent_id: movefolder_id,
      }),
    );
    expect(responseMoveFolderBack.status).toBe(200);
    expect(responseMoveFolderBack.body.ops[0].proc).toEqual('ok');
    expect(responseMoveFolderBack.body.ops[0].action_type).toEqual('move');

    const responseMoveConvBack = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: conv_id,
        company_id,
        obj_type: 'conv',
        folder_id: newfolder_id,
        parent_id: movefolder_id,
      }),
    );
    expect(responseMoveConvBack.status).toBe(200);
    expect(responseMoveConvBack.body.ops[0].proc).toEqual('ok');
    expect(responseMoveConvBack.body.ops[0].action_type).toEqual('move');

    const responseMoveDashBack = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.FOLDER,
        obj_id: dash_id,
        company_id,
        obj_type: 'dashboard',
        folder_id: newfolder_id,
        parent_id: movefolder_id,
      }),
    );
    expect(responseMoveDashBack.status).toBe(200);
    expect(responseMoveDashBack.body.ops[0].proc).toEqual('ok');
    expect(responseMoveDashBack.body.ops[0].action_type).toEqual('move');
  });

  test('should list folder after modify', async () => {
    const responseFavoriteFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folder_id,
        company_id,
        title: 'folderModify',
      }),
    );
    expect(responseFavoriteFolder.status).toBe(200);
    expect(responseFavoriteFolder.body.ops[0].proc).toEqual('ok');

    const responseFavoriteConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
        title: 'convModify',
      }),
    );
    expect(responseFavoriteConv.status).toBe(200);
    expect(responseFavoriteConv.body.ops[0].proc).toEqual('ok');

    const responseFavoriteDash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: dash_id,
        company_id,
        title: 'dashModify',
      }),
    );
    expect(responseFavoriteDash.status).toBe(200);
    expect(responseFavoriteDash.body.ops[0].proc).toEqual('ok');

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newfolder_id,
        company_id,
        sort: 'date',
        order: 'desc',
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.ops[0].proc).toEqual('ok');
    expect((responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === conv_id).title).toEqual(
      'convModify',
    );
    expect((responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === dash_id).title).toEqual(
      'dashModify',
    );
    expect((responseListFolder.body.ops[0].list as Array<any>).find(item => item.obj_id === folder_id).title).toEqual(
      'folderModify',
    );
  });

  test('should list folder after delete objects', async () => {
    const responseDeleteFolder = await requestDeleteObj(api, OBJ_TYPE.FOLDER, folder_id, company_id);
    expect(responseDeleteFolder.status).toBe(200);
    expect(responseDeleteFolder.body.ops[0].proc).toEqual('ok');

    const responseDeleteConv = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(responseDeleteConv.status).toBe(200);
    expect(responseDeleteConv.body.ops[0].proc).toEqual('ok');

    const responseDeleteDash = await requestDeleteObj(api, OBJ_TYPE.DASHBOARD, dash_id, company_id);
    expect(responseDeleteDash.status).toBe(200);
    expect(responseDeleteDash.body.ops[0].proc).toEqual('ok');

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newfolder_id,
        company_id,
        sort: 'date',
        order: 'desc',
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.ops[0].proc).toEqual('ok');
    expect(responseListFolder.body.ops[0].list).toBeArrayOfSize(0);
  });

  test('should list folder after restore objects', async () => {
    const responseRestoreFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folder_id,
        company_id,
      }),
    );
    expect(responseRestoreFolder.status).toBe(200);
    expect(responseRestoreFolder.body.ops[0].proc).toEqual('ok');

    const responseRestoreConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
      }),
    );
    expect(responseRestoreConv.status).toBe(200);
    expect(responseRestoreConv.body.ops[0].proc).toEqual('ok');

    const responseRestoreDash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.RESTORE,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: dash_id,
        company_id,
      }),
    );
    expect(responseRestoreDash.status).toBe(200);
    expect(responseRestoreDash.body.ops[0].proc).toEqual('ok');

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newfolder_id,
        company_id,
        sort: 'date',
        order: 'desc',
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.ops[0].proc).toEqual('ok');
    expect(responseListFolder.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: conv_id })]),
    );
    expect(responseListFolder.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: dash_id })]),
    );
    expect(responseListFolder.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: folder_id })]),
    );
  });

  test('should list folder after destroy objects', async () => {
    const responseDeleteFolder = await requestDeleteObj(api, OBJ_TYPE.FOLDER, folder_id, company_id);
    expect(responseDeleteFolder.status).toBe(200);
    expect(responseDeleteFolder.body.ops[0].proc).toEqual('ok');

    const responseDeleteConv = await requestDeleteObj(api, OBJ_TYPE.CONV, conv_id, company_id);
    expect(responseDeleteConv.status).toBe(200);
    expect(responseDeleteConv.body.ops[0].proc).toEqual('ok');

    const responseDeleteDash = await requestDeleteObj(api, OBJ_TYPE.DASHBOARD, dash_id, company_id);
    expect(responseDeleteDash.status).toBe(200);
    expect(responseDeleteDash.body.ops[0].proc).toEqual('ok');

    const responseDestroyFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.FOLDER,
        obj_id: folder_id,
        company_id,
      }),
    );
    expect(responseDestroyFolder.status).toBe(200);
    expect(responseDestroyFolder.body.ops[0].proc).toEqual('ok');

    const responseDestroyConv = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.CONV,
        obj_id: conv_id,
        company_id,
      }),
    );
    expect(responseDestroyConv.status).toBe(200);
    expect(responseDestroyConv.body.ops[0].proc).toEqual('ok');

    const responseDestroyDash = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DESTROY,
        obj: OBJ_TYPE.DASHBOARD,
        obj_id: dash_id,
        company_id,
      }),
    );
    expect(responseDestroyDash.status).toBe(200);
    expect(responseDestroyDash.body.ops[0].proc).toEqual('ok');

    const responseListFolder = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newfolder_id,
        company_id,
        sort: 'date',
        order: 'desc',
      }),
    );
    expect(responseListFolder.status).toBe(200);
    expect(responseListFolder.body.ops[0].proc).toEqual('ok');
    expect(responseListFolder.body.ops[0].list).toBeArrayOfSize(0);

    const responseListFolderDel = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
        sort: 'date',
        order: 'desc',
        filter: 'deleted',
      }),
    );
    expect(responseListFolderDel.status).toBe(200);
    expect(responseListFolderDel.body.ops[0].proc).toEqual('ok');
    expect(responseListFolder.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: conv_id })]),
    );
    expect(responseListFolder.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: dash_id })]),
    );
    expect(responseListFolder.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: folder_id })]),
    );
  });

  afterAll(async () => {
    const responseFolder = await requestDeleteObj(api, OBJ_TYPE.FOLDER, newfolder_id, company_id);
    expect(responseFolder.status).toBe(200);

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: newKeyObjId,
        company_id,
        group_id: 0,
        level: '',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('ok');
  });
});
