import { ApiKeyClient } from '../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../application/Application';
import { ApiKey } from '../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../utils/corezoidRequest';

describe('Company users (positive)', () => {
  let apikey: ApiKey;
  let apiSuper: ApiKeyClient;
  let apikeySuper: ApiKey;
  let company_id: any;
  let hash: string;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    apikeySuper = await application.getApiKeySuper();
    apiSuper = application.getApiKeyClient(apikeySuper);
    company_id = apikey.companies[0].id;

    const responseInvite = await apiSuper.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: ``,
        send_invite_if_user_not_exists: true,
        logins: [{ type: 'google', login: '<EMAIL>' }],
      }),
    );
    expect(responseInvite.status).toBe(200);
  });

  test(`should company user`, async () => {
    const response = await apiSuper.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        sort: 'title',
        filter: 'user',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    hash = (response.body.ops[0].list as Array<any>).find(item => item.title === `<EMAIL>`)
      ?.invite_hash;

    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ obj: 'user' })]));
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ title: '<EMAIL>' })]),
    );
    expect(response.body.ops[0].list).toEqual(expect.arrayContaining([expect.objectContaining({ is_invite: true })]));
  });

  afterAll(async () => {
    const responseInvite = await apiSuper.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.INVITE,
        company_id,
        hash,
      }),
    );
    expect(responseInvite.status).toBe(200);
  });
});
