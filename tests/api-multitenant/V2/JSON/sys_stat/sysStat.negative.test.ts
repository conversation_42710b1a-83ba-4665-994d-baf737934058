import { ApiKeyClient } from '../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../application/Application';
import { ApiKey } from '../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../utils/corezoidRequest';
import {
  integerTestCases,
  stringTestCases,
  undefinedTestCase,
  companyTestCases,
  stringNotValidTestCases,
  minLength,
  maxLength,
} from '../../../../api/negativeCases';

describe('Sys Stat (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let apiSuper: ApiKeyClient;
  let apikeySuper: ApiKey;
  const endUnix = Math.round(+new Date() / 1000);
  const startUnix = endUnix - 150;
  let company_id: any;
  let valuesToSkip: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    apikeySuper = await application.getApiKeySuper();
    apiSuper = application.getApiKeyClient(apikeySuper);
    company_id = apikey.companies[0].id;
  });

  test.skip.each([...companyTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't show sys stat with invalid company_id '%s'`,
    async (input, errors) => {
      const response = await apiSuper.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.SYS_STAT,
          company_id: input,
          interval: 'minute',
          start: startUnix,
          end: endUnix,
          obj_type: 'company',
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  valuesToSkip = [0, -1];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't show sys stat with invalid start '%s'`, async (input, errors) => {
    const response = await apiSuper.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        interval: 'minute',
        start: input,
        end: endUnix,
        obj_type: 'company',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0, -1];

  test.each(
    [...integerTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't show sys stat with invalid end '%s'`, async (input, errors) => {
    const response = await apiSuper.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        interval: 'minute',
        start: startUnix,
        end: input,
        obj_type: 'company',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't show sys stat with invalid interval '%s'`, async (input, errors) => {
    const response = await apiSuper.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        interval: input,
        start: startUnix,
        end: endUnix,
        obj_type: 'company',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't show sys stat with invalid order_field '%s'`, async (input, errors) => {
    const response = await apiSuper.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field: input,
        order_by: 'DESC',
        limit: 10,
        start: startUnix,
        end: endUnix,
        offset: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't show sys stat with invalid order_by '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field: 'opers',
        order_by: input,
        limit: 10,
        start: startUnix,
        end: endUnix,
        offset: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  valuesToSkip = [0];

  test.each(
    [...integerTestCases, ...maxLength]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't show sys stat with invalid offset '%s'`, async (input, errors) => {
    const response = await apiSuper.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field: 'opers',
        order_by: 'DESC',
        limit: 10,
        start: startUnix,
        end: endUnix,
        offset: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test(`shouldn't show sys stat with invalid offset more 20000`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field: 'opers',
        order_by: 'DESC',
        limit: 10,
        start: startUnix,
        end: endUnix,
        offset: 20001,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe(`bad offset`);
  });

  test.each([...integerTestCases, ...minLength, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't show sys stat with invalid limit '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.SHOW,
          obj: OBJ_TYPE.SYS_STAT,
          company_id,
          order_field: 'opers',
          order_by: 'DESC',
          limit: input,
          start: startUnix,
          end: endUnix,
          offset: 1,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test(`shouldn't show sys stat with invalid limit more 100`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_field: 'opers',
        order_by: 'DESC',
        limit: 101,
        start: startUnix,
        end: endUnix,
        offset: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe(`bad limit`);
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't show sys stat with invalid obj_type '%s'`, async (input, errors) => {
    const response = await apiSuper.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        obj_type: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });
});
