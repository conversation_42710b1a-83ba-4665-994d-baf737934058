import { ApiKeyClient } from '../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../application/Application';
import { Api<PERSON>ey } from '../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../application/api/SchemaValidator';
import showSysStatInstance from '../../../../api-multitenant/V2/schemas/V2/sys_stat/sysStatInstance.schema.json';
import showSysStatCompany from '../../../../api-multitenant/V2/schemas/V2/sys_stat/sysStatCompany.schema.json';
import showSysStatProcess from '../../../../api-multitenant/V2/schemas/V2/sys_stat/sysStatProcess.schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../utils/corezoidRequest';

describe('Sys Stat (positive)', () => {
  let apiSuper: ApiKeyClient;
  let apikeySuper: ApiKey;
  let newConv: string | number;
  let newConv2: string | number;
  let newApi: ApiKeyClient;
  const endUnix = Math.round(+new Date() / 1000);
  const startUnix = endUnix - 150;
  let company_id: any;

  beforeAll(async () => {
    apikeySuper = await application.getApiKeySuper();
    apiSuper = application.getApiKeyClient(apikeySuper);

    const response = await apiSuper.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,
        name: 'Key_company',
        description: 'test',
        login: '<EMAIL>',
        login_type: 'corezoid',
        auth_providers: ['corezoid'],
      }),
    );
    expect(response.status).toBe(200);
    company_id = response.body.ops[0].obj_id;

    const CreateKeyResponse = await apiSuper.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id,
        title: 'API',
        logins: [{ type: 'api' }],
      }),
    );
    expect(CreateKeyResponse.status).toBe(200);
    const user = CreateKeyResponse.body;
    const newApiKey = {
      key: `${user.ops[0].users[0].logins[0].obj_id}`,
      secret: user.ops[0].users[0].logins[0].key,
      companies: [],
      title: '',
      id: `${user.ops[0].users[0].obj_id}`,
    } as ApiKey;
    newApi = application.getApiKeyClient(newApiKey);

    const createConvResponse = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `conv_${Date.now()}`,
      }),
    );
    expect(createConvResponse.status).toBe(200);
    newConv = createConvResponse.body.ops[0].obj_id;

    const createConv2Response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.CONV,
        company_id,
        title: `conv_${Date.now()}`,
      }),
    );
    expect(createConvResponse.status).toBe(200);
    newConv2 = createConv2Response.body.ops[0].obj_id;

    let taskforConv;
    for (taskforConv = 0; taskforConv < 5; taskforConv++) {
      await newApi.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          company_id,
          conv_id: newConv,
          data: { x: '1' },
          ref: `task_${Date.now()}`,
        }),
      );
    }

    let taskforConv2;
    for (taskforConv2 = 0; taskforConv2 < 7; taskforConv2++) {
      await newApi.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.TASK,
          company_id,
          conv_id: newConv2,
          data: { x: '1' },
          ref: `task_${Date.now()}`,
        }),
      );
    }
  });

  test(`should show sys_stat instance`, async () => {
    await new Promise(r => setTimeout(r, 120000));
    const response = await apiSuper.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        obj_type: 'instance',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    SchemaValidator.validate(showSysStatInstance, response.body);
  });

  test(`should show sys_stat of company`, async () => {
    const response = await apiSuper.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        interval: 'minute',
        start: startUnix,
        end: endUnix,
        obj_type: 'company',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].data).toEqual(
      expect.arrayContaining([expect.objectContaining({ company_id: company_id })]),
    );
    expect(response.body.ops[0].data).toEqual(
      expect.arrayContaining([expect.objectContaining({ company_name: 'Key_company' })]),
    );
    expect(response.body.ops[0].data).toEqual(expect.arrayContaining([expect.objectContaining({ opers: 12 })]));
    expect(response.body.ops[0].data).toEqual(expect.arrayContaining([expect.objectContaining({ tacts: 36 })]));
    expect(response.body.ops[0].data).toEqual(expect.arrayContaining([expect.objectContaining({ traff: 0 })]));
    SchemaValidator.validate(showSysStatCompany, response.body);
  });

  test(`should show sys_stat instance - Access denied`, async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        obj_type: 'instance',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toEqual('Access denied');
  });

  test(`should show sys_stat of process`, async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_by: 'DESC',
        limit: 10,
        start: startUnix,
        end: endUnix,
        order_field: 'opers',
        offset: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].data[0].company_id).toEqual(company_id);
    expect(response.body.ops[0].data[0].conv_id).toEqual(newConv);
    expect(response.body.ops[0].data[0].opers).toEqual(5);
    expect(response.body.ops[0].data[0].tacts).toEqual(15);
    expect(response.body.ops[0].data[0].traff).toEqual(0);
    expect(response.body.ops[0].data).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ conv_id: newConv2 })]),
    );
    SchemaValidator.validate(showSysStatProcess, response.body);
  });

  test(`should show sys_stat of process with limit=1`, async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_by: 'DESC',
        limit: 1,
        start: startUnix,
        end: endUnix,
        order_field: 'tacts',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].data[0].company_id).toEqual(company_id);
    expect(response.body.ops[0].data[0].conv_id).toEqual(newConv2);
    expect(response.body.ops[0].data[0].opers).toEqual(7);
    expect(response.body.ops[0].data[0].tacts).toEqual(21);
    expect(response.body.ops[0].data[0].traff).toEqual(0);
    expect(response.body.ops[0].data).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ conv_id: newConv })]),
    );
    SchemaValidator.validate(showSysStatProcess, response.body);
  });

  test(`should show sys_stat of process with order_by: 'ASC'`, async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        order_by: 'ASC',
        limit: 10,
        start: startUnix,
        end: endUnix,
        order_field: 'traff',
        offset: 1,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].data[0].company_id).toEqual(company_id);
    expect(response.body.ops[0].data[0].conv_id).toEqual(newConv2);
    expect(response.body.ops[0].data[0].opers).toEqual(7);
    expect(response.body.ops[0].data[0].tacts).toEqual(21);
    expect(response.body.ops[0].data[0].traff).toEqual(0);
    expect(response.body.ops[0].data).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ conv_id: newConv })]),
    );
    SchemaValidator.validate(showSysStatProcess, response.body);
  });

  test(`should show sys_stat of company with interval: 'hour'`, async () => {
    const response = await apiSuper.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        interval: 'hour',
        start: startUnix,
        end: endUnix,
        obj_type: 'company',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].data).toEqual(
      expect.arrayContaining([expect.objectContaining({ company_id: company_id })]),
    );
    expect(response.body.ops[0].data).toEqual(
      expect.arrayContaining([expect.objectContaining({ company_name: 'Key_company' })]),
    );
    expect(response.body.ops[0].data).toEqual(expect.arrayContaining([expect.objectContaining({ opers: 12 })]));
    expect(response.body.ops[0].data).toEqual(expect.arrayContaining([expect.objectContaining({ tacts: 36 })]));
    expect(response.body.ops[0].data).toEqual(expect.arrayContaining([expect.objectContaining({ traff: 0 })]));
    SchemaValidator.validate(showSysStatCompany, response.body);
  });

  test(`should show sys_stat of company with interval: 'day'`, async () => {
    const response = await apiSuper.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        interval: 'day',
        start: startUnix,
        end: endUnix,
        obj_type: 'company',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].data).toEqual(
      expect.arrayContaining([expect.objectContaining({ company_id: company_id })]),
    );
    expect(response.body.ops[0].data).toEqual(
      expect.arrayContaining([expect.objectContaining({ company_name: 'Key_company' })]),
    );
    expect(response.body.ops[0].data).toEqual(expect.arrayContaining([expect.objectContaining({ opers: 12 })]));
    expect(response.body.ops[0].data).toEqual(expect.arrayContaining([expect.objectContaining({ tacts: 36 })]));
    expect(response.body.ops[0].data).toEqual(expect.arrayContaining([expect.objectContaining({ traff: 0 })]));
    SchemaValidator.validate(showSysStatCompany, response.body);
  });

  test(`should show sys_stat of company with interval: 'month'`, async () => {
    const response = await apiSuper.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.SYS_STAT,
        company_id,
        interval: 'month',
        start: startUnix,
        end: endUnix,
        obj_type: 'company',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].data).toEqual(
      expect.arrayContaining([expect.objectContaining({ company_id: company_id })]),
    );
    expect(response.body.ops[0].data).toEqual(
      expect.arrayContaining([expect.objectContaining({ company_name: 'Key_company' })]),
    );
    expect(response.body.ops[0].data).toEqual(expect.arrayContaining([expect.objectContaining({ opers: 12 })]));
    expect(response.body.ops[0].data).toEqual(expect.arrayContaining([expect.objectContaining({ tacts: 36 })]));
    expect(response.body.ops[0].data).toEqual(expect.arrayContaining([expect.objectContaining({ traff: 0 })]));
    SchemaValidator.validate(showSysStatCompany, response.body);
  });

  afterAll(async () => {
    const response = await apiSuper.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.COMPANY,
        obj_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
