import { ApiKeyClient } from '../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../application/Application';
import { ApiKey } from '../../../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../utils/corezoidRequest';
import faker from 'faker';
import {
  stringTestCases,
  undefinedTestCase,
  maxLength,
  minLength,
  stringNotValidTestCases,
  arrayTestCases,
  securityTestCases,
  companyTestCases,
  boolTestCases,
} from '../../../../../tests/api/negativeCases';

describe('Company (negative)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  const valuesToSkip: any = [null];

  beforeAll(async () => {
    apikey = await application.getApiKeySuper();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,

        name: 'Key_company',
        description: 'test',
        login: '<EMAIL>',
        login_type: 'corezoid',
      }),
    );
    expect(response.status).toBe(200);
    company_id = response.body.ops[0].obj_id;
  });

  test.each([...stringTestCases, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't create company with invalid name '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.COMPANY,

          login_type: 'corezoid',
          name: input,
          description: 'test',
          login: '<EMAIL>',
          auth_providers: ['corezoid'],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases]
      .filter(({ input }) => !valuesToSkip.includes(input as string | number))
      .map(({ input, errors }) => [input, errors]),
  )(`shouldn't create company with invalid description '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,

        login_type: 'corezoid',
        name: 'company',
        description: input,
        login: '<EMAIL>',
        auth_providers: ['corezoid'],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test(`shouldn't create company with invalid description maxLength`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,

        login_type: 'corezoid',
        name: 'company',
        description: faker.random.alphaNumeric(2001),
        login: '<EMAIL>',
        auth_providers: ['corezoid'],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toContain(
      `Value is not valid. Value's byte_size is more than maximum allowed: 2000`,
    );
  });

  test.each(
    [
      ...stringTestCases,
      ...undefinedTestCase,
      ...stringNotValidTestCases,
      ...securityTestCases,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create company with invalid login '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,

        login_type: 'corezoid',
        name: 'company',
        description: 'test',
        login: input,
        auth_providers: ['corezoid'],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [
      ...stringTestCases,
      ...undefinedTestCase,
      ...stringNotValidTestCases,
      ...securityTestCases,
    ].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create company with invalid login_type '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,

        login_type: input,
        name: 'company',
        description: 'test',
        login: '<EMAIL>',
        auth_providers: ['corezoid'],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...arrayTestCases, ...undefinedTestCase, ...securityTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't create company with invalid auth_providers '%s'`, async (input, errors) => {
    void errors;
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,

        login_type: 'corezoid',
        name: 'company',
        description: 'test',
        login: '<EMAIL>',
        auth_providers: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    expect(response.body.ops[0].description).toBeString();
  });

  test.each(
    [...companyTestCases, ...undefinedTestCase, ...maxLength, ...securityTestCases].map(({ input, errors }) => [
      input,
      errors,
    ]),
  )(`shouldn't modify company with invalid company_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.COMPANY,
        company_id: input,

        status: 'blocked',
        blocked_reason: 'Test',
        description: 'new_description',
        name: 'New_name',
        migrate_users: true,
        auth_providers: ['corezoid'],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each(
    [...stringTestCases, ...stringNotValidTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]),
  )(`shouldn't modify company with invalid status '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.COMPANY,
        company_id,

        status: input,
        blocked_reason: 'Test',
        description: 'new_description',
        name: 'New_name',
        migrate_users: true,
        auth_providers: ['corezoid'],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  test.each([...stringTestCases, ...maxLength, ...minLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify company with invalid blocked_reason '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.COMPANY,
          company_id,

          status: 'blocked',
          blocked_reason: input,
          description: 'new_description',
          name: 'New_name',
          migrate_users: true,
          auth_providers: ['corezoid'],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify company with invalid description '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.COMPANY,
          company_id,

          status: 'blocked',
          blocked_reason: 'test',
          description: input,
          name: 'New_name',
          migrate_users: true,
          auth_providers: ['corezoid'],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...stringTestCases, ...maxLength].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify company with invalid name '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.COMPANY,
          company_id,

          status: 'blocked',
          blocked_reason: 'test',
          description: 'desc',
          name: input,
          migrate_users: true,
          auth_providers: ['corezoid'],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...boolTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify company with invalid migrate_users '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.COMPANY,
          company_id,

          status: 'blocked',
          blocked_reason: 'test',
          description: 'desc',
          name: 'name',
          migrate_users: input,
          auth_providers: ['corezoid'],
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each([...arrayTestCases, ...securityTestCases].map(({ input, errors }) => [input, errors]))(
    `shouldn't modify company with invalid auth_providers '%s'`,
    async (input, errors) => {
      const response = await api.request(
        createRequestWithOps({
          type: REQUEST_TYPE.MODIFY,
          obj: OBJ_TYPE.COMPANY,
          company_id,

          status: 'blocked',
          blocked_reason: 'test',
          description: 'desc',
          name: 'name',
          migrate_users: true,
          auth_providers: input,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toEqual('error');
      const error = response.body.ops[0].description;
      expect(errors).toEqual(expect.arrayContaining([error]));
    },
  );

  test.each(
    [...stringTestCases, ...maxLength, ...minLength, ...undefinedTestCase].map(({ input, errors }) => [input, errors]),
  )(`shouldn't delete company with invalid obj_id '%s'`, async (input, errors) => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.COMPANY,
        obj_id: input,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toEqual('error');
    const error = response.body.ops[0].description;
    expect(errors).toEqual(expect.arrayContaining([error]));
  });

  afterAll(async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.COMPANY,
        obj_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
  });
});
