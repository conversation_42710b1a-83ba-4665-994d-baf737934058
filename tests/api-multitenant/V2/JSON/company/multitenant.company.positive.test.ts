import { ApiKeyClient } from '../../../../../application/api/ApiKeyClient';
import { application } from '../../../../../application/Application';
import { ApiKey } from '../../../../../infrastructure/model/ApiKey';
import { SchemaValidator } from '../../../../../application/api/SchemaValidator';
import listCompany from '../../../../api/corezoid-api/schemas/v2/company/listCompany.Schema.json';
import createCompany from '../../../../api/corezoid-api/schemas/v2/company/createCompanyMultitenant.Schema.json';
import deleteCompany from '../../../../api/corezoid-api/schemas/v2/company/deleteCompanyMultitenant.Schema.json';
import modifyCompany from '../../../../api/corezoid-api/schemas/v2/company/modifyCompanyMultitenant.Schema.json';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../../../utils/corezoidRequest';

describe('Company (positive)', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let company_id_2: any;

  beforeAll(async () => {
    apikey = await application.getApiKeySuper();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
  });

  test(`should show create company`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,
        name: 'Key_company',
        description: 'test',
        login: '<EMAIL>',
        login_type: 'corezoid',
        auth_providers: ['corezoid'],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('company');
    company_id = response.body.ops[0].obj_id;
    SchemaValidator.validate(createCompany, response.body);
  });

  test(`should show create company with required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.COMPANY,
        login: '<EMAIL>',
        login_type: 'corezoid',
        auth_providers: ['corezoid'],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('company');
    company_id_2 = response.body.ops[0].obj_id;
    SchemaValidator.validate(createCompany, response.body);
  });

  test(`should show list company`, async () => {
    await new Promise(r => setTimeout(r, 1000));
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].list).toEqual(
      expect.arrayContaining([expect.objectContaining({ company_id: company_id })]),
    );
    expect((response.body.ops[0].list as Array<any>).find(item => item.company_id === company_id).description).toEqual(
      'test',
    );
    expect((response.body.ops[0].list as Array<any>).find(item => item.company_id === company_id).title).toEqual(
      'Key_company',
    );
    SchemaValidator.validate(listCompany, response.body);
  });

  test(`should show modify company`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.COMPANY,
        company_id,
        status: 'blocked',
        blocked_reason: 'Test',
        description: 'new_description',
        name: 'New_name',
        migrate_users: true,
        auth_providers: ['corezoid'],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('company');
    expect(response.body.ops[0].company_id).toBe(company_id);
    expect(response.body.ops[0].name).toBe('New_name');
    expect(response.body.ops[0].description).toBe('new_description');
    expect(response.body.ops[0].status).toBe('blocked');
    expect(response.body.ops[0].new_status).toBe('blocked');
    expect(response.body.ops[0].old_status).toBe('actived');
    expect(response.body.ops[0].migrated_count).toBe(0);
    SchemaValidator.validate(modifyCompany, response.body);
  });

  test(`should show modify company with required param`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.COMPANY,
        company_id: company_id_2,
        auth_providers: ['corezoid'],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('company');
    expect(response.body.ops[0].company_id).toBe(company_id_2);
  });

  test(`should show modify company actived`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.COMPANY,
        company_id: company_id,
        status: 'actived',
        name: 'actived',
        migrate_users: false,
        auth_providers: ['corezoid'],
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('company');
    expect(response.body.ops[0].company_id).toBe(company_id);
    expect(response.body.ops[0].name).toBe('actived');
    expect(response.body.ops[0].description).toBe('new_description');
    expect(response.body.ops[0].status).toBe('actived');
    expect(response.body.ops[0].new_status).toBe('actived');
    expect(response.body.ops[0].old_status).toBe('blocked');
    SchemaValidator.validate(modifyCompany, response.body);
  });

  test(`should show modify default company`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.COMPANY,
        company_id: 'i000000000',
        status: 'blocked',
        blocked_reason: 'Test',
        description: 'new_description',
        name: 'New_name',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].proc).toBe(`error`);
    expect(response.body.ops[0].description).toBe(`It's not allowed to modify system_company i000000000`);
  });

  test(`should show delete company`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.COMPANY,
        obj_id: company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('company');
    SchemaValidator.validate(deleteCompany, response.body);
  });

  test(`should show delete company2`, async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.COMPANY,
        obj_id: company_id_2,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.request_proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('company');
    SchemaValidator.validate(deleteCompany, response.body);
  });
});
