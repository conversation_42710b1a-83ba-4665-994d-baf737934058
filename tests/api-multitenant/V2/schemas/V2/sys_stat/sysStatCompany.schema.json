{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "obj_type", "data"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "obj_type": {"type": "string"}, "data": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["company_id", "company_name", "date", "opers", "tacts", "traff"], "additionalProperties": false, "properties": {"company_id": {"type": "string"}, "company_name": {"type": "string"}, "date": {"type": "string"}, "opers": {"type": "integer"}, "tacts": {"type": "integer"}, "traff": {"type": "number"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}