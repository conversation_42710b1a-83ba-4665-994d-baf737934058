{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "required": ["request_proc", "ops"], "additionalProperties": false, "properties": {"request_proc": {"type": "string"}, "ops": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["id", "proc", "obj", "obj_type", "data"], "additionalProperties": false, "properties": {"id": {"type": "string"}, "proc": {"type": "string"}, "obj": {"type": "string"}, "obj_type": {"type": "string"}, "data": {"type": "array", "additionalItems": true, "items": {"type": "object", "required": ["conv_id", "conv_name", "owner_id", "owner_name", "date", "opers", "tacts", "company_id", "folder_id", "traff"], "additionalProperties": false, "properties": {"conv_id": {"type": "integer"}, "conv_name": {"type": "string"}, "owner_id": {"type": "integer"}, "owner_name": {"type": "string"}, "date": {"type": "string"}, "opers": {"type": "integer"}, "tacts": {"type": "integer"}, "company_id": {"type": "string"}, "folder_id": {"type": "integer"}, "traff": {"type": "integer"}}}, "uniqueItems": true}}}, "uniqueItems": true}}}