import { debug } from '../../../support/utils/logger';
import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { Api<PERSON><PERSON> } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';
import { axiosInstance } from '../../../application/api/AxiosClient';

describe('Headers', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;
  });

  test('should create GET', async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}`;
    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(200);
    expect(response.headers['cache-control']).toBe('max-age=31536000');
    expect(response.headers['strict-transport-security']).toBeString();
    expect(response.headers['x-frame-options']).toBe('SAMEORIGIN');
    expect(response.headers['x-content-type-options']).toBe('nosniff');
    expect(response.headers['content-security-policy']).toInclude(`frame-ancestors`);
  });

  test('should create GET me', async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}auth/me`;
    const response = await axiosInstance({
      method: 'GET',
      url: uri,
    });
    expect(response.status).toBe(200);
    expect(response.headers['cache-control']).toBe('no-store, no-cache, must-revalidate');
    expect(response.headers['strict-transport-security']).toBeString();
    expect(response.headers['x-frame-options']).toBe('SAMEORIGIN');
    expect(response.headers['x-content-type-options']).toBe('nosniff');
  });

  test('should get list of api keys', async () => {
    const response = await api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        company_id,
        filter: 'api_key',
        sort: 'title',
        order: 'asc',
      }),
    );
    expect(response.status).toBe(200);
    debug(response.headers);
    expect(response.headers['x-frame-options']).toBe('SAMEORIGIN');
    expect(response.headers['cache-control']).toBe('no-store, no-cache, must-revalidate');
    expect(response.headers['strict-transport-security']).toBeString();
    expect(response.headers['content-security-policy']).toBeString();
  });
});
