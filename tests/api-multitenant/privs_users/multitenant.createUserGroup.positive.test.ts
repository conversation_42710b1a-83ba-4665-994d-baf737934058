import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { ApiUserClient } from '../../../application/api/ApiUserClient';

import {
  createRequestWithOps,
  createRequestWithObj,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../utils/corezoidRequest';
import { requestDeleteObj } from '../../../application/api/ApiObj';
import {
  setupTestConfiguration,
  createApiKey,
  findAdminGroup,
  linkUserToAdminGroup,
  createTestCompany,
  cleanupTestResources,
} from './helpers/multitenantTestHelpers';

const TEST_EMAIL_DOMAIN = 'gmail.com';
const TEST_PREFIX = 'test';

const generateUniqueName = (prefix: string = TEST_PREFIX): string =>
  `${prefix}_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

describe('Privs create in Users&Groups multitenant (positive)', () => {
  let company_id: any;
  let key_id: string | number;
  let group_id: string | number;
  let group_idSA: string | number;
  let newApi: ApiKeyClient;
  let newApiKeyId: string | number;
  let newApiKeyIdSA: string | number;
  let newApiAdm: ApiKeyClient;
  let newApiKeyIdAdm: string | number;
  let apiUserCookie: ApiUserClient;
  let newCompany_idSuperAdmin: string;
  let newCompanyNameSuperAdmin: string;

  const createUser = async (client: any, companyId: string | number, options = {}): Promise<any> => {
    const defaults = {
      title: '',
      send_invite_if_user_not_exists: true,
      logins: [{ type: 'google', login: `${generateUniqueName()}@${TEST_EMAIL_DOMAIN}` }],
    };

    const mergedOptions = { ...defaults, ...options };

    return client.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id: companyId,
        ...mergedOptions,
      }),
    );
  };

  const createGroup = async (client: any, companyId: string | number, options = {}): Promise<any> => {
    const defaults = {
      title: 'test',
      obj_type: 'admins',
    };

    const mergedOptions = { ...defaults, ...options };

    return client.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id: companyId,
        ...mergedOptions,
      }),
    );
  };

  const fetchInvites = async (companyId: string | number): Promise<any[]> => {
    const response = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        sort: 'title',
        order: 'asc',
        filter: 'user',
        company_id: companyId,
      }),
    );

    expect(response.status).toBe(RESP_STATUS.OK);

    return response.body.ops[0].list
      .filter((user: { is_invite: boolean; invite_hash: string }) => user.is_invite && user.invite_hash)
      .map((user: { invite_hash: string }) => user.invite_hash);
  };

  const deleteInvites = async (inviteHashes: string[], companyId: string | number): Promise<void> => {
    const ops = inviteHashes.map((hash: string) => ({
      type: REQUEST_TYPE.DELETE,
      obj: OBJ_TYPE.INVITE,
      hash,
      company_id: companyId,
    }));

    const response = await apiUserCookie.request(
      createRequestWithObj({
        ops,
      }),
    );

    expect(response.status).toBe(RESP_STATUS.OK);
  };

  const cleanupInvites = async (companyIds: (string | number)[]): Promise<void> => {
    for (const compId of companyIds) {
      const invites = await fetchInvites(compId);
      if (invites.length > 0) {
        await deleteInvites(invites, compId);
      }
    }
  };

  beforeAll(async () => {
    const config = await setupTestConfiguration();
    company_id = config.company_id;
    apiUserCookie = config.apiUserCookie;
    newCompanyNameSuperAdmin = 'newCompanySuperAdmin';

    const userKeyResult = await createApiKey(apiUserCookie, 'apiUser1', company_id);
    newApi = userKeyResult.client;
    newApiKeyId = userKeyResult.keyId;

    const adminKeyResult = await createApiKey(apiUserCookie, 'apiUserAdmin', company_id);
    newApiAdm = adminKeyResult.client;
    newApiKeyIdAdm = adminKeyResult.keyId;

    group_id = await findAdminGroup(apiUserCookie, company_id);
    await linkUserToAdminGroup(apiUserCookie, newApiKeyIdAdm, group_id, company_id);

    newCompany_idSuperAdmin = await createTestCompany(apiUserCookie, newCompanyNameSuperAdmin);

    await new Promise(r => setTimeout(r, 3000));
  });

  const userGroupTestCases = [
    {
      description: `shouldn't create user/group in default Company for user`,
      user: (): ApiKeyClient => newApi,
      company: (): string | number => company_id,
      proc: (): string => PROC_STATUS.ERROR,
      path: (): string => `description`,
      result: (): string => 'Access denied',
    },
    {
      description: `shouldn't create user/group in new Company for admin default company`,
      user: (): ApiKeyClient => newApiAdm,
      company: (): string | number => newCompany_idSuperAdmin,
      proc: (): string => PROC_STATUS.ERROR,
      path: (): string => `description`,
      result: (): string => 'Access denied',
    },
    {
      description: `should create user/group in default Company for admin`,
      user: (): ApiKeyClient => newApiAdm,
      company: (): string | number => company_id,
      path: (): string => `obj`,
      proc: (): string => PROC_STATUS.OK,
      result: (): string => 'group',
    },
  ];

  describe('User and Group Management', (): void => {
    describe.each(userGroupTestCases)('$description', ({ user, company, path, proc, result }): void => {
      test(`create user`, async (): Promise<void> => {
        const responseCreateUser = await createUser(user(), company());

        expect(responseCreateUser.status).toBe(RESP_STATUS.OK);
        expect(responseCreateUser.body.ops[0].proc).toBe(proc());
      });

      test(`create group`, async (): Promise<void> => {
        const responseCreateGroup = await createGroup(user(), company());

        expect(responseCreateGroup.status).toBe(RESP_STATUS.OK);
        expect(responseCreateGroup.body.ops[0].proc).toBe(proc());

        const groupResponse = responseCreateGroup.body.ops[0];
        expect(groupResponse[path()]).toBe(result());

        if (proc() === PROC_STATUS.OK) {
          group_id = groupResponse.obj_id;
        }
      });
    });
  });

  const apiUserTestCases = [
    {
      description: `should create api_key in default Company for user`,
      user: (): ApiKeyClient => newApi,
      company: (): string | number => company_id,
      proc: (): string => PROC_STATUS.OK,
      path: (): string => `obj`,
      result: (): string => 'user',
    },
    {
      description: `should create api_key in default Company for adminuser`,
      user: (): ApiKeyClient => newApiAdm,
      company: (): string | number => company_id,
      proc: (): string => PROC_STATUS.OK,
      path: (): string => `obj`,
      result: (): string => 'user',
    },
    {
      description: `shouldn't create api_key in new Company for adminuser`,
      user: (): ApiKeyClient => newApiAdm,
      company: (): string | number => newCompany_idSuperAdmin,
      proc: (): string => PROC_STATUS.ERROR,
      path: (): string => `description`,
      result: (): string => 'Access denied',
    },
  ];

  describe('API Key Management', (): void => {
    describe.each(apiUserTestCases)('$description', ({ user, company, path, proc, result }): void => {
      test(`create api user`, async (): Promise<void> => {
        const responseCreateUser = await user().request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.USER,
            company_id: company(),
            title: 'test_key',
            logins: [{ type: 'api' }],
          }),
        );

        expect(responseCreateUser.status).toBe(RESP_STATUS.OK);
        expect(responseCreateUser.body.ops[0].proc).toBe(proc());

        const userResponse = responseCreateUser.body.ops[0];
        expect(userResponse[path()]).toBe(result());

        if (responseCreateUser.body.ops[0].proc === PROC_STATUS.OK) {
          key_id = responseCreateUser.body.ops[0].users[0].obj_id;

          const responseDeleteKey = await requestDeleteObj(user(), OBJ_TYPE.USER, key_id, company());
          expect(responseDeleteKey.status).toBe(200);
        }
      });
    });
  });

  const superUserTestCases = [
    {
      description: `should create user/key/group in default Company for superadmin`,
      company: (): string | number => company_id,
    },
    {
      description: `should create user/key/group in new Company for superadmin`,
      company: (): string | number => newCompany_idSuperAdmin,
    },
  ];

  describe('Super Admin Capabilities', (): void => {
    describe.each(superUserTestCases)('$description', ({ company }): void => {
      test('should create user/key/group', async (): Promise<void> => {
        const responseApi = await apiUserCookie.request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.USER,
            company_id: company(),
            title: 'keySA',
            logins: [{ type: 'api' }],
          }),
        );

        expect(responseApi.status).toBe(RESP_STATUS.OK);
        expect(responseApi.body.ops[0].proc).toBe(PROC_STATUS.OK);
        expect(responseApi.body.ops[0].obj).toBe('user');

        newApiKeyIdSA = responseApi.body.ops[0].users[0].obj_id;

        const responseGroup = await apiUserCookie.request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.GROUP,
            company_id: company(),
            title: 'testSA',
            obj_type: 'admins',
          }),
        );

        expect(responseGroup.status).toBe(RESP_STATUS.OK);
        expect(responseGroup.body.ops[0].proc).toBe(PROC_STATUS.OK);
        expect(responseGroup.body.ops[0].obj).toBe('group');

        group_idSA = responseGroup.body.ops[0].obj_id;

        const responseCreateUser = await apiUserCookie.request(
          createRequestWithOps({
            type: REQUEST_TYPE.CREATE,
            obj: OBJ_TYPE.USER,
            company_id: company(),
            title: '',
            logins: [{ type: 'google', login: `${generateUniqueName()}@${TEST_EMAIL_DOMAIN}` }],
            send_invite_if_user_not_exists: true,
          }),
        );

        expect(responseCreateUser.status).toBe(RESP_STATUS.OK);
        expect(responseCreateUser.body.ops[0].proc).toBe(PROC_STATUS.OK);

        const DeleteResponse = await apiUserCookie.request(
          createRequestWithObj({
            ops: [
              { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newApiKeyIdSA, company_id: company() },
              { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.GROUP, obj_id: group_idSA, company_id: company() },
            ],
          }),
        );

        expect(DeleteResponse.status).toBe(RESP_STATUS.OK);
      });
    });
  });

  afterEach(
    async (): Promise<void> => {
      await cleanupInvites([company_id, newCompany_idSuperAdmin]);
    },
  );

  afterAll(
    async (): Promise<void> => {
      const deleteOperations = [
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newApiKeyId, company_id },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newApiKeyIdAdm, company_id },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.GROUP, obj_id: group_id, company_id },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.COMPANY, obj_id: newCompany_idSuperAdmin },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.COMPANY, obj_id: company_id },
      ];

      await cleanupTestResources(apiUserCookie, deleteOperations);
    },
  );
});
