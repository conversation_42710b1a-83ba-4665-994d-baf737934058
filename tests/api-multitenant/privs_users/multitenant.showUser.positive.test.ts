import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { ApiUserClient } from '../../../application/api/ApiUserClient';
import { axiosInstance } from '../../../application/api/AxiosClient';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import {
  setupTestConfiguration,
  createApiKey,
  findAdminGroup,
  linkUserToAdminGroup,
  createTestCompany,
  cleanupTestResources,
} from './helpers/multitenantTestHelpers';

describe('Privs Show users multitenant (positive)', () => {
  let company_id: any;
  let newCompany_id: string;
  let groupAdminObjId: string | number;
  let groupSuperObjId: string | number;
  let user_id: string | number;
  let group_id: string | number;
  let newApi: ApiKeyClient;
  let newApiKeyId: string | number;
  let newApiAdm: ApiKeyClient;
  let newApiKeyIdAdm: string | number;
  let keyNewCompany: string | number;
  let newKeyAdmin: string | number;
  let newKeyUser: string | number;
  let hash: string;
  let login: string;
  let apiUserCookie: ApiUserClient;

  beforeAll(async () => {
    const config = await setupTestConfiguration();
    company_id = config.company_id;
    apiUserCookie = config.apiUserCookie;
    login = `testuser-${Date.now()}@gmail.com`;

    const CreateUserResponse = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        title: 'User',
        logins: [{ type: 'google', login }],
        company_id,
        send_invite_if_user_not_exists: true,
      }),
    );
    expect(CreateUserResponse.status).toBe(200);

    await new Promise(r => setTimeout(r, 5000));

    const ListUsersResponse = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(ListUsersResponse.status).toBe(200);
    hash = (ListUsersResponse.body.ops[0].list as Array<any>).find(item => item.title === login).invite_hash;

    const CreateUser = await axiosInstance({
      method: 'POST',
      url: `${config.host}api/2/plugins/invite/${hash}`,
      data: { nick: 'User', password: 'Test12345!' },
    });
    expect(CreateUser.status).toBe(200);

    await new Promise(r => setTimeout(r, 2000));

    const apiKeyResult = await createApiKey(apiUserCookie, 'apiUser', company_id);
    newApi = apiKeyResult.client;
    newApiKeyId = apiKeyResult.keyId;

    const adminKeyResult = await createApiKey(apiUserCookie, 'apiUserAdmin', company_id);
    newApiAdm = adminKeyResult.client;
    newApiKeyIdAdm = adminKeyResult.keyId;

    group_id = await findAdminGroup(apiUserCookie, company_id);

    const listUserResponse = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(listUserResponse.status).toBe(200);
    user_id = listUserResponse.body.ops[0].list[0].obj_id;

    await linkUserToAdminGroup(apiUserCookie, newApiKeyIdAdm, group_id, company_id);

    const response = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_type: 'admins',
        title: 'testGroupAdmin',
      }),
    );
    expect(response.status).toBe(200);
    groupAdminObjId = response.body.ops[0].obj_id;
    const responseLink = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        company_id,
        level: 1,
        group_id: groupAdminObjId,
        obj_id: newApiKeyId,
      }),
    );
    expect(responseLink.status).toBe(200);

    const createGroupresponse = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        obj_type: 'admins',
        company_id,
        title: 'testGroupSuper',
      }),
    );
    expect(createGroupresponse.status).toBe(200);
    groupSuperObjId = createGroupresponse.body.ops[0].obj_id;

    const responseKeyAdm = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,

        title: 'apiUser',
        logins: [{ type: 'api' }],
        company_id,
      }),
    );
    expect(responseKeyAdm.status).toBe(200);
    newKeyAdmin = responseKeyAdm.body.ops[0].users[0].obj_id;

    const responseLink1 = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        company_id,
        level: 1,
        group_id: groupAdminObjId,
        obj_id: newKeyAdmin,
      }),
    );
    expect(responseLink1.status).toBe(200);

    const responseKeyUser = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,

        title: 'apiUser',
        logins: [{ type: 'api' }],
        company_id,
      }),
    );
    expect(responseKeyUser.status).toBe(200);
    newKeyUser = responseKeyUser.body.ops[0].users[0].obj_id;

    newCompany_id = await createTestCompany(apiUserCookie, 'new');

    const createGroupResponse = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        obj_type: 'admins',
        company_id: newCompany_id,
        title: 'testGroupSuper',
      }),
    );
    expect(createGroupResponse.status).toBe(200);

    const createUserResponse = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        title: 'apiUser',
        logins: [{ type: 'api' }],
        company_id: newCompany_id,
      }),
    );
    expect(createUserResponse.status).toBe(200);
    keyNewCompany = createUserResponse.body.ops[0].users[0].obj_id;
  });

  test('should show key in default Company with obj_type = company for superadmin', async () => {
    const responseApi = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        obj_id: newApiKeyId,
        obj_type: 'company',
        company_id,
      }),
    );
    expect(responseApi.status).toBe(200);
    expect(responseApi.body.ops[0].proc).toBe('ok');
    expect(responseApi.body.ops[0].obj).toBe('user');
    expect(responseApi.body.ops[0].obj_id).toBe(newApiKeyId);
    expect(responseApi.body.ops[0].logins[0].type).toBe('api');
  });

  test('should show key in other Company with obj_type = company for superadmin', async () => {
    const responseApiNewCompany = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        obj_id: keyNewCompany,
        obj_type: 'company',
        company_id: newCompany_id,
      }),
    );
    expect(responseApiNewCompany.status).toBe(200);
    expect(responseApiNewCompany.body.ops[0].proc).toBe('ok');
    expect(responseApiNewCompany.body.ops[0].obj).toBe('user');
    expect(responseApiNewCompany.body.ops[0].obj_id).toBe(keyNewCompany);
    expect(responseApiNewCompany.body.ops[0].logins[0].type).toBe('api');
  });

  test('should show user in default Company with obj_type = company for superadmin', async () => {
    const responseUser = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        obj_id: user_id,
        obj_type: 'company',
        company_id,
      }),
    );
    expect(responseUser.status).toBe(200);
    expect(responseUser.body.ops[0].proc).toBe('ok');
    expect(responseUser.body.ops[0].obj).toBe('user');
    expect(responseUser.body.ops[0].obj_id).toBe(user_id);
    expect(responseUser.body.ops[0].logins[0].type).toBe('corezoid');
  });

  test('should show key in default Company with obj_type = company for admin', async () => {
    const response = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        obj_id: newKeyAdmin,
        company_id,
        obj_type: 'company',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('user');
    expect(response.body.ops[0].obj_id).toBe(newKeyAdmin);
    expect(response.body.ops[0].logins[0].type).toBe('api');
    expect(response.body.ops[0].groups[0].id).toBe(groupAdminObjId);
  });

  test('should show key in other Company with obj_type = company for admin', async () => {
    const responseWithoutAccessCompany = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        obj_id: keyNewCompany,
        company_id: `${newCompany_id}`,
        obj_type: 'company',
      }),
    );
    expect(responseWithoutAccessCompany.status).toBe(200);
    expect(responseWithoutAccessCompany.body.ops[0].proc).toBe('error');
    expect(responseWithoutAccessCompany.body.ops[0].description).toBe('Company does not exists');
  });

  test('should show key in other Company with obj_type = company for admin (without company_id)', async () => {
    const responseWithoutAccess = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        obj_id: keyNewCompany,
        obj_type: 'company',
      }),
    );
    expect(responseWithoutAccess.status).toBe(200);
    expect(responseWithoutAccess.body.ops[0].proc).toBe('error');
    expect(responseWithoutAccess.body.ops[0].description).toBe('User has no rights');
  });

  test('should show user in default Company with obj_type = company for admin', async () => {
    const responseUser = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        obj_id: user_id,
        company_id,
        obj_type: 'company',
      }),
    );
    expect(responseUser.status).toBe(200);
    expect(responseUser.body.ops[0].proc).toBe('ok');
    expect(responseUser.body.ops[0].obj).toBe('user');
    expect(responseUser.body.ops[0].obj_id).toBe(user_id);
    expect(responseUser.body.ops[0].logins[0].type).toBe('corezoid');
  });

  test('should show your key in default Company with obj_type = company for user', async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        obj_id: newKeyUser,
        company_id,
        obj_type: 'company',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('user');
    expect(response.body.ops[0].obj_id).toBe(newKeyUser);
    expect(response.body.ops[0].logins[0].type).toBe('api');
  });

  test('should show user in default Company with obj_type = company for user', async () => {
    const responseUser = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        obj_id: user_id,
        company_id,
        obj_type: 'company',
      }),
    );
    expect(responseUser.status).toBe(200);
    expect(responseUser.body.ops[0].proc).toBe('ok');
    expect(responseUser.body.ops[0].obj).toBe('user');
    expect(responseUser.body.ops[0].obj_id).toBe(user_id);
    expect(responseUser.body.ops[0].logins[0].type).toBe('corezoid');
  });

  test('should show other key in default Company with obj_type = company for user', async () => {
    const responseKey = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        obj_id: newKeyAdmin,
        company_id,
        obj_type: 'company',
      }),
    );
    expect(responseKey.status).toBe(200);
    expect(responseKey.body.ops[0].proc).toBe('error');
    expect(responseKey.body.ops[0].description).toBe('Access denied');
  });

  test('should show key in other Company with obj_type = company for user', async () => {
    const responseKeyCompany = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        obj_id: keyNewCompany,
        company_id: newCompany_id,
        obj_type: 'company',
      }),
    );
    expect(responseKeyCompany.status).toBe(200);
    expect(responseKeyCompany.body.ops[0].proc).toBe('error');
    expect(responseKeyCompany.body.ops[0].description).toBe('Company does not exists');
  });

  test('should show key other Company with obj_type = company for user (without company_id)', async () => {
    const responseKeyCompany = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.SHOW,
        obj: OBJ_TYPE.USER,
        obj_id: keyNewCompany,
        obj_type: 'company',
      }),
    );
    expect(responseKeyCompany.status).toBe(200);
    expect(responseKeyCompany.body.ops[0].proc).toBe('error');
    expect(responseKeyCompany.body.ops[0].description).toBe('User has no rights');
  });

  afterAll(async () => {
    const deleteOperations = [
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newApiKeyId, company_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newApiKeyIdAdm, company_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.GROUP, obj_id: groupAdminObjId, company_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.GROUP, obj_id: groupSuperObjId, company_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, level: '', company_id, group_id: '0', obj_id: user_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.COMPANY, obj_id: newCompany_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.COMPANY, obj_id: company_id },
    ];

    await cleanupTestResources(apiUserCookie, deleteOperations);
  });
});
