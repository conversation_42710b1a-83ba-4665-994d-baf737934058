import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { ApiUserClient } from '../../../application/api/ApiUserClient';
import { SchemaValidator } from '../../../application/api/SchemaValidator';

import {
  createRequestWithOps,
  createRequestWithObj,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../utils/corezoidRequest';
import {
  setupTestConfiguration,
  createApiKey,
  findAdminGroup,
  linkUserToAdminGroup,
  createTestCompany,
} from './helpers/multitenantTestHelpers';

import listUser from '../../api/corezoid-api/schemas/v2/company_users/listCompanyUsersUser.schema.json';
import listApiKey from '../../api/corezoid-api/schemas/v2/company_users/listCompanyUsersApikey.schema.json';
import listGroup from '../../api/corezoid-api/schemas/v2/company_users/listCompanyUsersGroup.schema.json';

describe('Company Users List Operation in Multitenant Environment', () => {
  let company1_id: any;
  let superadmin_id: any;
  let apiUserCookie: ApiUserClient;

  let company2_id: string;
  let company2Name: string;

  let userApiClient: ApiKeyClient;
  let userApiKeyId: string | number;
  const userApiKeys: Array<string | number> = [];

  let adminApiClient: ApiKeyClient;
  let adminApiKeyId: string | number;
  const adminApiKeys: Array<string | number> = [];

  const superadminApiKeys: Array<string | number> = [];

  let adminGroupId: string | number;
  const adminCreatedGroups: Array<string | number> = [];
  const superadminCreatedGroups: Array<string | number> = [];

  let company2ApiKeyId: string | number;
  let company2GroupId: string | number;

  let testUserId: string | number;
  let company2TestUserId: string | number;

  let inviteUserHash: string;
  let company2InviteUserHash: string;

  const createGroup = async (title: string, companyId: string | number): Promise<any> => {
    const response = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id: companyId,
        title,
        obj_type: 'admins',
      }),
    );

    expect(response.status).toBe(RESP_STATUS.OK);
    return response.body;
  };

  const cleanupResources = async (): Promise<void> => {
    const ops: any[] = [];

    [...userApiKeys, ...adminApiKeys, ...superadminApiKeys].forEach(keyId => {
      if (keyId) {
        ops.push({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.USER,
          obj_id: keyId,
          company_id: company1_id,
        });
      }
    });

    [...adminCreatedGroups, ...superadminCreatedGroups].forEach(groupId => {
      if (groupId) {
        ops.push({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.GROUP,
          obj_id: groupId,
          company_id: company1_id,
        });
      }
    });

    if (company2ApiKeyId) {
      ops.push({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: company2ApiKeyId,
        company_id: company2_id,
      });
    }

    if (company2GroupId) {
      ops.push({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        obj_id: company2GroupId,
        company_id: company2_id,
      });
    }

    if (company2_id) {
      ops.push({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.COMPANY,
        obj_id: company2_id,
      });
    }

    ops.push({
      type: REQUEST_TYPE.DELETE,
      obj: OBJ_TYPE.COMPANY,
      obj_id: company1_id,
    });

    if (ops.length > 0) {
      const response = await apiUserCookie.request(
        createRequestWithObj({
          ops,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
    }
  };

  beforeAll(async () => {
    const config = await setupTestConfiguration();
    superadmin_id = config.superadmin_id;
    company1_id = config.company_id;
    apiUserCookie = config.apiUserCookie;
    company2Name = `Company2_${Date.now()}`;

    company2_id = await createTestCompany(apiUserCookie, company2Name);

    const userKeyResult = await createApiKey(apiUserCookie, 'UserApiKey', company1_id);
    userApiClient = userKeyResult.client;
    userApiKeyId = userKeyResult.keyId;
    userApiKeys.push(userApiKeyId);

    for (let i = 0; i < 2; i++) {
      const response = await userApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.USER,
          title: `UserCreatedKey_${i}`,
          logins: [{ type: 'api' }],
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);

      const keyId = response.body.ops[0].users[0].obj_id;
      userApiKeys.push(keyId);
    }

    const adminKeyResult = await createApiKey(apiUserCookie, 'AdminApiKey', company1_id);
    adminApiClient = adminKeyResult.client;
    adminApiKeyId = adminKeyResult.keyId;
    adminApiKeys.push(adminApiKeyId);

    adminGroupId = await findAdminGroup(apiUserCookie, company1_id);
    await linkUserToAdminGroup(apiUserCookie, adminApiKeyId, adminGroupId, company1_id);

    for (let i = 0; i < 2; i++) {
      const response = await adminApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.USER,
          title: `AdminCreatedKey_${i}`,
          logins: [{ type: 'api' }],
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);

      const keyId = response.body.ops[0].users[0].obj_id;
      adminApiKeys.push(keyId);
    }

    const keyResult = await createApiKey(apiUserCookie, `SuperadminApiKey`, company1_id);
    const keyId = keyResult.keyId;
    superadminApiKeys.push(keyId);

    const groupDataA = await adminApiClient.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id: company1_id,
        title: `AdminGroup`,
        obj_type: 'admins',
      }),
    );

    expect(groupDataA.status).toBe(RESP_STATUS.OK);
    expect(groupDataA.body.ops[0].proc).toBe(PROC_STATUS.OK);

    adminCreatedGroups.push(groupDataA.body.ops[0].obj_id);

    await linkUserToAdminGroup(apiUserCookie, userApiKeyId, groupDataA.body.ops[0].obj_id, company1_id);

    await linkUserToAdminGroup(apiUserCookie, superadminApiKeys[0], groupDataA.body.ops[0].obj_id, company1_id);

    await linkUserToAdminGroup(apiUserCookie, superadmin_id, groupDataA.body.ops[0].obj_id, company1_id);

    for (let i = 0; i < 2; i++) {
      const groupData = await createGroup(`SuperadminGroup_${i}`, company1_id);
      superadminCreatedGroups.push(groupData.ops[0].obj_id);

      if (i === 0) {
        await apiUserCookie.request(
          createRequestWithOps({
            type: REQUEST_TYPE.LINK,
            obj: OBJ_TYPE.USER,
            obj_id: superadminApiKeys[0],
            company_id: company1_id,
            group_id: groupData.ops[0].obj_id,
            level: 1,
          }),
        );
      }
    }

    const company2KeyResult = await createApiKey(apiUserCookie, 'Company2ApiKey', company2_id);
    company2ApiKeyId = company2KeyResult.keyId;

    const company2GroupData = await createGroup('Company2Group', company2_id);
    company2GroupId = company2GroupData.ops[0].obj_id;

    const testUserResponse = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id: company1_id,
        title: 'TestApiUser',
        logins: [{ type: 'api' }],
      }),
    );

    expect(testUserResponse.status).toBe(RESP_STATUS.OK);
    expect(testUserResponse.body.ops[0].proc).toBe(PROC_STATUS.OK);
    testUserId = testUserResponse.body.ops[0].users[0].obj_id;

    const company2TestUserResponse = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id: company2_id,
        title: 'TestApiUser2',
        logins: [{ type: 'api' }],
      }),
    );

    expect(company2TestUserResponse.status).toBe(RESP_STATUS.OK);
    expect(company2TestUserResponse.body.ops[0].proc).toBe(PROC_STATUS.OK);
    company2TestUserId = company2TestUserResponse.body.ops[0].users[0].obj_id;

    const inviteUserResponse = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id: company1_id,
        title: '',
        logins: [{ type: 'google', login: `testinvite_${Date.now()}@gmail.com` }],
        send_invite_if_user_not_exists: true,
      }),
    );

    expect(inviteUserResponse.status).toBe(RESP_STATUS.OK);
    expect(inviteUserResponse.body.ops[0].proc).toBe(PROC_STATUS.OK);
    inviteUserHash = inviteUserResponse.body.ops[0].invite_hash;

    const company2InviteUserResponse = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        company_id: company2_id,
        title: '',
        logins: [{ type: 'google', login: `testinvite2_${Date.now()}@gmail.com` }],
        send_invite_if_user_not_exists: true,
      }),
    );

    expect(company2InviteUserResponse.status).toBe(RESP_STATUS.OK);
    expect(company2InviteUserResponse.body.ops[0].proc).toBe(PROC_STATUS.OK);
    company2InviteUserHash = company2InviteUserResponse.body.ops[0].invite_hash;

    await new Promise(r => setTimeout(r, 3000));
  });

  describe('User Role - Company Users List Tests', () => {
    test('User can list company_users with filter=user - sees only accessible users', async () => {
      const response = await userApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'user',
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);
      expect(Array.isArray(response.body.ops[0].list)).toBe(true);
      SchemaValidator.validate(listUser, response.body);
    });

    test('User can list company_users with filter=api_key - sees only accessible API keys', async () => {
      const response = await userApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'api_key',
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);
      expect(Array.isArray(response.body.ops[0].list)).toBe(true);
      SchemaValidator.validate(listApiKey, response.body);
    });

    test('User can list company_users with filter=group - sees only accessible groups', async () => {
      const response = await userApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'group',
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);
      expect(Array.isArray(response.body.ops[0].list)).toBe(true);
      SchemaValidator.validate(listGroup, response.body);
    });

    test('User can list company_users with filter=shared - sees only shared resources', async () => {
      const response = await userApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'shared',
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);
      expect(Array.isArray(response.body.ops[0].list)).toBe(true);
      SchemaValidator.validate(listGroup, response.body);
    });

    test('User receives access denied when trying to access other company resources with filter=user', async () => {
      const response = await userApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'user',
          company_id: company2_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.ERROR);
      expect(response.body.ops[0].description).toBe('Access denied');
    });

    test('User receives access denied when trying to access other company resources with filter=api_key', async () => {
      const response = await userApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'api_key',
          company_id: company2_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.ERROR);
      expect(response.body.ops[0].description).toBe('Access denied');
    });

    test('User receives access denied when trying to access other company resources with filter=group', async () => {
      const response = await userApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'group',
          company_id: company2_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.ERROR);
      expect(response.body.ops[0].description).toBe('Access denied');
    });
  });

  describe('Admin Role - Company Users List Tests', () => {
    test('Admin can list company_users with filter=user - sees all company users', async () => {
      const response = await adminApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'user',
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);
      expect(Array.isArray(response.body.ops[0].list)).toBe(true);
      expect(response.body.ops[0].list.length).toBeGreaterThan(0);

      const userWithInvite = response.body.ops[0].list.find((user: any) => user.is_invite === true);
      expect(userWithInvite).toBeDefined();
      expect(userWithInvite.invite_hash).toBeDefined();
    });

    test('Admin can list company_users with filter=api_key - sees all company API keys', async () => {
      const response = await adminApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'api_key',
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);
      expect(Array.isArray(response.body.ops[0].list)).toBe(true);
      expect(response.body.ops[0].list.length).toBeGreaterThan(0);
      SchemaValidator.validate(listApiKey, response.body);
    });

    test('Admin can list company_users with filter=group - sees all company groups', async () => {
      const response = await adminApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'group',
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);
      expect(Array.isArray(response.body.ops[0].list)).toBe(true);
      expect(response.body.ops[0].list.length).toBeGreaterThan(0);
      SchemaValidator.validate(listGroup, response.body);
    });

    test('Admin can list company_users with filter=shared - sees all shared resources in company', async () => {
      const response = await adminApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'shared',
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);
      expect(Array.isArray(response.body.ops[0].list)).toBe(true);
      SchemaValidator.validate(listGroup, response.body);
    });

    test('Admin receives access denied when trying to access other company resources with filter=user', async () => {
      const response = await adminApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'user',
          company_id: company2_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.ERROR);
      expect(response.body.ops[0].description).toBe('Access denied');
    });

    test('Admin receives access denied when trying to access other company resources with filter=api_key', async () => {
      const response = await adminApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'api_key',
          company_id: company2_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.ERROR);
      expect(response.body.ops[0].description).toBe('Access denied');
    });

    test('Admin receives access denied when trying to access other company resources with filter=group', async () => {
      const response = await adminApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'group',
          company_id: company2_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.ERROR);
      expect(response.body.ops[0].description).toBe('Access denied');
    });
  });

  describe('Superadmin Role - Company Users List Tests', () => {
    test('Superadmin can list company_users across all companies with filter=user', async () => {
      const response1 = await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'user',
          company_id: company1_id,
        }),
      );

      expect(response1.status).toBe(RESP_STATUS.OK);
      expect(response1.body.ops[0].proc).toBe(PROC_STATUS.OK);
      expect(Array.isArray(response1.body.ops[0].list)).toBe(true);

      const userWithInvite1 = response1.body.ops[0].list.find((user: any) => user.is_invite === true);
      expect(userWithInvite1).toBeDefined();
      expect(userWithInvite1.invite_hash).toBeDefined();

      const response2 = await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'user',
          company_id: company2_id,
        }),
      );

      expect(response2.status).toBe(RESP_STATUS.OK);
      expect(response2.body.ops[0].proc).toBe(PROC_STATUS.OK);
      expect(Array.isArray(response2.body.ops[0].list)).toBe(true);

      const userWithInvite2 = response2.body.ops[0].list.find((user: any) => user.is_invite === true);
      expect(userWithInvite2).toBeDefined();
      expect(userWithInvite2.invite_hash).toBeDefined();
    });

    test('Superadmin can list company_users across all companies with filter=api_key', async () => {
      const response1 = await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'api_key',
          company_id: company1_id,
        }),
      );

      expect(response1.status).toBe(RESP_STATUS.OK);
      expect(response1.body.ops[0].proc).toBe(PROC_STATUS.OK);
      expect(Array.isArray(response1.body.ops[0].list)).toBe(true);
      SchemaValidator.validate(listApiKey, response1.body);

      const response2 = await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'api_key',
          company_id: company2_id,
        }),
      );

      expect(response2.status).toBe(RESP_STATUS.OK);
      expect(response2.body.ops[0].proc).toBe(PROC_STATUS.OK);
      expect(Array.isArray(response2.body.ops[0].list)).toBe(true);
      SchemaValidator.validate(listApiKey, response2.body);
    });

    test('Superadmin can list company_users across all companies with filter=group', async () => {
      const response1 = await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'group',
          company_id: company1_id,
        }),
      );

      expect(response1.status).toBe(RESP_STATUS.OK);
      expect(response1.body.ops[0].proc).toBe(PROC_STATUS.OK);
      expect(Array.isArray(response1.body.ops[0].list)).toBe(true);
      SchemaValidator.validate(listGroup, response1.body);

      const response2 = await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'group',
          company_id: company2_id,
        }),
      );

      expect(response2.status).toBe(RESP_STATUS.OK);
      expect(response2.body.ops[0].proc).toBe(PROC_STATUS.OK);
      expect(Array.isArray(response2.body.ops[0].list)).toBe(true);
      SchemaValidator.validate(listGroup, response2.body);
    });

    test('Superadmin can list company_users across all companies with filter=shared', async () => {
      const response1 = await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'shared',
          company_id: company1_id,
        }),
      );

      expect(response1.status).toBe(RESP_STATUS.OK);
      expect(response1.body.ops[0].proc).toBe(PROC_STATUS.OK);
      expect(Array.isArray(response1.body.ops[0].list)).toBe(true);
      expect(response1.body.ops[0].list[0].is_owner).toBe(false);
      SchemaValidator.validate(listGroup, response1.body);

      const response2 = await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          sort: 'title',
          order: 'asc',
          filter: 'shared',
          company_id: company2_id,
        }),
      );

      expect(response2.status).toBe(RESP_STATUS.OK);
      expect(response2.body.ops[0].proc).toBe(PROC_STATUS.OK);
      expect(Array.isArray(response2.body.ops[0].list)).toBe(true);
      SchemaValidator.validate(listGroup, response2.body);
    });
  });

  afterAll(async () => {
    if (inviteUserHash) {
      await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.INVITE,
          hash: inviteUserHash,
          company_id: company1_id,
        }),
      );
    }

    if (company2InviteUserHash) {
      await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.INVITE,
          hash: company2InviteUserHash,
          company_id: company2_id,
        }),
      );
    }

    if (testUserId) {
      await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.USER,
          obj_id: testUserId,
          company_id: company1_id,
        }),
      );
    }

    if (company2TestUserId) {
      await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.USER,
          obj_id: company2TestUserId,
          company_id: company2_id,
        }),
      );
    }

    await cleanupResources();

    const response = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY,
        filter: company2Name,
      }),
    );

    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
