import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { ApiUserClient } from '../../../application/api/ApiUserClient';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import { requestCreateObjNew } from '../../../application/api/ApiObj';
import {
  setupTestConfiguration,
  createApiKey,
  findAdminGroup,
  linkUserToAdminGroup,
  createTestCompany,
  cleanupTestResources,
} from './helpers/multitenantTestHelpers';

describe('Privs list objs multitenant (positive)', (): void => {
  let company_id: any;
  let newCompany_idSuperAdmin: string;
  let newCompanyNameSuperAdmin: string;
  let group_id: string | number;
  let newApi: ApiKeyClient;
  let newApiKeyId: string | number;
  let newApiAdm: ApiKeyClient;
  let newApiKeyIdAdm: string | number;
  let newApiUser: ApiKeyClient;
  let newConvAdmin: string | number;
  let newConv1: string | number;
  let newConv2: string | number;
  let newKeyAdminId: string | number;
  let newKeyUserId1: string | number;
  let newKeyUserId2: string | number;
  let apiUserCookie: ApiUserClient;

  beforeAll(
    async (): Promise<void> => {
      const config = await setupTestConfiguration();
      company_id = config.company_id;
      apiUserCookie = config.apiUserCookie;
      newCompanyNameSuperAdmin = 'newCompanySuperAdmin';

      const apiKeyResult = await createApiKey(apiUserCookie, 'apiUser1', company_id);
      newApi = apiKeyResult.client;
      newApiKeyId = apiKeyResult.keyId;

      const adminKeyResult = await createApiKey(apiUserCookie, 'apiUserAdmin', company_id);
      newApiAdm = adminKeyResult.client;
      newApiKeyIdAdm = adminKeyResult.keyId;

      group_id = await findAdminGroup(apiUserCookie, company_id);
      await linkUserToAdminGroup(apiUserCookie, newApiKeyIdAdm, group_id, company_id);

      const responseConvAdm = await requestCreateObjNew(newApiAdm, OBJ_TYPE.CONV, company_id, `Conv_${Date.now()}`, {
        conv_type: 'process',
      });
      newConvAdmin = responseConvAdm.body.ops[0].obj_id;

      const responseKeyAdmin = await requestCreateObjNew(newApiAdm, OBJ_TYPE.USER, company_id, `keyUserAdmin`, {
        logins: [{ type: 'api' }],
      });
      expect(responseKeyAdmin.status).toBe(200);
      newKeyAdminId = responseKeyAdmin.body.ops[0].users[0].obj_id;

      const responseConv1 = await requestCreateObjNew(newApi, OBJ_TYPE.CONV, company_id, `Conv_${Date.now()}`, {
        conv_type: 'process',
      });
      newConv1 = responseConv1.body.ops[0].obj_id;

      const responseKey1 = await requestCreateObjNew(newApi, OBJ_TYPE.USER, company_id, `keyUser1`, {
        logins: [{ type: 'api' }],
      });
      expect(responseKey1.status).toBe(200);
      newKeyUserId1 = responseKey1.body.ops[0].users[0].obj_id;

      newCompany_idSuperAdmin = await createTestCompany(apiUserCookie, newCompanyNameSuperAdmin);

      const userKeyResult = await createApiKey(apiUserCookie, 'apiUser2', newCompany_idSuperAdmin);
      newApiUser = userKeyResult.client;

      const responseConv2 = await requestCreateObjNew(
        newApiUser,
        OBJ_TYPE.CONV,
        newCompany_idSuperAdmin,
        `Conv_${Date.now()}`,
        {
          conv_type: 'process',
        },
      );
      newConv2 = responseConv2.body.ops[0].obj_id;

      const responseKey2 = await requestCreateObjNew(newApiUser, OBJ_TYPE.USER, newCompany_idSuperAdmin, `keyUser2`, {
        logins: [{ type: 'api' }],
      });
      expect(responseKey2.status).toBe(200);
      newKeyUserId2 = responseKey2.body.ops[0].users[0].obj_id;

      await new Promise(r => setTimeout(r, 3000));
    },
  );

  const testCasesCompany = [
    {
      description: 'should list objs obj_type = company in default Company for admin',
      user: (): ApiKeyClient => newApiAdm,
      company_idSearch: (): string | number => newCompany_idSuperAdmin,
      userCompany_id: (): string | number => company_id,
    },
    {
      description: 'should list objs obj_type = company in default Company for user1 from company default',
      user: (): ApiKeyClient => newApi,
      company_idSearch: (): string | number => newCompany_idSuperAdmin,
      userCompany_id: (): string | number => company_id,
    },
    {
      description: 'should list objs obj_type = company in default Company for user2 from new company',
      user: (): ApiKeyClient => newApiUser,
      company_idSearch: (): string | number => company_id,
      userCompany_id: (): string | number => newCompany_idSuperAdmin,
    },
  ];

  describe.each(testCasesCompany)('$description', ({ user, userCompany_id, company_idSearch }): void => {
    test(`shouldn't list objs company with obj_type = company`, async (): Promise<void> => {
      const response = await user().request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.OBJS,
          company_id: userCompany_id(),
          filter: 'company',
          pattern: company_idSearch(),
          limit: 31,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toBe('ok');
      expect(response.body.ops[0].obj).toBe('objs');
      expect(response.body.ops[0].list).toBeArrayOfSize(0);
    });

    test(`should list objs company with obj_type = company`, async (): Promise<void> => {
      const response = await user().request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.OBJS,
          company_id: userCompany_id(),
          filter: 'company',
          pattern: userCompany_id(),
          limit: 31,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toBe('ok');
      expect(response.body.ops[0].obj).toBe('objs');
      expect(response.body.ops[0].list[0].obj_id).toBe(userCompany_id());
    });
  });

  test('should list objs obj_type = company in default Company for superadmin', async (): Promise<void> => {
    const responseApi = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.OBJS,
        filter: 'company',
        pattern: `${newCompany_idSuperAdmin}`,
        company_id,
        limit: 31,
      }),
    );
    expect(responseApi.status).toBe(200);
    expect(responseApi.body.ops[0].proc).toBe('ok');
    expect(responseApi.body.ops[0].obj).toBe('objs');
    expect(responseApi.body.ops[0].list[0].title).toBe(newCompanyNameSuperAdmin);
    expect(responseApi.body.ops[0].list[0].obj_id).toBe(newCompany_idSuperAdmin);
  });

  const testCasesConvPositive = [
    {
      description: 'should list objs obj_type = conv admin in default Company for admin',
      user: (): ApiKeyClient => newApiAdm,
      company_idSearch: (): string | number => company_id,
      conv: (): string | number => newConvAdmin,
    },

    {
      description: 'should list objs obj_type = conv in default Company for user1',
      user: (): ApiKeyClient => newApi,
      company_idSearch: (): string | number => company_id,
      conv: (): string | number => newConv1,
    },
    {
      description: 'should list objs obj_type = conv in new Company for user2',
      user: (): ApiKeyClient => newApiUser,
      company_idSearch: (): string | number => newCompany_idSuperAdmin,
      conv: (): string | number => newConv2,
    },
  ];

  describe.each(testCasesConvPositive)('$description', ({ user, conv, company_idSearch }): void => {
    test(`should list objs with obj_type = conv`, async (): Promise<void> => {
      const response = await user().request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.OBJS,
          company_id: company_idSearch(),
          filter: 'conv',
          pattern: `${conv()}`,
          limit: 31,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toBe('ok');
      expect(response.body.ops[0].obj).toBe('objs');
      expect(response.body.ops[0].list[0].obj_id).toBe(conv());
    });
  });

  const testCasesConvNegative = [
    {
      description: 'should list objs obj_type = conv admin in default Company for admin',
      user: (): ApiKeyClient => newApiAdm,
      company_idSearch: (): string | number => company_id,
      conv: (): string | number => newConv2,
    },
    {
      description: 'should list objs obj_type = conv user in default Company for admin',
      user: (): ApiKeyClient => newApiAdm,
      company_idSearch: (): string | number => newCompany_idSuperAdmin,
      conv: (): string | number => newConv2,
    },
    {
      description: 'should list objs obj_type = conv in default Company for user1',
      user: (): ApiKeyClient => newApi,
      company_idSearch: (): string | number => company_id,
      conv: (): string | number => newConvAdmin,
    },
    {
      description: 'should list objs obj_type = conv in default Company for user1',
      user: (): ApiKeyClient => newApi,
      company_idSearch: (): string | number => company_id,
      conv: (): string | number => newConv2,
    },
    {
      description: 'should list objs obj_type = conv in default Company for user1',
      user: (): ApiKeyClient => newApi,
      company_idSearch: (): string | number => newCompany_idSuperAdmin,
      conv: (): string | number => newConv2,
    },
    {
      description: 'should list objs obj_type = conv in new Company for user2',
      user: (): ApiKeyClient => newApiUser,
      company_idSearch: (): string | number => newCompany_idSuperAdmin,
      conv: (): string | number => newConv1,
    },
    {
      description: 'should list objs obj_type = conv in new Company for user2',
      user: (): ApiKeyClient => newApiUser,
      company_idSearch: (): string | number => company_id,
      conv: (): string | number => newConv1,
    },
  ];

  describe.each(testCasesConvNegative)('$description', ({ user, conv, company_idSearch }): void => {
    test(`shouldn't list objs with obj_type = conv`, async (): Promise<void> => {
      const response = await user().request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.OBJS,
          company_id: company_idSearch(),
          filter: 'conv',
          pattern: `${conv()}`,
          limit: 31,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toBe('ok');
      expect(response.body.ops[0].obj).toBe('objs');
      expect(response.body.ops[0].list).toBeArrayOfSize(0);
    });
  });

  const testCasesConvSuperadmin = [
    {
      description: 'should list objs obj_type = conv user1 in default Company for superadmin',
      conv: (): string | number => newConv1,
      company: (): string | number => company_id,
    },
    {
      description: 'should list objs obj_type = conv user2 in new Company for superadmin',
      conv: (): string | number => newConv2,
      company: (): string | number => newCompany_idSuperAdmin,
    },
    {
      description: 'should list objs obj_type = conv userAdmin in default Company for superadmin',
      conv: (): string | number => newConvAdmin,
      company: (): string | number => company_id,
    },
  ];

  describe.each(testCasesConvSuperadmin)('$description', ({ conv, company }): void => {
    test('should list objs obj_type = conv', async (): Promise<void> => {
      const responseApi1 = await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.OBJS,
          filter: 'conv',
          pattern: `${conv()}`,
          company_id: company(),
          limit: 31,
        }),
      );
      expect(responseApi1.status).toBe(200);
      expect(responseApi1.body.ops[0].proc).toBe('ok');
      expect(responseApi1.body.ops[0].obj).toBe('objs');
      expect(responseApi1.body.ops[0].list[0].obj_id).toBe(conv());
    });
  });

  const testCasesUserSuperadmin = [
    {
      description: 'should list objs obj_type = user user1 in default Company for superadmin',
      user: (): string => 'apiUser1',
      company: (): string | number => company_id,
    },
    {
      description: 'should list objs obj_type = user user2 in new Company for superadmin',
      user: (): string => 'apiUser2',
      company: (): string | number => newCompany_idSuperAdmin,
    },
    {
      description: 'should list objs obj_type = user userAdmin in default Company for superadmin',
      user: (): string => 'apiUserAdmin',
      company: (): string | number => company_id,
    },
  ];

  describe.each(testCasesUserSuperadmin)('$description', ({ user, company }): void => {
    test('should list objs obj_type = conv', async (): Promise<void> => {
      const responseApi1 = await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.OBJS,
          filter: 'user',
          pattern: `${user()}`,
          company_id: company(),
          limit: 31,
        }),
      );
      expect(responseApi1.status).toBe(200);
      expect(responseApi1.body.ops[0].proc).toBe('ok');
      expect(responseApi1.body.ops[0].obj).toBe('objs');
      expect(responseApi1.body.ops[0].list[0].title).toBe(user());
    });
  });

  const testCasesUserNegative = [
    {
      description: `shouldn't list objs obj_type = user apiUser2 in default Company for admin`,
      userSearch: (): string => 'apiUser2',
      user: (): ApiKeyClient => newApiAdm,
      company: (): string | number => company_id,
    },
    {
      description: `shouldn't list objs obj_type = user apiUser1 in default Company for admin`,
      userSearch: (): string => 'apiUser1',
      user: (): ApiKeyClient => newApiAdm,
      company: (): string | number => company_id,
    },
    {
      description: `shouldn't list objs obj_type = user apiUser1 in default Company for user2`,
      user: (): ApiKeyClient => newApiUser,
      userSearch: (): string => 'apiUser1',
      company: (): string | number => newCompany_idSuperAdmin,
    },
    {
      description: `shouldn't list objs obj_type = user apiUser2 in default Company for user2`,
      user: (): ApiKeyClient => newApiUser,
      userSearch: (): string => 'apiUser2',
      company: (): string | number => company_id,
    },
    {
      description: `shouldn't list objs obj_type = user apiUserAdmin in default Company for user1`,
      user: (): ApiKeyClient => newApi,
      userSearch: (): string => 'apiUserAdmin',
      company: (): string | number => company_id,
    },
    {
      description: `shouldn't list objs obj_type = user apiUser2 in default Company for user1`,
      user: (): ApiKeyClient => newApi,
      userSearch: (): string => 'apiUser2',
      company: (): string | number => newCompany_idSuperAdmin,
    },
  ];

  describe.each(testCasesUserNegative)('$description', ({ userSearch, user, company }): void => {
    test('should list objs obj_type = user', async (): Promise<void> => {
      const response = await user().request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.OBJS,
          company_id: company(),
          filter: 'user',
          pattern: `${userSearch()}`,
          limit: 31,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toBe('ok');
      expect(response.body.ops[0].obj).toBe('objs');
      expect(response.body.ops[0].list).toBeArrayOfSize(0);
    });
  });

  const testCasesUserPositive = [
    {
      description: 'should list objs obj_type = user for UserAdmin his key - keyUserAdmin',
      userSearch: (): string => 'keyUserAdmin',
      user: (): ApiKeyClient => newApiAdm,
      company: (): string | number => company_id,
    },
    {
      description: 'should list objs obj_type = user for User1 his key - keyUser1',
      userSearch: (): string => 'keyUser1',
      user: (): ApiKeyClient => newApi,
      company: (): string | number => company_id,
    },
    {
      description: 'should list objs obj_type = user for User2 his key - keyUser2',
      userSearch: (): string => 'keyUser2',
      user: (): ApiKeyClient => newApiUser,
      company: (): string | number => newCompany_idSuperAdmin,
    },
  ];

  describe.each(testCasesUserPositive)('$description', ({ userSearch, user, company }): void => {
    test('should list objs obj_type = user', async (): Promise<void> => {
      const response = await user().request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.OBJS,
          company_id: company(),
          filter: 'user',
          pattern: `${userSearch()}`,
          limit: 31,
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toBe('ok');
      expect(response.body.ops[0].obj).toBe('objs');
      expect(response.body.ops[0].list[0].obj_type).toBe('user');
    });
  });

  afterAll(
    async (): Promise<void> => {
      const deleteOperations = [
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newApiKeyId, company_id },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newApiKeyIdAdm, company_id },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newKeyAdminId, company_id },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, level: '', company_id, group_id: '0' },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newKeyUserId1, company_id },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newKeyUserId2, company_id: newCompany_idSuperAdmin },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.COMPANY, obj_id: newCompany_idSuperAdmin },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.COMPANY, obj_id: company_id },
      ];

      await cleanupTestResources(apiUserCookie, deleteOperations);
    },
  );
});
