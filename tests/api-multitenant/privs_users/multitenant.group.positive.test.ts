import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { ApiUserClient } from '../../../application/api/ApiUserClient';
import { axiosInstance } from '../../../application/api/AxiosClient';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import {
  setupTestConfiguration,
  createApiKey,
  findAdminGroup,
  linkUserToAdminGroup,
  createTestCompany,
  cleanupTestResources,
} from './helpers/multitenantTestHelpers';

describe('Privs List/Modify group multitenant (positive)', () => {
  let company_id: any;
  let newCompany_id: string;
  let groupAdminObjId: string | number;
  let groupSuperObjId: string | number;
  let user_id: string | number;
  let group_id: string | number;
  let newApi: ApiKeyClient;
  let newApiKeyId: string | number;
  let newApiAdm: ApiKeyClient;
  let newApiKeyIdAdm: string | number;
  let groupNewCompany: string | number;
  let hash: string;
  let login: string;
  let apiUserCookie: ApiUserClient;

  beforeAll(async () => {
    const config = await setupTestConfiguration();
    company_id = config.company_id;
    apiUserCookie = config.apiUserCookie;
    login = `testuser-${Date.now()}@gmail.com`;

    const CreateUserResponse = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.USER,
        title: 'User',
        logins: [{ type: 'google', login }],
        company_id,
        send_invite_if_user_not_exists: true,
      }),
    );
    expect(CreateUserResponse.status).toBe(200);

    await new Promise(r => setTimeout(r, 5000));

    const ListUsersResponse = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(ListUsersResponse.status).toBe(200);
    hash = (ListUsersResponse.body.ops[0].list as Array<any>).find(item => item.title === login).invite_hash;

    const CreateUser = await axiosInstance({
      method: 'POST',
      url: `${config.host}api/2/plugins/invite/${hash}`,
      data: { nick: 'User', password: 'Test12345!' },
    });
    expect(CreateUser.status).toBe(200);

    await new Promise(r => setTimeout(r, 2000));

    const apiKeyResult = await createApiKey(apiUserCookie, 'apiUser', company_id);
    newApi = apiKeyResult.client;
    newApiKeyId = apiKeyResult.keyId;

    const adminKeyResult = await createApiKey(apiUserCookie, 'apiUserAdmin', company_id);
    newApiAdm = adminKeyResult.client;
    newApiKeyIdAdm = adminKeyResult.keyId;

    group_id = await findAdminGroup(apiUserCookie, company_id);

    const listUserResponse = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY_USERS,
        filter: 'user',
        company_id,
      }),
    );
    expect(listUserResponse.status).toBe(200);
    user_id = listUserResponse.body.ops[0].list[0].obj_id;

    await linkUserToAdminGroup(apiUserCookie, newApiKeyIdAdm, group_id, company_id);

    const response = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id,
        obj_type: 'admins',
        title: 'testGroupAdmin',
      }),
    );
    expect(response.status).toBe(200);
    groupAdminObjId = response.body.ops[0].obj_id;
    const responseLink = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LINK,
        obj: OBJ_TYPE.USER,
        company_id,
        level: 1,
        group_id: groupAdminObjId,
        obj_id: newApiKeyId,
      }),
    );
    expect(responseLink.status).toBe(200);

    const createGroupresponse = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        obj_type: 'admins',
        company_id,
        title: 'testGroupSuper',
      }),
    );
    expect(createGroupresponse.status).toBe(200);
    groupSuperObjId = createGroupresponse.body.ops[0].obj_id;

    newCompany_id = await createTestCompany(apiUserCookie, 'new');

    const createGroupResponse = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        obj_type: 'admins',
        company_id: newCompany_id,
        title: 'testGroupSuper',
      }),
    );
    expect(createGroupResponse.status).toBe(200);
    groupNewCompany = createGroupResponse.body.ops[0].obj_id;
  });

  test('should list your group in Company for superadmin', async () => {
    const response = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupSuperObjId,
        list_obj: 'user',
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('group');
    expect(response.body.ops[0].list).toBeArrayOfSize(0);
  });

  test('should list admins group in Company for superadmin', async () => {
    const responseAdminGroup = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupAdminObjId,
        list_obj: 'user',
        company_id,
      }),
    );
    expect(responseAdminGroup.status).toBe(200);
    expect(responseAdminGroup.body.ops[0].proc).toBe('ok');
    expect(responseAdminGroup.body.ops[0].obj).toBe('group');
    expect(responseAdminGroup.body.ops[0].list[0].obj_id).toBe(newApiKeyId);
  });

  test('should list group in other Company for superadmin', async () => {
    const responseAdminGroup = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupNewCompany,
        list_obj: 'user',
        company_id: newCompany_id,
      }),
    );
    expect(responseAdminGroup.status).toBe(200);
    expect(responseAdminGroup.body.ops[0].proc).toBe('ok');
    expect(responseAdminGroup.body.ops[0].obj).toBe('group');
  });

  test('should list your group in Company for admin', async () => {
    const response = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupAdminObjId,
        company_id,
        list_obj: 'user',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('group');
    expect(response.body.ops[0].list[0].obj_id).toBe(newApiKeyId);
  });

  test('should list group (with access) in Company for admin', async () => {
    const responseWithAccess = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupSuperObjId,
        company_id,
        list_obj: 'user',
      }),
    );
    expect(responseWithAccess.status).toBe(200);
    expect(responseWithAccess.body.ops[0].proc).toBe('ok');
    expect(responseWithAccess.body.ops[0].obj).toBe('group');
    expect(responseWithAccess.body.ops[0].list).toBeArrayOfSize(0);
  });

  test('should list group in another Company for admin (Access denied)', async () => {
    const response = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupNewCompany,
        company_id: newCompany_id,
        list_obj: 'user',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Access denied');
  });

  test('should list group in another Company (without company_id) for admin (Access denied)', async () => {
    const responseWithoutCompany = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupNewCompany,
        list_obj: 'user',
      }),
    );
    expect(responseWithoutCompany.status).toBe(200);
    expect(responseWithoutCompany.body.ops[0].proc).toBe('error');
    expect(responseWithoutCompany.body.ops[0].description).toBe('Access denied');
  });

  test('should list group in Company for USER with access', async () => {
    const responseWithAccess = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupAdminObjId,
        company_id,
        list_obj: 'user',
      }),
    );
    expect(responseWithAccess.status).toBe(200);
    expect(responseWithAccess.body.ops[0].proc).toBe('ok');
    expect(responseWithAccess.body.ops[0].obj).toBe('group');
    expect(responseWithAccess.body.ops[0].list[0].obj_id).toBe(newApiKeyId);
  });

  test('should list group in Company for USER without access', async () => {
    const responseWithoutAccess = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupSuperObjId,
        company_id,
        list_obj: 'user',
      }),
    );
    expect(responseWithoutAccess.status).toBe(200);
    expect(responseWithoutAccess.body.ops[0].proc).toBe('error');
    expect(responseWithoutAccess.body.ops[0].description).toBe('Access denied');
  });

  test('should list group in another Company for user (Access denied)', async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupNewCompany,
        company_id: newCompany_id,
        list_obj: 'user',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Access denied');
  });

  test('should list group in another Company (without company_id) for user (Access denied)', async () => {
    const responseWithoutCompany = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupNewCompany,
        list_obj: 'user',
      }),
    );
    expect(responseWithoutCompany.status).toBe(200);
    expect(responseWithoutCompany.body.ops[0].proc).toBe('error');
    expect(responseWithoutCompany.body.ops[0].description).toBe('Access denied');
  });

  test('should modify your group in Company for superadmin (ignore status)', async () => {
    const response = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupSuperObjId,
        title: '123',
        obj_type: 'admins',
        status: 'deleted',
        company_id,
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('group');

    const responseList = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupSuperObjId,
        list_obj: 'user',
        company_id,
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toBe('ok');
  });

  test('should modify admins group in Company for superadmin (ignore status)', async () => {
    const responseAdminGroup = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupAdminObjId,
        title: '123',
        obj_type: 'admins',
        status: 'deleted',
        company_id,
      }),
    );
    expect(responseAdminGroup.status).toBe(200);
    expect(responseAdminGroup.body.ops[0].proc).toBe('ok');
    expect(responseAdminGroup.body.ops[0].obj).toBe('group');

    const responseAdminGroupList = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupAdminObjId,
        list_obj: 'user',
        company_id,
      }),
    );
    expect(responseAdminGroupList.status).toBe(200);
    expect(responseAdminGroupList.body.ops[0].proc).toBe('ok');
  });

  test('should modify group in other Company for superadmin (ignore status)', async () => {
    const responseAdminGroup = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupNewCompany,
        title: '123',
        obj_type: 'admins',
        status: 'deleted',
        company_id: newCompany_id,
      }),
    );
    expect(responseAdminGroup.status).toBe(200);
    expect(responseAdminGroup.body.ops[0].proc).toBe('ok');
    expect(responseAdminGroup.body.ops[0].obj).toBe('group');

    const responseAdminGroupList = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupNewCompany,
        list_obj: 'user',
        company_id: newCompany_id,
      }),
    );
    expect(responseAdminGroupList.status).toBe(200);
    expect(responseAdminGroupList.body.ops[0].proc).toBe('ok');
  });

  test(`shouldn't modify group in other Company for superadmin (ignore status)(request with other superadmins company)`, async () => {
    const responseAdminGroup = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupNewCompany,
        title: '123',
        obj_type: 'admins',
        status: 'deleted',
        company_id,
      }),
    );
    expect(responseAdminGroup.status).toBe(200);
    expect(responseAdminGroup.body.ops[0].proc).toBe('error');
    expect(responseAdminGroup.body.ops[0].description).toBe(
      `Object's company ID does not match company ID in the request`,
    );

    const responseAdminGroupList = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupNewCompany,
        list_obj: 'user',
        company_id,
      }),
    );
    expect(responseAdminGroupList.status).toBe(200);
    expect(responseAdminGroupList.body.ops[0].proc).toBe('ok');
  });

  test('should modify your group in Company for admin (ignore status)', async () => {
    const response = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupAdminObjId,
        company_id,
        title: 'testAdmin',
        obj_type: 'admins',
        status: 'deleted',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('ok');
    expect(response.body.ops[0].obj).toBe('group');

    const responseList = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupAdminObjId,
        company_id,
        list_obj: 'user',
      }),
    );
    expect(responseList.status).toBe(200);
    expect(responseList.body.ops[0].proc).toBe('ok');
    expect(responseList.body.ops[0].obj).toBe('group');
  });

  test('should modify group (with access) in Company for admin (ignore status)', async () => {
    const responseWithAccess = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupSuperObjId,
        company_id,
        title: 'testAdmin',
        obj_type: 'admins',
        status: 'deleted',
      }),
    );
    expect(responseWithAccess.status).toBe(200);
    expect(responseWithAccess.body.ops[0].proc).toBe('ok');
    expect(responseWithAccess.body.ops[0].obj).toBe('group');

    const responseWithAccessList = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupSuperObjId,
        company_id,
        list_obj: 'user',
      }),
    );
    expect(responseWithAccessList.status).toBe(200);
    expect(responseWithAccessList.body.ops[0].proc).toBe('ok');
    expect(responseWithAccessList.body.ops[0].obj).toBe('group');
  });

  test('shouldnt modify group in another Company for admin (Access denied)', async () => {
    const response = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupNewCompany,
        company_id: newCompany_id,
        title: 'testAdmin',
        obj_type: 'admins',
        status: 'deleted',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Access denied');
  });

  test('shouldnt modify group in another Company (without company_id) for admin (Access denied)', async () => {
    const responseWithoutCompany = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupNewCompany,
        title: 'testAdmin',
        obj_type: 'admins',
        status: 'deleted',
      }),
    );
    expect(responseWithoutCompany.status).toBe(200);
    expect(responseWithoutCompany.body.ops[0].proc).toBe('error');
    expect(responseWithoutCompany.body.ops[0].description).toBe('Access denied');
  });

  test('shouldnt modify group in Company for USER with access', async () => {
    const responseWithAccess = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupAdminObjId,
        company_id,
        title: 'testAdmin',
        obj_type: 'admins',
        status: 'deleted',
      }),
    );
    expect(responseWithAccess.status).toBe(200);
    expect(responseWithAccess.body.ops[0].proc).toBe('error');
    expect(responseWithAccess.body.ops[0].description).toBe('Access denied');
  });

  test('shouldnt modify group in Company for USER without access', async () => {
    const responseWithoutAccess = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupSuperObjId,
        company_id,
        title: 'testAdmin',
        obj_type: 'admins',
        status: 'deleted',
      }),
    );
    expect(responseWithoutAccess.status).toBe(200);
    expect(responseWithoutAccess.body.ops[0].proc).toBe('error');
    expect(responseWithoutAccess.body.ops[0].description).toBe('Access denied');
  });

  test('shouldnt modify group in another Company for user (Access denied)', async () => {
    const response = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupNewCompany,
        company_id: newCompany_id,
        title: 'testAdmin',
        obj_type: 'admins',
        status: 'deleted',
      }),
    );
    expect(response.status).toBe(200);
    expect(response.body.ops[0].proc).toBe('error');
    expect(response.body.ops[0].description).toBe('Access denied');
  });

  test('shouldnt modify group in another Company (without company_id) for user (Access denied)', async () => {
    const responseWithoutCompany = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.GROUP,
        obj_id: groupNewCompany,
        title: 'testAdmin',
        obj_type: 'admins',
        status: 'deleted',
      }),
    );
    expect(responseWithoutCompany.status).toBe(200);
    expect(responseWithoutCompany.body.ops[0].proc).toBe('error');
    expect(responseWithoutCompany.body.ops[0].description).toBe('Access denied');
  });

  afterAll(async () => {
    const deleteOperations = [
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newApiKeyId, company_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newApiKeyIdAdm, company_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.GROUP, obj_id: groupAdminObjId, company_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.GROUP, obj_id: groupSuperObjId, company_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, level: '', company_id, group_id: '0', obj_id: user_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.COMPANY, obj_id: newCompany_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.COMPANY, obj_id: company_id },
    ];

    await cleanupTestResources(apiUserCookie, deleteOperations);
  });
});
