import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { ApiUserClient } from '../../../application/api/ApiUserClient';
import { axiosInstance } from '../../../application/api/AxiosClient';
import { User } from '../../../infrastructure/model/User';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import { requestCreateObjNew } from '../../../application/api/ApiObj';
import {
  setupTestConfiguration,
  createApiKey,
  findAdminGroup,
  linkUserToAdminGroup,
  createTestCompany,
  cleanupTestResources,
} from './helpers/multitenantTestHelpers';

describe('Privs list objs multitenant (positive)', (): void => {
  let host: string;
  let company_id: any;
  let newCompany_idSuperAdmin: string;
  let newCompanyNameSuperAdmin: string;
  let user: User;
  let user_id: string | number;
  let group_id: string | number;
  let cookieUser: string;
  let newApi: ApiKeyClient;
  let newApiKeyId: string | number;
  let newApiAdm: ApiKeyClient;
  let newApiKeyIdAdm: string | number;
  let newApiUser: ApiKeyClient;
  let newApiKeyIdUser: string | number;
  let newKeyAdminId: string | number;
  let newKeyUserId1: string | number;
  let newKeyUserId2: string | number;
  let newKeyUserId3: string | number;
  let apiUserCookie: ApiUserClient;

  beforeAll(
    async (): Promise<void> => {
      const config = await setupTestConfiguration();
      host = config.host;
      company_id = config.company_id;
      user = config.user;
      apiUserCookie = config.apiUserCookie;
      cookieUser = user.cookieUser;
      newCompanyNameSuperAdmin = 'newCompanySuperAdmin';

      const apiKeyResult = await createApiKey(apiUserCookie, 'apiUser1', company_id);
      newApi = apiKeyResult.client;
      newApiKeyId = apiKeyResult.keyId;

      const adminKeyResult = await createApiKey(apiUserCookie, 'apiUserAdmin', company_id);
      newApiAdm = adminKeyResult.client;
      newApiKeyIdAdm = adminKeyResult.keyId;

      const meResponse = await axiosInstance({
        method: 'GET',
        url: `${host}auth/me`,
        headers: {
          Cookie: cookieUser,
          Origin: host,
        },
      });
      expect(meResponse.data.result).toBe('ok');
      user_id = meResponse.data.user_id;

      group_id = await findAdminGroup(apiUserCookie, company_id);
      await linkUserToAdminGroup(apiUserCookie, newApiKeyIdAdm, group_id, company_id);

      const responseKeyAdmin = await requestCreateObjNew(newApiAdm, OBJ_TYPE.USER, company_id, `keyUserAdmin`, {
        logins: [{ type: 'api' }],
      });
      expect(responseKeyAdmin.status).toBe(200);
      newKeyAdminId = responseKeyAdmin.body.ops[0].users[0].obj_id;

      const responseKey1 = await requestCreateObjNew(newApi, OBJ_TYPE.USER, company_id, `keyUser1`, {
        logins: [{ type: 'api' }],
      });
      expect(responseKey1.status).toBe(200);
      newKeyUserId1 = responseKey1.body.ops[0].users[0].obj_id;

      newCompany_idSuperAdmin = await createTestCompany(apiUserCookie, newCompanyNameSuperAdmin);

      const userKeyResult = await createApiKey(apiUserCookie, 'apiUser2', newCompany_idSuperAdmin);
      newApiUser = userKeyResult.client;
      newApiKeyIdUser = userKeyResult.keyId;

      const responseKey2 = await requestCreateObjNew(newApiUser, OBJ_TYPE.USER, newCompany_idSuperAdmin, `keyUser2`, {
        logins: [{ type: 'api' }],
      });
      expect(responseKey2.status).toBe(200);
      newKeyUserId2 = responseKey2.body.ops[0].users[0].obj_id;

      const responseKey3 = await requestCreateObjNew(newApiUser, OBJ_TYPE.USER, newCompany_idSuperAdmin, `keyUser3`, {
        logins: [{ type: 'api' }],
      });
      expect(responseKey3.status).toBe(200);
      newKeyUserId3 = responseKey3.body.ops[0].users[0].obj_id;

      await new Promise(r => setTimeout(r, 3000));
    },
  );

  const testCasesSuperAdmin = [
    {
      description: `should link user_to_company by superadmin to new company`,
      key: (): string | number => newKeyAdminId,
      company_id: (): string | number => newCompany_idSuperAdmin,
    },
    {
      description: `should link user_to_company by superadmin to default company`,
      key: (): string | number => newKeyUserId2,
      company_id: (): any => company_id,
    },
  ];

  describe.each(testCasesSuperAdmin)('$description', ({ key, company_id }): void => {
    test('should link user to company for superadmin', async (): Promise<void> => {
      const responseLinkKey = await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.USER_TO_COMPANY,
          obj_id: key(),
          company_id: company_id(),
        }),
      );
      expect(responseLinkKey.status).toBe(200);
      expect(responseLinkKey.body.ops[0].proc).toBe('ok');
      expect(responseLinkKey.body.ops[0].obj).toBe('user_to_company');
      expect(responseLinkKey.body.ops[0].obj_id).toBe(key());

      const responseList = await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.LIST,
          obj: OBJ_TYPE.COMPANY_USERS,
          filter: 'api_key',
          company_id: company_id(),
        }),
      );
      expect(responseList.status).toBe(200);
      expect(responseList.body.ops[0].proc).toBe('ok');
      expect(responseList.body.ops[0].list).toEqual(
        expect.arrayContaining([expect.objectContaining({ obj_id: key() })]),
      );
    });
  });

  const testCasesUser = [
    {
      description: `shouldn't link user_to_company by user to his company`,
      user: (): ApiKeyClient => newApiUser,
      key: (): string | number => newKeyUserId1,
      company_id: (): string | number => newCompany_idSuperAdmin,
      proc: (): string => 'error',
      result: (): string => 'Access denied',
    },
    {
      description: `shouldn't link user_to_company by user to other company (User already in company)`,
      user: (): ApiKeyClient => newApiUser,
      key: (): string | number => newKeyUserId2,
      company_id: (): any => company_id,
      proc: (): string => 'error',
      result: (): string => 'Access denied',
    },
    {
      description: `shouldn't link user_to_company by user to other company`,
      user: (): ApiKeyClient => newApiUser,
      key: (): string | number => newKeyUserId3,
      company_id: (): any => company_id,
      proc: (): string => 'error',
      result: (): string => 'Access denied',
    },
    {
      description: `shouldn't link user_to_company(admin) by user to his company`,
      user: (): ApiKeyClient => newApiUser,
      key: (): string | number => user_id,
      company_id: (): string | number => newCompany_idSuperAdmin,
      proc: (): string => 'error',
      result: (): string => 'Access denied',
    },
  ];
  describe.each(testCasesUser)('$description', ({ user, key, company_id, proc, result }): void => {
    test(`shouldn't link user to company by user`, async (): Promise<void> => {
      const response = await user().request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.USER_TO_COMPANY,
          company_id: company_id(),
          obj_id: key(),
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toBe(proc());
      expect(response.body.ops[0].description).toBe(result());
    });
  });

  const testCasesAdmin = [
    {
      description: `shouldn't link user_to_company by admin to other company`,
      user: (): ApiKeyClient => newApiAdm,
      key: (): string | number => newKeyUserId2,
      company_id: (): string | number => newCompany_idSuperAdmin,
      proc: (): string => 'error',
      result: (): string => 'Access denied',
      user_to_company: (): undefined => undefined,
    },
    {
      description: `shouldn't link user_to_company by admin to his company (User already in company)`,
      user: (): ApiKeyClient => newApiAdm,
      key: (): string | number => newKeyAdminId,
      company_id: (): any => company_id,
      proc: (): string => 'error',
      result: (): string => 'User already in company',
      user_to_company: (): undefined => undefined,
    },
    {
      description: `should link user_to_company by admin to his company`,
      user: (): ApiKeyClient => newApiAdm,
      key: (): string | number => newApiKeyIdUser,
      company_id: (): any => company_id,
      proc: (): string => 'ok',
      result: (): undefined => undefined,
      user_to_company: (): string => 'user_to_company',
    },
  ];
  describe.each(testCasesAdmin)('$description', ({ user, key, company_id, proc, result, user_to_company }): void => {
    test(`should link user to company by admin`, async (): Promise<void> => {
      const response = await user().request(
        createRequestWithOps({
          type: REQUEST_TYPE.LINK,
          obj: OBJ_TYPE.USER_TO_COMPANY,
          company_id: company_id(),
          obj_id: key(),
        }),
      );
      expect(response.status).toBe(200);
      expect(response.body.ops[0].proc).toBe(proc());
      expect(response.body.ops[0].description).toBe(result());
      expect(response.body.ops[0].obj).toBe(user_to_company());
    });
  });

  afterAll(
    async (): Promise<void> => {
      const deleteOperations = [
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newApiKeyId, company_id },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newApiKeyIdAdm, company_id },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newKeyAdminId, company_id },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, level: '', company_id, group_id: '0', obj_id: user_id },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newKeyUserId1, company_id },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newKeyUserId2, company_id: newCompany_idSuperAdmin },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.COMPANY, obj_id: newCompany_idSuperAdmin },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newKeyUserId2, company_id },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newApiKeyIdUser, company_id },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newKeyUserId3, company_id: newCompany_idSuperAdmin },
        { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.COMPANY, obj_id: company_id },
      ];

      await cleanupTestResources(apiUserCookie, deleteOperations);
    },
  );
});
