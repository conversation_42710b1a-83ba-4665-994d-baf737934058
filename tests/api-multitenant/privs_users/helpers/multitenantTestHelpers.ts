import { ApiKeyClient } from '../../../../application/api/ApiKeyClient';
import { ApiUserClient } from '../../../../application/api/ApiUserClient';
import { application } from '../../../../application/Application';
import { ApiKey } from '../../../../infrastructure/model/ApiKey';
import { ConfigurationManager } from '../../../../infrastructure/config/ConfigurationManager';
import { User } from '../../../../infrastructure/model/User';
import { Method } from '../../../../utils/request';
import { axiosInstance } from '../../../../application/api/AxiosClient';

import {
  createRequestWithOps,
  createRequestWithObj,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
} from '../../../../utils/corezoidRequest';
import { debug } from '../../../../support/utils/logger';

export interface ApiKeySetupResult {
  client: ApiKeyClient;
  keyId: number;
  apiKey: ApiKey;
}

export interface CompanySetupResult {
  companyId: string;
  companyName: string;
}

export interface MultitenantTestSetup {
  host: string;
  company_id: any;
  user: User;
  apiUserCookie: ApiUserClient;
  newApi: ApiKeyClient;
  newApiKeyId: number;
  newApiAdm: ApiKeyClient;
  newApiKeyIdAdm: number;
  group_id: string | number;
}

export async function createTestCompany(apiUserCookie: ApiUserClient, name: string): Promise<string> {
  const response = await apiUserCookie.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.COMPANY,
      name,
      login: '<EMAIL>',
      title: 'testGroupSuper',
      login_type: 'corezoid',
      auth_providers: ['corezoid'],
    }),
  );
  expect(response.status).toBe(RESP_STATUS.OK);
  return response.body.ops[0].obj_id;
}

export async function setupTestConfiguration(): Promise<{
  host: string;
  company_id: any;
  superadmin_id: any;
  user: User;
  apiUserCookie: ApiUserClient;
}> {
  const config = ConfigurationManager.getConfiguration();
  const host = config.getApiUrl();
  debug(host);

  const user = await application.getAuthorizedUser({ company: {} }, 5);
  const apiUserCookie = await application.getApiUserClient(user);

  const testCompanyName = `TestCompany_${Date.now()}`;
  const company_id = await createTestCompany(apiUserCookie, testCompanyName);

  const responseMe = await axiosInstance({
    method: Method.GET,
    url: `${host}auth/me`,
    data: {},
    headers: {
      Cookie: user.cookieUser,
      Origin: host,
    },
  });
  expect(responseMe.status).toBe(200);
  expect(responseMe.data.result).toBe('ok');
  const superadmin_id = responseMe.data.user_id;

  return { host, company_id, user, apiUserCookie, superadmin_id };
}

export async function createApiKey(
  apiUserCookie: ApiUserClient,
  title: string,
  company_id: any,
): Promise<ApiKeySetupResult> {
  const response = await apiUserCookie.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.USER,
      title,
      logins: [{ type: 'api' }],
      company_id,
    }),
  );
  expect(response.status).toBe(RESP_STATUS.OK);

  const user_api = response.body;
  const apiKey = {
    key: `${user_api.ops[0].users[0].logins[0].obj_id}`,
    secret: user_api.ops[0].users[0].logins[0].key,
    companies: [],
    title: '',
    id: `${user_api.ops[0].users[0].obj_id}`,
  } as ApiKey;

  const client = application.getApiKeyClient(apiKey);
  const keyId = +apiKey.id;

  return { client, keyId, apiKey };
}

export async function findAdminGroup(apiUserCookie: ApiUserClient, company_id: any): Promise<string | number> {
  const response = await apiUserCookie.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LIST,
      obj: OBJ_TYPE.COMPANY_USERS,
      filter: 'group',
      company_id,
    }),
  );
  expect(response.status).toBe(RESP_STATUS.OK);
  return (response.body.ops[0].list as Array<any>).find(item => item.title === 'Admins').obj_id;
}

export async function linkUserToAdminGroup(
  apiUserCookie: ApiUserClient,
  userId: string | number,
  groupId: string | number,
  company_id: any,
): Promise<void> {
  const response = await apiUserCookie.request(
    createRequestWithOps({
      type: REQUEST_TYPE.LINK,
      obj: OBJ_TYPE.USER,
      level: 1,
      company_id,
      group_id: groupId,
      obj_id: userId,
    }),
  );
  expect(response.status).toBe(RESP_STATUS.OK);
}

export async function cleanupTestResources(apiUserCookie: ApiUserClient, operations: Array<any>): Promise<void> {
  const response = await apiUserCookie.request(
    createRequestWithObj({
      ops: operations,
    }),
  );
  expect(response.status).toBe(RESP_STATUS.OK);
}
