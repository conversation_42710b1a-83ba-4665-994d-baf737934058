import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { ApiUserClient } from '../../../application/api/ApiUserClient';

import {
  createRequestWithOps,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
  generateName,
} from '../../../utils/corezoidRequest';
import { requestCreateObjNew } from '../../../application/api/ApiObj';
import {
  setupTestConfiguration,
  createApiKey,
  findAdminGroup,
  linkUserToAdminGroup,
  createTestCompany,
  cleanupTestResources,
} from './helpers/multitenantTestHelpers';

describe('Privs list objs multitenant (positive)', () => {
  let company_id: any;
  let group_id: string | number;
  let newApi: ApiKeyClient;
  let newApiKeyId: string | number;
  let newApiAdm: ApiKeyClient;
  let newApiKeyIdAdm: string | number;
  let apiUserCookie: ApiUserClient;
  let newCompany_idSuperAdmin: string;
  let newCompanyNameSuperAdmin: string;
  let newConvAdmin: string | number;
  let newConv1: string | number;
  let newFolderAdmin: string | number;
  let newFolder1: string | number;
  let newProjectAdmin: string | number;
  let newProject1: string | number;
  let newProjectCompany: string | number;
  let newFolderCompany: string | number;
  let project_short_nameAdm: string | number;
  let project_short_nameUser: string | number;

  beforeAll(async () => {
    const config = await setupTestConfiguration();
    company_id = config.company_id;
    apiUserCookie = config.apiUserCookie;
    project_short_nameAdm = generateName(OBJ_TYPE.PROJECT);
    project_short_nameUser = generateName(OBJ_TYPE.PROJECT);
    newCompanyNameSuperAdmin = 'newCompanySuperAdmin';

    const apiKeyResult = await createApiKey(apiUserCookie, 'apiUser1', company_id);
    newApi = apiKeyResult.client;
    newApiKeyId = apiKeyResult.keyId;

    const adminKeyResult = await createApiKey(apiUserCookie, 'apiUserAdmin', company_id);
    newApiAdm = adminKeyResult.client;
    newApiKeyIdAdm = adminKeyResult.keyId;

    group_id = await findAdminGroup(apiUserCookie, company_id);
    await linkUserToAdminGroup(apiUserCookie, newApiKeyIdAdm, group_id, company_id);

    const responseConvAdm = await requestCreateObjNew(newApiAdm, OBJ_TYPE.CONV, company_id, `Conv_${Date.now()}`, {
      conv_type: 'process',
    });
    newConvAdmin = responseConvAdm.body.ops[0].obj_id;

    const responseFolderAdm = await requestCreateObjNew(
      newApiAdm,
      OBJ_TYPE.FOLDER,
      company_id,
      `Folder_${Date.now()}`,
      {},
    );
    newFolderAdmin = responseFolderAdm.body.ops[0].obj_id;

    const responseProjectAdm = await requestCreateObjNew(
      newApiAdm,
      OBJ_TYPE.PROJECT,
      company_id,
      `Project_${Date.now()}`,
      {
        short_name: project_short_nameAdm,
      },
    );
    newProjectAdmin = responseProjectAdm.body.ops[0].obj_id;

    const responseConv1 = await requestCreateObjNew(newApi, OBJ_TYPE.CONV, company_id, `Conv_${Date.now()}`, {
      conv_type: 'process',
    });
    newConv1 = responseConv1.body.ops[0].obj_id;

    const responseFolder1 = await requestCreateObjNew(newApi, OBJ_TYPE.FOLDER, company_id, `Folder_${Date.now()}`, {});
    newFolder1 = responseFolder1.body.ops[0].obj_id;

    const responseProject1 = await requestCreateObjNew(newApi, OBJ_TYPE.PROJECT, company_id, `Project_${Date.now()}`, {
      short_name: project_short_nameUser,
    });
    newProject1 = responseProject1.body.ops[0].obj_id;

    newCompany_idSuperAdmin = await createTestCompany(apiUserCookie, newCompanyNameSuperAdmin);

    const responseFolder = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.FOLDER,
        title: `FolderC_${Date.now()}`,
        company_id: newCompany_idSuperAdmin,
      }),
    );
    expect(responseFolder.status).toBe(RESP_STATUS.OK);
    newFolderCompany = responseFolder.body.ops[0].obj_id;

    const responseProject = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.PROJECT,
        title: `ProjectC_${Date.now()}`,
        short_name: project_short_nameAdm,
        company_id: newCompany_idSuperAdmin,
      }),
    );
    expect(responseProject.status).toBe(RESP_STATUS.OK);
    newProjectCompany = responseProject.body.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 3000));
  });

  test('should list folders/projects in default Company for superadmin', async () => {
    const responseApi = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        company_id,
        obj_id: 0,
      }),
    );
    expect(responseApi.status).toBe(RESP_STATUS.OK);
    expect(responseApi.body.ops[0].proc).toBe(PROC_STATUS.OK);
    expect(responseApi.body.ops[0].obj).toBe('folder');
    expect((responseApi.body.ops[0].list as Array<any>).find(item => item.obj_id === newConv1).is_owner).toEqual(false);
    expect((responseApi.body.ops[0].list as Array<any>).find(item => item.obj_id === newConvAdmin).is_owner).toEqual(
      false,
    );
    expect((responseApi.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder1).is_owner).toEqual(
      false,
    );
    expect((responseApi.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolderAdmin).is_owner).toEqual(
      false,
    );

    const responseList = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECTS,
        company_id,
        obj_id: 0,
      }),
    );
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toBe(PROC_STATUS.OK);
    expect(responseList.body.ops[0].obj).toBe('projects');
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newProject1).is_owner).toEqual(
      false,
    );
    expect(
      (responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newProjectAdmin).is_owner,
    ).toEqual(false);
  });

  test('should list folder/projects in default Company for admin', async () => {
    const responseList = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
      }),
    );
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toBe(PROC_STATUS.OK);
    expect(responseList.body.ops[0].obj).toBe('folder');
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newConv1).is_owner).toEqual(
      false,
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newConvAdmin).is_owner).toEqual(
      true,
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder1).is_owner).toEqual(
      false,
    );
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolderAdmin).is_owner).toEqual(
      true,
    );

    const responseListProjects = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECTS,
        obj_id: 0,
        company_id,
      }),
    );
    expect(responseListProjects.status).toBe(RESP_STATUS.OK);
    expect(responseListProjects.body.ops[0].proc).toBe(PROC_STATUS.OK);
    expect(responseListProjects.body.ops[0].obj).toBe('projects');
    expect(
      (responseListProjects.body.ops[0].list as Array<any>).find(item => item.obj_id === newProject1).is_owner,
    ).toEqual(false);
    expect(
      (responseListProjects.body.ops[0].list as Array<any>).find(item => item.obj_id === newProjectAdmin).is_owner,
    ).toEqual(true);
  });

  test('should list Folders/Projects in default Company for user', async () => {
    const responseList = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id,
      }),
    );
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toBe(PROC_STATUS.OK);
    expect(responseList.body.ops[0].obj).toBe('folder');
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newConv1).is_owner).toEqual(true);
    expect((responseList.body.ops[0].list as Array<any>).find(item => item.obj_id === newFolder1).is_owner).toEqual(
      true,
    );
    expect(responseList.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newConvAdmin })]),
    );
    expect(responseList.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newFolderAdmin })]),
    );
    const responseListProjects = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECTS,
        obj_id: 0,
        company_id,
      }),
    );
    expect(responseListProjects.status).toBe(RESP_STATUS.OK);
    expect(responseListProjects.body.ops[0].proc).toBe(PROC_STATUS.OK);
    expect(responseListProjects.body.ops[0].obj).toBe('projects');
    expect(
      (responseListProjects.body.ops[0].list as Array<any>).find(item => item.obj_id === newProject1).is_owner,
    ).toEqual(true);
    expect(responseListProjects.body.ops[0].list).not.toEqual(
      expect.arrayContaining([expect.objectContaining({ obj_id: newProjectAdmin })]),
    );
  });

  test(`shouldn't list admin's folder/projects in default Company for user`, async () => {
    const responseList = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolderAdmin,
        company_id,
      }),
    );
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toBe(PROC_STATUS.ERROR);
    expect(responseList.body.ops[0].description).toBe('Access denied');

    const responseListProjects = await newApi.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProjectAdmin,
        company_id,
      }),
    );
    expect(responseListProjects.status).toBe(RESP_STATUS.OK);
    expect(responseListProjects.body.ops[0].proc).toBe(PROC_STATUS.ERROR);
    expect(responseListProjects.body.ops[0].description).toBe('Access denied');
  });

  test(`shouldn't list folder/projects in other Company for admin`, async () => {
    const responseList = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: 0,
        company_id: newCompany_idSuperAdmin,
      }),
    );
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toBe(PROC_STATUS.ERROR);
    expect(responseList.body.ops[0].description).toBe('User not in company');

    const responseListProjects = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECTS,
        obj_id: 0,
        company_id: newCompany_idSuperAdmin,
      }),
    );
    expect(responseListProjects.status).toBe(RESP_STATUS.OK);
    expect(responseListProjects.body.ops[0].proc).toBe(PROC_STATUS.ERROR);
    expect(responseListProjects.body.ops[0].description).toBe('User not in company');
  });

  test(`shouldn't list users's folder/projects in other Company for admin`, async () => {
    const responseList = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.FOLDER,
        obj_id: newFolderCompany,
        company_id,
      }),
    );
    expect(responseList.status).toBe(RESP_STATUS.OK);
    expect(responseList.body.ops[0].proc).toBe(PROC_STATUS.ERROR);
    expect(responseList.body.ops[0].description).toBe(`Object's company ID does not match company ID in the request`);

    const responseListProjects = await newApiAdm.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.PROJECT,
        obj_id: newProjectCompany,
        company_id,
      }),
    );
    expect(responseListProjects.status).toBe(RESP_STATUS.OK);
    expect(responseListProjects.body.ops[0].proc).toBe(PROC_STATUS.ERROR);
    expect(responseListProjects.body.ops[0].description).toBe(
      `Object's company ID does not match company ID in the request`,
    );
  });

  afterAll(async () => {
    const deleteOperations = [
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newApiKeyId, company_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.USER, obj_id: newApiKeyIdAdm, company_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.CONV, obj_id: newConv1, company_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.CONV, obj_id: newConvAdmin, company_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.FOLDER, obj_id: newFolder1, company_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.FOLDER, obj_id: newFolderAdmin, company_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.PROJECT, obj_id: newProject1, company_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.PROJECT, obj_id: newProjectAdmin, company_id },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.COMPANY, obj_id: newCompany_idSuperAdmin },
      { type: REQUEST_TYPE.DELETE, obj: OBJ_TYPE.COMPANY, obj_id: company_id },
    ];

    await cleanupTestResources(apiUserCookie, deleteOperations);
  });
});
