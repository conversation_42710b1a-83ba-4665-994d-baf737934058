import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { ApiUserClient } from '../../../application/api/ApiUserClient';

import {
  createRequestWithOps,
  createRequestWithObj,
  OBJ_TYPE,
  REQUEST_TYPE,
  RESP_STATUS,
  PROC_STATUS,
} from '../../../utils/corezoidRequest';
import {
  setupTestConfiguration,
  createApiKey,
  findAdminGroup,
  linkUserToAdminGroup,
  createTestCompany,
} from './helpers/multitenantTestHelpers';

describe('API Key and Group Deletion in Multitenant Environment', () => {
  let company1_id: any;
  let apiUserCookie: ApiUserClient;

  let company2_id: string;
  let company2Name: string;

  let userApiClient: ApiKeyClient;
  let userApiKeyId: string | number;
  let userApiKeys: Array<string | number> = [];

  let adminApiClient: ApiKeyClient;
  let adminApiKeyId: string | number;
  let adminApiKeys: Array<string | number> = [];

  const superadminApiKeys: Array<string | number> = [];

  let adminGroupId: string | number;
  let adminCreatedGroups: Array<string | number> = [];
  let superadminCreatedGroups: Array<string | number> = [];

  let company2ApiKeyId: string | number;
  let company2GroupId: string | number;

  const createGroup = async (title: string, companyId: string | number): Promise<any> => {
    const response = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id: companyId,
        title,
        obj_type: 'admins',
      }),
    );

    expect(response.status).toBe(RESP_STATUS.OK);
    return response.body;
  };

  const cleanupResources = async (): Promise<void> => {
    const ops = [];

    [...userApiKeys, ...adminApiKeys, ...superadminApiKeys].forEach(keyId => {
      if (keyId) {
        ops.push({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.USER,
          obj_id: keyId,
          company_id: company1_id,
        });
      }
    });

    [...adminCreatedGroups, ...superadminCreatedGroups].forEach(groupId => {
      if (groupId) {
        ops.push({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.GROUP,
          obj_id: groupId,
          company_id: company1_id,
        });
      }
    });

    if (company2ApiKeyId) {
      ops.push({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.USER,
        obj_id: company2ApiKeyId,
        company_id: company2_id,
      });
    }

    if (company2GroupId) {
      ops.push({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.GROUP,
        obj_id: company2GroupId,
        company_id: company2_id,
      });
    }

    if (company2_id) {
      ops.push({
        type: REQUEST_TYPE.DELETE,
        obj: OBJ_TYPE.COMPANY,
        obj_id: company2_id,
      });
    }

    ops.push({
      type: REQUEST_TYPE.DELETE,
      obj: OBJ_TYPE.COMPANY,
      obj_id: company1_id,
    });

    if (ops.length > 0) {
      const response = await apiUserCookie.request(
        createRequestWithObj({
          ops,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
    }
  };

  beforeAll(async () => {
    const config = await setupTestConfiguration();
    company1_id = config.company_id;
    apiUserCookie = config.apiUserCookie;
    company2Name = `Company2_${Date.now()}`;

    company2_id = await createTestCompany(apiUserCookie, company2Name);

    const userKeyResult = await createApiKey(apiUserCookie, 'UserApiKey', company1_id);
    userApiClient = userKeyResult.client;
    userApiKeyId = userKeyResult.keyId;
    userApiKeys.push(userApiKeyId);

    for (let i = 0; i < 2; i++) {
      const response = await userApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.USER,
          title: `UserCreatedKey_${i}`,
          logins: [{ type: 'api' }],
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);

      const keyId = response.body.ops[0].users[0].obj_id;
      userApiKeys.push(keyId);
    }

    const adminKeyResult = await createApiKey(apiUserCookie, 'AdminApiKey', company1_id);
    adminApiClient = adminKeyResult.client;
    adminApiKeyId = adminKeyResult.keyId;
    adminApiKeys.push(adminApiKeyId);

    adminGroupId = await findAdminGroup(apiUserCookie, company1_id);
    await linkUserToAdminGroup(apiUserCookie, adminApiKeyId, adminGroupId, company1_id);

    for (let i = 0; i < 2; i++) {
      const response = await adminApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.CREATE,
          obj: OBJ_TYPE.USER,
          title: `AdminCreatedKey_${i}`,
          logins: [{ type: 'api' }],
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);

      const keyId = response.body.ops[0].users[0].obj_id;
      adminApiKeys.push(keyId);
    }

    const keyResult = await createApiKey(apiUserCookie, `SuperadminApiKey`, company1_id);
    const keyId = keyResult.keyId;
    superadminApiKeys.push(keyId);

    const groupDataA = await adminApiClient.request(
      createRequestWithOps({
        type: REQUEST_TYPE.CREATE,
        obj: OBJ_TYPE.GROUP,
        company_id: company1_id,
        title: `AdminGroup`,
        obj_type: 'admins',
      }),
    );

    expect(groupDataA.status).toBe(RESP_STATUS.OK);
    expect(groupDataA.body.ops[0].proc).toBe(PROC_STATUS.OK);

    adminCreatedGroups.push(groupDataA.body.ops[0].obj_id);

    for (let i = 0; i < 2; i++) {
      const groupData = await createGroup(`SuperadminGroup_${i}`, company1_id);
      superadminCreatedGroups.push(groupData.ops[0].obj_id);
    }

    const company2KeyResult = await createApiKey(apiUserCookie, 'Company2ApiKey', company2_id);
    company2ApiKeyId = company2KeyResult.keyId;

    const company2GroupData = await createGroup('Company2Group', company2_id);
    company2GroupId = company2GroupData.ops[0].obj_id;

    await new Promise(r => setTimeout(r, 3000));
  });

  describe('API Key Deletion Tests', () => {
    test('User can delete only their own keys', async () => {
      const response = await userApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.USER,
          obj_id: userApiKeys[1], // Delete one of the user's own keys
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);

      userApiKeys = userApiKeys.filter(id => id !== userApiKeys[1]);
    });

    test("User receives access error when trying to delete other users' keys", async () => {
      const response = await userApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.USER,
          obj_id: adminApiKeys[0],
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.ERROR);
      expect(response.body.ops[0].description).toBe('Access denied');
    });

    test('User receives access error when trying to delete keys from another company', async () => {
      const response = await userApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.USER,
          obj_id: company2ApiKeyId,
          company_id: company2_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.ERROR);
      expect(response.body.ops[0].description).toBe('Access denied');
    });

    test('Admin can delete their own keys', async () => {
      const response = await adminApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.USER,
          obj_id: adminApiKeys[1],
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);

      adminApiKeys = adminApiKeys.filter(id => id !== adminApiKeys[1]);
    });

    test('Admin can delete keys created by other users in Company1', async () => {
      const response = await adminApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.USER,
          obj_id: userApiKeys[1],
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);

      userApiKeys = userApiKeys.filter(id => id !== userApiKeys[1]);
    });

    test('Admin can delete keys created by Superadmin in Company1', async () => {
      const response = await adminApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.USER,
          obj_id: superadminApiKeys[0],
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);

      superadminApiKeys.splice(0, 1);
    });

    test('Admin receives access error when deleting keys from Company2', async () => {
      const response = await adminApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.USER,
          obj_id: company2ApiKeyId,
          company_id: company2_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.ERROR);
      expect(response.body.ops[0].description).toBe('Access denied');
    });

    test('Superadmin can delete any keys in any company', async () => {
      const response1 = await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.USER,
          obj_id: adminApiKeys[1],
          company_id: company1_id,
        }),
      );

      expect(response1.status).toBe(RESP_STATUS.OK);
      expect(response1.body.ops[0].proc).toBe(PROC_STATUS.OK);

      adminApiKeys = adminApiKeys.filter(id => id !== adminApiKeys[1]);

      const response2 = await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.USER,
          obj_id: company2ApiKeyId,
          company_id: company2_id,
        }),
      );

      expect(response2.status).toBe(RESP_STATUS.OK);
      expect(response2.body.ops[0].proc).toBe(PROC_STATUS.OK);

      company2ApiKeyId = '';
    });
  });

  describe('Group Deletion Tests', () => {
    test('User cannot delete any group — access denied', async () => {
      const response = await userApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.GROUP,
          obj_id: adminCreatedGroups[0],
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.ERROR);
      expect(response.body.ops[0].description).toBe('Access denied');
    });

    test('Admin can delete groups created by Admin in Company1', async () => {
      const response = await adminApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.GROUP,
          obj_id: adminCreatedGroups[0],
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);

      adminCreatedGroups = adminCreatedGroups.filter(id => id !== adminCreatedGroups[0]);
    });

    test('Admin can delete groups created by Superadmin in Company1', async () => {
      const response = await adminApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.GROUP,
          obj_id: superadminCreatedGroups[1],
          company_id: company1_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.OK);

      superadminCreatedGroups = superadminCreatedGroups.filter(id => id !== superadminCreatedGroups[1]);
    });

    test('Admin receives access error when deleting groups in Company2', async () => {
      const response = await adminApiClient.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.GROUP,
          obj_id: company2GroupId,
          company_id: company2_id,
        }),
      );

      expect(response.status).toBe(RESP_STATUS.OK);
      expect(response.body.ops[0].proc).toBe(PROC_STATUS.ERROR);
      expect(response.body.ops[0].description).toBe('Access denied');
    });

    test('Superadmin can delete any group in any company', async () => {
      const response1 = await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.GROUP,
          obj_id: superadminCreatedGroups[0],
          company_id: company1_id,
        }),
      );

      expect(response1.status).toBe(RESP_STATUS.OK);
      expect(response1.body.ops[0].proc).toBe(PROC_STATUS.OK);

      superadminCreatedGroups = superadminCreatedGroups.filter(id => id !== superadminCreatedGroups[0]);

      const response2 = await apiUserCookie.request(
        createRequestWithOps({
          type: REQUEST_TYPE.DELETE,
          obj: OBJ_TYPE.GROUP,
          obj_id: company2GroupId,
          company_id: company2_id,
        }),
      );

      expect(response2.status).toBe(RESP_STATUS.OK);
      expect(response2.body.ops[0].proc).toBe(PROC_STATUS.OK);

      company2GroupId = '';
    });
  });

  afterAll(async () => {
    await cleanupResources();

    const response = await apiUserCookie.request(
      createRequestWithOps({
        type: REQUEST_TYPE.LIST,
        obj: OBJ_TYPE.COMPANY,
        filter: company2Name,
      }),
    );

    expect(response.status).toBe(RESP_STATUS.OK);
  });
});
