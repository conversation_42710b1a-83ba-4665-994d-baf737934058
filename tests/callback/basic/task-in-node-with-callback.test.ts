import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import {
  createCallbackConveyor,
  configureCallbackNode,
  commitConveyor,
  createTaskInConveyor,
  showTask,
  cleanupConveyor,
  sendDirectCallbackRequest,
  waitForCallback,
  CallbackConveyorSetup,
} from '../helpers/callbackTestHelpers';

describe('Task in Node with Callback', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let setup: CallbackConveyorSetup;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    setup = await createCallbackConveyor(api, company_id, 'CallbackConvTest');

    await configureCallbackNode(setup, {
      type: 'api_callback',
      obj_id_path: 'task_id',
    });

    await commitConveyor(setup);
  });

  test('should modify waiting task through direct callback URL', async () => {
    const { task_id } = await createTaskInConveyor(setup, { a: 1 });

    await waitForCallback(1000);

    const callbackResponse = await sendDirectCallbackRequest(setup, {
      task_id: `${task_id}`,
      b: '2',
      modified: true,
    });

    expect(callbackResponse.status).toBe(RESP_STATUS.OK);
    expect(callbackResponse.data.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(callbackResponse.data.ops[0].obj).toEqual('task');

    await waitForCallback(2000);

    const taskData = await showTask(setup, task_id);
    expect(taskData.data.a).toBe(1);
    expect(taskData.data.b).toBe('2');
    expect(taskData.data.modified).toBe(true);
    expect(taskData.data.task_id).toBe(`${task_id}`);
  });

  afterAll(async () => {
    await cleanupConveyor(setup);
  });
});
