import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import {
  createCallbackConveyor,
  commitConveyor,
  createTaskInConveyor,
  showTask,
  cleanupConveyor,
  sendDirectCallbackRequest,
  waitForCallback,
  CallbackConveyorSetup,
} from '../helpers/callbackTestHelpers';

describe('Task in Node without Callback', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let setup: CallbackConveyorSetup;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    setup = await createCallbackConveyor(api, company_id, 'ProcessConvNoCallback');

    const response = await setup.api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: setup.process_node_id,
        conv_id: setup.conv_id,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            node_title: 'final',
            to_conv_title: 'post_application/json',
            to_node_title: 'final',
            to_node_id: setup.final_node_id,
            edit: true,
            format: 'json',
            type: 'go',
            conv_title: 'post_application/json',
          },
        ],
        semaphors: [],
        node_location: [50, 180],
        version: 22,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);

    await commitConveyor(setup);
  });

  test('should get error when trying to modify task in non-callback node', async () => {
    const { task_id } = await createTaskInConveyor(setup, { test_data: 'no_callback_test' });

    await waitForCallback(1000);

    const callbackResponse = await sendDirectCallbackRequest(setup, {
      task_id: `${task_id}`,
      b: '2',
    });

    expect(callbackResponse.status).toBe(RESP_STATUS.OK);
    expect(callbackResponse.data.proc).toEqual(PROC_STATUS.ERROR);

    const taskResponse = await showTask(setup, task_id);
    expect(taskResponse.status).toBe(RESP_STATUS.OK);
    expect(taskResponse.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    const taskData = taskResponse.body.ops[0];
    expect(taskData.data.test_data).toBe('no_callback_test');
    expect(taskData.data.b).toBeUndefined();
  });

  afterAll(async () => {
    await cleanupConveyor(setup);
  });
});
