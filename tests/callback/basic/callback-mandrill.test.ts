import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE } from '../../../utils/corezoidRequest';
import { requestList } from '../../../application/api/ApiObj';
import { axiosInstance } from '../../../application/api/AxiosClient';
import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';
import { addMsg } from 'jest-html-reporters/helper';

import {
  createCallbackConveyor,
  configureCallbackNode,
  commitConveyor,
  createTaskInConveyor,
  showTask,
  cleanupConveyor,
  CallbackConveyorSetup,
  expectJsonKeyInData,
} from '../helpers/callbackTestHelpers';

describe('Callback + Callback Mandrill', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let setup: CallbackConveyorSetup;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    setup = await createCallbackConveyor(api, company_id, 'CallbackMandrillConv');

    await configureCallbackNode(setup, {
      type: 'api_callback',
      obj_id_path: 'task_id',
    });

    await commitConveyor(setup);
  });

  test('should create task via direct callback URL json', async () => {
    const { task_id } = await createTaskInConveyor(setup, { test_data: 'test_callback' });
    await new Promise(r => setTimeout(r, 2000));
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/${setup.process_node_id}/${setup.hash}/${task_id}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { new: 4545 },
    });

    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.data.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 2000));

    const responseShow = await requestList(api, setup.final_node_id, setup.conv_id, company_id, 1);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].list[0].data.new).toBe(4545);
    expect(responseShow.body.ops[0].list[0].data.test_data).toBe('test_callback');
  });

  test('should create task via direct callback URL nvp', async () => {
    const { task_id } = await createTaskInConveyor(setup, { test_data: 'test_callback' });
    await new Promise(r => setTimeout(r, 2000));
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/nvp/callback/${setup.process_node_id}/${setup.hash}/${task_id}`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { new: 4545 },
    });

    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data).toContain('request_proc=ok');

    await new Promise(r => setTimeout(r, 2000));

    const responseShow = await requestList(api, setup.final_node_id, setup.conv_id, company_id, 1);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].list[0].data["{\"new\":4545}"]).toBe(true);
    expect(responseShow.body.ops[0].list[0].data.test_data).toBe('test_callback');
  });

  test.each([
    { apiVersion: 'api/1', description: 'API v1' },
    { apiVersion: 'api/2', description: 'API v2' }
  ])('should modify task via direct callback URL without task_id - $description', async ({ apiVersion }) => {
    const { task_id } = await createTaskInConveyor(setup, { test_data: 'test_callback' });
    await new Promise(r => setTimeout(r, 2000));
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}${apiVersion}/json/callback/${setup.process_node_id}/${setup.hash
      }`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { new: 4545, task_id: `${task_id}` },
    });

    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(response.data.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 2000));

    const responseShow = await requestList(api, setup.final_node_id, setup.conv_id, company_id, 1);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].list[0].data.new).toBe(4545);
    expect(responseShow.body.ops[0].list[0].data.test_data).toBe('test_callback');
  });

  test('should modify task via Mandrill plugin URL', async () => {
    const { task_id } = await createTaskInConveyor(setup, { test_data: 'test_callback' });
    await new Promise(r => setTimeout(r, 2000));

    const mandrillUri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/plugins/mandrill/${setup.process_node_id
      }/${setup.hash}`;
    const mandrillResponse = await axiosInstance({
      method: 'POST',
      url: mandrillUri,
      data: { task_id: `${task_id}`, c: 'mandrill_test' },
    });

    expect(mandrillResponse.status).toBe(RESP_STATUS.OK);
    expect(mandrillResponse.data.ops.proc).toEqual(PROC_STATUS.OK);
    expect(mandrillResponse.data.ops.obj).toEqual('task');

    await new Promise(r => setTimeout(r, 2000));

    const taskData = await showTask(setup, task_id);
    expect(taskData.data.c).toBe('mandrill_test');
    expect(taskData.data.test_data).toBe('test_callback');
    expect(taskData.data.task_id).toBe(`${task_id}`);
  });

  test('should modify task via Mandrill events format', async () => {
    const { task_id } = await createTaskInConveyor(setup, { test_data: 'test_callback' });
    await new Promise(r => setTimeout(r, 2000));

    const mandrillUri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/plugins/mandrill/${setup.process_node_id
      }/${setup.hash}`;
    const mandrillResponse = await axiosInstance({
      method: 'POST',
      url: mandrillUri,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data: `mandrill_events=[{ "task_id": "${task_id}", "c": "mandrill_events_test" }]`,
    });

    expect(mandrillResponse.status).toBe(RESP_STATUS.OK);
    expect(mandrillResponse.data.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(mandrillResponse.data.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 3000));

    const taskData = await showTask(setup, task_id);
    expect(taskData.data.c).toBe('mandrill_events_test');
    expect(taskData.data.test_data).toBe('test_callback');
    expect(taskData.data.task_id).toBe(`${task_id}`);
  });

  test('should modify task via Mandrill events format with create task by public url', async () => {
    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/public/${setup.conv_id}/${setup.hash
      }`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: { new: 4545 },
    });

    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.ops.proc).toEqual(PROC_STATUS.OK);
    expect(response.data.ops.obj).toEqual('task');

    await new Promise(r => setTimeout(r, 2000));

    const responseShow = await requestList(api, setup.process_node_id, setup.conv_id, company_id, 10);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    const task_id = responseShow.body.ops[0].list[0].obj_id;

    const mandrillUri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/plugins/mandrill/${setup.process_node_id
      }/${setup.hash}`;
    const mandrillResponse = await axiosInstance({
      method: 'POST',
      url: mandrillUri,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data: `mandrill_events=[{ "task_id": "${task_id}", "c": "mandrill_events_test" }]`,
    });

    expect(mandrillResponse.status).toBe(RESP_STATUS.OK);
    expect(mandrillResponse.data.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(mandrillResponse.data.ops[0].obj).toEqual('task');

    await new Promise(r => setTimeout(r, 3000));

    const taskData = await showTask(setup, task_id);
    expect(taskData.data.c).toBe('mandrill_events_test');
    expect(taskData.data.new).toBe(4545);
    expect(taskData.data.task_id).toBe(`${task_id}`);
  });

    test('should modify task', async () => {
    const { ref } = await createTaskInConveyor(setup, { test_data: 'test_callback' });
    await new Promise(r => setTimeout(r, 2000));

      const response = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.MODIFY,
            obj: OBJ_TYPE.TASK,
            conv_id:setup.conv_id, ref, data:{new:4545},
          }),
        );
        expect(response.status).toBe(200);
        expect(response.body.request_proc).toBe('ok');

    await new Promise(r => setTimeout(r, 2000));

    const responseShow = await requestList(api, setup.final_node_id, setup.conv_id, company_id, 1);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
    expect(responseShow.body.ops[0].list[0].data.new).toBe(4545);
    expect(responseShow.body.ops[0].list[0].data.test_data).toBe('test_callback');
  });

    test('should modify task', async () => {
    const { task_id } = await createTaskInConveyor(setup, { test_data: 'test_callback' });
    await new Promise(r => setTimeout(r, 2000));

      const response = await api.request(
          createRequestWithOps({
            type: REQUEST_TYPE.DELETE,
            obj: OBJ_TYPE.TASK,
            conv_id:setup.conv_id, 
            obj_id:task_id,
            node_id:setup.process_node_id,
          }),
        );
        expect(response.status).toBe(200);
        expect(response.body.request_proc).toBe('ok');

    await new Promise(r => setTimeout(r, 2000));

    const taskData = await showTask(setup, task_id);
    expect(taskData.description).toBe('task not found');
  });

  test.skip('should handle blocked/paused callback scenario', async () => {
    addMsg({ message: 'Testing blocked/paused callback scenario', context: '' });

    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/public/${setup.conv_id}/${setup.hash
      }`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: {
        blocked_test: true,
        status: 'blocked',
        blocked_reason: 'paused_for_testing',
      },
    });

    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.ops.proc).toEqual(PROC_STATUS.OK);
    expect(response.data.ops.obj).toEqual('task');

    const task_id = response.data.ops.obj_id;
    await new Promise(r => setTimeout(r, 3000));

    const mandrillUri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/plugins/mandrill/${setup.process_node_id
      }/${setup.hash}`;

    const mandrillResponse = await axiosInstance({
      method: 'POST',
      url: mandrillUri,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data: `mandrill_events=[{"event":"blocked","status":"paused","task_id":"${task_id}","blocked_reason":"paused_for_testing","timestamp":${Date.now()}}]`,
    });

    expect(mandrillResponse.status).toBe(RESP_STATUS.OK);
    expect(mandrillResponse.data.ops.proc).toEqual(PROC_STATUS.OK);

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await requestList(api, setup.process_node_id, setup.conv_id, company_id, 10);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
  });

  test.skip('should handle delete callback scenario', async () => {
    addMsg({ message: 'Testing delete callback scenario', context: '' });

    const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/public/${setup.conv_id}/${setup.hash
      }`;
    const response = await axiosInstance({
      method: 'POST',
      url: uri,
      data: {
        delete_test: true,
        action: 'delete',
        delete_reason: 'cleanup_test',
      },
    });

    expect(response.status).toBe(RESP_STATUS.OK);
    expect(response.data.ops.proc).toEqual(PROC_STATUS.OK);
    expect(response.data.ops.obj).toEqual('task');

    const task_id = response.data.ops.obj_id;
    await new Promise(r => setTimeout(r, 3000));

    const mandrillUri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/plugins/mandrill/${setup.process_node_id
      }/${setup.hash}`;

    const mandrillResponse = await axiosInstance({
      method: 'POST',
      url: mandrillUri,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data: `mandrill_events=[{"event":"deleted","status":"removed","task_id":"${task_id}","delete_reason":"cleanup_test","timestamp":${Date.now()}}]`,
    });

    expect(mandrillResponse.status).toBe(RESP_STATUS.OK);
    expect(mandrillResponse.data.ops.proc).toEqual(PROC_STATUS.OK);

    await new Promise(r => setTimeout(r, 3000));

    const responseShow = await requestList(api, setup.final_node_id, setup.conv_id, company_id, 10);
    expect(responseShow.status).toBe(RESP_STATUS.OK);
    expect(responseShow.body.ops[0].proc).toEqual(PROC_STATUS.OK);
  });

  afterAll(async () => {
    await cleanupConveyor(setup);
  });
});
