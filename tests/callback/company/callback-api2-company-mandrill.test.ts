import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import {
  createCallbackConveyor,
  configureCallbackNode,
  commitConveyor,
  showTask,
  cleanupConveyor,
  waitForCallback,
  CallbackConveyorSetup,
} from '../helpers/callbackTestHelpers';
import { sendMandrillCallback, sendDirectUrlCallback } from '../helpers/mandrillTestHelpers';

describe('Callback API2 Company with Mandrill', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let setup: CallbackConveyorSetup;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    setup = await createCallbackConveyor(api, company_id, 'CallbackApi2CompanyMandrill');

    await configureCallbackNode(setup, {
      type: 'api_callback',
      obj_id_path: 'task_id',
    });

    await commitConveyor(setup);
  });

  test('should handle company-scoped Mandrill callback integration', async () => {
    const taskResponse = await sendDirectUrlCallback(setup, {
      company_id: company_id,
      mandrill_test: 'company_integration',
      email_event: 'delivered',
    });

    await waitForCallback(3000);

    const task_id = taskResponse.data.ops.obj_id;

    await sendMandrillCallback(
      setup,
      {
        event: 'delivered',
        status: 'success',
        company_context: company_id,
      },
      task_id,
    );

    await waitForCallback();

    const showTaskResponse = await showTask(setup, task_id);
    expect(showTaskResponse.status).toBe(RESP_STATUS.OK);
    expect(showTaskResponse.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    const taskData = showTaskResponse.body.ops[0];
    expect(taskData.data.company_id).toBe(company_id);
    expect(taskData.data.mandrill_test).toBe('company_integration');
    expect(taskData.data.email_event).toBe('delivered');
    expect(taskData.data.event).toBe('delivered');
    expect(taskData.data.status).toBe('success');
    expect(taskData.data.company_context).toBe(company_id);
  });

  test('should validate company isolation with Mandrill callbacks', async () => {
    const taskResponse = await sendDirectUrlCallback(setup, {
      isolation_test: true,
      company_boundary: company_id,
    });

    const task_id = taskResponse.data.ops.obj_id;
    await waitForCallback();

    await sendMandrillCallback(
      setup,
      {
        validation_event: 'company_isolation',
        authorized_company: company_id,
        test_boundary: 'validated',
      },
      task_id,
    );

    await waitForCallback();

    const showTaskResponse2 = await showTask(setup, task_id);
    expect(showTaskResponse2.status).toBe(RESP_STATUS.OK);
    expect(showTaskResponse2.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    const taskData = showTaskResponse2.body.ops[0];
    expect(taskData.data.isolation_test).toBe(true);
    expect(taskData.data.company_boundary).toBe(company_id);
    expect(taskData.data.validation_event).toBe('company_isolation');
    expect(taskData.data.authorized_company).toBe(company_id);
    expect(taskData.data.test_boundary).toBe('validated');
  });

  test('should handle multiple Mandrill events for company context', async () => {
    const taskResponse = await sendDirectUrlCallback(setup, {
      multi_event_test: true,
      company_id: company_id,
    });

    const task_id = taskResponse.data.ops.obj_id;
    await waitForCallback();

    const events = ['sent', 'delivered', 'opened', 'clicked'];

    for (const event of events) {
      await sendMandrillCallback(
        setup,
        {
          event_type: event,
          timestamp: Date.now(),
          company_scope: company_id,
        },
        task_id,
      );
      await waitForCallback(1000);
    }

    const showTaskResponse3 = await showTask(setup, task_id);
    expect(showTaskResponse3.status).toBe(RESP_STATUS.OK);
    expect(showTaskResponse3.body.ops[0].proc).toEqual(PROC_STATUS.OK);

    const taskData = showTaskResponse3.body.ops[0];
    expect(taskData.data.multi_event_test).toBe(true);
    expect(taskData.data.company_id).toBe(company_id);
    expect(taskData.data.event_type).toBe('clicked');
    expect(taskData.data.company_scope).toBe(company_id);
  });

  afterAll(async () => {
    await cleanupConveyor(setup);
  });
});
