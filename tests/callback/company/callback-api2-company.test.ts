import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { application } from '../../../application/Application';
import { ApiKey } from '../../../infrastructure/model/ApiKey';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS } from '../../../utils/corezoidRequest';
import {
  createCallbackConveyor,
  configureCallbackNode,
  commitConveyor,
  createTaskInConveyor,
  showTask,
  cleanupConveyor,
  waitForCallback,
  CallbackConveyorSetup,
} from '../helpers/callbackTestHelpers';

describe('Callback API2 Company Scoped', () => {
  let api: ApiKeyClient;
  let apikey: ApiKey;
  let company_id: any;
  let setup: CallbackConveyorSetup;
  let targetSetup: CallbackConveyorSetup;

  beforeAll(async () => {
    apikey = await application.getApiKey();
    api = application.getApiKeyClient(apikey);
    company_id = apikey.companies[0].id;

    setup = await createCallbackConveyor(api, company_id, 'CallbackApi2Company');
    targetSetup = await createCallbackConveyor(api, company_id, 'TargetCallbackCompany');

    await configureCallbackNode(targetSetup, {
      type: 'api_callback',
      obj_id_path: '',
    });

    await commitConveyor(targetSetup);
  });

  test('should handle company-scoped callback API2 with modify mode', async () => {
    const response = await setup.api.request(
      createRequestWithOps({
        type: REQUEST_TYPE.MODIFY,
        obj: OBJ_TYPE.NODE,
        obj_id: setup.process_node_id,
        conv_id: setup.conv_id,
        title: 'process',
        description: '',
        obj_type: 0,
        logics: [
          {
            type: 'api_copy',
            sync: true,
            err_node_id: '',
            conv_id: targetSetup.conv_id,
            mode: 'modify',
            ref: 'company_callback_ref',
            data: {
              company_id: company_id,
              test_type: 'company_scoped',
              callback_mode: 'api2',
            },
            data_type: {
              company_id: 'string',
              test_type: 'string',
              callback_mode: 'string',
            },
            group: 'all',
          },
          {
            to_node_id: setup.final_node_id,
            format: 'json',
            type: 'go',
            index: 1,
            node_title: 'final',
          },
        ],
        semaphors: [],
        version: 22,
      }),
    );
    expect(response.status).toBe(RESP_STATUS.OK);

    await commitConveyor(setup);

    const { task_id } = await createTaskInConveyor(setup, {
      source_company: company_id,
      test_scenario: 'company_callback_api2',
    });

    await waitForCallback(3000);

    const taskData = await showTask(setup, task_id);
    expect(taskData.data.source_company).toBe(company_id);
    expect(taskData.data.test_scenario).toBe('company_callback_api2');
  });

  test('should validate company isolation in callback processing', async () => {
    const { task_id } = await createTaskInConveyor(
      targetSetup,
      {
        company_validation: true,
        isolation_test: 'company_boundary',
      },
      'company_callback_ref',
    );

    await waitForCallback();

    const taskData = await showTask(targetSetup, task_id);
    expect(taskData.data.company_validation).toBe(true);
    expect(taskData.data.isolation_test).toBe('company_boundary');
    expect(taskData.data.company_id).toBe(company_id);
    expect(taskData.data.test_type).toBe('company_scoped');
    expect(taskData.data.callback_mode).toBe('api2');
  });

  test('should handle multiple company callback scenarios', async () => {
    const tasks = [];

    for (let i = 0; i < 3; i++) {
      const { task_id } = await createTaskInConveyor(
        setup,
        {
          batch_test: true,
          batch_index: i,
          company_context: company_id,
        },
        `batch_ref_${i}`,
      );
      tasks.push(task_id);
    }

    await waitForCallback(4000);

    for (let i = 0; i < tasks.length; i++) {
      const taskData = await showTask(setup, tasks[i]);
      expect(taskData.data.batch_test).toBe(true);
      expect(taskData.data.batch_index).toBe(i);
      expect(taskData.data.company_context).toBe(company_id);
    }
  });

  afterAll(async () => {
    await cleanupConveyor(setup);
    await cleanupConveyor(targetSetup);
  });
});
