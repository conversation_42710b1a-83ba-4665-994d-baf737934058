import { ApiKeyClient } from '../../../application/api/ApiKeyClient';
import { createRequestWithOps, OBJ_TYPE, REQUEST_TYPE, RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import { requestCreateObj, requestDeleteObj, requestConfirm, requestListConv } from '../../../application/api/ApiObj';

export interface CallbackConveyorSetup {
  api: ApiKeyClient;
  company_id: string | number;
  conv_id: number;
  process_node_id: string | number;
  final_node_id: string | number;
  hash?: string;
}

export interface CallbackNodeConfig {
  type: 'api_callback';
  obj_id_path?: string;
  is_sync?: boolean;
}

export const createCallbackConveyor = async (
  api: ApiKeyClient,
  company_id: string | number,
  title = `CallbackConv_${Date.now()}`,
): Promise<CallbackConveyorSetup> => {
  const responseConv = await requestCreateObj(api, OBJ_TYPE.CONV, company_id, title);
  expect(responseConv.status).toBe(RESP_STATUS.OK);

  const conv_id = responseConv.body.ops[0].obj_id;
  const hash = responseConv.body.ops[0].hash;

  const responseList = await requestListConv(api, conv_id, company_id);
  expect(responseList.status).toBe(RESP_STATUS.OK);

  const process_node_id = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'process').obj_id;
  const final_node_id = (responseList.body.ops[0].list as Array<any>).find(item => item.title === 'final').obj_id;

  return {
    api,
    company_id,
    conv_id,
    process_node_id,
    final_node_id,
    hash,
  };
};

export const configureCallbackNode = async (
  setup: CallbackConveyorSetup,
  callbackConfig: CallbackNodeConfig,
  nodeTitle = 'process',
): Promise<void> => {
  const responseFirst = await setup.api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: setup.process_node_id,
      conv_id: setup.conv_id,
      title: nodeTitle,
      description: '',
      obj_type: 0,
      logics: [
        { type: 'api_callback', obj_id_path: '' },
        {
          node_title: 'final',
          to_conv_title: 'post_application/json',
          to_node_title: 'final',
          to_node_id: setup.final_node_id,
          edit: true,
          format: 'json',
          type: 'go',
          conv_title: 'post_application/json',
        },
      ],
      semaphors: [],
      node_location: [50, 180],
      version: 22,
    }),
  );
  expect(responseFirst.status).toBe(RESP_STATUS.OK);

  const responseSecond = await setup.api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.MODIFY,
      obj: OBJ_TYPE.NODE,
      obj_id: setup.process_node_id,
      conv_id: setup.conv_id,
      title: 'process1',
      description: 'new',
      obj_type: 0,
      logics: [
        callbackConfig,
        {
          node_title: 'final',
          to_conv_title: 'post_application/json',
          to_node_title: 'final',
          to_node_id: setup.final_node_id,
          edit: true,
          format: 'json',
          type: 'go',
          conv_title: 'post_application/json',
        },
      ],
      semaphors: [],
      node_location: [50, 180],
      version: 22,
    }),
  );
  expect(responseSecond.status).toBe(RESP_STATUS.OK);
};

export const commitConveyor = async (setup: CallbackConveyorSetup): Promise<void> => {
  const response = await requestConfirm(setup.api, setup.conv_id, setup.company_id);
  expect(response.status).toBe(RESP_STATUS.OK);
};

export const createTaskInConveyor = async (
  setup: CallbackConveyorSetup,
  data: any = {},
  ref?: string,
): Promise<{ task_id: string | number; ref: string }> => {
  const taskRef = ref || `task_${Date.now()}`;

  const response = await setup.api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.CREATE,
      obj: OBJ_TYPE.TASK,
      company_id: setup.company_id,
      conv_id: setup.conv_id,
      data,
      ref: taskRef,
    }),
  );

  expect(response.status).toBe(RESP_STATUS.OK);
  expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK);
  expect(response.body.ops[0].obj).toEqual('task');

  return {
    task_id: response.body.ops[0].obj_id,
    ref: taskRef,
  };
};

export const showTask = async (setup: CallbackConveyorSetup, task_id: string | number): Promise<any> => {
  const response = await setup.api.request(
    createRequestWithOps({
      type: REQUEST_TYPE.SHOW,
      obj: OBJ_TYPE.TASK,
      company_id: setup.company_id,
      conv_id: setup.conv_id,
      obj_id: task_id,
    }),
  );

  return response;
};

export const cleanupConveyor = async (setup: CallbackConveyorSetup): Promise<void> => {
  const response = await requestDeleteObj(setup.api, OBJ_TYPE.CONV, setup.conv_id, setup.company_id);
  expect(response.status).toBe(RESP_STATUS.OK);
};

export const waitForCallback = (ms = 2000): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

export const sendDirectCallbackRequest = async (setup: CallbackConveyorSetup, data: any): Promise<any> => {
  const { axiosInstance } = await import('../../../application/api/AxiosClient');
  const { ConfigurationManager } = await import('../../../infrastructure/config/ConfigurationManager');

  const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/${setup.process_node_id}/${
    setup.hash
  }`;
  const response = await axiosInstance({
    method: 'POST',
    url: uri,
    data,
  });

  return response;
};

/**
 * Вспомогательная функция для проверки JSON-ключей в данных задачи
 * Используется когда данные отправляются через NVP формат и преобразуются в JSON-строку как ключ
 * @param data - объект данных для проверки
 * @param expectedObject - ожидаемый объект, который будет преобразован в JSON-ключ
 * @param expectedValue - ожидаемое значение для этого ключа (по умолчанию true)
 */
export const expectJsonKeyInData = (data: any, expectedObject: any, expectedValue: any = true): void => {
  const jsonKey = JSON.stringify(expectedObject);
  expect(data).toHaveProperty(jsonKey, expectedValue);
};

/**
 * Проверяет наличие JSON-ключа в данных без проверки значения
 * @param data - объект данных для проверки
 * @param expectedObject - ожидаемый объект, который будет преобразован в JSON-ключ
 */
export const expectJsonKeyExists = (data: any, expectedObject: any): void => {
  const jsonKey = JSON.stringify(expectedObject);
  expect(data).toHaveProperty(jsonKey);
};

/**
 * Парсит NVP (Name-Value Pair) ответ от API
 * @param responseData - строка ответа в формате URL-encoded
 * @returns объект с распарсенными данными
 */
export const parseNvpResponse = (responseData: string): { request_proc: string | null; ops: any[] } => {
  const params = new URLSearchParams(responseData);
  const opsString = params.get('ops');

  return {
    request_proc: params.get('request_proc'),
    ops: opsString ? JSON.parse(opsString) : [],
  };
};
