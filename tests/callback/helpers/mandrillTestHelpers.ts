import { ConfigurationManager } from '../../../infrastructure/config/ConfigurationManager';
import { axiosInstance } from '../../../application/api/AxiosClient';
import { RESP_STATUS, PROC_STATUS } from '../../../utils/corezoidRequest';
import { CallbackConveyorSetup } from './callbackTestHelpers';

export const sendMandrillCallback = async (
  setup: CallbackConveyorSetup,
  data: any,
  taskId?: string | number,
): Promise<any> => {
  const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/plugins/mandrill/${
    setup.process_node_id
  }/${setup.hash}`;

  const requestData = taskId ? { task_id: `${taskId}`, ...data } : data;

  const response = await axiosInstance({
    method: 'POST',
    url: uri,
    data: requestData,
  });

  expect(response.status).toBe(RESP_STATUS.OK);
  expect(response.data.ops.proc).toEqual(PROC_STATUS.OK);
  expect(response.data.ops.obj).toEqual('task');

  return response;
};

export const sendMandrillEvents = async (
  setup: CallbackConveyorSetup,
  events: any[],
  taskId?: string | number,
): Promise<any> => {
  const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/plugins/mandrill/${
    setup.process_node_id
  }/${setup.hash}`;

  const eventsData = events.map(event => (taskId ? { task_id: `${taskId}`, ...event } : event));

  const response = await axiosInstance({
    method: 'POST',
    url: uri,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: `mandrill_events=[${eventsData.map(e => JSON.stringify(e)).join(',')}]`,
  });

  expect(response.status).toBe(RESP_STATUS.OK);
  expect(response.data.ops.proc).toEqual(PROC_STATUS.OK);
  expect(response.data.ops.obj).toEqual('task');

  return response;
};

export const sendDirectUrlCallback = async (setup: CallbackConveyorSetup, data: any): Promise<any> => {
  const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/public/${setup.conv_id}/${setup.hash}`;

  const response = await axiosInstance({
    method: 'POST',
    url: uri,
    data,
  });

  expect(response.status).toBe(RESP_STATUS.OK);
  expect(response.data.ops.proc).toEqual(PROC_STATUS.OK);
  expect(response.data.ops.obj).toEqual('task');

  return response;
};

export const sendCallbackUrl = async (setup: CallbackConveyorSetup, data: any): Promise<any> => {
  const uri = `${ConfigurationManager.getConfiguration().getApiUrl()}api/1/json/callback/${setup.process_node_id}/${
    setup.hash
  }`;

  const response = await axiosInstance({
    method: 'POST',
    url: uri,
    data,
  });

  expect(response.status).toBe(RESP_STATUS.OK);
  expect(response.data.ops[0].proc).toEqual(PROC_STATUS.OK);
  expect(response.data.ops[0].obj).toEqual('task');

  return response;
};
