import http from 'k6/http';
import { debug } from '../utils/logger.js';
import { check, group } from 'k6';
import { SharedArray } from 'k6/data';
import EnvConfig from './config/envconfig.js';

const host = EnvConfig.corezoid_perf.BASE_URL;
const params = EnvConfig.params;

export const options = {
  scenarios: {
    webhook_test: {
      executor: 'ramping-arrival-rate',

      startRate: 1,
      timeUnit: '1s',
      stages: [
        { target: 200, duration: '60s' },
        { target: 270, duration: '1m' },
        { target: 270, duration: '3m' },
      ],
      preAllocatedVUs: 100,
      maxVUs: 5000,
    },
  },
  thresholds: {
    'http_req_duration{scenario:webhook_test}': ['p(99)<500'],
  },
};

const dataTask = new SharedArray('some data name', function() {
  return JSON.parse(open('../data/dataTask1KB.json'));
});

const dataTaskID = new SharedArray('conv_id', function() {
  return JSON.parse(open(`../data/dataConv.json`));
});

const dataConv = dataTaskID.map(data => {
  return data.conv_id;
});
const dataHash = dataTaskID.map(data => {
  return data.hash;
});

let dataIndex = 0;

export default function() {
  const convID = dataConv[dataIndex];
  const hashID = dataHash[dataIndex];

  dataIndex = (dataIndex + 1) % dataConv.length;

  group('v1 API testing', function() {
    group('create task', function() {
      const payload2 = JSON.stringify(dataTask[0]);
      const url = `${host}/public/${convID}/${hashID}`;
      const response = http.post(url, payload2, params);
      const body = JSON.parse(JSON.stringify(response.body, null, 2));

      const checkOutput = check(response, {
        'is status 200': r => r.status === 200,
        proc_ok: () => body && body.includes && body.includes(`"proc":"ok"`),
      });

      if (!checkOutput) {
        debug(body);
        debug(`Status Code: ${response.status}`);
        fail('Check failed');
      }
    });
  });
}
