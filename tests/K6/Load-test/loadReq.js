import http from 'k6/http';
import { error } from '../utils/logger.js';
import { check, group, sleep } from 'k6';
import EnvConfig from './config/envconfig.js';

const host = EnvConfig.account_dev.BASE_URL;

export const options = {
  scenarios: {
    my_api_test: {
      executor: 'ramping-arrival-rate',

      startRate: 1,
      timeUnit: '1s',
      stages: [
        { target: 10, duration: '60s' },
        { target: 20, duration: '1m' },
      ],
      preAllocatedVUs: 100,
      maxVUs: 5000,
    },
  },
  thresholds: {
    'http_req_duration{scenario:my_api_test}': ['p(99)<500'],
  },
};

export default function() {
  group('v1 API testing', function() {
    group('Get conf', function() {
      const url = `${host}/face/api/1/auth/conf`;
      const response = http.get(url);
      const body = JSON.parse(JSON.stringify(response.body, null, 2));

      const checkOutput = check(response, {
        'is status 200': r => r.status === 200,
      });

      if (!checkOutput) {
        error(body);
        error(`Request failed with status ${response.status}`);
      }
      sleep(2);
    });
  });
}
