import http from 'k6/http';
import { debug } from '../utils/logger.js';
import { check, group } from 'k6';
import { Trend } from 'k6/metrics';
import { htmlReport } from 'https://raw.githubusercontent.com/benc-uk/k6-reporter/2.4.0/dist/bundle.js';
import EnvConfig from './config/envconfig.js';
import { generateUrlWithSignature } from '../utils/generateSignature.js';

const config = EnvConfig.k8;
const host = config.BASE_URL;
const API_LOGIN = config.API_LOGIN;
const API_SECRET = config.API_SECRET;
const params = EnvConfig.params;

export const TrendRTT = new Trend('RTT');

export const options = {
  insecureSkipTLSVerify: true,

  stages: [{ duration: '1m', target: 1 }],

  thresholds: {
    RTT: ['avg<500'],
  },
};

export default function() {
  const conv_id = 113;

  group('SyncAPI testing', function() {
    group('create task', function() {
      const payload2 = JSON.stringify({
        timeout: 59,
        ops: [
          {
            conv_id,
            type: 'create',
            obj: 'task',
            data: {},
          },
        ],
      });

      const url = generateUrlWithSignature(payload2, API_LOGIN, API_SECRET, host);
      const response = http.post(url, payload2, params);
      const body = JSON.parse(JSON.stringify(response.body, null, 2));

      const checkOutput = check(response, {
        'is status 200': r => r.status === 200,
        proc_ok: () => body && body.includes && body.includes(`"proc":"ok"`),
      });

      if (!checkOutput) {
        debug(body);
        debug(`Status Code: ${response.status}`);
        fail('Check failed');
      }
      TrendRTT.add(response.timings.duration);
    });
  });
}

export function handleSummary(data) {
  return {
    'report/K6/summary.html': htmlReport(data),
  };
}
