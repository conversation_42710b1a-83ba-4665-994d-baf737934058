const EnvConfig = {
  perf: {
    BASE_URL: 'https://admin-perf.corezoid.com/api/2/json',
    API_LOGIN: 41,
    API_SECRET: '1fAFdP6Ml9LDThdlgUqIjOvO7XcMp7CbhLIWIPTebnEXOSRF2I',
    SIZE: 'dataTask10KB',
    WEBHOOK_CONV1: '195/32a8864a1b7945962e99b2ec0419b4f556c57b67',
    WEBHOOK_CONV2: '201/4f389581ebfc5fc9972a10c7e225c195f680a344',
    CONV_SD: 193,
    CONVS_CODE: '../data/dataConvCode.json',
    CONVS_APICALL: '../data/dataConvApiCall.json',
  },
  k8s: {
    BASE_URL: 'https://corezoid-k8s-develop.dev-devops.corezoid.com/api/2/json',
    API_LOGIN: 238,
    API_SECRET: 'hBCMnNA8GmG1VRdZgxN4gOWmSM9WjsHzFfxaaoot1LzJphI3DM',
    SIZE: 'dataTask10KB',
    WEBHOOK_CONV1: '1820/6d0d63d887e9752604f2bdbe2406b9e309682077',
    WEBHOOK_CONV2: '1830/ef7319f1291cbd9770b91b8b02538c651be718ae',
    CONV_SD: 1818,
  },
  eks: {
    BASE_URL: 'https://cor-full.eks.simulator.company/api/2/json',
    API_LOGIN: 7,
    API_SECRET: 'beP18i9gq9FtN09mDGo5dTzZaOnkN5YYGot77zG34zmBRV5rNq',
    SIZE: 'dataTask10KB',
    WEBHOOK_CONV1: '298/6a18f1faf6b2a342fbf0f9b5147a04e8d86d7852',
    WEBHOOK_CONV2: '302/c0921f65efb7a289f77280f41c35ba7ad003a482',
    CONV_SD: 295,
    CONVS_CODE: '../data/eks/dataConvCode.json',
    CONVS_APICALL: '../data/eks/dataConvApiCall.json',
  },
  pre: {
    BASE_URL: 'https://admin-pre.corezoid.com/api/2/json',
    API_LOGIN: 10357,
    API_SECRET: 'yGSLv7H4uB0VT5fllolohysUBhJ0xTZ59rtnnuRKnVOOgvJbup',
    API_LOGIN2: 6746,
    API_SECRET2: 'YTCuBCrO7IeVqBHOzvok1VdcTADcS7awWy2q6kyArvSNajRxA7',
    API_LOGIN3: 105235,
    API_SECRET3: 'O8WTYb1IeMy4sUKPbVv0nHzgx3FTbGVFGzir2YGEERlA2fwGLE',
    SIZE: 'dataTask1KB',
    WEBHOOK_CONV1: '1697331/d6ccea0691fae378048c030cfab787534961ee5c',
    WEBHOOK_CONV2: '1697335/eb1ede912b9fa9ad91539fe1e6fb1f0aa3e7ce45',
    CONV_SD: 1697329,
    CONVS_CODE: '',
    CONVS_APICALL: '',
  },
  k8: {
    BASE_URL: 'https://syncapi-k8s.dev-devops.corezoid.com/api/2/json',
    API_LOGIN: 6,
    API_SECRET: 'hHJ88Sj8RRH2oVMuW6703Y6U6WX1AAt2dqqlZAAvDZhPINX4Wv',
    SIZE: 'dataTask1KB',
  },
  corezoid_perf: {
    BASE_URL: 'https://corezoid-perf.corez-dev.aws.pmicloud.biz/api/2/json',
  },
  account_dev: {
    BASE_URL: 'https://account.dev.corezoid.com',
  },
  params: {
    headers: {
      'Content-Type': 'application/json',
      Connection: 'keep-alive',
    },
  },
  sendResults: {
    webhook: 'https://www.corezoid.com/api/2/json/public/1408900/093c4fe357b8e9a467fe7239d49126ddbf32c2d8',
    headers: {
      'Content-Type': 'application/json',
    },
  },
};

module.exports = EnvConfig;
