import http from 'k6/http';
import { check, group } from 'k6';
import { SharedArray } from 'k6/data';
import { randomItem } from 'https://jslib.k6.io/k6-utils/1.2.0/index.js';
import { sendResultsToWebhook } from '../utils/sendResult.js';
import { htmlReport } from 'https://raw.githubusercontent.com/benc-uk/k6-reporter/2.4.0/dist/bundle.js';
import { generateUrlWithSignature } from '../utils/generateSignature.js';
import EnvConfig from './config/envconfig.js';

const config = EnvConfig.pre;
const params = EnvConfig.params;
const host = config.BASE_URL;
const API_LOGIN = config.API_LOGIN2;
const API_SECRET = config.API_SECRET2;

const dataTaskID = new SharedArray('conv_id', function() {
  return JSON.parse(open(`../data/dataConvCode.json`));
});
const dataConv = dataTaskID.map(data => {
  return data.conv_id;
});
const convID = randomItem(dataConv);

function generateUID() {
  const timestamp = Date.now();
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = (timestamp + Math.random() * 16) % 16 | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

export const options = {
  scenarios: {
    my_api_test: {
      executor: 'ramping-arrival-rate',

      startRate: 1,
      timeUnit: '10s',
      stages: [
        { target: 100, duration: '1m' },
        { target: 100, duration: '3m' },
      ],
      preAllocatedVUs: 10,
      maxVUs: 1000,
    },
  },
};

export default function() {
  group('Load code', function() {
    const uniqueUID = generateUID();
    const payload2 = JSON.stringify({
      ops: [
        {
          conv_id: convID,
          type: 'create',
          obj: 'task',
          action: 'user',
          ref: `${uniqueUID}`,
          data: { time: 1739544077 },
        },
      ],
    });

    const url = generateUrlWithSignature(payload2, API_LOGIN, API_SECRET, host);
    const response = http.post(url, payload2, params);
    const body = JSON.parse(JSON.stringify(response.body, null, 2));

    check(response, {
      'is status 200': r => r.status === 200,
      proc_ok: () => body && body.includes && body.includes(`"proc":"ok"`),
    });
  });
}

export function handleSummary(data) {
  sendResultsToWebhook(data);
  return {
    'report/K6/summary.html': htmlReport(data),
  };
}
