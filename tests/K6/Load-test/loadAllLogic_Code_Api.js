import http from 'k6/http';
import { debug } from '../utils/logger.js';
import { check, group } from 'k6';
import { SharedArray } from 'k6/data';
import { randomItem } from 'https://jslib.k6.io/k6-utils/1.2.0/index.js';
import { sendResultsToWebhook } from '../utils/sendResult.js';
import { htmlReport } from 'https://raw.githubusercontent.com/benc-uk/k6-reporter/2.4.0/dist/bundle.js';
import EnvConfig from './config/envconfig.js';
import { generateUrlWithSignature } from '../utils/generateSignature.js';

const Config = EnvConfig[__ENV.ENVIRONMENT] || EnvConfig['eks'];
const BASE_URL = Config.BASE_URL;
const size = Config.SIZE;
const API_LOGIN = Config.API_LOGIN;
const API_SECRET = Config.API_SECRET;
const webhook_conv1 = Config.WEBHOOK_CONV1;
const webhook_conv2 = Config.WEBHOOK_CONV2;
const CONV_SD = Config.CONV_SD;
const convs_Code = Config.CONVS_CODE;
const convs_ApiCall = Config.CONVS_APICALL;
const additionalParams = {
  local: false,
  name_test: 'allLogics',
  project: 'Corezoid',
  env: BASE_URL,
};

export const options = {
  scenarios: {
    my_api_test: {
      executor: 'ramping-arrival-rate',

      startRate: 1,
      timeUnit: '1s',
      stages: [
        { target: 200, duration: '60s' },
        { target: 200, duration: '119m' },
      ],
      preAllocatedVUs: 100,
      maxVUs: 5000,
    },
  },
  thresholds: {
    'http_req_duration{scenario:my_api_test}': ['p(99)<500'],
  },
};

const dataTask = new SharedArray('some data name', function() {
  return JSON.parse(open(`../data/${size}.json`));
});

const dataTaskIDApi = new SharedArray('conv_id', function() {
  return JSON.parse(open(`${convs_ApiCall}`));
});
const dataConvApi = dataTaskIDApi.map(data => {
  return data.conv_id;
});
const convIDApi = randomItem(dataConvApi);

const dataTaskIDCode = new SharedArray('conv_id1', function() {
  return JSON.parse(open(`${convs_Code}`));
});
const dataConvCode = dataTaskIDCode.map(data => {
  return data.conv_id;
});
const convIDCode = randomItem(dataConvCode);

export default function() {
  const params = {
    headers: {
      'Content-Type': 'application/json',
      Connection: 'keep-alive',
    },
    timeout: 180000,
  };

  group('All logics', function() {
    group('create task1', function() {
      const payload2 = JSON.stringify(dataTask[0]);
      const url = `${BASE_URL}/public/${webhook_conv1}`;
      const response = http.post(url, payload2, params);
      const body = JSON.parse(response.body);

      const checkOutput = check(response, {
        proc_ok: () => body && body.includes && body.includes(`"proc":"ok"`),
      });

      if (!checkOutput) {
        debug(body);
        debug(`Status Code: ${response.status}`);
        fail('Check failed');
      }
    });

    group('create task2', function() {
      const payload2 = JSON.stringify(dataTask[0]);
      const url = `${BASE_URL}/public/${webhook_conv2}`;
      const response = http.post(url, payload2, params);
      const body = JSON.parse(response.body);

      const checkOutput = check(response, {
        proc_ok: () => body && body.includes && body.includes(`"proc":"ok"`),
      });

      if (!checkOutput) {
        debug(body);
        debug(`Status Code: ${response.status}`);
        fail('Check failed');
      }
    });

    group('modify task', function() {
      function getRandomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
      }
      const ref = getRandomInt(1, 100);
      const payload = dataTask[0];
      const payload2 = JSON.stringify({
        ops: [
          {
            conv_id: `${CONV_SD}`,
            type: 'modify',
            obj: 'task',
            action: 'user',
            ref: `${ref}`,
            data: payload,
          },
        ],
      });

      const url = generateUrlWithSignature(payload2, API_LOGIN, API_SECRET, BASE_URL);
      const response = http.post(url, payload2, params);
      const body = JSON.parse(response.body);

      const checkOutput = check(response, {
        proc_ok: () => body && body.includes && body.includes(`"proc":"ok"`),
      });

      if (!checkOutput) {
        debug(body);
        debug(`Status Code: ${response.status}`);
        fail('Check failed');
      }
    });

    group('create task Code', function() {
      const payload = dataTask[0];
      const payload2 = JSON.stringify({
        ops: [
          {
            conv_id: convIDCode,
            type: 'create',
            obj: 'task',
            action: 'user',
            ref: `${Math.random()}`,
            data: payload,
          },
        ],
      });

      const url = generateUrlWithSignature(payload2, API_LOGIN, API_SECRET, BASE_URL);
      const response = http.post(url, payload2, params);
      const body = JSON.parse(response.body);

      const checkOutput = check(response, {
        proc_ok: () => body && body.includes && body.includes(`"proc":"ok"`),
      });
      if (!checkOutput) {
        debug(body);
        debug(`Status Code: ${response.status}`);
        fail('Check failed');
      }
    });

    group('create task Api', function() {
      const payload = dataTask[0];
      const payload2 = JSON.stringify({
        ops: [
          {
            conv_id: convIDApi,
            type: 'create',
            obj: 'task',
            action: 'user',
            ref: `${Math.random()}`,
            data: payload,
          },
        ],
      });

      const url = generateUrlWithSignature(payload2, API_LOGIN, API_SECRET, BASE_URL);
      const response = http.post(url, payload2, params);
      const body = JSON.parse(response.body);

      const checkOutput = check(response, {
        proc_ok: () => body && body.includes && body.includes(`"proc":"ok"`),
      });
      if (!checkOutput) {
        debug(body);
        debug(`Status Code: ${response.status}`);
        fail('Check failed');
      }
    });
  });
}

export function handleSummary(data) {
  sendResultsToWebhook(data, additionalParams);
  return {
    'report/summary.html': htmlReport(data),
  };
}
