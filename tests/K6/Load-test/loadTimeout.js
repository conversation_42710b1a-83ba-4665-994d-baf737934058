import http from 'k6/http';
import { debug } from '../utils/logger.js';
import { check, group, fail } from 'k6';
import { sendResultsToWebhook } from '../utils/sendResult.js';
import { htmlReport } from 'https://raw.githubusercontent.com/benc-uk/k6-reporter/2.4.0/dist/bundle.js';
import EnvConfig from './config/envconfig.js';
import { generateUrlWithSignature } from '../utils/generateSignature.js';

const config = EnvConfig.pre;
const host = config.BASE_URL;
const API_LOGIN = config.API_LOGIN3;
const API_SECRET = config.API_SECRET3;
const size = config.SIZE;
const params = EnvConfig.params;

const additionalParams = {
  local: false,
  name_test: 'allLogics',
  project: 'Corezoid',
  env: host,
  taskSize: size,
};

export const options = {
  scenarios: {
    my_api_test: {
      executor: 'ramping-arrival-rate',

      startRate: 1,
      timeUnit: '1s',
      stages: [{ target: 1, duration: '10m' }],
      preAllocatedVUs: 1,
      maxVUs: 10000,
    },
  },
  thresholds: {
    'http_req_duration{scenario:my_api_test}': ['p(99)<300'],
  },
};

export default function() {
  group('All logics', function() {
    group('get callbackhash', function() {
      const payload2 = JSON.stringify({
        ops: [
          {
            conv_id: 2207841,
            type: 'get',
            obj: 'callback_hash',
          },
          {
            obj_id: 2207841,
            type: 'list',
            obj: 'conv',
          },
          {
            conv_id: 2207841,
            type: 'show',
            obj: 'conf',
            obj_type: 'timer',
          },
        ],
      });

      const url = generateUrlWithSignature(payload2, API_LOGIN, API_SECRET, host);
      const response = http.post(url, payload2, params);
      const body = JSON.parse(response.body);

      const checkOutput = check(response, {
        proc_ok: () => body && body.proc === 'ok',
      });

      if (!checkOutput) {
        debug('task3 - errorCode', response.status, 'task3 - errorMessage', body);
        fail('Request validation failed');
      }
    });
  });
}

export function handleSummary(data) {
  sendResultsToWebhook(data, additionalParams);
  return {
    'report/K6/summary.html': htmlReport(data),
  };
}
