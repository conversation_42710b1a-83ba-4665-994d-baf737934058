import http from 'k6/http';
import { debug } from '../utils/logger.js';
import { check, group, fail } from 'k6';
import { SharedArray } from 'k6/data';
import { sendResultsToWebhook } from '../utils/sendResult.js';
import { htmlReport } from 'https://raw.githubusercontent.com/benc-uk/k6-reporter/2.4.0/dist/bundle.js';
import EnvConfig from './config/envconfig.js';
import { generateUrlWithSignature } from '../utils/generateSignature.js';

const config = EnvConfig.k8s;
const host = config.BASE_URL;
const API_LOGIN = config.API_LOGIN;
const API_SECRET = config.API_SECRET;
const params = EnvConfig.params;
const size = config.SIZE;

const additionalParams = {
  local: false,
  name_test: 'allLogics',
  project: 'Corezoid',
  env: host,
  taskSize: size,
};

export const options = {
  scenarios: {
    my_api_test: {
      executor: 'ramping-arrival-rate',
      startRate: 1,
      timeUnit: '1s',
      stages: [
        { target: 10, duration: '60s' },
        { target: 500, duration: '1140s' },
        { target: 500, duration: '600s' },
      ],
      preAllocatedVUs: 1,
      maxVUs: 10000,
    },
  },
  thresholds: {
    'http_req_duration{scenario:my_api_test}': ['p(99)<300'],
  },
};

const dataTask = new SharedArray('some data name', function() {
  return JSON.parse(open(`../data/${size}.json`));
});

export default function() {
  group('All logics', function() {
    group('create task1', function() {
      const payload2 = JSON.stringify(dataTask[0]);
      const url = `${host}/public/${config.WEBHOOK_CONV1}`;
      const response = http.post(url, payload2, params);
      const body = JSON.parse(response.body);

      const checkOutput = check(response, {
        proc_ok: () => body && body.ops.proc === 'ok',
      });

      if (!checkOutput) {
        fail('task1 - errorCode: ' + response.status + ' task1 - errorMessage: ' + body);
      }
    });

    group('create task2', function() {
      const payload2 = JSON.stringify(dataTask[0]);
      const url = `${host}/public/${config.WEBHOOK_CONV2}`;
      const response = http.post(url, payload2, params);
      const body = JSON.parse(response.body);

      const checkOutput2 = check(response, {
        proc_ok: () => body && body.ops.proc === 'ok',
      });

      if (!checkOutput2) {
        fail('task2 - errorCode: ' + response.status + ' task2 - errorMessage: ' + body);
      }
    });

    group('modify task', function() {
      function getRandomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
      }
      const ref = getRandomInt(1, 100);
      const payload = dataTask[0];
      const payload2 = JSON.stringify({
        ops: [
          {
            conv_id: `${config.CONV_SD}`,
            type: 'modify',
            obj: 'task',
            action: 'user',
            ref: `${ref}`,
            data: payload,
          },
        ],
      });

      const url = generateUrlWithSignature(payload2, config.API_LOGIN, config.API_SECRET, config.BASE_URL);
      const response = http.post(url, payload2, params);
      const body = JSON.parse(response.body);

      const checkOutput = check(response, {
        proc_ok: r => body && body.ops[0].proc === 'ok',
      });

      if (!checkOutput) {
        debug(body);
        debug(`Status Code: ${response.status}`);
        fail('Check failed');
      }
    });
  });
}

export function handleSummary(data) {
  sendResultsToWebhook(data, additionalParams);
  return {
    'report/K6/summary.html': htmlReport(data),
  };
}
