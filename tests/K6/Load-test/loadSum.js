import http from 'k6/http';
import { check, group } from 'k6';
import { SharedArray } from 'k6/data';
import { randomItem } from 'https://jslib.k6.io/k6-utils/1.2.0/index.js';
import { sendResultsToWebhook } from '../utils/sendResult.js';
import { htmlReport } from 'https://raw.githubusercontent.com/benc-uk/k6-reporter/2.4.0/dist/bundle.js';
import EnvConfig from './config/envconfig.js';
import { generateUrlWithSignature } from '../utils/generateSignature.js';

const config = EnvConfig.pre;
const host = config.BASE_URL;
const API_LOGIN = config.API_LOGIN;
const API_SECRET = config.API_SECRET;
const params = EnvConfig.params;

export const options = {
  scenarios: {
    my_api_test: {
      executor: 'ramping-arrival-rate',

      startRate: 1,
      timeUnit: '10s',
      stages: [
        { target: 5, duration: '30s' },
        { target: 5, duration: '30s' },
      ],
      preAllocatedVUs: 10,
      maxVUs: 1000,
    },
  },
};

const dataTaskID = new SharedArray('conv_id', function() {
  return JSON.parse(open(`../data/dataConvApiCall.json`));
});

const dataConv = dataTaskID.map(data => {
  return data.conv_id;
});

export default function() {
  const convID = randomItem(dataConv);
  const GMT_UNIXTIME = Math.floor(Date.now() / 1000);

  group('Load code', function() {
    const payload2 = JSON.stringify({
      ops: [
        {
          conv_id: convID,
          type: 'create',
          obj: 'task',
          action: 'user',
          ref: `${GMT_UNIXTIME}`,
          data: { a: 1 },
        },
      ],
    });

    const url = generateUrlWithSignature(payload2, API_LOGIN, API_SECRET, host);
    const response = http.post(url, payload2, params);
    const body = JSON.parse(JSON.stringify(response.body, null, 2));

    check(response, {
      'is status 200': r => r.status === 200,
      proc_ok: () => body && body.includes && body.includes(`"proc":"ok"`),
    });
  });
}

export function handleSummary(data) {
  sendResultsToWebhook(data);
  return {
    'report/K6/summary.html': htmlReport(data),
  };
}
