[{"code": "data.a=123;"}, {"code": "data.b=6787898989;"}, {"code": "var a = [123];data.n=a;"}, {"code": "require(\"libs/sha1.js\");var In = data.paramName;var out = CryptoJS.SHA1(In).toString();data.res = out;"}, {"code": "let i = 1;while (i <= 10000){data.a = i, i++};"}, {"code": "require(\"libs/moment.js\");data.datetime = moment().format('MMMM Do YYYY, h:mm:ss a');"}, {"code": "require(\"libs/md5.js\");var In = data.paramName;var out = CryptoJS.MD5(In).toString();data.res = out;"}, {"code": "require(\"libs/base64.js\");var In = data.paramName;var words = CryptoJS.enc.Utf8.parse(In);var base64 = CryptoJS.enc.Base64.stringify(words);data.res = base64;"}, {"code": "require(\"libs/tripledes.js\");var In = data.paramName;var passphrase = \"Secret\";var DESencrypted = CryptoJS.DES.encrypt(In, passphrase).toString();data.res = DESencrypted;"}, {"code": "require(\"libs/tripledes.js\");var In = data.paramName;var passphrase = \"Secret\";var DES3encrypted = CryptoJS.TripleDES.encrypt(In, passphrase).toString();data.res = DES3encrypted;"}, {"code": "require(\"libs/dateutils.js\");var dt = data.paramName;var formatedData = fn_convertDate(dt, \"yyyyMMdd\", \"yyyy-MM-dd\");data.res = formatedData;"}, {"code": "require(\"libs/dateutils.js\");var dt = data.paramName;var dataFromPattern = fn_getDate(dt,\"yyyy-MM-dd\");data.res = dataFromPattern;"}, {"code": "require(\"libs/dateutils.js\");var dt = data.paramName;var formatedData = fn_formatDate(dt,\"yyyyMMdd\");data.res = formatedData;"}, {"code": "require(\"libs/moment-timezone.js\");data.currentTimeUnix = moment().unix();data.kardcode = \"test\";data.qr = \"1\" + data.kardcode.toString() +data.currentTimeUnix;"}, {"code": "data.key = new Date(1575483042039).toLocaleDateString('uk-UA');"}, {"code": "require(\"libs/aes.js\");data.paramName = \"edq\";var In = data.paramName;var passphrase = \"Secret\";var dec = CryptoJS.AES.decrypt(In,passphrase).toString(CryptoJS.enc.Utf8);data.res = dec;"}, {"code": "data.a=123;"}, {"code": "data.b=6787898989;"}, {"code": "var a = [123];data.n=a;"}, {"code": "require(\"libs/sha1.js\");var In = data.paramName;var out = CryptoJS.SHA1(In).toString();data.res = out;"}, {"code": "let i = 1;while (i <= 10000){data.a = i, i++};"}, {"code": "require(\"libs/moment.js\");data.datetime = moment().format('MMMM Do YYYY, h:mm:ss a');"}, {"code": "require(\"libs/md5.js\");var In = data.paramName;var out = CryptoJS.MD5(In).toString();data.res = out;"}, {"code": "require(\"libs/base64.js\");var In = data.paramName;var words = CryptoJS.enc.Utf8.parse(In);var base64 = CryptoJS.enc.Base64.stringify(words);data.res = base64;"}, {"code": "require(\"libs/tripledes.js\");var In = data.paramName;var passphrase = \"Secret\";var DESencrypted = CryptoJS.DES.encrypt(In, passphrase).toString();data.res = DESencrypted;"}, {"code": "require(\"libs/tripledes.js\");var In = data.paramName;var passphrase = \"Secret\";var DES3encrypted = CryptoJS.TripleDES.encrypt(In, passphrase).toString();data.res = DES3encrypted;"}, {"code": "require(\"libs/dateutils.js\");var dt = data.paramName;var formatedData = fn_convertDate(dt, \"yyyyMMdd\", \"yyyy-MM-dd\");data.res = formatedData;"}, {"code": "require(\"libs/dateutils.js\");var dt = data.paramName;var dataFromPattern = fn_getDate(dt,\"yyyy-MM-dd\");data.res = dataFromPattern;"}, {"code": "require(\"libs/dateutils.js\");var dt = data.paramName;var formatedData = fn_formatDate(dt,\"yyyyMMdd\");data.res = formatedData;"}, {"code": "require(\"libs/moment-timezone.js\");data.currentTimeUnix = moment().unix();data.kardcode = \"test\";data.qr = \"1\" + data.kardcode.toString() +data.currentTimeUnix;"}, {"code": "data.key = new Date(1575483042039).toLocaleDateString('uk-UA');"}, {"code": "require(\"libs/aes.js\");data.paramName = \"edq\";var In = data.paramName;var passphrase = \"Secret\";var dec = CryptoJS.AES.decrypt(In,passphrase).toString(CryptoJS.enc.Utf8);data.res = dec;"}]