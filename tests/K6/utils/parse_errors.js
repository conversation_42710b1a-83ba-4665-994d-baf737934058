import fs from 'fs';
import readline from 'readline';
import { debug, error } from './logger.js';

const inputFile = 'report/K6/log/stderr.log';
const outputFile = 'report/K6/log/parsed_errors.json';

const maxErrorsPerCode = 10;

const rl = readline.createInterface({
  input: fs.createReadStream(inputFile),
  output: process.stdout,
  terminal: false,
});

const limitedErrors = {};

rl.on('line', line => {
  try {
    const errorMatch = line.match(/errorCode (\d+)/);
    if (errorMatch) {
      const errorCode = errorMatch[1];
      if (!limitedErrors[errorCode]) {
        limitedErrors[errorCode] = [];
      }
      if (limitedErrors[errorCode].length < maxErrorsPerCode) {
        limitedErrors[errorCode].push({ message: line });
      }
    }
  } catch (e) {
    error(`Error parsing line: ${line}`, e);
  }
});

rl.on('close', () => {
  const errorsArray = Object.values(limitedErrors).reduce((acc, cur) => acc.concat(cur), []);
  fs.writeFileSync(outputFile, JSON.stringify(errorsArray, null, 2));
  debug(`Parsed limited errors have been written to ${outputFile}`);
});
