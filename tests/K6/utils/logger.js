/**
 * Utility functions for logging in K6 tests
 */

const isLoggingEnabled = () => process.env.LOG_ENABLED === 'true';

const debug = (message, ...args) => {
  if (isLoggingEnabled()) {
    console.log(`${new Date().toISOString()} [DEBUG]: ${message}`, ...args);
  }
};

const info = (message, ...args) => {
  if (isLoggingEnabled()) {
    console.log(`${new Date().toISOString()} [INFO]: ${message}`, ...args);
  }
};

const warn = (message, ...args) => {
  if (isLoggingEnabled()) {
    console.log(`${new Date().toISOString()} [WARN]: ${message}`, ...args);
  }
};

const error = (message, ...args) => {
  if (isLoggingEnabled()) {
    console.log(`${new Date().toISOString()} [ERROR]: ${message}`, ...args);
  }
};

module.exports = {
  isLoggingEnabled,
  debug,
  info,
  warn,
  error,
};
