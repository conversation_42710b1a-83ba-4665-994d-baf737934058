import http from 'k6/http';
import EnvConfig from '../Load-test/config/envconfig.js';
import { debug } from './logger.js';

export function sendResultsToWebhook(data, additionalParams) {
  Object.assign(data, additionalParams);

  const webhookUrl = EnvConfig.sendResults.webhook;
  const params = EnvConfig.sendResults.headers;
  const response = http.post(webhookUrl, JSON.stringify(data), params);
  debug('Webhook response:', response.status);
}
