import { ConfigurationManager } from '../infrastructure/config/ConfigurationManager';

describe('Simple test', () => {
  it('should use environment variables from global setup', () => {
    // Проверяем, что переменные окружения установлены
    expect(process.env.API_KEY).toBeDefined();
    expect(process.env.API_SECRET).toBeDefined();
    expect(process.env.COMPANY_ID).toBeDefined();

    // Получаем API Key через ConfigurationManager
    const config = ConfigurationManager.getConfiguration();
    const apiKey = config.getApiKey();

    // Проверяем, что apiKey использует значения из переменных окружения
    expect(apiKey.key).toBe(process.env.API_KEY);
    expect(apiKey.secret).toBe(process.env.API_SECRET);
    expect(apiKey.companies[0].id).toBe(process.env.COMPANY_ID);

    console.log('API Key ID from environment:', process.env.API_KEY);
    console.log('API Key Secret from environment:', process.env.API_SECRET?.substring(0, 10) + '...');
    console.log('Company ID from environment:', process.env.COMPANY_ID);
  });
});
