# Callback Test Migration Plan - COR-12057

## Overview
Migration of JMeter callback test cases to Playwright test suite under `tests/callback/` folder.

## JMeter Test Cases Analysis

### Test Cases to Migrate:
1. `{{task_in_node_with_callback}}`
2. `{{task_in_node_with_out_callback}}`
3. `{{callback+callback_mandrill}}`
4. `{{callback+callback_mandrill (blocked, paused)}}`
5. `{{callback+callback_mandrill (del)}}`
6. `{{task_in_node_with_callbackApi2}}`
7. `{{task_in_node_with_callbackApi2_company}}`
8. `{{task_in_node_with_callbackApi2_company_with_mandrill}}`

## Existing Playwright Test Coverage

### ✅ Already Implemented:
| Test Name | Playwright Location | Coverage Status | Notes |
|-----------|-------------------|-----------------|-------|
| LogicCallbackApi2+Modify | `tests/api/Universal-test-suite/LogicCallbackApi2Modify.test.ts` | **COMPLETE** | Covers api_callback with api_copy modify mode, sync/async scenarios |
| LogicCallback URL Mandrill | `tests/api/Universal-test-suite/LogicCallbackCallbackUrlMandrill.test.ts` | **COMPLETE** | Covers callback URL with Mandrill plugin integration |
| Callback Hash Management | `tests/api/corezoid-api/v2/part-one/JSON/callback_hash/` | **COMPLETE** | CRUD operations for callback_hash (positive/negative) |
| Single Account Callback | `tests/api/corezoid-user-story/united_workspace/webhook_callback.test.ts` | **COMPLETE** | Webhook callback for united workspace scenarios |

### 🔍 Existing Test Capabilities:
- **API Callback Logic**: `api_callback` node type with `obj_id_path` configuration
- **Mandrill Integration**: Plugin callback via `/api/1/json/callback/plugins/mandrill/` endpoint
- **Callback Hash**: Direct URL callback hash generation, modification, deletion
- **Task Modification**: Callback-triggered task modifications with data validation
- **Sync/Async Modes**: Both synchronous and asynchronous callback processing
- **Error Handling**: Comprehensive negative test cases for invalid inputs

## Gap Analysis

### ❌ Missing Test Scenarios:
| JMeter Test Case | Migration Status | Priority | Implementation Notes |
|------------------|------------------|----------|---------------------|
| `task_in_node_with_callback` | **MISSING** | HIGH | Basic callback node functionality - may be covered by existing LogicCallbackApi2Modify |
| `task_in_node_with_out_callback` | **MISSING** | HIGH | Node without callback - negative scenario testing |
| `callback+callback_mandrill (blocked, paused)` | **MISSING** | MEDIUM | Blocked/paused state handling - mark as skipped |
| `callback+callback_mandrill (del)` | **MISSING** | MEDIUM | Callback deletion scenarios |
| `task_in_node_with_callbackApi2_company` | **MISSING** | HIGH | Company-scoped callback API2 scenarios |
| `task_in_node_with_callbackApi2_company_with_mandrill` | **MISSING** | HIGH | Combined company + Mandrill callback scenarios |

### ⚠️ Partially Covered:
| JMeter Test Case | Existing Coverage | Gap Description |
|------------------|-------------------|-----------------|
| `callback+callback_mandrill` | LogicCallbackCallbackUrlMandrill.test.ts | May need additional edge cases |
| `task_in_node_with_callbackApi2` | LogicCallbackApi2Modify.test.ts | Basic coverage exists, may need expansion |

## Migration Plan

### Phase 1: Structure Setup
- Create `tests/callback/` folder structure
- Establish base test utilities and helpers
- Set up CI configuration for callback test suite

### Phase 2: Core Callback Tests
```
tests/callback/
├── basic/
│   ├── task-in-node-with-callback.test.ts
│   ├── task-in-node-without-callback.test.ts
│   └── callback-api2-basic.test.ts
├── mandrill/
│   ├── callback-mandrill-basic.test.ts
│   ├── callback-mandrill-blocked.test.ts (skipped)
│   ├── callback-mandrill-delete.test.ts
│   └── callback-mandrill-company.test.ts
├── company/
│   ├── callback-api2-company.test.ts
│   └── callback-api2-company-mandrill.test.ts
└── helpers/
    ├── callbackTestHelpers.ts
    └── mandrillTestHelpers.ts
```

### Phase 3: Test Implementation Details

#### Test Patterns to Follow:
1. **Setup Pattern**: Create conveyor → Configure callback node → Commit changes
2. **Execution Pattern**: Send task → Trigger callback → Validate results
3. **Cleanup Pattern**: Delete test objects → Verify cleanup

#### Key Test Scenarios:
- **Basic Callback**: `api_callback` node with task processing
- **Mandrill Integration**: Plugin callback with `mandrill_events` data
- **Company Scoping**: Multi-tenant callback scenarios
- **Error Handling**: Invalid callback configurations
- **State Management**: Blocked/paused callback handling (skipped tests)

### Phase 4: CI Integration
- Update Jest configuration for callback test suite
- Add callback tests to CI pipeline
- Ensure proper test isolation and cleanup

## Implementation Notes

### Reusable Components:
- Leverage existing `ApiKeyClient` and `application` utilities
- Reuse `createRequestWithOps` patterns from Universal-test-suite
- Extend `requestCreateObj`, `requestDeleteObj` helpers

### Test Data Management:
- Use dynamic test data generation (`Date.now()`, `faker` library)
- Implement proper test isolation with unique identifiers
- Follow existing cleanup patterns in `afterAll` blocks

### Assertion Patterns:
- Status code validation: `expect(response.status).toBe(RESP_STATUS.OK)`
- Process status: `expect(response.body.ops[0].proc).toEqual(PROC_STATUS.OK)`
- Data validation: Verify callback data propagation and transformation

## Blocked/Paused Test Handling
Tests marked as "blocked" or "paused" will be implemented with `test.skip()` and appropriate comments explaining the unavailable logic.

## Success Criteria
- [ ] All missing callback test scenarios implemented
- [ ] Tests follow existing framework patterns
- [ ] CI integration working properly
- [ ] Linting passes: `npm run lint`
- [ ] Tests pass: `npx jest -i -c jest.api.config.js tests/callback/`
- [ ] Proper GitLab merge request created

## Timeline
- **Phase 1**: Structure setup (1-2 hours)
- **Phase 2**: Core test implementation (4-6 hours)
- **Phase 3**: Integration and validation (2-3 hours)
- **Phase 4**: CI integration and cleanup (1-2 hours)

**Total Estimated Effort**: 8-13 hours
