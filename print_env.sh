#!/bin/bash

# Цветовые коды
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}=============================================${NC}"
echo -e "${BLUE}            ENVIRONMENT VARIABLES            ${NC}"
echo -e "${GREEN}=============================================${NC}"
echo -e "${BLUE}API_URL:${NC}        ${API_URL}"
echo -e "${BLUE}ENV:${NC}            ${ENV}"
echo -e "${BLUE}ACTIVE_CONFIG:${NC}  ${ACTIVE_CONFIG}"
echo -e "${BLUE}COMPANY_ID:${NC}     i738314881"
echo -e "${BLUE}API_TEST:${NC}       ${API_TEST}"
echo -e "${BLUE}SUITE:${NC}          ${SUITE}"
echo -e "${BLUE}LOG_ENABLED:${NC}    false"
echo -e "${GREEN}=============================================${NC}"
