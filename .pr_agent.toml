[pr_reviewer] 
enable_review_labels_effort = true
require_score_review=true
require_tests_review=true
require_estimate_effort_to_review=true
require_can_be_split_review=false
require_security_review=false
require_ticket_analysis_review=true

extra_instructions="""\
- Ensure that all new tests cover functional, integration, and end-to-end scenarios. Use the existing Jest and Playwright configurations.
- Review the structure and readability of the tests: test cases should be clear and logically structured.
- Evaluate the completeness of test coverage for the changes, including both positive and negative scenarios.
- Verify that fixture data and mock data from `/fixture/testdata` are reused and kept up to date.
- Check that the validation schemas used in tests align with definitions in `/tests/api-multitenant/V2/schemas`.
- Ensure that updates include new test data or modifications to existing test cases.
- Confirm that new tests are properly integrated into the CI/CD pipeline, leveraging configurations in `Makefile` and `Dockerfile`.
- Check the code quality and structure of tests: ensure adherence to DRY principles, avoid duplication, make optimal use of utilities from `/utils`, and ensure logical placement of tests within the project.
- Ensure that global test configurations (e.g., `jest.global-setup.ts` and `jest.global-teardown.ts`) are aligned with the new functionality.
- Verify that test results are logged and collected into reports using `jest-html-reporters` or similar tools.
"""