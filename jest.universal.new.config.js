module.exports = {
  testEnvironment: 'node',
  globalSetup: '<rootDir>/jest.global-setup.new.ts',
  globalTeardown: '<rootDir>/jest.global-teardown.new.ts',
  setupFilesAfterEnv: ['./jest.setup.js', 'jest-extended'],
  reporters: [
    'default',
    [
      'jest-junit',
      {
        outputDirectory: './report/junit',
        outputName: 'junit.xml',
        classNameTemplate: '{classname}',
        titleTemplate: '{title}',
        ancestorSeparator: ' › ',
        usePathForSuiteName: 'true',
      },
    ],
    [
      'jest-html-reporters',
      {
        publicPath: './report/html-report',
        filename: 'CorezoidReport.html',
        expand: false,
        openReport: true,
        darkTheme: true,
      },
    ],
    ['./support/utils/custom-reporter.js', {}],
  ],
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
};
