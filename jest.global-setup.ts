import { debug, error } from './support/utils/logger';
import { application } from './application/Application';
import axios from 'axios';
import { ConfigurationManager } from './infrastructure/config/ConfigurationManager';

module.exports = async (): Promise<void> => {
  try {
    const user = await application.getAuthorizedUser({ company: {} }, 0);
    const cookie = user.cookieUser;
    const company_id = user.companies[0].id;
    const config = ConfigurationManager.getConfiguration();
    const host = config.getApiUrl();

    const responseCreateKey = await axios({
      method: 'POST',
      url: `${host}api/2/json`,
      data: {
        ops: [{ type: 'create', obj: 'user', company_id, title: `Key_${Date.now()}`, logins: [{ type: 'api' }] }],
      },
      headers: {
        Cookie: cookie,
        Origin: host,
      },
    });

    if (responseCreateKey.status !== 200) {
      error('Ошибка при создании API ключа, статус:', responseCreateKey.status);
      error('Ответ:', responseCreateKey.data);
      throw new Error('Ошибка при создании API ключа');
    }

    const createdKey = responseCreateKey.data?.ops?.[0]?.users?.[0]?.logins?.[0];
    const createdUser = responseCreateKey.data?.ops?.[0]?.users?.[0];

    if (!createdKey || !createdUser) {
      error('Ошибка: не удалось получить данные ключа или пользователя из ответа.');
      error('Ответ:', JSON.stringify(responseCreateKey.data, null, 2));
      throw new Error('Неверный формат ответа при создании API ключа');
    }

    process.env.API_SECRET = createdKey.key;
    process.env.API_KEY = createdKey.obj_id;
    process.env.OBJ_ID_KEY = createdUser.obj_id;

    debug('Create Api key, LOGIN:', process.env.API_KEY);
  } catch (err) {
    error('Ошибка в globalSetup:', err);
  }
};
